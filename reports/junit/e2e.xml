<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="38" skipped="0" tests="38" time="147.245" timestamp="2025-07-29T15:27:11.871082" hostname="ANDERSON-APES-DESKTOP"><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_successful_login_flow" time="0.019"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E985A03B0&gt;

    @mock_aws
    def test_successful_login_flow(self):
        """Test successful login with verified user."""
    
        # Create verified user
&gt;       register_data, verify_data = self._create_verified_user()

tests\e2e\auth\test_login_flow.py:83: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_login_flow.py:55: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_login_with_unverified_user" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E96935370&gt;

    @mock_aws
    def test_login_with_unverified_user(self):
        """Test login attempt with unverified user."""
    
        # Register user but don't verify
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:

tests\e2e\auth\test_login_flow.py:155: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_login_with_invalid_credentials" time="0.010"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E98D8F200&gt;

    @mock_aws
    def test_login_with_invalid_credentials(self):
        """Test login with invalid credentials."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_login_flow.py:188: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_login_flow.py:55: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_token_refresh_flow" time="0.009"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E98D8F380&gt;

    @mock_aws
    def test_token_refresh_flow(self):
        """Test token refresh functionality."""
    
        # Create verified user and login
&gt;       self._create_verified_user()

tests\e2e\auth\test_login_flow.py:246: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_login_flow.py:55: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_refresh_with_invalid_token" time="0.009"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E98D8F500&gt;

    @mock_aws
    def test_refresh_with_invalid_token(self):
        """Test refresh with invalid refresh token."""
    
        refresh_event = {
            'httpMethod': 'POST',
            'path': '/auth/refresh',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'refresh_token': 'invalid-refresh-token'
            }),
            'requestContext': {
                'requestId': 'test-refresh-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
&gt;       response = refresh_handler(refresh_event, {})

tests\e2e\auth\test_login_flow.py:325: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\refresh.py:53: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x0000022E985D7E90&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_login_rate_limiting" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E98D8F680&gt;

    @mock_aws
    def test_login_rate_limiting(self):
        """Test login rate limiting for failed attempts."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_login_flow.py:339: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_login_flow.py:55: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_login_flow.TestLoginFlow" name="test_concurrent_login_sessions" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_login_flow.TestLoginFlow object at 0x0000022E98D8F800&gt;

    @mock_aws
    def test_concurrent_login_sessions(self):
        """Test multiple concurrent login sessions for same user."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_login_flow.py:376: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_login_flow.py:55: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_complete_password_reset_flow" time="0.009"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCD370&gt;

    @mock_aws
    def test_complete_password_reset_flow(self):
        """Test complete password reset flow from request to completion."""
    
        # Create verified user
&gt;       user_data = self._create_verified_user()

tests\e2e\auth\test_password_reset_flow.py:79: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_password_reset_flow.py:54: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_forgot_password_with_nonexistent_email" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCD4F0&gt;

    @mock_aws
    def test_forgot_password_with_nonexistent_email(self):
        """Test forgot password with email that doesn't exist."""
    
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': '<EMAIL>'
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:

tests\e2e\auth\test_password_reset_flow.py:198: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_forgot_password_with_unverified_user" time="0.010"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCD670&gt;

    @mock_aws
    def test_forgot_password_with_unverified_user(self):
        """Test forgot password with unverified user."""
    
        # Register user but don't verify
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_verify_email:

tests\e2e\auth\test_password_reset_flow.py:233: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_reset_password_with_invalid_token" time="0.010"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCD7F0&gt;

    @mock_aws
    def test_reset_password_with_invalid_token(self):
        """Test password reset with invalid token."""
    
        reset_event = {
            'httpMethod': 'POST',
            'path': '/auth/reset-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'token': 'invalid-reset-token-123',
                'new_password': self.new_password
            }),
            'requestContext': {
                'requestId': 'test-reset-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
&gt;       response = reset_password_handler(reset_event, {})

tests\e2e\auth\test_password_reset_flow.py:283: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\reset_password.py:54: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x0000022E985D7E90&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_reset_password_with_expired_token" time="0.110"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCD970&gt;

    @mock_aws
    def test_reset_password_with_expired_token(self):
        """Test password reset with expired token."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_password_reset_flow.py:297: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_password_reset_flow.py:54: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_reset_password_with_weak_password" time="0.007"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCDBB0&gt;

    @mock_aws
    def test_reset_password_with_weak_password(self):
        """Test password reset with weak new password."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_password_reset_flow.py:352: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_password_reset_flow.py:54: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_reset_password_token_single_use" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCDD90&gt;

    @mock_aws
    def test_reset_password_token_single_use(self):
        """Test that reset tokens can only be used once."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_password_reset_flow.py:409: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_password_reset_flow.py:54: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_password_reset_flow.TestPasswordResetFlow" name="test_forgot_password_rate_limiting" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_password_reset_flow.TestPasswordResetFlow object at 0x0000022E98DCDF70&gt;

    @mock_aws
    def test_forgot_password_rate_limiting(self):
        """Test rate limiting for forgot password requests."""
    
        # Create verified user
&gt;       self._create_verified_user()

tests\e2e\auth\test_password_reset_flow.py:470: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_password_reset_flow.py:54: in _create_verified_user
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_complete_registration_flow" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCD3A0&gt;

    @mock_aws
    def test_complete_registration_flow(self):
        """Test complete registration flow from start to verification."""
    
        # Step 1: Register new user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:

tests\e2e\auth\test_registration_flow.py:58: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_registration_with_duplicate_email" time="0.009"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCF620&gt;

    @mock_aws
    def test_registration_with_duplicate_email(self):
        """Test registration with duplicate email address."""
    
        # First registration
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:

tests\e2e\auth\test_registration_flow.py:157: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_registration_with_invalid_data" time="0.011"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCF800&gt;

    @mock_aws
    def test_registration_with_invalid_data(self):
        """Test registration with various invalid data scenarios."""
    
        test_cases = [
            # Invalid email
            {
                'data': {
                    'email': 'invalid-email',
                    'password': self.test_password,
                    'name': self.test_name,
                    'company': self.test_company
                },
                'expected_error': 'email'
            },
            # Weak password
            {
                'data': {
                    'email': self.test_email,
                    'password': '123',
                    'name': self.test_name,
                    'company': self.test_company
                },
                'expected_error': 'password'
            },
            # Missing name
            {
                'data': {
                    'email': self.test_email,
                    'password': self.test_password,
                    'company': self.test_company
                },
                'expected_error': 'name'
            },
            # Missing company
            {
                'data': {
                    'email': self.test_email,
                    'password': self.test_password,
                    'name': self.test_name
                },
                'expected_error': 'company'
            }
        ]
    
        for test_case in test_cases:
            register_event = {
                'httpMethod': 'POST',
                'path': '/auth/register',
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps(test_case['data']),
                'requestContext': {
                    'requestId': 'test-request-123',
                    'identity': {
                        'sourceIp': '127.0.0.1'
                    }
                }
            }
    
&gt;           response = register_handler(register_event, {})

tests\e2e\auth\test_registration_flow.py:233: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\register.py:55: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x0000022E985D7E90&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_email_verification_with_invalid_token" time="0.010"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCF9E0&gt;

    @mock_aws
    def test_email_verification_with_invalid_token(self):
        """Test email verification with invalid token."""
    
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'token': 'invalid-token-123'
            }),
            'requestContext': {
                'requestId': 'test-verify-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
&gt;       response = verify_email_handler(verify_event, {})

tests\e2e\auth\test_registration_flow.py:268: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\verify_email.py:51: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x0000022E985D7E90&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_email_verification_with_expired_token" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCFBC0&gt;

    @mock_aws
    def test_email_verification_with_expired_token(self):
        """Test email verification with expired token."""
    
        # First register a user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:

tests\e2e\auth\test_registration_flow.py:302: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_registration_tenant_isolation" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCFDA0&gt;

    @mock_aws
    def test_registration_tenant_isolation(self):
        """Test that tenant isolation works correctly during registration."""
    
        # Register first user/tenant
        register_event1 = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': '<EMAIL>',
                'password': self.test_password,
                'name': 'User One',
                'company': 'Company One'
            }),
            'requestContext': {
                'requestId': 'test-request-1',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
        # Register second user/tenant
        register_event2 = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': '<EMAIL>',
                'password': self.test_password,
                'name': 'User Two',
                'company': 'Company Two'
            }),
            'requestContext': {
                'requestId': 'test-request-2',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:

tests\e2e\auth\test_registration_flow.py:386: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_registration_flow.TestRegistrationFlow" name="test_registration_rate_limiting" time="0.010"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_registration_flow.TestRegistrationFlow object at 0x0000022E98DCFF80&gt;

    @mock_aws
    def test_registration_rate_limiting(self):
        """Test rate limiting during registration."""
    
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
&gt;       with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:

tests\e2e\auth\test_registration_flow.py:438: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_tenant_data_isolation" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E01BE0&gt;

    @mock_aws
    def test_tenant_data_isolation(self):
        """Test that tenant data is properly isolated."""
    
        # Create two separate tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:112: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_token_tenant_isolation" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E02060&gt;

    @mock_aws
    def test_token_tenant_isolation(self):
        """Test that JWT tokens contain correct tenant information."""
    
        # Create two tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:142: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_authorizer_tenant_isolation" time="0.010"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E02240&gt;

    @mock_aws
    def test_authorizer_tenant_isolation(self):
        """Test that API Gateway authorizer enforces tenant isolation."""
    
        # Create two tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:166: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_cross_tenant_token_rejection" time="0.010"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E02420&gt;

    @mock_aws
    def test_cross_tenant_token_rejection(self):
        """Test that tokens from one tenant cannot access another tenant's resources."""
    
        # Create two tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:217: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_database_tenant_isolation" time="0.010"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E02600&gt;

    @mock_aws
    def test_database_tenant_isolation(self):
        """Test that database queries are properly scoped to tenant."""
    
        # Create two tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:261: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_tenant_user_roles_isolation" time="0.009"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E027E0&gt;

    @mock_aws
    def test_tenant_user_roles_isolation(self):
        """Test that user roles are properly isolated per tenant."""
    
        # Create two tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:296: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.auth.test_tenant_isolation.TestTenantIsolation" name="test_tenant_resource_naming_isolation" time="0.009"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;auth.test_tenant_isolation.TestTenantIsolation object at 0x0000022E98E029C0&gt;

    @mock_aws
    def test_tenant_resource_naming_isolation(self):
        """Test that tenant resources are properly named and isolated."""
    
        # Create two tenants
&gt;       tenant1 = self._create_verified_tenant(self.tenant1_data)

tests\e2e\auth\test_tenant_isolation.py:343: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\auth\test_tenant_isolation.py:58: in _create_verified_tenant
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_complete_subscription_lifecycle" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD06FF0&gt;

    @mock_aws
    def test_complete_subscription_lifecycle(self):
        """Test complete subscription lifecycle from creation to cancellation."""
    
        # Setup authenticated tenant
&gt;       tenant_data = self._create_verified_tenant_with_auth()

tests\e2e\payment\test_subscription_lifecycle.py:122: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\payment\test_subscription_lifecycle.py:57: in _create_verified_tenant_with_auth
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_subscription_plan_upgrade" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD35FD0&gt;

    @mock_aws
    def test_subscription_plan_upgrade(self):
        """Test subscription plan upgrade flow."""
    
        # Setup authenticated tenant
&gt;       tenant_data = self._create_verified_tenant_with_auth()

tests\e2e\payment\test_subscription_lifecycle.py:275: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\payment\test_subscription_lifecycle.py:57: in _create_verified_tenant_with_auth
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_subscription_without_payment_method" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD361B0&gt;

    @mock_aws
    def test_subscription_without_payment_method(self):
        """Test subscription creation without payment method (trial only)."""
    
        # Setup authenticated tenant
&gt;       tenant_data = self._create_verified_tenant_with_auth()

tests\e2e\payment\test_subscription_lifecycle.py:355: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\payment\test_subscription_lifecycle.py:57: in _create_verified_tenant_with_auth
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_subscription_with_invalid_plan" time="0.009"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD36390&gt;

    @mock_aws
    def test_subscription_with_invalid_plan(self):
        """Test subscription creation with invalid plan ID."""
    
        # Setup authenticated tenant
&gt;       tenant_data = self._create_verified_tenant_with_auth()

tests\e2e\payment\test_subscription_lifecycle.py:409: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\payment\test_subscription_lifecycle.py:57: in _create_verified_tenant_with_auth
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_subscription_unauthorized_user" time="0.009"><failure message="assert 401 == 403">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD36570&gt;

    @mock_aws
    def test_subscription_unauthorized_user(self):
        """Test subscription operations with unauthorized user."""
    
        # Create auth context for MEMBER user (not MASTER)
        auth_context = Mock()
        auth_context.tenant_id = 'tenant-123'
        auth_context.user_id = 'user-456'
        auth_context.email = '<EMAIL>'
        auth_context.role = 'MEMBER'
        auth_context.is_master.return_value = False
        auth_context.is_member.return_value = True
    
        # Try to create subscription as MEMBER
        create_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': 'plan-basic',
                'billing_interval': 'MONTHLY'
            }),
            'auth_context': auth_context,
            'requestContext': {
                'requestId': 'test-create-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
        response = create_subscription_handler(create_subscription_event, {})
    
        # Should return authorization error
&gt;       assert response['statusCode'] == 403
E       assert 401 == 403

tests\e2e\payment\test_subscription_lifecycle.py:468: AssertionError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_get_subscription_without_subscription" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD36750&gt;

    @mock_aws
    def test_get_subscription_without_subscription(self):
        """Test getting subscription when none exists."""
    
        # Setup authenticated tenant
&gt;       tenant_data = self._create_verified_tenant_with_auth()

tests\e2e\payment\test_subscription_lifecycle.py:479: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\payment\test_subscription_lifecycle.py:57: in _create_verified_tenant_with_auth
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_subscription_lifecycle.TestSubscriptionLifecycle" name="test_cancel_nonexistent_subscription" time="0.008"><failure message="AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?">self = &lt;payment.test_subscription_lifecycle.TestSubscriptionLifecycle object at 0x0000022E9BD36930&gt;

    @mock_aws
    def test_cancel_nonexistent_subscription(self):
        """Test cancelling subscription when none exists."""
    
        # Setup authenticated tenant
&gt;       tenant_data = self._create_verified_tenant_with_auth()

tests\e2e\payment\test_subscription_lifecycle.py:509: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests\e2e\payment\test_subscription_lifecycle.py:57: in _create_verified_tenant_with_auth
    with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
C:\Python312\Lib\unittest\mock.py:1445: in __enter__
    self.target = self.getter()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

name = 'src.auth.services.email_service.email_service'

    def resolve_name(name):
        """
        Resolve a name to an object.
    
        It is expected that `name` will be a string in one of the following
        formats, where W is shorthand for a valid Python identifier and dot stands
        for a literal period in these pseudo-regexes:
    
        W(.W)*
        W(.W)*:(W(.W)*)?
    
        The first form is intended for backward compatibility only. It assumes that
        some part of the dotted name is a package, and the rest is an object
        somewhere within that package, possibly nested inside other objects.
        Because the place where the package stops and the object hierarchy starts
        can't be inferred by inspection, repeated attempts to import must be done
        with this form.
    
        In the second form, the caller makes the division point clear through the
        provision of a single colon: the dotted name to the left of the colon is a
        package to be imported, and the dotted name to the right is the object
        hierarchy within that package. Only one import is needed in this form. If
        it ends with the colon, then a module object is returned.
    
        The function will return an object (which might be a module), or raise one
        of the following exceptions:
    
        ValueError - if `name` isn't in a recognised format
        ImportError - if an import failed when it shouldn't have
        AttributeError - if a failure occurred when traversing the object hierarchy
                         within the imported package to get to the desired object.
        """
        global _NAME_PATTERN
        if _NAME_PATTERN is None:
            # Lazy import to speedup Python startup time
            import re
            dotted_words = r'(?!\d)(\w+)(\.(?!\d)(\w+))*'
            _NAME_PATTERN = re.compile(f'^(?P&lt;pkg&gt;{dotted_words})'
                                       f'(?P&lt;cln&gt;:(?P&lt;obj&gt;{dotted_words})?)?$',
                                       re.UNICODE)
    
        m = _NAME_PATTERN.match(name)
        if not m:
            raise ValueError(f'invalid format: {name!r}')
        gd = m.groupdict()
        if gd.get('cln'):
            # there is a colon - a one-step import is all that's needed
            mod = importlib.import_module(gd['pkg'])
            parts = gd.get('obj')
            parts = parts.split('.') if parts else []
        else:
            # no colon - have to iterate to find the package boundary
            parts = name.split('.')
            modname = parts.pop(0)
            # first part *must* be a module/package.
            mod = importlib.import_module(modname)
            while parts:
                p = parts[0]
                s = f'{modname}.{p}'
                try:
                    mod = importlib.import_module(s)
                    parts.pop(0)
                    modname = s
                except ImportError:
                    break
        # if we reach this point, mod is the module, already imported, and
        # parts is the list of parts in the object hierarchy to be traversed, or
        # an empty list if just the module is wanted.
        result = mod
        for p in parts:
&gt;           result = getattr(result, p)
E           AttributeError: module 'src.auth.services.email_service' has no attribute 'email_service'. Did you mean: 'EmailService'?

C:\Python312\Lib\pkgutil.py:528: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_webhook_processing.TestWebhookProcessing" name="test_subscription_created_webhook" time="0.009"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;payment.test_webhook_processing.TestWebhookProcessing object at 0x0000022E9BD76210&gt;

    @mock_aws
    def test_subscription_created_webhook(self):
        """Test processing of subscription.created webhook."""
    
        # Create subscription data
        subscription_data = {
            'id': self.stripe_subscription_id,
            'object': 'subscription',
            'status': 'trialing',
            'customer': 'cus_test123',
            'current_period_start': int(time.time()),
            'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
            'trial_end': int(time.time()) + (14 * 24 * 60 * 60),
            'cancel_at_period_end': False,
            'items': {
                'data': [{
                    'price': {
                        'id': 'price_test123',
                        'unit_amount': 2999
                    }
                }]
            }
        }
    
        # Create webhook event
        webhook_event = self._create_webhook_event('customer.subscription.created', subscription_data)
    
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
    
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
    
&gt;               response = webhook_handler(webhook_event, {})

tests\e2e\payment\test_webhook_processing.py:116: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\payment\handlers\stripe_webhook.py:46: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x0000022E985D7E90&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.e2e.payment.test_webhook_processing.TestWebhookProcessing" name="test_subscription_updated_webhook" time="66.443"><failure message="botocore.exceptions.EndpointConnectionError: Could not connect to the endpoint URL: &quot;http://localhost:8000/&quot;">self = &lt;botocore.awsrequest.AWSHTTPConnection object at 0x0000022E9BF9C5F0&gt;

    def _new_conn(self) -&gt; socket.socket:
        """Establish a socket connection and set nodelay settings on it.
    
        :return: New socket connection.
        """
        try:
&gt;           sock = connection.create_connection(
                (self._dns_host, self.port),
                self.timeout,
                source_address=self.source_address,
                socket_options=self.socket_options,
            )

..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connection.py:198: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\util\connection.py:85: in create_connection
    raise err
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

address = ('localhost', 8000), timeout = 60, source_address = None
socket_options = [(6, 1, 1)]

    def create_connection(
        address: tuple[str, int],
        timeout: _TYPE_TIMEOUT = _DEFAULT_TIMEOUT,
        source_address: tuple[str, int] | None = None,
        socket_options: _TYPE_SOCKET_OPTIONS | None = None,
    ) -&gt; socket.socket:
        """Connect to *address* and return the socket object.
    
        Convenience function.  Connect to *address* (a 2-tuple ``(host,
        port)``) and return the socket object.  Passing the optional
        *timeout* parameter will set the timeout on the socket instance
        before attempting to connect.  If no *timeout* is supplied, the
        global default timeout setting returned by :func:`socket.getdefaulttimeout`
        is used.  If *source_address* is set it must be a tuple of (host, port)
        for the socket to bind as a source address before making the connection.
        An host of '' or port 0 tells the OS to use the default.
        """
    
        host, port = address
        if host.startswith("["):
            host = host.strip("[]")
        err = None
    
        # Using the value from allowed_gai_family() in the context of getaddrinfo lets
        # us select whether to work with IPv4 DNS records, IPv6 records, or both.
        # The original create_connection function always returns all records.
        family = allowed_gai_family()
    
        try:
            host.encode("idna")
        except UnicodeError:
            raise LocationParseError(f"'{host}', label empty or too long") from None
    
        for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
            af, socktype, proto, canonname, sa = res
            sock = None
            try:
                sock = socket.socket(af, socktype, proto)
    
                # If provided, set socket level options before connecting.
                _set_socket_options(sock, socket_options)
    
                if timeout is not _DEFAULT_TIMEOUT:
                    sock.settimeout(timeout)
                if source_address:
                    sock.bind(source_address)
&gt;               sock.connect(sa)
E               ConnectionRefusedError: [WinError 10061] No se puede establecer una conexión ya que el equipo de destino denegó expresamente dicha conexión

..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\util\connection.py:73: ConnectionRefusedError

The above exception was the direct cause of the following exception:

self = &lt;botocore.httpsession.URLLib3Session object at 0x0000022E98D07620&gt;
request = &lt;AWSPreparedRequest stream_output=False, method=POST, url=http://localhost:8000/, headers={'X-Amz-Target': b'DynamoDB_...tion-id': b'31e904db-6ac7-4bd7-9cbb-d61425edacd7', 'amz-sdk-request': b'attempt=10; max=10', 'Content-Length': '1010'}&gt;

    def send(self, request):
        try:
            proxy_url = self._proxy_config.proxy_url_for(request.url)
            manager = self._get_connection_manager(request.url, proxy_url)
            conn = manager.connection_from_url(request.url)
            self._setup_ssl_cert(conn, request.url, self._verify)
            if ensure_boolean(
                os.environ.get('BOTO_EXPERIMENTAL__ADD_PROXY_HOST_HEADER', '')
            ):
                # This is currently an "experimental" feature which provides
                # no guarantees of backwards compatibility. It may be subject
                # to change or removal in any patch version. Anyone opting in
                # to this feature should strictly pin botocore.
                host = urlparse(request.url).hostname
                conn.proxy_headers['host'] = host
    
            request_target = self._get_request_target(request.url, proxy_url)
&gt;           urllib_response = conn.urlopen(
                method=request.method,
                url=request_target,
                body=request.body,
                headers=request.headers,
                retries=Retry(False),
                assert_same_host=False,
                preload_content=False,
                decode_content=False,
                chunked=self._chunked(request.headers),
            )

..\..\AppData\Roaming\Python\Python312\site-packages\botocore\httpsession.py:464: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connectionpool.py:841: in urlopen
    retries = retries.increment(
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\util\retry.py:449: in increment
    raise reraise(type(error), error, _stacktrace)
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\util\util.py:39: in reraise
    raise value
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connectionpool.py:787: in urlopen
    response = self._make_request(
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connectionpool.py:493: in _make_request
    conn.request(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\awsrequest.py:96: in request
    rval = super().request(method, url, body, headers, *args, **kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connection.py:494: in request
    self.endheaders()
C:\Python312\Lib\http\client.py:1331: in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\awsrequest.py:123: in _send_output
    self.send(msg)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\awsrequest.py:223: in send
    return super().send(str)
C:\Python312\Lib\http\client.py:1035: in send
    self.connect()
..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connection.py:325: in connect
    self.sock = self._new_conn()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;botocore.awsrequest.AWSHTTPConnection object at 0x0000022E9BF9C5F0&gt;

    def _new_conn(self) -&gt; socket.socket:
        """Establish a socket connection and set nodelay settings on it.
    
        :return: New socket connection.
        """
        try:
            sock = connection.create_connection(
                (self._dns_host, self.port),
                self.timeout,
                source_address=self.source_address,
                socket_options=self.socket_options,
            )
        except socket.gaierror as e:
            raise NameResolutionError(self.host, self, e) from e
        except SocketTimeout as e:
            raise ConnectTimeoutError(
                self,
                f"Connection to {self.host} timed out. (connect timeout={self.timeout})",
            ) from e
    
        except OSError as e:
&gt;           raise NewConnectionError(
                self, f"Failed to establish a new connection: {e}"
            ) from e
E           urllib3.exceptions.NewConnectionError: &lt;botocore.awsrequest.AWSHTTPConnection object at 0x0000022E9BF9C5F0&gt;: Failed to establish a new connection: [WinError 10061] No se puede establecer una conexión ya que el equipo de destino denegó expresamente dicha conexión

..\..\AppData\Roaming\Python\Python312\site-packages\urllib3\connection.py:213: NewConnectionError

During handling of the above exception, another exception occurred:

self = &lt;payment.test_webhook_processing.TestWebhookProcessing object at 0x0000022E9BD763F0&gt;

    @mock_aws
    def test_subscription_updated_webhook(self):
        """Test processing of subscription.updated webhook."""
    
        # First create a subscription in our system
        subscription = Subscription(
            tenant_id=self.tenant_id,
            plan_id='plan-basic',
            stripe_subscription_id=self.stripe_subscription_id,
            status=SubscriptionStatus.TRIAL
        )
&gt;       subscription.save()

tests\e2e\payment\test_webhook_processing.py:137: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\payment\models\subscription.py:125: in save
    db_client.put_item(subscription_data, self.tenant_id)
src\shared\database.py:139: in put_item
    self.table.put_item(**put_kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\boto3\resources\factory.py:581: in do_action
    response = action(self, *args, **kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\boto3\resources\action.py:88: in __call__
    response = getattr(parent.meta.client, operation_name)(*args, **params)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\client.py:565: in _api_call
    return self._make_api_call(operation_name, kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\client.py:1001: in _make_api_call
    http, parsed_response = self._make_request(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\client.py:1027: in _make_request
    return self._endpoint.make_request(operation_model, request_dict)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\endpoint.py:119: in make_request
    return self._send_request(request_dict, operation_model)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\endpoint.py:202: in _send_request
    while self._needs_retry(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\endpoint.py:354: in _needs_retry
    responses = self._event_emitter.emit(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\hooks.py:412: in emit
    return self._emitter.emit(aliased_event_name, **kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\hooks.py:256: in emit
    return self._emit(event_name, kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\hooks.py:239: in _emit
    response = handler(**kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\retryhandler.py:207: in __call__
    if self._checker(**checker_kwargs):
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\retryhandler.py:284: in __call__
    should_retry = self._should_retry(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\retryhandler.py:320: in _should_retry
    return self._checker(attempt_number, response, caught_exception)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\retryhandler.py:363: in __call__
    checker_response = checker(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\retryhandler.py:247: in __call__
    return self._check_caught_exception(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\retryhandler.py:416: in _check_caught_exception
    raise caught_exception
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\endpoint.py:281: in _do_get_response
    http_response = self._send(request)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\endpoint.py:377: in _send
    return self.http_session.send(request)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;botocore.httpsession.URLLib3Session object at 0x0000022E98D07620&gt;
request = &lt;AWSPreparedRequest stream_output=False, method=POST, url=http://localhost:8000/, headers={'X-Amz-Target': b'DynamoDB_...tion-id': b'31e904db-6ac7-4bd7-9cbb-d61425edacd7', 'amz-sdk-request': b'attempt=10; max=10', 'Content-Length': '1010'}&gt;

    def send(self, request):
        try:
            proxy_url = self._proxy_config.proxy_url_for(request.url)
            manager = self._get_connection_manager(request.url, proxy_url)
            conn = manager.connection_from_url(request.url)
            self._setup_ssl_cert(conn, request.url, self._verify)
            if ensure_boolean(
                os.environ.get('BOTO_EXPERIMENTAL__ADD_PROXY_HOST_HEADER', '')
            ):
                # This is currently an "experimental" feature which provides
                # no guarantees of backwards compatibility. It may be subject
                # to change or removal in any patch version. Anyone opting in
                # to this feature should strictly pin botocore.
                host = urlparse(request.url).hostname
                conn.proxy_headers['host'] = host
    
            request_target = self._get_request_target(request.url, proxy_url)
            urllib_response = conn.urlopen(
                method=request.method,
                url=request_target,
                body=request.body,
                headers=request.headers,
                retries=Retry(False),
                assert_same_host=False,
                preload_content=False,
                decode_content=False,
                chunked=self._chunked(request.headers),
            )
    
            http_response = botocore.awsrequest.AWSResponse(
                request.url,
                urllib_response.status,
                urllib_response.headers,
                urllib_response,
            )
    
            if not request.stream_output:
                # Cause the raw stream to be exhausted immediately. We do it
                # this way instead of using preload_content because
                # preload_content will never buffer chunked responses
                http_response.content
    
            return http_response
        except URLLib3SSLError as e:
            raise SSLError(endpoint_url=request.url, error=e)
        except (NewConnectionError, socket.gaierror) as e:
&gt;           raise EndpointConnectionError(endpoint_url=request.url, error=e)
E           botocore.exceptions.EndpointConnectionError: Could not connect to the endpoint URL: "http://localhost:8000/"

..\..\AppData\Roaming\Python\Python312\site-packages\botocore\httpsession.py:493: EndpointConnectionError</failure></testcase><testcase time="0.001" /></testsuite></testsuites>