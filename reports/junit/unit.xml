<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="26" failures="34" skipped="20" tests="277" time="19.650" timestamp="2025-07-29T15:26:29.846595" hostname="ANDERSON-APES-DESKTOP"><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_creation" time="0.005" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_to_dict" time="0.002" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_is_active" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_validation_invalid_name" time="0.002" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_validation_valid_name" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_validation_invalid_billing_email" time="0.002" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_validation_valid_billing_email" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_activate" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_suspend" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_deactivate" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_update_settings" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_update_settings_partial" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_set_subscription" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_remove_subscription" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_has_subscription" time="0.001" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_save" time="0.012" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_get_by_id" time="0.007" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_get_by_name" time="0.007" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_get_by_name_not_found" time="0.007" /><testcase classname="tests.unit.auth.models.test_tenant.TestTenant" name="test_tenant_slug_generation" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_creation" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_to_dict" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_is_active" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_role_permissions" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_validation_invalid_phone" time="0.002" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_validation_valid_phone" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_to_dict_exclude_sensitive" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_update_last_login" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_verify_email" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_activate" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_deactivate" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_suspend" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_can_login" time="0.001" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_save" time="0.007" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_get_by_id" time="0.006" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_get_by_email" time="0.007" /><testcase classname="tests.unit.auth.models.test_user.TestUser" name="test_user_get_by_email_not_found" time="0.006" /><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_init_creates_ses_client" time="0.036"><failure message="AssertionError: expected call not found.&#10;Expected: client('ses')&#10;  Actual: client('ses', region_name='us-east-1')&#10;&#10;pytest introspection follows:&#10;&#10;Kwargs:&#10;assert {'region_name': 'us-east-1'} == {}&#10;  &#10;  Left contains 1 more item:&#10;  {'region_name': 'us-east-1'}&#10;  Use -v to get more diff">self = &lt;MagicMock name='client' id='1916430611952'&gt;, args = ('ses',)
kwargs = {}, expected = call('ses')
actual = call('ses', region_name='us-east-1')
_error_message = &lt;function NonCallableMock.assert_called_with.&lt;locals&gt;._error_message at 0x000001BE342E4A40&gt;
cause = None

    def assert_called_with(self, /, *args, **kwargs):
        """assert that the last call was made with the specified arguments.
    
        Raises an AssertionError if the args and keyword args passed in are
        different to the last call to the mock."""
        if self.call_args is None:
            expected = self._format_mock_call_signature(args, kwargs)
            actual = 'not called.'
            error_message = ('expected call not found.\nExpected: %s\n  Actual: %s'
                    % (expected, actual))
            raise AssertionError(error_message)
    
        def _error_message():
            msg = self._format_mock_failure_message(args, kwargs)
            return msg
        expected = self._call_matcher(_Call((args, kwargs), two=True))
        actual = self._call_matcher(self.call_args)
        if actual != expected:
            cause = expected if isinstance(expected, Exception) else None
&gt;           raise AssertionError(_error_message()) from cause
E           AssertionError: expected call not found.
E           Expected: client('ses')
E             Actual: client('ses', region_name='us-east-1')

C:\Python312\Lib\unittest\mock.py:947: AssertionError

During handling of the above exception, another exception occurred:

self = &lt;MagicMock name='client' id='1916430611952'&gt;, args = ('ses',)
kwargs = {}

    def assert_called_once_with(self, /, *args, **kwargs):
        """assert that the mock was called exactly once and that that call was
        with the specified arguments."""
        if not self.call_count == 1:
            msg = ("Expected '%s' to be called once. Called %s times.%s"
                   % (self._mock_name or 'mock',
                      self.call_count,
                      self._calls_repr()))
            raise AssertionError(msg)
&gt;       return self.assert_called_with(*args, **kwargs)
E       AssertionError: expected call not found.
E       Expected: client('ses')
E         Actual: client('ses', region_name='us-east-1')
E       
E       pytest introspection follows:
E       
E       Kwargs:
E       assert {'region_name': 'us-east-1'} == {}
E         
E         Left contains 1 more item:
E         {'region_name': 'us-east-1'}
E         Use -v to get more diff

C:\Python312\Lib\unittest\mock.py:959: AssertionError

During handling of the above exception, another exception occurred:

self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118A690&gt;
mock_boto3_client = &lt;MagicMock name='client' id='1916430611952'&gt;

    @patch('src.auth.services.email_service.boto3.client')
    def test_init_creates_ses_client(self, mock_boto3_client):
        """Test EmailService initialization creates SES client."""
        service = EmailService()
&gt;       mock_boto3_client.assert_called_once_with('ses')
E       AssertionError: expected call not found.
E       Expected: client('ses')
E         Actual: client('ses', region_name='us-east-1')
E       
E       pytest introspection follows:
E       
E       Kwargs:
E       assert {'region_name': 'us-east-1'} == {}
E         
E         Left contains 1 more item:
E         {'region_name': 'us-east-1'}
E         Use -v to get more diff

tests\unit\auth\services\test_email_service.py:27: AssertionError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_render_template_verification_email" time="0.008"><failure message="AttributeError: 'EmailService' object has no attribute '_render_template'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118A870&gt;

    def test_render_template_verification_email(self):
        """Test rendering verification email template."""
&gt;       result = self.email_service._render_template(
            'verification',
            {
                'user_name': 'John Doe',
                'verification_code': '123456',
                'company_name': 'Test Company'
            }
        )
E       AttributeError: 'EmailService' object has no attribute '_render_template'

tests\unit\auth\services\test_email_service.py:32: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_render_template_welcome_email" time="0.007"><failure message="AttributeError: 'EmailService' object has no attribute '_render_template'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118AA50&gt;

    def test_render_template_welcome_email(self):
        """Test rendering welcome email template."""
&gt;       result = self.email_service._render_template(
            'welcome',
            {
                'user_name': 'Jane Smith',
                'company_name': 'Test Corp',
                'login_url': 'https://app.test.com/login'
            }
        )
E       AttributeError: 'EmailService' object has no attribute '_render_template'

tests\unit\auth\services\test_email_service.py:49: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_render_template_password_reset" time="0.008"><failure message="AttributeError: 'EmailService' object has no attribute '_render_template'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118AC30&gt;

    def test_render_template_password_reset(self):
        """Test rendering password reset email template."""
&gt;       result = self.email_service._render_template(
            'password_reset',
            {
                'user_name': 'Bob Wilson',
                'reset_code': 'ABC123',
                'company_name': 'Test Inc'
            }
        )
E       AttributeError: 'EmailService' object has no attribute '_render_template'

tests\unit\auth\services\test_email_service.py:65: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_render_template_invitation" time="0.007"><failure message="AttributeError: 'EmailService' object has no attribute '_render_template'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118AE10&gt;

    def test_render_template_invitation(self):
        """Test rendering invitation email template."""
&gt;       result = self.email_service._render_template(
            'invitation',
            {
                'invitee_name': 'Alice Johnson',
                'inviter_name': 'John Admin',
                'company_name': 'Test LLC',
                'invitation_url': 'https://app.test.com/accept/xyz'
            }
        )
E       AttributeError: 'EmailService' object has no attribute '_render_template'

tests\unit\auth\services\test_email_service.py:81: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_render_template_invalid_template" time="0.007"><failure message="AttributeError: 'EmailService' object has no attribute '_render_template'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118AFF0&gt;

    def test_render_template_invalid_template(self):
        """Test rendering with invalid template raises exception."""
        with pytest.raises(EmailException) as exc_info:
&gt;           self.email_service._render_template('invalid_template', {})
E           AttributeError: 'EmailService' object has no attribute '_render_template'

tests\unit\auth\services\test_email_service.py:100: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_email_success" time="0.007"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_email_ses_error" time="0.008"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_verification_email" time="0.007"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_welcome_email" time="0.006"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_password_reset_email" time="0.006"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_invitation_email" time="0.006"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_validate_email_address_valid" time="0.108"><failure message="AttributeError: 'EmailService' object has no attribute '_validate_email_address'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118B6B0&gt;

    def test_validate_email_address_valid(self):
        """Test email address validation with valid emails."""
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
    
        for email in valid_emails:
&gt;           assert self.email_service._validate_email_address(email) is True
E           AttributeError: 'EmailService' object has no attribute '_validate_email_address'

tests\unit\auth\services\test_email_service.py:263: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_validate_email_address_invalid" time="0.006"><failure message="AttributeError: 'EmailService' object has no attribute '_validate_email_address'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118B890&gt;

    def test_validate_email_address_invalid(self):
        """Test email address validation with invalid emails."""
        invalid_emails = [
            'invalid-email',
            '@domain.com',
            'user@',
            '<EMAIL>',
            'user@domain',
            ''
        ]
    
        for email in invalid_emails:
&gt;           assert self.email_service._validate_email_address(email) is False
E           AttributeError: 'EmailService' object has no attribute '_validate_email_address'

tests\unit\auth\services\test_email_service.py:277: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_email_invalid_email_address" time="0.006"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_get_template_data_keys_verification" time="0.006"><failure message="AttributeError: 'EmailService' object has no attribute '_get_template_data_keys'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118BC50&gt;

    def test_get_template_data_keys_verification(self):
        """Test getting required template data keys for verification."""
&gt;       keys = self.email_service._get_template_data_keys('verification')
E       AttributeError: 'EmailService' object has no attribute '_get_template_data_keys'

tests\unit\auth\services\test_email_service.py:292: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_get_template_data_keys_welcome" time="0.008"><failure message="AttributeError: 'EmailService' object has no attribute '_get_template_data_keys'">self = &lt;services.test_email_service.TestEmailService object at 0x000001BE3118BE30&gt;

    def test_get_template_data_keys_welcome(self):
        """Test getting required template data keys for welcome."""
&gt;       keys = self.email_service._get_template_data_keys('welcome')
E       AttributeError: 'EmailService' object has no attribute '_get_template_data_keys'

tests\unit\auth\services\test_email_service.py:298: AttributeError</failure></testcase><testcase classname="tests.unit.auth.services.test_email_service.TestEmailService" name="test_send_email_missing_template_data" time="0.006"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_hash_password" time="0.276" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_hash_password_different_salts" time="0.494" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_verify_password_correct" time="0.466" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_verify_password_incorrect" time="0.473" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_verify_password_case_sensitive" time="1.192" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_strength_weak" time="0.002" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_strength_strong" time="0.002"><failure message="src.shared.exceptions.ValidationException: Password contains common pattern: password">self = &lt;services.test_password_service.TestPasswordService object at 0x000001BE311B1610&gt;

    def test_validate_password_strength_strong(self):
        """Test password strength validation with strong passwords."""
        for strong_password in self.strong_passwords:
            # Should not raise exception
&gt;           self.password_service.validate_password_strength(strong_password)

tests\unit\auth\services\test_password_service.py:105: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.auth.services.password_service.PasswordService object at 0x000001BE345C7290&gt;
password = 'TestPassword123!'

    def validate_password_strength(self, password: str) -&gt; None:
        """
        Validate password strength according to security requirements.
    
        Args:
            password: Password to validate
    
        Raises:
            ValidationException: If password doesn't meet requirements
        """
        if not password:
            raise ValidationException("Password is required")
    
        # Length check
        if len(password) &lt; self.min_length:
            raise ValidationException(f"Password must be at least {self.min_length} characters long")
    
        if len(password) &gt; self.max_length:
            raise ValidationException(f"Password must be no more than {self.max_length} characters long")
    
        # Character requirements
        if not re.search(r'[A-Z]', password):
            raise ValidationException("Password must contain at least one uppercase letter")
    
        if not re.search(r'[a-z]', password):
            raise ValidationException("Password must contain at least one lowercase letter")
    
        if not re.search(r'\d', password):
            raise ValidationException("Password must contain at least one digit")
    
        if not re.search(r'[!@#$%^&amp;*(),.?":{}|&lt;&gt;]', password):
            raise ValidationException("Password must contain at least one special character")
    
        # Check for common patterns
        password_lower = password.lower()
        for pattern in self.common_patterns:
            if pattern in password_lower:
&gt;               raise ValidationException(f"Password contains common pattern: {pattern}")
E               src.shared.exceptions.ValidationException: Password contains common pattern: password

src\auth\services\password_service.py:119: ValidationException</failure></testcase><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_length_requirements" time="0.001"><failure message="src.shared.exceptions.ValidationException: Password contains common pattern: password">self = &lt;services.test_password_service.TestPasswordService object at 0x000001BE311B17F0&gt;

    def test_validate_password_length_requirements(self):
        """Test password length requirements."""
        # Too short
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("Abc1!")
    
        # Too long
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("A" * 129 + "bc1!")
    
        # Just right
&gt;       self.password_service.validate_password_strength("TestPassword123!")

tests\unit\auth\services\test_password_service.py:118: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.auth.services.password_service.PasswordService object at 0x000001BE345C7B00&gt;
password = 'TestPassword123!'

    def validate_password_strength(self, password: str) -&gt; None:
        """
        Validate password strength according to security requirements.
    
        Args:
            password: Password to validate
    
        Raises:
            ValidationException: If password doesn't meet requirements
        """
        if not password:
            raise ValidationException("Password is required")
    
        # Length check
        if len(password) &lt; self.min_length:
            raise ValidationException(f"Password must be at least {self.min_length} characters long")
    
        if len(password) &gt; self.max_length:
            raise ValidationException(f"Password must be no more than {self.max_length} characters long")
    
        # Character requirements
        if not re.search(r'[A-Z]', password):
            raise ValidationException("Password must contain at least one uppercase letter")
    
        if not re.search(r'[a-z]', password):
            raise ValidationException("Password must contain at least one lowercase letter")
    
        if not re.search(r'\d', password):
            raise ValidationException("Password must contain at least one digit")
    
        if not re.search(r'[!@#$%^&amp;*(),.?":{}|&lt;&gt;]', password):
            raise ValidationException("Password must contain at least one special character")
    
        # Check for common patterns
        password_lower = password.lower()
        for pattern in self.common_patterns:
            if pattern in password_lower:
&gt;               raise ValidationException(f"Password contains common pattern: {pattern}")
E               src.shared.exceptions.ValidationException: Password contains common pattern: password

src\auth\services\password_service.py:119: ValidationException</failure></testcase><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_character_requirements" time="0.002"><failure message="src.shared.exceptions.ValidationException: Password contains common pattern: password">self = &lt;services.test_password_service.TestPasswordService object at 0x000001BE311B19D0&gt;

    def test_validate_password_character_requirements(self):
        """Test password character requirements."""
        # Missing uppercase
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("testpassword123!")
    
        # Missing lowercase
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("TESTPASSWORD123!")
    
        # Missing digit
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("TestPassword!")
    
        # Missing special character
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("TestPassword123")
    
        # All requirements met
&gt;       self.password_service.validate_password_strength("TestPassword123!")

tests\unit\auth\services\test_password_service.py:139: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.auth.services.password_service.PasswordService object at 0x000001BE34659130&gt;
password = 'TestPassword123!'

    def validate_password_strength(self, password: str) -&gt; None:
        """
        Validate password strength according to security requirements.
    
        Args:
            password: Password to validate
    
        Raises:
            ValidationException: If password doesn't meet requirements
        """
        if not password:
            raise ValidationException("Password is required")
    
        # Length check
        if len(password) &lt; self.min_length:
            raise ValidationException(f"Password must be at least {self.min_length} characters long")
    
        if len(password) &gt; self.max_length:
            raise ValidationException(f"Password must be no more than {self.max_length} characters long")
    
        # Character requirements
        if not re.search(r'[A-Z]', password):
            raise ValidationException("Password must contain at least one uppercase letter")
    
        if not re.search(r'[a-z]', password):
            raise ValidationException("Password must contain at least one lowercase letter")
    
        if not re.search(r'\d', password):
            raise ValidationException("Password must contain at least one digit")
    
        if not re.search(r'[!@#$%^&amp;*(),.?":{}|&lt;&gt;]', password):
            raise ValidationException("Password must contain at least one special character")
    
        # Check for common patterns
        password_lower = password.lower()
        for pattern in self.common_patterns:
            if pattern in password_lower:
&gt;               raise ValidationException(f"Password contains common pattern: {pattern}")
E               src.shared.exceptions.ValidationException: Password contains common pattern: password

src\auth\services\password_service.py:119: ValidationException</failure></testcase><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_common_patterns" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_sequential_characters" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_validate_password_repeated_characters" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_generate_secure_password" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_generate_secure_password_custom_length" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_generate_secure_password_uniqueness" time="0.009" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_check_password_breach" time="0.002"><failure message="Failed: DID NOT RAISE &lt;class 'src.shared.exceptions.ValidationException'&gt;">self = &lt;services.test_password_service.TestPasswordService object at 0x000001BE311B26F0&gt;

    def test_check_password_breach(self):
        """Test password breach checking (mocked)."""
        with patch('src.auth.services.password_service.requests.get') as mock_get:
            # Mock API response for breached password
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = "5E884898DA28047151D0E56F8DC6292773603D0D6AABBDD62A11EF721D1542D8:3"
            mock_get.return_value = mock_response
    
            # This should raise an exception for breached password
&gt;           with pytest.raises(ValidationException):
E           Failed: DID NOT RAISE &lt;class 'src.shared.exceptions.ValidationException'&gt;

tests\unit\auth\services\test_password_service.py:223: Failed</failure></testcase><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_check_password_no_breach" time="0.002" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_hash_password_empty_string" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_hash_password_none" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_verify_password_empty_hash" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_verify_password_invalid_hash" time="0.001" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_password_entropy_calculation" time="0.002" /><testcase classname="tests.unit.auth.services.test_password_service.TestPasswordService" name="test_password_strength_score" time="0.002"><failure message="assert 0 &gt;= 1">self = &lt;services.test_password_service.TestPasswordService object at 0x000001BE311B2660&gt;

    def test_password_strength_score(self):
        """Test password strength scoring."""
        test_cases = [
            ("123", 0),          # Very weak
            ("password", 1),     # Weak
            ("Password1", 2),    # Fair
            ("Password123", 3),  # Good
            ("TestPassword123!", 4)  # Strong
        ]
    
        for password, expected_min_score in test_cases:
            score = self.password_service.get_password_strength_score(password)
&gt;           assert score &gt;= expected_min_score
E           assert 0 &gt;= 1

tests\unit\auth\services\test_password_service.py:282: AssertionError</failure></testcase><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_creation" time="0.003" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_to_dict" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_is_active" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_validation_invalid_price" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_validation_free_plan" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_is_free" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_calculate_yearly_discount" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_calculate_yearly_discount_no_yearly_price" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_get_price_for_interval" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_get_price_for_interval_no_yearly" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_update_features" time="0.008" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_activate" time="0.008" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_deactivate" time="0.007" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_deprecate" time="0.008" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_save" time="0.007" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_get_by_id" time="0.007" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_get_all_active" time="0.006" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_create_default_plans" time="0.001" /><testcase classname="tests.unit.payment.models.test_plan.TestPlan" name="test_plan_comparison" time="0.001" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_configure_stripe_test_environment" time="0.006" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_configure_stripe_production_environment" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_customer_success" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_customer_with_phone" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_customer_stripe_error" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_get_customer_success" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_get_customer_not_found" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_subscription_success" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_subscription_with_trial" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_get_subscription_success" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_update_subscription_success" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_cancel_subscription_at_period_end" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_cancel_subscription_immediately" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_product_success" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_create_price_success" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_construct_webhook_event_success" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_construct_webhook_event_invalid_payload" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_construct_webhook_event_invalid_signature" time="0.003" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_handle_stripe_error_card_error" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_handle_stripe_error_rate_limit" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_handle_stripe_error_invalid_request" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientService" name="test_handle_stripe_error_authentication" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientErrorHandling" name="test_handle_stripe_error_connection_error" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientErrorHandling" name="test_handle_stripe_error_generic" time="0.002" /><testcase classname="tests.unit.payment.services.test_stripe_client.TestStripeClientErrorHandling" name="test_handle_unknown_error" time="0.003" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_process_webhook_success" time="0.003" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_process_webhook_unhandled_event" time="0.003" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_process_webhook_validation_error" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_customer_created" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_customer_updated_found" time="0.003" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_customer_updated_not_found" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_customer_deleted" time="0.003" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_subscription_created" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_subscription_updated" time="0.003" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_subscription_deleted" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_subscription_trial_will_end" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_invoice_created" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_invoice_payment_succeeded" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_invoice_payment_failed" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_payment_intent_succeeded" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_handle_payment_intent_failed" time="0.002" /><testcase classname="tests.unit.payment.services.test_webhook_service.TestWebhookService" name="test_map_stripe_status" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_plan_creation" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_plan_creation_with_custom_values" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_plan_type_from_string" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_plan_status_from_string" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_is_active" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_is_free" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_get_price" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_has_feature" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_get_limit" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_save" time="0.003" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_update" time="0.003" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_to_dict" time="0.001" /><testcase classname="tests.unit.payment.test_plan_model.TestPlan" name="test_create_default_plans" time="0.003" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_subscription_creation" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_subscription_creation_with_custom_values" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_subscription_status_from_string" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_subscription_billing_interval_from_string" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_is_active" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_is_trial" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_is_trial_expired" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_days_until_trial_end" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_save" time="0.002" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_update" time="0.002" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_to_dict" time="0.001" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_cancel" time="0.002" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_suspend" time="0.003" /><testcase classname="tests.unit.payment.test_subscription_model.TestSubscription" name="test_reactivate" time="0.002" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_validate_email_valid" time="0.014" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_validate_email_invalid" time="0.002" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_validate_uuid_valid" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_validate_uuid_invalid" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_validate_tenant_name_valid" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_validate_tenant_name_invalid" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_register_user_request_valid" time="0.002" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_register_user_request_with_phone" time="0.004"><failure message="AssertionError: assert '+11234567890' == '+1234567890'&#10;  &#10;  - +1234567890&#10;  + +11234567890&#10;  ?  +">self = &lt;shared.test_validators.TestValidators object at 0x000001BE34190770&gt;

    def test_register_user_request_with_phone(self):
        """Test user registration request with phone."""
        valid_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'user_name': 'Test User',
            'tenant_name': 'Test Company',
            'phone': '+1234567890'
        }
    
        request = RegisterUserRequest(**valid_data)
&gt;       assert request.phone == '+1234567890'
E       AssertionError: assert '+11234567890' == '+1234567890'
E         
E         - +1234567890
E         + +11234567890
E         ?  +

tests\unit\shared\test_validators.py:127: AssertionError</failure></testcase><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_register_user_request_invalid_email" time="0.002"><failure message="src.shared.exceptions.InvalidEmailException: Invalid email address: An email address must have an @-sign.">email = 'invalid-email', check_deliverability = False

    def validate_email_address(email: str, check_deliverability: bool = True) -&gt; str:
        """Validate email address format."""
        try:
            # Validate and get normalized result
&gt;           valid = validate_email(email, check_deliverability=check_deliverability)

src\shared\validators.py:32: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\email_validator\validate_email.py:71: in validate_email
    = split_email(email)
..\..\AppData\Roaming\Python\Python312\site-packages\email_validator\syntax.py:123: in split_email
    left_part, right_part = split_string_at_unquoted_special(email, ("@", "&lt;"))
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

text = 'invalid-email', specials = ('@', '&lt;')

    def split_string_at_unquoted_special(text: str, specials: Tuple[str, ...]) -&gt; Tuple[str, str]:
        # Split the string at the first character in specials (an @-sign
        # or left angle bracket) that does not occur within quotes and
        # is not followed by a Unicode combining character.
        # If no special character is found, raise an error.
        inside_quote = False
        escaped = False
        left_part = ""
        for i, c in enumerate(text):
            # &lt; plus U+0338 (Combining Long Solidus Overlay) normalizes to
            # ≮ U+226E (Not Less-Than), and  it would be confusing to treat
            # the &lt; as the start of "&lt;email&gt;" syntax in that case. Liekwise,
            # if anything combines with an @ or ", we should probably not
            # treat it as a special character.
            if unicodedata.normalize("NFC", text[i:])[0] != c:
                left_part += c
    
            elif inside_quote:
                left_part += c
                if c == '\\' and not escaped:
                    escaped = True
                elif c == '"' and not escaped:
                    # The only way to exit the quote is an unescaped quote.
                    inside_quote = False
                    escaped = False
                else:
                    escaped = False
            elif c == '"':
                left_part += c
                inside_quote = True
            elif c in specials:
                # When unquoted, stop before a special character.
                break
            else:
                left_part += c
    
        if len(left_part) == len(text):
&gt;           raise EmailSyntaxError("An email address must have an @-sign.")
E           email_validator.exceptions_types.EmailSyntaxError: An email address must have an @-sign.

..\..\AppData\Roaming\Python\Python312\site-packages\email_validator\syntax.py:86: EmailSyntaxError

During handling of the above exception, another exception occurred:

self = &lt;shared.test_validators.TestValidators object at 0x000001BE34190920&gt;

    def test_register_user_request_invalid_email(self):
        """Test user registration with invalid email."""
        invalid_data = {
            'email': 'invalid-email',
            'password': 'TestPassword123!',
            'user_name': 'Test User',
            'tenant_name': 'Test Company'
        }
    
        with pytest.raises(ValidationError):
&gt;           RegisterUserRequest(**invalid_data)

tests\unit\shared\test_validators.py:139: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\shared\validators.py:159: in validate_email_field
    return validate_email_address(v, check_deliverability=False)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

email = 'invalid-email', check_deliverability = False

    def validate_email_address(email: str, check_deliverability: bool = True) -&gt; str:
        """Validate email address format."""
        try:
            # Validate and get normalized result
            valid = validate_email(email, check_deliverability=check_deliverability)
            return valid.email
        except EmailNotValidError as e:
&gt;           raise InvalidEmailException(f"Invalid email address: {str(e)}")
E           src.shared.exceptions.InvalidEmailException: Invalid email address: An email address must have an @-sign.

src\shared\validators.py:35: InvalidEmailException</failure></testcase><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_register_user_request_invalid_password" time="0.002" /><testcase classname="tests.unit.shared.test_validators.TestValidators" name="test_register_user_request_missing_fields" time="0.002" /><testcase classname="tests.unit.shared.test_validators.TestLoginRequest" name="test_login_request_valid" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestLoginRequest" name="test_login_request_invalid_email" time="0.001"><failure message="src.shared.exceptions.InvalidEmailException: Invalid email address: An email address must have an @-sign.">email = 'invalid-email', check_deliverability = False

    def validate_email_address(email: str, check_deliverability: bool = True) -&gt; str:
        """Validate email address format."""
        try:
            # Validate and get normalized result
&gt;           valid = validate_email(email, check_deliverability=check_deliverability)

src\shared\validators.py:32: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\email_validator\validate_email.py:71: in validate_email
    = split_email(email)
..\..\AppData\Roaming\Python\Python312\site-packages\email_validator\syntax.py:123: in split_email
    left_part, right_part = split_string_at_unquoted_special(email, ("@", "&lt;"))
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

text = 'invalid-email', specials = ('@', '&lt;')

    def split_string_at_unquoted_special(text: str, specials: Tuple[str, ...]) -&gt; Tuple[str, str]:
        # Split the string at the first character in specials (an @-sign
        # or left angle bracket) that does not occur within quotes and
        # is not followed by a Unicode combining character.
        # If no special character is found, raise an error.
        inside_quote = False
        escaped = False
        left_part = ""
        for i, c in enumerate(text):
            # &lt; plus U+0338 (Combining Long Solidus Overlay) normalizes to
            # ≮ U+226E (Not Less-Than), and  it would be confusing to treat
            # the &lt; as the start of "&lt;email&gt;" syntax in that case. Liekwise,
            # if anything combines with an @ or ", we should probably not
            # treat it as a special character.
            if unicodedata.normalize("NFC", text[i:])[0] != c:
                left_part += c
    
            elif inside_quote:
                left_part += c
                if c == '\\' and not escaped:
                    escaped = True
                elif c == '"' and not escaped:
                    # The only way to exit the quote is an unescaped quote.
                    inside_quote = False
                    escaped = False
                else:
                    escaped = False
            elif c == '"':
                left_part += c
                inside_quote = True
            elif c in specials:
                # When unquoted, stop before a special character.
                break
            else:
                left_part += c
    
        if len(left_part) == len(text):
&gt;           raise EmailSyntaxError("An email address must have an @-sign.")
E           email_validator.exceptions_types.EmailSyntaxError: An email address must have an @-sign.

..\..\AppData\Roaming\Python\Python312\site-packages\email_validator\syntax.py:86: EmailSyntaxError

During handling of the above exception, another exception occurred:

self = &lt;shared.test_validators.TestLoginRequest object at 0x000001BE34191100&gt;

    def test_login_request_invalid_email(self):
        """Test login request with invalid email."""
        invalid_data = {
            'email': 'invalid-email',
            'password': 'TestPassword123!'
        }
    
        with pytest.raises(ValidationError):
&gt;           LoginRequest(**invalid_data)

tests\unit\shared\test_validators.py:211: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\shared\validators.py:178: in validate_email_field
    return validate_email_address(v, check_deliverability=False)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

email = 'invalid-email', check_deliverability = False

    def validate_email_address(email: str, check_deliverability: bool = True) -&gt; str:
        """Validate email address format."""
        try:
            # Validate and get normalized result
            valid = validate_email(email, check_deliverability=check_deliverability)
            return valid.email
        except EmailNotValidError as e:
&gt;           raise InvalidEmailException(f"Invalid email address: {str(e)}")
E           src.shared.exceptions.InvalidEmailException: Invalid email address: An email address must have an @-sign.

src\shared\validators.py:35: InvalidEmailException</failure></testcase><testcase classname="tests.unit.shared.test_validators.TestLoginRequest" name="test_login_request_missing_fields" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestCreateSubscriptionRequest" name="test_create_subscription_request_valid" time="0.002" /><testcase classname="tests.unit.shared.test_validators.TestCreateSubscriptionRequest" name="test_create_subscription_request_with_payment_method" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestCreateSubscriptionRequest" name="test_create_subscription_request_invalid_billing_interval" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestCreateSubscriptionRequest" name="test_create_subscription_request_missing_fields" time="0.001"><failure message="Failed: DID NOT RAISE &lt;class 'pydantic_core._pydantic_core.ValidationError'&gt;">self = &lt;shared.test_validators.TestCreateSubscriptionRequest object at 0x000001BE341798B0&gt;

    def test_create_subscription_request_missing_fields(self):
        """Test subscription creation with missing fields."""
        # Missing plan_id
        with pytest.raises(ValidationError):
            CreateSubscriptionRequest(billing_interval='MONTHLY')
    
        # Missing billing_interval
&gt;       with pytest.raises(ValidationError):
E       Failed: DID NOT RAISE &lt;class 'pydantic_core._pydantic_core.ValidationError'&gt;

tests\unit\shared\test_validators.py:269: Failed</failure></testcase><testcase classname="tests.unit.shared.test_validators.TestUpdateUserRequest" name="test_update_user_request_valid" time="0.001"><failure message="AssertionError: assert '+11234567890' == '+1234567890'&#10;  &#10;  - +1234567890&#10;  + +11234567890&#10;  ?  +">self = &lt;shared.test_validators.TestUpdateUserRequest object at 0x000001BE34191520&gt;

    def test_update_user_request_valid(self):
        """Test valid user update request."""
        valid_data = {
            'name': 'Updated Name',
            'phone': '+1234567890'
        }
    
        request = UpdateUserRequest(**valid_data)
    
        assert request.name == 'Updated Name'
&gt;       assert request.phone == '+1234567890'
E       AssertionError: assert '+11234567890' == '+1234567890'
E         
E         - +1234567890
E         + +11234567890
E         ?  +

tests\unit\shared\test_validators.py:286: AssertionError</failure></testcase><testcase classname="tests.unit.shared.test_validators.TestUpdateUserRequest" name="test_update_user_request_role_and_status" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestUpdateUserRequest" name="test_update_user_request_invalid_role" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestUpdateUserRequest" name="test_update_user_request_invalid_status" time="0.001" /><testcase classname="tests.unit.shared.test_validators.TestUpdateUserRequest" name="test_update_user_request_empty" time="0.001" /><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_validate_tenant_access_valid" time="0.003"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193650&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_validate_tenant_access_invalid_tenant" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193830&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_validate_tenant_access_no_context" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193A10&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_enforce_data_isolation_valid_key" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193BF0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_enforce_data_isolation_invalid_tenant_in_pk" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193DD0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_enforce_data_isolation_no_tenant_in_pk" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193FB0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_enforce_data_isolation_invalid_key_format" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC1D0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_query_success" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC3B0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_query_cross_tenant_data_filtered" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC560&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_get_item_success" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC710&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_get_item_cross_tenant_access_denied" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC8F0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_put_item_success" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341ACAA0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_put_item_cross_tenant_denied" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341ACC50&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_delete_item_success" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341ACE00&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_secure_delete_item_cross_tenant_denied" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341ACFB0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_extract_tenant_from_key_valid" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AD190&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_extract_tenant_from_key_global" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AD370&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_extract_tenant_from_key_invalid_format" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AD550&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_enrich_item_with_tenant_metadata" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193D10&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_audit_data_access" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE34193770&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_validate_data_key_format_valid" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AD2B0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_validate_data_key_format_missing_pk" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341ACD40&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_validate_data_key_format_missing_sk" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC6B0&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_get_tenant_data_summary" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AC140&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_check_tenant_isolation_compliance_success" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341AD820&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_data_isolation_service.TestDataIsolationService" name="test_check_tenant_isolation_compliance_violations" time="0.001"><error message="failed on setup with &quot;TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'&quot;">self = &lt;tenant.services.test_data_isolation_service.TestDataIsolationService object at 0x000001BE341ADA60&gt;

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
&gt;       self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            role='MEMBER',
            permissions=['read', 'write']
        )
E       TypeError: AuthContext.__init__() got an unexpected keyword argument 'permissions'

tests\unit\tenant\services\test_data_isolation_service.py:27: TypeError</error></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_create_tenant_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_create_tenant_duplicate_slug" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_create_tenant_invalid_plan" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_generate_tenant_slug_basic" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341AD6A0&gt;

    def test_generate_tenant_slug_basic(self):
        """Test basic tenant slug generation."""
&gt;       slug = self.provisioning_service._generate_tenant_slug('Test Company')
E       AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'

tests\unit\tenant\services\test_provisioning_service.py:106: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_generate_tenant_slug_special_chars" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341AF320&gt;

    def test_generate_tenant_slug_special_chars(self):
        """Test tenant slug generation with special characters."""
&gt;       slug = self.provisioning_service._generate_tenant_slug('Test &amp; Company, Inc.')
E       AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'

tests\unit\tenant\services\test_provisioning_service.py:111: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_generate_tenant_slug_numbers" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341AF500&gt;

    def test_generate_tenant_slug_numbers(self):
        """Test tenant slug generation with numbers."""
&gt;       slug = self.provisioning_service._generate_tenant_slug('Company 123 LLC')
E       AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'

tests\unit\tenant\services\test_provisioning_service.py:116: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_generate_tenant_slug_unicode" time="0.002"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341AF6E0&gt;

    def test_generate_tenant_slug_unicode(self):
        """Test tenant slug generation with unicode characters."""
&gt;       slug = self.provisioning_service._generate_tenant_slug('Compañía Española')
E       AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_slug'

tests\unit\tenant\services\test_provisioning_service.py:121: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_provision_tenant_resources_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_setup_tenant_configuration_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_create_admin_user_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_validate_tenant_data_valid" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341AFDD0&gt;

    def test_validate_tenant_data_valid(self):
        """Test tenant data validation with valid data."""
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
    
        # Should not raise exception
&gt;       self.provisioning_service._validate_tenant_data(tenant_data)
E       AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'

tests\unit\tenant\services\test_provisioning_service.py:208: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_validate_tenant_data_missing_name" time="0.002"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341AFFB0&gt;

    def test_validate_tenant_data_missing_name(self):
        """Test tenant data validation with missing name."""
        tenant_data = {
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
    
        with pytest.raises(ValidationException) as exc_info:
&gt;           self.provisioning_service._validate_tenant_data(tenant_data)
E           AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'

tests\unit\tenant\services\test_provisioning_service.py:220: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_validate_tenant_data_invalid_email" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341BC1D0&gt;

    def test_validate_tenant_data_invalid_email(self):
        """Test tenant data validation with invalid email."""
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': 'invalid-email',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
    
        with pytest.raises(ValidationException) as exc_info:
&gt;           self.provisioning_service._validate_tenant_data(tenant_data)
E           AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'

tests\unit\tenant\services\test_provisioning_service.py:235: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_validate_tenant_data_invalid_slug" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341BC3B0&gt;

    def test_validate_tenant_data_invalid_slug(self):
        """Test tenant data validation with invalid slug."""
        tenant_data = {
            'name': 'Test Company',
            'slug': 'invalid slug with spaces',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
    
        with pytest.raises(ValidationException) as exc_info:
&gt;           self.provisioning_service._validate_tenant_data(tenant_data)
E           AttributeError: 'TenantProvisioningService' object has no attribute '_validate_tenant_data'

tests\unit\tenant\services\test_provisioning_service.py:250: AttributeError</failure></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_suspend_tenant_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_reactivate_tenant_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_get_tenant_status_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_get_tenant_status_not_found" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_delete_tenant_success" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_delete_tenant_active_tenant" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.unit.tenant.services.test_provisioning_service.TestTenantProvisioningService" name="test_generate_tenant_id" time="0.001"><failure message="AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_id'">self = &lt;tenant.services.test_provisioning_service.TestTenantProvisioningService object at 0x000001BE341BD010&gt;

    def test_generate_tenant_id(self):
        """Test tenant ID generation."""
&gt;       tenant_id = self.provisioning_service._generate_tenant_id()
E       AttributeError: 'TenantProvisioningService' object has no attribute '_generate_tenant_id'

tests\unit\tenant\services\test_provisioning_service.py:362: AttributeError</failure></testcase><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_tenant_initialization" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_tenant_is_active" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_tenant_trial_expired" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_tenant_can_add_user" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_get_max_users_for_plan" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_get_features_for_plan" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestTenantModel" name="test_tenant_to_dict" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_initialization" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_is_active" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_is_master" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_is_locked" time="0.001" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_authenticate_success" time="0.003" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_authenticate_invalid_password" time="0.003" /><testcase classname="tests.unit.test_auth_models.TestUserModel" name="test_user_to_dict" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_hash_password" time="0.319" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_verify_password_correct" time="0.462" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_verify_password_incorrect" time="0.545" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_strong" time="0.002" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_weak_length" time="0.002" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_no_uppercase" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_no_lowercase" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_no_numbers" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_no_special" time="0.002" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_validate_password_strength_common_password" time="0.002" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_generate_secure_token" time="0.002" /><testcase classname="tests.unit.test_shared_auth.TestPasswordManager" name="test_generate_secure_token_custom_length" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_create_access_token" time="0.914"><failure message="AssertionError: assert 'agent-scl-platform' == 'platform-auth'&#10;  &#10;  - platform-auth&#10;  + agent-scl-platform">self = &lt;test_shared_auth.TestJWTManager object at 0x000001BE341F5970&gt;

    def test_create_access_token(self):
        """Test access token creation."""
        jwt_manager = JWTManager()
    
        token = jwt_manager.create_access_token(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            role="MEMBER"
        )
    
        assert isinstance(token, str)
        assert len(token) &gt; 100
    
        # Decode without verification to check payload
        payload = jwt.decode(token, options={"verify_signature": False})
        assert payload["sub"] == "user-123"
        assert payload["tenant_id"] == "tenant-123"
        assert payload["email"] == "<EMAIL>"
        assert payload["role"] == "MEMBER"
        assert payload["type"] == "access"
&gt;       assert payload["iss"] == "platform-auth"
E       AssertionError: assert 'agent-scl-platform' == 'platform-auth'
E         
E         - platform-auth
E         + agent-scl-platform

tests\unit\test_shared_auth.py:173: AssertionError</failure></testcase><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_create_refresh_token" time="0.806"><failure message="AssertionError: assert 'agent-scl-platform' == 'platform-auth'&#10;  &#10;  - platform-auth&#10;  + agent-scl-platform">self = &lt;test_shared_auth.TestJWTManager object at 0x000001BE341F5B20&gt;

    def test_create_refresh_token(self):
        """Test refresh token creation."""
        jwt_manager = JWTManager()
    
        token = jwt_manager.create_refresh_token(
            user_id="user-123",
            tenant_id="tenant-123"
        )
    
        assert isinstance(token, str)
        assert len(token) &gt; 100
    
        # Decode without verification to check payload
        payload = jwt.decode(token, options={"verify_signature": False})
        assert payload["sub"] == "user-123"
        assert payload["tenant_id"] == "tenant-123"
        assert payload["type"] == "refresh"
&gt;       assert payload["iss"] == "platform-auth"
E       AssertionError: assert 'agent-scl-platform' == 'platform-auth'
E         
E         - platform-auth
E         + agent-scl-platform

tests\unit\test_shared_auth.py:193: AssertionError</failure></testcase><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_verify_token_valid" time="0.793"><failure message="src.shared.exceptions.InvalidTokenException: Token validation failed: Not enough segments">self = &lt;jwt.api_jws.PyJWS object at 0x000001BE310A54F0&gt;, jwt = b'valid_token'

    def _load(self, jwt: str | bytes) -&gt; tuple[bytes, bytes, dict[str, Any], bytes]:
        if isinstance(jwt, str):
            jwt = jwt.encode("utf-8")
    
        if not isinstance(jwt, bytes):
            raise DecodeError(f"Invalid token type. Token must be a {bytes}")
    
        try:
&gt;           signing_input, crypto_segment = jwt.rsplit(b".", 1)
E           ValueError: not enough values to unpack (expected 2, got 1)

..\..\AppData\Roaming\Python\Python312\site-packages\jwt\api_jws.py:257: ValueError

The above exception was the direct cause of the following exception:

self = &lt;src.shared.auth.JWTManager object at 0x000001BE34C2CDA0&gt;
token = 'valid_token', token_type = 'access'

    def verify_token(self, token: str, token_type: str = "access") -&gt; Dict[str, Any]:
        """Verify and decode a JWT token using multi-key support."""
        try:
            config = self._get_jwt_config()
    
            # Try to get key ID from token header
&gt;           unverified_header = jwt.get_unverified_header(token)

src\shared\auth.py:281: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\jwt\api_jws.py:244: in get_unverified_header
    headers = self._load(jwt)[2]
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;jwt.api_jws.PyJWS object at 0x000001BE310A54F0&gt;, jwt = b'valid_token'

    def _load(self, jwt: str | bytes) -&gt; tuple[bytes, bytes, dict[str, Any], bytes]:
        if isinstance(jwt, str):
            jwt = jwt.encode("utf-8")
    
        if not isinstance(jwt, bytes):
            raise DecodeError(f"Invalid token type. Token must be a {bytes}")
    
        try:
            signing_input, crypto_segment = jwt.rsplit(b".", 1)
            header_segment, payload_segment = signing_input.split(b".", 1)
        except ValueError as err:
&gt;           raise DecodeError("Not enough segments") from err
E           jwt.exceptions.DecodeError: Not enough segments

..\..\AppData\Roaming\Python\Python312\site-packages\jwt\api_jws.py:260: DecodeError

During handling of the above exception, another exception occurred:

self = &lt;test_shared_auth.TestJWTManager object at 0x000001BE341F5CA0&gt;
mock_decode = &lt;MagicMock name='decode' id='1916440596544'&gt;

    @patch('src.shared.auth.jwt.decode')
    def test_verify_token_valid(self, mock_decode):
        """Test token verification with valid token."""
        jwt_manager = JWTManager()
    
        mock_payload = {
            "sub": "user-123",
            "tenant_id": "tenant-123",
            "type": "access",
            "exp": int(time.time()) + 3600,  # 1 hour from now
            "iss": "platform-auth",
            "aud": "platform-api"
        }
        mock_decode.return_value = mock_payload
    
&gt;       result = jwt_manager.verify_token("valid_token", "access")

tests\unit\test_shared_auth.py:211: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.shared.auth.JWTManager object at 0x000001BE34C2CDA0&gt;
token = 'valid_token', token_type = 'access'

    def verify_token(self, token: str, token_type: str = "access") -&gt; Dict[str, Any]:
        """Verify and decode a JWT token using multi-key support."""
        try:
            config = self._get_jwt_config()
    
            # Try to get key ID from token header
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get('kid')
    
            keys_to_try = []
    
            # If we have a key ID, try that key first
            if kid:
                specific_key = self.get_key_by_kid(kid)
                if specific_key:
                    keys_to_try.append(specific_key)
    
            # Add active key if not already included
            active_key = self.get_active_key()
            if not kid or active_key['kid'] != kid:
                keys_to_try.append(active_key)
    
            # Add all other keys as fallback
            all_keys = self.get_all_keys()
            for key in all_keys:
                if key not in keys_to_try:
                    keys_to_try.append(key)
    
            # Try to validate with each key
            last_exception = None
            for key in keys_to_try:
                try:
                    payload = jwt.decode(
                        token,
                        key['secret_key'],
                        algorithms=[config.get('algorithm', self.algorithm)],
                        audience=config.get('audience', self.settings.jwt_audience),
                        issuer=config.get('issuer', self.settings.jwt_issuer)
                    )
    
                    # Verify token type
                    if payload.get("type") != token_type:
                        raise InvalidTokenException(f"Invalid token type. Expected {token_type}")
    
                    # Check if token is expired (additional check)
                    exp = payload.get("exp")
                    if exp and datetime.utcfromtimestamp(exp) &lt; datetime.utcnow():
                        raise TokenExpiredException("Token has expired")
    
                    logger.info(f"Token validated with key {key['kid']}")
                    return payload
    
                except jwt.ExpiredSignatureError:
                    raise TokenExpiredException("Token has expired")
                except jwt.InvalidTokenError as e:
                    last_exception = e
                    continue
    
            # If we get here, no key worked
            logger.warning(f"Token validation failed with all available keys")
            raise InvalidTokenException(f"Invalid token: {str(last_exception)}")
    
        except (TokenExpiredException, InvalidTokenException):
            # Re-raise these specific exceptions
            raise
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
&gt;           raise InvalidTokenException(f"Token validation failed: {str(e)}")
E           src.shared.exceptions.InvalidTokenException: Token validation failed: Not enough segments

src\shared\auth.py:342: InvalidTokenException</failure></testcase><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_verify_token_wrong_type" time="0.801"><failure message="AssertionError: assert 'Invalid token type' in 'Token validation failed: Not enough segments'&#10; +  where 'Token validation failed: Not enough segments' = str(InvalidTokenException('Token validation failed: Not enough segments'))&#10; +    where InvalidTokenException('Token validation failed: Not enough segments') = &lt;ExceptionInfo InvalidTokenException('Token validation failed: Not enough segments') tblen=2&gt;.value">self = &lt;test_shared_auth.TestJWTManager object at 0x000001BE341F5E20&gt;
mock_decode = &lt;MagicMock name='decode' id='1916440322624'&gt;

    @patch('src.shared.auth.jwt.decode')
    def test_verify_token_wrong_type(self, mock_decode):
        """Test token verification with wrong token type."""
        jwt_manager = JWTManager()
    
        mock_payload = {
            "sub": "user-123",
            "tenant_id": "tenant-123",
            "type": "refresh",  # Wrong type
            "exp": int(time.time()) + 3600,
            "iss": "platform-auth",
            "aud": "platform-api"
        }
        mock_decode.return_value = mock_payload
    
        with pytest.raises(InvalidTokenException) as exc_info:
            jwt_manager.verify_token("token", "access")
    
&gt;       assert "Invalid token type" in str(exc_info.value)
E       AssertionError: assert 'Invalid token type' in 'Token validation failed: Not enough segments'
E        +  where 'Token validation failed: Not enough segments' = str(InvalidTokenException('Token validation failed: Not enough segments'))
E        +    where InvalidTokenException('Token validation failed: Not enough segments') = &lt;ExceptionInfo InvalidTokenException('Token validation failed: Not enough segments') tblen=2&gt;.value

tests\unit\test_shared_auth.py:234: AssertionError</failure></testcase><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_verify_token_expired" time="0.817"><failure message="src.shared.exceptions.InvalidTokenException: Token validation failed: Not enough segments">self = &lt;jwt.api_jws.PyJWS object at 0x000001BE310A54F0&gt;, jwt = b'expired_token'

    def _load(self, jwt: str | bytes) -&gt; tuple[bytes, bytes, dict[str, Any], bytes]:
        if isinstance(jwt, str):
            jwt = jwt.encode("utf-8")
    
        if not isinstance(jwt, bytes):
            raise DecodeError(f"Invalid token type. Token must be a {bytes}")
    
        try:
&gt;           signing_input, crypto_segment = jwt.rsplit(b".", 1)
E           ValueError: not enough values to unpack (expected 2, got 1)

..\..\AppData\Roaming\Python\Python312\site-packages\jwt\api_jws.py:257: ValueError

The above exception was the direct cause of the following exception:

self = &lt;src.shared.auth.JWTManager object at 0x000001BE34AD0290&gt;
token = 'expired_token', token_type = 'access'

    def verify_token(self, token: str, token_type: str = "access") -&gt; Dict[str, Any]:
        """Verify and decode a JWT token using multi-key support."""
        try:
            config = self._get_jwt_config()
    
            # Try to get key ID from token header
&gt;           unverified_header = jwt.get_unverified_header(token)

src\shared\auth.py:281: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\jwt\api_jws.py:244: in get_unverified_header
    headers = self._load(jwt)[2]
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;jwt.api_jws.PyJWS object at 0x000001BE310A54F0&gt;, jwt = b'expired_token'

    def _load(self, jwt: str | bytes) -&gt; tuple[bytes, bytes, dict[str, Any], bytes]:
        if isinstance(jwt, str):
            jwt = jwt.encode("utf-8")
    
        if not isinstance(jwt, bytes):
            raise DecodeError(f"Invalid token type. Token must be a {bytes}")
    
        try:
            signing_input, crypto_segment = jwt.rsplit(b".", 1)
            header_segment, payload_segment = signing_input.split(b".", 1)
        except ValueError as err:
&gt;           raise DecodeError("Not enough segments") from err
E           jwt.exceptions.DecodeError: Not enough segments

..\..\AppData\Roaming\Python\Python312\site-packages\jwt\api_jws.py:260: DecodeError

During handling of the above exception, another exception occurred:

self = &lt;test_shared_auth.TestJWTManager object at 0x000001BE341F5FD0&gt;
mock_decode = &lt;MagicMock name='decode' id='1916439173104'&gt;

    @patch('src.shared.auth.jwt.decode')
    def test_verify_token_expired(self, mock_decode):
        """Test token verification with expired token."""
        jwt_manager = JWTManager()
    
        mock_decode.side_effect = jwt.ExpiredSignatureError("Token expired")
    
        with pytest.raises(TokenExpiredException):
&gt;           jwt_manager.verify_token("expired_token", "access")

tests\unit\test_shared_auth.py:244: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.shared.auth.JWTManager object at 0x000001BE34AD0290&gt;
token = 'expired_token', token_type = 'access'

    def verify_token(self, token: str, token_type: str = "access") -&gt; Dict[str, Any]:
        """Verify and decode a JWT token using multi-key support."""
        try:
            config = self._get_jwt_config()
    
            # Try to get key ID from token header
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get('kid')
    
            keys_to_try = []
    
            # If we have a key ID, try that key first
            if kid:
                specific_key = self.get_key_by_kid(kid)
                if specific_key:
                    keys_to_try.append(specific_key)
    
            # Add active key if not already included
            active_key = self.get_active_key()
            if not kid or active_key['kid'] != kid:
                keys_to_try.append(active_key)
    
            # Add all other keys as fallback
            all_keys = self.get_all_keys()
            for key in all_keys:
                if key not in keys_to_try:
                    keys_to_try.append(key)
    
            # Try to validate with each key
            last_exception = None
            for key in keys_to_try:
                try:
                    payload = jwt.decode(
                        token,
                        key['secret_key'],
                        algorithms=[config.get('algorithm', self.algorithm)],
                        audience=config.get('audience', self.settings.jwt_audience),
                        issuer=config.get('issuer', self.settings.jwt_issuer)
                    )
    
                    # Verify token type
                    if payload.get("type") != token_type:
                        raise InvalidTokenException(f"Invalid token type. Expected {token_type}")
    
                    # Check if token is expired (additional check)
                    exp = payload.get("exp")
                    if exp and datetime.utcfromtimestamp(exp) &lt; datetime.utcnow():
                        raise TokenExpiredException("Token has expired")
    
                    logger.info(f"Token validated with key {key['kid']}")
                    return payload
    
                except jwt.ExpiredSignatureError:
                    raise TokenExpiredException("Token has expired")
                except jwt.InvalidTokenError as e:
                    last_exception = e
                    continue
    
            # If we get here, no key worked
            logger.warning(f"Token validation failed with all available keys")
            raise InvalidTokenException(f"Invalid token: {str(last_exception)}")
    
        except (TokenExpiredException, InvalidTokenException):
            # Re-raise these specific exceptions
            raise
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
&gt;           raise InvalidTokenException(f"Token validation failed: {str(e)}")
E           src.shared.exceptions.InvalidTokenException: Token validation failed: Not enough segments

src\shared\auth.py:342: InvalidTokenException</failure></testcase><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_verify_token_invalid" time="0.784" /><testcase classname="tests.unit.test_shared_auth.TestJWTManager" name="test_extract_user_context" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_auth_context_initialization" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_is_master_true" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_is_master_false" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_is_member_true" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_is_member_false" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_permissions_master" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_permissions_member" time="0.001" /><testcase classname="tests.unit.test_shared_auth.TestAuthContext" name="test_to_dict" time="0.001" /></testsuite></testsuites>