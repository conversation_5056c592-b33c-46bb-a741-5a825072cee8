<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="14" skipped="1" tests="43" time="9.748" timestamp="2025-07-17T15:52:48.633606" hostname="ANDERSON-APES-DESKTOP"><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_aws_region_configuration" time="0.002" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_dynamodb_connectivity" time="0.659" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_s3_connectivity" time="0.469" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_secrets_manager_connectivity" time="0.318" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_database_client_configuration" time="0.001"><failure message="AssertionError: assert False&#10; +  where False = hasattr(&lt;src.shared.database.DynamoDBClient object at 0x000001BAAE35DBB0&gt;, 'table_name')">self = &lt;infrastructure.test_aws_connectivity.TestAWSConnectivity object at 0x000001BAAEAF15B0&gt;

    def test_database_client_configuration(self):
        """Test database client configuration."""
        # Test that db_client is properly configured
        assert db_client is not None
    
        # Test configuration attributes
&gt;       assert hasattr(db_client, 'table_name')
E       AssertionError: assert False
E        +  where False = hasattr(&lt;src.shared.database.DynamoDBClient object at 0x000001BAAE35DBB0&gt;, 'table_name')

tests\infrastructure\test_aws_connectivity.py:211: AssertionError</failure></testcase><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_settings_configuration" time="0.001"><failure message="AssertionError: assert 'test' in ['dev', 'staging', 'prod']&#10; +  where 'test' = Settings(project_name='platform', environment='test', aws_region='us-east-1', service_name='', function_name='', dynamodb_table='platform-main-test', s3_bucket='platform-data-test', log_level='INFO', jwt_issuer='platform-auth', jwt_audience='platform-api', jwt_algorithm='RS256', jwt_access_token_expire_minutes=30, jwt_refresh_token_expire_days=7, rate_limit_per_second=10, rate_limit_per_minute=100, rate_limit_per_hour=1000, password_min_length=8, password_require_uppercase=True, password_require_lowercase=True, password_require_numbers=True, password_require_special=True, from_email='<EMAIL>', reply_to_email='<EMAIL>', n8n_base_url=None, n8n_api_key=None, n8n_webhook_secret=None, stripe_publishable_key=None, stripe_secret_key=None, stripe_webhook_secret=None, payu_merchant_id=None, payu_account_id=None, payu_api_key=None, payu_api_login=None, debug=False, testing=True).environment">self = &lt;infrastructure.test_aws_connectivity.TestAWSConnectivity object at 0x000001BAAEAF1730&gt;

    def test_settings_configuration(self):
        """Test application settings configuration."""
        settings = get_settings()
    
        # Test required settings
        assert settings.project_name is not None
        assert settings.environment is not None
        assert settings.aws_region is not None
    
        # Test environment-specific settings
        valid_environments = ['dev', 'staging', 'prod']
&gt;       assert settings.environment in valid_environments
E       AssertionError: assert 'test' in ['dev', 'staging', 'prod']
E        +  where 'test' = Settings(project_name='platform', environment='test', aws_region='us-east-1', service_name='', function_name='', dynamodb_table='platform-main-test', s3_bucket='platform-data-test', log_level='INFO', jwt_issuer='platform-auth', jwt_audience='platform-api', jwt_algorithm='RS256', jwt_access_token_expire_minutes=30, jwt_refresh_token_expire_days=7, rate_limit_per_second=10, rate_limit_per_minute=100, rate_limit_per_hour=1000, password_min_length=8, password_require_uppercase=True, password_require_lowercase=True, password_require_numbers=True, password_require_special=True, from_email='<EMAIL>', reply_to_email='<EMAIL>', n8n_base_url=None, n8n_api_key=None, n8n_webhook_secret=None, stripe_publishable_key=None, stripe_secret_key=None, stripe_webhook_secret=None, payu_merchant_id=None, payu_account_id=None, payu_api_key=None, payu_api_login=None, debug=False, testing=True).environment

tests\infrastructure\test_aws_connectivity.py:229: AssertionError</failure></testcase><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_logging_configuration" time="0.004" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_exception_handling_configuration" time="0.001" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_response_handling_configuration" time="0.002"><failure message="KeyError: 'error'">self = &lt;infrastructure.test_aws_connectivity.TestAWSConnectivity object at 0x000001BAAEAF1BB0&gt;

    def test_response_handling_configuration(self):
        """Test API response handling configuration."""
        from src.shared.responses import APIResponse
    
        # Test success response
        response = APIResponse.success(data={'test': 'data'})
        assert response['statusCode'] == 200
    
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['test'] == 'data'
    
        # Test error response
        response = APIResponse.error(message="Test error", status_code=400)
        assert response['statusCode'] == 400
    
        body = json.loads(response['body'])
        assert body['success'] is False
&gt;       assert body['error']['message'] == "Test error"
E       KeyError: 'error'

tests\infrastructure\test_aws_connectivity.py:296: KeyError</failure></testcase><testcase classname="tests.infrastructure.test_aws_connectivity.TestAWSConnectivity" name="test_cors_configuration" time="0.001" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestEnvironmentSpecificConfiguration" name="test_development_environment_config" time="0.000" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestEnvironmentSpecificConfiguration" name="test_staging_environment_config" time="0.000" /><testcase classname="tests.infrastructure.test_aws_connectivity.TestEnvironmentSpecificConfiguration" name="test_production_environment_config" time="0.001" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_environment_variables_configured" time="0.001"><failure message="AssertionError: Missing required environment variables: ['AWS_REGION', 'PROJECT_NAME', 'DYNAMODB_TABLE_NAME']&#10;assert not ['AWS_REGION', 'PROJECT_NAME', 'DYNAMODB_TABLE_NAME']">self = &lt;infrastructure.test_deployment.TestInfrastructureDeployment object at 0x000001BAAEAF2F30&gt;

    def test_environment_variables_configured(self):
        """Test that all required environment variables are set."""
        required_vars = [
            'AWS_REGION',
            'ENVIRONMENT',
            'PROJECT_NAME',
            'DYNAMODB_TABLE_NAME'
        ]
    
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
    
&gt;       assert not missing_vars, f"Missing required environment variables: {missing_vars}"
E       AssertionError: Missing required environment variables: ['AWS_REGION', 'PROJECT_NAME', 'DYNAMODB_TABLE_NAME']
E       assert not ['AWS_REGION', 'PROJECT_NAME', 'DYNAMODB_TABLE_NAME']

tests\infrastructure\test_deployment.py:44: AssertionError</failure></testcase><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_aws_credentials_configured" time="0.864" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_dynamodb_table_configuration" time="0.271"><failure message="botocore.exceptions.ParamValidationError: Parameter validation failed:&#10;Unknown parameter in GlobalSecondaryIndexes[0]: &quot;BillingMode&quot;, must be one of: IndexName, KeySchema, Projection, ProvisionedThroughput, OnDemandThroughput&#10;Unknown parameter in GlobalSecondaryIndexes[1]: &quot;BillingMode&quot;, must be one of: IndexName, KeySchema, Projection, ProvisionedThroughput, OnDemandThroughput">self = &lt;infrastructure.test_deployment.TestInfrastructureDeployment object at 0x000001BAAEAF2C60&gt;

    @mock_aws
    def test_dynamodb_table_configuration(self):
        """Test DynamoDB table exists and is properly configured."""
        # Create mock DynamoDB table for testing
        dynamodb = boto3.resource('dynamodb', region_name=self.aws_region)
    
        table_name = os.getenv('DYNAMODB_TABLE_NAME', 'platform-dev')
    
        # Create table with expected schema
&gt;       table = dynamodb.create_table(
            TableName=table_name,
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI1PK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI1SK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI2PK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI2SK', 'AttributeType': 'S'}
            ],
            GlobalSecondaryIndexes=[
                {
                    'IndexName': 'GSI1',
                    'KeySchema': [
                        {'AttributeName': 'GSI1PK', 'KeyType': 'HASH'},
                        {'AttributeName': 'GSI1SK', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'BillingMode': 'PAY_PER_REQUEST'
                },
                {
                    'IndexName': 'GSI2',
                    'KeySchema': [
                        {'AttributeName': 'GSI2PK', 'KeyType': 'HASH'},
                        {'AttributeName': 'GSI2SK', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'BillingMode': 'PAY_PER_REQUEST'
                }
            ],
            BillingMode='PAY_PER_REQUEST'
        )

tests\infrastructure\test_deployment.py:69: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\AppData\Roaming\Python\Python312\site-packages\boto3\resources\factory.py:581: in do_action
    response = action(self, *args, **kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\boto3\resources\action.py:88: in __call__
    response = getattr(parent.meta.client, operation_name)(*args, **params)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\client.py:565: in _api_call
    return self._make_api_call(operation_name, kwargs)
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\client.py:974: in _make_api_call
    request_dict = self._convert_to_request_dict(
..\..\AppData\Roaming\Python\Python312\site-packages\botocore\client.py:1048: in _convert_to_request_dict
    request_dict = self._serializer.serialize_to_request(
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;botocore.validate.ParamValidationDecorator object at 0x000001BAAEBC0800&gt;
parameters = {'AttributeDefinitions': [{'AttributeName': 'PK', 'AttributeType': 'S'}, {'AttributeName': 'SK', 'AttributeType': 'S'}... 'ALL'}}], 'KeySchema': [{'AttributeName': 'PK', 'KeyType': 'HASH'}, {'AttributeName': 'SK', 'KeyType': 'RANGE'}], ...}
operation_model = OperationModel(name=CreateTable)

    def serialize_to_request(self, parameters, operation_model):
        input_shape = operation_model.input_shape
        if input_shape is not None:
            report = self._param_validator.validate(
                parameters, operation_model.input_shape
            )
            if report.has_errors():
&gt;               raise ParamValidationError(report=report.generate_report())
E               botocore.exceptions.ParamValidationError: Parameter validation failed:
E               Unknown parameter in GlobalSecondaryIndexes[0]: "BillingMode", must be one of: IndexName, KeySchema, Projection, ProvisionedThroughput, OnDemandThroughput
E               Unknown parameter in GlobalSecondaryIndexes[1]: "BillingMode", must be one of: IndexName, KeySchema, Projection, ProvisionedThroughput, OnDemandThroughput

..\..\AppData\Roaming\Python\Python312\site-packages\botocore\validate.py:381: ParamValidationError</failure></testcase><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_s3_bucket_configuration" time="0.211" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_secrets_manager_configuration" time="0.777" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_ses_configuration" time="0.800" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_lambda_function_configuration" time="2.055"><failure message="src.shared.exceptions.ConfigurationException: Failed to initialize Stripe: Stripe credentials not found in AWS Secrets Manager">self = &lt;src.payment.services.stripe_service.StripeService object at 0x000001BAB108F260&gt;

    def _initialize_stripe(self) -&gt; None:
        """Initialize Stripe with API keys."""
        try:
            credentials = get_integration_credentials('stripe')
    
            if not credentials:
&gt;               raise ConfigurationException(
                    "Stripe credentials not found in AWS Secrets Manager",
                    config_key="stripe-credentials"
                )
E               src.shared.exceptions.ConfigurationException: Stripe credentials not found in AWS Secrets Manager

src\payment\services\stripe_service.py:39: ConfigurationException

During handling of the above exception, another exception occurred:

self = &lt;infrastructure.test_deployment.TestInfrastructureDeployment object at 0x000001BAAEAF25A0&gt;

    def test_lambda_function_configuration(self):
        """Test Lambda function configuration (mock)."""
        # This would test actual Lambda functions in real deployment
        # For now, we'll test the handler imports work correctly
    
        try:
            # Test auth handlers can be imported
            from src.auth.handlers import register, login, refresh, verify_email
            from src.auth.handlers import forgot_password, reset_password, authorizer
    
            # Test payment handlers can be imported
&gt;           from src.payment.handlers import create_subscription, get_subscription

tests\infrastructure\test_deployment.py:231: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\payment\handlers\create_subscription.py:24: in &lt;module&gt;
    from ..services.subscription_service import subscription_service
src\payment\services\subscription_service.py:23: in &lt;module&gt;
    from ..services.stripe_service import stripe_service
src\payment\services\stripe_service.py:408: in &lt;module&gt;
    stripe_service = StripeService()
src\payment\services\stripe_service.py:31: in __init__
    self._initialize_stripe()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.payment.services.stripe_service.StripeService object at 0x000001BAB108F260&gt;

    def _initialize_stripe(self) -&gt; None:
        """Initialize Stripe with API keys."""
        try:
            credentials = get_integration_credentials('stripe')
    
            if not credentials:
                raise ConfigurationException(
                    "Stripe credentials not found in AWS Secrets Manager",
                    config_key="stripe-credentials"
                )
    
            # Set Stripe API key
            stripe.api_key = credentials.get('secret_key')
    
            if not stripe.api_key:
                raise ConfigurationException(
                    "Stripe secret key not found in credentials",
                    config_key="stripe.secret_key"
                )
    
            # Set API version
            stripe.api_version = "2023-10-16"
    
            logger.info("Stripe service initialized successfully")
    
        except Exception as e:
            logger.error(f"Failed to initialize Stripe service: {str(e)}")
&gt;           raise ConfigurationException(
                f"Failed to initialize Stripe: {str(e)}",
                config_key="stripe"
            )
E           src.shared.exceptions.ConfigurationException: Failed to initialize Stripe: Stripe credentials not found in AWS Secrets Manager

src\payment\services\stripe_service.py:60: ConfigurationException</failure></testcase><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_api_gateway_configuration" time="0.001" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_cloudwatch_configuration" time="0.054"><skipped type="pytest.skip" message="CloudWatch not available in test environment: Parameter validation failed:&#10;Unknown parameter in input: &quot;MaxRecords&quot;, must be one of: Namespace, MetricName, Dimensions, NextToken, RecentlyActive, IncludeLinkedAccounts, OwningAccount">C:\Users\<USER>\Projects\the-jungle-agents\tests\infrastructure\test_deployment.py:285: CloudWatch not available in test environment: Parameter validation failed:
Unknown parameter in input: "MaxRecords", must be one of: Namespace, MetricName, Dimensions, NextToken, RecentlyActive, IncludeLinkedAccounts, OwningAccount</skipped></testcase><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_iam_roles_configuration" time="0.833" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_kms_configuration" time="0.728" /><testcase classname="tests.infrastructure.test_deployment.TestInfrastructureDeployment" name="test_terraform_outputs_validation" time="0.001" /><testcase classname="tests.infrastructure.test_deployment.TestDeploymentValidation" name="test_health_check_endpoints" time="0.001" /><testcase classname="tests.infrastructure.test_deployment.TestDeploymentValidation" name="test_cors_configuration" time="0.001" /><testcase classname="tests.infrastructure.test_deployment.TestDeploymentValidation" name="test_error_handling_configuration" time="0.001"><failure message="KeyError: 'error'">self = &lt;infrastructure.test_deployment.TestDeploymentValidation object at 0x000001BAAEB11FA0&gt;

    def test_error_handling_configuration(self):
        """Test error handling configuration."""
        from src.shared.exceptions import PlatformException, ValidationException
        from src.shared.responses import APIResponse
    
        # Test exception handling
        try:
&gt;           raise ValidationException(
                "Test validation error",
                validation_errors=[{"field": "test", "message": "test error"}]
            )
E           src.shared.exceptions.ValidationException: Test validation error

tests\infrastructure\test_deployment.py:373: ValidationException

During handling of the above exception, another exception occurred:

self = &lt;infrastructure.test_deployment.TestDeploymentValidation object at 0x000001BAAEB11FA0&gt;

    def test_error_handling_configuration(self):
        """Test error handling configuration."""
        from src.shared.exceptions import PlatformException, ValidationException
        from src.shared.responses import APIResponse
    
        # Test exception handling
        try:
            raise ValidationException(
                "Test validation error",
                validation_errors=[{"field": "test", "message": "test error"}]
            )
        except ValidationException as e:
            response = APIResponse.validation_error(
                message=e.message,
                validation_errors=e.validation_errors
            )
    
            assert response['statusCode'] == 422
            response_body = json.loads(response['body'])
            assert response_body['success'] is False
&gt;           assert 'validation_errors' in response_body['error']
E           KeyError: 'error'

tests\infrastructure\test_deployment.py:386: KeyError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_terraform_directory_structure" time="0.001" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_required_terraform_modules" time="0.003" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_terraform_module_syntax" time="0.091" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_environment_configurations" time="0.020"><failure message="AssertionError: File variables.tf not found in environment prod&#10;assert False&#10; +  where False = &lt;bound method Path.exists of WindowsPath('C:/Users/<USER>/Projects/the-jungle-agents/terraform/environments/prod/variables.tf')&gt;()&#10; +    where &lt;bound method Path.exists of WindowsPath('C:/Users/<USER>/Projects/the-jungle-agents/terraform/environments/prod/variables.tf')&gt; = WindowsPath('C:/Users/<USER>/Projects/the-jungle-agents/terraform/environments/prod/variables.tf').exists">self = &lt;infrastructure.test_terraform_validation.TestTerraformModules object at 0x000001BAAEB11CA0&gt;

    def test_environment_configurations(self):
        """Test environment-specific Terraform configurations."""
        environments = ['dev', 'staging', 'prod']
    
        for env in environments:
            env_dir = self.environments_dir / env
    
            # Test required files
            required_files = ['main.tf', 'variables.tf', 'outputs.tf']
            for file in required_files:
                file_path = env_dir / file
&gt;               assert file_path.exists(), f"File {file} not found in environment {env}"
E               AssertionError: File variables.tf not found in environment prod
E               assert False
E                +  where False = &lt;bound method Path.exists of WindowsPath('C:/Users/<USER>/Projects/the-jungle-agents/terraform/environments/prod/variables.tf')&gt;()
E                +    where &lt;bound method Path.exists of WindowsPath('C:/Users/<USER>/Projects/the-jungle-agents/terraform/environments/prod/variables.tf')&gt; = WindowsPath('C:/Users/<USER>/Projects/the-jungle-agents/terraform/environments/prod/variables.tf').exists

tests\infrastructure\test_terraform_validation.py:106: AssertionError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_terraform_variables" time="0.012"><failure message="AssertionError: Variable environment not found in dev/variables.tf&#10;assert 'variable &quot;environment&quot;' in '# terraform/environments/dev/variables.tf\n# Implementado segÃºn &quot;Infrastructure as Code Configuration&quot;\n\n# Import shared variables\nvariable &quot;project_name&quot; {\n  description = &quot;Name of the project&quot;\n  type        = string\n  default     = &quot;platform&quot;\n}\n\nvariable &quot;aws_region&quot; {\n  description = &quot;AWS region&quot;\n  type        = string\n  default     = &quot;us-east-1&quot;\n}\n\n# ðŸš¨ CONSULTA REQUERIDA: Configurar estos valores\n# Ver documento &quot;Information Gaps &amp; Clarification Requirements&quot;\nvariable &quot;aws_account_id&quot; {\n  description = &quot;AWS Account ID&quot;\n  type        = string\n  default     = &quot;&quot;\n}\n\nvariable &quot;domain_name&quot; {\n  description = &quot;Domain name for the application&quot;\n  type        = string\n  default     = &quot;&quot;\n}\n\nvariable &quot;ssl_certificate_arn&quot; {\n  description = &quot;ARN of the SSL certificate&quot;\n  type        = string\n  default     = &quot;&quot;\n}\n\nvariable &quot;common_tags&quot; {\n  description = &quot;Common tags to apply to all resources&quot;\n  type        = map(string)\n  default = {\n    Project     = &quot;Platform Multitenant Agents&quot;\n    ManagedBy   = &quot;Terraform&quot;\n    Owner       = &quot;Platform Team&quot;\n    Environment = &quot;dev&quot;\n  }\n}\n\n# VPC Configuration\nvariable &quot;vpc_cidr&quot; {\n  description = &quot;CIDR...lt     = false  # Disabled for dev to save costs\n}\n\nvariable &quot;s3_lifecycle_enabled&quot; {\n  description = &quot;Enable S3 lifecycle management&quot;\n  type        = bool\n  default     = false  # Disabled for dev\n}\n\n# Security Configuration\nvariable &quot;kms_key_id&quot; {\n  description = &quot;KMS key ID for encryption (optional)&quot;\n  type        = string\n  default     = &quot;&quot;\n}\n\n# Monitoring Configuration\nvariable &quot;enable_monitoring&quot; {\n  description = &quot;Enable CloudWatch monitoring and alarms&quot;\n  type        = bool\n  default     = true\n}\n\nvariable &quot;alarm_email&quot; {\n  description = &quot;Email address for alarm notifications&quot;\n  type        = string\n  default     = &quot;&quot;\n}\n\n# Environment-specific configuration\nvariable &quot;environment_config&quot; {\n  description = &quot;Environment-specific configuration&quot;\n  type = map(object({\n    instance_count          = number\n    enable_nat_gateway     = bool\n    deletion_protection    = bool\n    monitoring_level       = string\n  }))\n  default = {\n    dev = {\n      instance_count       = 1\n      enable_nat_gateway  = false  # Save costs in dev\n      deletion_protection = false  # Allow easy cleanup in dev\n      monitoring_level    = &quot;basic&quot;\n    }\n  }\n}\n'">self = &lt;infrastructure.test_terraform_validation.TestTerraformModules object at 0x000001BAAEB12E40&gt;

    def test_terraform_variables(self):
        """Test Terraform variables configuration."""
        for env in ['dev', 'staging', 'prod']:
            variables_tf = self.environments_dir / env / "variables.tf"
            content = variables_tf.read_text()
    
            # Check for required variables
            required_variables = [
                'aws_region',
                'project_name',
                'environment'
            ]
    
            for var in required_variables:
&gt;               assert f'variable "{var}"' in content, f"Variable {var} not found in {env}/variables.tf"
E               AssertionError: Variable environment not found in dev/variables.tf
E               assert 'variable "environment"' in '# terraform/environments/dev/variables.tf\n# Implementado segÃºn "Infrastructure as Code Configuration"\n\n# Import shared variables\nvariable "project_name" {\n  description = "Name of the project"\n  type        = string\n  default     = "platform"\n}\n\nvariable "aws_region" {\n  description = "AWS region"\n  type        = string\n  default     = "us-east-1"\n}\n\n# ðŸš¨ CONSULTA REQUERIDA: Configurar estos valores\n# Ver documento "Information Gaps &amp; Clarification Requirements"\nvariable "aws_account_id" {\n  description = "AWS Account ID"\n  type        = string\n  default     = ""\n}\n\nvariable "domain_name" {\n  description = "Domain name for the application"\n  type        = string\n  default     = ""\n}\n\nvariable "ssl_certificate_arn" {\n  description = "ARN of the SSL certificate"\n  type        = string\n  default     = ""\n}\n\nvariable "common_tags" {\n  description = "Common tags to apply to all resources"\n  type        = map(string)\n  default = {\n    Project     = "Platform Multitenant Agents"\n    ManagedBy   = "Terraform"\n    Owner       = "Platform Team"\n    Environment = "dev"\n  }\n}\n\n# VPC Configuration\nvariable "vpc_cidr" {\n  description = "CIDR...lt     = false  # Disabled for dev to save costs\n}\n\nvariable "s3_lifecycle_enabled" {\n  description = "Enable S3 lifecycle management"\n  type        = bool\n  default     = false  # Disabled for dev\n}\n\n# Security Configuration\nvariable "kms_key_id" {\n  description = "KMS key ID for encryption (optional)"\n  type        = string\n  default     = ""\n}\n\n# Monitoring Configuration\nvariable "enable_monitoring" {\n  description = "Enable CloudWatch monitoring and alarms"\n  type        = bool\n  default     = true\n}\n\nvariable "alarm_email" {\n  description = "Email address for alarm notifications"\n  type        = string\n  default     = ""\n}\n\n# Environment-specific configuration\nvariable "environment_config" {\n  description = "Environment-specific configuration"\n  type = map(object({\n    instance_count          = number\n    enable_nat_gateway     = bool\n    deletion_protection    = bool\n    monitoring_level       = string\n  }))\n  default = {\n    dev = {\n      instance_count       = 1\n      enable_nat_gateway  = false  # Save costs in dev\n      deletion_protection = false  # Allow easy cleanup in dev\n      monitoring_level    = "basic"\n    }\n  }\n}\n'

tests\infrastructure\test_terraform_validation.py:135: AssertionError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_terraform_outputs" time="0.012"><failure message="UnicodeDecodeError: 'charmap' codec can't decode byte 0x8d in position 3358: character maps to &lt;undefined&gt;">self = &lt;infrastructure.test_terraform_validation.TestTerraformModules object at 0x000001BAAEB13050&gt;

    def test_terraform_outputs(self):
        """Test Terraform outputs configuration."""
        for env in ['dev', 'staging', 'prod']:
            outputs_tf = self.environments_dir / env / "outputs.tf"
&gt;           content = outputs_tf.read_text()

tests\infrastructure\test_terraform_validation.py:141: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\pathlib.py:1028: in read_text
    return f.read()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;encodings.cp1252.IncrementalDecoder object at 0x000001BAB2E12DB0&gt;
input = b'# terraform/environments/dev/outputs.tf\n# Implementado seg\xc3\xban "Infrastructure as Code Configuration"\n\n# VPC....stage_invoke_url\n    environment                 = "dev"\n    aws_region                  = var.aws_region\n  }\n}\n'
final = True

    def decode(self, input, final=False):
&gt;       return codecs.charmap_decode(input,self.errors,decoding_table)[0]
E       UnicodeDecodeError: 'charmap' codec can't decode byte 0x8d in position 3358: character maps to &lt;undefined&gt;

C:\Python312\Lib\encodings\cp1252.py:23: UnicodeDecodeError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_module_variables_consistency" time="0.088" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformModules" name="test_module_outputs_consistency" time="0.102" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformSecurity" name="test_no_hardcoded_secrets" time="0.011"><failure message="Failed: Potential hardcoded secret in C:\Users\<USER>\Projects\the-jungle-agents\terraform\shared\variables.tf:124: description = &quot;kms key id for encryption (optional)&quot;">self = &lt;infrastructure.test_terraform_validation.TestTerraformSecurity object at 0x000001BAAEB137A0&gt;

    def test_no_hardcoded_secrets(self):
        """Test that no secrets are hardcoded in Terraform files."""
        sensitive_patterns = [
            'password',
            'secret',
            'key',
            'token',
            'credential'
        ]
    
        for tf_file in self.terraform_dir.rglob("*.tf"):
            content = tf_file.read_text().lower()
    
            for pattern in sensitive_patterns:
                # Check for potential hardcoded values
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if pattern in line and '=' in line:
                        # Skip variable declarations and references
                        if 'variable' in line or 'var.' in line or 'data.' in line:
                            continue
    
                        # Skip comments
                        if line.strip().startswith('#'):
                            continue
    
                        # Check for hardcoded values (not variables or data sources)
                        if '"' in line and 'var.' not in line and 'data.' not in line:
                            # This might be a hardcoded secret
&gt;                           pytest.fail(f"Potential hardcoded secret in {tf_file}:{i}: {line.strip()}")
E                           Failed: Potential hardcoded secret in C:\Users\<USER>\Projects\the-jungle-agents\terraform\shared\variables.tf:124: description = "kms key id for encryption (optional)"

tests\infrastructure\test_terraform_validation.py:223: Failed</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformSecurity" name="test_encryption_configuration" time="0.002" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformSecurity" name="test_iam_least_privilege" time="0.001"><failure message="AttributeError: module 'pytest' has no attribute 'warning'">self = &lt;infrastructure.test_terraform_validation.TestTerraformSecurity object at 0x000001BAAEB13B60&gt;

    def test_iam_least_privilege(self):
        """Test that IAM policies follow least privilege principle."""
        iam_main = self.terraform_dir / "modules" / "iam" / "main.tf"
        if iam_main.exists():
            content = iam_main.read_text()
    
            # Should not have overly permissive policies
            dangerous_permissions = ['*:*', '"*"']
            for permission in dangerous_permissions:
                if permission in content:
                    # Check if it's in a comment or properly scoped
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if permission in line and not line.strip().startswith('#'):
                            # This might be overly permissive
&gt;                           pytest.warning(f"Potentially overly permissive IAM policy in {iam_main}:{i}")
E                           AttributeError: module 'pytest' has no attribute 'warning'

tests\infrastructure\test_terraform_validation.py:255: AttributeError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformBestPractices" name="test_resource_naming_conventions" time="0.002"><failure message="UnicodeDecodeError: 'charmap' codec can't decode byte 0x8d in position 3358: character maps to &lt;undefined&gt;">self = &lt;infrastructure.test_terraform_validation.TestTerraformBestPractices object at 0x000001BAAEB13E90&gt;

    def test_resource_naming_conventions(self):
        """Test that resources follow naming conventions."""
        for tf_file in self.terraform_dir.rglob("*.tf"):
&gt;           content = tf_file.read_text()

tests\infrastructure\test_terraform_validation.py:270: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\pathlib.py:1028: in read_text
    return f.read()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;encodings.cp1252.IncrementalDecoder object at 0x000001BAB0A115B0&gt;
input = b'# terraform/environments/dev/outputs.tf\n# Implementado seg\xc3\xban "Infrastructure as Code Configuration"\n\n# VPC....stage_invoke_url\n    environment                 = "dev"\n    aws_region                  = var.aws_region\n  }\n}\n'
final = True

    def decode(self, input, final=False):
&gt;       return codecs.charmap_decode(input,self.errors,decoding_table)[0]
E       UnicodeDecodeError: 'charmap' codec can't decode byte 0x8d in position 3358: character maps to &lt;undefined&gt;

C:\Python312\Lib\encodings\cp1252.py:23: UnicodeDecodeError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformBestPractices" name="test_variable_descriptions" time="0.017" /><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformBestPractices" name="test_output_descriptions" time="0.002"><failure message="UnicodeDecodeError: 'charmap' codec can't decode byte 0x8d in position 3358: character maps to &lt;undefined&gt;">self = &lt;infrastructure.test_terraform_validation.TestTerraformBestPractices object at 0x000001BAAEB2CA10&gt;

    def test_output_descriptions(self):
        """Test that outputs have descriptions."""
        for outputs_tf in self.terraform_dir.rglob("outputs.tf"):
&gt;           content = outputs_tf.read_text()

tests\infrastructure\test_terraform_validation.py:303: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\pathlib.py:1028: in read_text
    return f.read()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;encodings.cp1252.IncrementalDecoder object at 0x000001BAB09910A0&gt;
input = b'# terraform/environments/dev/outputs.tf\n# Implementado seg\xc3\xban "Infrastructure as Code Configuration"\n\n# VPC....stage_invoke_url\n    environment                 = "dev"\n    aws_region                  = var.aws_region\n  }\n}\n'
final = True

    def decode(self, input, final=False):
&gt;       return codecs.charmap_decode(input,self.errors,decoding_table)[0]
E       UnicodeDecodeError: 'charmap' codec can't decode byte 0x8d in position 3358: character maps to &lt;undefined&gt;

C:\Python312\Lib\encodings\cp1252.py:23: UnicodeDecodeError</failure></testcase><testcase classname="tests.infrastructure.test_terraform_validation.TestTerraformBestPractices" name="test_terraform_version_constraints" time="0.013" /></testsuite></testsuites>