<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="14" skipped="4" tests="26" time="12.762" timestamp="2025-07-29T15:26:54.232549" hostname="ANDERSON-APES-DESKTOP"><testcase classname="tests.integration.payment.test_subscription_flow.TestSubscriptionFlow" name="test_list_plans_flow" time="0.013"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;payment.test_subscription_flow.TestSubscriptionFlow object at 0x000001CC56D7E780&gt;
mock_tenant_db = &lt;MagicMock name='db_client' id='1977135782256'&gt;
mock_sub_db = &lt;MagicMock name='db_client' id='1977142403552'&gt;
mock_plan_db = &lt;MagicMock name='db_client' id='1977142407344'&gt;
lambda_event = {'body': None, 'headers': {'Content-Type': 'application/json'}, 'httpMethod': 'GET', 'path': '/payment/plans', ...}

    @patch('src.payment.models.plan.db_client')
    @patch('src.payment.models.subscription.db_client')
    @patch('src.auth.models.tenant.db_client')
    def test_list_plans_flow(self, mock_tenant_db, mock_sub_db, mock_plan_db, lambda_event):
        """Test listing available plans."""
        # Setup
        lambda_event['httpMethod'] = 'GET'
        lambda_event['path'] = '/payment/plans'
        lambda_event['body'] = None
    
        # Mock plan data
        mock_plan_db.query.return_value = {
            'Items': [
                {
                    'plan_id': 'plan-free',
                    'name': 'Free Plan',
                    'plan_type': 'FREE',
                    'status': 'ACTIVE',
                    'monthly_price': '0.00',
                    'yearly_price': '0.00',
                    'features': ['basic_chat'],
                    'limits': {'max_users': 1},
                    'trial_days': 0,
                    'created_at': 1234567890,
                    'updated_at': 1234567890
                },
                {
                    'plan_id': 'plan-basic',
                    'name': 'Basic Plan',
                    'plan_type': 'BASIC',
                    'status': 'ACTIVE',
                    'monthly_price': '29.99',
                    'yearly_price': '299.99',
                    'features': ['basic_chat', 'email_support'],
                    'limits': {'max_users': 5, 'storage_gb': 5},
                    'trial_days': 14,
                    'created_at': 1234567890,
                    'updated_at': 1234567890
                }
            ]
        }
    
        # Execute
&gt;       response = list_plans_handler(lambda_event, {})

tests\integration\payment\test_subscription_flow.py:111: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\payment\handlers\list_plans.py:39: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x000001CC53723890&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.integration.payment.test_subscription_flow.TestSubscriptionFlow" name="test_create_subscription_flow" time="0.030"><failure message="assert 401 == 201">self = &lt;payment.test_subscription_flow.TestSubscriptionFlow object at 0x000001CC56D7E600&gt;
mock_tenant_db = &lt;MagicMock name='db_client' id='1977142721568'&gt;
mock_sub_db = &lt;MagicMock name='db_client' id='1977142718016'&gt;
mock_plan_db = &lt;MagicMock name='db_client' id='1977143836992'&gt;
mock_stripe = &lt;MagicMock name='stripe_service' id='1977143840784'&gt;
auth_context = &lt;Mock spec='AuthContext' id='1977142721040'&gt;
basic_plan = &lt;src.payment.models.plan.Plan object at 0x000001CC56E3B9B0&gt;
lambda_event = {'auth_context': &lt;Mock spec='AuthContext' id='1977142721040'&gt;, 'body': '{"plan_id": "plan-basic", "billing_interval": "MONTHLY"}', 'headers': {'Content-Type': 'application/json'}, 'httpMethod': 'POST', ...}

    @patch('src.payment.services.stripe_service.stripe_service')
    @patch('src.payment.models.plan.db_client')
    @patch('src.payment.models.subscription.db_client')
    @patch('src.auth.models.tenant.db_client')
    def test_create_subscription_flow(self, mock_tenant_db, mock_sub_db, mock_plan_db, mock_stripe, auth_context, basic_plan, lambda_event):
        """Test creating a subscription."""
        # Setup
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'plan_id': 'plan-basic',
            'billing_interval': 'MONTHLY'
        })
    
        # Mock plan lookup
        mock_plan_db.get_item.return_value = {
            'plan_id': 'plan-basic',
            'name': 'Basic Plan',
            'plan_type': 'BASIC',
            'status': 'ACTIVE',
            'monthly_price': '29.99',
            'yearly_price': '299.99',
            'features': ['basic_chat', 'email_support'],
            'limits': {'max_users': 5, 'storage_gb': 5},
            'trial_days': 14,
            'stripe_monthly_price_id': 'price_123',
            'created_at': 1234567890,
            'updated_at': 1234567890
        }
    
        # Mock tenant lookup
        mock_tenant_db.get_item.return_value = {
            'tenant_id': 'tenant-123',
            'name': 'Test Tenant',
            'master_user_email': '<EMAIL>',
            'status': 'ACTIVE',
            'plan': 'FREE',
            'created_at': 1234567890,
            'updated_at': 1234567890
        }
    
        # Mock existing subscription check
        mock_sub_db.query.return_value = {'Items': []}
    
        # Mock Stripe service
        mock_stripe.create_customer.return_value = {
            'customer_id': 'cus_123',
            'email': '<EMAIL>',
            'name': 'Test Tenant'
        }
    
        mock_stripe.create_subscription.return_value = {
            'subscription_id': 'sub_123',
            'status': 'trialing',
            'current_period_start': 1234567890,
            'current_period_end': 1234567890 + (30 * 24 * 60 * 60),
            'trial_end': 1234567890 + (14 * 24 * 60 * 60),
            'client_secret': 'pi_123_secret_456'
        }
    
        # Execute
        response = create_subscription_handler(lambda_event, {})
    
        # Verify
&gt;       assert response['statusCode'] == 201
E       assert 401 == 201

tests\integration\payment\test_subscription_flow.py:193: AssertionError</failure></testcase><testcase classname="tests.integration.payment.test_subscription_flow.TestSubscriptionFlow" name="test_get_subscription_flow" time="0.004"><failure message="assert 401 == 200">self = &lt;payment.test_subscription_flow.TestSubscriptionFlow object at 0x000001CC56D7E9F0&gt;
mock_plan_db = &lt;MagicMock name='db_client' id='1977143178448'&gt;
mock_sub_db = &lt;MagicMock name='db_client' id='1977143363728'&gt;
auth_context = &lt;Mock spec='AuthContext' id='1977143180416'&gt;
lambda_event = {'auth_context': &lt;Mock spec='AuthContext' id='1977143180416'&gt;, 'body': None, 'headers': {'Content-Type': 'application/json'}, 'httpMethod': 'GET', ...}

    @patch('src.payment.models.subscription.db_client')
    @patch('src.payment.models.plan.db_client')
    def test_get_subscription_flow(self, mock_plan_db, mock_sub_db, auth_context, lambda_event):
        """Test getting current subscription."""
        # Setup
        lambda_event['httpMethod'] = 'GET'
        lambda_event['path'] = '/payment/subscription'
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = None
    
        # Mock subscription lookup
        mock_sub_db.query.return_value = {
            'Items': [{
                'subscription_id': 'sub-123',
                'tenant_id': 'tenant-123',
                'plan_id': 'plan-basic',
                'status': 'ACTIVE',
                'billing_interval': 'MONTHLY',
                'amount': '29.99',
                'currency': 'USD',
                'current_period_start': 1234567890,
                'current_period_end': 1234567890 + (30 * 24 * 60 * 60),
                'created_at': 1234567890,
                'updated_at': 1234567890
            }]
        }
    
        # Mock plan lookup
        mock_plan_db.get_item.return_value = {
            'plan_id': 'plan-basic',
            'name': 'Basic Plan',
            'plan_type': 'BASIC',
            'status': 'ACTIVE',
            'monthly_price': '29.99',
            'yearly_price': '299.99',
            'features': ['basic_chat', 'email_support'],
            'limits': {'max_users': 5, 'storage_gb': 5},
            'trial_days': 14,
            'created_at': 1234567890,
            'updated_at': 1234567890
        }
    
        # Execute
        response = get_subscription_handler(lambda_event, {})
    
        # Verify
&gt;       assert response['statusCode'] == 200
E       assert 401 == 200

tests\integration\payment\test_subscription_flow.py:264: AssertionError</failure></testcase><testcase classname="tests.integration.payment.test_subscription_flow.TestSubscriptionFlow" name="test_cancel_subscription_flow" time="0.004"><failure message="assert 401 == 200">self = &lt;payment.test_subscription_flow.TestSubscriptionFlow object at 0x000001CC56D7EBA0&gt;
mock_sub_db = &lt;MagicMock name='db_client' id='1977143377312'&gt;
mock_stripe = &lt;MagicMock name='stripe_service' id='1977143427440'&gt;
auth_context = &lt;Mock spec='AuthContext' id='1977143377408'&gt;
lambda_event = {'auth_context': &lt;Mock spec='AuthContext' id='1977143377408'&gt;, 'body': '{"reason": "No longer needed", "at_period_end": true}', 'headers': {'Content-Type': 'application/json'}, 'httpMethod': 'POST', ...}

    @patch('src.payment.services.stripe_service.stripe_service')
    @patch('src.payment.models.subscription.db_client')
    def test_cancel_subscription_flow(self, mock_sub_db, mock_stripe, auth_context, lambda_event):
        """Test cancelling a subscription."""
        # Setup
        lambda_event['httpMethod'] = 'POST'
        lambda_event['path'] = '/payment/subscription/cancel'
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'reason': 'No longer needed',
            'at_period_end': True
        })
    
        # Mock subscription lookup
        mock_sub_db.query.return_value = {
            'Items': [{
                'subscription_id': 'sub-123',
                'tenant_id': 'tenant-123',
                'plan_id': 'plan-basic',
                'status': 'ACTIVE',
                'billing_interval': 'MONTHLY',
                'amount': '29.99',
                'stripe_subscription_id': 'sub_stripe_123',
                'current_period_start': 1234567890,
                'current_period_end': 1234567890 + (30 * 24 * 60 * 60),
                'created_at': 1234567890,
                'updated_at': 1234567890
            }]
        }
    
        # Mock Stripe cancellation
        mock_stripe.cancel_subscription.return_value = {
            'subscription_id': 'sub_stripe_123',
            'status': 'active',
            'canceled_at': None,
            'cancel_at_period_end': True
        }
    
        # Execute
        response = cancel_subscription_handler(lambda_event, {})
    
        # Verify
&gt;       assert response['statusCode'] == 200
E       assert 401 == 200

tests\integration\payment\test_subscription_flow.py:321: AssertionError</failure></testcase><testcase classname="tests.integration.payment.test_subscription_flow.TestSubscriptionFlow" name="test_subscription_flow_unauthorized_user" time="0.003"><failure message="assert 401 == 403">self = &lt;payment.test_subscription_flow.TestSubscriptionFlow object at 0x000001CC56D7ED20&gt;
lambda_event = {'auth_context': &lt;Mock spec='AuthContext' id='1977143352576'&gt;, 'body': '{"plan_id": "plan-basic", "billing_interval": "MONTHLY"}', 'headers': {'Content-Type': 'application/json'}, 'httpMethod': 'POST', ...}

    def test_subscription_flow_unauthorized_user(self, lambda_event):
        """Test subscription operations with unauthorized user."""
        # Setup unauthorized context
        auth_context = Mock(spec=AuthContext)
        auth_context.tenant_id = "tenant-123"
        auth_context.user_id = "user-456"
        auth_context.role = "MEMBER"
        auth_context.is_master.return_value = False
        auth_context.is_member.return_value = True
    
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'plan_id': 'plan-basic',
            'billing_interval': 'MONTHLY'
        })
    
        # Execute
        response = create_subscription_handler(lambda_event, {})
    
        # Verify
&gt;       assert response['statusCode'] == 403
E       assert 401 == 403

tests\integration\payment\test_subscription_flow.py:357: AssertionError</failure></testcase><testcase classname="tests.integration.payment.test_subscription_flow.TestSubscriptionFlow" name="test_subscription_flow_invalid_plan" time="0.003"><failure message="assert 401 == 404">self = &lt;payment.test_subscription_flow.TestSubscriptionFlow object at 0x000001CC56D7EE70&gt;
auth_context = &lt;Mock spec='AuthContext' id='1977143370400'&gt;
lambda_event = {'auth_context': &lt;Mock spec='AuthContext' id='1977143370400'&gt;, 'body': '{"plan_id": "invalid-plan", "billing_interval": "MONTHLY"}', 'headers': {'Content-Type': 'application/json'}, 'httpMethod': 'POST', ...}

    def test_subscription_flow_invalid_plan(self, auth_context, lambda_event):
        """Test subscription creation with invalid plan."""
        # Setup
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'plan_id': 'invalid-plan',
            'billing_interval': 'MONTHLY'
        })
    
        # Mock plan lookup to return None
        with patch('src.payment.models.plan.db_client') as mock_plan_db:
            mock_plan_db.get_item.return_value = None
    
            # Execute
            response = create_subscription_handler(lambda_event, {})
    
            # Verify
&gt;           assert response['statusCode'] == 404
E           assert 401 == 404

tests\integration\payment\test_subscription_flow.py:381: AssertionError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_complete_registration_flow" time="1.351"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA0650&gt;
dynamodb_mock = dynamodb.ServiceResource()
ses_mock = &lt;botocore.client.SES object at 0x000001CC580425A0&gt;
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_complete_registration_flow(self, dynamodb_mock, ses_mock, mock_environment):
        """Test complete user registration flow."""
        # Mock email service
        with patch('src.auth.services.email_service.EmailService.send_verification_email') as mock_email:
            mock_email.return_value = True
    
            # Prepare registration request
            event = {
                "httpMethod": "POST",
                "path": "/auth/register",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "tenant_name": "Test Company",
                    "user_name": "John Doe",
                    "email": "<EMAIL>",
                    "password": "SecurePassword123!",
                    "phone": "+1234567890"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
    
            context = MagicMock()
    
            # Execute registration
&gt;           response = register.handler(event, context)

tests\integration\test_auth_flow.py:51: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\register.py:55: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x000001CC53723890&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_registration_with_weak_password" time="0.746"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA07D0&gt;
dynamodb_mock = dynamodb.ServiceResource()
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_registration_with_weak_password(self, dynamodb_mock, mock_environment):
        """Test registration with weak password."""
        event = {
            "httpMethod": "POST",
            "path": "/auth/register",
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({
                "tenant_name": "Test Company",
                "user_name": "John Doe",
                "email": "<EMAIL>",
                "password": "weak",  # Weak password
                "phone": "+1234567890"
            }),
            "requestContext": {
                "requestId": "test-request-123",
                "identity": {"sourceIp": "127.0.0.1"}
            }
        }
    
        context = MagicMock()
    
        # Execute registration
&gt;       response = register.handler(event, context)

tests\integration\test_auth_flow.py:90: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\register.py:55: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x000001CC53723890&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_registration_duplicate_email" time="0.728"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA09B0&gt;
dynamodb_mock = dynamodb.ServiceResource()
test_helpers = &lt;class 'conftest.TestHelpers'&gt;
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_registration_duplicate_email(self, dynamodb_mock, test_helpers, mock_environment):
        """Test registration with duplicate email."""
        # Create existing tenant and user
        test_helpers.create_test_tenant(dynamodb_mock)
        test_helpers.create_test_user(dynamodb_mock)
    
        with patch('src.auth.models.user.User.get_by_email') as mock_get_user:
            # Mock existing user
            existing_user = User(
                user_id="existing-user",
                tenant_id="existing-tenant",
                email="<EMAIL>",
                name="Existing User"
            )
            mock_get_user.return_value = existing_user
    
            event = {
                "httpMethod": "POST",
                "path": "/auth/register",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "tenant_name": "Test Company",
                    "user_name": "John Doe",
                    "email": "<EMAIL>",  # Duplicate email
                    "password": "SecurePassword123!",
                    "phone": "+1234567890"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
    
            context = MagicMock()
    
            # Execute registration
&gt;           response = register.handler(event, context)

tests\integration\test_auth_flow.py:136: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\register.py:55: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x000001CC53723890&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_login_flow_success" time="0.685"><failure message="AttributeError: &lt;module 'src.auth.handlers.login' from 'C:\\Users\\<USER>\\Projects\\the-jungle-agents\\src\\auth\\handlers\\login.py'&gt; does not have the attribute '_find_user_by_email'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA0B90&gt;
dynamodb_mock = dynamodb.ServiceResource()
test_helpers = &lt;class 'conftest.TestHelpers'&gt;
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_login_flow_success(self, dynamodb_mock, test_helpers, mock_environment):
        """Test successful login flow."""
        # Create test tenant and user
        tenant_data = test_helpers.create_test_tenant(dynamodb_mock)
        user_data = test_helpers.create_test_user(dynamodb_mock)
    
        # Add required fields for authentication
        user_data.update({
            "password_hash": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.e",  # "password123"
            "email_verified": True,
            "login_attempts": 0
        })
    
        with patch('src.auth.models.user.User.get_by_email') as mock_get_user, \
             patch('src.auth.models.tenant.Tenant.get_by_id') as mock_get_tenant, \
&gt;            patch('src.auth.handlers.login._find_user_by_email') as mock_find_user:

tests\integration\test_auth_flow.py:160: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1461: in __enter__
    original, local = self.get_original()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;unittest.mock._patch object at 0x000001CC580434A0&gt;

    def get_original(self):
        target = self.getter()
        name = self.attribute
    
        original = DEFAULT
        local = False
    
        try:
            original = target.__dict__[name]
        except (AttributeError, KeyError):
            original = getattr(target, name, DEFAULT)
        else:
            local = True
    
        if name in _builtins and isinstance(target, ModuleType):
            self.create = True
    
        if not self.create and original is DEFAULT:
&gt;           raise AttributeError(
                "%s does not have the attribute %r" % (target, name)
            )
E           AttributeError: &lt;module 'src.auth.handlers.login' from 'C:\\Users\\<USER>\\Projects\\the-jungle-agents\\src\\auth\\handlers\\login.py'&gt; does not have the attribute '_find_user_by_email'

C:\Python312\Lib\unittest\mock.py:1434: AttributeError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_login_invalid_credentials" time="0.004"><failure message="AttributeError: &lt;module 'src.auth.handlers.login' from 'C:\\Users\\<USER>\\Projects\\the-jungle-agents\\src\\auth\\handlers\\login.py'&gt; does not have the attribute '_find_user_by_email'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA0D70&gt;
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_login_invalid_credentials(self, mock_environment):
        """Test login with invalid credentials."""
&gt;       with patch('src.auth.handlers.login._find_user_by_email') as mock_find_user:

tests\integration\test_auth_flow.py:208: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1461: in __enter__
    original, local = self.get_original()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;unittest.mock._patch object at 0x000001CC51A4A4B0&gt;

    def get_original(self):
        target = self.getter()
        name = self.attribute
    
        original = DEFAULT
        local = False
    
        try:
            original = target.__dict__[name]
        except (AttributeError, KeyError):
            original = getattr(target, name, DEFAULT)
        else:
            local = True
    
        if name in _builtins and isinstance(target, ModuleType):
            self.create = True
    
        if not self.create and original is DEFAULT:
&gt;           raise AttributeError(
                "%s does not have the attribute %r" % (target, name)
            )
E           AttributeError: &lt;module 'src.auth.handlers.login' from 'C:\\Users\\<USER>\\Projects\\the-jungle-agents\\src\\auth\\handlers\\login.py'&gt; does not have the attribute '_find_user_by_email'

C:\Python312\Lib\unittest\mock.py:1434: AttributeError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_token_refresh_flow" time="0.006"><failure message="AttributeError: 'Logger' object has no attribute 'get_current_timestamp'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA0EC0&gt;
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_token_refresh_flow(self, mock_environment):
        """Test token refresh flow."""
        with patch('src.shared.auth.jwt_manager.verify_token') as mock_verify, \
             patch('src.auth.models.user.User.get_by_id') as mock_get_user, \
             patch('src.auth.models.tenant.Tenant.get_by_id') as mock_get_tenant:
    
            # Mock token verification
            mock_verify.return_value = {
                "sub": "user-123",
                "tenant_id": "tenant-123",
                "type": "refresh"
            }
    
            # Mock user and tenant
            user = User(
                user_id="user-123",
                tenant_id="tenant-123",
                email="<EMAIL>",
                name="Test User",
                status="ACTIVE"
            )
            tenant = Tenant(
                tenant_id="tenant-123",
                name="Test Company",
                status="ACTIVE"
            )
    
            mock_get_user.return_value = user
            mock_get_tenant.return_value = tenant
    
            event = {
                "httpMethod": "POST",
                "path": "/auth/refresh",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "refresh_token": "valid_refresh_token"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
    
            context = MagicMock()
    
            # Execute refresh
&gt;           response = refresh.handler(event, context)

tests\integration\test_auth_flow.py:283: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
src\auth\handlers\refresh.py:53: in handler
    start_time = lambda_logger.get_current_timestamp()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;aws_lambda_powertools.logging.logger.Logger object at 0x000001CC53723890&gt;
name = 'get_current_timestamp'

    def __getattr__(self, name):
        # Proxy attributes not found to actual logger to support backward compatibility
        # https://github.com/aws-powertools/powertools-lambda-python/issues/97
&gt;       return getattr(self._logger, name)
E       AttributeError: 'Logger' object has no attribute 'get_current_timestamp'

..\..\AppData\Roaming\Python\Python312\site-packages\aws_lambda_powertools\logging\logger.py:278: AttributeError</failure></testcase><testcase classname="tests.integration.test_auth_flow.TestAuthenticationFlow" name="test_email_verification_flow" time="0.520"><failure message="NameError: name 'time' is not defined. Did you forget to import 'time'">self = &lt;test_auth_flow.TestAuthenticationFlow object at 0x000001CC56DA11F0&gt;
dynamodb_mock = dynamodb.ServiceResource()
test_helpers = &lt;class 'conftest.TestHelpers'&gt;
mock_environment = {'AWS_REGION': 'us-east-1', 'DYNAMODB_TABLE': 'platform-main-test', 'ENVIRONMENT': 'test', 'FROM_EMAIL': '<EMAIL>', ...}

    def test_email_verification_flow(self, dynamodb_mock, test_helpers, mock_environment):
        """Test email verification flow."""
        # Create test user with verification token
        user_data = test_helpers.create_test_user(dynamodb_mock)
        user_data.update({
            "email_verification_token": "valid_token_123",
&gt;           "email_verification_expires": int(time.time()) + 3600,  # 1 hour from now
            "email_verified": False
        })
E       NameError: name 'time' is not defined. Did you forget to import 'time'

tests\integration\test_auth_flow.py:299: NameError</failure></testcase><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_complete_user_registration_flow" time="0.009" /><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_complete_payment_flow" time="0.007" /><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_multi_tenant_isolation" time="0.008" /><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_subscription_lifecycle" time="0.010" /><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_plan_management" time="0.010" /><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_user_permissions_and_roles" time="0.006" /><testcase classname="tests.integration.test_complete_system_flow.TestCompleteSystemFlow" name="test_validation_and_error_handling" time="0.008" /><testcase classname="tests.integration.test_stripe_integration.TestStripeIntegration" name="test_complete_subscription_flow" time="0.003"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.integration.test_stripe_integration.TestStripeIntegration" name="test_webhook_subscription_updated_flow" time="0.001"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.integration.test_stripe_integration.TestStripeIntegration" name="test_subscription_cancellation_flow" time="0.002"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.integration.test_stripe_integration.TestStripeIntegration" name="test_customer_sync_flow" time="0.002"><skipped type="pytest.skip" message="async def function and no async plugin installed (see warnings)">C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\_pytest\python.py:152: async def function and no async plugin installed (see warnings)</skipped></testcase><testcase classname="tests.integration.test_stripe_integration.TestStripeIntegration" name="test_error_handling_integration" time="0.003" /><testcase classname="tests.integration.test_stripe_integration.TestStripeIntegration" name="test_webhook_signature_verification" time="0.005"><failure message="src.shared.exceptions.PaymentException: Failed to process webhook: catching classes that do not inherit from BaseException is not allowed">self = &lt;src.payment.services.stripe_client.StripeClientService object at 0x000001CC56D32E70&gt;
payload = '{"id": "evt_test123"}', signature = 't=123,v1=invalid_signature'
endpoint_secret = 'whsec_test', tolerance = 300

    def construct_webhook_event(
        self,
        payload: str,
        signature: str,
        endpoint_secret: str,
        tolerance: int = 300
    ) -&gt; stripe.Event:
        """Construct and verify webhook event with timestamp tolerance."""
        try:
            # Validate inputs
            if not payload:
                raise ValidationException("Webhook payload cannot be empty")
    
            if not signature:
                raise ValidationException("Webhook signature cannot be empty")
    
            if not endpoint_secret:
                raise ValidationException("Webhook endpoint secret cannot be empty")
    
            # Construct event with tolerance for timestamp validation
&gt;           event = stripe.Webhook.construct_event(
                payload, signature, endpoint_secret, tolerance
            )

src\payment\services\stripe_client.py:457: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Python312\Lib\unittest\mock.py:1137: in __call__
    return self._mock_call(*args, **kwargs)
C:\Python312\Lib\unittest\mock.py:1141: in _mock_call
    return self._execute_mock_call(*args, **kwargs)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;MagicMock name='stripe.Webhook.construct_event' id='1977169839072'&gt;
args = ('{"id": "evt_test123"}', 't=123,v1=invalid_signature', 'whsec_test', 300)
kwargs = {}
effect = SignatureVerificationError(message='Invalid signature', http_status=None, request_id=None)

    def _execute_mock_call(self, /, *args, **kwargs):
        # separate from _increment_mock_call so that awaited functions are
        # executed separately from their call, also AsyncMock overrides this method
    
        effect = self.side_effect
        if effect is not None:
            if _is_exception(effect):
&gt;               raise effect
E               stripe._error.SignatureVerificationError: Invalid signature

C:\Python312\Lib\unittest\mock.py:1196: SignatureVerificationError

During handling of the above exception, another exception occurred:

self = &lt;src.payment.services.webhook_service.WebhookService object at 0x000001CC56DA3020&gt;
payload = '{"id": "evt_test123"}', signature = 't=123,v1=invalid_signature'
endpoint_secret = 'whsec_test', tolerance = 300

    def process_webhook(
        self,
        payload: str,
        signature: str,
        endpoint_secret: str,
        tolerance: int = 300
    ) -&gt; Dict[str, Any]:
        """
        Process a Stripe webhook event with security validation.
    
        Args:
            payload: Raw webhook payload
            signature: Stripe signature header
            endpoint_secret: Webhook endpoint secret
            tolerance: Timestamp tolerance in seconds (default: 5 minutes)
    
        Returns:
            Processing result
    
        Raises:
            ValidationException: If webhook verification fails
            PaymentException: If event processing fails
        """
        try:
            # Verify webhook signature with timestamp tolerance
&gt;           event = self.stripe_client.construct_webhook_event(
                payload, signature, endpoint_secret, tolerance
            )

src\payment\services\webhook_service.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.payment.services.stripe_client.StripeClientService object at 0x000001CC56D32E70&gt;
payload = '{"id": "evt_test123"}', signature = 't=123,v1=invalid_signature'
endpoint_secret = 'whsec_test', tolerance = 300

    def construct_webhook_event(
        self,
        payload: str,
        signature: str,
        endpoint_secret: str,
        tolerance: int = 300
    ) -&gt; stripe.Event:
        """Construct and verify webhook event with timestamp tolerance."""
        try:
            # Validate inputs
            if not payload:
                raise ValidationException("Webhook payload cannot be empty")
    
            if not signature:
                raise ValidationException("Webhook signature cannot be empty")
    
            if not endpoint_secret:
                raise ValidationException("Webhook endpoint secret cannot be empty")
    
            # Construct event with tolerance for timestamp validation
            event = stripe.Webhook.construct_event(
                payload, signature, endpoint_secret, tolerance
            )
    
            # Additional security validation
            if not event.get('id'):
                raise ValidationException("Webhook event missing required ID")
    
            if not event.get('type'):
                raise ValidationException("Webhook event missing required type")
    
            return event
    
        except ValueError as e:
            # Invalid payload
            logger.warning(f"Invalid webhook payload: {str(e)}")
            raise ValidationException(f"Invalid webhook payload: {str(e)}")
&gt;       except stripe.error.SignatureVerificationError as e:
E       TypeError: catching classes that do not inherit from BaseException is not allowed

src\payment\services\stripe_client.py:474: TypeError

During handling of the above exception, another exception occurred:

self = &lt;test_stripe_integration.TestStripeIntegration object at 0x000001CC56DEC200&gt;
mock_stripe = &lt;MagicMock name='stripe' id='1977169626992'&gt;

    @patch('src.payment.services.stripe_client.stripe')
    def test_webhook_signature_verification(self, mock_stripe):
        """Test webhook signature verification."""
        # Test valid signature
        mock_event = {"id": "evt_test123", "type": "customer.created"}
        mock_stripe.Webhook.construct_event.return_value = mock_event
    
        result = webhook_service.process_webhook(
            payload='{"id": "evt_test123"}',
            signature="t=123,v1=valid_signature",
            endpoint_secret="whsec_test"
        )
    
        assert result["success"] is True
    
        # Test invalid signature
        mock_stripe.Webhook.construct_event.side_effect = stripe.error.SignatureVerificationError(
            message="Invalid signature",
            sig_header="invalid"
        )
    
        from src.shared.exceptions import ValidationException
    
        with pytest.raises(ValidationException) as exc_info:
&gt;           webhook_service.process_webhook(
                payload='{"id": "evt_test123"}',
                signature="t=123,v1=invalid_signature",
                endpoint_secret="whsec_test"
            )

tests\integration\test_stripe_integration.py:303: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = &lt;src.payment.services.webhook_service.WebhookService object at 0x000001CC56DA3020&gt;
payload = '{"id": "evt_test123"}', signature = 't=123,v1=invalid_signature'
endpoint_secret = 'whsec_test', tolerance = 300

    def process_webhook(
        self,
        payload: str,
        signature: str,
        endpoint_secret: str,
        tolerance: int = 300
    ) -&gt; Dict[str, Any]:
        """
        Process a Stripe webhook event with security validation.
    
        Args:
            payload: Raw webhook payload
            signature: Stripe signature header
            endpoint_secret: Webhook endpoint secret
            tolerance: Timestamp tolerance in seconds (default: 5 minutes)
    
        Returns:
            Processing result
    
        Raises:
            ValidationException: If webhook verification fails
            PaymentException: If event processing fails
        """
        try:
            # Verify webhook signature with timestamp tolerance
            event = self.stripe_client.construct_webhook_event(
                payload, signature, endpoint_secret, tolerance
            )
    
            logger.info(f"Processing webhook event: {event['type']} - {event['id']}")
    
            # Get event handler
            event_type = event["type"]
            handler = self.event_handlers.get(event_type)
    
            if handler:
                # Process event
                result = handler(event)
    
                logger.info(f"Successfully processed webhook event: {event['id']}")
                return {
                    "success": True,
                    "event_id": event["id"],
                    "event_type": event_type,
                    "processed_at": datetime.utcnow().isoformat(),
                    "result": result
                }
            else:
                # Log unhandled event
                logger.warning(f"Unhandled webhook event type: {event_type}")
                return {
                    "success": True,
                    "event_id": event["id"],
                    "event_type": event_type,
                    "processed_at": datetime.utcnow().isoformat(),
                    "result": "unhandled_event_type"
                }
    
        except ValidationException:
            raise
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
&gt;           raise PaymentException(f"Failed to process webhook: {str(e)}")
E           src.shared.exceptions.PaymentException: Failed to process webhook: catching classes that do not inherit from BaseException is not allowed

src\payment\services\webhook_service.py:126: PaymentException</failure></testcase></testsuite></testsuites>