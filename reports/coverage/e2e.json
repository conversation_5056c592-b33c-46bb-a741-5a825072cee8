{"meta": {"format": 3, "version": "7.9.2", "timestamp": "2025-07-29T15:29:37.561937", "branch_coverage": false, "show_contexts": false}, "files": {"src\\auth\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\handlers\\authorizer.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 18, 19, 22, 25, 46, 101, 156, 184], "summary": {"covered_lines": 15, "num_statements": 93, "percent_covered": 16.129032258064516, "percent_covered_display": "16", "missing_lines": 78, "excluded_lines": 0}, "missing_lines": [27, 28, 29, 30, 31, 33, 48, 49, 52, 53, 55, 58, 59, 60, 61, 62, 65, 66, 67, 70, 71, 72, 75, 76, 77, 86, 87, 89, 90, 92, 93, 95, 97, 98, 99, 106, 108, 110, 111, 112, 115, 118, 119, 120, 121, 122, 124, 125, 126, 129, 142, 143, 145, 146, 149, 160, 161, 163, 165, 166, 167, 170, 171, 172, 173, 174, 177, 178, 179, 181, 192, 207, 209, 210, 211, 212, 214, 216], "excluded_lines": [], "functions": {"get_jwt_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [27, 28, 29, 30, 31, 33], "excluded_lines": []}, "validate_token_with_multikey": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [48, 49, 52, 53, 55, 58, 59, 60, 61, 62, 65, 66, 67, 70, 71, 72, 75, 76, 77, 86, 87, 89, 90, 92, 93, 95, 97, 98, 99], "excluded_lines": []}, "handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [106, 108, 110, 111, 112, 115, 118, 119, 120, 121, 122, 124, 125, 126, 129, 142, 143, 145, 146, 149], "excluded_lines": []}, "extract_token_from_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [160, 161, 163, 165, 166, 167, 170, 171, 172, 173, 174, 177, 178, 179, 181], "excluded_lines": []}, "generate_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [192, 207, 209, 210, 211, 212, 214, 216], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 18, 19, 22, 25, 46, 101, 156, 184], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 18, 19, 22, 25, 46, 101, 156, 184], "summary": {"covered_lines": 15, "num_statements": 93, "percent_covered": 16.129032258064516, "percent_covered_display": "16", "missing_lines": 78, "excluded_lines": 0}, "missing_lines": [27, 28, 29, 30, 31, 33, 48, 49, 52, 53, 55, 58, 59, 60, 61, 62, 65, 66, 67, 70, 71, 72, 75, 76, 77, 86, 87, 89, 90, 92, 93, 95, 97, 98, 99, 106, 108, 110, 111, 112, 115, 118, 119, 120, 121, 122, 124, 125, 126, 129, 142, 143, 145, 146, 149, 160, 161, 163, 165, 166, 167, 170, 171, 172, 173, 174, 177, 178, 179, 181, 192, 207, 209, 210, 211, 212, 214, 216], "excluded_lines": []}}}, "src\\auth\\handlers\\forgot_password.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 21, 22, 23, 24, 27], "summary": {"covered_lines": 12, "num_statements": 73, "percent_covered": 16.438356164383563, "percent_covered_display": "16", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 68, 70, 72, 81, 82, 91, 96, 97, 99, 110, 111, 120, 125, 126, 137, 138, 147, 152, 153, 156, 157, 160, 172, 186, 187, 192, 195, 196, 197, 203, 209, 211, 219, 228, 229, 240, 244, 245, 246, 256, 262, 263, 264, 274, 281, 282, 283, 293, 299], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 61, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 68, 70, 72, 81, 82, 91, 96, 97, 99, 110, 111, 120, 125, 126, 137, 138, 147, 152, 153, 156, 157, 160, 172, 186, 187, 192, 195, 196, 197, 203, 209, 211, 219, 228, 229, 240, 244, 245, 246, 256, 262, 263, 264, 274, 281, 282, 283, 293, 299], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 21, 22, 23, 24, 27], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 21, 22, 23, 24, 27], "summary": {"covered_lines": 12, "num_statements": 73, "percent_covered": 16.438356164383563, "percent_covered_display": "16", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 68, 70, 72, 81, 82, 91, 96, 97, 99, 110, 111, 120, 125, 126, 137, 138, 147, 152, 153, 156, 157, 160, 172, 186, 187, 192, 195, 196, 197, 203, 209, 211, 219, 228, 229, 240, 244, 245, 246, 256, 262, 263, 264, 274, 281, 282, 283, 293, 299], "excluded_lines": []}}}, "src\\auth\\handlers\\login.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 66, "percent_covered": 15.151515151515152, "percent_covered_display": "15", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 72, 74, 76, 83, 86, 87, 88, 92, 95, 96, 104, 107, 108, 116, 119, 122, 129, 135, 159, 168, 169, 180, 185, 187, 197, 199, 200, 201, 211, 217, 218, 219, 229, 234, 235, 236, 246, 253, 254, 255, 265, 271], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 56, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 72, 74, 76, 83, 86, 87, 88, 92, 95, 96, 104, 107, 108, 116, 119, 122, 129, 135, 159, 168, 169, 180, 185, 187, 197, 199, 200, 201, 211, 217, 218, 219, 229, 234, 235, 236, 246, 253, 254, 255, 265, 271], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 66, "percent_covered": 15.151515151515152, "percent_covered_display": "15", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 72, 74, 76, 83, 86, 87, 88, 92, 95, 96, 104, 107, 108, 116, 119, 122, 129, 135, 159, 168, 169, 180, 185, 187, 197, 199, 200, 201, 211, 217, 218, 219, 229, 234, 235, 236, 246, 253, 254, 255, 265, 271], "excluded_lines": []}}}, "src\\auth\\handlers\\refresh.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27, 38, 41, 42, 43, 46, 53], "summary": {"covered_lines": 16, "num_statements": 70, "percent_covered": 22.857142857142858, "percent_covered_display": "23", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [39, 55, 57, 58, 60, 62, 67, 68, 69, 70, 77, 80, 81, 83, 84, 90, 93, 94, 95, 103, 105, 106, 107, 115, 118, 119, 127, 130, 131, 139, 142, 150, 156, 171, 180, 181, 192, 197, 198, 199, 209, 215, 216, 217, 227, 232, 233, 234, 244, 251, 252, 253, 263, 268], "excluded_lines": [], "functions": {"handler": {"executed_lines": [38, 41, 42, 43, 46, 53], "summary": {"covered_lines": 6, "num_statements": 60, "percent_covered": 10.0, "percent_covered_display": "10", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [39, 55, 57, 58, 60, 62, 67, 68, 69, 70, 77, 80, 81, 83, 84, 90, 93, 94, 95, 103, 105, 106, 107, 115, 118, 119, 127, 130, 131, 139, 142, 150, 156, 171, 180, 181, 192, 197, 198, 199, 209, 215, 216, 217, 227, 232, 233, 234, 244, 251, 252, 253, 263, 268], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27, 38, 41, 42, 43, 46, 53], "summary": {"covered_lines": 16, "num_statements": 70, "percent_covered": 22.857142857142858, "percent_covered_display": "23", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [39, 55, 57, 58, 60, 62, 67, 68, 69, 70, 77, 80, 81, 83, 84, 90, 93, 94, 95, 103, 105, 106, 107, 115, 118, 119, 127, 130, 131, 139, 142, 150, 156, 171, 180, 181, 192, 197, 198, 199, 209, 215, 216, 217, 227, 232, 233, 234, 244, 251, 252, 253, 263, 268], "excluded_lines": []}}}, "src\\auth\\handlers\\register.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 23, 24, 27, 42, 45, 48, 55], "summary": {"covered_lines": 15, "num_statements": 63, "percent_covered": 23.80952380952381, "percent_covered_display": "24", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [43, 57, 59, 60, 62, 63, 64, 65, 66, 69, 70, 71, 83, 89, 96, 106, 109, 110, 112, 113, 119, 124, 126, 134, 158, 159, 170, 175, 176, 177, 187, 193, 194, 195, 205, 211, 212, 213, 223, 228, 229, 230, 240, 247, 248, 249, 259, 264], "excluded_lines": [], "functions": {"handler": {"executed_lines": [42, 45, 48, 55], "summary": {"covered_lines": 4, "num_statements": 52, "percent_covered": 7.6923076923076925, "percent_covered_display": "8", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [43, 57, 59, 60, 62, 63, 64, 65, 66, 69, 70, 71, 83, 89, 96, 106, 109, 110, 112, 113, 119, 124, 126, 134, 158, 159, 170, 175, 176, 177, 187, 193, 194, 195, 205, 211, 212, 213, 223, 228, 229, 230, 240, 247, 248, 249, 259, 264], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 23, 24, 27, 42, 45, 48, 55], "summary": {"covered_lines": 15, "num_statements": 63, "percent_covered": 23.80952380952381, "percent_covered_display": "24", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [43, 57, 59, 60, 62, 63, 64, 65, 66, 69, 70, 71, 83, 89, 96, 106, 109, 110, 112, 113, 119, 124, 126, 134, 158, 159, 170, 175, 176, 177, 187, 193, 194, 195, 205, 211, 212, 213, 223, 228, 229, 230, 240, 247, 248, 249, 259, 264], "excluded_lines": []}}}, "src\\auth\\handlers\\reset_password.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 22, 23, 24, 27, 39, 42, 43, 44, 47, 54], "summary": {"covered_lines": 17, "num_statements": 87, "percent_covered": 19.54022988505747, "percent_covered_display": "20", "missing_lines": 70, "excluded_lines": 0}, "missing_lines": [40, 56, 58, 59, 61, 62, 64, 70, 71, 72, 81, 83, 84, 91, 94, 95, 96, 104, 107, 108, 116, 119, 120, 123, 129, 130, 138, 141, 142, 143, 151, 153, 154, 155, 157, 162, 165, 166, 169, 177, 184, 192, 193, 198, 201, 213, 222, 223, 234, 239, 240, 241, 251, 257, 258, 259, 269, 275, 276, 277, 287, 293, 294, 295, 305, 312, 313, 314, 324, 329], "excluded_lines": [], "functions": {"handler": {"executed_lines": [39, 42, 43, 44, 47, 54], "summary": {"covered_lines": 6, "num_statements": 76, "percent_covered": 7.894736842105263, "percent_covered_display": "8", "missing_lines": 70, "excluded_lines": 0}, "missing_lines": [40, 56, 58, 59, 61, 62, 64, 70, 71, 72, 81, 83, 84, 91, 94, 95, 96, 104, 107, 108, 116, 119, 120, 123, 129, 130, 138, 141, 142, 143, 151, 153, 154, 155, 157, 162, 165, 166, 169, 177, 184, 192, 193, 198, 201, 213, 222, 223, 234, 239, 240, 241, 251, 257, 258, 259, 269, 275, 276, 277, 287, 293, 294, 295, 305, 312, 313, 314, 324, 329], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 22, 23, 24, 27, 39, 42, 43, 44, 47, 54], "summary": {"covered_lines": 17, "num_statements": 87, "percent_covered": 19.54022988505747, "percent_covered_display": "20", "missing_lines": 70, "excluded_lines": 0}, "missing_lines": [40, 56, 58, 59, 61, 62, 64, 70, 71, 72, 81, 83, 84, 91, 94, 95, 96, 104, 107, 108, 116, 119, 120, 123, 129, 130, 138, 141, 142, 143, 151, 153, 154, 155, 157, 162, 165, 166, 169, 177, 184, 192, 193, 198, 201, 213, 222, 223, 234, 239, 240, 241, 251, 257, 258, 259, 269, 275, 276, 277, 287, 293, 294, 295, 305, 312, 313, 314, 324, 329], "excluded_lines": []}}}, "src\\auth\\handlers\\verify_email.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 20, 21, 22, 23, 26, 37, 40, 41, 44, 51], "summary": {"covered_lines": 15, "num_statements": 61, "percent_covered": 24.59016393442623, "percent_covered_display": "25", "missing_lines": 46, "excluded_lines": 0}, "missing_lines": [38, 53, 55, 56, 58, 60, 65, 67, 68, 74, 77, 78, 79, 83, 86, 88, 89, 96, 99, 100, 101, 107, 112, 114, 122, 144, 152, 153, 164, 169, 170, 171, 181, 187, 188, 189, 199, 205, 206, 207, 217, 224, 225, 226, 236, 241], "excluded_lines": [], "functions": {"handler": {"executed_lines": [37, 40, 41, 44, 51], "summary": {"covered_lines": 5, "num_statements": 51, "percent_covered": 9.803921568627452, "percent_covered_display": "10", "missing_lines": 46, "excluded_lines": 0}, "missing_lines": [38, 53, 55, 56, 58, 60, 65, 67, 68, 74, 77, 78, 79, 83, 86, 88, 89, 96, 99, 100, 101, 107, 112, 114, 122, 144, 152, 153, 164, 169, 170, 171, 181, 187, 188, 189, 199, 205, 206, 207, 217, 224, 225, 226, 236, 241], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 20, 21, 22, 23, 26], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 20, 21, 22, 23, 26, 37, 40, 41, 44, 51], "summary": {"covered_lines": 15, "num_statements": 61, "percent_covered": 24.59016393442623, "percent_covered_display": "25", "missing_lines": 46, "excluded_lines": 0}, "missing_lines": [38, 53, 55, 56, 58, 60, 65, 67, 68, 74, 77, 78, 79, 83, 86, 88, 89, 96, 99, 100, 101, 107, 112, 114, 122, 144, 152, 153, 164, 169, 170, 171, 181, 187, 188, 189, 199, 205, 206, 207, 217, 224, 225, 226, 236, 241], "excluded_lines": []}}}, "src\\auth\\models\\tenant.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 76, 77, 127, 128, 158, 159, 194, 238, 257, 276, 282, 286, 294, 295, 305, 306, 322, 327, 332, 337, 344, 349, 354, 358, 396, 397, 407, 408, 424, 425, 453], "summary": {"covered_lines": 51, "num_statements": 216, "percent_covered": 23.61111111111111, "percent_covered_display": "24", "missing_lines": 165, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 74, 84, 85, 87, 104, 106, 112, 118, 120, 121, 125, 130, 131, 137, 139, 143, 144, 145, 146, 148, 149, 151, 152, 156, 161, 164, 172, 173, 175, 179, 180, 181, 182, 184, 185, 187, 188, 192, 196, 197, 199, 200, 201, 202, 203, 205, 206, 208, 209, 217, 223, 224, 225, 227, 229, 230, 234, 236, 240, 241, 248, 250, 251, 255, 259, 260, 267, 269, 270, 274, 278, 279, 280, 284, 288, 289, 291, 292, 297, 303, 308, 320, 324, 325, 329, 330, 334, 335, 339, 340, 341, 342, 346, 347, 351, 352, 356, 360, 361, 377, 378, 379, 380, 381, 382, 383, 384, 386, 387, 389, 390, 394, 399, 400, 402, 403, 404, 405, 410, 411, 413, 414, 416, 417, 420, 421, 422, 427, 428, 430, 432, 433, 435, 436, 438, 439, 442, 443, 444, 446, 447, 448, 450, 451, 455, 472, 473, 474, 475, 477], "excluded_lines": [], "functions": {"Tenant.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 74], "excluded_lines": []}, "Tenant.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [84, 85, 87, 104, 106, 112, 118, 120, 121, 125], "excluded_lines": []}, "Tenant.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [130, 131, 137, 139, 143, 144, 145, 146, 148, 149, 151, 152, 156], "excluded_lines": []}, "Tenant.get_by_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [161, 164, 172, 173, 175, 179, 180, 181, 182, 184, 185, 187, 188, 192], "excluded_lines": []}, "Tenant.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [196, 197, 199, 200, 201, 202, 203, 205, 206, 208, 209, 217, 223, 224, 225, 227, 229, 230, 234, 236], "excluded_lines": []}, "Tenant.increment_user_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [240, 241, 248, 250, 251, 255], "excluded_lines": []}, "Tenant.decrement_user_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [259, 260, 267, 269, 270, 274], "excluded_lines": []}, "Tenant.can_add_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [278, 279, 280], "excluded_lines": []}, "Tenant.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [284], "excluded_lines": []}, "Tenant.is_trial_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [288, 289, 291, 292], "excluded_lines": []}, "Tenant._get_max_users_for_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [297, 303], "excluded_lines": []}, "Tenant._get_features_for_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [308, 320], "excluded_lines": []}, "Tenant.activate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [324, 325], "excluded_lines": []}, "Tenant.suspend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [329, 330], "excluded_lines": []}, "Tenant.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [334, 335], "excluded_lines": []}, "Tenant.update_settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [339, 340, 341, 342], "excluded_lines": []}, "Tenant.set_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [346, 347], "excluded_lines": []}, "Tenant.remove_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [351, 352], "excluded_lines": []}, "Tenant.has_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [356], "excluded_lines": []}, "Tenant.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [360, 361, 377, 378, 379, 380, 381, 382, 383, 384, 386, 387, 389, 390, 394], "excluded_lines": []}, "Tenant.slug": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [399, 400, 402, 403, 404, 405], "excluded_lines": []}, "Tenant._validate_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [410, 411, 413, 414, 416, 417, 420, 421, 422], "excluded_lines": []}, "Tenant._validate_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [427, 428, 430, 432, 433, 435, 436, 438, 439, 442, 443, 444, 446, 447, 448, 450, 451], "excluded_lines": []}, "Tenant.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [455, 472, 473, 474, 475, 477], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 76, 77, 127, 128, 158, 159, 194, 238, 257, 276, 282, 286, 294, 295, 305, 306, 322, 327, 332, 337, 344, 349, 354, 358, 396, 397, 407, 408, 424, 425, 453], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TenantStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantPlan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 165, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 165, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 74, 84, 85, 87, 104, 106, 112, 118, 120, 121, 125, 130, 131, 137, 139, 143, 144, 145, 146, 148, 149, 151, 152, 156, 161, 164, 172, 173, 175, 179, 180, 181, 182, 184, 185, 187, 188, 192, 196, 197, 199, 200, 201, 202, 203, 205, 206, 208, 209, 217, 223, 224, 225, 227, 229, 230, 234, 236, 240, 241, 248, 250, 251, 255, 259, 260, 267, 269, 270, 274, 278, 279, 280, 284, 288, 289, 291, 292, 297, 303, 308, 320, 324, 325, 329, 330, 334, 335, 339, 340, 341, 342, 346, 347, 351, 352, 356, 360, 361, 377, 378, 379, 380, 381, 382, 383, 384, 386, 387, 389, 390, 394, 399, 400, 402, 403, 404, 405, 410, 411, 413, 414, 416, 417, 420, 421, 422, 427, 428, 430, 432, 433, 435, 436, 438, 439, 442, 443, 444, 446, 447, 448, 450, 451, 455, 472, 473, 474, 475, 477], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 76, 77, 127, 128, 158, 159, 194, 238, 257, 276, 282, 286, 294, 295, 305, 306, 322, 327, 332, 337, 344, 349, 354, 358, 396, 397, 407, 408, 424, 425, 453], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\models\\user.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 24, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 41, 42, 44, 85, 86, 159, 160, 191, 192, 227, 228, 254, 255, 281, 282, 307, 345, 378, 422, 434, 438, 442, 446, 470, 489, 509, 536, 541, 546, 551, 557, 561, 565, 572, 581, 618, 619, 647, 648, 659, 660], "summary": {"covered_lines": 56, "num_statements": 286, "percent_covered": 19.58041958041958, "percent_covered_display": "20", "missing_lines": 230, "excluded_lines": 0}, "missing_lines": [57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 80, 81, 83, 96, 97, 100, 103, 105, 129, 131, 132, 133, 140, 142, 149, 151, 152, 157, 162, 163, 169, 171, 175, 176, 177, 178, 180, 181, 183, 184, 189, 194, 196, 197, 204, 205, 207, 211, 212, 213, 214, 216, 217, 219, 220, 225, 230, 232, 233, 240, 241, 243, 244, 245, 247, 248, 252, 257, 259, 260, 267, 268, 270, 271, 272, 274, 275, 279, 284, 286, 287, 294, 295, 297, 298, 300, 301, 305, 309, 311, 312, 315, 316, 319, 320, 321, 322, 325, 326, 328, 334, 336, 337, 343, 347, 348, 349, 351, 362, 363, 365, 370, 371, 376, 380, 381, 382, 384, 385, 387, 388, 391, 403, 404, 406, 412, 414, 415, 420, 424, 425, 428, 429, 430, 432, 436, 440, 444, 448, 449, 450, 461, 462, 464, 465, 472, 473, 481, 483, 484, 491, 492, 493, 501, 503, 504, 511, 528, 529, 534, 538, 539, 543, 544, 548, 549, 553, 559, 563, 567, 568, 569, 574, 575, 576, 577, 578, 579, 583, 584, 602, 603, 604, 605, 607, 608, 610, 611, 616, 621, 622, 624, 626, 627, 629, 630, 632, 633, 636, 637, 638, 640, 641, 642, 644, 645, 650, 651, 653, 654, 656, 657, 662, 663, 665, 667, 668, 671, 674, 675, 678, 679], "excluded_lines": [], "functions": {"User.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 80, 81, 83], "excluded_lines": []}, "User.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [96, 97, 100, 103, 105, 129, 131, 132, 133, 140, 142, 149, 151, 152, 157], "excluded_lines": []}, "User.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [162, 163, 169, 171, 175, 176, 177, 178, 180, 181, 183, 184, 189], "excluded_lines": []}, "User.get_by_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [194, 196, 197, 204, 205, 207, 211, 212, 213, 214, 216, 217, 219, 220, 225], "excluded_lines": []}, "User.get_by_verification_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [230, 232, 233, 240, 241, 243, 244, 245, 247, 248, 252], "excluded_lines": []}, "User.get_by_reset_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [257, 259, 260, 267, 268, 270, 271, 272, 274, 275, 279], "excluded_lines": []}, "User.find_by_email_global": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [284, 286, 287, 294, 295, 297, 298, 300, 301, 305], "excluded_lines": []}, "User.authenticate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [309, 311, 312, 315, 316, 319, 320, 321, 322, 325, 326, 328, 334, 336, 337, 343], "excluded_lines": []}, "User.update_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [347, 348, 349, 351, 362, 363, 365, 370, 371, 376], "excluded_lines": []}, "User.verify_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [574, 575, 576, 577, 578, 579], "excluded_lines": []}, "User.is_locked": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [424, 425, 428, 429, 430, 432], "excluded_lines": []}, "User.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [436], "excluded_lines": []}, "User.is_master": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [440], "excluded_lines": []}, "User.is_member": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [444], "excluded_lines": []}, "User._increment_login_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [448, 449, 450, 461, 462, 464, 465], "excluded_lines": []}, "User._reset_login_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [472, 473, 481, 483, 484], "excluded_lines": []}, "User._update_last_login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [491, 492, 493, 501, 503, 504], "excluded_lines": []}, "User.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [511, 528, 529, 534], "excluded_lines": []}, "User.activate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [538, 539], "excluded_lines": []}, "User.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [543, 544], "excluded_lines": []}, "User.suspend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [548, 549], "excluded_lines": []}, "User.can_login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [553], "excluded_lines": []}, "User.can_manage_users": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [559], "excluded_lines": []}, "User.can_manage_billing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [563], "excluded_lines": []}, "User.update_last_login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [567, 568, 569], "excluded_lines": []}, "User.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [583, 584, 602, 603, 604, 605, 607, 608, 610, 611, 616], "excluded_lines": []}, "User._validate_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [621, 622, 624, 626, 627, 629, 630, 632, 633, 636, 637, 638, 640, 641, 642, 644, 645], "excluded_lines": []}, "User._validate_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [650, 651, 653, 654, 656, 657], "excluded_lines": []}, "User._validate_phone": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [662, 663, 665, 667, 668, 671, 674, 675, 678, 679], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 24, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 41, 42, 44, 85, 86, 159, 160, 191, 192, 227, 228, 254, 255, 281, 282, 307, 345, 378, 422, 434, 438, 442, 446, 470, 489, 509, 536, 541, 546, 551, 557, 561, 565, 572, 581, 618, 619, 647, 648, 659, 660], "summary": {"covered_lines": 56, "num_statements": 56, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"UserRole": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 230, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 230, "excluded_lines": 0}, "missing_lines": [57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 80, 81, 83, 96, 97, 100, 103, 105, 129, 131, 132, 133, 140, 142, 149, 151, 152, 157, 162, 163, 169, 171, 175, 176, 177, 178, 180, 181, 183, 184, 189, 194, 196, 197, 204, 205, 207, 211, 212, 213, 214, 216, 217, 219, 220, 225, 230, 232, 233, 240, 241, 243, 244, 245, 247, 248, 252, 257, 259, 260, 267, 268, 270, 271, 272, 274, 275, 279, 284, 286, 287, 294, 295, 297, 298, 300, 301, 305, 309, 311, 312, 315, 316, 319, 320, 321, 322, 325, 326, 328, 334, 336, 337, 343, 347, 348, 349, 351, 362, 363, 365, 370, 371, 376, 380, 381, 382, 384, 385, 387, 388, 391, 403, 404, 406, 412, 414, 415, 420, 424, 425, 428, 429, 430, 432, 436, 440, 444, 448, 449, 450, 461, 462, 464, 465, 472, 473, 481, 483, 484, 491, 492, 493, 501, 503, 504, 511, 528, 529, 534, 538, 539, 543, 544, 548, 549, 553, 559, 563, 567, 568, 569, 574, 575, 576, 577, 578, 579, 583, 584, 602, 603, 604, 605, 607, 608, 610, 611, 616, 621, 622, 624, 626, 627, 629, 630, 632, 633, 636, 637, 638, 640, 641, 642, 644, 645, 650, 651, 653, 654, 656, 657, 662, 663, 665, 667, 668, 671, 674, 675, 678, 679], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 24, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 41, 42, 44, 85, 86, 159, 160, 191, 192, 227, 228, 254, 255, 281, 282, 307, 345, 378, 422, 434, 438, 442, 446, 470, 489, 509, 536, 541, 546, 551, 557, 561, 565, 572, 581, 618, 619, 647, 648, 659, 660], "summary": {"covered_lines": 56, "num_statements": 56, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\__init__.py": {"executed_lines": [0], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\email_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 76, "percent_covered": 23.68421052631579, "percent_covered_display": "24", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 37, 39, 41, 47, 63, 79, 81, 83, 88, 104, 120, 122, 128, 143, 159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254, 266, 312, 359, 409, 410, 412, 418, 432, 439, 440, 445, 470, 471, 474, 476, 484, 503, 510, 511, 517, 528], "excluded_lines": [], "functions": {"EmailService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25], "excluded_lines": []}, "EmailService.send_verification_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 47, 63], "excluded_lines": []}, "EmailService.send_password_reset_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [79, 81, 83, 88, 104], "excluded_lines": []}, "EmailService.send_welcome_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [409, 410, 412, 418, 432, 439, 440, 445], "excluded_lines": []}, "EmailService._send_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254], "excluded_lines": []}, "EmailService._get_verification_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [266], "excluded_lines": []}, "EmailService._get_password_reset_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [312], "excluded_lines": []}, "EmailService._get_welcome_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [359], "excluded_lines": []}, "EmailService.send_user_invitation_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [470, 471, 474, 476, 484, 503, 510, 511, 517], "excluded_lines": []}, "EmailService._get_invitation_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [528], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"EmailService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 58, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 37, 39, 41, 47, 63, 79, 81, 83, 88, 104, 120, 122, 128, 143, 159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254, 266, 312, 359, 409, 410, 412, 418, 432, 439, 440, 445, 470, 471, 474, 476, 484, 503, 510, 511, 517, 528], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\password_service.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 131, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 131, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 13, 14, 15, 17, 20, 23, 25, 26, 27, 30, 35, 48, 49, 51, 52, 55, 56, 57, 59, 61, 72, 73, 75, 76, 77, 78, 79, 80, 82, 92, 93, 96, 97, 99, 100, 103, 104, 106, 107, 109, 110, 112, 113, 116, 117, 118, 119, 122, 123, 126, 127, 129, 131, 137, 139, 140, 141, 142, 144, 145, 147, 149, 151, 152, 153, 154, 156, 166, 167, 170, 171, 172, 173, 176, 184, 185, 186, 189, 191, 193, 204, 207, 208, 210, 212, 213, 215, 217, 218, 219, 220, 223, 225, 227, 237, 240, 242, 243, 244, 245, 246, 247, 248, 249, 251, 252, 255, 256, 258, 268, 271, 272, 273, 274, 277, 278, 279, 280, 281, 282, 285, 286, 287, 288, 289, 291, 295], "excluded_lines": [], "functions": {"PasswordService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [25, 26, 27, 30], "excluded_lines": []}, "PasswordService.hash_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [48, 49, 51, 52, 55, 56, 57, 59], "excluded_lines": []}, "PasswordService.verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [72, 73, 75, 76, 77, 78, 79, 80], "excluded_lines": []}, "PasswordService.validate_password_strength": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [92, 93, 96, 97, 99, 100, 103, 104, 106, 107, 109, 110, 112, 113, 116, 117, 118, 119, 122, 123, 126, 127], "excluded_lines": []}, "PasswordService._has_sequential_chars": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [131, 137, 139, 140, 141, 142, 144, 145, 147], "excluded_lines": []}, "PasswordService._has_repeated_chars": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [151, 152, 153, 154], "excluded_lines": []}, "PasswordService.generate_secure_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [166, 167, 170, 171, 172, 173, 176, 184, 185, 186, 189, 191], "excluded_lines": []}, "PasswordService.check_password_breach": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [204, 207, 208, 210, 212, 213, 215, 217, 218, 219, 220, 223, 225], "excluded_lines": []}, "PasswordService.calculate_password_entropy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [237, 240, 242, 243, 244, 245, 246, 247, 248, 249, 251, 252, 255, 256], "excluded_lines": []}, "PasswordService.get_password_strength_score": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [268, 271, 272, 273, 274, 277, 278, 279, 280, 281, 282, 285, 286, 287, 288, 289, 291], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 13, 14, 15, 17, 20, 23, 35, 61, 82, 129, 149, 156, 193, 227, 258, 295], "excluded_lines": []}}, "classes": {"PasswordService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 111, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 111, "excluded_lines": 0}, "missing_lines": [25, 26, 27, 30, 48, 49, 51, 52, 55, 56, 57, 59, 72, 73, 75, 76, 77, 78, 79, 80, 92, 93, 96, 97, 99, 100, 103, 104, 106, 107, 109, 110, 112, 113, 116, 117, 118, 119, 122, 123, 126, 127, 131, 137, 139, 140, 141, 142, 144, 145, 147, 151, 152, 153, 154, 166, 167, 170, 171, 172, 173, 176, 184, 185, 186, 189, 191, 204, 207, 208, 210, 212, 213, 215, 217, 218, 219, 220, 223, 225, 237, 240, 242, 243, 244, 245, 246, 247, 248, 249, 251, 252, 255, 256, 268, 271, 272, 273, 274, 277, 278, 279, 280, 281, 282, 285, 286, 287, 288, 289, 291], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 13, 14, 15, 17, 20, 23, 35, 61, 82, 129, 149, 156, 193, 227, 258, 295], "excluded_lines": []}}}, "src\\payment\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\handlers\\cancel_subscription.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 25, 26], "summary": {"covered_lines": 10, "num_statements": 48, "percent_covered": 20.833333333333332, "percent_covered_display": "21", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 45, 54, 56, 58, 59, 61, 62, 64, 72, 79, 88, 89, 100, 105, 106, 107, 119, 125, 126, 127, 139, 144, 145, 146, 158, 163, 164, 165, 177, 184, 185, 186, 198, 204], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 45, 54, 56, 58, 59, 61, 62, 64, 72, 79, 88, 89, 100, 105, 106, 107, 119, 125, 126, 127, 139, 144, 145, 146, 158, 163, 164, 165, 177, 184, 185, 186, 198, 204], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 25, 26], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 25, 26], "summary": {"covered_lines": 10, "num_statements": 48, "percent_covered": 20.833333333333332, "percent_covered_display": "21", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 45, 54, 56, 58, 59, 61, 62, 64, 72, 79, 88, 89, 100, 105, 106, 107, 119, 125, 126, 127, 139, 144, 145, 146, 158, 163, 164, 165, 177, 184, 185, 186, 198, 204], "excluded_lines": []}}}, "src\\payment\\handlers\\create_subscription.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27, 28], "summary": {"covered_lines": 11, "num_statements": 62, "percent_covered": 17.741935483870968, "percent_covered_display": "18", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [41, 42, 44, 45, 48, 57, 59, 61, 62, 64, 65, 66, 69, 70, 71, 72, 77, 85, 93, 100, 101, 102, 104, 106, 107, 118, 123, 124, 125, 137, 143, 144, 145, 157, 162, 163, 164, 176, 183, 184, 185, 197, 202, 203, 204, 216, 223, 224, 225, 237, 243], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 51, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [41, 42, 44, 45, 48, 57, 59, 61, 62, 64, 65, 66, 69, 70, 71, 72, 77, 85, 93, 100, 101, 102, 104, 106, 107, 118, 123, 124, 125, 137, 143, 144, 145, 157, 162, 163, 164, 176, 183, 184, 185, 197, 202, 203, 204, 216, 223, 224, 225, 237, 243], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27, 28], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27, 28], "summary": {"covered_lines": 11, "num_statements": 62, "percent_covered": 17.741935483870968, "percent_covered_display": "18", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [41, 42, 44, 45, 48, 57, 59, 61, 62, 64, 65, 66, 69, 70, 71, 72, 77, 85, 93, 100, 101, 102, 104, 106, 107, 118, 123, 124, 125, 137, 143, 144, 145, 157, 162, 163, 164, 176, 183, 184, 185, 197, 202, 203, 204, 216, 223, 224, 225, 237, 243], "excluded_lines": []}}}, "src\\payment\\handlers\\get_subscription.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 18, 19, 22, 23], "summary": {"covered_lines": 9, "num_statements": 39, "percent_covered": 23.076923076923077, "percent_covered_display": "23", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 35, 38, 47, 49, 50, 56, 59, 60, 65, 67, 72, 74, 75, 86, 91, 92, 93, 105, 110, 111, 112, 124, 131, 132, 133, 145, 151], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 35, 38, 47, 49, 50, 56, 59, 60, 65, 67, 72, 74, 75, 86, 91, 92, 93, 105, 110, 111, 112, 124, 131, 132, 133, 145, 151], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 18, 19, 22, 23], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 18, 19, 22, 23], "summary": {"covered_lines": 9, "num_statements": 39, "percent_covered": 23.076923076923077, "percent_covered_display": "23", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 35, 38, 47, 49, 50, 56, 59, 60, 65, 67, 72, 74, 75, 86, 91, 92, 93, 105, 110, 111, 112, 124, 131, 132, 133, 145, 151], "excluded_lines": []}}}, "src\\payment\\handlers\\list_plans.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 18, 109], "summary": {"covered_lines": 8, "num_statements": 57, "percent_covered": 14.035087719298245, "percent_covered_display": "14", "missing_lines": 49, "excluded_lines": 0}, "missing_lines": [26, 27, 29, 32, 39, 41, 42, 45, 48, 53, 54, 63, 68, 69, 70, 80, 87, 88, 89, 99, 104, 117, 118, 120, 123, 130, 132, 134, 135, 137, 138, 139, 144, 147, 149, 150, 151, 156, 157, 166, 171, 172, 173, 183, 190, 191, 192, 202, 208], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [26, 27, 29, 32, 39, 41, 42, 45, 48, 53, 54, 63, 68, 69, 70, 80, 87, 88, 89, 99, 104], "excluded_lines": []}, "get_plan_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [117, 118, 120, 123, 130, 132, 134, 135, 137, 138, 139, 144, 147, 149, 150, 151, 156, 157, 166, 171, 172, 173, 183, 190, 191, 192, 202, 208], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 18, 109], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 18, 109], "summary": {"covered_lines": 8, "num_statements": 57, "percent_covered": 14.035087719298245, "percent_covered_display": "14", "missing_lines": 49, "excluded_lines": 0}, "missing_lines": [26, 27, 29, 32, 39, 41, 42, 45, 48, 53, 54, 63, 68, 69, 70, 80, 87, 88, 89, 99, 104, 117, 118, 120, 123, 130, 132, 134, 135, 137, 138, 139, 144, 147, 149, 150, 151, 156, 157, 166, 171, 172, 173, 183, 190, 191, 192, 202, 208], "excluded_lines": []}}}, "src\\payment\\handlers\\stripe_webhook.py": {"executed_lines": [4, 9, 10, 12, 13, 18, 19, 20, 21, 24, 32, 35, 36, 39, 46, 203, 229, 261, 305, 336, 366], "summary": {"covered_lines": 20, "num_statements": 130, "percent_covered": 15.384615384615385, "percent_covered_display": "15", "missing_lines": 110, "excluded_lines": 0}, "missing_lines": [33, 48, 50, 51, 53, 54, 60, 69, 70, 72, 73, 74, 81, 82, 83, 88, 89, 96, 98, 99, 101, 108, 110, 111, 120, 125, 126, 127, 137, 143, 144, 145, 155, 162, 163, 164, 174, 181, 182, 183, 193, 198, 206, 207, 209, 210, 212, 213, 215, 216, 218, 219, 222, 226, 231, 233, 234, 236, 237, 244, 250, 252, 253, 258, 263, 265, 266, 268, 270, 277, 279, 287, 294, 296, 297, 302, 307, 309, 310, 312, 313, 319, 325, 327, 328, 333, 338, 340, 341, 342, 344, 345, 347, 353, 355, 356, 361, 363, 368, 370, 371, 372, 374, 375, 377, 383, 385, 386, 391, 393], "excluded_lines": [], "functions": {"handler": {"executed_lines": [32, 35, 36, 39, 46], "summary": {"covered_lines": 5, "num_statements": 47, "percent_covered": 10.638297872340425, "percent_covered_display": "11", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [33, 48, 50, 51, 53, 54, 60, 69, 70, 72, 73, 74, 81, 82, 83, 88, 89, 96, 98, 99, 101, 108, 110, 111, 120, 125, 126, 127, 137, 143, 144, 145, 155, 162, 163, 164, 174, 181, 182, 183, 193, 198], "excluded_lines": []}, "_process_webhook_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [206, 207, 209, 210, 212, 213, 215, 216, 218, 219, 222, 226], "excluded_lines": []}, "_handle_subscription_created": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [231, 233, 234, 236, 237, 244, 250, 252, 253, 258], "excluded_lines": []}, "_handle_subscription_updated": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [263, 265, 266, 268, 270, 277, 279, 287, 294, 296, 297, 302], "excluded_lines": []}, "_handle_subscription_deleted": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [307, 309, 310, 312, 313, 319, 325, 327, 328, 333], "excluded_lines": []}, "_handle_payment_succeeded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [338, 340, 341, 342, 344, 345, 347, 353, 355, 356, 361, 363], "excluded_lines": []}, "_handle_payment_failed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [368, 370, 371, 372, 374, 375, 377, 383, 385, 386, 391, 393], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 18, 19, 20, 21, 24, 203, 229, 261, 305, 336, 366], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 18, 19, 20, 21, 24, 32, 35, 36, 39, 46, 203, 229, 261, 305, 336, 366], "summary": {"covered_lines": 20, "num_statements": 130, "percent_covered": 15.384615384615385, "percent_covered_display": "15", "missing_lines": 110, "excluded_lines": 0}, "missing_lines": [33, 48, 50, 51, 53, 54, 60, 69, 70, 72, 73, 74, 81, 82, 83, 88, 89, 96, 98, 99, 101, 108, 110, 111, 120, 125, 126, 127, 137, 143, 144, 145, 155, 162, 163, 164, 174, 181, 182, 183, 193, 198, 206, 207, 209, 210, 212, 213, 215, 216, 218, 219, 222, 226, 231, 233, 234, 236, 237, 244, 250, 252, 253, 258, 263, 265, 266, 268, 270, 277, 279, 287, 294, 296, 297, 302, 307, 309, 310, 312, 313, 319, 325, 327, 328, 333, 338, 340, 341, 342, 344, 345, 347, 353, 355, 356, 361, 363, 368, 370, 371, 372, 374, 375, 377, 383, 385, 386, 391, 393], "excluded_lines": []}}}, "src\\payment\\models\\customer.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 40, 43, 44, 47, 48, 50, 54, 68, 84, 85, 99, 127, 145, 146, 168, 169, 190, 191, 212, 213, 248, 252, 257, 262, 267, 272, 276, 283, 287], "summary": {"covered_lines": 49, "num_statements": 129, "percent_covered": 37.98449612403101, "percent_covered_display": "38", "missing_lines": 80, "excluded_lines": 0}, "missing_lines": [52, 56, 57, 59, 60, 62, 63, 65, 66, 70, 88, 89, 90, 91, 94, 95, 97, 101, 102, 104, 115, 120, 121, 123, 124, 125, 129, 130, 138, 139, 141, 142, 143, 148, 150, 159, 160, 162, 164, 165, 166, 171, 172, 181, 182, 184, 186, 187, 188, 193, 194, 203, 204, 206, 208, 209, 210, 220, 221, 231, 232, 234, 236, 238, 244, 245, 246, 250, 254, 255, 259, 260, 264, 265, 269, 270, 274, 278, 285, 289], "excluded_lines": [], "functions": {"Customer.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [52], "excluded_lines": []}, "Customer._validate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [56, 57, 59, 60, 62, 63, 65, 66], "excluded_lines": []}, "Customer.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [70], "excluded_lines": []}, "Customer.from_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [88, 89, 90, 91, 94, 95, 97], "excluded_lines": []}, "Customer.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [101, 102, 104, 115, 120, 121, 123, 124, 125], "excluded_lines": []}, "Customer.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [129, 130, 138, 139, 141, 142, 143], "excluded_lines": []}, "Customer.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [148, 150, 159, 160, 162, 164, 165, 166], "excluded_lines": []}, "Customer.get_by_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [171, 172, 181, 182, 184, 186, 187, 188], "excluded_lines": []}, "Customer.get_by_stripe_customer_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [193, 194, 203, 204, 206, 208, 209, 210], "excluded_lines": []}, "Customer.list_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [220, 221, 231, 232, 234, 236, 238, 244, 245, 246], "excluded_lines": []}, "Customer.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [250], "excluded_lines": []}, "Customer.activate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [254, 255], "excluded_lines": []}, "Customer.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [259, 260], "excluded_lines": []}, "Customer.suspend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [264, 265], "excluded_lines": []}, "Customer.update_stripe_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [269, 270], "excluded_lines": []}, "Customer.get_display_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [274], "excluded_lines": []}, "Customer.has_valid_payment_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [278], "excluded_lines": []}, "Customer.is_delinquent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [285], "excluded_lines": []}, "Customer.get_balance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [289], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 40, 43, 44, 47, 48, 50, 54, 68, 84, 85, 99, 127, 145, 146, 168, 169, 190, 191, 212, 213, 248, 252, 257, 262, 267, 272, 276, 283, 287], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CustomerStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 80, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 80, "excluded_lines": 0}, "missing_lines": [52, 56, 57, 59, 60, 62, 63, 65, 66, 70, 88, 89, 90, 91, 94, 95, 97, 101, 102, 104, 115, 120, 121, 123, 124, 125, 129, 130, 138, 139, 141, 142, 143, 148, 150, 159, 160, 162, 164, 165, 166, 171, 172, 181, 182, 184, 186, 187, 188, 193, 194, 203, 204, 206, 208, 209, 210, 220, 221, 231, 232, 234, 236, 238, 244, 245, 246, 250, 254, 255, 259, 260, 264, 265, 269, 270, 274, 278, 285, 289], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 40, 43, 44, 47, 48, 50, 54, 68, 84, 85, 99, 127, 145, 146, 168, 169, 190, 191, 212, 213, 248, 252, 257, 262, 267, 272, 276, 283, 287], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\plan.py": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 20, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 38, 39, 41, 85, 121, 171, 186, 187, 207, 208, 236, 237, 270, 271, 374, 378, 382, 388, 392, 396, 425, 434, 442, 451, 457, 463, 469, 470], "summary": {"covered_lines": 45, "num_statements": 191, "percent_covered": 23.56020942408377, "percent_covered_display": "24", "missing_lines": 146, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 83, 87, 88, 90, 91, 94, 95, 96, 99, 100, 103, 104, 106, 107, 108, 111, 112, 115, 116, 118, 119, 123, 124, 126, 152, 154, 160, 162, 163, 168, 173, 174, 175, 176, 177, 178, 179, 180, 182, 184, 189, 190, 196, 197, 198, 200, 201, 205, 210, 211, 213, 220, 221, 223, 224, 225, 227, 229, 230, 234, 239, 240, 242, 249, 250, 251, 254, 261, 262, 264, 265, 268, 273, 366, 367, 368, 369, 370, 372, 376, 380, 384, 385, 386, 390, 394, 398, 416, 417, 423, 427, 428, 430, 431, 432, 436, 437, 438, 439, 440, 444, 445, 447, 448, 449, 453, 454, 455, 459, 460, 461, 465, 466, 467, 472, 473, 483, 484, 486, 490, 491, 492, 493, 495, 497, 499, 500, 501], "excluded_lines": [], "functions": {"Plan.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 83], "excluded_lines": []}, "Plan._validate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [87, 88, 90, 91, 94, 95, 96, 99, 100, 103, 104, 106, 107, 108, 111, 112, 115, 116, 118, 119], "excluded_lines": []}, "Plan.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [123, 124, 126, 152, 154, 160, 162, 163, 168], "excluded_lines": []}, "Plan.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [173, 174, 175, 176, 177, 178, 179, 180, 182, 184], "excluded_lines": []}, "Plan.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [189, 190, 196, 197, 198, 200, 201, 205], "excluded_lines": []}, "Plan.get_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [210, 211, 213, 220, 221, 223, 224, 225, 227, 229, 230, 234], "excluded_lines": []}, "Plan.list_active_plans": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [239, 240, 242, 249, 250, 251, 254, 261, 262, 264, 265, 268], "excluded_lines": []}, "Plan.create_default_plans": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [273, 366, 367, 368, 369, 370, 372], "excluded_lines": []}, "Plan.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [376], "excluded_lines": []}, "Plan.is_free": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [380], "excluded_lines": []}, "Plan.get_price": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [384, 385, 386], "excluded_lines": []}, "Plan.has_feature": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [390], "excluded_lines": []}, "Plan.get_limit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [394], "excluded_lines": []}, "Plan.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [398, 416, 417, 423], "excluded_lines": []}, "Plan.calculate_yearly_discount": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [427, 428, 430, 431, 432], "excluded_lines": []}, "Plan.get_price_for_interval": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [436, 437, 438, 439, 440], "excluded_lines": []}, "Plan.update_features": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [444, 445, 447, 448, 449], "excluded_lines": []}, "Plan.activate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [453, 454, 455], "excluded_lines": []}, "Plan.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [459, 460, 461], "excluded_lines": []}, "Plan.deprecate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [465, 466, 467], "excluded_lines": []}, "Plan.get_all_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [472, 473, 483, 484, 486, 490, 491, 492, 493, 495, 497, 499, 500, 501], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 20, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 38, 39, 41, 85, 121, 171, 186, 187, 207, 208, 236, 237, 270, 271, 374, 378, 382, 388, 392, 396, 425, 434, 442, 451, 457, 463, 469, 470], "summary": {"covered_lines": 45, "num_statements": 45, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PlanStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PlanType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 146, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 146, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 83, 87, 88, 90, 91, 94, 95, 96, 99, 100, 103, 104, 106, 107, 108, 111, 112, 115, 116, 118, 119, 123, 124, 126, 152, 154, 160, 162, 163, 168, 173, 174, 175, 176, 177, 178, 179, 180, 182, 184, 189, 190, 196, 197, 198, 200, 201, 205, 210, 211, 213, 220, 221, 223, 224, 225, 227, 229, 230, 234, 239, 240, 242, 249, 250, 251, 254, 261, 262, 264, 265, 268, 273, 366, 367, 368, 369, 370, 372, 376, 380, 384, 385, 386, 390, 394, 398, 416, 417, 423, 427, 428, 430, 431, 432, 436, 437, 438, 439, 440, 444, 445, 447, 448, 449, 453, 454, 455, 459, 460, 461, 465, 466, 467, 472, 473, 483, 484, 486, 490, 491, 492, 493, 495, 497, 499, 500, 501], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 20, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 38, 39, 41, 85, 121, 171, 186, 187, 207, 208, 236, 237, 270, 271, 374, 378, 382, 388, 392, 396, 425, 434, 442, 451, 457, 463, 469, 470], "summary": {"covered_lines": 45, "num_statements": 45, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\subscription.py": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 53, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 91, 94, 96, 97, 99, 125, 133, 134, 139, 141, 157, 158, 179, 180, 206, 207, 232, 236, 243, 247, 253, 261, 284, 307, 328, 359, 360, 382, 383], "summary": {"covered_lines": 73, "num_statements": 163, "percent_covered": 44.785276073619634, "percent_covered_display": "45", "missing_lines": 90, "excluded_lines": 0}, "missing_lines": [92, 127, 143, 145, 146, 147, 148, 149, 150, 153, 155, 160, 161, 167, 168, 169, 171, 172, 177, 182, 183, 185, 191, 192, 194, 195, 197, 199, 200, 204, 209, 210, 212, 219, 220, 221, 223, 225, 226, 230, 234, 238, 245, 249, 250, 251, 255, 256, 258, 259, 263, 264, 270, 276, 277, 282, 286, 287, 293, 299, 300, 305, 309, 310, 315, 320, 321, 326, 330, 350, 351, 357, 362, 363, 372, 373, 374, 376, 378, 379, 380, 385, 386, 401, 402, 403, 405, 407, 408, 409], "excluded_lines": [], "functions": {"Subscription.__init__": {"executed_lines": [73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 91], "summary": {"covered_lines": 17, "num_statements": 18, "percent_covered": 94.44444444444444, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [92], "excluded_lines": []}, "Subscription.save": {"executed_lines": [96, 97, 99, 125, 133, 134, 139], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [127], "excluded_lines": []}, "Subscription.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [143, 145, 146, 147, 148, 149, 150, 153, 155], "excluded_lines": []}, "Subscription.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [160, 161, 167, 168, 169, 171, 172, 177], "excluded_lines": []}, "Subscription.get_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [182, 183, 185, 191, 192, 194, 195, 197, 199, 200, 204], "excluded_lines": []}, "Subscription.get_by_stripe_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [209, 210, 212, 219, 220, 221, 223, 225, 226, 230], "excluded_lines": []}, "Subscription.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [234], "excluded_lines": []}, "Subscription.can_access_features": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [238], "excluded_lines": []}, "Subscription.is_trial": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [245], "excluded_lines": []}, "Subscription.is_trial_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [249, 250, 251], "excluded_lines": []}, "Subscription.days_until_trial_end": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [255, 256, 258, 259], "excluded_lines": []}, "Subscription.cancel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [263, 264, 270, 276, 277, 282], "excluded_lines": []}, "Subscription.suspend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [286, 287, 293, 299, 300, 305], "excluded_lines": []}, "Subscription.reactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [309, 310, 315, 320, 321, 326], "excluded_lines": []}, "Subscription.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [330, 350, 351, 357], "excluded_lines": []}, "Subscription.get_by_stripe_subscription_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [362, 363, 372, 373, 374, 376, 378, 379, 380], "excluded_lines": []}, "Subscription.get_active_by_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [385, 386, 401, 402, 403, 405, 407, 408, 409], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 53, 94, 141, 157, 158, 179, 180, 206, 207, 232, 236, 243, 247, 253, 261, 284, 307, 328, 359, 360, 382, 383], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SubscriptionStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BillingInterval": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PaymentMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription": {"executed_lines": [73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 91, 96, 97, 99, 125, 133, 134, 139], "summary": {"covered_lines": 24, "num_statements": 114, "percent_covered": 21.05263157894737, "percent_covered_display": "21", "missing_lines": 90, "excluded_lines": 0}, "missing_lines": [92, 127, 143, 145, 146, 147, 148, 149, 150, 153, 155, 160, 161, 167, 168, 169, 171, 172, 177, 182, 183, 185, 191, 192, 194, 195, 197, 199, 200, 204, 209, 210, 212, 219, 220, 221, 223, 225, 226, 230, 234, 238, 245, 249, 250, 251, 255, 256, 258, 259, 263, 264, 270, 276, 277, 282, 286, 287, 293, 299, 300, 305, 309, 310, 315, 320, 321, 326, 330, 350, 351, 357, 362, 363, 372, 373, 374, 376, 378, 379, 380, 385, 386, 401, 402, 403, 405, 407, 408, 409], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 53, 94, 141, 157, 158, 179, 180, 206, 207, 232, 236, 243, 247, 253, 261, 284, 307, 328, 359, 360, 382, 383], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\customer_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 17, 24, 25, 27, 29, 31, 111, 119, 127, 210, 254, 287, 324, 342], "summary": {"covered_lines": 20, "num_statements": 136, "percent_covered": 14.705882352941176, "percent_covered_display": "15", "missing_lines": 116, "excluded_lines": 0}, "missing_lines": [54, 56, 57, 60, 61, 62, 63, 66, 68, 77, 86, 99, 101, 102, 104, 105, 106, 107, 108, 109, 113, 114, 115, 116, 117, 121, 122, 123, 124, 125, 154, 156, 157, 158, 161, 163, 164, 165, 167, 168, 169, 171, 172, 173, 175, 176, 177, 179, 180, 186, 187, 193, 194, 196, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 224, 226, 227, 228, 231, 234, 235, 236, 237, 238, 240, 242, 243, 245, 246, 247, 248, 249, 250, 251, 252, 267, 268, 269, 270, 273, 274, 276, 278, 279, 281, 282, 283, 284, 285, 301, 302, 303, 304, 307, 310, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 326], "excluded_lines": [], "functions": {"CustomerService.__init__": {"executed_lines": [29], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CustomerService.create_customer_for_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [54, 56, 57, 60, 61, 62, 63, 66, 68, 77, 86, 99, 101, 102, 104, 105, 106, 107, 108, 109], "excluded_lines": []}, "CustomerService.get_customer_by_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [113, 114, 115, 116, 117], "excluded_lines": []}, "CustomerService.get_customer_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [121, 122, 123, 124, 125], "excluded_lines": []}, "CustomerService.update_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [154, 156, 157, 158, 161, 163, 164, 165, 167, 168, 169, 171, 172, 173, 175, 176, 177, 179, 180, 186, 187, 193, 194, 196, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208], "excluded_lines": []}, "CustomerService.sync_customer_with_stripe": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [224, 226, 227, 228, 231, 234, 235, 236, 237, 238, 240, 242, 243, 245, 246, 247, 248, 249, 250, 251, 252], "excluded_lines": []}, "CustomerService.deactivate_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [267, 268, 269, 270, 273, 274, 276, 278, 279, 281, 282, 283, 284, 285], "excluded_lines": []}, "CustomerService.delete_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [301, 302, 303, 304, 307, 310, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322], "excluded_lines": []}, "CustomerService._serialize_stripe_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [326], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 17, 24, 25, 27, 31, 111, 119, 127, 210, 254, 287, 324, 342], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CustomerService": {"executed_lines": [29], "summary": {"covered_lines": 1, "num_statements": 117, "percent_covered": 0.8547008547008547, "percent_covered_display": "1", "missing_lines": 116, "excluded_lines": 0}, "missing_lines": [54, 56, 57, 60, 61, 62, 63, 66, 68, 77, 86, 99, 101, 102, 104, 105, 106, 107, 108, 109, 113, 114, 115, 116, 117, 121, 122, 123, 124, 125, 154, 156, 157, 158, 161, 163, 164, 165, 167, 168, 169, 171, 172, 173, 175, 176, 177, 179, 180, 186, 187, 193, 194, 196, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 224, 226, 227, 228, 231, 234, 235, 236, 237, 238, 240, 242, 243, 245, 246, 247, 248, 249, 250, 251, 252, 267, 268, 269, 270, 273, 274, 276, 278, 279, 281, 282, 283, 284, 285, 301, 302, 303, 304, 307, 310, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 326], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 17, 24, 25, 27, 31, 111, 119, 127, 210, 254, 287, 324, 342], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\stripe_client.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 24, 25, 27, 29, 30, 32, 35, 42, 43, 49, 53, 56, 59, 60, 66, 67, 69, 118, 154, 162, 175, 184, 236, 247, 260, 281, 309, 336, 358, 384, 402, 420, 437, 481, 483, 486, 487, 488, 491], "summary": {"covered_lines": 46, "num_statements": 207, "percent_covered": 22.22222222222222, "percent_covered_display": "22", "missing_lines": 161, "excluded_lines": 0}, "missing_lines": [36, 37, 38, 44, 50, 71, 72, 74, 76, 78, 82, 84, 88, 90, 93, 95, 99, 101, 105, 107, 113, 127, 128, 134, 135, 137, 143, 144, 146, 148, 149, 151, 152, 156, 157, 158, 159, 160, 168, 169, 170, 171, 172, 173, 177, 178, 179, 180, 181, 182, 194, 196, 197, 201, 212, 213, 214, 215, 217, 220, 222, 223, 225, 226, 228, 230, 231, 233, 234, 238, 239, 243, 244, 245, 253, 254, 255, 256, 257, 258, 266, 267, 268, 273, 275, 276, 278, 279, 290, 291, 298, 299, 301, 303, 304, 306, 307, 316, 317, 322, 323, 325, 326, 328, 330, 331, 333, 334, 343, 344, 349, 350, 352, 353, 355, 356, 366, 367, 373, 374, 376, 378, 379, 381, 382, 390, 391, 396, 397, 399, 400, 408, 409, 414, 415, 417, 418, 426, 427, 432, 434, 435, 445, 447, 448, 450, 451, 453, 454, 457, 462, 463, 465, 466, 468, 470, 472, 473, 474, 476, 477], "excluded_lines": [], "functions": {"StripeClientService.__init__": {"executed_lines": [29, 30], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StripeClientService._configure_stripe": {"executed_lines": [35, 42, 43, 49, 53, 56, 59, 60, 66, 67], "summary": {"covered_lines": 10, "num_statements": 15, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [36, 37, 38, 44, 50], "excluded_lines": []}, "StripeClientService._handle_stripe_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [71, 72, 74, 76, 78, 82, 84, 88, 90, 93, 95, 99, 101, 105, 107, 113], "excluded_lines": []}, "StripeClientService.create_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [127, 128, 134, 135, 137, 143, 144, 146, 148, 149, 151, 152], "excluded_lines": []}, "StripeClientService.get_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [156, 157, 158, 159, 160], "excluded_lines": []}, "StripeClientService.update_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [168, 169, 170, 171, 172, 173], "excluded_lines": []}, "StripeClientService.delete_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [177, 178, 179, 180, 181, 182], "excluded_lines": []}, "StripeClientService.create_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [194, 196, 197, 201, 212, 213, 214, 215, 217, 220, 222, 223, 225, 226, 228, 230, 231, 233, 234], "excluded_lines": []}, "StripeClientService.get_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [238, 239, 243, 244, 245], "excluded_lines": []}, "StripeClientService.update_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [253, 254, 255, 256, 257, 258], "excluded_lines": []}, "StripeClientService.cancel_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [266, 267, 268, 273, 275, 276, 278, 279], "excluded_lines": []}, "StripeClientService.create_price": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [290, 291, 298, 299, 301, 303, 304, 306, 307], "excluded_lines": []}, "StripeClientService.create_product": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [316, 317, 322, 323, 325, 326, 328, 330, 331, 333, 334], "excluded_lines": []}, "StripeClientService.list_prices": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [343, 344, 349, 350, 352, 353, 355, 356], "excluded_lines": []}, "StripeClientService.create_setup_intent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [366, 367, 373, 374, 376, 378, 379, 381, 382], "excluded_lines": []}, "StripeClientService.confirm_setup_intent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [390, 391, 396, 397, 399, 400], "excluded_lines": []}, "StripeClientService.attach_payment_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [408, 409, 414, 415, 417, 418], "excluded_lines": []}, "StripeClientService.list_payment_methods": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [426, 427, 432, 434, 435], "excluded_lines": []}, "StripeClientService.construct_webhook_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [445, 447, 448, 450, 451, 453, 454, 457, 462, 463, 465, 466, 468, 470, 472, 473, 474, 476, 477], "excluded_lines": []}, "get_stripe_client": {"executed_lines": [486, 487, 488], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 24, 25, 27, 32, 69, 118, 154, 162, 175, 184, 236, 247, 260, 281, 309, 336, 358, 384, 402, 420, 437, 481, 483, 491], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"StripeClientService": {"executed_lines": [29, 30, 35, 42, 43, 49, 53, 56, 59, 60, 66, 67], "summary": {"covered_lines": 12, "num_statements": 173, "percent_covered": 6.936416184971098, "percent_covered_display": "7", "missing_lines": 161, "excluded_lines": 0}, "missing_lines": [36, 37, 38, 44, 50, 71, 72, 74, 76, 78, 82, 84, 88, 90, 93, 95, 99, 101, 105, 107, 113, 127, 128, 134, 135, 137, 143, 144, 146, 148, 149, 151, 152, 156, 157, 158, 159, 160, 168, 169, 170, 171, 172, 173, 177, 178, 179, 180, 181, 182, 194, 196, 197, 201, 212, 213, 214, 215, 217, 220, 222, 223, 225, 226, 228, 230, 231, 233, 234, 238, 239, 243, 244, 245, 253, 254, 255, 256, 257, 258, 266, 267, 268, 273, 275, 276, 278, 279, 290, 291, 298, 299, 301, 303, 304, 306, 307, 316, 317, 322, 323, 325, 326, 328, 330, 331, 333, 334, 343, 344, 349, 350, 352, 353, 355, 356, 366, 367, 373, 374, 376, 378, 379, 381, 382, 390, 391, 396, 397, 399, 400, 408, 409, 414, 415, 417, 418, 426, 427, 432, 434, 435, 445, 447, 448, 450, 451, 453, 454, 457, 462, 463, 465, 466, 468, 470, 472, 473, 474, 476, 477], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 24, 25, 27, 32, 69, 118, 154, 162, 175, 184, 236, 247, 260, 281, 309, 336, 358, 384, 402, 420, 437, 481, 483, 486, 487, 488, 491], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\stripe_service.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 21, 22, 23, 26, 27, 29, 40, 72, 128, 191, 232, 277, 311, 372, 415, 417, 426], "summary": {"covered_lines": 23, "num_statements": 133, "percent_covered": 17.293233082706767, "percent_covered_display": "17", "missing_lines": 110, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 33, 36, 37, 38, 42, 43, 45, 46, 52, 54, 55, 61, 63, 65, 66, 67, 80, 81, 87, 88, 90, 96, 102, 109, 110, 115, 120, 121, 126, 136, 137, 147, 148, 150, 151, 153, 155, 162, 172, 173, 178, 183, 184, 189, 197, 198, 203, 208, 215, 216, 220, 225, 226, 230, 238, 239, 240, 245, 247, 253, 260, 261, 265, 270, 271, 275, 279, 280, 282, 294, 295, 299, 304, 305, 309, 319, 320, 328, 329, 331, 332, 334, 336, 343, 351, 352, 358, 363, 364, 370, 379, 380, 384, 389, 391, 392, 395, 399, 400, 403, 407, 408, 411, 420, 421, 422, 423], "excluded_lines": [], "functions": {"StripeService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 33, 36, 37, 38], "excluded_lines": []}, "StripeService._initialize_stripe": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 46, 52, 54, 55, 61, 63, 65, 66, 67], "excluded_lines": []}, "StripeService.create_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [80, 81, 87, 88, 90, 96, 102, 109, 110, 115, 120, 121, 126], "excluded_lines": []}, "StripeService.create_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [136, 137, 147, 148, 150, 151, 153, 155, 162, 172, 173, 178, 183, 184, 189], "excluded_lines": []}, "StripeService.update_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [197, 198, 203, 208, 215, 216, 220, 225, 226, 230], "excluded_lines": []}, "StripeService.cancel_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [238, 239, 240, 245, 247, 253, 260, 261, 265, 270, 271, 275], "excluded_lines": []}, "StripeService.get_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [279, 280, 282, 294, 295, 299, 304, 305, 309], "excluded_lines": []}, "StripeService.create_payment_intent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [319, 320, 328, 329, 331, 332, 334, 336, 343, 351, 352, 358, 363, 364, 370], "excluded_lines": []}, "StripeService.verify_webhook_signature": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [379, 380, 384, 389, 391, 392, 395, 399, 400, 403, 407, 408, 411], "excluded_lines": []}, "get_stripe_service": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [420, 421, 422, 423], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 21, 22, 23, 26, 27, 29, 40, 72, 128, 191, 232, 277, 311, 372, 415, 417, 426], "summary": {"covered_lines": 23, "num_statements": 23, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"StripeService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 106, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 106, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 33, 36, 37, 38, 42, 43, 45, 46, 52, 54, 55, 61, 63, 65, 66, 67, 80, 81, 87, 88, 90, 96, 102, 109, 110, 115, 120, 121, 126, 136, 137, 147, 148, 150, 151, 153, 155, 162, 172, 173, 178, 183, 184, 189, 197, 198, 203, 208, 215, 216, 220, 225, 226, 230, 238, 239, 240, 245, 247, 253, 260, 261, 265, 270, 271, 275, 279, 280, 282, 294, 295, 299, 304, 305, 309, 319, 320, 328, 329, 331, 332, 334, 336, 343, 351, 352, 358, 363, 364, 370, 379, 380, 384, 389, 391, 392, 395, 399, 400, 403, 407, 408, 411], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 21, 22, 23, 26, 27, 29, 40, 72, 128, 191, 232, 277, 311, 372, 415, 417, 426], "summary": {"covered_lines": 23, "num_statements": 27, "percent_covered": 85.18518518518519, "percent_covered_display": "85", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [420, 421, 422, 423], "excluded_lines": []}}}, "src\\payment\\services\\subscription_service.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 28, 29, 31, 33, 34, 36, 153, 161, 169, 226, 234, 247, 265], "summary": {"covered_lines": 23, "num_statements": 100, "percent_covered": 23.0, "percent_covered_display": "23", "missing_lines": 77, "excluded_lines": 0}, "missing_lines": [64, 66, 67, 68, 70, 71, 74, 75, 76, 81, 82, 83, 85, 86, 89, 90, 91, 94, 95, 96, 99, 108, 117, 135, 138, 139, 141, 142, 144, 145, 146, 147, 148, 149, 150, 151, 155, 156, 157, 158, 159, 163, 164, 165, 166, 167, 190, 192, 193, 194, 197, 203, 204, 205, 206, 207, 209, 210, 212, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 228, 229, 230, 231, 232, 236, 245, 249], "excluded_lines": [], "functions": {"SubscriptionService.__init__": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SubscriptionService.create_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [64, 66, 67, 68, 70, 71, 74, 75, 76, 81, 82, 83, 85, 86, 89, 90, 91, 94, 95, 96, 99, 108, 117, 135, 138, 139, 141, 142, 144, 145, 146, 147, 148, 149, 150, 151], "excluded_lines": []}, "SubscriptionService.get_subscription_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [155, 156, 157, 158, 159], "excluded_lines": []}, "SubscriptionService.get_active_subscription_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 167], "excluded_lines": []}, "SubscriptionService.cancel_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [190, 192, 193, 194, 197, 203, 204, 205, 206, 207, 209, 210, 212, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224], "excluded_lines": []}, "SubscriptionService._get_price_id_for_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [228, 229, 230, 231, 232], "excluded_lines": []}, "SubscriptionService._map_stripe_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [236, 245], "excluded_lines": []}, "SubscriptionService._serialize_stripe_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [249], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 28, 29, 31, 36, 153, 161, 169, 226, 234, 247, 265], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SubscriptionService": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 79, "percent_covered": 2.5316455696202533, "percent_covered_display": "3", "missing_lines": 77, "excluded_lines": 0}, "missing_lines": [64, 66, 67, 68, 70, 71, 74, 75, 76, 81, 82, 83, 85, 86, 89, 90, 91, 94, 95, 96, 99, 108, 117, 135, 138, 139, 141, 142, 144, 145, 146, 147, 148, 149, 150, 151, 155, 156, 157, 158, 159, 163, 164, 165, 166, 167, 190, 192, 193, 194, 197, 203, 204, 205, 206, 207, 209, 210, 212, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 228, 229, 230, 231, 232, 236, 245, 249], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 28, 29, 31, 36, 153, 161, 169, 226, 234, 247, 265], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\auth.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 26, 29, 30, 32, 33, 34, 36, 40, 44, 78, 83, 84, 86, 87, 88, 89, 90, 92, 121, 136, 146, 151, 152, 157, 158, 163, 221, 275, 344, 357, 358, 360, 374, 378, 382, 386, 390, 394, 405, 408, 410, 419, 422, 432, 438, 444, 449, 451, 452, 453, 455, 457, 458, 494, 498, 501, 502], "summary": {"covered_lines": 63, "num_statements": 208, "percent_covered": 30.28846153846154, "percent_covered_display": "30", "missing_lines": 145, "excluded_lines": 0}, "missing_lines": [38, 42, 46, 49, 50, 53, 54, 57, 58, 61, 62, 65, 66, 69, 73, 74, 76, 80, 94, 97, 101, 103, 104, 105, 106, 108, 109, 111, 112, 114, 123, 125, 126, 127, 130, 131, 132, 134, 138, 140, 141, 142, 144, 148, 149, 154, 155, 161, 172, 173, 174, 176, 177, 179, 183, 198, 200, 207, 215, 217, 218, 219, 228, 229, 230, 232, 233, 235, 239, 252, 254, 261, 269, 271, 272, 273, 277, 278, 281, 282, 284, 287, 288, 289, 290, 293, 294, 295, 298, 299, 300, 301, 304, 305, 306, 307, 316, 317, 320, 321, 322, 324, 325, 327, 328, 329, 330, 331, 334, 335, 337, 339, 340, 341, 342, 346, 348, 368, 369, 370, 371, 372, 376, 380, 384, 388, 392, 396, 411, 424, 426, 427, 429, 434, 435, 440, 441, 468, 471, 473, 474, 475, 483, 484, 485], "excluded_lines": [], "functions": {"PasswordManager.__init__": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordManager.hash_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [38], "excluded_lines": []}, "PasswordManager.verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [42], "excluded_lines": []}, "PasswordManager.validate_password_strength": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [46, 49, 50, 53, 54, 57, 58, 61, 62, 65, 66, 69, 73, 74, 76], "excluded_lines": []}, "PasswordManager.generate_secure_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [80], "excluded_lines": []}, "JWTManager.__init__": {"executed_lines": [87, 88, 89, 90], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "JWTManager._get_jwt_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [94, 97, 101, 103, 104, 105, 106, 108, 109, 111, 112, 114], "excluded_lines": []}, "JWTManager.get_active_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [123, 125, 126, 127, 130, 131, 132, 134], "excluded_lines": []}, "JWTManager.get_key_by_kid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [138, 140, 141, 142, 144], "excluded_lines": []}, "JWTManager.get_all_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [148, 149], "excluded_lines": []}, "JWTManager.secret_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [154, 155], "excluded_lines": []}, "JWTManager.public_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [161], "excluded_lines": []}, "JWTManager.create_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [172, 173, 174, 176, 177, 179, 183, 198, 200, 207, 215, 217, 218, 219], "excluded_lines": []}, "JWTManager.create_refresh_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [228, 229, 230, 232, 233, 235, 239, 252, 254, 261, 269, 271, 272, 273], "excluded_lines": []}, "JWTManager.verify_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [277, 278, 281, 282, 284, 287, 288, 289, 290, 293, 294, 295, 298, 299, 300, 301, 304, 305, 306, 307, 316, 317, 320, 321, 322, 324, 325, 327, 328, 329, 330, 331, 334, 335, 337, 339, 340, 341, 342], "excluded_lines": []}, "JWTManager.extract_user_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [346, 348], "excluded_lines": []}, "AuthContext.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [368, 369, 370, 371, 372], "excluded_lines": []}, "AuthContext.is_master": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [376], "excluded_lines": []}, "AuthContext.is_member": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [380], "excluded_lines": []}, "AuthContext.can_manage_users": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [384], "excluded_lines": []}, "AuthContext.can_manage_billing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [388], "excluded_lines": []}, "AuthContext.can_access_analytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [392], "excluded_lines": []}, "AuthContext.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [396], "excluded_lines": []}, "extract_auth_context_from_event": {"executed_lines": [408, 410, 419], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [411], "excluded_lines": []}, "require_auth": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [424, 426, 427, 429], "excluded_lines": []}, "require_master_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [434, 435], "excluded_lines": []}, "require_tenant_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [440, 441], "excluded_lines": []}, "auth_required": {"executed_lines": [449, 451, 452, 494], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "auth_required.wrapper": {"executed_lines": [453, 455, 457, 458], "summary": {"covered_lines": 4, "num_statements": 12, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [468, 471, 473, 474, 475, 483, 484, 485], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 26, 29, 30, 32, 36, 40, 44, 78, 83, 84, 86, 92, 121, 136, 146, 151, 152, 157, 158, 163, 221, 275, 344, 357, 358, 360, 374, 378, 382, 386, 390, 394, 405, 422, 432, 438, 444, 498, 501, 502], "summary": {"covered_lines": 46, "num_statements": 46, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PasswordManager": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 20, "percent_covered": 10.0, "percent_covered_display": "10", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [38, 42, 46, 49, 50, 53, 54, 57, 58, 61, 62, 65, 66, 69, 73, 74, 76, 80], "excluded_lines": []}, "JWTManager": {"executed_lines": [87, 88, 89, 90], "summary": {"covered_lines": 4, "num_statements": 103, "percent_covered": 3.883495145631068, "percent_covered_display": "4", "missing_lines": 99, "excluded_lines": 0}, "missing_lines": [94, 97, 101, 103, 104, 105, 106, 108, 109, 111, 112, 114, 123, 125, 126, 127, 130, 131, 132, 134, 138, 140, 141, 142, 144, 148, 149, 154, 155, 161, 172, 173, 174, 176, 177, 179, 183, 198, 200, 207, 215, 217, 218, 219, 228, 229, 230, 232, 233, 235, 239, 252, 254, 261, 269, 271, 272, 273, 277, 278, 281, 282, 284, 287, 288, 289, 290, 293, 294, 295, 298, 299, 300, 301, 304, 305, 306, 307, 316, 317, 320, 321, 322, 324, 325, 327, 328, 329, 330, 331, 334, 335, 337, 339, 340, 341, 342, 346, 348], "excluded_lines": []}, "AuthContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [368, 369, 370, 371, 372, 376, 380, 384, 388, 392, 396], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 26, 29, 30, 32, 36, 40, 44, 78, 83, 84, 86, 92, 121, 136, 146, 151, 152, 157, 158, 163, 221, 275, 344, 357, 358, 360, 374, 378, 382, 386, 390, 394, 405, 408, 410, 419, 422, 432, 438, 444, 449, 451, 452, 453, 455, 457, 458, 494, 498, 501, 502], "summary": {"covered_lines": 57, "num_statements": 74, "percent_covered": 77.02702702702703, "percent_covered_display": "77", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [411, 424, 426, 427, 429, 434, 435, 440, 441, 468, 471, 473, 474, 475, 483, 484, 485], "excluded_lines": []}}}, "src\\shared\\config.py": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 65, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 82, 83, 85, 92, 95, 97, 100, 102, 105, 107, 110, 115, 117, 119, 122, 125, 142, 144, 151], "summary": {"covered_lines": 62, "num_statements": 71, "percent_covered": 87.32394366197182, "percent_covered_display": "87", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [112, 118, 120, 127, 128, 133, 134, 136, 153], "excluded_lines": [], "functions": {"get_settings": {"executed_lines": [97], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_development": {"executed_lines": [102], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_production": {"executed_lines": [107], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_testing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [112], "excluded_lines": []}, "get_log_level": {"executed_lines": [117, 119, 122], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [118, 120], "excluded_lines": []}, "get_cors_origins": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [127, 128, 133, 134, 136], "excluded_lines": []}, "get_database_config": {"executed_lines": [144], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_storage_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [153], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 65, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 82, 83, 85, 92, 95, 100, 105, 110, 115, 125, 142, 151], "summary": {"covered_lines": 55, "num_statements": 55, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 65, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 82, 83, 85, 92, 95, 97, 100, 102, 105, 107, 110, 115, 117, 119, 122, 125, 142, 144, 151], "summary": {"covered_lines": 62, "num_statements": 71, "percent_covered": 87.32394366197182, "percent_covered_display": "87", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [112, 118, 120, 127, 128, 133, 134, 136, 153], "excluded_lines": []}}}, "src\\shared\\database.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 24, 25, 28, 30, 42, 44, 46, 48, 50, 57, 112, 119, 121, 123, 124, 127, 130, 131, 133, 135, 136, 139, 157, 178, 251, 306, 389], "summary": {"covered_lines": 37, "num_statements": 136, "percent_covered": 27.205882352941178, "percent_covered_display": "27", "missing_lines": 99, "excluded_lines": 0}, "missing_lines": [37, 47, 52, 53, 54, 55, 65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106, 132, 137, 141, 142, 152, 153, 155, 158, 159, 169, 170, 172, 189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245, 259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300, 318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": [], "functions": {"DynamoDBClient.__init__": {"executed_lines": [24, 25, 28, 30, 42], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "DynamoDBClient._add_tenant_context": {"executed_lines": [46, 48], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [47], "excluded_lines": []}, "DynamoDBClient._remove_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55], "excluded_lines": []}, "DynamoDBClient.get_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106], "excluded_lines": []}, "DynamoDBClient.put_item": {"executed_lines": [119, 121, 123, 124, 127, 130, 131, 133, 135, 136, 139, 157], "summary": {"covered_lines": 12, "num_statements": 24, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [132, 137, 141, 142, 152, 153, 155, 158, 159, 169, 170, 172], "excluded_lines": []}, "DynamoDBClient.update_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245], "excluded_lines": []}, "DynamoDBClient.delete_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300], "excluded_lines": []}, "DynamoDBClient.query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DynamoDBClient": {"executed_lines": [24, 25, 28, 30, 42, 46, 48, 119, 121, 123, 124, 127, 130, 131, 133, 135, 136, 139, 157], "summary": {"covered_lines": 19, "num_statements": 118, "percent_covered": 16.10169491525424, "percent_covered_display": "16", "missing_lines": 99, "excluded_lines": 0}, "missing_lines": [37, 47, 52, 53, 54, 55, 65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106, 132, 137, 141, 142, 152, 153, 155, 158, 159, 169, 170, 172, 189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245, 259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300, 318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\exceptions.py": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 66, 67, 69, 77, 78, 80, 88, 89, 91, 99, 100, 102, 118, 119, 121, 137, 138, 140, 153, 154, 156, 172, 173, 175, 191, 192, 194, 210, 211, 213, 226, 227, 229, 242, 243, 245, 261, 262, 264, 280, 281, 283, 297, 298, 300, 304, 305, 307, 311, 312, 314, 318, 319, 321, 325, 326, 328, 333, 334, 336, 340, 341, 343, 347, 348, 350, 354, 355, 357, 368, 369, 371, 384, 385, 387], "summary": {"covered_lines": 59, "num_statements": 154, "percent_covered": 38.311688311688314, "percent_covered_display": "38", "missing_lines": 95, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26, 38, 39, 50, 62, 63, 74, 85, 96, 109, 110, 111, 112, 113, 115, 128, 129, 130, 131, 132, 134, 146, 147, 148, 150, 163, 164, 165, 166, 167, 169, 182, 183, 184, 185, 186, 188, 201, 202, 203, 204, 205, 207, 219, 220, 221, 223, 235, 236, 237, 239, 252, 253, 254, 255, 256, 258, 271, 272, 273, 274, 275, 277, 289, 290, 291, 293, 301, 308, 315, 322, 329, 337, 344, 351, 358, 359, 360, 361, 362, 372, 373, 374, 375, 376, 377, 388, 389, 390, 391, 392], "excluded_lines": [], "functions": {"PlatformException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26], "excluded_lines": []}, "ValidationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [38, 39], "excluded_lines": []}, "BusinessLogicException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "PaymentException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [388, 389, 390, 391, 392], "excluded_lines": []}, "AuthenticationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [74], "excluded_lines": []}, "AuthorizationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [85], "excluded_lines": []}, "SecurityException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [96], "excluded_lines": []}, "ResourceNotFoundException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 112, 113, 115], "excluded_lines": []}, "ResourceConflictException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 134], "excluded_lines": []}, "RateLimitException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [146, 147, 148, 150], "excluded_lines": []}, "ExternalServiceException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 167, 169], "excluded_lines": []}, "EmailException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [182, 183, 184, 185, 186, 188], "excluded_lines": []}, "DatabaseException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [201, 202, 203, 204, 205, 207], "excluded_lines": []}, "TenantException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [219, 220, 221, 223], "excluded_lines": []}, "UserException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 239], "excluded_lines": []}, "AgentException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [271, 272, 273, 274, 275, 277], "excluded_lines": []}, "ConfigurationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [289, 290, 291, 293], "excluded_lines": []}, "InvalidCredentialsException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [301], "excluded_lines": []}, "TokenExpiredException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [308], "excluded_lines": []}, "InvalidTokenException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [315], "excluded_lines": []}, "AccountLockedException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [322], "excluded_lines": []}, "EmailNotVerifiedException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [329], "excluded_lines": []}, "InvalidEmailException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [337], "excluded_lines": []}, "WeakPasswordException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [344], "excluded_lines": []}, "InvalidTenantException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [351], "excluded_lines": []}, "TenantLimitExceededException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [358, 359, 360, 361, 362], "excluded_lines": []}, "SecurityViolationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [372, 373, 374, 375, 376, 377], "excluded_lines": []}, "": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 66, 67, 69, 77, 78, 80, 88, 89, 91, 99, 100, 102, 118, 119, 121, 137, 138, 140, 153, 154, 156, 172, 173, 175, 191, 192, 194, 210, 211, 213, 226, 227, 229, 242, 243, 245, 261, 262, 264, 280, 281, 283, 297, 298, 300, 304, 305, 307, 311, 312, 314, 318, 319, 321, 325, 326, 328, 333, 334, 336, 340, 341, 343, 347, 348, 350, 354, 355, 357, 368, 369, 371, 384, 385, 387], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PlatformException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26], "excluded_lines": []}, "ValidationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [38, 39], "excluded_lines": []}, "BusinessLogicException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "PaymentException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [388, 389, 390, 391, 392], "excluded_lines": []}, "AuthenticationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [74], "excluded_lines": []}, "AuthorizationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [85], "excluded_lines": []}, "SecurityException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [96], "excluded_lines": []}, "ResourceNotFoundException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 112, 113, 115], "excluded_lines": []}, "ResourceConflictException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 134], "excluded_lines": []}, "RateLimitException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [146, 147, 148, 150], "excluded_lines": []}, "ExternalServiceException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 167, 169], "excluded_lines": []}, "EmailException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [182, 183, 184, 185, 186, 188], "excluded_lines": []}, "DatabaseException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [201, 202, 203, 204, 205, 207], "excluded_lines": []}, "TenantException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [219, 220, 221, 223], "excluded_lines": []}, "UserException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 239], "excluded_lines": []}, "AgentException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [271, 272, 273, 274, 275, 277], "excluded_lines": []}, "ConfigurationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [289, 290, 291, 293], "excluded_lines": []}, "InvalidCredentialsException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [301], "excluded_lines": []}, "TokenExpiredException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [308], "excluded_lines": []}, "InvalidTokenException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [315], "excluded_lines": []}, "AccountLockedException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [322], "excluded_lines": []}, "EmailNotVerifiedException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [329], "excluded_lines": []}, "InvalidEmailException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [337], "excluded_lines": []}, "WeakPasswordException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [344], "excluded_lines": []}, "InvalidTenantException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [351], "excluded_lines": []}, "TenantLimitExceededException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [358, 359, 360, 361, 362], "excluded_lines": []}, "SecurityViolationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [372, 373, 374, 375, 376, 377], "excluded_lines": []}, "": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 66, 67, 69, 77, 78, 80, 88, 89, 91, 99, 100, 102, 118, 119, 121, 137, 138, 140, 153, 154, 156, 172, 173, 175, 191, 192, 194, 210, 211, 213, 226, 227, 229, 242, 243, 245, 261, 262, 264, 280, 281, 283, 297, 298, 300, 304, 305, 307, 311, 312, 314, 318, 319, 321, 325, 326, 328, 333, 334, 336, 340, 341, 343, 347, 348, 350, 354, 355, 357, 368, 369, 371, 384, 385, 387], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\logger.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 25, 28, 39, 43, 44, 51, 53, 56, 58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82, 85, 87, 89, 98, 104, 107, 117, 127, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 43, "num_statements": 59, "percent_covered": 72.88135593220339, "percent_covered_display": "73", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [40, 66, 142, 154, 155, 157, 170, 180, 181, 183, 197, 208, 209, 211, 224, 234], "excluded_lines": [], "functions": {"StructuredFormatter.format": {"executed_lines": [25, 28, 39, 43, 44, 51, 53], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "setup_logging": {"executed_lines": [58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [66], "excluded_lines": []}, "get_lambda_logger": {"executed_lines": [87, 89, 98, 104], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "log_api_request": {"executed_lines": [117, 127], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "log_api_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [142, 154, 155, 157], "excluded_lines": []}, "log_database_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [170, 180, 181, 183], "excluded_lines": []}, "log_external_api_call": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [197, 208, 209, 211], "excluded_lines": []}, "log_security_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [224, 234], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 85, 107, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"StructuredFormatter": {"executed_lines": [25, 28, 39, 43, 44, 51, 53], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82, 85, 87, 89, 98, 104, 107, 117, 127, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 36, "num_statements": 51, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [66, 142, 154, 155, 157, 170, 180, 181, 183, 197, 208, 209, 211, 224, 234], "excluded_lines": []}}}, "src\\shared\\responses.py": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 201, 203, 204, 234], "summary": {"covered_lines": 33, "num_statements": 69, "percent_covered": 47.82608695652174, "percent_covered_display": "48", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37, 57, 64, 65, 67, 68, 70, 71, 73, 91, 98, 99, 101, 106, 123, 131, 139, 147, 156, 169, 176, 177, 179, 180, 181, 189, 197, 212, 213, 214, 216, 227, 236], "excluded_lines": [], "functions": {"APIResponse.success": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37], "excluded_lines": []}, "APIResponse.error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [57, 64, 65, 67, 68, 70, 71, 73], "excluded_lines": []}, "APIResponse.created": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [91, 98, 99, 101], "excluded_lines": []}, "APIResponse.no_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [106], "excluded_lines": []}, "APIResponse.unauthorized": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [123], "excluded_lines": []}, "APIResponse.forbidden": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [131], "excluded_lines": []}, "APIResponse.not_found": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [139], "excluded_lines": []}, "APIResponse.conflict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [147], "excluded_lines": []}, "APIResponse.validation_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [156], "excluded_lines": []}, "APIResponse.rate_limit_exceeded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [169, 176, 177, 179, 180, 181], "excluded_lines": []}, "APIResponse.internal_server_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "APIResponse.service_unavailable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [197], "excluded_lines": []}, "PaginatedResponse.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 216, 227], "excluded_lines": []}, "handle_cors_preflight": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [236], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 201, 203, 204, 234], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"APIResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37, 57, 64, 65, 67, 68, 70, 71, 73, 91, 98, 99, 101, 106, 123, 131, 139, 147, 156, 169, 176, 177, 179, 180, 181, 189, 197], "excluded_lines": []}, "PaginatedResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 216, 227], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 201, 203, 204, 234], "summary": {"covered_lines": 33, "num_statements": 34, "percent_covered": 97.05882352941177, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [236], "excluded_lines": []}}}, "src\\shared\\secrets.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 19, 20, 22, 23, 24, 25, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 309, 337], "summary": {"covered_lines": 24, "num_statements": 120, "percent_covered": 20.0, "percent_covered_display": "20", "missing_lines": 96, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 57, 59, 60, 61, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85, 104, 106, 107, 108, 109, 110, 130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154, 173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203, 218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237, 251, 257, 258, 264, 265, 267, 268, 270, 285, 287, 288, 289, 290, 292, 296, 297, 306, 311, 313, 314, 315, 316, 318, 339], "excluded_lines": [], "functions": {"SecretsManager.__init__": {"executed_lines": [23, 24, 25], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecretsManager.get_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 57, 59, 60, 61, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85], "excluded_lines": []}, "SecretsManager.get_secret_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [104, 106, 107, 108, 109, 110], "excluded_lines": []}, "SecretsManager.create_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154], "excluded_lines": []}, "SecretsManager.update_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203], "excluded_lines": []}, "SecretsManager.get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237], "excluded_lines": []}, "SecretsManager._create_default_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [251, 257, 258, 264, 265, 267, 268, 270], "excluded_lines": []}, "SecretsManager.get_integration_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [285, 287, 288, 289, 290, 292], "excluded_lines": []}, "SecretsManager.clear_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [296, 297], "excluded_lines": []}, "get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}, "get_jwt_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [311, 313, 314, 315, 316, 318], "excluded_lines": []}, "get_integration_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [339], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 19, 20, 22, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 309, 337], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SecretsManager": {"executed_lines": [23, 24, 25], "summary": {"covered_lines": 3, "num_statements": 91, "percent_covered": 3.2967032967032965, "percent_covered_display": "3", "missing_lines": 88, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 57, 59, 60, 61, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85, 104, 106, 107, 108, 109, 110, 130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154, 173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203, 218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237, 251, 257, 258, 264, 265, 267, 268, 270, 285, 287, 288, 289, 290, 292, 296, 297], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 19, 20, 22, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 309, 337], "summary": {"covered_lines": 21, "num_statements": 29, "percent_covered": 72.41379310344827, "percent_covered_display": "72", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [306, 311, 313, 314, 315, 316, 318, 339], "excluded_lines": []}}}, "src\\shared\\validators.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 28, 38, 47, 65, 83, 104, 123, 137, 138, 140, 141, 142, 143, 144, 146, 147, 148, 151, 152, 153, 156, 157, 158, 161, 162, 163, 169, 170, 172, 173, 175, 176, 177, 181, 182, 184, 186, 187, 188, 192, 193, 195, 196, 199, 200, 202, 205, 206, 208, 211, 212, 214, 215, 216, 217, 219, 220, 221, 224, 225, 226, 230, 231, 233, 234, 235, 238, 239, 241, 242, 243, 246, 247, 249, 250, 256, 257, 259, 260, 261, 262, 264, 265, 266, 271, 272, 273, 279, 280, 282, 283, 286, 287, 289, 291, 292, 293, 313, 314, 316, 317, 319, 320, 321, 326, 327, 328, 334, 335, 337, 338, 340, 341, 342, 346, 385, 406, 407, 408, 409], "summary": {"covered_lines": 113, "num_statements": 225, "percent_covered": 50.22222222222222, "percent_covered_display": "50", "missing_lines": 112, "excluded_lines": 0}, "missing_lines": [30, 32, 33, 34, 35, 40, 41, 42, 43, 44, 49, 50, 52, 53, 55, 56, 59, 60, 62, 67, 68, 70, 71, 73, 74, 77, 78, 80, 85, 86, 89, 92, 93, 96, 97, 98, 99, 101, 106, 107, 109, 117, 118, 120, 125, 126, 128, 129, 131, 132, 134, 149, 154, 159, 164, 165, 166, 178, 189, 222, 227, 267, 268, 269, 274, 275, 276, 294, 295, 298, 299, 300, 302, 303, 304, 306, 307, 308, 310, 322, 323, 324, 329, 330, 331, 343, 352, 354, 356, 357, 359, 362, 363, 365, 366, 367, 368, 370, 371, 372, 373, 377, 382, 387, 388, 391, 394, 395, 398, 399, 400, 402], "excluded_lines": [], "functions": {"validate_email_address": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [30, 32, 33, 34, 35], "excluded_lines": []}, "validate_uuid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [40, 41, 42, 43, 44], "excluded_lines": []}, "validate_tenant_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [49, 50, 52, 53, 55, 56, 59, 60, 62], "excluded_lines": []}, "validate_user_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [67, 68, 70, 71, 73, 74, 77, 78, 80], "excluded_lines": []}, "validate_phone_number": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [85, 86, 89, 92, 93, 96, 97, 98, 99, 101], "excluded_lines": []}, "validate_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [106, 107, 109, 117, 118, 120], "excluded_lines": []}, "validate_pagination_params": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [125, 126, 128, 129, 131, 132, 134], "excluded_lines": []}, "RegisterRequestValidator.validate_tenant_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [149], "excluded_lines": []}, "RegisterRequestValidator.validate_user_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [154], "excluded_lines": []}, "RegisterRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [159], "excluded_lines": []}, "RegisterRequestValidator.validate_phone_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [164, 165, 166], "excluded_lines": []}, "LoginRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [178], "excluded_lines": []}, "ForgotPasswordRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "InviteUserRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [222], "excluded_lines": []}, "InviteUserRequestValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [227], "excluded_lines": []}, "UpdateUserRequestValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [267, 268, 269], "excluded_lines": []}, "UpdateUserRequestValidator.validate_phone_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [274, 275, 276], "excluded_lines": []}, "TenantSettingsValidator.validate_settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [294, 295, 298, 299, 300, 302, 303, 304, 306, 307, 308, 310], "excluded_lines": []}, "UserProfileUpdateValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [322, 323, 324], "excluded_lines": []}, "UserProfileUpdateValidator.validate_phone_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [329, 330, 331], "excluded_lines": []}, "ChangePasswordValidator.validate_new_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "validate_request_body": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [352, 354, 356, 357, 359, 362, 363, 365, 366, 367, 368, 370, 371, 372, 373, 377, 382], "excluded_lines": []}, "sanitize_string": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [387, 388, 391, 394, 395, 398, 399, 400, 402], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 28, 38, 47, 65, 83, 104, 123, 137, 138, 140, 141, 142, 143, 144, 146, 147, 148, 151, 152, 153, 156, 157, 158, 161, 162, 163, 169, 170, 172, 173, 175, 176, 177, 181, 182, 184, 186, 187, 188, 192, 193, 195, 196, 199, 200, 202, 205, 206, 208, 211, 212, 214, 215, 216, 217, 219, 220, 221, 224, 225, 226, 230, 231, 233, 234, 235, 238, 239, 241, 242, 243, 246, 247, 249, 250, 256, 257, 259, 260, 261, 262, 264, 265, 266, 271, 272, 273, 279, 280, 282, 283, 286, 287, 289, 291, 292, 293, 313, 314, 316, 317, 319, 320, 321, 326, 327, 328, 334, 335, 337, 338, 340, 341, 342, 346, 385, 406, 407, 408, 409], "summary": {"covered_lines": 113, "num_statements": 113, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BaseValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RegisterRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [149, 154, 159, 164, 165, 166], "excluded_lines": []}, "LoginRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [178], "excluded_lines": []}, "ForgotPasswordRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "ResetPasswordRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VerifyEmailRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RefreshTokenRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InviteUserRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [222, 227], "excluded_lines": []}, "AcceptInvitationRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CreateSubscriptionRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CancelSubscriptionRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UpdateUserRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [267, 268, 269, 274, 275, 276], "excluded_lines": []}, "PaginationValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantSettingsValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [294, 295, 298, 299, 300, 302, 303, 304, 306, 307, 308, 310], "excluded_lines": []}, "UserProfileUpdateValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [322, 323, 324, 329, 330, 331], "excluded_lines": []}, "ChangePasswordValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 28, 38, 47, 65, 83, 104, 123, 137, 138, 140, 141, 142, 143, 144, 146, 147, 148, 151, 152, 153, 156, 157, 158, 161, 162, 163, 169, 170, 172, 173, 175, 176, 177, 181, 182, 184, 186, 187, 188, 192, 193, 195, 196, 199, 200, 202, 205, 206, 208, 211, 212, 214, 215, 216, 217, 219, 220, 221, 224, 225, 226, 230, 231, 233, 234, 235, 238, 239, 241, 242, 243, 246, 247, 249, 250, 256, 257, 259, 260, 261, 262, 264, 265, 266, 271, 272, 273, 279, 280, 282, 283, 286, 287, 289, 291, 292, 293, 313, 314, 316, 317, 319, 320, 321, 326, 327, 328, 334, 335, 337, 338, 340, 341, 342, 346, 385, 406, 407, 408, 409], "summary": {"covered_lines": 113, "num_statements": 190, "percent_covered": 59.473684210526315, "percent_covered_display": "59", "missing_lines": 77, "excluded_lines": 0}, "missing_lines": [30, 32, 33, 34, 35, 40, 41, 42, 43, 44, 49, 50, 52, 53, 55, 56, 59, 60, 62, 67, 68, 70, 71, 73, 74, 77, 78, 80, 85, 86, 89, 92, 93, 96, 97, 98, 99, 101, 106, 107, 109, 117, 118, 120, 125, 126, 128, 129, 131, 132, 134, 352, 354, 356, 357, 359, 362, 363, 365, 366, 367, 368, 370, 371, 372, 373, 377, 382, 387, 388, 391, 394, 395, 398, 399, 400, 402], "excluded_lines": []}}}, "src\\tenant\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [9, 10], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [9, 10], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [9, 10], "excluded_lines": []}}}, "src\\user\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\user\\handlers\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\user\\handlers\\change_password.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 23, 26, 38, 39, 41, 42, 45, 52, 54, 56, 57, 58, 59, 64, 67, 68, 69, 70, 72, 73, 75, 76, 77, 83, 84, 85, 86, 87, 92, 93, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 126, 132, 135, 136, 139, 141, 142, 143, 149, 157, 158, 167, 172, 173, 174, 184, 191, 192, 193, 203, 209], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 59, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 59, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 45, 52, 54, 56, 57, 58, 59, 64, 67, 68, 69, 70, 72, 73, 75, 76, 77, 83, 84, 85, 86, 87, 92, 93, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 126, 132, 135, 136, 139, 141, 142, 143, 149, 157, 158, 167, 172, 173, 174, 184, 191, 192, 193, 203, 209], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 23, 26], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 23, 26, 38, 39, 41, 42, 45, 52, 54, 56, 57, 58, 59, 64, 67, 68, 69, 70, 72, 73, 75, 76, 77, 83, 84, 85, 86, 87, 92, 93, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 126, 132, 135, 136, 139, 141, 142, 143, 149, 157, 158, 167, 172, 173, 174, 184, 191, 192, 193, 203, 209], "excluded_lines": []}}}, "src\\user\\handlers\\deactivate_user.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 81, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 81, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 35, 38, 45, 47, 49, 50, 51, 52, 57, 60, 61, 62, 63, 64, 66, 67, 69, 70, 71, 77, 78, 79, 85, 86, 88, 89, 90, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 118, 124, 125, 126, 132, 133, 134, 140, 141, 142, 143, 146, 148, 149, 150, 156, 169, 170, 171, 172, 174, 180, 181, 190, 195, 196, 197, 207, 214, 215, 216, 226, 233], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 73, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 73, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 35, 38, 45, 47, 49, 50, 51, 52, 57, 60, 61, 62, 63, 64, 66, 67, 69, 70, 71, 77, 78, 79, 85, 86, 88, 89, 90, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 118, 124, 125, 126, 132, 133, 134, 140, 141, 142, 143, 146, 148, 149, 150, 156, 169, 170, 171, 172, 174, 180, 181, 190, 195, 196, 197, 207, 214, 215, 216, 226, 233], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 81, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 81, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 35, 38, 45, 47, 49, 50, 51, 52, 57, 60, 61, 62, 63, 64, 66, 67, 69, 70, 71, 77, 78, 79, 85, 86, 88, 89, 90, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 118, 124, 125, 126, 132, 133, 134, 140, 141, 142, 143, 146, 148, 149, 150, 156, 169, 170, 171, 172, 174, 180, 181, 190, 195, 196, 197, 207, 214, 215, 216, 226, 233], "excluded_lines": []}}}, "src\\user\\handlers\\get_profile.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 37, 44, 46, 48, 49, 50, 51, 56, 59, 60, 61, 62, 64, 65, 67, 68, 69, 74, 77, 79, 80, 81, 87, 88, 89, 95, 96, 97, 98, 100, 109, 110, 119, 124, 125, 126, 136, 143, 144, 145, 155, 161], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 45, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 37, 44, 46, 48, 49, 50, 51, 56, 59, 60, 61, 62, 64, 65, 67, 68, 69, 74, 77, 79, 80, 81, 87, 88, 89, 95, 96, 97, 98, 100, 109, 110, 119, 124, 125, 126, 136, 143, 144, 145, 155, 161], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 37, 44, 46, 48, 49, 50, 51, 56, 59, 60, 61, 62, 64, 65, 67, 68, 69, 74, 77, 79, 80, 81, 87, 88, 89, 95, 96, 97, 98, 100, 109, 110, 119, 124, 125, 126, 136, 143, 144, 145, 155, 161], "excluded_lines": []}}}, "src\\user\\handlers\\update_profile.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 37, 38, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 70, 71, 73, 74, 75, 81, 82, 83, 84, 85, 90, 93, 95, 96, 97, 103, 104, 105, 111, 112, 114, 115, 118, 120, 121, 122, 128, 129, 130, 131, 133, 137, 138, 147, 152, 153, 154, 164, 171, 172, 173, 183, 189], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 58, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [37, 38, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 70, 71, 73, 74, 75, 81, 82, 83, 84, 85, 90, 93, 95, 96, 97, 103, 104, 105, 111, 112, 114, 115, 118, 120, 121, 122, 128, 129, 130, 131, 133, 137, 138, 147, 152, 153, 154, 164, 171, 172, 173, 183, 189], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 37, 38, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 70, 71, 73, 74, 75, 81, 82, 83, 84, 85, 90, 93, 95, 96, 97, 103, 104, 105, 111, 112, 114, 115, 118, 120, 121, 122, 128, 129, 130, 131, 133, 137, 138, 147, 152, 153, 154, 164, 171, 172, 173, 183, 189], "excluded_lines": []}}}, "src\\user\\handlers\\update_role.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 85, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 36, 37, 39, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 82, 83, 84, 90, 91, 93, 94, 95, 101, 102, 103, 104, 105, 110, 111, 112, 117, 120, 122, 123, 124, 130, 131, 132, 138, 139, 140, 146, 147, 148, 154, 155, 158, 160, 161, 162, 168, 182, 183, 184, 185, 187, 192, 193, 202, 207, 208, 209, 219, 226, 227, 228, 238, 245], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 76, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 76, "excluded_lines": 0}, "missing_lines": [36, 37, 39, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 82, 83, 84, 90, 91, 93, 94, 95, 101, 102, 103, 104, 105, 110, 111, 112, 117, 120, 122, 123, 124, 130, 131, 132, 138, 139, 140, 146, 147, 148, 154, 155, 158, 160, 161, 162, 168, 182, 183, 184, 185, 187, 192, 193, 202, 207, 208, 209, 219, 226, 227, 228, 238, 245], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 85, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 36, 37, 39, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 82, 83, 84, 90, 91, 93, 94, 95, 101, 102, 103, 104, 105, 110, 111, 112, 117, 120, 122, 123, 124, 130, 131, 132, 138, 139, 140, 146, 147, 148, 154, 155, 158, 160, 161, 162, 168, 182, 183, 184, 185, 187, 192, 193, 202, 207, 208, 209, 219, 226, 227, 228, 238, 245], "excluded_lines": []}}}}, "totals": {"covered_lines": 1002, "num_statements": 4022, "percent_covered": 24.912978617603184, "percent_covered_display": "25", "missing_lines": 3020, "excluded_lines": 0}}