{"meta": {"format": 3, "version": "7.9.2", "timestamp": "2025-07-17T16:46:38.577079", "branch_coverage": false, "show_contexts": false}, "files": {"src\\auth\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\handlers\\authorizer.py": {"executed_lines": [4, 9, 10, 12, 13, 18, 21, 123, 151, 186, 195, 204], "summary": {"covered_lines": 11, "num_statements": 62, "percent_covered": 17.741935483870968, "percent_covered_display": "18", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [28, 30, 32, 33, 34, 37, 40, 41, 42, 43, 44, 46, 47, 53, 56, 66, 79, 85, 87, 88, 94, 103, 109, 110, 116, 127, 128, 130, 132, 133, 134, 137, 138, 139, 140, 141, 144, 145, 146, 148, 159, 174, 176, 177, 178, 179, 181, 183, 192, 200, 209], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [28, 30, 32, 33, 34, 37, 40, 41, 42, 43, 44, 46, 47, 53, 56, 66, 79, 85, 87, 88, 94, 103, 109, 110, 116], "excluded_lines": []}, "_extract_token_from_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [127, 128, 130, 132, 133, 134, 137, 138, 139, 140, 141, 144, 145, 146, 148], "excluded_lines": []}, "_generate_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [159, 174, 176, 177, 178, 179, 181, 183], "excluded_lines": []}, "_generate_allow_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [192], "excluded_lines": []}, "_generate_deny_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [200], "excluded_lines": []}, "create_test_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [209], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 18, 21, 123, 151, 186, 195, 204], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 18, 21, 123, 151, 186, 195, 204], "summary": {"covered_lines": 11, "num_statements": 62, "percent_covered": 17.741935483870968, "percent_covered_display": "18", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [28, 30, 32, 33, 34, 37, 40, 41, 42, 43, 44, 46, 47, 53, 56, 66, 79, 85, 87, 88, 94, 103, 109, 110, 116, 127, 128, 130, 132, 133, 134, 137, 138, 139, 140, 141, 144, 145, 146, 148, 159, 174, 176, 177, 178, 179, 181, 183, 192, 200, 209], "excluded_lines": []}}}, "src\\auth\\handlers\\forgot_password.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 21, 22, 23, 24, 27], "summary": {"covered_lines": 12, "num_statements": 73, "percent_covered": 16.438356164383563, "percent_covered_display": "16", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 68, 70, 72, 81, 82, 91, 96, 97, 99, 110, 111, 120, 125, 126, 137, 138, 147, 152, 153, 156, 157, 160, 172, 186, 187, 192, 195, 196, 197, 203, 209, 211, 219, 228, 229, 240, 244, 245, 246, 256, 262, 263, 264, 274, 281, 282, 283, 293, 299], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 61, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 68, 70, 72, 81, 82, 91, 96, 97, 99, 110, 111, 120, 125, 126, 137, 138, 147, 152, 153, 156, 157, 160, 172, 186, 187, 192, 195, 196, 197, 203, 209, 211, 219, 228, 229, 240, 244, 245, 246, 256, 262, 263, 264, 274, 281, 282, 283, 293, 299], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 21, 22, 23, 24, 27], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 21, 22, 23, 24, 27], "summary": {"covered_lines": 12, "num_statements": 73, "percent_covered": 16.438356164383563, "percent_covered_display": "16", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 68, 70, 72, 81, 82, 91, 96, 97, 99, 110, 111, 120, 125, 126, 137, 138, 147, 152, 153, 156, 157, 160, 172, 186, 187, 192, 195, 196, 197, 203, 209, 211, 219, 228, 229, 240, 244, 245, 246, 256, 262, 263, 264, 274, 281, 282, 283, 293, 299], "excluded_lines": []}}}, "src\\auth\\handlers\\login.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 66, "percent_covered": 15.151515151515152, "percent_covered_display": "15", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 72, 74, 76, 83, 86, 87, 88, 92, 95, 96, 104, 107, 108, 116, 119, 122, 129, 135, 159, 168, 169, 180, 185, 187, 197, 199, 200, 201, 211, 217, 218, 219, 229, 234, 235, 236, 246, 253, 254, 255, 265, 271], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 56, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 72, 74, 76, 83, 86, 87, 88, 92, 95, 96, 104, 107, 108, 116, 119, 122, 129, 135, 159, 168, 169, 180, 185, 187, 197, 199, 200, 201, 211, 217, 218, 219, 229, 234, 235, 236, 246, 253, 254, 255, 265, 271], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 66, "percent_covered": 15.151515151515152, "percent_covered_display": "15", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 72, 74, 76, 83, 86, 87, 88, 92, 95, 96, 104, 107, 108, 116, 119, 122, 129, 135, 159, 168, 169, 180, 185, 187, 197, 199, 200, 201, 211, 217, 218, 219, 229, 234, 235, 236, 246, 253, 254, 255, 265, 271], "excluded_lines": []}}}, "src\\auth\\handlers\\refresh.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 70, "percent_covered": 14.285714285714286, "percent_covered_display": "14", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 67, 68, 69, 70, 77, 80, 81, 83, 84, 90, 93, 94, 95, 103, 105, 106, 107, 115, 118, 119, 127, 130, 131, 139, 142, 150, 156, 171, 180, 181, 192, 197, 198, 199, 209, 215, 216, 217, 227, 232, 233, 234, 244, 251, 252, 253, 263, 268], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 60, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 67, 68, 69, 70, 77, 80, 81, 83, 84, 90, 93, 94, 95, 103, 105, 106, 107, 115, 118, 119, 127, 130, 131, 139, 142, 150, 156, 171, 180, 181, 192, 197, 198, 199, 209, 215, 216, 217, 227, 232, 233, 234, 244, 251, 252, 253, 263, 268], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 22, 23, 24, 27], "summary": {"covered_lines": 10, "num_statements": 70, "percent_covered": 14.285714285714286, "percent_covered_display": "14", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 43, 46, 53, 55, 57, 58, 60, 62, 67, 68, 69, 70, 77, 80, 81, 83, 84, 90, 93, 94, 95, 103, 105, 106, 107, 115, 118, 119, 127, 130, 131, 139, 142, 150, 156, 171, 180, 181, 192, 197, 198, 199, 209, 215, 216, 217, 227, 232, 233, 234, 244, 251, 252, 253, 263, 268], "excluded_lines": []}}}, "src\\auth\\handlers\\register.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 63, "percent_covered": 17.46031746031746, "percent_covered_display": "17", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 48, 55, 57, 59, 60, 62, 63, 64, 65, 66, 69, 70, 71, 83, 89, 96, 106, 109, 110, 112, 113, 119, 124, 126, 134, 158, 159, 170, 175, 176, 177, 187, 193, 194, 195, 205, 211, 212, 213, 223, 228, 229, 230, 240, 247, 248, 249, 259, 264], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 48, 55, 57, 59, 60, 62, 63, 64, 65, 66, 69, 70, 71, 83, 89, 96, 106, 109, 110, 112, 113, 119, 124, 126, 134, 158, 159, 170, 175, 176, 177, 187, 193, 194, 195, 205, 211, 212, 213, 223, 228, 229, 230, 240, 247, 248, 249, 259, 264], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 15, 21, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 63, "percent_covered": 17.46031746031746, "percent_covered_display": "17", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 48, 55, 57, 59, 60, 62, 63, 64, 65, 66, 69, 70, 71, 83, 89, 96, 106, 109, 110, 112, 113, 119, 124, 126, 134, 158, 159, 170, 175, 176, 177, 187, 193, 194, 195, 205, 211, 212, 213, 223, 228, 229, 230, 240, 247, 248, 249, 259, 264], "excluded_lines": []}}}, "src\\auth\\handlers\\reset_password.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 87, "percent_covered": 12.64367816091954, "percent_covered_display": "13", "missing_lines": 76, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 71, 72, 81, 83, 84, 91, 94, 95, 96, 104, 107, 108, 116, 119, 120, 123, 129, 130, 138, 141, 142, 143, 151, 153, 154, 155, 157, 162, 165, 166, 169, 177, 184, 192, 193, 198, 201, 213, 222, 223, 234, 239, 240, 241, 251, 257, 258, 259, 269, 275, 276, 277, 287, 293, 294, 295, 305, 312, 313, 314, 324, 329], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 76, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 76, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 71, 72, 81, 83, 84, 91, 94, 95, 96, 104, 107, 108, 116, 119, 120, 123, 129, 130, 138, 141, 142, 143, 151, 153, 154, 155, 157, 162, 165, 166, 169, 177, 184, 192, 193, 198, 201, 213, 222, 223, 234, 239, 240, 241, 251, 257, 258, 259, 269, 275, 276, 277, 287, 293, 294, 295, 305, 312, 313, 314, 324, 329], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 22, 23, 24, 27], "summary": {"covered_lines": 11, "num_statements": 87, "percent_covered": 12.64367816091954, "percent_covered_display": "13", "missing_lines": 76, "excluded_lines": 0}, "missing_lines": [39, 40, 42, 43, 44, 47, 54, 56, 58, 59, 61, 62, 64, 70, 71, 72, 81, 83, 84, 91, 94, 95, 96, 104, 107, 108, 116, 119, 120, 123, 129, 130, 138, 141, 142, 143, 151, 153, 154, 155, 157, 162, 165, 166, 169, 177, 184, 192, 193, 198, 201, 213, 222, 223, 234, 239, 240, 241, 251, 257, 258, 259, 269, 275, 276, 277, 287, 293, 294, 295, 305, 312, 313, 314, 324, 329], "excluded_lines": []}}}, "src\\auth\\handlers\\verify_email.py": {"executed_lines": [4, 9, 10, 12, 13, 14, 20, 21, 22, 23, 26], "summary": {"covered_lines": 10, "num_statements": 61, "percent_covered": 16.39344262295082, "percent_covered_display": "16", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [37, 38, 40, 41, 44, 51, 53, 55, 56, 58, 60, 65, 67, 68, 74, 77, 78, 79, 83, 86, 88, 89, 96, 99, 100, 101, 107, 112, 114, 122, 144, 152, 153, 164, 169, 170, 171, 181, 187, 188, 189, 199, 205, 206, 207, 217, 224, 225, 226, 236, 241], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 51, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [37, 38, 40, 41, 44, 51, 53, 55, 56, 58, 60, 65, 67, 68, 74, 77, 78, 79, 83, 86, 88, 89, 96, 99, 100, 101, 107, 112, 114, 122, 144, 152, 153, 164, 169, 170, 171, 181, 187, 188, 189, 199, 205, 206, 207, 217, 224, 225, 226, 236, 241], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 12, 13, 14, 20, 21, 22, 23, 26], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10, 12, 13, 14, 20, 21, 22, 23, 26], "summary": {"covered_lines": 10, "num_statements": 61, "percent_covered": 16.39344262295082, "percent_covered_display": "16", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [37, 38, 40, 41, 44, 51, 53, 55, 56, 58, 60, 65, 67, 68, 74, 77, 78, 79, 83, 86, 88, 89, 96, 99, 100, 101, 107, 112, 114, 122, 144, 152, 153, 164, 169, 170, 171, 181, 187, 188, 189, 199, 205, 206, 207, 217, 224, 225, 226, 236, 241], "excluded_lines": []}}}, "src\\auth\\models\\tenant.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 42, 63, 64, 114, 115, 135, 136, 143, 187, 206, 225, 231, 235, 243, 244, 254, 255, 271], "summary": {"covered_lines": 36, "num_statements": 108, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55, 56, 57, 60, 61, 71, 72, 74, 91, 93, 99, 105, 107, 108, 112, 117, 118, 124, 125, 126, 128, 129, 133, 141, 145, 146, 148, 149, 150, 151, 152, 154, 155, 157, 158, 166, 172, 173, 174, 176, 178, 179, 183, 185, 189, 190, 197, 199, 200, 204, 208, 209, 216, 218, 219, 223, 227, 228, 229, 233, 237, 238, 240, 241, 246, 252, 257, 269, 273], "excluded_lines": [], "functions": {"Tenant.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55, 56, 57, 60, 61], "excluded_lines": []}, "Tenant.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [71, 72, 74, 91, 93, 99, 105, 107, 108, 112], "excluded_lines": []}, "Tenant.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [117, 118, 124, 125, 126, 128, 129, 133], "excluded_lines": []}, "Tenant.get_by_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [141], "excluded_lines": []}, "Tenant.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [145, 146, 148, 149, 150, 151, 152, 154, 155, 157, 158, 166, 172, 173, 174, 176, 178, 179, 183, 185], "excluded_lines": []}, "Tenant.increment_user_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [189, 190, 197, 199, 200, 204], "excluded_lines": []}, "Tenant.decrement_user_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [208, 209, 216, 218, 219, 223], "excluded_lines": []}, "Tenant.can_add_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [227, 228, 229], "excluded_lines": []}, "Tenant.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [233], "excluded_lines": []}, "Tenant.is_trial_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [237, 238, 240, 241], "excluded_lines": []}, "Tenant._get_max_users_for_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [246, 252], "excluded_lines": []}, "Tenant._get_features_for_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [257, 269], "excluded_lines": []}, "Tenant.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [273], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 42, 63, 64, 114, 115, 135, 136, 143, 187, 206, 225, 231, 235, 243, 244, 254, 255, 271], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TenantStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantPlan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 72, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55, 56, 57, 60, 61, 71, 72, 74, 91, 93, 99, 105, 107, 108, 112, 117, 118, 124, 125, 126, 128, 129, 133, 141, 145, 146, 148, 149, 150, 151, 152, 154, 155, 157, 158, 166, 172, 173, 174, 176, 178, 179, 183, 185, 189, 190, 197, 199, 200, 204, 208, 209, 216, 218, 219, 223, 227, 228, 229, 233, 237, 238, 240, 241, 246, 252, 257, 269, 273], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 42, 63, 64, 114, 115, 135, 136, 143, 187, 206, 225, 231, 235, 243, 244, 254, 255, 271], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\models\\user.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 23, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 68, 69, 142, 143, 164, 165, 190, 191, 217, 218, 244, 245, 270, 308, 341, 385, 397, 401, 405, 429, 448, 468], "summary": {"covered_lines": 40, "num_statements": 186, "percent_covered": 21.50537634408602, "percent_covered_display": "22", "missing_lines": 146, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 58, 59, 60, 61, 62, 65, 66, 79, 80, 83, 86, 88, 112, 114, 115, 116, 123, 125, 132, 134, 135, 140, 145, 146, 152, 153, 154, 156, 157, 162, 167, 169, 170, 177, 178, 179, 180, 182, 183, 188, 193, 195, 196, 203, 204, 206, 207, 208, 210, 211, 215, 220, 222, 223, 230, 231, 233, 234, 235, 237, 238, 242, 247, 249, 250, 257, 258, 260, 261, 263, 264, 268, 272, 274, 275, 278, 279, 282, 283, 284, 285, 288, 289, 291, 297, 299, 300, 306, 310, 311, 312, 314, 325, 326, 328, 333, 334, 339, 343, 344, 345, 347, 348, 350, 351, 354, 366, 367, 369, 375, 377, 378, 383, 387, 388, 391, 392, 393, 395, 399, 403, 407, 408, 409, 420, 421, 423, 424, 431, 432, 440, 442, 443, 450, 451, 452, 460, 462, 463, 470, 487, 488, 493], "excluded_lines": [], "functions": {"User.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 58, 59, 60, 61, 62, 65, 66], "excluded_lines": []}, "User.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [79, 80, 83, 86, 88, 112, 114, 115, 116, 123, 125, 132, 134, 135, 140], "excluded_lines": []}, "User.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [145, 146, 152, 153, 154, 156, 157, 162], "excluded_lines": []}, "User.get_by_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [167, 169, 170, 177, 178, 179, 180, 182, 183, 188], "excluded_lines": []}, "User.get_by_verification_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [193, 195, 196, 203, 204, 206, 207, 208, 210, 211, 215], "excluded_lines": []}, "User.get_by_reset_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [220, 222, 223, 230, 231, 233, 234, 235, 237, 238, 242], "excluded_lines": []}, "User.find_by_email_global": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [247, 249, 250, 257, 258, 260, 261, 263, 264, 268], "excluded_lines": []}, "User.authenticate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [272, 274, 275, 278, 279, 282, 283, 284, 285, 288, 289, 291, 297, 299, 300, 306], "excluded_lines": []}, "User.update_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [310, 311, 312, 314, 325, 326, 328, 333, 334, 339], "excluded_lines": []}, "User.verify_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [343, 344, 345, 347, 348, 350, 351, 354, 366, 367, 369, 375, 377, 378, 383], "excluded_lines": []}, "User.is_locked": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [387, 388, 391, 392, 393, 395], "excluded_lines": []}, "User.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [399], "excluded_lines": []}, "User.is_master": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [403], "excluded_lines": []}, "User._increment_login_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [407, 408, 409, 420, 421, 423, 424], "excluded_lines": []}, "User._reset_login_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [431, 432, 440, 442, 443], "excluded_lines": []}, "User._update_last_login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [450, 451, 452, 460, 462, 463], "excluded_lines": []}, "User.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [470, 487, 488, 493], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 23, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 68, 69, 142, 143, 164, 165, 190, 191, 217, 218, 244, 245, 270, 308, 341, 385, 397, 401, 405, 429, 448, 468], "summary": {"covered_lines": 40, "num_statements": 40, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"UserRole": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 146, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 146, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 58, 59, 60, 61, 62, 65, 66, 79, 80, 83, 86, 88, 112, 114, 115, 116, 123, 125, 132, 134, 135, 140, 145, 146, 152, 153, 154, 156, 157, 162, 167, 169, 170, 177, 178, 179, 180, 182, 183, 188, 193, 195, 196, 203, 204, 206, 207, 208, 210, 211, 215, 220, 222, 223, 230, 231, 233, 234, 235, 237, 238, 242, 247, 249, 250, 257, 258, 260, 261, 263, 264, 268, 272, 274, 275, 278, 279, 282, 283, 284, 285, 288, 289, 291, 297, 299, 300, 306, 310, 311, 312, 314, 325, 326, 328, 333, 334, 339, 343, 344, 345, 347, 348, 350, 351, 354, 366, 367, 369, 375, 377, 378, 383, 387, 388, 391, 392, 393, 395, 399, 403, 407, 408, 409, 420, 421, 423, 424, 431, 432, 440, 442, 443, 450, 451, 452, 460, 462, 463, 470, 487, 488, 493], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 23, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 68, 69, 142, 143, 164, 165, 190, 191, 217, 218, 244, 245, 270, 308, 341, 385, 397, 401, 405, 429, 448, 468], "summary": {"covered_lines": 40, "num_statements": 40, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\email_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 76, "percent_covered": 23.68421052631579, "percent_covered_display": "24", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 37, 39, 41, 47, 63, 79, 81, 83, 88, 104, 120, 122, 128, 143, 159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254, 266, 312, 359, 409, 410, 412, 418, 432, 439, 440, 445, 470, 471, 474, 476, 484, 503, 510, 511, 517, 528], "excluded_lines": [], "functions": {"EmailService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25], "excluded_lines": []}, "EmailService.send_verification_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 47, 63], "excluded_lines": []}, "EmailService.send_password_reset_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [79, 81, 83, 88, 104], "excluded_lines": []}, "EmailService.send_welcome_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [409, 410, 412, 418, 432, 439, 440, 445], "excluded_lines": []}, "EmailService._send_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254], "excluded_lines": []}, "EmailService._get_verification_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [266], "excluded_lines": []}, "EmailService._get_password_reset_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [312], "excluded_lines": []}, "EmailService._get_welcome_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [359], "excluded_lines": []}, "EmailService.send_user_invitation_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [470, 471, 474, 476, 484, 503, 510, 511, 517], "excluded_lines": []}, "EmailService._get_invitation_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [528], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"EmailService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 58, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 37, 39, 41, 47, 63, 79, 81, 83, 88, 104, 120, 122, 128, 143, 159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254, 266, 312, 359, 409, 410, 412, 418, 432, 439, 440, 445, 470, 471, 474, 476, 484, 503, 510, 511, 517, 528], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\plan.py": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 37, 38, 40, 81, 128, 143, 144, 164, 165, 193, 194, 227, 228, 331, 335, 339, 345, 349, 353], "summary": {"covered_lines": 35, "num_statements": 121, "percent_covered": 28.925619834710744, "percent_covered_display": "29", "missing_lines": 86, "excluded_lines": 0}, "missing_lines": [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 78, 79, 83, 84, 86, 112, 114, 120, 121, 126, 130, 131, 132, 133, 134, 135, 136, 137, 139, 141, 146, 147, 153, 154, 155, 157, 158, 162, 167, 168, 170, 177, 178, 180, 181, 182, 184, 186, 187, 191, 196, 197, 199, 206, 207, 208, 211, 218, 219, 221, 222, 225, 230, 323, 324, 325, 326, 327, 329, 333, 337, 341, 342, 343, 347, 351, 355, 373, 374, 380], "excluded_lines": [], "functions": {"Plan.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 78, 79], "excluded_lines": []}, "Plan.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [83, 84, 86, 112, 114, 120, 121, 126], "excluded_lines": []}, "Plan.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [130, 131, 132, 133, 134, 135, 136, 137, 139, 141], "excluded_lines": []}, "Plan.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [146, 147, 153, 154, 155, 157, 158, 162], "excluded_lines": []}, "Plan.get_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [167, 168, 170, 177, 178, 180, 181, 182, 184, 186, 187, 191], "excluded_lines": []}, "Plan.list_active_plans": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [196, 197, 199, 206, 207, 208, 211, 218, 219, 221, 222, 225], "excluded_lines": []}, "Plan.create_default_plans": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [230, 323, 324, 325, 326, 327, 329], "excluded_lines": []}, "Plan.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [333], "excluded_lines": []}, "Plan.is_free": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [337], "excluded_lines": []}, "Plan.get_price": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [341, 342, 343], "excluded_lines": []}, "Plan.has_feature": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [347], "excluded_lines": []}, "Plan.get_limit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [351], "excluded_lines": []}, "Plan.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [355, 373, 374, 380], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 37, 38, 40, 81, 128, 143, 144, 164, 165, 193, 194, 227, 228, 331, 335, 339, 345, 349, 353], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PlanStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PlanType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 86, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 86, "excluded_lines": 0}, "missing_lines": [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 78, 79, 83, 84, 86, 112, 114, 120, 121, 126, 130, 131, 132, 133, 134, 135, 136, 137, 139, 141, 146, 147, 153, 154, 155, 157, 158, 162, 167, 168, 170, 177, 178, 180, 181, 182, 184, 186, 187, 191, 196, 197, 199, 206, 207, 208, 211, 218, 219, 221, 222, 225, 230, 323, 324, 325, 326, 327, 329, 333, 337, 341, 342, 343, 347, 351, 355, 373, 374, 380], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 37, 38, 40, 81, 128, 143, 144, 164, 165, 193, 194, 227, 228, 331, 335, 339, 345, 349, 353], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\subscription.py": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 39, 40, 41, 42, 43, 46, 47, 49, 90, 137, 152, 153, 174, 175, 201, 202, 227, 231, 235, 241, 249, 272, 295, 316], "summary": {"covered_lines": 40, "num_statements": 136, "percent_covered": 29.41176470588235, "percent_covered_display": "29", "missing_lines": 96, "excluded_lines": 0}, "missing_lines": [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 87, 88, 92, 93, 95, 121, 123, 129, 130, 135, 139, 140, 141, 142, 143, 144, 145, 146, 148, 150, 155, 156, 162, 163, 164, 166, 167, 172, 177, 178, 180, 186, 187, 189, 190, 192, 194, 195, 199, 204, 205, 207, 214, 215, 216, 218, 220, 221, 225, 229, 233, 237, 238, 239, 243, 244, 246, 247, 251, 252, 258, 264, 265, 270, 274, 275, 281, 287, 288, 293, 297, 298, 303, 308, 309, 314, 318, 338, 339, 345], "excluded_lines": [], "functions": {"Subscription.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 87, 88], "excluded_lines": []}, "Subscription.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [92, 93, 95, 121, 123, 129, 130, 135], "excluded_lines": []}, "Subscription.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [139, 140, 141, 142, 143, 144, 145, 146, 148, 150], "excluded_lines": []}, "Subscription.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [155, 156, 162, 163, 164, 166, 167, 172], "excluded_lines": []}, "Subscription.get_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [177, 178, 180, 186, 187, 189, 190, 192, 194, 195, 199], "excluded_lines": []}, "Subscription.get_by_stripe_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [204, 205, 207, 214, 215, 216, 218, 220, 221, 225], "excluded_lines": []}, "Subscription.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [229], "excluded_lines": []}, "Subscription.is_trial": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [233], "excluded_lines": []}, "Subscription.is_trial_expired": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [237, 238, 239], "excluded_lines": []}, "Subscription.days_until_trial_end": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [243, 244, 246, 247], "excluded_lines": []}, "Subscription.cancel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [251, 252, 258, 264, 265, 270], "excluded_lines": []}, "Subscription.suspend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [274, 275, 281, 287, 288, 293], "excluded_lines": []}, "Subscription.reactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [297, 298, 303, 308, 309, 314], "excluded_lines": []}, "Subscription.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [318, 338, 339, 345], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 39, 40, 41, 42, 43, 46, 47, 49, 90, 137, 152, 153, 174, 175, 201, 202, 227, 231, 235, 241, 249, 272, 295, 316], "summary": {"covered_lines": 40, "num_statements": 40, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SubscriptionStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BillingInterval": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PaymentMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 96, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 96, "excluded_lines": 0}, "missing_lines": [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 87, 88, 92, 93, 95, 121, 123, 129, 130, 135, 139, 140, 141, 142, 143, 144, 145, 146, 148, 150, 155, 156, 162, 163, 164, 166, 167, 172, 177, 178, 180, 186, 187, 189, 190, 192, 194, 195, 199, 204, 205, 207, 214, 215, 216, 218, 220, 221, 225, 229, 233, 237, 238, 239, 243, 244, 246, 247, 251, 252, 258, 264, 265, 270, 274, 275, 281, 287, 288, 293, 297, 298, 303, 308, 309, 314, 318, 338, 339, 345], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 39, 40, 41, 42, 43, 46, 47, 49, 90, 137, 152, 153, 174, 175, 201, 202, 227, 231, 235, 241, 249, 272, 295, 316], "summary": {"covered_lines": 40, "num_statements": 40, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\auth.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 25, 28, 29, 31, 32, 33, 35, 39, 43, 77, 82, 83, 85, 86, 87, 88, 90, 97, 98, 103, 104, 109, 149, 185, 212, 225, 226, 228, 242, 246, 250, 254, 258, 262, 273, 290, 300, 307, 308], "summary": {"covered_lines": 45, "num_statements": 120, "percent_covered": 37.5, "percent_covered_display": "38", "missing_lines": 75, "excluded_lines": 0}, "missing_lines": [37, 41, 45, 48, 49, 52, 53, 56, 57, 60, 61, 64, 65, 68, 72, 73, 75, 79, 92, 93, 94, 95, 100, 101, 106, 107, 118, 119, 121, 125, 138, 140, 147, 156, 157, 159, 163, 174, 176, 183, 187, 188, 197, 198, 201, 202, 203, 205, 207, 208, 209, 210, 214, 216, 236, 237, 238, 239, 240, 244, 248, 252, 256, 260, 264, 276, 278, 279, 287, 292, 294, 295, 297, 302, 303], "excluded_lines": [], "functions": {"PasswordManager.__init__": {"executed_lines": [32, 33], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordManager.hash_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "PasswordManager.verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [41], "excluded_lines": []}, "PasswordManager.validate_password_strength": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [45, 48, 49, 52, 53, 56, 57, 60, 61, 64, 65, 68, 72, 73, 75], "excluded_lines": []}, "PasswordManager.generate_secure_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [79], "excluded_lines": []}, "JWTManager.__init__": {"executed_lines": [86, 87, 88], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "JWTManager._get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [92, 93, 94, 95], "excluded_lines": []}, "JWTManager.secret_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [100, 101], "excluded_lines": []}, "JWTManager.public_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [106, 107], "excluded_lines": []}, "JWTManager.create_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [118, 119, 121, 125, 138, 140, 147], "excluded_lines": []}, "JWTManager.create_refresh_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [156, 157, 159, 163, 174, 176, 183], "excluded_lines": []}, "JWTManager.verify_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [187, 188, 197, 198, 201, 202, 203, 205, 207, 208, 209, 210], "excluded_lines": []}, "JWTManager.extract_user_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [214, 216], "excluded_lines": []}, "AuthContext.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [236, 237, 238, 239, 240], "excluded_lines": []}, "AuthContext.is_master": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [244], "excluded_lines": []}, "AuthContext.is_member": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [248], "excluded_lines": []}, "AuthContext.can_manage_users": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [252], "excluded_lines": []}, "AuthContext.can_manage_billing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [256], "excluded_lines": []}, "AuthContext.can_access_analytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [260], "excluded_lines": []}, "AuthContext.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [264], "excluded_lines": []}, "extract_auth_context_from_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [276, 278, 279, 287], "excluded_lines": []}, "require_auth": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [292, 294, 295, 297], "excluded_lines": []}, "require_master_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [302, 303], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 25, 28, 29, 31, 35, 39, 43, 77, 82, 83, 85, 90, 97, 98, 103, 104, 109, 149, 185, 212, 225, 226, 228, 242, 246, 250, 254, 258, 262, 273, 290, 300, 307, 308], "summary": {"covered_lines": 40, "num_statements": 40, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PasswordManager": {"executed_lines": [32, 33], "summary": {"covered_lines": 2, "num_statements": 20, "percent_covered": 10.0, "percent_covered_display": "10", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [37, 41, 45, 48, 49, 52, 53, 56, 57, 60, 61, 64, 65, 68, 72, 73, 75, 79], "excluded_lines": []}, "JWTManager": {"executed_lines": [86, 87, 88], "summary": {"covered_lines": 3, "num_statements": 39, "percent_covered": 7.6923076923076925, "percent_covered_display": "8", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [92, 93, 94, 95, 100, 101, 106, 107, 118, 119, 121, 125, 138, 140, 147, 156, 157, 159, 163, 174, 176, 183, 187, 188, 197, 198, 201, 202, 203, 205, 207, 208, 209, 210, 214, 216], "excluded_lines": []}, "AuthContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [236, 237, 238, 239, 240, 244, 248, 252, 256, 260, 264], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 25, 28, 29, 31, 35, 39, 43, 77, 82, 83, 85, 90, 97, 98, 103, 104, 109, 149, 185, 212, 225, 226, 228, 242, 246, 250, 254, 258, 262, 273, 290, 300, 307, 308], "summary": {"covered_lines": 40, "num_statements": 50, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [276, 278, 279, 287, 292, 294, 295, 297, 302, 303], "excluded_lines": []}}}, "src\\shared\\config.py": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 64, 65, 66, 68, 69, 70, 71, 74, 75, 77, 78, 79, 83, 86, 88, 91, 93, 96, 98, 101, 106, 108, 110, 113, 116, 133, 135, 142], "summary": {"covered_lines": 59, "num_statements": 68, "percent_covered": 86.76470588235294, "percent_covered_display": "87", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [103, 109, 111, 118, 119, 124, 125, 127, 144], "excluded_lines": [], "functions": {"get_settings": {"executed_lines": [88], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_development": {"executed_lines": [93], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_production": {"executed_lines": [98], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_testing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [103], "excluded_lines": []}, "get_log_level": {"executed_lines": [108, 110, 113], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [109, 111], "excluded_lines": []}, "get_cors_origins": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [118, 119, 124, 125, 127], "excluded_lines": []}, "get_database_config": {"executed_lines": [135], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_storage_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [144], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 64, 65, 66, 68, 69, 70, 71, 74, 75, 77, 78, 79, 83, 86, 91, 96, 101, 106, 116, 133, 142], "summary": {"covered_lines": 52, "num_statements": 52, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Settings.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 64, 65, 66, 68, 69, 70, 71, 74, 75, 77, 78, 79, 83, 86, 88, 91, 93, 96, 98, 101, 106, 108, 110, 113, 116, 133, 135, 142], "summary": {"covered_lines": 59, "num_statements": 68, "percent_covered": 86.76470588235294, "percent_covered_display": "87", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [103, 109, 111, 118, 119, 124, 125, 127, 144], "excluded_lines": []}}}, "src\\shared\\database.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 24, 25, 28, 30, 42, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 23, "num_statements": 136, "percent_covered": 16.91176470588235, "percent_covered_display": "17", "missing_lines": 113, "excluded_lines": 0}, "missing_lines": [37, 46, 47, 48, 52, 53, 54, 55, 65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106, 119, 121, 123, 124, 127, 130, 131, 132, 133, 135, 136, 137, 139, 141, 142, 152, 153, 155, 157, 158, 159, 169, 170, 172, 189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245, 259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300, 318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": [], "functions": {"DynamoDBClient.__init__": {"executed_lines": [24, 25, 28, 30, 42], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "DynamoDBClient._add_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [46, 47, 48], "excluded_lines": []}, "DynamoDBClient._remove_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55], "excluded_lines": []}, "DynamoDBClient.get_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106], "excluded_lines": []}, "DynamoDBClient.put_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [119, 121, 123, 124, 127, 130, 131, 132, 133, 135, 136, 137, 139, 141, 142, 152, 153, 155, 157, 158, 159, 169, 170, 172], "excluded_lines": []}, "DynamoDBClient.update_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245], "excluded_lines": []}, "DynamoDBClient.delete_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300], "excluded_lines": []}, "DynamoDBClient.query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DynamoDBClient": {"executed_lines": [24, 25, 28, 30, 42], "summary": {"covered_lines": 5, "num_statements": 118, "percent_covered": 4.237288135593221, "percent_covered_display": "4", "missing_lines": 113, "excluded_lines": 0}, "missing_lines": [37, 46, 47, 48, 52, 53, 54, 55, 65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106, 119, 121, 123, 124, 127, 130, 131, 132, 133, 135, 136, 137, 139, 141, 142, 152, 153, 155, 157, 158, 159, 169, 170, 172, 189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245, 259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300, 318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\exceptions.py": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 64, 65, 67, 83, 84, 86, 102, 103, 105, 118, 119, 121, 137, 138, 140, 156, 157, 159, 172, 173, 175, 188, 189, 191, 207, 208, 210, 226, 227, 229, 243, 244, 246, 250, 251, 253, 257, 258, 260, 264, 265, 267, 271, 272, 274, 279, 280, 282, 286, 287, 289, 293, 294, 296, 300, 301, 303, 314, 315, 317, 330, 331, 333], "summary": {"covered_lines": 51, "num_statements": 136, "percent_covered": 37.5, "percent_covered_display": "38", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26, 38, 39, 50, 61, 74, 75, 76, 77, 78, 80, 93, 94, 95, 96, 97, 99, 111, 112, 113, 115, 128, 129, 130, 131, 132, 134, 147, 148, 149, 150, 151, 153, 165, 166, 167, 169, 181, 182, 183, 185, 198, 199, 200, 201, 202, 204, 217, 218, 219, 220, 221, 223, 235, 236, 237, 239, 247, 254, 261, 268, 275, 283, 290, 297, 304, 305, 306, 307, 308, 318, 319, 320, 321, 322, 323, 334, 335, 336, 337, 338], "excluded_lines": [], "functions": {"PlatformException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26], "excluded_lines": []}, "ValidationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [38, 39], "excluded_lines": []}, "AuthenticationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "AuthorizationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "ResourceNotFoundException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 77, 78, 80], "excluded_lines": []}, "ResourceConflictException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [93, 94, 95, 96, 97, 99], "excluded_lines": []}, "RateLimitException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [111, 112, 113, 115], "excluded_lines": []}, "ExternalServiceException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 134], "excluded_lines": []}, "DatabaseException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [147, 148, 149, 150, 151, 153], "excluded_lines": []}, "TenantException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [165, 166, 167, 169], "excluded_lines": []}, "UserException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 185], "excluded_lines": []}, "PaymentException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [334, 335, 336, 337, 338], "excluded_lines": []}, "AgentException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [217, 218, 219, 220, 221, 223], "excluded_lines": []}, "ConfigurationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 239], "excluded_lines": []}, "InvalidCredentialsException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [247], "excluded_lines": []}, "TokenExpiredException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [254], "excluded_lines": []}, "InvalidTokenException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [261], "excluded_lines": []}, "AccountLockedException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [268], "excluded_lines": []}, "EmailNotVerifiedException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [275], "excluded_lines": []}, "InvalidEmailException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [283], "excluded_lines": []}, "WeakPasswordException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [290], "excluded_lines": []}, "InvalidTenantException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [297], "excluded_lines": []}, "TenantLimitExceededException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [304, 305, 306, 307, 308], "excluded_lines": []}, "SecurityViolationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [318, 319, 320, 321, 322, 323], "excluded_lines": []}, "": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 64, 65, 67, 83, 84, 86, 102, 103, 105, 118, 119, 121, 137, 138, 140, 156, 157, 159, 172, 173, 175, 188, 189, 191, 207, 208, 210, 226, 227, 229, 243, 244, 246, 250, 251, 253, 257, 258, 260, 264, 265, 267, 271, 272, 274, 279, 280, 282, 286, 287, 289, 293, 294, 296, 300, 301, 303, 314, 315, 317, 330, 331, 333], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PlatformException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26], "excluded_lines": []}, "ValidationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [38, 39], "excluded_lines": []}, "AuthenticationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "AuthorizationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [61], "excluded_lines": []}, "ResourceNotFoundException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 77, 78, 80], "excluded_lines": []}, "ResourceConflictException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [93, 94, 95, 96, 97, 99], "excluded_lines": []}, "RateLimitException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [111, 112, 113, 115], "excluded_lines": []}, "ExternalServiceException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 134], "excluded_lines": []}, "DatabaseException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [147, 148, 149, 150, 151, 153], "excluded_lines": []}, "TenantException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [165, 166, 167, 169], "excluded_lines": []}, "UserException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 185], "excluded_lines": []}, "PaymentException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [334, 335, 336, 337, 338], "excluded_lines": []}, "AgentException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [217, 218, 219, 220, 221, 223], "excluded_lines": []}, "ConfigurationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 239], "excluded_lines": []}, "InvalidCredentialsException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [247], "excluded_lines": []}, "TokenExpiredException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [254], "excluded_lines": []}, "InvalidTokenException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [261], "excluded_lines": []}, "AccountLockedException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [268], "excluded_lines": []}, "EmailNotVerifiedException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [275], "excluded_lines": []}, "InvalidEmailException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [283], "excluded_lines": []}, "WeakPasswordException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [290], "excluded_lines": []}, "InvalidTenantException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [297], "excluded_lines": []}, "TenantLimitExceededException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [304, 305, 306, 307, 308], "excluded_lines": []}, "SecurityViolationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [318, 319, 320, 321, 322, 323], "excluded_lines": []}, "": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 64, 65, 67, 83, 84, 86, 102, 103, 105, 118, 119, 121, 137, 138, 140, 156, 157, 159, 172, 173, 175, 188, 189, 191, 207, 208, 210, 226, 227, 229, 243, 244, 246, 250, 251, 253, 257, 258, 260, 264, 265, 267, 271, 272, 274, 279, 280, 282, 286, 287, 289, 293, 294, 296, 300, 301, 303, 314, 315, 317, 330, 331, 333], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\logger.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82, 85, 87, 89, 98, 104, 107, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 34, "num_statements": 59, "percent_covered": 57.6271186440678, "percent_covered_display": "58", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [25, 28, 39, 40, 43, 44, 51, 53, 66, 117, 127, 142, 154, 155, 157, 170, 180, 181, 183, 197, 208, 209, 211, 224, 234], "excluded_lines": [], "functions": {"StructuredFormatter.format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [25, 28, 39, 40, 43, 44, 51, 53], "excluded_lines": []}, "setup_logging": {"executed_lines": [58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [66], "excluded_lines": []}, "get_lambda_logger": {"executed_lines": [87, 89, 98, 104], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "log_api_request": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [117, 127], "excluded_lines": []}, "log_api_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [142, 154, 155, 157], "excluded_lines": []}, "log_database_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [170, 180, 181, 183], "excluded_lines": []}, "log_external_api_call": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [197, 208, 209, 211], "excluded_lines": []}, "log_security_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [224, 234], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 85, 107, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"StructuredFormatter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [25, 28, 39, 40, 43, 44, 51, 53], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82, 85, 87, 89, 98, 104, 107, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 34, "num_statements": 51, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [66, 117, 127, 142, 154, 155, 157, 170, 180, 181, 183, 197, 208, 209, 211, 224, 234], "excluded_lines": []}}}, "src\\shared\\responses.py": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 201, 203, 204, 234], "summary": {"covered_lines": 33, "num_statements": 69, "percent_covered": 47.82608695652174, "percent_covered_display": "48", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37, 57, 64, 65, 67, 68, 70, 71, 73, 91, 98, 99, 101, 106, 123, 131, 139, 147, 156, 169, 176, 177, 179, 180, 181, 189, 197, 212, 213, 214, 216, 227, 236], "excluded_lines": [], "functions": {"APIResponse.success": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37], "excluded_lines": []}, "APIResponse.error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [57, 64, 65, 67, 68, 70, 71, 73], "excluded_lines": []}, "APIResponse.created": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [91, 98, 99, 101], "excluded_lines": []}, "APIResponse.no_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [106], "excluded_lines": []}, "APIResponse.unauthorized": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [123], "excluded_lines": []}, "APIResponse.forbidden": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [131], "excluded_lines": []}, "APIResponse.not_found": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [139], "excluded_lines": []}, "APIResponse.conflict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [147], "excluded_lines": []}, "APIResponse.validation_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [156], "excluded_lines": []}, "APIResponse.rate_limit_exceeded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [169, 176, 177, 179, 180, 181], "excluded_lines": []}, "APIResponse.internal_server_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "APIResponse.service_unavailable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [197], "excluded_lines": []}, "PaginatedResponse.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 216, 227], "excluded_lines": []}, "handle_cors_preflight": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [236], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 201, 203, 204, 234], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"APIResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37, 57, 64, 65, 67, 68, 70, 71, 73, 91, 98, 99, 101, 106, 123, 131, 139, 147, 156, 169, 176, 177, 179, 180, 181, 189, 197], "excluded_lines": []}, "PaginatedResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 216, 227], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 201, 203, 204, 234], "summary": {"covered_lines": 33, "num_statements": 34, "percent_covered": 97.05882352941177, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [236], "excluded_lines": []}}}, "src\\shared\\secrets.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 113, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 113, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 14, 15, 16, 19, 22, 23, 24, 25, 27, 42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 57, 59, 60, 61, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85, 90, 104, 106, 107, 108, 109, 110, 115, 130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154, 159, 173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203, 208, 218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237, 239, 251, 257, 258, 264, 265, 267, 268, 270, 272, 285, 287, 288, 289, 290, 292, 294, 296, 297, 301, 304, 306, 309, 311], "excluded_lines": [], "functions": {"SecretsManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [23, 24, 25], "excluded_lines": []}, "SecretsManager.get_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 57, 59, 60, 61, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85], "excluded_lines": []}, "SecretsManager.get_secret_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [104, 106, 107, 108, 109, 110], "excluded_lines": []}, "SecretsManager.create_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154], "excluded_lines": []}, "SecretsManager.update_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203], "excluded_lines": []}, "SecretsManager.get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237], "excluded_lines": []}, "SecretsManager._create_default_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [251, 257, 258, 264, 265, 267, 268, 270], "excluded_lines": []}, "SecretsManager.get_integration_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [285, 287, 288, 289, 290, 292], "excluded_lines": []}, "SecretsManager.clear_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [296, 297], "excluded_lines": []}, "get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}, "get_integration_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [311], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 14, 15, 16, 19, 22, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 309], "excluded_lines": []}}, "classes": {"SecretsManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 91, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 91, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 57, 59, 60, 61, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85, 104, 106, 107, 108, 109, 110, 130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154, 173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203, 218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237, 251, 257, 258, 264, 265, 267, 268, 270, 285, 287, 288, 289, 290, 292, 296, 297], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 14, 15, 16, 19, 22, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 306, 309, 311], "excluded_lines": []}}}, "src\\shared\\validators.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 22, 23, 24, 27, 37, 46, 64, 82, 103, 122, 136, 137, 139, 140, 141, 142, 143, 145, 146, 149, 150, 153, 154, 157, 158, 164, 165, 167, 168, 170, 171, 175, 176, 178, 180, 181, 185, 186, 188, 189, 192, 193, 195, 198, 199, 201, 204, 205, 207, 208, 209, 210, 212, 213, 216, 217, 221, 222, 224, 225, 226, 229, 230, 232, 233, 234, 237, 238, 240, 241, 244, 245, 247, 248, 249, 252, 253, 255, 256, 257, 258, 260, 261, 266, 267, 273, 274, 276, 277, 280, 319], "summary": {"covered_lines": 86, "num_statements": 179, "percent_covered": 48.04469273743017, "percent_covered_display": "48", "missing_lines": 93, "excluded_lines": 0}, "missing_lines": [29, 31, 32, 33, 34, 39, 40, 41, 42, 43, 48, 49, 51, 52, 54, 55, 58, 59, 61, 66, 67, 69, 70, 72, 73, 76, 77, 79, 84, 85, 88, 91, 92, 95, 96, 97, 98, 100, 105, 106, 108, 116, 117, 119, 124, 125, 127, 128, 130, 131, 133, 147, 151, 155, 159, 160, 161, 172, 182, 214, 218, 262, 263, 264, 268, 269, 270, 286, 288, 290, 291, 293, 296, 297, 299, 300, 301, 302, 304, 305, 306, 307, 311, 316, 321, 322, 325, 328, 329, 332, 333, 334, 336], "excluded_lines": [], "functions": {"validate_email_address": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [29, 31, 32, 33, 34], "excluded_lines": []}, "validate_uuid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [39, 40, 41, 42, 43], "excluded_lines": []}, "validate_tenant_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [48, 49, 51, 52, 54, 55, 58, 59, 61], "excluded_lines": []}, "validate_user_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [66, 67, 69, 70, 72, 73, 76, 77, 79], "excluded_lines": []}, "validate_phone_number": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [84, 85, 88, 91, 92, 95, 96, 97, 98, 100], "excluded_lines": []}, "validate_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [105, 106, 108, 116, 117, 119], "excluded_lines": []}, "validate_pagination_params": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [124, 125, 127, 128, 130, 131, 133], "excluded_lines": []}, "RegisterRequestValidator.validate_tenant_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [147], "excluded_lines": []}, "RegisterRequestValidator.validate_user_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [151], "excluded_lines": []}, "RegisterRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [155], "excluded_lines": []}, "RegisterRequestValidator.validate_phone_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [159, 160, 161], "excluded_lines": []}, "LoginRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [172], "excluded_lines": []}, "ForgotPasswordRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [182], "excluded_lines": []}, "InviteUserRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [214], "excluded_lines": []}, "InviteUserRequestValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [218], "excluded_lines": []}, "UpdateUserRequestValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [262, 263, 264], "excluded_lines": []}, "UpdateUserRequestValidator.validate_phone_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [268, 269, 270], "excluded_lines": []}, "validate_request_body": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [286, 288, 290, 291, 293, 296, 297, 299, 300, 301, 302, 304, 305, 306, 307, 311, 316], "excluded_lines": []}, "sanitize_string": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [321, 322, 325, 328, 329, 332, 333, 334, 336], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 22, 23, 24, 27, 37, 46, 64, 82, 103, 122, 136, 137, 139, 140, 141, 142, 143, 145, 146, 149, 150, 153, 154, 157, 158, 164, 165, 167, 168, 170, 171, 175, 176, 178, 180, 181, 185, 186, 188, 189, 192, 193, 195, 198, 199, 201, 204, 205, 207, 208, 209, 210, 212, 213, 216, 217, 221, 222, 224, 225, 226, 229, 230, 232, 233, 234, 237, 238, 240, 241, 244, 245, 247, 248, 249, 252, 253, 255, 256, 257, 258, 260, 261, 266, 267, 273, 274, 276, 277, 280, 319], "summary": {"covered_lines": 86, "num_statements": 86, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BaseValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseValidator.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RegisterRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [147, 151, 155, 159, 160, 161], "excluded_lines": []}, "LoginRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [172], "excluded_lines": []}, "ForgotPasswordRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [182], "excluded_lines": []}, "ResetPasswordRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VerifyEmailRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RefreshTokenRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InviteUserRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [214, 218], "excluded_lines": []}, "AcceptInvitationRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CreateSubscriptionRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CancelSubscriptionRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UpdateUserRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [262, 263, 264, 268, 269, 270], "excluded_lines": []}, "PaginationValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 22, 23, 24, 27, 37, 46, 64, 82, 103, 122, 136, 137, 139, 140, 141, 142, 143, 145, 146, 149, 150, 153, 154, 157, 158, 164, 165, 167, 168, 170, 171, 175, 176, 178, 180, 181, 185, 186, 188, 189, 192, 193, 195, 198, 199, 201, 204, 205, 207, 208, 209, 210, 212, 213, 216, 217, 221, 222, 224, 225, 226, 229, 230, 232, 233, 234, 237, 238, 240, 241, 244, 245, 247, 248, 249, 252, 253, 255, 256, 257, 258, 260, 261, 266, 267, 273, 274, 276, 277, 280, 319], "summary": {"covered_lines": 86, "num_statements": 163, "percent_covered": 52.760736196319016, "percent_covered_display": "53", "missing_lines": 77, "excluded_lines": 0}, "missing_lines": [29, 31, 32, 33, 34, 39, 40, 41, 42, 43, 48, 49, 51, 52, 54, 55, 58, 59, 61, 66, 67, 69, 70, 72, 73, 76, 77, 79, 84, 85, 88, 91, 92, 95, 96, 97, 98, 100, 105, 106, 108, 116, 117, 119, 124, 125, 127, 128, 130, 131, 133, 286, 288, 290, 291, 293, 296, 297, 299, 300, 301, 302, 304, 305, 306, 307, 311, 316, 321, 322, 325, 328, 329, 332, 333, 334, 336], "excluded_lines": []}}}, "src\\tenant\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [9, 10], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [9, 10], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [9, 10], "excluded_lines": []}}}}, "totals": {"covered_lines": 581, "num_statements": 1997, "percent_covered": 29.093640460691038, "percent_covered_display": "29", "missing_lines": 1416, "excluded_lines": 0}}