{"meta": {"format": 3, "version": "7.9.2", "timestamp": "2025-07-29T15:26:48.721245", "branch_coverage": false, "show_contexts": false}, "files": {"src\\auth\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\models\\tenant.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 74, 76, 77, 127, 128, 130, 131, 137, 139, 143, 144, 145, 148, 158, 159, 161, 164, 172, 173, 175, 179, 180, 181, 184, 185, 194, 238, 257, 276, 278, 279, 280, 282, 284, 286, 288, 289, 291, 292, 294, 295, 297, 303, 305, 306, 308, 320, 322, 324, 325, 327, 329, 330, 332, 334, 335, 337, 339, 341, 342, 344, 346, 347, 349, 351, 352, 354, 356, 358, 360, 361, 377, 378, 379, 381, 382, 383, 384, 386, 387, 396, 397, 399, 400, 402, 403, 404, 405, 407, 408, 410, 411, 413, 414, 416, 417, 420, 421, 422, 424, 425, 427, 430, 432, 433, 435, 436, 438, 439, 442, 443, 446, 447, 450, 453, 455, 472, 473, 474, 477], "summary": {"covered_lines": 155, "num_statements": 216, "percent_covered": 71.75925925925925, "percent_covered_display": "72", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [84, 85, 87, 104, 106, 112, 118, 120, 121, 125, 146, 149, 151, 152, 156, 182, 187, 188, 192, 196, 197, 199, 200, 201, 202, 203, 205, 206, 208, 209, 217, 223, 224, 225, 227, 229, 230, 234, 236, 240, 241, 248, 250, 251, 255, 259, 260, 267, 269, 270, 274, 340, 380, 389, 390, 394, 428, 444, 448, 451, 475], "excluded_lines": [], "functions": {"Tenant.__init__": {"executed_lines": [54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 74], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [84, 85, 87, 104, 106, 112, 118, 120, 121, 125], "excluded_lines": []}, "Tenant.get_by_id": {"executed_lines": [130, 131, 137, 139, 143, 144, 145, 148], "summary": {"covered_lines": 8, "num_statements": 13, "percent_covered": 61.53846153846154, "percent_covered_display": "62", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [146, 149, 151, 152, 156], "excluded_lines": []}, "Tenant.get_by_name": {"executed_lines": [161, 164, 172, 173, 175, 179, 180, 181, 184, 185], "summary": {"covered_lines": 10, "num_statements": 14, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [182, 187, 188, 192], "excluded_lines": []}, "Tenant.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [196, 197, 199, 200, 201, 202, 203, 205, 206, 208, 209, 217, 223, 224, 225, 227, 229, 230, 234, 236], "excluded_lines": []}, "Tenant.increment_user_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [240, 241, 248, 250, 251, 255], "excluded_lines": []}, "Tenant.decrement_user_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [259, 260, 267, 269, 270, 274], "excluded_lines": []}, "Tenant.can_add_user": {"executed_lines": [278, 279, 280], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.is_active": {"executed_lines": [284], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.is_trial_expired": {"executed_lines": [288, 289, 291, 292], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant._get_max_users_for_plan": {"executed_lines": [297, 303], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant._get_features_for_plan": {"executed_lines": [308, 320], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.activate": {"executed_lines": [324, 325], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.suspend": {"executed_lines": [329, 330], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.deactivate": {"executed_lines": [334, 335], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.update_settings": {"executed_lines": [339, 341, 342], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [340], "excluded_lines": []}, "Tenant.set_subscription": {"executed_lines": [346, 347], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.remove_subscription": {"executed_lines": [351, 352], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.has_subscription": {"executed_lines": [356], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant.save": {"executed_lines": [360, 361, 377, 378, 379, 381, 382, 383, 384, 386, 387], "summary": {"covered_lines": 11, "num_statements": 15, "percent_covered": 73.33333333333333, "percent_covered_display": "73", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [380, 389, 390, 394], "excluded_lines": []}, "Tenant.slug": {"executed_lines": [399, 400, 402, 403, 404, 405], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant._validate_name": {"executed_lines": [410, 411, 413, 414, 416, 417, 420, 421, 422], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant._validate_email": {"executed_lines": [427, 430, 432, 433, 435, 436, 438, 439, 442, 443, 446, 447, 450], "summary": {"covered_lines": 13, "num_statements": 17, "percent_covered": 76.47058823529412, "percent_covered_display": "76", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [428, 444, 448, 451], "excluded_lines": []}, "Tenant.to_dict": {"executed_lines": [455, 472, 473, 474, 477], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [475], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 76, 77, 127, 128, 158, 159, 194, 238, 257, 276, 282, 286, 294, 295, 305, 306, 322, 327, 332, 337, 344, 349, 354, 358, 396, 397, 407, 408, 424, 425, 453], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TenantStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantPlan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Tenant": {"executed_lines": [54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 74, 130, 131, 137, 139, 143, 144, 145, 148, 161, 164, 172, 173, 175, 179, 180, 181, 184, 185, 278, 279, 280, 284, 288, 289, 291, 292, 297, 303, 308, 320, 324, 325, 329, 330, 334, 335, 339, 341, 342, 346, 347, 351, 352, 356, 360, 361, 377, 378, 379, 381, 382, 383, 384, 386, 387, 399, 400, 402, 403, 404, 405, 410, 411, 413, 414, 416, 417, 420, 421, 422, 427, 430, 432, 433, 435, 436, 438, 439, 442, 443, 446, 447, 450, 455, 472, 473, 474, 477], "summary": {"covered_lines": 104, "num_statements": 165, "percent_covered": 63.03030303030303, "percent_covered_display": "63", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [84, 85, 87, 104, 106, 112, 118, 120, 121, 125, 146, 149, 151, 152, 156, 182, 187, 188, 192, 196, 197, 199, 200, 201, 202, 203, 205, 206, 208, 209, 217, 223, 224, 225, 227, 229, 230, 234, 236, 240, 241, 248, 250, 251, 255, 259, 260, 267, 269, 270, 274, 340, 380, 389, 390, 394, 428, 444, 448, 451, 475], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 40, 41, 43, 76, 77, 127, 128, 158, 159, 194, 238, 257, 276, 282, 286, 294, 295, 305, 306, 322, 327, 332, 337, 344, 349, 354, 358, 396, 397, 407, 408, 424, 425, 453], "summary": {"covered_lines": 51, "num_statements": 51, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\models\\user.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 24, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 41, 42, 44, 57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 80, 81, 83, 85, 86, 159, 160, 162, 163, 169, 171, 175, 176, 177, 178, 180, 191, 192, 194, 196, 197, 204, 205, 207, 211, 212, 213, 214, 216, 217, 227, 228, 254, 255, 281, 282, 307, 309, 311, 315, 319, 320, 321, 322, 325, 326, 328, 334, 336, 337, 343, 345, 378, 422, 424, 425, 428, 429, 430, 432, 434, 436, 438, 440, 442, 444, 446, 470, 489, 509, 511, 528, 529, 534, 536, 538, 539, 541, 543, 544, 546, 548, 549, 551, 553, 557, 559, 561, 563, 565, 567, 568, 569, 572, 574, 575, 576, 577, 578, 579, 581, 583, 584, 602, 604, 607, 608, 618, 619, 621, 624, 626, 629, 632, 636, 637, 640, 641, 644, 647, 648, 650, 653, 656, 659, 660, 662, 665, 667, 668, 671, 674, 675, 678, 679], "summary": {"covered_lines": 171, "num_statements": 286, "percent_covered": 59.79020979020979, "percent_covered_display": "60", "missing_lines": 115, "excluded_lines": 0}, "missing_lines": [96, 97, 100, 103, 105, 129, 131, 132, 133, 140, 142, 149, 151, 152, 157, 181, 183, 184, 189, 219, 220, 225, 230, 232, 233, 240, 241, 243, 244, 245, 247, 248, 252, 257, 259, 260, 267, 268, 270, 271, 272, 274, 275, 279, 284, 286, 287, 294, 295, 297, 298, 300, 301, 305, 312, 316, 347, 348, 349, 351, 362, 363, 365, 370, 371, 376, 380, 381, 382, 384, 385, 387, 388, 391, 403, 404, 406, 412, 414, 415, 420, 448, 449, 450, 461, 462, 464, 465, 472, 473, 481, 483, 484, 491, 492, 493, 501, 503, 504, 603, 605, 610, 611, 616, 622, 627, 630, 633, 638, 642, 645, 651, 654, 657, 663], "excluded_lines": [], "functions": {"User.__init__": {"executed_lines": [57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 80, 81, 83], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [96, 97, 100, 103, 105, 129, 131, 132, 133, 140, 142, 149, 151, 152, 157], "excluded_lines": []}, "User.get_by_id": {"executed_lines": [162, 163, 169, 171, 175, 176, 177, 178, 180], "summary": {"covered_lines": 9, "num_statements": 13, "percent_covered": 69.23076923076923, "percent_covered_display": "69", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [181, 183, 184, 189], "excluded_lines": []}, "User.get_by_email": {"executed_lines": [194, 196, 197, 204, 205, 207, 211, 212, 213, 214, 216, 217], "summary": {"covered_lines": 12, "num_statements": 15, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [219, 220, 225], "excluded_lines": []}, "User.get_by_verification_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [230, 232, 233, 240, 241, 243, 244, 245, 247, 248, 252], "excluded_lines": []}, "User.get_by_reset_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [257, 259, 260, 267, 268, 270, 271, 272, 274, 275, 279], "excluded_lines": []}, "User.find_by_email_global": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [284, 286, 287, 294, 295, 297, 298, 300, 301, 305], "excluded_lines": []}, "User.authenticate": {"executed_lines": [309, 311, 315, 319, 320, 321, 322, 325, 326, 328, 334, 336, 337, 343], "summary": {"covered_lines": 14, "num_statements": 16, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [312, 316], "excluded_lines": []}, "User.update_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [347, 348, 349, 351, 362, 363, 365, 370, 371, 376], "excluded_lines": []}, "User.verify_email": {"executed_lines": [574, 575, 576, 577, 578, 579], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.is_locked": {"executed_lines": [424, 425, 428, 429, 430, 432], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.is_active": {"executed_lines": [436], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.is_master": {"executed_lines": [440], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.is_member": {"executed_lines": [444], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User._increment_login_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [448, 449, 450, 461, 462, 464, 465], "excluded_lines": []}, "User._reset_login_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [472, 473, 481, 483, 484], "excluded_lines": []}, "User._update_last_login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [491, 492, 493, 501, 503, 504], "excluded_lines": []}, "User.to_dict": {"executed_lines": [511, 528, 529, 534], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.activate": {"executed_lines": [538, 539], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.deactivate": {"executed_lines": [543, 544], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.suspend": {"executed_lines": [548, 549], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.can_login": {"executed_lines": [553], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.can_manage_users": {"executed_lines": [559], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.can_manage_billing": {"executed_lines": [563], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.update_last_login": {"executed_lines": [567, 568, 569], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User.save": {"executed_lines": [583, 584, 602, 604, 607, 608], "summary": {"covered_lines": 6, "num_statements": 11, "percent_covered": 54.54545454545455, "percent_covered_display": "55", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [603, 605, 610, 611, 616], "excluded_lines": []}, "User._validate_email": {"executed_lines": [621, 624, 626, 629, 632, 636, 637, 640, 641, 644], "summary": {"covered_lines": 10, "num_statements": 17, "percent_covered": 58.8235294117647, "percent_covered_display": "59", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [622, 627, 630, 633, 638, 642, 645], "excluded_lines": []}, "User._validate_name": {"executed_lines": [650, 653, 656], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [651, 654, 657], "excluded_lines": []}, "User._validate_phone": {"executed_lines": [662, 665, 667, 668, 671, 674, 675, 678, 679], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [663], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 24, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 41, 42, 44, 85, 86, 159, 160, 191, 192, 227, 228, 254, 255, 281, 282, 307, 345, 378, 422, 434, 438, 442, 446, 470, 489, 509, 536, 541, 546, 551, 557, 561, 565, 572, 581, 618, 619, 647, 648, 659, 660], "summary": {"covered_lines": 56, "num_statements": 56, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"UserRole": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User": {"executed_lines": [57, 58, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 80, 81, 83, 162, 163, 169, 171, 175, 176, 177, 178, 180, 194, 196, 197, 204, 205, 207, 211, 212, 213, 214, 216, 217, 309, 311, 315, 319, 320, 321, 322, 325, 326, 328, 334, 336, 337, 343, 424, 425, 428, 429, 430, 432, 436, 440, 444, 511, 528, 529, 534, 538, 539, 543, 544, 548, 549, 553, 559, 563, 567, 568, 569, 574, 575, 576, 577, 578, 579, 583, 584, 602, 604, 607, 608, 621, 624, 626, 629, 632, 636, 637, 640, 641, 644, 650, 653, 656, 662, 665, 667, 668, 671, 674, 675, 678, 679], "summary": {"covered_lines": 115, "num_statements": 230, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 115, "excluded_lines": 0}, "missing_lines": [96, 97, 100, 103, 105, 129, 131, 132, 133, 140, 142, 149, 151, 152, 157, 181, 183, 184, 189, 219, 220, 225, 230, 232, 233, 240, 241, 243, 244, 245, 247, 248, 252, 257, 259, 260, 267, 268, 270, 271, 272, 274, 275, 279, 284, 286, 287, 294, 295, 297, 298, 300, 301, 305, 312, 316, 347, 348, 349, 351, 362, 363, 365, 370, 371, 376, 380, 381, 382, 384, 385, 387, 388, 391, 403, 404, 406, 412, 414, 415, 420, 448, 449, 450, 461, 462, 464, 465, 472, 473, 481, 483, 484, 491, 492, 493, 501, 503, 504, 603, 605, 610, 611, 616, 622, 627, 630, 633, 638, 642, 645, 651, 654, 657, 663], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 24, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 41, 42, 44, 85, 86, 159, 160, 191, 192, 227, 228, 254, 255, 281, 282, 307, 345, 378, 422, 434, 438, 442, 446, 470, 489, 509, 536, 541, 546, 551, 557, 561, 565, 572, 581, 618, 619, 647, 648, 659, 660], "summary": {"covered_lines": 56, "num_statements": 56, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\__init__.py": {"executed_lines": [0], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\email_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 22, 23, 24, 25, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 22, "num_statements": 76, "percent_covered": 28.94736842105263, "percent_covered_display": "29", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 47, 63, 79, 81, 83, 88, 104, 120, 122, 128, 143, 159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254, 266, 312, 359, 409, 410, 412, 418, 432, 439, 440, 445, 470, 471, 474, 476, 484, 503, 510, 511, 517, 528], "excluded_lines": [], "functions": {"EmailService.__init__": {"executed_lines": [22, 23, 24, 25], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmailService.send_verification_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 47, 63], "excluded_lines": []}, "EmailService.send_password_reset_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [79, 81, 83, 88, 104], "excluded_lines": []}, "EmailService.send_welcome_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [409, 410, 412, 418, 432, 439, 440, 445], "excluded_lines": []}, "EmailService._send_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254], "excluded_lines": []}, "EmailService._get_verification_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [266], "excluded_lines": []}, "EmailService._get_password_reset_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [312], "excluded_lines": []}, "EmailService._get_welcome_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [359], "excluded_lines": []}, "EmailService.send_user_invitation_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [470, 471, 474, 476, 484, 503, 510, 511, 517], "excluded_lines": []}, "EmailService._get_invitation_email_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [528], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"EmailService": {"executed_lines": [22, 23, 24, 25], "summary": {"covered_lines": 4, "num_statements": 58, "percent_covered": 6.896551724137931, "percent_covered_display": "7", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 47, 63, 79, 81, 83, 88, 104, 120, 122, 128, 143, 159, 161, 162, 186, 187, 198, 204, 206, 207, 208, 210, 211, 222, 229, 235, 236, 237, 248, 254, 266, 312, 359, 409, 410, 412, 418, 432, 439, 440, 445, 470, 471, 474, 476, 484, 503, 510, 511, 517, 528], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 18, 19, 21, 27, 70, 111, 150, 259, 306, 352, 396, 447, 519], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\auth\\services\\password_service.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 25, 26, 27, 30, 35, 48, 49, 51, 55, 56, 57, 59, 61, 72, 73, 75, 76, 77, 78, 79, 80, 82, 92, 96, 97, 99, 100, 103, 104, 106, 107, 109, 110, 112, 113, 116, 117, 118, 119, 122, 123, 126, 129, 131, 137, 139, 140, 141, 142, 144, 145, 147, 149, 151, 152, 154, 156, 166, 170, 171, 172, 173, 176, 184, 185, 186, 189, 191, 193, 204, 207, 208, 210, 212, 213, 215, 217, 218, 219, 227, 237, 240, 242, 243, 244, 245, 246, 247, 248, 249, 251, 255, 256, 258, 268, 271, 272, 273, 277, 279, 280, 281, 285, 286, 287, 288, 289, 291, 295], "summary": {"covered_lines": 119, "num_statements": 131, "percent_covered": 90.83969465648855, "percent_covered_display": "91", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [52, 93, 127, 153, 167, 220, 223, 225, 252, 274, 278, 282], "excluded_lines": [], "functions": {"PasswordService.__init__": {"executed_lines": [25, 26, 27, 30], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordService.hash_password": {"executed_lines": [48, 49, 51, 55, 56, 57, 59], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [52], "excluded_lines": []}, "PasswordService.verify_password": {"executed_lines": [72, 73, 75, 76, 77, 78, 79, 80], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordService.validate_password_strength": {"executed_lines": [92, 96, 97, 99, 100, 103, 104, 106, 107, 109, 110, 112, 113, 116, 117, 118, 119, 122, 123, 126], "summary": {"covered_lines": 20, "num_statements": 22, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [93, 127], "excluded_lines": []}, "PasswordService._has_sequential_chars": {"executed_lines": [131, 137, 139, 140, 141, 142, 144, 145, 147], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordService._has_repeated_chars": {"executed_lines": [151, 152, 154], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [153], "excluded_lines": []}, "PasswordService.generate_secure_password": {"executed_lines": [166, 170, 171, 172, 173, 176, 184, 185, 186, 189, 191], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [167], "excluded_lines": []}, "PasswordService.check_password_breach": {"executed_lines": [204, 207, 208, 210, 212, 213, 215, 217, 218, 219], "summary": {"covered_lines": 10, "num_statements": 13, "percent_covered": 76.92307692307692, "percent_covered_display": "77", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [220, 223, 225], "excluded_lines": []}, "PasswordService.calculate_password_entropy": {"executed_lines": [237, 240, 242, 243, 244, 245, 246, 247, 248, 249, 251, 255, 256], "summary": {"covered_lines": 13, "num_statements": 14, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [252], "excluded_lines": []}, "PasswordService.get_password_strength_score": {"executed_lines": [268, 271, 272, 273, 277, 279, 280, 281, 285, 286, 287, 288, 289, 291], "summary": {"covered_lines": 14, "num_statements": 17, "percent_covered": 82.3529411764706, "percent_covered_display": "82", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [274, 278, 282], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 35, 61, 82, 129, 149, 156, 193, 227, 258, 295], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PasswordService": {"executed_lines": [25, 26, 27, 30, 48, 49, 51, 55, 56, 57, 59, 72, 73, 75, 76, 77, 78, 79, 80, 92, 96, 97, 99, 100, 103, 104, 106, 107, 109, 110, 112, 113, 116, 117, 118, 119, 122, 123, 126, 131, 137, 139, 140, 141, 142, 144, 145, 147, 151, 152, 154, 166, 170, 171, 172, 173, 176, 184, 185, 186, 189, 191, 204, 207, 208, 210, 212, 213, 215, 217, 218, 219, 237, 240, 242, 243, 244, 245, 246, 247, 248, 249, 251, 255, 256, 268, 271, 272, 273, 277, 279, 280, 281, 285, 286, 287, 288, 289, 291], "summary": {"covered_lines": 99, "num_statements": 111, "percent_covered": 89.1891891891892, "percent_covered_display": "89", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [52, 93, 127, 153, 167, 220, 223, 225, 252, 274, 278, 282], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 35, 61, 82, 129, 149, 156, 193, 227, 258, 295], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\customer.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 40, 43, 44, 47, 48, 50, 54, 68, 84, 85, 99, 127, 145, 146, 168, 169, 190, 191, 212, 213, 248, 252, 257, 262, 267, 272, 276, 283, 287], "summary": {"covered_lines": 49, "num_statements": 129, "percent_covered": 37.98449612403101, "percent_covered_display": "38", "missing_lines": 80, "excluded_lines": 0}, "missing_lines": [52, 56, 57, 59, 60, 62, 63, 65, 66, 70, 88, 89, 90, 91, 94, 95, 97, 101, 102, 104, 115, 120, 121, 123, 124, 125, 129, 130, 138, 139, 141, 142, 143, 148, 150, 159, 160, 162, 164, 165, 166, 171, 172, 181, 182, 184, 186, 187, 188, 193, 194, 203, 204, 206, 208, 209, 210, 220, 221, 231, 232, 234, 236, 238, 244, 245, 246, 250, 254, 255, 259, 260, 264, 265, 269, 270, 274, 278, 285, 289], "excluded_lines": [], "functions": {"Customer.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [52], "excluded_lines": []}, "Customer._validate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [56, 57, 59, 60, 62, 63, 65, 66], "excluded_lines": []}, "Customer.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [70], "excluded_lines": []}, "Customer.from_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [88, 89, 90, 91, 94, 95, 97], "excluded_lines": []}, "Customer.save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [101, 102, 104, 115, 120, 121, 123, 124, 125], "excluded_lines": []}, "Customer.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [129, 130, 138, 139, 141, 142, 143], "excluded_lines": []}, "Customer.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [148, 150, 159, 160, 162, 164, 165, 166], "excluded_lines": []}, "Customer.get_by_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [171, 172, 181, 182, 184, 186, 187, 188], "excluded_lines": []}, "Customer.get_by_stripe_customer_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [193, 194, 203, 204, 206, 208, 209, 210], "excluded_lines": []}, "Customer.list_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [220, 221, 231, 232, 234, 236, 238, 244, 245, 246], "excluded_lines": []}, "Customer.is_active": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [250], "excluded_lines": []}, "Customer.activate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [254, 255], "excluded_lines": []}, "Customer.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [259, 260], "excluded_lines": []}, "Customer.suspend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [264, 265], "excluded_lines": []}, "Customer.update_stripe_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [269, 270], "excluded_lines": []}, "Customer.get_display_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [274], "excluded_lines": []}, "Customer.has_valid_payment_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [278], "excluded_lines": []}, "Customer.is_delinquent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [285], "excluded_lines": []}, "Customer.get_balance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [289], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 40, 43, 44, 47, 48, 50, 54, 68, 84, 85, 99, 127, 145, 146, 168, 169, 190, 191, 212, 213, 248, 252, 257, 262, 267, 272, 276, 283, 287], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CustomerStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 80, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 80, "excluded_lines": 0}, "missing_lines": [52, 56, 57, 59, 60, 62, 63, 65, 66, 70, 88, 89, 90, 91, 94, 95, 97, 101, 102, 104, 115, 120, 121, 123, 124, 125, 129, 130, 138, 139, 141, 142, 143, 148, 150, 159, 160, 162, 164, 165, 166, 171, 172, 181, 182, 184, 186, 187, 188, 193, 194, 203, 204, 206, 208, 209, 210, 220, 221, 231, 232, 234, 236, 238, 244, 245, 246, 250, 254, 255, 259, 260, 264, 265, 269, 270, 274, 278, 285, 289], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 40, 43, 44, 47, 48, 50, 54, 68, 84, 85, 99, 127, 145, 146, 168, 169, 190, 191, 212, 213, 248, 252, 257, 262, 267, 272, 276, 283, 287], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\plan.py": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 20, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 38, 39, 41, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 83, 85, 87, 90, 94, 95, 96, 99, 100, 103, 104, 106, 107, 111, 115, 118, 121, 123, 124, 126, 152, 154, 160, 171, 173, 174, 175, 177, 179, 180, 182, 184, 186, 187, 189, 190, 196, 197, 207, 208, 236, 237, 270, 271, 273, 366, 367, 368, 369, 370, 372, 374, 376, 378, 380, 382, 384, 385, 386, 388, 390, 392, 394, 396, 398, 416, 417, 423, 425, 427, 428, 430, 431, 432, 434, 436, 437, 438, 439, 442, 444, 445, 448, 449, 451, 453, 454, 455, 457, 459, 460, 461, 463, 465, 466, 467, 469, 470, 472, 473, 483, 484, 486, 490, 491, 492, 493, 495, 497], "summary": {"covered_lines": 147, "num_statements": 191, "percent_covered": 76.96335078534031, "percent_covered_display": "77", "missing_lines": 44, "excluded_lines": 0}, "missing_lines": [88, 91, 108, 112, 116, 119, 162, 163, 168, 176, 178, 198, 200, 201, 205, 210, 211, 213, 220, 221, 223, 224, 225, 227, 229, 230, 234, 239, 240, 242, 249, 250, 251, 254, 261, 262, 264, 265, 268, 440, 447, 499, 500, 501], "excluded_lines": [], "functions": {"Plan.__init__": {"executed_lines": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 83], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan._validate": {"executed_lines": [87, 90, 94, 95, 96, 99, 100, 103, 104, 106, 107, 111, 115, 118], "summary": {"covered_lines": 14, "num_statements": 20, "percent_covered": 70.0, "percent_covered_display": "70", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [88, 91, 108, 112, 116, 119], "excluded_lines": []}, "Plan.save": {"executed_lines": [123, 124, 126, 152, 154, 160], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [162, 163, 168], "excluded_lines": []}, "Plan.update": {"executed_lines": [173, 174, 175, 177, 179, 180, 182, 184], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [176, 178], "excluded_lines": []}, "Plan.get_by_id": {"executed_lines": [189, 190, 196, 197], "summary": {"covered_lines": 4, "num_statements": 8, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [198, 200, 201, 205], "excluded_lines": []}, "Plan.get_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [210, 211, 213, 220, 221, 223, 224, 225, 227, 229, 230, 234], "excluded_lines": []}, "Plan.list_active_plans": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [239, 240, 242, 249, 250, 251, 254, 261, 262, 264, 265, 268], "excluded_lines": []}, "Plan.create_default_plans": {"executed_lines": [273, 366, 367, 368, 369, 370, 372], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.is_active": {"executed_lines": [376], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.is_free": {"executed_lines": [380], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.get_price": {"executed_lines": [384, 385, 386], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.has_feature": {"executed_lines": [390], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.get_limit": {"executed_lines": [394], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.to_dict": {"executed_lines": [398, 416, 417, 423], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.calculate_yearly_discount": {"executed_lines": [427, 428, 430, 431, 432], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.get_price_for_interval": {"executed_lines": [436, 437, 438, 439], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [440], "excluded_lines": []}, "Plan.update_features": {"executed_lines": [444, 445, 448, 449], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [447], "excluded_lines": []}, "Plan.activate": {"executed_lines": [453, 454, 455], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.deactivate": {"executed_lines": [459, 460, 461], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.deprecate": {"executed_lines": [465, 466, 467], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan.get_all_active": {"executed_lines": [472, 473, 483, 484, 486, 490, 491, 492, 493, 495, 497], "summary": {"covered_lines": 11, "num_statements": 14, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [499, 500, 501], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 20, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 38, 39, 41, 85, 121, 171, 186, 187, 207, 208, 236, 237, 270, 271, 374, 378, 382, 388, 392, 396, 425, 434, 442, 451, 457, 463, 469, 470], "summary": {"covered_lines": 45, "num_statements": 45, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PlanStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PlanType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Plan": {"executed_lines": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 83, 87, 90, 94, 95, 96, 99, 100, 103, 104, 106, 107, 111, 115, 118, 123, 124, 126, 152, 154, 160, 173, 174, 175, 177, 179, 180, 182, 184, 189, 190, 196, 197, 273, 366, 367, 368, 369, 370, 372, 376, 380, 384, 385, 386, 390, 394, 398, 416, 417, 423, 427, 428, 430, 431, 432, 436, 437, 438, 439, 444, 445, 448, 449, 453, 454, 455, 459, 460, 461, 465, 466, 467, 472, 473, 483, 484, 486, 490, 491, 492, 493, 495, 497], "summary": {"covered_lines": 102, "num_statements": 146, "percent_covered": 69.86301369863014, "percent_covered_display": "70", "missing_lines": 44, "excluded_lines": 0}, "missing_lines": [88, 91, 108, 112, 116, 119, 162, 163, 168, 176, 178, 198, 200, 201, 205, 210, 211, 213, 220, 221, 223, 224, 225, 227, 229, 230, 234, 239, 240, 242, 249, 250, 251, 254, 261, 262, 264, 265, 268, 440, 447, 499, 500, 501], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 19, 20, 23, 24, 25, 26, 27, 30, 31, 32, 33, 34, 35, 38, 39, 41, 85, 121, 171, 186, 187, 207, 208, 236, 237, 270, 271, 374, 378, 382, 388, 392, 396, 425, 434, 442, 451, 457, 463, 469, 470], "summary": {"covered_lines": 45, "num_statements": 45, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\models\\subscription.py": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 53, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 91, 94, 96, 97, 99, 125, 127, 141, 143, 145, 147, 149, 150, 153, 155, 157, 158, 179, 180, 206, 207, 232, 234, 236, 243, 245, 247, 249, 250, 251, 253, 255, 256, 258, 259, 261, 263, 264, 270, 284, 286, 287, 293, 307, 309, 310, 315, 328, 330, 350, 351, 357, 359, 360, 382, 383], "summary": {"covered_lines": 100, "num_statements": 163, "percent_covered": 61.34969325153374, "percent_covered_display": "61", "missing_lines": 63, "excluded_lines": 0}, "missing_lines": [92, 133, 134, 139, 146, 148, 160, 161, 167, 168, 169, 171, 172, 177, 182, 183, 185, 191, 192, 194, 195, 197, 199, 200, 204, 209, 210, 212, 219, 220, 221, 223, 225, 226, 230, 238, 276, 277, 282, 299, 300, 305, 320, 321, 326, 362, 363, 372, 373, 374, 376, 378, 379, 380, 385, 386, 401, 402, 403, 405, 407, 408, 409], "excluded_lines": [], "functions": {"Subscription.__init__": {"executed_lines": [73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 91], "summary": {"covered_lines": 17, "num_statements": 18, "percent_covered": 94.44444444444444, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [92], "excluded_lines": []}, "Subscription.save": {"executed_lines": [96, 97, 99, 125, 127], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [133, 134, 139], "excluded_lines": []}, "Subscription.update": {"executed_lines": [143, 145, 147, 149, 150, 153, 155], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [146, 148], "excluded_lines": []}, "Subscription.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [160, 161, 167, 168, 169, 171, 172, 177], "excluded_lines": []}, "Subscription.get_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [182, 183, 185, 191, 192, 194, 195, 197, 199, 200, 204], "excluded_lines": []}, "Subscription.get_by_stripe_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [209, 210, 212, 219, 220, 221, 223, 225, 226, 230], "excluded_lines": []}, "Subscription.is_active": {"executed_lines": [234], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription.can_access_features": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [238], "excluded_lines": []}, "Subscription.is_trial": {"executed_lines": [245], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription.is_trial_expired": {"executed_lines": [249, 250, 251], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription.days_until_trial_end": {"executed_lines": [255, 256, 258, 259], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription.cancel": {"executed_lines": [263, 264, 270], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [276, 277, 282], "excluded_lines": []}, "Subscription.suspend": {"executed_lines": [286, 287, 293], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [299, 300, 305], "excluded_lines": []}, "Subscription.reactivate": {"executed_lines": [309, 310, 315], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [320, 321, 326], "excluded_lines": []}, "Subscription.to_dict": {"executed_lines": [330, 350, 351, 357], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription.get_by_stripe_subscription_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [362, 363, 372, 373, 374, 376, 378, 379, 380], "excluded_lines": []}, "Subscription.get_active_by_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [385, 386, 401, 402, 403, 405, 407, 408, 409], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 53, 94, 141, 157, 158, 179, 180, 206, 207, 232, 236, 243, 247, 253, 261, 284, 307, 328, 359, 360, 382, 383], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SubscriptionStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BillingInterval": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PaymentMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Subscription": {"executed_lines": [73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 91, 96, 97, 99, 125, 127, 143, 145, 147, 149, 150, 153, 155, 234, 245, 249, 250, 251, 255, 256, 258, 259, 263, 264, 270, 286, 287, 293, 309, 310, 315, 330, 350, 351, 357], "summary": {"covered_lines": 51, "num_statements": 114, "percent_covered": 44.73684210526316, "percent_covered_display": "45", "missing_lines": 63, "excluded_lines": 0}, "missing_lines": [92, 133, 134, 139, 146, 148, 160, 161, 167, 168, 169, 171, 172, 177, 182, 183, 185, 191, 192, 194, 195, 197, 199, 200, 204, 209, 210, 212, 219, 220, 221, 223, 225, 226, 230, 238, 276, 277, 282, 299, 300, 305, 320, 321, 326, 362, 363, 372, 373, 374, 376, 378, 379, 380, 385, 386, 401, 402, 403, 405, 407, 408, 409], "excluded_lines": []}, "": {"executed_lines": [4, 8, 9, 10, 11, 12, 14, 15, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 53, 94, 141, 157, 158, 179, 180, 206, 207, 232, 236, 243, 247, 253, 261, 284, 307, 328, 359, 360, 382, 383], "summary": {"covered_lines": 49, "num_statements": 49, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\customer_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 17, 24, 25, 27, 29, 31, 111, 119, 127, 210, 254, 287, 324, 342], "summary": {"covered_lines": 20, "num_statements": 136, "percent_covered": 14.705882352941176, "percent_covered_display": "15", "missing_lines": 116, "excluded_lines": 0}, "missing_lines": [54, 56, 57, 60, 61, 62, 63, 66, 68, 77, 86, 99, 101, 102, 104, 105, 106, 107, 108, 109, 113, 114, 115, 116, 117, 121, 122, 123, 124, 125, 154, 156, 157, 158, 161, 163, 164, 165, 167, 168, 169, 171, 172, 173, 175, 176, 177, 179, 180, 186, 187, 193, 194, 196, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 224, 226, 227, 228, 231, 234, 235, 236, 237, 238, 240, 242, 243, 245, 246, 247, 248, 249, 250, 251, 252, 267, 268, 269, 270, 273, 274, 276, 278, 279, 281, 282, 283, 284, 285, 301, 302, 303, 304, 307, 310, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 326], "excluded_lines": [], "functions": {"CustomerService.__init__": {"executed_lines": [29], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CustomerService.create_customer_for_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [54, 56, 57, 60, 61, 62, 63, 66, 68, 77, 86, 99, 101, 102, 104, 105, 106, 107, 108, 109], "excluded_lines": []}, "CustomerService.get_customer_by_tenant_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [113, 114, 115, 116, 117], "excluded_lines": []}, "CustomerService.get_customer_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [121, 122, 123, 124, 125], "excluded_lines": []}, "CustomerService.update_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [154, 156, 157, 158, 161, 163, 164, 165, 167, 168, 169, 171, 172, 173, 175, 176, 177, 179, 180, 186, 187, 193, 194, 196, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208], "excluded_lines": []}, "CustomerService.sync_customer_with_stripe": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [224, 226, 227, 228, 231, 234, 235, 236, 237, 238, 240, 242, 243, 245, 246, 247, 248, 249, 250, 251, 252], "excluded_lines": []}, "CustomerService.deactivate_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [267, 268, 269, 270, 273, 274, 276, 278, 279, 281, 282, 283, 284, 285], "excluded_lines": []}, "CustomerService.delete_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [301, 302, 303, 304, 307, 310, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322], "excluded_lines": []}, "CustomerService._serialize_stripe_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [326], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 17, 24, 25, 27, 31, 111, 119, 127, 210, 254, 287, 324, 342], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CustomerService": {"executed_lines": [29], "summary": {"covered_lines": 1, "num_statements": 117, "percent_covered": 0.8547008547008547, "percent_covered_display": "1", "missing_lines": 116, "excluded_lines": 0}, "missing_lines": [54, 56, 57, 60, 61, 62, 63, 66, 68, 77, 86, 99, 101, 102, 104, 105, 106, 107, 108, 109, 113, 114, 115, 116, 117, 121, 122, 123, 124, 125, 154, 156, 157, 158, 161, 163, 164, 165, 167, 168, 169, 171, 172, 173, 175, 176, 177, 179, 180, 186, 187, 193, 194, 196, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 224, 226, 227, 228, 231, 234, 235, 236, 237, 238, 240, 242, 243, 245, 246, 247, 248, 249, 250, 251, 252, 267, 268, 269, 270, 273, 274, 276, 278, 279, 281, 282, 283, 284, 285, 301, 302, 303, 304, 307, 310, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 326], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 15, 16, 17, 24, 25, 27, 31, 111, 119, 127, 210, 254, 287, 324, 342], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\stripe_client.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 24, 25, 27, 29, 30, 32, 35, 36, 37, 42, 43, 49, 53, 56, 59, 60, 66, 67, 69, 71, 72, 74, 76, 78, 82, 84, 88, 90, 93, 95, 99, 101, 105, 107, 113, 118, 127, 128, 134, 137, 143, 144, 146, 148, 149, 151, 152, 154, 156, 157, 158, 159, 160, 162, 175, 184, 194, 196, 201, 212, 213, 214, 215, 217, 222, 223, 225, 228, 230, 231, 236, 238, 239, 243, 247, 253, 254, 255, 256, 260, 266, 267, 268, 273, 275, 276, 281, 290, 291, 298, 301, 303, 304, 309, 316, 317, 322, 323, 325, 328, 330, 331, 336, 358, 384, 402, 420, 437, 445, 447, 450, 453, 457, 462, 465, 468, 470, 472, 473, 474, 476, 477, 481, 483, 486, 487, 488, 491], "summary": {"covered_lines": 135, "num_statements": 207, "percent_covered": 65.21739130434783, "percent_covered_display": "65", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [38, 44, 50, 135, 168, 169, 170, 171, 172, 173, 177, 178, 179, 180, 181, 182, 197, 220, 226, 233, 234, 244, 245, 257, 258, 278, 279, 299, 306, 307, 326, 333, 334, 343, 344, 349, 350, 352, 353, 355, 356, 366, 367, 373, 374, 376, 378, 379, 381, 382, 390, 391, 396, 397, 399, 400, 408, 409, 414, 415, 417, 418, 426, 427, 432, 434, 435, 448, 451, 454, 463, 466], "excluded_lines": [], "functions": {"StripeClientService.__init__": {"executed_lines": [29, 30], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StripeClientService._configure_stripe": {"executed_lines": [35, 36, 37, 42, 43, 49, 53, 56, 59, 60, 66, 67], "summary": {"covered_lines": 12, "num_statements": 15, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [38, 44, 50], "excluded_lines": []}, "StripeClientService._handle_stripe_error": {"executed_lines": [71, 72, 74, 76, 78, 82, 84, 88, 90, 93, 95, 99, 101, 105, 107, 113], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StripeClientService.create_customer": {"executed_lines": [127, 128, 134, 137, 143, 144, 146, 148, 149, 151, 152], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [135], "excluded_lines": []}, "StripeClientService.get_customer": {"executed_lines": [156, 157, 158, 159, 160], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StripeClientService.update_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [168, 169, 170, 171, 172, 173], "excluded_lines": []}, "StripeClientService.delete_customer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [177, 178, 179, 180, 181, 182], "excluded_lines": []}, "StripeClientService.create_subscription": {"executed_lines": [194, 196, 201, 212, 213, 214, 215, 217, 222, 223, 225, 228, 230, 231], "summary": {"covered_lines": 14, "num_statements": 19, "percent_covered": 73.6842105263158, "percent_covered_display": "74", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [197, 220, 226, 233, 234], "excluded_lines": []}, "StripeClientService.get_subscription": {"executed_lines": [238, 239, 243], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [244, 245], "excluded_lines": []}, "StripeClientService.update_subscription": {"executed_lines": [253, 254, 255, 256], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [257, 258], "excluded_lines": []}, "StripeClientService.cancel_subscription": {"executed_lines": [266, 267, 268, 273, 275, 276], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [278, 279], "excluded_lines": []}, "StripeClientService.create_price": {"executed_lines": [290, 291, 298, 301, 303, 304], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [299, 306, 307], "excluded_lines": []}, "StripeClientService.create_product": {"executed_lines": [316, 317, 322, 323, 325, 328, 330, 331], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [326, 333, 334], "excluded_lines": []}, "StripeClientService.list_prices": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [343, 344, 349, 350, 352, 353, 355, 356], "excluded_lines": []}, "StripeClientService.create_setup_intent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [366, 367, 373, 374, 376, 378, 379, 381, 382], "excluded_lines": []}, "StripeClientService.confirm_setup_intent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [390, 391, 396, 397, 399, 400], "excluded_lines": []}, "StripeClientService.attach_payment_method": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [408, 409, 414, 415, 417, 418], "excluded_lines": []}, "StripeClientService.list_payment_methods": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [426, 427, 432, 434, 435], "excluded_lines": []}, "StripeClientService.construct_webhook_event": {"executed_lines": [445, 447, 450, 453, 457, 462, 465, 468, 470, 472, 473, 474, 476, 477], "summary": {"covered_lines": 14, "num_statements": 19, "percent_covered": 73.6842105263158, "percent_covered_display": "74", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [448, 451, 454, 463, 466], "excluded_lines": []}, "get_stripe_client": {"executed_lines": [486, 487, 488], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 24, 25, 27, 32, 69, 118, 154, 162, 175, 184, 236, 247, 260, 281, 309, 336, 358, 384, 402, 420, 437, 481, 483, 491], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"StripeClientService": {"executed_lines": [29, 30, 35, 36, 37, 42, 43, 49, 53, 56, 59, 60, 66, 67, 71, 72, 74, 76, 78, 82, 84, 88, 90, 93, 95, 99, 101, 105, 107, 113, 127, 128, 134, 137, 143, 144, 146, 148, 149, 151, 152, 156, 157, 158, 159, 160, 194, 196, 201, 212, 213, 214, 215, 217, 222, 223, 225, 228, 230, 231, 238, 239, 243, 253, 254, 255, 256, 266, 267, 268, 273, 275, 276, 290, 291, 298, 301, 303, 304, 316, 317, 322, 323, 325, 328, 330, 331, 445, 447, 450, 453, 457, 462, 465, 468, 470, 472, 473, 474, 476, 477], "summary": {"covered_lines": 101, "num_statements": 173, "percent_covered": 58.38150289017341, "percent_covered_display": "58", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [38, 44, 50, 135, 168, 169, 170, 171, 172, 173, 177, 178, 179, 180, 181, 182, 197, 220, 226, 233, 234, 244, 245, 257, 258, 278, 279, 299, 306, 307, 326, 333, 334, 343, 344, 349, 350, 352, 353, 355, 356, 366, 367, 373, 374, 376, 378, 379, 381, 382, 390, 391, 396, 397, 399, 400, 408, 409, 414, 415, 417, 418, 426, 427, 432, 434, 435, 448, 451, 454, 463, 466], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 24, 25, 27, 32, 69, 118, 154, 162, 175, 184, 236, 247, 260, 281, 309, 336, 358, 384, 402, 420, 437, 481, 483, 486, 487, 488, 491], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\subscription_service.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 28, 29, 31, 33, 34, 36, 153, 161, 169, 226, 234, 247, 265], "summary": {"covered_lines": 23, "num_statements": 100, "percent_covered": 23.0, "percent_covered_display": "23", "missing_lines": 77, "excluded_lines": 0}, "missing_lines": [64, 66, 67, 68, 70, 71, 74, 75, 76, 81, 82, 83, 85, 86, 89, 90, 91, 94, 95, 96, 99, 108, 117, 135, 138, 139, 141, 142, 144, 145, 146, 147, 148, 149, 150, 151, 155, 156, 157, 158, 159, 163, 164, 165, 166, 167, 190, 192, 193, 194, 197, 203, 204, 205, 206, 207, 209, 210, 212, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 228, 229, 230, 231, 232, 236, 245, 249], "excluded_lines": [], "functions": {"SubscriptionService.__init__": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SubscriptionService.create_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [64, 66, 67, 68, 70, 71, 74, 75, 76, 81, 82, 83, 85, 86, 89, 90, 91, 94, 95, 96, 99, 108, 117, 135, 138, 139, 141, 142, 144, 145, 146, 147, 148, 149, 150, 151], "excluded_lines": []}, "SubscriptionService.get_subscription_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [155, 156, 157, 158, 159], "excluded_lines": []}, "SubscriptionService.get_active_subscription_by_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 167], "excluded_lines": []}, "SubscriptionService.cancel_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [190, 192, 193, 194, 197, 203, 204, 205, 206, 207, 209, 210, 212, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224], "excluded_lines": []}, "SubscriptionService._get_price_id_for_plan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [228, 229, 230, 231, 232], "excluded_lines": []}, "SubscriptionService._map_stripe_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [236, 245], "excluded_lines": []}, "SubscriptionService._serialize_stripe_subscription": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [249], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 28, 29, 31, 36, 153, 161, 169, 226, 234, 247, 265], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SubscriptionService": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 79, "percent_covered": 2.5316455696202533, "percent_covered_display": "3", "missing_lines": 77, "excluded_lines": 0}, "missing_lines": [64, 66, 67, 68, 70, 71, 74, 75, 76, 81, 82, 83, 85, 86, 89, 90, 91, 94, 95, 96, 99, 108, 117, 135, 138, 139, 141, 142, 144, 145, 146, 147, 148, 149, 150, 151, 155, 156, 157, 158, 159, 163, 164, 165, 166, 167, 190, 192, 193, 194, 197, 203, 204, 205, 206, 207, 209, 210, 212, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 228, 229, 230, 231, 232, 236, 245, 249], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 28, 29, 31, 36, 153, 161, 169, 226, 234, 247, 265], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\payment\\services\\webhook_service.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 27, 28, 30, 32, 33, 34, 37, 64, 87, 89, 93, 96, 97, 99, 101, 103, 104, 113, 114, 122, 123, 128, 130, 131, 132, 135, 141, 143, 144, 145, 148, 149, 151, 152, 153, 154, 155, 157, 159, 160, 162, 163, 169, 171, 172, 173, 176, 177, 178, 179, 181, 182, 191, 193, 194, 195, 198, 204, 206, 207, 208, 211, 212, 214, 215, 216, 217, 218, 219, 220, 222, 224, 225, 234, 236, 237, 238, 241, 242, 243, 244, 245, 246, 248, 250, 251, 260, 262, 263, 264, 267, 268, 270, 271, 280, 282, 283, 284, 287, 293, 295, 296, 297, 300, 306, 308, 309, 310, 313, 319, 332, 334, 335, 336, 338, 344, 346, 347, 348, 350, 356, 368, 380, 382, 391, 395], "summary": {"covered_lines": 136, "num_statements": 204, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 68, "excluded_lines": 0}, "missing_lines": [124, 125, 126, 137, 138, 139, 165, 166, 167, 184, 185, 187, 188, 189, 200, 201, 202, 227, 228, 230, 231, 232, 253, 254, 256, 257, 258, 273, 274, 276, 277, 278, 289, 290, 291, 302, 303, 304, 315, 316, 317, 321, 322, 323, 326, 328, 329, 330, 340, 341, 342, 352, 353, 354, 358, 359, 360, 362, 364, 365, 366, 370, 371, 372, 374, 376, 377, 378], "excluded_lines": [], "functions": {"WebhookService.__init__": {"executed_lines": [32, 33, 34, 37], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "WebhookService.process_webhook": {"executed_lines": [87, 89, 93, 96, 97, 99, 101, 103, 104, 113, 114, 122, 123], "summary": {"covered_lines": 13, "num_statements": 16, "percent_covered": 81.25, "percent_covered_display": "81", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [124, 125, 126], "excluded_lines": []}, "WebhookService._handle_customer_created": {"executed_lines": [130, 131, 132, 135], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [137, 138, 139], "excluded_lines": []}, "WebhookService._handle_customer_updated": {"executed_lines": [143, 144, 145, 148, 149, 151, 152, 153, 154, 155, 157, 159, 160, 162, 163], "summary": {"covered_lines": 15, "num_statements": 18, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [165, 166, 167], "excluded_lines": []}, "WebhookService._handle_customer_deleted": {"executed_lines": [171, 172, 173, 176, 177, 178, 179, 181, 182], "summary": {"covered_lines": 9, "num_statements": 14, "percent_covered": 64.28571428571429, "percent_covered_display": "64", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [184, 185, 187, 188, 189], "excluded_lines": []}, "WebhookService._handle_subscription_created": {"executed_lines": [193, 194, 195, 198], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [200, 201, 202], "excluded_lines": []}, "WebhookService._handle_subscription_updated": {"executed_lines": [206, 207, 208, 211, 212, 214, 215, 216, 217, 218, 219, 220, 222, 224, 225], "summary": {"covered_lines": 15, "num_statements": 20, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [227, 228, 230, 231, 232], "excluded_lines": []}, "WebhookService._handle_subscription_deleted": {"executed_lines": [236, 237, 238, 241, 242, 243, 244, 245, 246, 248, 250, 251], "summary": {"covered_lines": 12, "num_statements": 17, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [253, 254, 256, 257, 258], "excluded_lines": []}, "WebhookService._handle_subscription_trial_will_end": {"executed_lines": [262, 263, 264, 267, 268, 270, 271], "summary": {"covered_lines": 7, "num_statements": 12, "percent_covered": 58.333333333333336, "percent_covered_display": "58", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [273, 274, 276, 277, 278], "excluded_lines": []}, "WebhookService._handle_invoice_created": {"executed_lines": [282, 283, 284, 287], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [289, 290, 291], "excluded_lines": []}, "WebhookService._handle_invoice_payment_succeeded": {"executed_lines": [295, 296, 297, 300], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [302, 303, 304], "excluded_lines": []}, "WebhookService._handle_invoice_payment_failed": {"executed_lines": [308, 309, 310, 313], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [315, 316, 317], "excluded_lines": []}, "WebhookService._handle_invoice_upcoming": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [321, 322, 323, 326, 328, 329, 330], "excluded_lines": []}, "WebhookService._handle_payment_intent_succeeded": {"executed_lines": [334, 335, 336, 338], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [340, 341, 342], "excluded_lines": []}, "WebhookService._handle_payment_intent_failed": {"executed_lines": [346, 347, 348, 350], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [352, 353, 354], "excluded_lines": []}, "WebhookService._handle_setup_intent_succeeded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [358, 359, 360, 362, 364, 365, 366], "excluded_lines": []}, "WebhookService._handle_setup_intent_failed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [370, 371, 372, 374, 376, 377, 378], "excluded_lines": []}, "WebhookService._map_stripe_status": {"executed_lines": [382, 391], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 27, 28, 30, 64, 128, 141, 169, 191, 204, 234, 260, 280, 293, 306, 319, 332, 344, 356, 368, 380, 395], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"WebhookService": {"executed_lines": [32, 33, 34, 37, 87, 89, 93, 96, 97, 99, 101, 103, 104, 113, 114, 122, 123, 130, 131, 132, 135, 143, 144, 145, 148, 149, 151, 152, 153, 154, 155, 157, 159, 160, 162, 163, 171, 172, 173, 176, 177, 178, 179, 181, 182, 193, 194, 195, 198, 206, 207, 208, 211, 212, 214, 215, 216, 217, 218, 219, 220, 222, 224, 225, 236, 237, 238, 241, 242, 243, 244, 245, 246, 248, 250, 251, 262, 263, 264, 267, 268, 270, 271, 282, 283, 284, 287, 295, 296, 297, 300, 308, 309, 310, 313, 334, 335, 336, 338, 346, 347, 348, 350, 382, 391], "summary": {"covered_lines": 105, "num_statements": 173, "percent_covered": 60.69364161849711, "percent_covered_display": "61", "missing_lines": 68, "excluded_lines": 0}, "missing_lines": [124, 125, 126, 137, 138, 139, 165, 166, 167, 184, 185, 187, 188, 189, 200, 201, 202, 227, 228, 230, 231, 232, 253, 254, 256, 257, 258, 273, 274, 276, 277, 278, 289, 290, 291, 302, 303, 304, 315, 316, 317, 321, 322, 323, 326, 328, 329, 330, 340, 341, 342, 352, 353, 354, 358, 359, 360, 362, 364, 365, 366, 370, 371, 372, 374, 376, 377, 378], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 27, 28, 30, 64, 128, 141, 169, 191, 204, 234, 260, 280, 293, 306, 319, 332, 344, 356, 368, 380, 395], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\auth.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 26, 29, 30, 32, 33, 34, 36, 38, 40, 42, 44, 46, 49, 50, 53, 54, 57, 58, 61, 62, 65, 66, 69, 73, 74, 76, 78, 80, 83, 84, 86, 87, 88, 89, 90, 92, 94, 97, 101, 103, 104, 105, 106, 108, 109, 121, 123, 125, 126, 127, 136, 146, 151, 152, 157, 158, 163, 172, 173, 174, 176, 179, 183, 198, 200, 207, 215, 221, 228, 229, 230, 232, 235, 239, 252, 254, 261, 269, 275, 277, 278, 281, 337, 340, 341, 342, 344, 346, 348, 357, 358, 360, 368, 369, 370, 371, 372, 374, 376, 378, 380, 382, 384, 386, 388, 390, 392, 394, 396, 405, 422, 432, 438, 444, 498, 501, 502], "summary": {"covered_lines": 123, "num_statements": 208, "percent_covered": 59.13461538461539, "percent_covered_display": "59", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [111, 112, 114, 130, 131, 132, 134, 138, 140, 141, 142, 144, 148, 149, 154, 155, 161, 177, 217, 218, 219, 233, 271, 272, 273, 282, 284, 287, 288, 289, 290, 293, 294, 295, 298, 299, 300, 301, 304, 305, 306, 307, 316, 317, 320, 321, 322, 324, 325, 327, 328, 329, 330, 331, 334, 335, 339, 408, 410, 411, 419, 424, 426, 427, 429, 434, 435, 440, 441, 449, 451, 452, 453, 455, 457, 458, 468, 471, 473, 474, 475, 483, 484, 485, 494], "excluded_lines": [], "functions": {"PasswordManager.__init__": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordManager.hash_password": {"executed_lines": [38], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordManager.verify_password": {"executed_lines": [42], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordManager.validate_password_strength": {"executed_lines": [46, 49, 50, 53, 54, 57, 58, 61, 62, 65, 66, 69, 73, 74, 76], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PasswordManager.generate_secure_token": {"executed_lines": [80], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "JWTManager.__init__": {"executed_lines": [87, 88, 89, 90], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "JWTManager._get_jwt_config": {"executed_lines": [94, 97, 101, 103, 104, 105, 106, 108, 109], "summary": {"covered_lines": 9, "num_statements": 12, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [111, 112, 114], "excluded_lines": []}, "JWTManager.get_active_key": {"executed_lines": [123, 125, 126, 127], "summary": {"covered_lines": 4, "num_statements": 8, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [130, 131, 132, 134], "excluded_lines": []}, "JWTManager.get_key_by_kid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [138, 140, 141, 142, 144], "excluded_lines": []}, "JWTManager.get_all_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [148, 149], "excluded_lines": []}, "JWTManager.secret_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [154, 155], "excluded_lines": []}, "JWTManager.public_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [161], "excluded_lines": []}, "JWTManager.create_access_token": {"executed_lines": [172, 173, 174, 176, 179, 183, 198, 200, 207, 215], "summary": {"covered_lines": 10, "num_statements": 14, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [177, 217, 218, 219], "excluded_lines": []}, "JWTManager.create_refresh_token": {"executed_lines": [228, 229, 230, 232, 235, 239, 252, 254, 261, 269], "summary": {"covered_lines": 10, "num_statements": 14, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [233, 271, 272, 273], "excluded_lines": []}, "JWTManager.verify_token": {"executed_lines": [277, 278, 281, 337, 340, 341, 342], "summary": {"covered_lines": 7, "num_statements": 39, "percent_covered": 17.94871794871795, "percent_covered_display": "18", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [282, 284, 287, 288, 289, 290, 293, 294, 295, 298, 299, 300, 301, 304, 305, 306, 307, 316, 317, 320, 321, 322, 324, 325, 327, 328, 329, 330, 331, 334, 335, 339], "excluded_lines": []}, "JWTManager.extract_user_context": {"executed_lines": [346, 348], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.__init__": {"executed_lines": [368, 369, 370, 371, 372], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.is_master": {"executed_lines": [376], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.is_member": {"executed_lines": [380], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.can_manage_users": {"executed_lines": [384], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.can_manage_billing": {"executed_lines": [388], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.can_access_analytics": {"executed_lines": [392], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthContext.to_dict": {"executed_lines": [396], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "extract_auth_context_from_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [408, 410, 411, 419], "excluded_lines": []}, "require_auth": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [424, 426, 427, 429], "excluded_lines": []}, "require_master_role": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [434, 435], "excluded_lines": []}, "require_tenant_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [440, 441], "excluded_lines": []}, "auth_required": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [449, 451, 452, 494], "excluded_lines": []}, "auth_required.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [453, 455, 457, 458, 468, 471, 473, 474, 475, 483, 484, 485], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 26, 29, 30, 32, 36, 40, 44, 78, 83, 84, 86, 92, 121, 136, 146, 151, 152, 157, 158, 163, 221, 275, 344, 357, 358, 360, 374, 378, 382, 386, 390, 394, 405, 422, 432, 438, 444, 498, 501, 502], "summary": {"covered_lines": 46, "num_statements": 46, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PasswordManager": {"executed_lines": [33, 34, 38, 42, 46, 49, 50, 53, 54, 57, 58, 61, 62, 65, 66, 69, 73, 74, 76, 80], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "JWTManager": {"executed_lines": [87, 88, 89, 90, 94, 97, 101, 103, 104, 105, 106, 108, 109, 123, 125, 126, 127, 172, 173, 174, 176, 179, 183, 198, 200, 207, 215, 228, 229, 230, 232, 235, 239, 252, 254, 261, 269, 277, 278, 281, 337, 340, 341, 342, 346, 348], "summary": {"covered_lines": 46, "num_statements": 103, "percent_covered": 44.66019417475728, "percent_covered_display": "45", "missing_lines": 57, "excluded_lines": 0}, "missing_lines": [111, 112, 114, 130, 131, 132, 134, 138, 140, 141, 142, 144, 148, 149, 154, 155, 161, 177, 217, 218, 219, 233, 271, 272, 273, 282, 284, 287, 288, 289, 290, 293, 294, 295, 298, 299, 300, 301, 304, 305, 306, 307, 316, 317, 320, 321, 322, 324, 325, 327, 328, 329, 330, 331, 334, 335, 339], "excluded_lines": []}, "AuthContext": {"executed_lines": [368, 369, 370, 371, 372, 376, 380, 384, 388, 392, 396], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 18, 26, 29, 30, 32, 36, 40, 44, 78, 83, 84, 86, 92, 121, 136, 146, 151, 152, 157, 158, 163, 221, 275, 344, 357, 358, 360, 374, 378, 382, 386, 390, 394, 405, 422, 432, 438, 444, 498, 501, 502], "summary": {"covered_lines": 46, "num_statements": 74, "percent_covered": 62.16216216216216, "percent_covered_display": "62", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [408, 410, 411, 419, 424, 426, 427, 429, 434, 435, 440, 441, 449, 451, 452, 453, 455, 457, 458, 468, 471, 473, 474, 475, 483, 484, 485, 494], "excluded_lines": []}}}, "src\\shared\\config.py": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 65, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 82, 83, 85, 92, 95, 97, 100, 102, 105, 107, 110, 115, 117, 119, 122, 125, 142, 144, 151], "summary": {"covered_lines": 62, "num_statements": 71, "percent_covered": 87.32394366197182, "percent_covered_display": "87", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [112, 118, 120, 127, 128, 133, 134, 136, 153], "excluded_lines": [], "functions": {"get_settings": {"executed_lines": [97], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_development": {"executed_lines": [102], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_production": {"executed_lines": [107], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_testing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [112], "excluded_lines": []}, "get_log_level": {"executed_lines": [117, 119, 122], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [118, 120], "excluded_lines": []}, "get_cors_origins": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [127, 128, 133, 134, 136], "excluded_lines": []}, "get_database_config": {"executed_lines": [144], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_storage_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [153], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 65, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 82, 83, 85, 92, 95, 100, 105, 110, 115, 125, 142, 151], "summary": {"covered_lines": 55, "num_statements": 55, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 15, 16, 19, 20, 21, 24, 25, 28, 31, 34, 37, 38, 39, 40, 41, 44, 45, 46, 49, 50, 51, 52, 53, 56, 57, 60, 61, 62, 65, 66, 67, 68, 69, 70, 73, 74, 76, 77, 78, 79, 82, 83, 85, 92, 95, 97, 100, 102, 105, 107, 110, 115, 117, 119, 122, 125, 142, 144, 151], "summary": {"covered_lines": 62, "num_statements": 71, "percent_covered": 87.32394366197182, "percent_covered_display": "87", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [112, 118, 120, 127, 128, 133, 134, 136, 153], "excluded_lines": []}}}, "src\\shared\\database.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 24, 25, 28, 30, 42, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 23, "num_statements": 136, "percent_covered": 16.91176470588235, "percent_covered_display": "17", "missing_lines": 113, "excluded_lines": 0}, "missing_lines": [37, 46, 47, 48, 52, 53, 54, 55, 65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106, 119, 121, 123, 124, 127, 130, 131, 132, 133, 135, 136, 137, 139, 141, 142, 152, 153, 155, 157, 158, 159, 169, 170, 172, 189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245, 259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300, 318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": [], "functions": {"DynamoDBClient.__init__": {"executed_lines": [24, 25, 28, 30, 42], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "DynamoDBClient._add_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [46, 47, 48], "excluded_lines": []}, "DynamoDBClient._remove_tenant_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55], "excluded_lines": []}, "DynamoDBClient.get_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106], "excluded_lines": []}, "DynamoDBClient.put_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [119, 121, 123, 124, 127, 130, 131, 132, 133, 135, 136, 137, 139, 141, 142, 152, 153, 155, 157, 158, 159, 169, 170, 172], "excluded_lines": []}, "DynamoDBClient.update_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245], "excluded_lines": []}, "DynamoDBClient.delete_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300], "excluded_lines": []}, "DynamoDBClient.query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DynamoDBClient": {"executed_lines": [24, 25, 28, 30, 42], "summary": {"covered_lines": 5, "num_statements": 118, "percent_covered": 4.237288135593221, "percent_covered_display": "4", "missing_lines": 113, "excluded_lines": 0}, "missing_lines": [37, 46, 47, 48, 52, 53, 54, 55, 65, 67, 68, 70, 78, 79, 88, 89, 91, 93, 95, 96, 97, 106, 119, 121, 123, 124, 127, 130, 131, 132, 133, 135, 136, 137, 139, 141, 142, 152, 153, 155, 157, 158, 159, 169, 170, 172, 189, 191, 192, 195, 196, 197, 199, 206, 207, 209, 210, 212, 214, 215, 224, 225, 226, 228, 230, 231, 232, 242, 243, 245, 259, 261, 262, 264, 268, 269, 271, 273, 274, 283, 285, 286, 287, 297, 298, 300, 318, 320, 321, 323, 324, 325, 327, 332, 333, 335, 336, 338, 339, 341, 342, 344, 346, 347, 358, 359, 360, 361, 363, 370, 371, 372, 381], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 16, 17, 20, 21, 23, 44, 50, 57, 112, 178, 251, 306, 389], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\exceptions.py": {"executed_lines": [4, 9, 12, 13, 15, 22, 23, 24, 25, 26, 29, 30, 32, 38, 39, 42, 43, 45, 53, 54, 56, 66, 67, 69, 74, 77, 78, 80, 88, 89, 91, 99, 100, 102, 118, 119, 121, 137, 138, 140, 153, 154, 156, 172, 173, 175, 191, 192, 194, 210, 211, 213, 226, 227, 229, 242, 243, 245, 261, 262, 264, 280, 281, 283, 289, 290, 291, 293, 297, 298, 300, 301, 304, 305, 307, 311, 312, 314, 315, 318, 319, 321, 325, 326, 328, 333, 334, 336, 337, 340, 341, 343, 347, 348, 350, 354, 355, 357, 368, 369, 371, 384, 385, 387, 388, 389, 390, 391, 392], "summary": {"covered_lines": 79, "num_statements": 154, "percent_covered": 51.298701298701296, "percent_covered_display": "51", "missing_lines": 75, "excluded_lines": 0}, "missing_lines": [50, 62, 63, 85, 96, 109, 110, 111, 112, 113, 115, 128, 129, 130, 131, 132, 134, 146, 147, 148, 150, 163, 164, 165, 166, 167, 169, 182, 183, 184, 185, 186, 188, 201, 202, 203, 204, 205, 207, 219, 220, 221, 223, 235, 236, 237, 239, 252, 253, 254, 255, 256, 258, 271, 272, 273, 274, 275, 277, 308, 322, 329, 344, 351, 358, 359, 360, 361, 362, 372, 373, 374, 375, 376, 377], "excluded_lines": [], "functions": {"PlatformException.__init__": {"executed_lines": [22, 23, 24, 25, 26], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationException.__init__": {"executed_lines": [38, 39], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BusinessLogicException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "PaymentException.__init__": {"executed_lines": [388, 389, 390, 391, 392], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthenticationException.__init__": {"executed_lines": [74], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthorizationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [85], "excluded_lines": []}, "SecurityException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [96], "excluded_lines": []}, "ResourceNotFoundException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 112, 113, 115], "excluded_lines": []}, "ResourceConflictException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 134], "excluded_lines": []}, "RateLimitException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [146, 147, 148, 150], "excluded_lines": []}, "ExternalServiceException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 167, 169], "excluded_lines": []}, "EmailException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [182, 183, 184, 185, 186, 188], "excluded_lines": []}, "DatabaseException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [201, 202, 203, 204, 205, 207], "excluded_lines": []}, "TenantException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [219, 220, 221, 223], "excluded_lines": []}, "UserException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 239], "excluded_lines": []}, "AgentException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [271, 272, 273, 274, 275, 277], "excluded_lines": []}, "ConfigurationException.__init__": {"executed_lines": [289, 290, 291, 293], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InvalidCredentialsException.__init__": {"executed_lines": [301], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TokenExpiredException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [308], "excluded_lines": []}, "InvalidTokenException.__init__": {"executed_lines": [315], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AccountLockedException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [322], "excluded_lines": []}, "EmailNotVerifiedException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [329], "excluded_lines": []}, "InvalidEmailException.__init__": {"executed_lines": [337], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "WeakPasswordException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [344], "excluded_lines": []}, "InvalidTenantException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [351], "excluded_lines": []}, "TenantLimitExceededException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [358, 359, 360, 361, 362], "excluded_lines": []}, "SecurityViolationException.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [372, 373, 374, 375, 376, 377], "excluded_lines": []}, "": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 66, 67, 69, 77, 78, 80, 88, 89, 91, 99, 100, 102, 118, 119, 121, 137, 138, 140, 153, 154, 156, 172, 173, 175, 191, 192, 194, 210, 211, 213, 226, 227, 229, 242, 243, 245, 261, 262, 264, 280, 281, 283, 297, 298, 300, 304, 305, 307, 311, 312, 314, 318, 319, 321, 325, 326, 328, 333, 334, 336, 340, 341, 343, 347, 348, 350, 354, 355, 357, 368, 369, 371, 384, 385, 387], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PlatformException": {"executed_lines": [22, 23, 24, 25, 26], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationException": {"executed_lines": [38, 39], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BusinessLogicException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "PaymentException": {"executed_lines": [388, 389, 390, 391, 392], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthenticationException": {"executed_lines": [74], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthorizationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [85], "excluded_lines": []}, "SecurityException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [96], "excluded_lines": []}, "ResourceNotFoundException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 112, 113, 115], "excluded_lines": []}, "ResourceConflictException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 134], "excluded_lines": []}, "RateLimitException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [146, 147, 148, 150], "excluded_lines": []}, "ExternalServiceException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 166, 167, 169], "excluded_lines": []}, "EmailException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [182, 183, 184, 185, 186, 188], "excluded_lines": []}, "DatabaseException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [201, 202, 203, 204, 205, 207], "excluded_lines": []}, "TenantException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [219, 220, 221, 223], "excluded_lines": []}, "UserException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 239], "excluded_lines": []}, "AgentException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [271, 272, 273, 274, 275, 277], "excluded_lines": []}, "ConfigurationException": {"executed_lines": [289, 290, 291, 293], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InvalidCredentialsException": {"executed_lines": [301], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TokenExpiredException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [308], "excluded_lines": []}, "InvalidTokenException": {"executed_lines": [315], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AccountLockedException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [322], "excluded_lines": []}, "EmailNotVerifiedException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [329], "excluded_lines": []}, "InvalidEmailException": {"executed_lines": [337], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "WeakPasswordException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [344], "excluded_lines": []}, "InvalidTenantException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [351], "excluded_lines": []}, "TenantLimitExceededException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [358, 359, 360, 361, 362], "excluded_lines": []}, "SecurityViolationException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [372, 373, 374, 375, 376, 377], "excluded_lines": []}, "": {"executed_lines": [4, 9, 12, 13, 15, 29, 30, 32, 42, 43, 45, 53, 54, 56, 66, 67, 69, 77, 78, 80, 88, 89, 91, 99, 100, 102, 118, 119, 121, 137, 138, 140, 153, 154, 156, 172, 173, 175, 191, 192, 194, 210, 211, 213, 226, 227, 229, 242, 243, 245, 261, 262, 264, 280, 281, 283, 297, 298, 300, 304, 305, 307, 311, 312, 314, 318, 319, 321, 325, 326, 328, 333, 334, 336, 340, 341, 343, 347, 348, 350, 354, 355, 357, 368, 369, 371, 384, 385, 387], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\shared\\logger.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 25, 28, 39, 43, 44, 51, 53, 56, 58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82, 85, 87, 89, 98, 104, 107, 130, 160, 186, 214, 224, 234, 238, 239], "summary": {"covered_lines": 43, "num_statements": 59, "percent_covered": 72.88135593220339, "percent_covered_display": "73", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [40, 66, 117, 127, 142, 154, 155, 157, 170, 180, 181, 183, 197, 208, 209, 211], "excluded_lines": [], "functions": {"StructuredFormatter.format": {"executed_lines": [25, 28, 39, 43, 44, 51, 53], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "setup_logging": {"executed_lines": [58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [66], "excluded_lines": []}, "get_lambda_logger": {"executed_lines": [87, 89, 98, 104], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "log_api_request": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [117, 127], "excluded_lines": []}, "log_api_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [142, 154, 155, 157], "excluded_lines": []}, "log_database_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [170, 180, 181, 183], "excluded_lines": []}, "log_external_api_call": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [197, 208, 209, 211], "excluded_lines": []}, "log_security_event": {"executed_lines": [224, 234], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 85, 107, 130, 160, 186, 214, 238, 239], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"StructuredFormatter": {"executed_lines": [25, 28, 39, 43, 44, 51, 53], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 23, 56, 58, 61, 62, 65, 69, 70, 73, 74, 77, 80, 82, 85, 87, 89, 98, 104, 107, 130, 160, 186, 214, 224, 234, 238, 239], "summary": {"covered_lines": 36, "num_statements": 51, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [66, 117, 127, 142, 154, 155, 157, 170, 180, 181, 183, 197, 208, 209, 211], "excluded_lines": []}}}, "src\\shared\\responses.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 15, 18, 19, 26, 34, 35, 37, 48, 49, 57, 64, 65, 67, 68, 70, 71, 73, 84, 85, 91, 98, 99, 101, 103, 104, 106, 117, 118, 123, 125, 126, 131, 133, 134, 139, 141, 142, 147, 149, 150, 156, 163, 164, 169, 176, 177, 179, 180, 181, 183, 184, 189, 191, 192, 197, 200, 203, 204, 212, 213, 214, 216, 227, 234, 236], "excluded_lines": [], "functions": {"APIResponse.success": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37], "excluded_lines": []}, "APIResponse.error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [57, 64, 65, 67, 68, 70, 71, 73], "excluded_lines": []}, "APIResponse.created": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [91, 98, 99, 101], "excluded_lines": []}, "APIResponse.no_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [106], "excluded_lines": []}, "APIResponse.unauthorized": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [123], "excluded_lines": []}, "APIResponse.forbidden": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [131], "excluded_lines": []}, "APIResponse.not_found": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [139], "excluded_lines": []}, "APIResponse.conflict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [147], "excluded_lines": []}, "APIResponse.validation_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [156], "excluded_lines": []}, "APIResponse.rate_limit_exceeded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [169, 176, 177, 179, 180, 181], "excluded_lines": []}, "APIResponse.internal_server_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "APIResponse.service_unavailable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [197], "excluded_lines": []}, "PaginatedResponse.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 216, 227], "excluded_lines": []}, "handle_cors_preflight": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [236], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 15, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 203, 204, 234], "excluded_lines": []}}, "classes": {"APIResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [26, 34, 35, 37, 57, 64, 65, 67, 68, 70, 71, 73, 91, 98, 99, 101, 106, 123, 131, 139, 147, 156, 169, 176, 177, 179, 180, 181, 189, 197], "excluded_lines": []}, "PaginatedResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 216, 227], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 15, 18, 19, 48, 49, 84, 85, 103, 104, 117, 118, 125, 126, 133, 134, 141, 142, 149, 150, 163, 164, 183, 184, 191, 192, 200, 203, 204, 234, 236], "excluded_lines": []}}}, "src\\shared\\secrets.py": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 19, 20, 22, 23, 24, 25, 27, 42, 45, 46, 56, 57, 59, 60, 61, 90, 104, 115, 159, 208, 239, 272, 294, 301, 304, 309, 311, 313, 314, 315, 316, 318, 337], "summary": {"covered_lines": 39, "num_statements": 120, "percent_covered": 32.5, "percent_covered_display": "32", "missing_lines": 81, "excluded_lines": 0}, "missing_lines": [43, 47, 50, 51, 53, 54, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85, 106, 107, 108, 109, 110, 130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154, 173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203, 218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237, 251, 257, 258, 264, 265, 267, 268, 270, 285, 287, 288, 289, 290, 292, 296, 297, 306, 339], "excluded_lines": [], "functions": {"SecretsManager.__init__": {"executed_lines": [23, 24, 25], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecretsManager.get_secret": {"executed_lines": [42, 45, 46, 56, 57, 59, 60, 61], "summary": {"covered_lines": 8, "num_statements": 25, "percent_covered": 32.0, "percent_covered_display": "32", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [43, 47, 50, 51, 53, 54, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85], "excluded_lines": []}, "SecretsManager.get_secret_json": {"executed_lines": [104], "summary": {"covered_lines": 1, "num_statements": 6, "percent_covered": 16.666666666666668, "percent_covered_display": "17", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [106, 107, 108, 109, 110], "excluded_lines": []}, "SecretsManager.create_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154], "excluded_lines": []}, "SecretsManager.update_secret": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203], "excluded_lines": []}, "SecretsManager.get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237], "excluded_lines": []}, "SecretsManager._create_default_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [251, 257, 258, 264, 265, 267, 268, 270], "excluded_lines": []}, "SecretsManager.get_integration_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [285, 287, 288, 289, 290, 292], "excluded_lines": []}, "SecretsManager.clear_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [296, 297], "excluded_lines": []}, "get_jwt_keys": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}, "get_jwt_config": {"executed_lines": [311, 313, 314, 315, 316, 318], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_integration_credentials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [339], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 19, 20, 22, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 309, 337], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SecretsManager": {"executed_lines": [23, 24, 25, 42, 45, 46, 56, 57, 59, 60, 61, 104], "summary": {"covered_lines": 12, "num_statements": 91, "percent_covered": 13.186813186813186, "percent_covered_display": "13", "missing_lines": 79, "excluded_lines": 0}, "missing_lines": [43, 47, 50, 51, 53, 54, 65, 66, 67, 71, 72, 73, 78, 79, 83, 84, 85, 106, 107, 108, 109, 110, 130, 131, 137, 138, 140, 141, 143, 144, 145, 147, 148, 152, 153, 154, 173, 174, 180, 181, 183, 184, 186, 187, 189, 190, 191, 196, 197, 201, 202, 203, 218, 220, 221, 223, 224, 229, 231, 233, 234, 235, 237, 251, 257, 258, 264, 265, 267, 268, 270, 285, 287, 288, 289, 290, 292, 296, 297], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 14, 15, 16, 19, 20, 22, 27, 90, 115, 159, 208, 239, 272, 294, 301, 304, 309, 311, 313, 314, 315, 316, 318, 337], "summary": {"covered_lines": 27, "num_statements": 29, "percent_covered": 93.10344827586206, "percent_covered_display": "93", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [306, 339], "excluded_lines": []}}}, "src\\shared\\validators.py": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 28, 30, 32, 33, 34, 35, 38, 40, 41, 42, 43, 44, 47, 49, 50, 52, 55, 59, 62, 65, 67, 70, 73, 77, 80, 83, 85, 89, 92, 96, 97, 104, 123, 137, 138, 140, 141, 142, 143, 144, 146, 147, 148, 149, 151, 152, 153, 154, 156, 157, 158, 159, 161, 162, 163, 164, 165, 169, 170, 172, 173, 175, 176, 177, 178, 181, 182, 184, 186, 187, 188, 192, 193, 195, 196, 199, 200, 202, 205, 206, 208, 211, 212, 214, 215, 216, 217, 219, 220, 221, 224, 225, 226, 230, 231, 233, 234, 235, 238, 239, 241, 242, 243, 246, 247, 249, 250, 256, 257, 259, 260, 261, 262, 264, 265, 266, 267, 268, 271, 272, 273, 274, 275, 279, 280, 282, 283, 286, 287, 289, 291, 292, 293, 313, 314, 316, 317, 319, 320, 321, 326, 327, 328, 334, 335, 337, 338, 340, 341, 342, 346, 385, 406, 407, 408, 409], "summary": {"covered_lines": 149, "num_statements": 225, "percent_covered": 66.22222222222223, "percent_covered_display": "66", "missing_lines": 76, "excluded_lines": 0}, "missing_lines": [53, 56, 60, 68, 71, 74, 78, 86, 93, 98, 99, 101, 106, 107, 109, 117, 118, 120, 125, 126, 128, 129, 131, 132, 134, 166, 189, 222, 227, 269, 276, 294, 295, 298, 299, 300, 302, 303, 304, 306, 307, 308, 310, 322, 323, 324, 329, 330, 331, 343, 352, 354, 356, 357, 359, 362, 363, 365, 366, 367, 368, 370, 371, 372, 373, 377, 382, 387, 388, 391, 394, 395, 398, 399, 400, 402], "excluded_lines": [], "functions": {"validate_email_address": {"executed_lines": [30, 32, 33, 34, 35], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "validate_uuid": {"executed_lines": [40, 41, 42, 43, 44], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "validate_tenant_name": {"executed_lines": [49, 50, 52, 55, 59, 62], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [53, 56, 60], "excluded_lines": []}, "validate_user_name": {"executed_lines": [67, 70, 73, 77, 80], "summary": {"covered_lines": 5, "num_statements": 9, "percent_covered": 55.55555555555556, "percent_covered_display": "56", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [68, 71, 74, 78], "excluded_lines": []}, "validate_phone_number": {"executed_lines": [85, 89, 92, 96, 97], "summary": {"covered_lines": 5, "num_statements": 10, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [86, 93, 98, 99, 101], "excluded_lines": []}, "validate_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [106, 107, 109, 117, 118, 120], "excluded_lines": []}, "validate_pagination_params": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [125, 126, 128, 129, 131, 132, 134], "excluded_lines": []}, "RegisterRequestValidator.validate_tenant_name_field": {"executed_lines": [149], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RegisterRequestValidator.validate_user_name_field": {"executed_lines": [154], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RegisterRequestValidator.validate_email_field": {"executed_lines": [159], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RegisterRequestValidator.validate_phone_field": {"executed_lines": [164, 165], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [166], "excluded_lines": []}, "LoginRequestValidator.validate_email_field": {"executed_lines": [178], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ForgotPasswordRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "InviteUserRequestValidator.validate_email_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [222], "excluded_lines": []}, "InviteUserRequestValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [227], "excluded_lines": []}, "UpdateUserRequestValidator.validate_name_field": {"executed_lines": [267, 268], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [269], "excluded_lines": []}, "UpdateUserRequestValidator.validate_phone_field": {"executed_lines": [274, 275], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [276], "excluded_lines": []}, "TenantSettingsValidator.validate_settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [294, 295, 298, 299, 300, 302, 303, 304, 306, 307, 308, 310], "excluded_lines": []}, "UserProfileUpdateValidator.validate_name_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [322, 323, 324], "excluded_lines": []}, "UserProfileUpdateValidator.validate_phone_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [329, 330, 331], "excluded_lines": []}, "ChangePasswordValidator.validate_new_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "validate_request_body": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [352, 354, 356, 357, 359, 362, 363, 365, 366, 367, 368, 370, 371, 372, 373, 377, 382], "excluded_lines": []}, "sanitize_string": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [387, 388, 391, 394, 395, 398, 399, 400, 402], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 28, 38, 47, 65, 83, 104, 123, 137, 138, 140, 141, 142, 143, 144, 146, 147, 148, 151, 152, 153, 156, 157, 158, 161, 162, 163, 169, 170, 172, 173, 175, 176, 177, 181, 182, 184, 186, 187, 188, 192, 193, 195, 196, 199, 200, 202, 205, 206, 208, 211, 212, 214, 215, 216, 217, 219, 220, 221, 224, 225, 226, 230, 231, 233, 234, 235, 238, 239, 241, 242, 243, 246, 247, 249, 250, 256, 257, 259, 260, 261, 262, 264, 265, 266, 271, 272, 273, 279, 280, 282, 283, 286, 287, 289, 291, 292, 293, 313, 314, 316, 317, 319, 320, 321, 326, 327, 328, 334, 335, 337, 338, 340, 341, 342, 346, 385, 406, 407, 408, 409], "summary": {"covered_lines": 113, "num_statements": 113, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BaseValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RegisterRequestValidator": {"executed_lines": [149, 154, 159, 164, 165], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [166], "excluded_lines": []}, "LoginRequestValidator": {"executed_lines": [178], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ForgotPasswordRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "ResetPasswordRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VerifyEmailRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RefreshTokenRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InviteUserRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [222, 227], "excluded_lines": []}, "AcceptInvitationRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CreateSubscriptionRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CancelSubscriptionRequestValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UpdateUserRequestValidator": {"executed_lines": [267, 268, 274, 275], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [269, 276], "excluded_lines": []}, "PaginationValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantSettingsValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [294, 295, 298, 299, 300, 302, 303, 304, 306, 307, 308, 310], "excluded_lines": []}, "UserProfileUpdateValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [322, 323, 324, 329, 330, 331], "excluded_lines": []}, "ChangePasswordValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 12, 13, 15, 18, 19, 21, 28, 30, 32, 33, 34, 35, 38, 40, 41, 42, 43, 44, 47, 49, 50, 52, 55, 59, 62, 65, 67, 70, 73, 77, 80, 83, 85, 89, 92, 96, 97, 104, 123, 137, 138, 140, 141, 142, 143, 144, 146, 147, 148, 151, 152, 153, 156, 157, 158, 161, 162, 163, 169, 170, 172, 173, 175, 176, 177, 181, 182, 184, 186, 187, 188, 192, 193, 195, 196, 199, 200, 202, 205, 206, 208, 211, 212, 214, 215, 216, 217, 219, 220, 221, 224, 225, 226, 230, 231, 233, 234, 235, 238, 239, 241, 242, 243, 246, 247, 249, 250, 256, 257, 259, 260, 261, 262, 264, 265, 266, 271, 272, 273, 279, 280, 282, 283, 286, 287, 289, 291, 292, 293, 313, 314, 316, 317, 319, 320, 321, 326, 327, 328, 334, 335, 337, 338, 340, 341, 342, 346, 385, 406, 407, 408, 409], "summary": {"covered_lines": 139, "num_statements": 190, "percent_covered": 73.15789473684211, "percent_covered_display": "73", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [53, 56, 60, 68, 71, 74, 78, 86, 93, 98, 99, 101, 106, 107, 109, 117, 118, 120, 125, 126, 128, 129, 131, 132, 134, 352, 354, 356, 357, 359, 362, 363, 365, 366, 367, 368, 370, 371, 372, 373, 377, 382, 387, 388, 391, 394, 395, 398, 399, 400, 402], "excluded_lines": []}}}, "src\\tenant\\__init__.py": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [4, 9, 10], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\tenant\\services\\data_isolation_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 19, 20, 23, 24, 25, 26, 27, 28, 31, 32, 34, 35, 37, 129, 157, 176, 194, 241, 247, 281, 344, 398], "summary": {"covered_lines": 25, "num_statements": 92, "percent_covered": 27.17391304347826, "percent_covered_display": "27", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [62, 71, 72, 82, 88, 89, 99, 105, 106, 115, 120, 127, 138, 139, 142, 144, 145, 148, 149, 152, 155, 174, 186, 187, 188, 189, 190, 191, 192, 215, 216, 218, 219, 221, 223, 224, 231, 232, 239, 245, 266, 267, 269, 271, 276, 277, 279, 302, 314, 315, 318, 319, 321, 328, 329, 336, 338, 339, 356, 363, 365, 372, 378, 387, 389, 390, 394], "excluded_lines": [], "functions": {"TenantDataIsolationService.__init__": {"executed_lines": [35], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantDataIsolationService.validate_tenant_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [62, 71, 72, 82, 88, 89, 99, 105, 106, 115, 120, 127], "excluded_lines": []}, "TenantDataIsolationService._validate_operation_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [138, 139, 142, 144, 145, 148, 149, 152, 155], "excluded_lines": []}, "TenantDataIsolationService.create_tenant_scoped_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [174], "excluded_lines": []}, "TenantDataIsolationService.extract_tenant_from_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [186, 187, 188, 189, 190, 191, 192], "excluded_lines": []}, "TenantDataIsolationService.validate_data_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [215, 216, 218, 219, 221, 223, 224, 231, 232, 239], "excluded_lines": []}, "TenantDataIsolationService._is_system_admin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [245], "excluded_lines": []}, "TenantDataIsolationService.create_isolated_query_params": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [266, 267, 269, 271, 276, 277, 279], "excluded_lines": []}, "TenantDataIsolationService.audit_data_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [302, 314, 315, 318, 319, 321, 328, 329, 336, 338, 339], "excluded_lines": []}, "TenantDataIsolationService.get_tenant_data_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [356, 363, 365, 372, 378, 387, 389, 390, 394], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 19, 20, 23, 24, 25, 26, 27, 28, 31, 32, 34, 37, 129, 157, 176, 194, 241, 247, 281, 344, 398], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DataAccessLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantDataIsolationService": {"executed_lines": [35], "summary": {"covered_lines": 1, "num_statements": 68, "percent_covered": 1.4705882352941178, "percent_covered_display": "1", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [62, 71, 72, 82, 88, 89, 99, 105, 106, 115, 120, 127, 138, 139, 142, 144, 145, 148, 149, 152, 155, 174, 186, 187, 188, 189, 190, 191, 192, 215, 216, 218, 219, 221, 223, 224, 231, 232, 239, 245, 266, 267, 269, 271, 276, 277, 279, 302, 314, 315, 318, 319, 321, 328, 329, 336, 338, 339, 356, 363, 365, 372, 378, 387, 389, 390, 394], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 19, 20, 23, 24, 25, 26, 27, 28, 31, 32, 34, 37, 129, 157, 176, 194, 241, 247, 281, 344, 398], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\tenant\\services\\provisioning_service.py": {"executed_lines": [4, 9, 10, 11, 13, 14, 19, 20, 21, 24, 25, 27, 28, 30, 144, 162, 180, 228, 246, 311, 339, 374, 385, 409], "summary": {"covered_lines": 22, "num_statements": 82, "percent_covered": 26.829268292682926, "percent_covered_display": "27", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [62, 68, 70, 71, 78, 79, 86, 95, 104, 107, 110, 112, 119, 131, 132, 139, 140, 142, 146, 148, 149, 156, 158, 159, 160, 164, 166, 167, 174, 176, 177, 178, 189, 190, 192, 220, 226, 237, 248, 251, 282, 285, 309, 313, 315, 337, 341, 372, 376, 383, 387, 388, 391, 398, 399, 400, 401, 402, 404, 405], "excluded_lines": [], "functions": {"TenantProvisioningService.__init__": {"executed_lines": [28], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TenantProvisioningService.provision_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [62, 68, 70, 71, 78, 79, 86, 95, 104, 107, 110, 112, 119, 131, 132, 139, 140, 142], "excluded_lines": []}, "TenantProvisioningService._check_tenant_name_exists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [146, 148, 149, 156, 158, 159, 160], "excluded_lines": []}, "TenantProvisioningService._check_email_exists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [164, 166, 167, 174, 176, 177, 178], "excluded_lines": []}, "TenantProvisioningService._create_tenant": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [189, 190, 192, 220, 226], "excluded_lines": []}, "TenantProvisioningService._create_master_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [237], "excluded_lines": []}, "TenantProvisioningService._initialize_tenant_resources": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [248, 251, 282, 285, 309], "excluded_lines": []}, "TenantProvisioningService._create_tenant_configuration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [313, 315, 337], "excluded_lines": []}, "TenantProvisioningService._get_plan_limits": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [341, 372], "excluded_lines": []}, "TenantProvisioningService._get_api_rate_limit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [376, 383], "excluded_lines": []}, "TenantProvisioningService._cleanup_failed_provisioning": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [387, 388, 391, 398, 399, 400, 401, 402, 404, 405], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 19, 20, 21, 24, 25, 27, 30, 144, 162, 180, 228, 246, 311, 339, 374, 385, 409], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TenantProvisioningService": {"executed_lines": [28], "summary": {"covered_lines": 1, "num_statements": 61, "percent_covered": 1.639344262295082, "percent_covered_display": "2", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [62, 68, 70, 71, 78, 79, 86, 95, 104, 107, 110, 112, 119, 131, 132, 139, 140, 142, 146, 148, 149, 156, 158, 159, 160, 164, 166, 167, 174, 176, 177, 178, 189, 190, 192, 220, 226, 237, 248, 251, 282, 285, 309, 313, 315, 337, 341, 372, 376, 383, 387, 388, 391, 398, 399, 400, 401, 402, 404, 405], "excluded_lines": []}, "": {"executed_lines": [4, 9, 10, 11, 13, 14, 19, 20, 21, 24, 25, 27, 30, 144, 162, 180, 228, 246, 311, 339, 374, 385, 409], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\user\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\user\\handlers\\__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src\\user\\handlers\\change_password.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 23, 26, 38, 39, 41, 42, 45, 52, 54, 56, 57, 58, 59, 64, 67, 68, 69, 70, 72, 73, 75, 76, 77, 83, 84, 85, 86, 87, 92, 93, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 126, 132, 135, 136, 139, 141, 142, 143, 149, 157, 158, 167, 172, 173, 174, 184, 191, 192, 193, 203, 209], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 59, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 59, "excluded_lines": 0}, "missing_lines": [38, 39, 41, 42, 45, 52, 54, 56, 57, 58, 59, 64, 67, 68, 69, 70, 72, 73, 75, 76, 77, 83, 84, 85, 86, 87, 92, 93, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 126, 132, 135, 136, 139, 141, 142, 143, 149, 157, 158, 167, 172, 173, 174, 184, 191, 192, 193, 203, 209], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 23, 26], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 23, 26, 38, 39, 41, 42, 45, 52, 54, 56, 57, 58, 59, 64, 67, 68, 69, 70, 72, 73, 75, 76, 77, 83, 84, 85, 86, 87, 92, 93, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 126, 132, 135, 136, 139, 141, 142, 143, 149, 157, 158, 167, 172, 173, 174, 184, 191, 192, 193, 203, 209], "excluded_lines": []}}}, "src\\user\\handlers\\deactivate_user.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 81, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 81, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 35, 38, 45, 47, 49, 50, 51, 52, 57, 60, 61, 62, 63, 64, 66, 67, 69, 70, 71, 77, 78, 79, 85, 86, 88, 89, 90, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 118, 124, 125, 126, 132, 133, 134, 140, 141, 142, 143, 146, 148, 149, 150, 156, 169, 170, 171, 172, 174, 180, 181, 190, 195, 196, 197, 207, 214, 215, 216, 226, 233], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 73, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 73, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 35, 38, 45, 47, 49, 50, 51, 52, 57, 60, 61, 62, 63, 64, 66, 67, 69, 70, 71, 77, 78, 79, 85, 86, 88, 89, 90, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 118, 124, 125, 126, 132, 133, 134, 140, 141, 142, 143, 146, 148, 149, 150, 156, 169, 170, 171, 172, 174, 180, 181, 190, 195, 196, 197, 207, 214, 215, 216, 226, 233], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 81, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 81, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 35, 38, 45, 47, 49, 50, 51, 52, 57, 60, 61, 62, 63, 64, 66, 67, 69, 70, 71, 77, 78, 79, 85, 86, 88, 89, 90, 95, 98, 100, 101, 102, 108, 109, 110, 116, 117, 118, 124, 125, 126, 132, 133, 134, 140, 141, 142, 143, 146, 148, 149, 150, 156, 169, 170, 171, 172, 174, 180, 181, 190, 195, 196, 197, 207, 214, 215, 216, 226, 233], "excluded_lines": []}}}, "src\\user\\handlers\\get_profile.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 37, 44, 46, 48, 49, 50, 51, 56, 59, 60, 61, 62, 64, 65, 67, 68, 69, 74, 77, 79, 80, 81, 87, 88, 89, 95, 96, 97, 98, 100, 109, 110, 119, 124, 125, 126, 136, 143, 144, 145, 155, 161], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 45, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [31, 32, 34, 37, 44, 46, 48, 49, 50, 51, 56, 59, 60, 61, 62, 64, 65, 67, 68, 69, 74, 77, 79, 80, 81, 87, 88, 89, 95, 96, 97, 98, 100, 109, 110, 119, 124, 125, 126, 136, 143, 144, 145, 155, 161], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 18, 19, 20, 23, 31, 32, 34, 37, 44, 46, 48, 49, 50, 51, 56, 59, 60, 61, 62, 64, 65, 67, 68, 69, 74, 77, 79, 80, 81, 87, 88, 89, 95, 96, 97, 98, 100, 109, 110, 119, 124, 125, 126, 136, 143, 144, 145, 155, 161], "excluded_lines": []}}}, "src\\user\\handlers\\update_profile.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 37, 38, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 70, 71, 73, 74, 75, 81, 82, 83, 84, 85, 90, 93, 95, 96, 97, 103, 104, 105, 111, 112, 114, 115, 118, 120, 121, 122, 128, 129, 130, 131, 133, 137, 138, 147, 152, 153, 154, 164, 171, 172, 173, 183, 189], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 58, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [37, 38, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 70, 71, 73, 74, 75, 81, 82, 83, 84, 85, 90, 93, 95, 96, 97, 103, 104, 105, 111, 112, 114, 115, 118, 120, 121, 122, 128, 129, 130, 131, 133, 137, 138, 147, 152, 153, 154, 164, 171, 172, 173, 183, 189], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 37, 38, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 70, 71, 73, 74, 75, 81, 82, 83, 84, 85, 90, 93, 95, 96, 97, 103, 104, 105, 111, 112, 114, 115, 118, 120, 121, 122, 128, 129, 130, 131, 133, 137, 138, 147, 152, 153, 154, 164, 171, 172, 173, 183, 189], "excluded_lines": []}}}, "src\\user\\handlers\\update_role.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 85, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 36, 37, 39, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 82, 83, 84, 90, 91, 93, 94, 95, 101, 102, 103, 104, 105, 110, 111, 112, 117, 120, 122, 123, 124, 130, 131, 132, 138, 139, 140, 146, 147, 148, 154, 155, 158, 160, 161, 162, 168, 182, 183, 184, 185, 187, 192, 193, 202, 207, 208, 209, 219, 226, 227, 228, 238, 245], "excluded_lines": [], "functions": {"handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 76, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 76, "excluded_lines": 0}, "missing_lines": [36, 37, 39, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 82, 83, 84, 90, 91, 93, 94, 95, 101, 102, 103, 104, 105, 110, 111, 112, 117, 120, 122, 123, 124, 130, 131, 132, 138, 139, 140, 146, 147, 148, 154, 155, 158, 160, 161, 162, 168, 182, 183, 184, 185, 187, 192, 193, 202, 207, 208, 209, 219, 226, 227, 228, 238, 245], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 85, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [9, 10, 12, 13, 14, 20, 21, 22, 25, 36, 37, 39, 40, 43, 50, 52, 54, 55, 56, 57, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 82, 83, 84, 90, 91, 93, 94, 95, 101, 102, 103, 104, 105, 110, 111, 112, 117, 120, 122, 123, 124, 130, 131, 132, 138, 139, 140, 146, 147, 148, 154, 155, 158, 160, 161, 162, 168, 182, 183, 184, 185, 187, 192, 193, 202, 207, 208, 209, 219, 226, 227, 228, 238, 245], "excluded_lines": []}}}}, "totals": {"covered_lines": 1650, "num_statements": 3418, "percent_covered": 48.27384435342305, "percent_covered_display": "48", "missing_lines": 1768, "excluded_lines": 0}}