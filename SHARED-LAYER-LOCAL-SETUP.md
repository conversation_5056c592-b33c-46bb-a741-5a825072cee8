# 🔧 Configuración de Shared Layer para Desarrollo Local

## 📋 Problema

En AWS, los servicios usan Lambda Layers para compartir código común (carpeta `shared`). En desarrollo local, necesitamos simular este comportamiento para que los imports funcionen correctamente.

## 🚀 Soluciones Disponibles

### **Opción 1: Instalación Editable con pip (Recomendada)**

Esta es la forma más limpia y profesional:

#### **Windows (PowerShell):**
```powershell
# Ejecutar desde la raíz del proyecto
.\install-shared-local.ps1
```

#### **Linux/Mac (Bash):**
```bash
# Ejecutar desde la raíz del proyecto
chmod +x install-shared-local.sh
./install-shared-local.sh
```

#### **Manual:**
```bash
# Navegar a la carpeta shared
cd shared/python

# Instalar en modo editable
pip install -e .

# Volver a la raíz
cd ../..
```

**✅ Ventajas:**
- Los cambios en `shared/` se reflejan inmediatamente
- Funciona en cualquier directorio
- Simula exactamente el comportamiento de AWS Layers
- Fácil de desinstalar: `pip uninstall agent-scl-shared`

---

### **Opción 2: Variable PYTHONPATH**

#### **Windows (PowerShell):**
```powershell
# Temporal (solo para la sesión actual)
$env:PYTHONPATH = "$env:PYTHONPATH;$PWD\shared\python"

# Permanente (agregar al perfil de PowerShell)
Add-Content $PROFILE '$env:PYTHONPATH = "$env:PYTHONPATH;C:\ruta\completa\al\proyecto\shared\python"'
```

#### **Linux/Mac (Bash):**
```bash
# Temporal (solo para la sesión actual)
export PYTHONPATH="$PYTHONPATH:$(pwd)/shared/python"

# Permanente (agregar a ~/.bashrc o ~/.zshrc)
echo 'export PYTHONPATH="$PYTHONPATH:/ruta/completa/al/proyecto/shared/python"' >> ~/.bashrc
```

**✅ Ventajas:**
- No requiere instalación
- Cambios inmediatos

**❌ Desventajas:**
- Hay que configurar en cada terminal
- Dependiente del directorio de trabajo

---

### **Opción 3: Modificación de sys.path en Código**

Agregar al inicio de cada handler que use shared:

```python
import sys
import os

# Agregar shared al path
shared_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared', 'python')
if shared_path not in sys.path:
    sys.path.insert(0, shared_path)

# Ahora puedes importar shared
from shared.models import UserRole
from shared.auth import require_auth
```

**✅ Ventajas:**
- No requiere configuración externa
- Funciona siempre

**❌ Desventajas:**
- Hay que agregarlo a cada archivo
- Código adicional en cada handler

---

### **Opción 4: Symlink (Solo Linux/Mac)**

```bash
# Crear enlace simbólico en site-packages
ln -sf $(pwd)/shared/python/shared $(python -c "import site; print(site.getsitepackages()[0])")/shared
```

**✅ Ventajas:**
- Funciona como instalación real
- Cambios inmediatos

**❌ Desventajas:**
- Solo funciona en Linux/Mac
- Requiere permisos de administrador

---

## 🧪 Verificar la Instalación

Después de cualquier método, verifica que funciona:

```python
# Ejecutar en terminal Python
python -c "from shared.models import UserRole; print('✅ Import successful!')"
```

Si no hay errores, la configuración está correcta.

## 🔄 Uso en Servicios

Una vez configurado, puedes usar imports normales en tus handlers:

```python
# services/user/src/handlers/get_profile.py
from shared.models import UserRole, UserStatus
from shared.auth import require_auth
from shared.responses import APIResponse
from shared.exceptions import ValidationException
from shared.logger import lambda_logger
```

## 🐛 Troubleshooting

### **Error: "ModuleNotFoundError: No module named 'shared'"**

**Solución:**
1. Verifica que la instalación se completó correctamente
2. Si usas virtual environment, asegúrate de que esté activado
3. Prueba reinstalar: `pip uninstall agent-scl-shared && pip install -e shared/python`

### **Error: "ImportError: cannot import name 'X' from 'shared'"**

**Solución:**
1. Verifica que el módulo existe en `shared/python/shared/`
2. Revisa que el `__init__.py` exporta correctamente
3. Verifica la sintaxis del import

### **Los cambios en shared no se reflejan**

**Solución:**
- Con instalación editable: Los cambios deberían ser inmediatos
- Con PYTHONPATH: Reinicia el proceso Python
- Con sys.path: Los cambios son inmediatos

## 💡 Recomendación

**Usa la Opción 1 (Instalación Editable)** porque:
- Es la más profesional
- Simula exactamente AWS Layers
- Los cambios son inmediatos
- Fácil de gestionar
- Funciona en cualquier sistema operativo

```bash
# Una sola vez por proyecto
pip install -e shared/python
```

¡Y listo! Tus servicios podrán importar `shared` como si fuera una layer de AWS. 🎉
