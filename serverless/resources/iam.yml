# serverless/resources/iam.yml
# Configuración de IAM - Migrado desde Terraform
# Roles y políticas para Lambda functions y servicios AWS

Resources:
  # Rol de ejecución para Lambda functions
  IamRoleLambdaExecution:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ${self:custom.projectName}-${self:custom.stage}-lambda-execution-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess
      
      Policies:
        # Política para DynamoDB
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:BatchGetItem
                  - dynamodb:BatchWriteItem
                  - dynamodb:DescribeTable
                  - dynamodb:DescribeStream
                  - dynamodb:GetRecords
                  - dynamodb:GetShardIterator
                  - dynamodb:ListStreams
                Resource:
                  - Fn::Sub: "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${self:custom.projectName}-main-${self:custom.stage}"
                  - Fn::Sub: "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${self:custom.projectName}-main-${self:custom.stage}/index/*"
                  - Fn::Sub: "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${self:custom.projectName}-sessions-${self:custom.stage}"
                  - Fn::Sub: "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${self:custom.projectName}-sessions-${self:custom.stage}/index/*"
                  - Fn::Sub: "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${self:custom.projectName}-rate-limit-${self:custom.stage}"
                  - Fn::Sub: "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${self:custom.projectName}-rate-limit-${self:custom.stage}/stream/*"
        
        # Política para S3
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:GetObjectVersion
                  - s3:PutObjectAcl
                  - s3:GetObjectAcl
                  - s3:ListBucket
                  - s3:GetBucketLocation
                  - s3:GetBucketVersioning
                Resource:
                  - Fn::Sub: "arn:aws:s3:::${self:custom.projectName}-data-${self:custom.stage}"
                  - Fn::Sub: "arn:aws:s3:::${self:custom.projectName}-data-${self:custom.stage}/*"
                  - Fn::Sub: "arn:aws:s3:::${self:custom.projectName}-logs-${self:custom.stage}"
                  - Fn::Sub: "arn:aws:s3:::${self:custom.projectName}-logs-${self:custom.stage}/*"
                  - Fn::Sub: "arn:aws:s3:::${self:custom.projectName}-backups-${self:custom.stage}"
                  - Fn::Sub: "arn:aws:s3:::${self:custom.projectName}-backups-${self:custom.stage}/*"
        
        # Política para Secrets Manager
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource:
                  - Fn::Sub: "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${self:custom.projectName}/${self:custom.stage}/*"
        
        # Política para SES (envío de emails)
        - PolicyName: SESAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ses:SendEmail
                  - ses:SendRawEmail
                  - ses:SendTemplatedEmail
                  - ses:GetSendQuota
                  - ses:GetSendStatistics
                Resource: "*"
                Condition:
                  StringEquals:
                    "ses:FromAddress": 
                      - "<EMAIL>"
                      - "<EMAIL>"
        
        # Política para CloudWatch Logs
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogGroups
                  - logs:DescribeLogStreams
                Resource:
                  - Fn::Sub: "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${self:custom.projectName}-${self:custom.stage}-*"
        
        # Política para KMS
        - PolicyName: KMSAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                  - kms:Encrypt
                  - kms:GenerateDataKey
                  - kms:ReEncrypt*
                Resource:
                  - Fn::Sub: "arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*"
                Condition:
                  StringEquals:
                    "kms:ViaService":
                      - Fn::Sub: "dynamodb.${AWS::Region}.amazonaws.com"
                      - Fn::Sub: "s3.${AWS::Region}.amazonaws.com"
                      - Fn::Sub: "secretsmanager.${AWS::Region}.amazonaws.com"
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-lambda-execution-role
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Lambda execution role
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Rol para API Gateway CloudWatch Logs
  ApiGatewayCloudWatchLogsRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ${self:custom.projectName}-${self:custom.stage}-apigateway-cloudwatch-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - apigateway.amazonaws.com
            Action: sts:AssumeRole
      
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-apigateway-cloudwatch-role
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: API Gateway CloudWatch logs role
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Política personalizada para acceso cross-service
  CrossServiceAccessPolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: ${self:custom.projectName}-${self:custom.stage}-cross-service-access
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          # Permitir invocación entre Lambda functions
          - Effect: Allow
            Action:
              - lambda:InvokeFunction
            Resource:
              - Fn::Sub: "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:custom.projectName}-${self:custom.stage}-*"
          
          # Permitir publicación en SNS topics
          - Effect: Allow
            Action:
              - sns:Publish
              - sns:GetTopicAttributes
            Resource:
              - Fn::Sub: "arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.projectName}-${self:custom.stage}-*"
          
          # Permitir envío a SQS queues
          - Effect: Allow
            Action:
              - sqs:SendMessage
              - sqs:GetQueueAttributes
              - sqs:GetQueueUrl
            Resource:
              - Fn::Sub: "arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:${self:custom.projectName}-${self:custom.stage}-*"
      
      Roles:
        - Ref: IamRoleLambdaExecution

Outputs:
  LambdaExecutionRoleArn:
    Description: ARN of the Lambda execution role
    Value:
      Fn::GetAtt:
        - IamRoleLambdaExecution
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-LambdaExecutionRoleArn
  
  ApiGatewayCloudWatchLogsRoleArn:
    Description: ARN of the API Gateway CloudWatch logs role
    Value:
      Fn::GetAtt:
        - ApiGatewayCloudWatchLogsRole
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayCloudWatchLogsRoleArn
