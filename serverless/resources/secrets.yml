# serverless/resources/secrets.yml
# Configuración de AWS Secrets Manager - Migrado desde Terraform
# Gestión segura de secretos y configuraciones sensibles

Resources:
  # Secreto para JWT
  JWTSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/jwt
      Description: JWT signing keys and configuration for ${self:custom.projectName} ${self:custom.stage}
      
      GenerateSecretString:
        SecretStringTemplate: '{"algorithm": "HS256", "issuer": "agent-scl-platform", "audience": "agent-scl-users"}'
        GenerateStringKey: 'secret_key'
        PasswordLength: 64
        ExcludeCharacters: '"@/\'
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-jwt-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: JWT configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential

  # Secreto para base de datos
  DatabaseSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/database
      Description: Database configuration for ${self:custom.projectName} ${self:custom.stage}
      
      SecretString:
        Fn::Sub: |
          {
            "dynamodb_table": "${self:custom.projectName}-main-${self:custom.stage}",
            "sessions_table": "${self:custom.projectName}-sessions-${self:custom.stage}",
            "rate_limit_table": "${self:custom.projectName}-rate-limit-${self:custom.stage}",
            "region": "${AWS::Region}",
            "encryption_key": "alias/aws/dynamodb"
          }
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-database-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Database configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Internal

  # Secreto para Stripe
  StripeSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/stripe
      Description: Stripe payment configuration for ${self:custom.projectName} ${self:custom.stage}

      SecretString:
        Fn::Sub: |
          {
            "publishable_key": "${self:custom.sharedVars.integrations.stripe.publishableKey, 'pk_test_placeholder'}",
            "secret_key": "${self:custom.sharedVars.integrations.stripe.secretKey, 'sk_test_placeholder'}",
            "webhook_secret": "${self:custom.sharedVars.integrations.stripe.webhookSecret, 'whsec_placeholder'}",
            "api_version": "2023-10-16",
            "environment": "${self:custom.stage}"
          }
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-stripe-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Stripe payment configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential

  # Secreto para email/SES
  EmailSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/email
      Description: Email service configuration for ${self:custom.projectName} ${self:custom.stage}
      
      SecretString:
        Fn::Sub: |
          {
            "from_address": "<EMAIL>",
            "support_address": "<EMAIL>",
            "region": "${AWS::Region}",
            "configuration_set": "${self:custom.projectName}-${self:custom.stage}-config-set",
            "templates": {
              "welcome": "${self:custom.projectName}-${self:custom.stage}-welcome",
              "password_reset": "${self:custom.projectName}-${self:custom.stage}-password-reset",
              "email_verification": "${self:custom.projectName}-${self:custom.stage}-email-verification",
              "invitation": "${self:custom.projectName}-${self:custom.stage}-invitation"
            },
            "bounce_handling": {
              "enabled": true,
              "sns_topic": "${self:custom.projectName}-${self:custom.stage}-email-bounces"
            },
            "complaint_handling": {
              "enabled": true,
              "sns_topic": "${self:custom.projectName}-${self:custom.stage}-email-complaints"
            }
          }
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-email-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Email service configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Internal

  # Secreto para n8n
  N8nSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/n8n
      Description: n8n integration configuration for ${self:custom.projectName} ${self:custom.stage}

      SecretString:
        Fn::Sub: |
          {
            "base_url": "${self:custom.sharedVars.integrations.n8n.baseUrl, ''}",
            "api_key": "${self:custom.sharedVars.integrations.n8n.apiKey, ''}",
            "webhook_secret": "${self:custom.sharedVars.integrations.n8n.webhookSecret, ''}",
            "environment": "${self:custom.stage}"
          }

      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-n8n-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: n8n integration configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential

  # Secreto para PayU
  PayUSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/payu
      Description: PayU payment configuration for ${self:custom.projectName} ${self:custom.stage}

      SecretString:
        Fn::Sub: |
          {
            "merchant_id": "${self:custom.sharedVars.integrations.payU.merchantId, ''}",
            "account_id": "${self:custom.sharedVars.integrations.payU.accountId, ''}",
            "api_key": "${self:custom.sharedVars.integrations.payU.apiKey, ''}",
            "api_login": "${self:custom.sharedVars.integrations.payU.apiLogin, ''}",
            "environment": "${self:custom.stage}",
            "test_mode": true
          }

      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-payu-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: PayU payment configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential

  # Secreto para integraciones externas
  IntegrationsSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/integrations
      Description: External integrations configuration for ${self:custom.projectName} ${self:custom.stage}

      SecretString:
        Fn::Sub: |
          {
            "openai": {
              "api_key": "",
              "organization": "",
              "model": "gpt-4"
            },
            "anthropic": {
              "api_key": "",
              "model": "claude-3-sonnet-20240229"
            },
            "webhook_urls": {
              "slack": "",
              "discord": ""
            }
          }
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-integrations-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: External integrations configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential

  # Secreto para configuración de aplicación
  ApplicationSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.projectName}/${self:custom.stage}/application
      Description: Application configuration for ${self:custom.projectName} ${self:custom.stage}
      
      SecretString:
        Fn::Sub: |
          {
            "encryption_key": "app-encryption-key-${self:custom.stage}",
            "session_timeout": 3600,
            "rate_limits": {
              "login_attempts": 5,
              "api_requests_per_minute": 100,
              "api_requests_per_hour": 1000
            },
            "features": {
              "email_verification_required": true,
              "multi_factor_auth": false,
              "audit_logging": true
            },
            "cors": {
              "allowed_origins": [
                "https://app.agentscl.com",
                "https://admin.agentscl.com"
              ],
              "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
              "allowed_headers": ["Content-Type", "Authorization", "X-Requested-With"]
            }
          }
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-application-secret
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Application configuration
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Internal

  # KMS Key para encriptación adicional
  ApplicationKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: KMS key for ${self:custom.projectName} ${self:custom.stage} application encryption
      KeyPolicy:
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS:
                Fn::Sub: "arn:aws:iam::${AWS::AccountId}:root"
            Action: "kms:*"
            Resource: "*"
          
          - Sid: Allow Lambda Functions
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - IamRoleLambdaExecution
                  - Arn
            Action:
              - "kms:Decrypt"
              - "kms:DescribeKey"
              - "kms:Encrypt"
              - "kms:GenerateDataKey"
              - "kms:ReEncrypt*"
            Resource: "*"
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-app-key
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Application encryption
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Alias para KMS Key
  ApplicationKMSKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: alias/${self:custom.projectName}-${self:custom.stage}-app
      TargetKeyId:
        Ref: ApplicationKMSKey

Outputs:
  JWTSecretArn:
    Description: ARN of the JWT secret
    Value:
      Ref: JWTSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-JWTSecretArn
  
  DatabaseSecretArn:
    Description: ARN of the database secret
    Value:
      Ref: DatabaseSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-DatabaseSecretArn
  
  StripeSecretArn:
    Description: ARN of the Stripe secret
    Value:
      Ref: StripeSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-StripeSecretArn
  
  ApplicationKMSKeyId:
    Description: ID of the application KMS key
    Value:
      Ref: ApplicationKMSKey
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ApplicationKMSKeyId
  
  ApplicationKMSKeyArn:
    Description: ARN of the application KMS key
    Value:
      Fn::GetAtt:
        - ApplicationKMSKey
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ApplicationKMSKeyArn

  ApplicationSecretArn:
    Description: ARN of the application secret
    Value:
      Ref: ApplicationSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ApplicationSecretArn

  EmailSecretArn:
    Description: ARN of the email secret
    Value:
      Ref: EmailSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-EmailSecretArn

  N8nSecretArn:
    Description: ARN of the n8n secret
    Value:
      Ref: N8nSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-N8nSecretArn

  PayUSecretArn:
    Description: ARN of the PayU secret
    Value:
      Ref: PayUSecret
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PayUSecretArn
