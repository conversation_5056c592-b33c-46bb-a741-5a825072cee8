# serverless/resources/messaging.yml
# SNS/SQS configuration for event-driven architecture

Resources:
  # SNS Topics for domain events
  TenantEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-tenant-events
      DisplayName: Tenant Domain Events
      KmsMasterKeyId: alias/aws/sns
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-tenant-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Tenant domain events
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  UserEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-user-events
      DisplayName: User Domain Events
      KmsMasterKeyId: alias/aws/sns
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-user-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: User domain events
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  PaymentEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-payment-events
      DisplayName: Payment Domain Events
      KmsMasterKeyId: alias/aws/sns
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-payment-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Payment domain events
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  SecurityEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-security-events
      DisplayName: Security Domain Events
      KmsMasterKeyId: alias/aws/sns
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-security-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Security domain events
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # SQS Queues for event processing
  TenantEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-tenant-events
      VisibilityTimeoutSeconds: 300
      MessageRetentionPeriod: 1209600  # 14 days
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt:
            - TenantEventsDeadLetterQueue
            - Arn
        maxReceiveCount: 3
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-tenant-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Tenant event processing
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  TenantEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-tenant-events-dlq
      MessageRetentionPeriod: 1209600  # 14 days
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-tenant-events-dlq
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Tenant events dead letter queue
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  UserEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-user-events
      VisibilityTimeoutSeconds: 180
      MessageRetentionPeriod: 1209600  # 14 days
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt:
            - UserEventsDeadLetterQueue
            - Arn
        maxReceiveCount: 3
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-user-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: User event processing
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  UserEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-user-events-dlq
      MessageRetentionPeriod: 1209600  # 14 days
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-user-events-dlq
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: User events dead letter queue
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  PaymentEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-payment-events
      VisibilityTimeoutSeconds: 120
      MessageRetentionPeriod: 86400  # 1 day
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt:
            - PaymentEventsDeadLetterQueue
            - Arn
        maxReceiveCount: 5
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-payment-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Payment event processing
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  PaymentEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-payment-events-dlq
      MessageRetentionPeriod: 1209600  # 14 days
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-payment-events-dlq
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Payment events dead letter queue
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  SecurityEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-security-events
      VisibilityTimeoutSeconds: 60
      MessageRetentionPeriod: 43200  # 12 hours
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt:
            - SecurityEventsDeadLetterQueue
            - Arn
        maxReceiveCount: 3
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-security-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Security event processing
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  SecurityEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:custom.projectName}-${self:custom.stage}-security-events-dlq
      MessageRetentionPeriod: 1209600  # 14 days
      KmsMasterKeyId: alias/aws/sqs
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-security-events-dlq
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Security events dead letter queue
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # SNS Subscriptions to SQS Queues
  TenantEventsSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn:
        Ref: TenantEventsTopic
      Protocol: sqs
      Endpoint:
        Fn::GetAtt:
          - TenantEventsQueue
          - Arn
      FilterPolicy:
        event_type:
          - tenant.created
          - tenant.updated
          - tenant.deleted
          - tenant.activated
          - tenant.suspended

  UserEventsSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn:
        Ref: UserEventsTopic
      Protocol: sqs
      Endpoint:
        Fn::GetAtt:
          - UserEventsQueue
          - Arn
      FilterPolicy:
        event_type:
          - user.created
          - user.updated
          - user.deleted
          - user.activated
          - user.deactivated
          - user.invited
          - user.login

  PaymentEventsSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn:
        Ref: PaymentEventsTopic
      Protocol: sqs
      Endpoint:
        Fn::GetAtt:
          - PaymentEventsQueue
          - Arn
      FilterPolicy:
        event_type:
          - payment.succeeded
          - payment.failed
          - subscription.created
          - subscription.updated
          - subscription.cancelled
          - invoice.created
          - invoice.paid

  SecurityEventsSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn:
        Ref: SecurityEventsTopic
      Protocol: sqs
      Endpoint:
        Fn::GetAtt:
          - SecurityEventsQueue
          - Arn
      FilterPolicy:
        event_type:
          - security.login_failed
          - security.account_locked
          - security.suspicious_activity
          - security.permission_denied
          - security.data_access

  # Queue Policies to allow SNS to send messages
  TenantEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: TenantEventsQueue
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: sns.amazonaws.com
            Action: sqs:SendMessage
            Resource:
              Fn::GetAtt:
                - TenantEventsQueue
                - Arn
            Condition:
              ArnEquals:
                aws:SourceArn:
                  Ref: TenantEventsTopic

  UserEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: UserEventsQueue
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: sns.amazonaws.com
            Action: sqs:SendMessage
            Resource:
              Fn::GetAtt:
                - UserEventsQueue
                - Arn
            Condition:
              ArnEquals:
                aws:SourceArn:
                  Ref: UserEventsTopic

  PaymentEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: PaymentEventsQueue
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: sns.amazonaws.com
            Action: sqs:SendMessage
            Resource:
              Fn::GetAtt:
                - PaymentEventsQueue
                - Arn
            Condition:
              ArnEquals:
                aws:SourceArn:
                  Ref: PaymentEventsTopic

  SecurityEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: SecurityEventsQueue
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: sns.amazonaws.com
            Action: sqs:SendMessage
            Resource:
              Fn::GetAtt:
                - SecurityEventsQueue
                - Arn
            Condition:
              ArnEquals:
                aws:SourceArn:
                  Ref: SecurityEventsTopic

Outputs:
  TenantEventsTopicArn:
    Description: ARN of the tenant events topic
    Value:
      Ref: TenantEventsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-TenantEventsTopicArn

  UserEventsTopicArn:
    Description: ARN of the user events topic
    Value:
      Ref: UserEventsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-UserEventsTopicArn

  PaymentEventsTopicArn:
    Description: ARN of the payment events topic
    Value:
      Ref: PaymentEventsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PaymentEventsTopicArn

  SecurityEventsTopicArn:
    Description: ARN of the security events topic
    Value:
      Ref: SecurityEventsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-SecurityEventsTopicArn

  TenantEventsQueueArn:
    Description: ARN of the tenant events queue
    Value:
      Fn::GetAtt:
        - TenantEventsQueue
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-TenantEventsQueueArn

  UserEventsQueueArn:
    Description: ARN of the user events queue
    Value:
      Fn::GetAtt:
        - UserEventsQueue
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-UserEventsQueueArn

  PaymentEventsQueueArn:
    Description: ARN of the payment events queue
    Value:
      Fn::GetAtt:
        - PaymentEventsQueue
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PaymentEventsQueueArn

  SecurityEventsQueueArn:
    Description: ARN of the security events queue
    Value:
      Fn::GetAtt:
        - SecurityEventsQueue
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-SecurityEventsQueueArn
