# serverless/resources/ses.yml
# AWS SES Configuration for Email Services
# Implementado según "Integration Specifications" y "Security Guidelines"

Resources:
  # Domain Identity for SES
  SESEmailDomain:
    Type: AWS::SES::EmailIdentity
    Properties:
      EmailIdentity: agentscl.com
      DkimSigningAttributes:
        NextSigningKeyLength: RSA_2048_BIT
      MailFromAttributes:
        MailFromDomain: mail.agentscl.com
        BehaviorOnMxFailure: UseDefaultValue
      FeedbackAttributes:
        EmailForwardingEnabled: false
      Tags:
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Service
          Value: ${self:custom.projectName}
        - Key: Purpose
          Value: Email-Domain-Identity

  # Email Identity for noreply address
  SESNoReplyEmailIdentity:
    Type: AWS::SES::EmailIdentity
    Properties:
      EmailIdentity: <EMAIL>
      Tags:
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Service
          Value: ${self:custom.projectName}
        - Key: Purpose
          Value: NoReply-Email-Identity

  # Email Identity for support address
  SESSupportEmailIdentity:
    Type: AWS::SES::EmailIdentity
    Properties:
      EmailIdentity: <EMAIL>
      Tags:
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Service
          Value: ${self:custom.projectName}
        - Key: Purpose
          Value: Support-Email-Identity

  # Configuration Set for tracking and analytics
  SESConfigurationSet:
    Type: AWS::SES::ConfigurationSet
    Properties:
      Name: ${self:custom.projectName}-${self:custom.stage}-config-set
      TrackingOptions:
        CustomRedirectDomain: agentscl.com
      Tags:
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Service
          Value: ${self:custom.projectName}

  # Event Destination for bounce and complaint handling
  SESEventDestination:
    Type: AWS::SES::ConfigurationSetEventDestination
    Properties:
      ConfigurationSetName: !Ref SESConfigurationSet
      EventDestination:
        Name: ${self:custom.projectName}-${self:custom.stage}-events
        Enabled: true
        MatchingEventTypes:
          - bounce
          - complaint
          - delivery
          - send
          - reject
          - open
          - click
        CloudWatchDestination:
          DefaultDimensionValue: ${self:custom.projectName}-${self:custom.stage}
          DimensionConfigurations:
            - DimensionName: MessageTag
              DimensionValueSource: messageTag
              DefaultDimensionValue: default
            - DimensionName: EmailAddress
              DimensionValueSource: emailHeader
              DefaultDimensionValue: unknown

  # Email Template for Email Verification
  SESEmailVerificationTemplate:
    Type: AWS::SES::Template
    Properties:
      Template:
        TemplateName: ${self:custom.projectName}-${self:custom.stage}-email-verification
        SubjectPart: "Verify your email for {{company_name}}"
        HtmlPart: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Email Verification</title>
              <style>
                  body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                  .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                  .header { background: #3498db; color: white; padding: 20px; text-align: center; }
                  .content { padding: 30px; background: #f9f9f9; }
                  .button { background: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; }
                  .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1>Email Verification</h1>
                  </div>
                  <div class="content">
                      <h2>Hi {{user_name}},</h2>
                      <p>Welcome to {{company_name}}! Please verify your email address to complete your registration.</p>
                      <p>Your verification code is: <strong>{{verification_code}}</strong></p>
                      <p>Or click the button below to verify automatically:</p>
                      <p><a href="{{verification_url}}" class="button">Verify Email</a></p>
                      <p>This verification link will expire in 24 hours.</p>
                      <p>If you didn't create this account, please ignore this email.</p>
                  </div>
                  <div class="footer">
                      <p>Best regards,<br>The {{company_name}} Team</p>
                  </div>
              </div>
          </body>
          </html>
        TextPart: |
          Hi {{user_name}},
          
          Welcome to {{company_name}}! Please verify your email address to complete your registration.
          
          Your verification code is: {{verification_code}}
          
          Or visit this link to verify: {{verification_url}}
          
          This verification link will expire in 24 hours.
          
          If you didn't create this account, please ignore this email.
          
          Best regards,
          The {{company_name}} Team

  # Email Template for Password Reset
  SESPasswordResetTemplate:
    Type: AWS::SES::Template
    Properties:
      Template:
        TemplateName: ${self:custom.projectName}-${self:custom.stage}-password-reset
        SubjectPart: "Reset your password for {{company_name}}"
        HtmlPart: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Password Reset</title>
              <style>
                  body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                  .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                  .header { background: #e74c3c; color: white; padding: 20px; text-align: center; }
                  .content { padding: 30px; background: #f9f9f9; }
                  .button { background: #e74c3c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; }
                  .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1>Password Reset</h1>
                  </div>
                  <div class="content">
                      <h2>Hi {{user_name}},</h2>
                      <p>We received a request to reset your password for your {{company_name}} account.</p>
                      <p>Click the button below to reset your password:</p>
                      <p><a href="{{reset_url}}" class="button">Reset Password</a></p>
                      <p>This link will expire in 1 hour for security reasons.</p>
                      <p>If you didn't request this password reset, please ignore this email or contact support if you have concerns.</p>
                  </div>
                  <div class="footer">
                      <p>Best regards,<br>The {{company_name}} Team</p>
                  </div>
              </div>
          </body>
          </html>
        TextPart: |
          Hi {{user_name}},
          
          We received a request to reset your password for your {{company_name}} account.
          
          Visit this link to reset your password: {{reset_url}}
          
          This link will expire in 1 hour for security reasons.
          
          If you didn't request this password reset, please ignore this email or contact support if you have concerns.
          
          Best regards,
          The {{company_name}} Team

  # Email Template for Welcome Email
  SESWelcomeTemplate:
    Type: AWS::SES::Template
    Properties:
      Template:
        TemplateName: ${self:custom.projectName}-${self:custom.stage}-welcome
        SubjectPart: "Welcome to {{company_name}}, {{user_name}}!"
        HtmlPart: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Welcome</title>
              <style>
                  body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                  .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                  .header { background: #27ae60; color: white; padding: 20px; text-align: center; }
                  .content { padding: 30px; background: #f9f9f9; }
                  .button { background: #27ae60; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; }
                  .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1>Welcome to {{company_name}}!</h1>
                  </div>
                  <div class="content">
                      <h2>Hi {{user_name}},</h2>
                      <p>Welcome to {{company_name}}! We're excited to have you on board.</p>
                      <p>Your account has been successfully created and verified. You can now access all features of our platform.</p>
                      <p><a href="{{login_url}}" class="button">Get Started</a></p>
                      <p>If you have any questions, feel free to reach out to our support team.</p>
                  </div>
                  <div class="footer">
                      <p>Best regards,<br>The {{company_name}} Team</p>
                  </div>
              </div>
          </body>
          </html>
        TextPart: |
          Hi {{user_name}},
          
          Welcome to {{company_name}}! We're excited to have you on board.
          
          Your account has been successfully created and verified. You can now access all features of our platform.
          
          Get started: {{login_url}}
          
          If you have any questions, feel free to reach out to our support team.
          
          Best regards,
          The {{company_name}} Team

  # Email Template for User Invitation
  SESInvitationTemplate:
    Type: AWS::SES::Template
    Properties:
      Template:
        TemplateName: ${self:custom.projectName}-${self:custom.stage}-invitation
        SubjectPart: "You're invited to join {{company_name}}"
        HtmlPart: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Invitation</title>
              <style>
                  body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                  .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                  .header { background: #9b59b6; color: white; padding: 20px; text-align: center; }
                  .content { padding: 30px; background: #f9f9f9; }
                  .button { background: #9b59b6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; }
                  .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1>You're Invited!</h1>
                  </div>
                  <div class="content">
                      <h2>Hi {{user_name}},</h2>
                      <p>{{inviter_name}} has invited you to join {{company_name}}.</p>
                      <p>Click the button below to accept the invitation and create your account:</p>
                      <p><a href="{{invitation_url}}" class="button">Accept Invitation</a></p>
                      <p>This invitation will expire in 7 days.</p>
                      <p>If you have any questions, feel free to reach out to our support team.</p>
                  </div>
                  <div class="footer">
                      <p>Best regards,<br>The {{company_name}} Team</p>
                  </div>
              </div>
          </body>
          </html>
        TextPart: |
          Hi {{user_name}},

          {{inviter_name}} has invited you to join {{company_name}}.

          Visit this link to accept the invitation and create your account: {{invitation_url}}

          This invitation will expire in 7 days.

          If you have any questions, feel free to reach out to our support team.

          Best regards,
          The {{company_name}} Team

Outputs:
  SESConfigurationSetName:
    Description: "SES Configuration Set Name"
    Value: !Ref SESConfigurationSet
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ses-config-set

  SESEmailDomain:
    Description: "SES Email Domain"
    Value: !Ref SESEmailDomain
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ses-domain

  SESEmailVerificationTemplate:
    Description: "SES Email Verification Template"
    Value: !Ref SESEmailVerificationTemplate
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-email-verification-template

  SESPasswordResetTemplate:
    Description: "SES Password Reset Template"
    Value: !Ref SESPasswordResetTemplate
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-password-reset-template

  SESWelcomeTemplate:
    Description: "SES Welcome Template"
    Value: !Ref SESWelcomeTemplate
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-welcome-template

  SESInvitationTemplate:
    Description: "SES Invitation Template"
    Value: !Ref SESInvitationTemplate
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-invitation-template
