# serverless/resources/vpc.yml
# Configuración de VPC - Migrado desde Terraform
# Red privada virtual para aislamiento y seguridad

Resources:
  # VPC principal
  PlatformVPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-vpc
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Platform VPC
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Internet Gateway
  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-igw
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Internet Gateway
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Attachment del Internet Gateway
  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId:
        Ref: InternetGateway
      VpcId:
        Ref: PlatformVPC

  # Subnet pública 1 (AZ a)
  PublicSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId:
        Ref: PlatformVPC
      AvailabilityZone:
        Fn::Select:
          - 0
          - Fn::GetAZs: ''
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-public-subnet-1
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Public
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Subnet pública 2 (AZ b)
  PublicSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId:
        Ref: PlatformVPC
      AvailabilityZone:
        Fn::Select:
          - 1
          - Fn::GetAZs: ''
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-public-subnet-2
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Public
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Subnet privada 1 (AZ a)
  PrivateSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId:
        Ref: PlatformVPC
      AvailabilityZone:
        Fn::Select:
          - 0
          - Fn::GetAZs: ''
      CidrBlock: *********/24
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-private-subnet-1
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Private
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Subnet privada 2 (AZ b)
  PrivateSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId:
        Ref: PlatformVPC
      AvailabilityZone:
        Fn::Select:
          - 1
          - Fn::GetAZs: ''
      CidrBlock: *********/24
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-private-subnet-2
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Private
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # NAT Gateway EIP 1
  NatGateway1EIP:
    Type: AWS::EC2::EIP
    DependsOn: InternetGatewayAttachment
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-nat-eip-1
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: NAT Gateway EIP
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # NAT Gateway EIP 2
  NatGateway2EIP:
    Type: AWS::EC2::EIP
    DependsOn: InternetGatewayAttachment
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-nat-eip-2
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: NAT Gateway EIP
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # NAT Gateway 1
  NatGateway1:
    Type: AWS::EC2::NatGateway
    Properties:
      AllocationId:
        Fn::GetAtt:
          - NatGateway1EIP
          - AllocationId
      SubnetId:
        Ref: PublicSubnet1
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-nat-gateway-1
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: NAT Gateway
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # NAT Gateway 2
  NatGateway2:
    Type: AWS::EC2::NatGateway
    Properties:
      AllocationId:
        Fn::GetAtt:
          - NatGateway2EIP
          - AllocationId
      SubnetId:
        Ref: PublicSubnet2
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-nat-gateway-2
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: NAT Gateway
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Route Table pública
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId:
        Ref: PlatformVPC
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-public-routes
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Public
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Ruta por defecto pública
  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId:
        Ref: PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId:
        Ref: InternetGateway

  # Asociación de subnet pública 1
  PublicSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId:
        Ref: PublicRouteTable
      SubnetId:
        Ref: PublicSubnet1

  # Asociación de subnet pública 2
  PublicSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId:
        Ref: PublicRouteTable
      SubnetId:
        Ref: PublicSubnet2

  # Route Table privada 1
  PrivateRouteTable1:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId:
        Ref: PlatformVPC
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-private-routes-1
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Private
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Ruta por defecto privada 1
  DefaultPrivateRoute1:
    Type: AWS::EC2::Route
    Properties:
      RouteTableId:
        Ref: PrivateRouteTable1
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId:
        Ref: NatGateway1

  # Asociación de subnet privada 1
  PrivateSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId:
        Ref: PrivateRouteTable1
      SubnetId:
        Ref: PrivateSubnet1

  # Route Table privada 2
  PrivateRouteTable2:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId:
        Ref: PlatformVPC
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-private-routes-2
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Type
          Value: Private
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Ruta por defecto privada 2
  DefaultPrivateRoute2:
    Type: AWS::EC2::Route
    Properties:
      RouteTableId:
        Ref: PrivateRouteTable2
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId:
        Ref: NatGateway2

  # Asociación de subnet privada 2
  PrivateSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId:
        Ref: PrivateRouteTable2
      SubnetId:
        Ref: PrivateSubnet2

  # Security Group para Lambda functions
  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: ${self:custom.projectName}-${self:custom.stage}-lambda-sg
      GroupDescription: Security group for Lambda functions
      VpcId:
        Ref: PlatformVPC

      SecurityGroupIngress:
        # Allow HTTPS traffic from within VPC
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 10.0.0.0/16
          Description: HTTPS from VPC
        # Allow HTTP traffic from within VPC (for internal services)
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 10.0.0.0/16
          Description: HTTP from VPC

      SecurityGroupEgress:
        # Allow HTTPS outbound (for AWS services)
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: HTTPS outbound for AWS services
        # Allow DNS outbound
        - IpProtocol: tcp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
          Description: DNS TCP outbound
        - IpProtocol: udp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
          Description: DNS UDP outbound
        # Allow NTP outbound
        - IpProtocol: udp
          FromPort: 123
          ToPort: 123
          CidrIp: 0.0.0.0/0
          Description: NTP outbound

      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-lambda-sg
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Lambda Security Group
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Security Group para API Gateway VPC Endpoint
  ApiGatewaySecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: ${self:custom.projectName}-${self:custom.stage}-api-gateway-sg
      GroupDescription: Security group for API Gateway VPC endpoint
      VpcId:
        Ref: PlatformVPC

      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 10.0.0.0/16
          Description: HTTPS from VPC

      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: Allow all outbound traffic

      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-api-gateway-sg
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: API Gateway Security Group
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

Outputs:
  VPC:
    Description: VPC ID
    Value:
      Ref: PlatformVPC
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-VPC
  
  PublicSubnets:
    Description: List of public subnet IDs
    Value:
      Fn::Join:
        - ','
        - - Ref: PublicSubnet1
          - Ref: PublicSubnet2
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PublicSubnets
  
  PrivateSubnets:
    Description: List of private subnet IDs
    Value:
      Fn::Join:
        - ','
        - - Ref: PrivateSubnet1
          - Ref: PrivateSubnet2
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnets
  
  PublicSubnet1:
    Description: Public subnet 1 ID
    Value:
      Ref: PublicSubnet1
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PublicSubnet1
  
  PublicSubnet2:
    Description: Public subnet 2 ID
    Value:
      Ref: PublicSubnet2
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PublicSubnet2
  
  PrivateSubnet1:
    Description: Private subnet 1 ID
    Value:
      Ref: PrivateSubnet1
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnet1
  
  PrivateSubnet2:
    Description: Private subnet 2 ID
    Value:
      Ref: PrivateSubnet2
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnet2

  PrivateSubnetId1:
    Description: Private subnet 1 ID (alias for compatibility)
    Value:
      Ref: PrivateSubnet1
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnetId1

  PrivateSubnetId2:
    Description: Private subnet 2 ID (alias for compatibility)
    Value:
      Ref: PrivateSubnet2
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnetId2

  LambdaSecurityGroupId:
    Description: Lambda security group ID
    Value:
      Ref: LambdaSecurityGroup
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-LambdaSecurityGroupId

  ApiGatewaySecurityGroupId:
    Description: API Gateway security group ID
    Value:
      Ref: ApiGatewaySecurityGroup
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewaySecurityGroupId

  # VPC Endpoints para servicios AWS críticos
  DynamoDBVPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcId:
        Ref: PlatformVPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.dynamodb'
      VpcEndpointType: Gateway
      RouteTableIds:
        - Ref: PrivateRouteTable1
        - Ref: PrivateRouteTable2
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal: '*'
            Action:
              - dynamodb:Query
              - dynamodb:GetItem
              - dynamodb:PutItem
              - dynamodb:UpdateItem
              - dynamodb:DeleteItem
              - dynamodb:BatchGetItem
              - dynamodb:BatchWriteItem
              - dynamodb:Scan
            Resource: '*'

  S3VPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcId:
        Ref: PlatformVPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.s3'
      VpcEndpointType: Gateway
      RouteTableIds:
        - Ref: PrivateRouteTable1
        - Ref: PrivateRouteTable2
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal: '*'
            Action:
              - s3:GetObject
              - s3:PutObject
              - s3:DeleteObject
              - s3:ListBucket
            Resource: '*'

  SecretsManagerVPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcId:
        Ref: PlatformVPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.secretsmanager'
      VpcEndpointType: Interface
      SubnetIds:
        - Ref: PrivateSubnet1
        - Ref: PrivateSubnet2
      SecurityGroupIds:
        - Ref: LambdaSecurityGroup
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal: '*'
            Action:
              - secretsmanager:GetSecretValue
              - secretsmanager:DescribeSecret
            Resource: '*'

  SESVPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcId:
        Ref: PlatformVPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.email-smtp'
      VpcEndpointType: Interface
      SubnetIds:
        - Ref: PrivateSubnet1
        - Ref: PrivateSubnet2
      SecurityGroupIds:
        - Ref: LambdaSecurityGroup
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal: '*'
            Action:
              - ses:SendEmail
              - ses:SendRawEmail
            Resource: '*'
