# serverless/resources/monitoring.yml
# Configuración de monitoreo - Migrado desde Terraform
# CloudWatch, SNS, y alertas para monitoreo completo

Resources:
  # SNS Topic para alertas
  AlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-alerts
      DisplayName: ${self:custom.projectName} ${self:custom.stage} Alerts
      
      # Configuración de encriptación
      KmsMasterKeyId: alias/aws/sns
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-alerts
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: System alerts and notifications
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Suscripción de email para alertas
  AlertsEmailSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn:
        Ref: AlertsTopic
      Protocol: email
      Endpoint: ${env:ALERTS_EMAIL, '<EMAIL>'}

  # SNS Topic para eventos de usuario
  UserEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-user-events
      DisplayName: ${self:custom.projectName} ${self:custom.stage} User Events
      KmsMasterKeyId: alias/aws/sns
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-user-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: User Events Notifications
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # SNS Topic para eventos de seguridad
  SecurityEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: ${self:custom.projectName}-${self:custom.stage}-security-events
      DisplayName: ${self:custom.projectName} ${self:custom.stage} Security Events
      KmsMasterKeyId: alias/aws/sns
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-security-events
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Security Events Notifications
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Health Check Alarm
  HealthCheckAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:service}-${self:custom.stage}-health-check-failure
      AlarmDescription: Health check endpoint failure
      MetricName: 4XXError
      Namespace: AWS/ApiGateway
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      Dimensions:
        - Name: ApiName
          Value: ${self:service}-${self:custom.stage}

  # Response Time Alarm
  ResponseTimeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:service}-${self:custom.stage}-high-response-time
      AlarmDescription: High API response time
      MetricName: Latency
      Namespace: AWS/ApiGateway
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      Threshold: 5000
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      Dimensions:
        - Name: ApiName
          Value: ${self:service}-${self:custom.stage}

  # CloudWatch Dashboard for Business Metrics
  BusinessMetricsDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: ${self:custom.projectName}-${self:custom.stage}-business-metrics
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AgentSCL/${self:custom.stage}", "TenantCreated", "MetricType", "business" ],
                  [ ".", "UserRegistered", ".", "." ],
                  [ ".", "SubscriptionCreated", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Business Growth Metrics",
                "period": 300,
                "stat": "Sum"
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AgentSCL/${self:custom.stage}", "PaymentProcessed", "MetricType", "business", "Status", "success" ],
                  [ "...", "failed" ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Payment Processing",
                "period": 300,
                "stat": "Sum"
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 6,
              "width": 24,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AgentSCL/${self:custom.stage}", "APICall", "MetricType", "business" ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "API Usage",
                "period": 300,
                "stat": "Sum"
              }
            }
          ]
        }

  # CloudWatch Dashboard for Performance Metrics
  PerformanceMetricsDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: ${self:custom.projectName}-${self:custom.stage}-performance-metrics
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AgentSCL/${self:custom.stage}", "FunctionDuration", "MetricType", "performance" ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Function Performance",
                "period": 300,
                "stat": "Average"
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/Lambda", "Duration", "FunctionName", "${self:custom.projectName}-${self:custom.stage}-auth-login" ],
                  [ "...", "${self:custom.projectName}-${self:custom.stage}-tenant-getProfile" ],
                  [ "...", "${self:custom.projectName}-${self:custom.stage}-payment-createSubscription" ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Lambda Duration",
                "period": 300,
                "stat": "Average"
              }
            }
          ]
        }

  # Alarma para errores de Lambda
  LambdaErrorsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-lambda-errors
      AlarmDescription: Lambda function errors
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      OKActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching
      
      Dimensions:
        - Name: FunctionName
          Value: ${self:custom.projectName}-${self:custom.stage}-auth-login

  # Alarma para duración de Lambda
  LambdaDurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-lambda-duration
      AlarmDescription: Lambda function duration too high
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      Threshold: 10000  # 10 seconds
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching

  # Alarma para throttling de Lambda
  LambdaThrottlesAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-lambda-throttles
      AlarmDescription: Lambda function throttles
      MetricName: Throttles
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching

  # Alarma para errores 4xx de API Gateway
  ApiGateway4xxAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-api-4xx-errors
      AlarmDescription: API Gateway 4xx errors
      MetricName: 4XXError
      Namespace: AWS/ApiGateway
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 20
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching
      
      Dimensions:
        - Name: ApiName
          Value: ${self:custom.projectName}-${self:custom.stage}

  # Alarma para errores 5xx de API Gateway
  ApiGateway5xxAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-api-5xx-errors
      AlarmDescription: API Gateway 5xx errors
      MetricName: 5XXError
      Namespace: AWS/ApiGateway
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching
      
      Dimensions:
        - Name: ApiName
          Value: ${self:custom.projectName}-${self:custom.stage}

  # Alarma para latencia de API Gateway
  ApiGatewayLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-api-latency
      AlarmDescription: API Gateway latency too high
      MetricName: Latency
      Namespace: AWS/ApiGateway
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      Threshold: 5000  # 5 seconds
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching
      
      Dimensions:
        - Name: ApiName
          Value: ${self:custom.projectName}-${self:custom.stage}

  # Alarma para errores de DynamoDB
  DynamoDBErrorsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-dynamodb-errors
      AlarmDescription: DynamoDB errors
      MetricName: SystemErrors
      Namespace: AWS/DynamoDB
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching
      
      Dimensions:
        - Name: TableName
          Value: ${self:custom.projectName}-main-${self:custom.stage}

  # Log Group para aplicación
  ApplicationLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /aws/lambda/${self:custom.projectName}-${self:custom.stage}
      RetentionInDays: 30
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-${self:custom.stage}-logs
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Application logs
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Alarma para intentos de login fallidos
  FailedLoginAttemptsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-failed-logins
      AlarmDescription: High number of failed login attempts
      MetricName: LoginAttempt
      Namespace: AgentSCL/${self:custom.stage}
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 50
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: Success
          Value: "false"
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching

  # Alarma para alta latencia en base de datos
  DatabaseHighLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-database-high-latency
      AlarmDescription: Database queries taking too long
      MetricName: DatabaseQueryDuration
      Namespace: AgentSCL/${self:custom.stage}
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 1000
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching

  # Alarma para errores de función
  FunctionErrorsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: ${self:custom.projectName}-${self:custom.stage}-function-errors
      AlarmDescription: High number of function errors
      MetricName: FunctionError
      Namespace: AgentSCL/${self:custom.stage}
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: AlertsTopic
      TreatMissingData: notBreaching

Outputs:
  AlertsTopicArn:
    Description: ARN of the alerts SNS topic
    Value:
      Ref: AlertsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-AlertsTopicArn
  
  BusinessMetricsDashboardURL:
    Description: URL of the business metrics dashboard
    Value:
      Fn::Sub: "https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${self:custom.projectName}-${self:custom.stage}-business-metrics"
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-BusinessMetricsDashboardURL

  PerformanceMetricsDashboardURL:
    Description: URL of the performance metrics dashboard
    Value:
      Fn::Sub: "https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${self:custom.projectName}-${self:custom.stage}-performance-metrics"
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PerformanceMetricsDashboardURL
