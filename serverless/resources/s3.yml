# serverless/resources/s3.yml
# Configuración de S3 - Migrado desde Terraform
# Buckets para almacenamiento de datos de la plataforma

Resources:
  # Bucket principal para datos de la plataforma
  PlatformDataBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:custom.projectName}-data-${self:custom.stage}
      
      # Configuración de versionado
      VersioningConfiguration:
        Status: Enabled
      
      # Configuración de encriptación
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
            BucketKeyEnabled: true
      
      # Configuración de acceso público
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      
      # Configuración de lifecycle
      LifecycleConfiguration:
        Rules:
          - Id: DeleteIncompleteMultipartUploads
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 7
          
          - Id: TransitionToIA
            Status: Enabled
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 90
                StorageClass: GLACIER
              - TransitionInDays: 365
                StorageClass: DEEP_ARCHIVE
          
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionTransitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 90
                StorageClass: GLACIER
            NoncurrentVersionExpiration:
              NoncurrentDays: 365
      
      # Configuración de notificaciones (comentado para primer deploy - función no existe aún)
      # NotificationConfiguration:
      #   LambdaConfigurations:
      #     - Event: s3:ObjectCreated:*
      #       Function:
      #         Fn::GetAtt:
      #           - S3ProcessorLambdaFunction
      #           - Arn
      #       Filter:
      #         S3Key:
      #           Rules:
      #             - Name: prefix
      #               Value: uploads/
      
      # Tags
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-data-${self:custom.stage}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Platform data storage
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential
        - Key: BackupRequired
          Value: "true"

  # Bucket para logs de aplicación
  LogsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:custom.projectName}-logs-${self:custom.stage}
      
      # Configuración de acceso público
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      
      # Configuración de lifecycle para logs
      LifecycleConfiguration:
        Rules:
          - Id: LogsRetention
            Status: Enabled
            ExpirationInDays: 90
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 60
                StorageClass: GLACIER
      
      # Tags
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-logs-${self:custom.stage}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Application logs storage
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

  # Bucket para backups
  BackupsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:custom.projectName}-backups-${self:custom.stage}
      
      # Configuración de versionado
      VersioningConfiguration:
        Status: Enabled
      
      # Configuración de encriptación
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
            BucketKeyEnabled: true
      
      # Configuración de acceso público
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      
      # Configuración de lifecycle para backups
      LifecycleConfiguration:
        Rules:
          - Id: BackupRetention
            Status: Enabled
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 90
                StorageClass: GLACIER
              - TransitionInDays: 180
                StorageClass: DEEP_ARCHIVE
            ExpirationInDays: 2555  # 7 años
      
      # Tags
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-backups-${self:custom.stage}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Data backups storage
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential
        - Key: RetentionPeriod
          Value: "7years"

  # Bucket policy para el bucket principal (comentado para primer deploy)
  # PlatformDataBucketPolicy:
  #   Type: AWS::S3::BucketPolicy
  #   Properties:
  #     Bucket:
  #       Ref: PlatformDataBucket
  #     PolicyDocument:
  #       Statement:
  #         - Sid: DenyInsecureConnections
  #           Effect: Deny
  #           Principal: "*"
  #           Action: "s3:*"
  #           Resource:
  #             - Fn::Sub: "${PlatformDataBucket}/*"
  #             - Fn::GetAtt: [PlatformDataBucket, Arn]
  #           Condition:
  #             Bool:
  #               "aws:SecureTransport": "false"

Outputs:
  PlatformDataBucketName:
    Description: Name of the platform data bucket
    Value:
      Ref: PlatformDataBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PlatformDataBucketName
  
  PlatformDataBucketArn:
    Description: ARN of the platform data bucket
    Value:
      Fn::GetAtt:
        - PlatformDataBucket
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PlatformDataBucketArn
  
  LogsBucketName:
    Description: Name of the logs bucket
    Value:
      Ref: LogsBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-LogsBucketName
  
  BackupsBucketName:
    Description: Name of the backups bucket
    Value:
      Ref: BackupsBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-BackupsBucketName

  S3BucketName:
    Description: Name of the platform data bucket (alias for compatibility)
    Value:
      Ref: PlatformDataBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-S3BucketName
