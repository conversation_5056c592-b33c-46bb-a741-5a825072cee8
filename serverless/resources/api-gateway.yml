# serverless/resources/api-gateway.yml
# Configuración de API Gateway - Migrado desde Terraform
# API Gateway con dominio personalizado y configuración de seguridad

Resources:
  # Configuración de cuenta de API Gateway
  ApiGatewayAccount:
    Type: AWS::ApiGateway::Account
    Properties:
      CloudWatchRoleArn:
        Fn::GetAtt:
          - ApiGatewayCloudWatchLogsRole
          - Arn

  # Dominio personalizado para API Gateway (comentado para primer deploy)
  # ApiGatewayDomainName:
  #   Type: AWS::ApiGateway::DomainName
  #   Properties:
  #     DomainName: ${self:custom.infrastructureConfig.${self:custom.stage}.domainName}
  #     CertificateArn: ${self:custom.infrastructureConfig.${self:custom.stage}.certificateArn}
  #     EndpointConfiguration:
  #       Types:
  #         - REGIONAL
  #     SecurityPolicy: TLS_1_2
  #
  #     Tags:
  #       - Key: Name
  #         Value: ${self:custom.projectName}-${self:custom.stage}-api-domain
  #       - Key: Environment
  #         Value: ${self:custom.stage}
  #       - Key: Purpose
  #         Value: API Gateway custom domain
  #       - Key: Project
  #         Value: Agent SCL Supply Chain Logistics
  #       - Key: ManagedBy
  #         Value: Serverless Framework

  # Mapeo del dominio personalizado (comentado hasta que se cree el API Gateway)
  # ApiGatewayBasePathMapping:
  #   Type: AWS::ApiGateway::BasePathMapping
  #   Properties:
  #     DomainName:
  #       Ref: ApiGatewayDomainName
  #     RestApiId:
  #       Ref: ApiGatewayRestApi
  #     Stage: ${self:custom.stage}
  #   DependsOn:
  #     - ApiGatewayDomainName

  # Registro DNS en Route 53 (comentado para primer deploy)
  # ApiGatewayDNSRecord:
  #   Type: AWS::Route53::RecordSet
  #   Properties:
  #     HostedZoneId: ${self:custom.infrastructureConfig.${self:custom.stage}.hostedZoneId}
  #     Name: ${self:custom.infrastructureConfig.${self:custom.stage}.domainName}
  #     Type: A
  #     AliasTarget:
  #       DNSName:
  #         Fn::GetAtt:
  #           - ApiGatewayDomainName
  #           - RegionalDomainName
  #       HostedZoneId:
  #         Fn::GetAtt:
  #           - ApiGatewayDomainName
  #           - RegionalHostedZoneId
  #       EvaluateTargetHealth: false

  # WAF Web ACL para protección de API (comentado para primer deploy)
  # ApiGatewayWebACL:
  #   Type: AWS::WAFv2::WebACL
  #   Properties:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-api-waf
  #     Scope: REGIONAL
  #     DefaultAction:
  #       Allow: {}
  #
  #     Rules:
  #       # Rate limiting rule
  #       - Name: RateLimitRule
  #         Priority: 1
  #         Statement:
  #           RateBasedStatement:
  #             Limit: 2000
  #             AggregateKeyType: IP
  #         Action:
  #           Block: {}
  #         VisibilityConfig:
  #           SampledRequestsEnabled: true
  #           CloudWatchMetricsEnabled: true
  #           MetricName: RateLimitRule
  #
  #       # AWS Managed Rules - Core Rule Set
  #       - Name: AWSManagedRulesCommonRuleSet
  #         Priority: 2
  #         OverrideAction:
  #           None: {}
  #         Statement:
  #           ManagedRuleGroupStatement:
  #             VendorName: AWS
  #             Name: AWSManagedRulesCommonRuleSet
  #         VisibilityConfig:
  #           SampledRequestsEnabled: true
  #           CloudWatchMetricsEnabled: true
  #           MetricName: CommonRuleSetMetric
  #
  #       # AWS Managed Rules - Known Bad Inputs
  #       - Name: AWSManagedRulesKnownBadInputsRuleSet
  #         Priority: 3
  #         OverrideAction:
  #           None: {}
  #         Statement:
  #           ManagedRuleGroupStatement:
  #             VendorName: AWS
  #             Name: AWSManagedRulesKnownBadInputsRuleSet
  #         VisibilityConfig:
  #           SampledRequestsEnabled: true
  #           CloudWatchMetricsEnabled: true
  #           MetricName: KnownBadInputsRuleSetMetric
  #
  #       # AWS Managed Rules - SQL Injection
  #       - Name: AWSManagedRulesSQLiRuleSet
  #         Priority: 4
  #         OverrideAction:
  #           None: {}
  #         Statement:
  #           ManagedRuleGroupStatement:
  #             VendorName: AWS
  #             Name: AWSManagedRulesSQLiRuleSet
  #         VisibilityConfig:
  #           SampledRequestsEnabled: true
  #           CloudWatchMetricsEnabled: true
  #           MetricName: SQLiRuleSetMetric
  #
  #     VisibilityConfig:
  #       SampledRequestsEnabled: true
  #       CloudWatchMetricsEnabled: true
  #       MetricName: ${self:custom.projectName}-${self:custom.stage}-api-waf
  #
  #     Tags:
  #       - Key: Name
  #         Value: ${self:custom.projectName}-${self:custom.stage}-api-waf
  #       - Key: Environment
  #         Value: ${self:custom.stage}
  #       - Key: Purpose
  #         Value: API Gateway WAF protection
  #       - Key: Project
  #         Value: Agent SCL Supply Chain Logistics
  #       - Key: ManagedBy
  #         Value: Serverless Framework

  # Asociación de WAF con API Gateway (comentado hasta que se cree el API Gateway)
  # ApiGatewayWebACLAssociation:
  #   Type: AWS::WAFv2::WebACLAssociation
  #   Properties:
  #     ResourceArn:
  #       Fn::Sub: "arn:aws:apigateway:${AWS::Region}::/restapis/${ApiGatewayRestApi}/stages/${self:custom.stage}"
  #     WebACLArn:
  #       Fn::GetAtt:
  #         - ApiGatewayWebACL
  #         - Arn
  #   DependsOn:
  #     - ApiGatewayWebACL

  # Request Validator para validación de entrada (comentado hasta que se cree el API Gateway)
  # ApiGatewayRequestValidator:
  #   Type: AWS::ApiGateway::RequestValidator
  #   Properties:
  #     RestApiId:
  #       Ref: ApiGatewayRestApi
  #     Name: ${self:custom.projectName}-${self:custom.stage}-request-validator
  #     ValidateRequestBody: true
  #     ValidateRequestParameters: true

  # Gateway Response para errores 4xx (comentado hasta que se cree el API Gateway)
  # ApiGateway4xxGatewayResponse:
  #   Type: AWS::ApiGateway::GatewayResponse
  #   Properties:
  #     RestApiId:
  #       Ref: ApiGatewayRestApi
  #     ResponseType: DEFAULT_4XX
  #     ResponseTemplates:
  #       application/json: |
  #         {
  #           "success": false,
  #           "error": {
  #             "message": "$context.error.message",
  #             "type": "$context.error.responseType",
  #             "requestId": "$context.requestId"
  #           },
  #           "timestamp": "$context.requestTime"
  #         }
  #     ResponseParameters:
  #       gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
  #       gatewayresponse.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
  #       gatewayresponse.header.Access-Control-Allow-Methods: "'GET,POST,PUT,DELETE,OPTIONS'"

  # Gateway Response para errores 5xx (comentado hasta que se cree el API Gateway)
  # ApiGateway5xxGatewayResponse:
  #   Type: AWS::ApiGateway::GatewayResponse
  #   Properties:
  #     RestApiId:
  #       Ref: ApiGatewayRestApi
  #     ResponseType: DEFAULT_5XX
  #     ResponseTemplates:
  #       application/json: |
  #         {
  #           "success": false,
  #           "error": {
  #             "message": "Internal server error",
  #             "type": "InternalServerError",
  #             "requestId": "$context.requestId"
  #           },
  #           "timestamp": "$context.requestTime"
  #         }
  #     ResponseParameters:
  #       gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
  #       gatewayresponse.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
  #       gatewayresponse.header.Access-Control-Allow-Methods: "'GET,POST,PUT,DELETE,OPTIONS'"

  # Usage Plan para rate limiting (comentado hasta que se cree el API Gateway)
  # ApiGatewayUsagePlan:
  #   Type: AWS::ApiGateway::UsagePlan
  #   Properties:
  #     UsagePlanName: ${self:custom.projectName}-${self:custom.stage}-usage-plan
  #     Description: Usage plan for ${self:custom.projectName} ${self:custom.stage} environment
  #     ApiStages:
  #       - ApiId:
  #           Ref: ApiGatewayRestApi
  #         Stage: ${self:custom.stage}
  #     Throttle:
  #       RateLimit: 1000
  #       BurstLimit: 2000
  #     Quota:
  #       Limit: 100000
  #       Period: DAY
  #
  #     Tags:
  #       - Key: Name
  #         Value: ${self:custom.projectName}-${self:custom.stage}-usage-plan
  #       - Key: Environment
  #         Value: ${self:custom.stage}
  #       - Key: Purpose
  #         Value: API Gateway usage plan
  #       - Key: Project
  #         Value: Agent SCL Supply Chain Logistics
  #       - Key: ManagedBy
  #         Value: Serverless Framework

Outputs:
  # ApiGatewayRestApiId (comentado hasta que se cree el API Gateway)
  # ApiGatewayRestApiId:
  #   Description: ID of the API Gateway REST API
  #   Value:
  #     Ref: ApiGatewayRestApi
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayRestApiId
  
  # ApiGatewayDomainName (comentado para primer deploy)
  # ApiGatewayDomainName:
  #   Description: Custom domain name for API Gateway
  #   Value:
  #     Ref: ApiGatewayDomainName
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayDomainName
  
  # ApiGatewayWebACLArn (comentado hasta que se despliegue WAF)
  # ApiGatewayWebACLArn:
  #   Description: ARN of the API Gateway WAF Web ACL
  #   Value:
  #     Fn::GetAtt:
  #       - ApiGatewayWebACL
  #       - Arn
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayWebACLArn
  
  # ApiGatewayUsagePlanId (comentado hasta que se cree el API Gateway)
  # ApiGatewayUsagePlanId:
  #   Description: ID of the API Gateway usage plan
  #   Value:
  #     Ref: ApiGatewayUsagePlan
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayUsagePlanId

  # RestApiId (comentado hasta que se cree el API Gateway)
  # RestApiId:
  #   Description: ID of the API Gateway REST API (alias for compatibility)
  #   Value:
  #     Ref: ApiGatewayRestApi
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-RestApiId

  # RestApiRootResourceId (comentado hasta que se cree el API Gateway)
  # RestApiRootResourceId:
  #   Description: Root resource ID of the API Gateway REST API
  #   Value:
  #     Fn::GetAtt:
  #       - ApiGatewayRestApi
  #       - RootResourceId
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-RestApiRootResourceId

  # ApiGatewayRootResourceId (comentado hasta que se cree el API Gateway)
  # ApiGatewayRootResourceId:
  #   Description: Root resource ID of the API Gateway REST API (alias for compatibility)
  #   Value:
  #     Fn::GetAtt:
  #       - ApiGatewayRestApi
  #       - RootResourceId
  #   Export:
  #     Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayRootResourceId
