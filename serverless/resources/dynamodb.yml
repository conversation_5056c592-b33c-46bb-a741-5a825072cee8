# serverless/resources/dynamodb.yml
# Configuración de DynamoDB - Migrado desde Terraform
# Single Table Design para máximo rendimiento

Resources:
  # Tabla principal de la plataforma (Single Table Design)
  PlatformMainTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ${self:custom.projectName}-main-${self:custom.stage}
      BillingMode: ${self:custom.infrastructureConfig.${self:custom.stage}.dynamodbBillingMode, 'PAY_PER_REQUEST'}
      
      # Configuración de claves
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
        - AttributeName: GSI1PK
          AttributeType: S
        - AttributeName: GSI1SK
          AttributeType: S
        - AttributeName: GSI2PK
          AttributeType: S
        - AttributeName: GSI2SK
          AttributeType: S
        - AttributeName: GSI3PK
          AttributeType: S
        - AttributeName: GSI3SK
          AttributeType: S
      
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      
      # Global Secondary Indexes
      GlobalSecondaryIndexes:
        # GSI1 - Para consultas de usuarios por tenant
        - IndexName: GSI1
          KeySchema:
            - AttributeName: GSI1PK
              KeyType: HASH
            - AttributeName: GSI1SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI2 - Para consultas de conversaciones por usuario
        - IndexName: GSI2
          KeySchema:
            - AttributeName: GSI2PK
              KeyType: HASH
            - AttributeName: GSI2SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI3 - Para consultas de suscripciones y pagos
        - IndexName: GSI3
          KeySchema:
            - AttributeName: GSI3PK
              KeyType: HASH
            - AttributeName: GSI3SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      
      # Configuración de streams para arquitectura event-driven
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      
      # Point-in-time recovery
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.infrastructureConfig.${self:custom.stage}.enableBackups, true}
      
      # Encriptación
      SSESpecification:
        SSEEnabled: true
        SSEType: KMS
        KMSMasterKeyId: alias/aws/dynamodb
      
      # Protección contra eliminación
      DeletionProtectionEnabled: ${self:custom.infrastructureConfig.${self:custom.stage}.deletionProtection, false}
      
      # Tags
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-main-${self:custom.stage}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: Main platform data storage
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Confidential
        - Key: BackupRequired
          Value: "true"

  # Tabla para sesiones de usuario (TTL habilitado)
  UserSessionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ${self:custom.projectName}-sessions-${self:custom.stage}
      BillingMode: PAY_PER_REQUEST
      
      AttributeDefinitions:
        - AttributeName: session_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      
      KeySchema:
        - AttributeName: session_id
          KeyType: HASH

      # GSI para consultas por usuario
      GlobalSecondaryIndexes:
        - IndexName: UserIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      
      # TTL para expiración automática de sesiones
      TimeToLiveSpecification:
        AttributeName: expires_at
        Enabled: true
      
      # Encriptación
      SSESpecification:
        SSEEnabled: true
        SSEType: KMS
        KMSMasterKeyId: alias/aws/dynamodb
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-sessions-${self:custom.stage}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: User session management
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework
        - Key: DataClassification
          Value: Internal

  # Tabla para rate limiting
  RateLimitTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ${self:custom.projectName}-rate-limit-${self:custom.stage}
      BillingMode: PAY_PER_REQUEST
      
      AttributeDefinitions:
        - AttributeName: identifier
          AttributeType: S
      
      KeySchema:
        - AttributeName: identifier
          KeyType: HASH
      
      # TTL para limpieza automática
      TimeToLiveSpecification:
        AttributeName: expires_at
        Enabled: true
      
      # Encriptación
      SSESpecification:
        SSEEnabled: true
        SSEType: KMS
        KMSMasterKeyId: alias/aws/dynamodb
      
      Tags:
        - Key: Name
          Value: ${self:custom.projectName}-rate-limit-${self:custom.stage}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Purpose
          Value: API rate limiting
        - Key: Project
          Value: Agent SCL Supply Chain Logistics
        - Key: ManagedBy
          Value: Serverless Framework

Outputs:
  DynamoDBTableName:
    Description: Name of the main DynamoDB table
    Value:
      Ref: PlatformMainTable
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName
  
  DynamoDBTableArn:
    Description: ARN of the main DynamoDB table
    Value:
      Fn::GetAtt:
        - PlatformMainTable
        - Arn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
  
  DynamoDBStreamArn:
    Description: ARN of the DynamoDB stream
    Value:
      Fn::GetAtt:
        - PlatformMainTable
        - StreamArn
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-DynamoDBStreamArn
  
  UserSessionsTableName:
    Description: Name of the user sessions table
    Value:
      Ref: UserSessionsTable
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-UserSessionsTableName
  
  RateLimitTableName:
    Description: Name of the rate limit table
    Value:
      Ref: RateLimitTable
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-RateLimitTableName
