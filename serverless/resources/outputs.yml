# serverless/resources/outputs.yml
# Outputs consolidados para la infraestructura principal

Outputs:
  # DynamoDB Outputs
  DynamoDBTableName:
    Description: Name of the main DynamoDB table
    Value:
      Ref: PlatformMainTable
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName
  
  DynamoDBTableArn:
    Description: ARN of the main DynamoDB table
    Value:
      Fn::GetAtt: [PlatformMainTable, Arn]
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
  
  RateLimitTableName:
    Description: Name of the rate limit table
    Value:
      Ref: RateLimitTable
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-RateLimitTableName
  
  UserSessionsTableName:
    Description: Name of the user sessions table
    Value:
      Ref: UserSessionsTable
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-UserSessionsTableName
  
  # S3 Outputs
  PlatformDataBucketName:
    Description: Name of the platform data bucket
    Value:
      Ref: PlatformDataBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PlatformDataBucketName
  
  PlatformDataBucketArn:
    Description: ARN of the platform data bucket
    Value:
      Fn::GetAtt: [PlatformDataBucket, Arn]
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PlatformDataBucketArn
  
  BackupsBucketName:
    Description: Name of the backups bucket
    Value:
      Ref: BackupsBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-BackupsBucketName
  
  LogsBucketName:
    Description: Name of the logs bucket
    Value:
      Ref: LogsBucket
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-LogsBucketName
  
  # IAM Outputs
  LambdaExecutionRoleArn:
    Description: ARN of the Lambda execution role
    Value:
      Fn::GetAtt: [IamRoleLambdaExecution, Arn]
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-LambdaExecutionRoleArn
  
  ApiGatewayCloudWatchLogsRoleArn:
    Description: ARN of the API Gateway CloudWatch logs role
    Value:
      Fn::GetAtt: [ApiGatewayCloudWatchLogsRole, Arn]
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-ApiGatewayCloudWatchLogsRoleArn
  
  # VPC Outputs (CRÍTICOS para servicios)
  LambdaSecurityGroupId:
    Description: Lambda security group ID
    Value:
      Ref: LambdaSecurityGroup
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-LambdaSecurityGroupId
  
  PrivateSubnetId1:
    Description: Private subnet 1 ID
    Value:
      Ref: PrivateSubnet1
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnetId1
  
  PrivateSubnetId2:
    Description: Private subnet 2 ID
    Value:
      Ref: PrivateSubnet2
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnetId2

  # SNS Topics Outputs
  UserEventsTopicArn:
    Description: ARN of the user events SNS topic
    Value:
      Ref: UserEventsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-UserEventsTopicArn

  SecurityEventsTopicArn:
    Description: ARN of the security events SNS topic
    Value:
      Ref: SecurityEventsTopic
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-SecurityEventsTopicArn
