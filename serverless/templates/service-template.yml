# serverless/templates/service-template.yml
# Standard template for all services

service: agent-scl-${self:custom.serviceName}

# Custom configuration
custom:
  # Service identification
  serviceName: ${opt:service, 'template'}
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl
  
  # Load shared variables
  sharedVars: ${file(../../shared/variables.yml)}
  
  # Environment-specific configuration
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}
  
  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn: 
    Fn::ImportValue: ${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
  jwtAuthorizerArn:
    Fn::ImportValue: ${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
  
  # VPC configuration
  vpcConfig:
    securityGroupIds:
      - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-lambda-sg
    subnetIds:
      - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-private-subnet-1
      - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-private-subnet-2
  
  # CORS configuration
  corsOrigins: ${self:custom.sharedVars.cors.${self:custom.stage}}
  
  # Python requirements optimization
  pythonRequirements:
    dockerizePip: false
    slim: true
    strip: false
    useDownloadCache: true
    useStaticCache: true
    fileName: requirements.txt
  
  # Warmup configuration
  warmup:
    enabled: true
    prewarm: true
    concurrency: 1
    schedule: rate(5 minutes)

# Provider configuration
provider:
  name: aws
  runtime: ${self:custom.stageConfig.lambda.defaults.runtime}
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # VPC configuration
  vpc: ${self:custom.vpcConfig}
  
  # Common environment variables
  environment:
    # Project identification
    PROJECT_NAME: ${self:custom.projectName}
    SERVICE_NAME: ${self:custom.serviceName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    
    # Infrastructure
    DYNAMODB_TABLE: ${self:custom.dynamodbTable}
    
    # Monitoring
    LOG_LEVEL: ${self:custom.stageConfig.logLevel}
    ENABLE_XRAY: ${self:custom.stageConfig.enableXRayTracing}
    ENABLE_METRICS: true
    
    # Security
    CORS_ORIGINS: ${self:custom.corsOrigins}
  
  # IAM permissions
  iamRoleStatements:
    # DynamoDB permissions
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:BatchGetItem
        - dynamodb:BatchWriteItem
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-dynamodb-table-arn
        - Fn::Join:
          - ""
          - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-dynamodb-table-arn
            - "/index/*"
    
    # Secrets Manager permissions
    - Effect: Allow
      Action:
        - secretsmanager:GetSecretValue
        - secretsmanager:DescribeSecret
      Resource:
        - arn:aws:secretsmanager:${self:custom.region}:*:secret:${self:custom.projectName}/${self:custom.stage}/*
    
    # CloudWatch Logs permissions
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource:
        - arn:aws:logs:${self:custom.region}:*:log-group:/aws/lambda/${self:service}-${self:custom.stage}-*
    
    # X-Ray permissions
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"
  
  # Deployment configuration
  deploymentBucket:
    name: ${self:custom.projectName}-${self:custom.stage}-deployments
    serverSideEncryption: AES256
    versioning: true
  
  # CloudFormation configuration
  stackTags:
    Project: ${self:custom.projectName}
    Environment: ${self:custom.stage}
    Service: ${self:custom.serviceName}
    ManagedBy: Serverless
  
  # Tracing
  tracing:
    lambda: ${self:custom.stageConfig.enableXRayTracing}

# Plugins
plugins:
  - serverless-python-requirements
  - serverless-plugin-warmup
  - serverless-plugin-tracing

# Package configuration
package:
  individually: true
  exclude:
    - .git/**
    - .pytest_cache/**
    - __pycache__/**
    - "*.pyc"
    - tests/**
    - docs/**
    - scripts/**
    - .env*
    - README.md
    - requirements-dev.txt

# Functions template (to be overridden in each service)
functions: {}

# Resources template (to be overridden in each service)
resources:
  Resources:
    # CloudWatch Log Group
    ServiceLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/lambda/${self:service}-${self:custom.stage}
        RetentionInDays: ${self:custom.stageConfig.logRetentionDays}
  
  # Outputs template
  Outputs:
    ServiceName:
      Description: Service name
      Value: ${self:service}
      Export:
        Name: ${self:service}-${self:custom.stage}-ServiceName
    
    ServiceStage:
      Description: Service stage
      Value: ${self:custom.stage}
      Export:
        Name: ${self:service}-${self:custom.stage}-ServiceStage
