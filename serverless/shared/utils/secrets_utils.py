"""
AWS Secrets Manager Utilities
Provides secure access to application secrets with caching.
"""

import json
import boto3
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class SecretsManager:
    """
    AWS Secrets Manager client with caching capabilities.
    """
    
    def __init__(self, region: str = "us-east-1"):
        self.region = region
        self.secrets_client = boto3.client('secretsmanager', region_name=region)
        self._cache = {}
        self._cache_ttl = 300  # 5 minutes cache TTL
        
    def get_secret(self, secret_name: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get secret value from AWS Secrets Manager with caching.
        
        Args:
            secret_name: Name of the secret to retrieve
            force_refresh: Force refresh from AWS even if cached
            
        Returns:
            Secret value as dictionary
        """
        now = datetime.utcnow()
        cache_key = secret_name
        
        # Check cache first
        if not force_refresh and cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if (now - cached_data['timestamp']).seconds < self._cache_ttl:
                logger.debug(f"Using cached secret: {secret_name}")
                return cached_data['value']
        
        try:
            response = self.secrets_client.get_secret_value(SecretId=secret_name)
            secret_value = json.loads(response['SecretString'])
            
            # Cache the result
            self._cache[cache_key] = {
                'value': secret_value,
                'timestamp': now
            }
            
            logger.info(f"Secret retrieved and cached: {secret_name}")
            return secret_value
            
        except Exception as e:
            logger.error(f"Failed to get secret {secret_name}: {str(e)}")
            raise Exception(f"Failed to retrieve secret {secret_name}: {str(e)}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return self.get_secret("agent-scl/dev/database")
    
    def get_jwt_config(self) -> Dict[str, Any]:
        """Get JWT configuration."""
        return self.get_secret("agent-scl/dev/jwt")
    
    def get_stripe_config(self) -> Dict[str, Any]:
        """Get Stripe configuration."""
        return self.get_secret("agent-scl/dev/stripe")
    
    def get_email_config(self) -> Dict[str, Any]:
        """Get email configuration."""
        return self.get_secret("agent-scl/dev/email")
    
    def get_application_config(self) -> Dict[str, Any]:
        """Get application configuration."""
        return self.get_secret("agent-scl/dev/application")
    
    def clear_cache(self, secret_name: Optional[str] = None):
        """
        Clear secret cache.
        
        Args:
            secret_name: Specific secret to clear, or None to clear all
        """
        if secret_name:
            self._cache.pop(secret_name, None)
            logger.info(f"Cache cleared for secret: {secret_name}")
        else:
            self._cache.clear()
            logger.info("All secret cache cleared")


# Global instance for easy access
secrets_manager = SecretsManager()

# Convenience functions
def get_secret(secret_name: str, force_refresh: bool = False) -> Dict[str, Any]:
    """Get secret using the global secrets manager."""
    return secrets_manager.get_secret(secret_name, force_refresh)

def get_database_config() -> Dict[str, Any]:
    """Get database configuration."""
    return secrets_manager.get_database_config()

def get_jwt_config() -> Dict[str, Any]:
    """Get JWT configuration."""
    return secrets_manager.get_jwt_config()

def get_stripe_config() -> Dict[str, Any]:
    """Get Stripe configuration."""
    return secrets_manager.get_stripe_config()

def get_email_config() -> Dict[str, Any]:
    """Get email configuration."""
    return secrets_manager.get_email_config()

def get_application_config() -> Dict[str, Any]:
    """Get application configuration."""
    return secrets_manager.get_application_config()

def clear_secret_cache(secret_name: Optional[str] = None):
    """Clear secret cache."""
    secrets_manager.clear_cache(secret_name)
