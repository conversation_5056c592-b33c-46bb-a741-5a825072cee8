"""
JWT Utilities with Multi-Key Support
Provides secure JWT token generation and validation with key rotation capabilities.
"""

import json
import jwt
import boto3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class JWTManager:
    """
    JWT Manager with multi-key support for secure token operations.
    Supports key rotation and backward compatibility.
    """
    
    def __init__(self, secret_name: str = "agent-scl/dev/jwt", region: str = "us-east-1"):
        self.secret_name = secret_name
        self.region = region
        self.secrets_client = boto3.client('secretsmanager', region_name=region)
        self._jwt_config = None
        self._last_refresh = None
        
    def _get_jwt_config(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get JWT configuration from AWS Secrets Manager with caching.
        
        Args:
            force_refresh: Force refresh from AWS even if cached
            
        Returns:
            JWT configuration dictionary
        """
        now = datetime.utcnow()
        
        # Cache for 5 minutes to avoid excessive API calls
        if (not force_refresh and 
            self._jwt_config and 
            self._last_refresh and 
            (now - self._last_refresh).seconds < 300):
            return self._jwt_config
            
        try:
            response = self.secrets_client.get_secret_value(SecretId=self.secret_name)
            self._jwt_config = json.loads(response['SecretString'])
            self._last_refresh = now
            
            logger.info(f"JWT config refreshed from {self.secret_name}")
            return self._jwt_config
            
        except Exception as e:
            logger.error(f"Failed to get JWT config: {str(e)}")
            raise Exception(f"Failed to retrieve JWT configuration: {str(e)}")
    
    def get_active_key(self) -> Dict[str, Any]:
        """
        Get the currently active key for signing new tokens.
        
        Returns:
            Active key configuration
            
        Raises:
            Exception: If no active key is found
        """
        config = self._get_jwt_config()
        
        for key in config.get('keys', []):
            if key.get('active', False):
                return key
                
        raise Exception("No active JWT key found")
    
    def get_key_by_kid(self, kid: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific key by its key ID.
        
        Args:
            kid: Key ID to search for
            
        Returns:
            Key configuration or None if not found
        """
        config = self._get_jwt_config()
        
        for key in config.get('keys', []):
            if key.get('kid') == kid:
                return key
                
        return None
    
    def get_all_keys(self) -> List[Dict[str, Any]]:
        """
        Get all available keys for token validation.
        
        Returns:
            List of all key configurations
        """
        config = self._get_jwt_config()
        return config.get('keys', [])
    
    def generate_token(self, payload: Dict[str, Any], expires_in_hours: int = 24) -> str:
        """
        Generate a new JWT token using the active key.
        
        Args:
            payload: Token payload data
            expires_in_hours: Token expiration time in hours
            
        Returns:
            Encoded JWT token
        """
        try:
            config = self._get_jwt_config()
            active_key = self.get_active_key()
            
            # Prepare token payload
            now = datetime.utcnow()
            token_payload = {
                **payload,
                'iss': config.get('issuer', 'agent-scl-platform'),
                'aud': config.get('audience', 'agent-scl-users'),
                'iat': now,
                'exp': now + timedelta(hours=expires_in_hours),
                'kid': active_key['kid']  # Include key ID in payload
            }
            
            # Generate token
            token = jwt.encode(
                token_payload,
                active_key['secret_key'],
                algorithm=config.get('algorithm', 'HS256'),
                headers={'kid': active_key['kid']}  # Include key ID in header
            )
            
            logger.info(f"Token generated with key {active_key['kid']}")
            return token
            
        except Exception as e:
            logger.error(f"Failed to generate token: {str(e)}")
            raise Exception(f"Token generation failed: {str(e)}")
    
    def validate_token(self, token: str) -> Dict[str, Any]:
        """
        Validate a JWT token using available keys.
        Tries active key first, then falls back to other keys.
        
        Args:
            token: JWT token to validate
            
        Returns:
            Decoded token payload
            
        Raises:
            Exception: If token is invalid or expired
        """
        try:
            config = self._get_jwt_config()
            
            # Try to get key ID from token header
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get('kid')
            
            keys_to_try = []
            
            # If we have a key ID, try that key first
            if kid:
                specific_key = self.get_key_by_kid(kid)
                if specific_key:
                    keys_to_try.append(specific_key)
            
            # Add active key if not already included
            active_key = self.get_active_key()
            if not kid or active_key['kid'] != kid:
                keys_to_try.append(active_key)
            
            # Add all other keys as fallback
            all_keys = self.get_all_keys()
            for key in all_keys:
                if key not in keys_to_try:
                    keys_to_try.append(key)
            
            # Try to validate with each key
            last_exception = None
            for key in keys_to_try:
                try:
                    payload = jwt.decode(
                        token,
                        key['secret_key'],
                        algorithms=[config.get('algorithm', 'HS256')],
                        issuer=config.get('issuer', 'agent-scl-platform'),
                        audience=config.get('audience', 'agent-scl-users')
                    )
                    
                    logger.info(f"Token validated with key {key['kid']}")
                    return payload
                    
                except jwt.InvalidTokenError as e:
                    last_exception = e
                    continue
            
            # If we get here, no key worked
            logger.warning(f"Token validation failed with all available keys")
            raise Exception(f"Invalid token: {str(last_exception)}")
            
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            raise Exception(f"Token validation failed: {str(e)}")
    
    def refresh_config(self):
        """Force refresh of JWT configuration from AWS Secrets Manager."""
        self._get_jwt_config(force_refresh=True)


# Global instance for easy access
jwt_manager = JWTManager()

# Convenience functions for backward compatibility
def generate_token(payload: Dict[str, Any], expires_in_hours: int = 24) -> str:
    """Generate JWT token using the global JWT manager."""
    return jwt_manager.generate_token(payload, expires_in_hours)

def validate_token(token: str) -> Dict[str, Any]:
    """Validate JWT token using the global JWT manager."""
    return jwt_manager.validate_token(token)

def get_active_key() -> Dict[str, Any]:
    """Get the currently active JWT key."""
    return jwt_manager.get_active_key()

def refresh_jwt_config():
    """Refresh JWT configuration from AWS."""
    jwt_manager.refresh_config()
