# serverless/shared/variables.yml
# Variables compartidas unificadas - Post migración Serverless Framework
# Configuración coherente y consistente para todos los servicios

# Project configuration
projectName: agent-scl
organizationName: agent-scl-org

# Stage-specific configurations
stages:
  dev:
    # Infrastructure stack reference (usando nombres consistentes)
    infrastructureStack: agent-scl-infrastructure-dev

    # Resource naming (unificado con configuración principal)
    dynamodbTable: agent-scl-main-dev
    s3Bucket:
      Fn::ImportValue: agent-scl-dev-PlatformDataBucketName

    # API Gateway configuration
    apiGatewayName: agent-scl-api-gateway-dev
    
    # Lambda configuration standards (TEMPORARY: LOW CONCURRENCY ACCOUNT)
    # NOTE: Reserved concurrency disabled due to account limit of 10 total concurrent executions
    # Will be re-enabled after AWS increases the limit to 1000+
    lambda:
      defaults:
        timeout: 29
        memorySize: 256
        # reservedConcurrency: 20  # DISABLED - Account limit too low
        runtime: python3.11
        tracing: Active
      profiles:
        ultra-quick:
          timeout: 5
          memorySize: 128
          # reservedConcurrency: 10  # DISABLED - Account limit too low
        quick:
          timeout: 15
          memorySize: 128
          # reservedConcurrency: 15  # DISABLED - Account limit too low
        standard:
          timeout: 29
          memorySize: 256
          # reservedConcurrency: 20  # DISABLED - Account limit too low
        heavy:
          timeout: 29
          memorySize: 512
          # reservedConcurrency: 10  # DISABLED - Account limit too low
        critical:
          timeout: 5
          memorySize: 256
          # reservedConcurrency: 50  # DISABLED - Account limit too low
        background:
          timeout: 300
          memorySize: 256
          # reservedConcurrency: 2   # DISABLED - Account limit too low
        admin:
          timeout: 29
          memorySize: 256
          # reservedConcurrency: 5   # DISABLED - Account limit too low
    
    # Monitoring
    logRetentionDays: 7
    enableXRayTracing: true
    
    # Security
    enableWAF: false

    # Infrastructure configuration
    logLevel: DEBUG
    enableMonitoring: true
    dynamodbBillingMode: PAY_PER_REQUEST

    # Email configuration
    email:
      fromAddress: "<EMAIL>"
      replyToAddress: "<EMAIL>"

    # CORS configuration
    cors:
      allowedOrigins:
        - "http://localhost:3000"
        - "http://localhost:3001"
        - "https://dev.agentscl.com"
        - "https://dev-app.agentscl.com"

    # Security configuration
    security:
      passwordMinLength: 8  # Relaxed for dev testing
      maxLoginAttempts: 10  # More lenient for dev
      accountLockDuration: 900  # 15 minutes for dev
    
  staging:
    # Infrastructure stack reference
    infrastructureStack: agent-scl-infrastructure-staging

    # Resource naming
    dynamodbTable: agent-scl-main-staging
    s3Bucket:
      Fn::ImportValue: agent-scl-staging-PlatformDataBucketName

    # API Gateway configuration
    apiGatewayName: agent-scl-api-gateway-staging

    # Lambda configuration standards
    lambda:
      defaults:
        timeout: 29
        memorySize: 512
        reservedConcurrency: 40
        runtime: python3.11
        tracing: Active
      profiles:
        ultra-quick:
          timeout: 5
          memorySize: 128
          reservedConcurrency: 20
        quick:
          timeout: 15
          memorySize: 128
          reservedConcurrency: 30
        standard:
          timeout: 29
          memorySize: 256
          reservedConcurrency: 40
        heavy:
          timeout: 29
          memorySize: 512
          reservedConcurrency: 25
        critical:
          timeout: 5
          memorySize: 256
          reservedConcurrency: 100
        background:
          timeout: 300
          memorySize: 256
          reservedConcurrency: 5
        admin:
          timeout: 29
          memorySize: 256
          reservedConcurrency: 10
    
    # Monitoring
    logRetentionDays: 30
    enableXRayTracing: true
    
    # Security
    enableWAF: true
    
  prod:
    # Infrastructure stack reference
    infrastructureStack: agent-scl-infrastructure-prod

    # Resource naming
    dynamodbTable: agent-scl-main-prod
    s3Bucket:
      Fn::ImportValue: agent-scl-prod-PlatformDataBucketName

    # API Gateway configuration
    apiGatewayName: agent-scl-api-gateway-prod

    # Lambda configuration standards
    lambda:
      defaults:
        timeout: 29
        memorySize: 1024
        reservedConcurrency: 80
        runtime: python3.11
        tracing: Active
      profiles:
        ultra-quick:
          timeout: 5
          memorySize: 128
          reservedConcurrency: 50
        quick:
          timeout: 15
          memorySize: 128
          reservedConcurrency: 100
        standard:
          timeout: 29
          memorySize: 256
          reservedConcurrency: 80
        heavy:
          timeout: 29
          memorySize: 512
          reservedConcurrency: 50
        critical:
          timeout: 5
          memorySize: 256
          reservedConcurrency: 300
        background:
          timeout: 300
          memorySize: 256
          reservedConcurrency: 10
        admin:
          timeout: 29
          memorySize: 256
          reservedConcurrency: 20
    
    # Monitoring
    logRetentionDays: 90
    enableXRayTracing: true
    
    # Security
    enableWAF: true

# Common Lambda layer configuration
layers:
  pythonRequirements:
    name: ${self:custom.projectName}-python-requirements-${self:custom.stage}
    description: "Python requirements layer for ${self:custom.stage} environment"
    compatibleRuntimes:
      - python3.11
    retain: false

# Common API Gateway configuration
apiGateway:
  cors:
    origin: '*'
    headers:
      - Content-Type
      - X-Amz-Date
      - Authorization
      - X-Api-Key
      - X-Amz-Security-Token
      - X-Amz-User-Agent
      - X-Tenant-Id
    allowCredentials: false
  
  # Request validation
  requestValidation:
    validateRequestBody: true
    validateRequestParameters: true
  
  # Throttling
  throttling:
    burstLimit: 5000
    rateLimit: 2000

# Common monitoring configuration
monitoring:
  # CloudWatch alarms
  alarms:
    - name: HighErrorRate
      metric: Errors
      threshold: 10
      comparisonOperator: GreaterThanThreshold
      evaluationPeriods: 2
      period: 300
      statistic: Sum
      treatMissingData: notBreaching
    
    - name: HighDuration
      metric: Duration
      threshold: 10000
      comparisonOperator: GreaterThanThreshold
      evaluationPeriods: 2
      period: 300
      statistic: Average
      treatMissingData: notBreaching
    
    - name: HighThrottles
      metric: Throttles
      threshold: 5
      comparisonOperator: GreaterThanThreshold
      evaluationPeriods: 2
      period: 300
      statistic: Sum
      treatMissingData: notBreaching

# Security configuration
security:
  # JWT configuration
  jwt:
    issuer: "platform-auth"
    audience: "platform-api"
    algorithm: "RS256"
    
  # Rate limiting
  rateLimiting:
    perSecond: 10
    perMinute: 100
    perHour: 1000
    
  # CORS configuration
  cors:
    allowedOrigins:
      dev: ["http://localhost:3000", "https://dev.platform.com"]
      staging: ["https://staging.platform.com"]
      prod: ["https://platform.com", "https://www.platform.com"]

# Integration configuration
integrations:
  # 🔐 MIGRADO A SECRETS MANAGER: Todos los secretos ahora se gestionan de forma segura
  # Los valores se obtienen dinámicamente desde AWS Secrets Manager
  n8n:
    # Configuración gestionada en AWS Secrets Manager
    # Secret: ${projectName}/${stage}/n8n
    secretArn:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-N8nSecretArn

  stripe:
    # Configuración gestionada en AWS Secrets Manager
    # Secret: ${projectName}/${stage}/stripe
    # TODOS LOS VALORES SE OBTIENEN DESDE SECRETS MANAGER
    secretArn:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-StripeSecretArn

  payU:
    # Configuración gestionada en AWS Secrets Manager
    # Secret: ${projectName}/${stage}/payu
    secretArn:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-PayUSecretArn
    
  ses:
    fromEmail: "<EMAIL>"
    replyToEmail: "<EMAIL>"
    region: "us-east-1"
