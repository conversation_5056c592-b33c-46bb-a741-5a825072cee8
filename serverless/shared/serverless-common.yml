# serverless/shared/serverless-common.yml
# Implementado según "Infrastructure as Code Configuration" y "Development Standards"

# Common configuration shared across all Serverless services
# This file contains shared settings, plugins, and configurations

# Serverless Framework version
frameworkVersion: '3'

# Common plugins used across all services
plugins:
  - serverless-python-requirements
  - serverless-iam-roles-per-function
  - serverless-plugin-tracing
  - serverless-plugin-log-retention
  - serverless-offline

# Common custom variables
custom:
  # Python requirements configuration
  pythonRequirements:
    dockerizePip: true
    slim: true
    strip: false
    layer: true
    useDownloadCache: true
    useStaticCache: true
    pipCmdExtraArgs:
      - --no-cache-dir
    
  # Log retention configuration
  logRetentionInDays:
    dev: 7
    staging: 30
    prod: 90
  
  # Common environment variables
  commonEnvironment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    AWS_REGION: ${self:custom.region}
    DYNAMODB_TABLE: ${self:custom.dynamodbTable}
    S3_BUCKET: ${self:custom.s3Bucket}
    LOG_LEVEL: ${self:custom.logLevel.${self:custom.stage}, 'INFO'}
    
  # Stage-specific log levels
  logLevel:
    dev: DEBUG
    staging: INFO
    prod: WARN
    
  # X-Ray tracing configuration
  tracing:
    lambda: true
    apiGateway: true
    
  # Common Lambda configuration
  lambdaDefaults:
    runtime: python3.11
    timeout: 29
    memorySize: 256
    reservedConcurrency: 20
    environment: ${self:custom.commonEnvironment}
    tracing: Active
    layers:
      - ${cf:${self:custom.projectName}-shared-${self:custom.stage}.PythonRequirementsLambdaLayerQualifiedArn}
    vpc:
      securityGroupIds:
        - ${cf:${self:custom.infrastructureStack}.LambdaSecurityGroupId}
      subnetIds:
        - ${cf:${self:custom.infrastructureStack}.PrivateSubnetId1}
        - ${cf:${self:custom.infrastructureStack}.PrivateSubnetId2}
    
  # API Gateway configuration
  apiGatewayDefaults:
    restApiId: ${cf:${self:custom.projectName}-api-gateway-${self:custom.stage}.RestApiId}
    restApiRootResourceId: ${cf:${self:custom.projectName}-api-gateway-${self:custom.stage}.RestApiRootResourceId}
    
  # Common IAM statements
  commonIamStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: 
        - "arn:aws:logs:${self:custom.region}:*:log-group:/aws/lambda/*"
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:BatchGetItem
        - dynamodb:BatchWriteItem
      Resource:
        - "arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.dynamodbTable}"
        - "arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.dynamodbTable}/index/*"
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
        - s3:DeleteObject
        - s3:ListBucket
      Resource:
        - "arn:aws:s3:::${self:custom.s3Bucket}"
        - "arn:aws:s3:::${self:custom.s3Bucket}/*"
    - Effect: Allow
      Action:
        - secretsmanager:GetSecretValue
        - secretsmanager:DescribeSecret
      Resource:
        - "arn:aws:secretsmanager:${self:custom.region}:*:secret:${self:custom.projectName}/${self:custom.stage}/*"

# Common provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  
  # Environment variables
  environment: ${self:custom.commonEnvironment}
  
  # Tracing
  tracing:
    lambda: true
    apiGateway: true
    
  # Logs
  logs:
    restApi: true
    
  # API Gateway configuration
  apiGateway:
    shouldStartNameWithService: true
    minimumCompressionSize: 1024
    
  # Deployment configuration
  deploymentBucket:
    name: ${self:custom.projectName}-serverless-deployments-${self:custom.stage}
    serverSideEncryption: AES256

  # Tags
  tags:
    Project: "Agent SCL - Supply Chain & Logistics"
    Environment: ${self:custom.stage}
    ManagedBy: "Serverless Framework"
    Service: ${self:service}

  # Stack tags
  stackTags:
    Project: "Agent SCL - Supply Chain & Logistics"
    Environment: ${self:custom.stage}
    ManagedBy: "Serverless Framework"
