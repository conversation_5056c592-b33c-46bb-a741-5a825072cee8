# 🌐 API Gateway Infrastructure

## 📋 **Overview**

The API Gateway Infrastructure provides centralized API management for the logistics AI platform. It handles request routing, authentication, rate limiting, monitoring, and documentation for all microservices.

## 🏗️ **Architecture**

### **Core Components**
- **Request Router**: Routes requests to appropriate microservices
- **Health Aggregator**: Monitors and aggregates service health status
- **Configuration Manager**: Manages API Gateway settings and routing rules
- **Documentation Generator**: Provides OpenAPI specs and Swagger UI
- **Security Layer**: WAF protection and rate limiting

### **Features**
- **Centralized Routing**: Single entry point for all API requests
- **Service Discovery**: Automatic service endpoint resolution
- **Health Monitoring**: Real-time health checks across all services
- **API Documentation**: Auto-generated OpenAPI specifications
- **Security**: WAF protection, rate limiting, and CORS handling
- **Monitoring**: CloudWatch metrics and logging

## 🔧 **API Endpoints**

### **GET /health**
Aggregated health check for all services.

**Response:**
```json
{
  "success": true,
  "data": {
    "overall_status": "healthy",
    "timestamp": "2023-08-15T10:30:00Z",
    "total_services": 6,
    "healthy_services": 6,
    "unhealthy_services": 0,
    "services": [
      {
        "service_name": "auth",
        "status": "healthy",
        "response_time_ms": 45.2,
        "last_check": "2023-08-15T10:30:00Z"
      }
    ]
  }
}
```

### **GET /health?service=auth**
Health check for specific service.

### **GET /api/config**
Get current API Gateway configuration.

**Response:**
```json
{
  "success": true,
  "data": {
    "configuration": {
      "stage": "dev",
      "region": "us-east-1",
      "api_id": "abc123def456",
      "services": [...],
      "routes": [...]
    },
    "metrics": {
      "total_requests": 1250,
      "error_rate": 2.4
    }
  }
}
```

### **POST /api/config**
Update API Gateway configuration.

### **GET /docs**
API documentation index.

### **GET /docs/openapi.json**
OpenAPI 3.0 specification in JSON format.

### **GET /docs/openapi.yaml**
OpenAPI 3.0 specification in YAML format.

### **GET /docs/swagger**
Interactive Swagger UI documentation.

## 🛣️ **Routing Configuration**

### **Service Routes**

**Auth Service:**
- `POST /auth/register` → `auth-service/auth/register`
- `POST /auth/login` → `auth-service/auth/login`
- `GET /auth/profile` → `auth-service/auth/profile`

**Tenant Service:**
- `GET /tenant/profile` → `tenant-service/tenant/profile`
- `GET /tenant/users` → `tenant-service/tenant/users`
- `POST /tenant/users` → `tenant-service/tenant/users`

**Payment Service:**
- `GET /payment/plans` → `payment-service/payment/plans`
- `POST /payment/subscriptions` → `payment-service/payment/subscriptions`
- `POST /payment/webhooks/stripe` → `payment-service/payment/webhooks/stripe`

**Orchestrator Service:**
- `POST /registration/complete` → `orchestrator-service/registration/complete`
- `GET /registration/status/{id}` → `orchestrator-service/registration/status/{id}`

**Setup Service:**
- `POST /setup/tenant/{id}` → `setup-service/setup/tenant/{id}`
- `GET /setup/status/{id}` → `setup-service/setup/status/{id}`

**Jobs Service:**
- `POST /jobs/execute` → `jobs-service/jobs/execute`
- `GET /jobs/status` → `jobs-service/jobs/status`

## 🔒 **Security Features**

### **WAF Protection**
- **Rate Limiting**: 2000 requests per IP per 5 minutes
- **Common Attack Protection**: SQL injection, XSS, etc.
- **IP Whitelisting**: Configurable IP restrictions
- **Geographic Blocking**: Block requests from specific countries

### **CORS Configuration**
```yaml
cors:
  origin: '*'
  headers:
    - Content-Type
    - Authorization
    - X-Api-Key
    - X-Request-ID
    - X-Tenant-ID
  methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  maxAge: 86400
```

### **Rate Limiting**
- **Global**: 1000 requests/minute per IP
- **Per Service**: Configurable limits per service
- **Authenticated Users**: Higher limits for authenticated requests
- **Burst Protection**: Temporary spike handling

## 📊 **Monitoring & Metrics**

### **CloudWatch Metrics**
- `RequestCount` - Total API requests
- `4XXError` - Client error count
- `5XXError` - Server error count
- `Latency` - Request latency
- `IntegrationLatency` - Backend service latency

### **Custom Metrics**
- `ServiceHealth` - Individual service health status
- `RouteLatency` - Per-route response times
- `ErrorRate` - Error rate by service
- `ThroughputByService` - Requests per service

### **Alarms**
- High error rate (>5% in 5 minutes)
- High latency (>2 seconds average)
- Service unavailability
- WAF blocked requests spike

## 🚀 **Deployment**

### **Environment Variables**
```bash
# API Gateway
API_GATEWAY_ID=abc123def456
STAGE=dev
REGION=us-east-1

# Service Endpoints
AUTH_SERVICE_URL=https://api.dev.com/auth
TENANT_SERVICE_URL=https://api.dev.com/tenant
PAYMENT_SERVICE_URL=https://api.dev.com/payment
ORCHESTRATOR_SERVICE_URL=https://api.dev.com/orchestrator
SETUP_SERVICE_URL=https://api.dev.com/setup
JOBS_SERVICE_URL=https://api.dev.com/jobs
```

### **Deploy Commands**
```bash
# Install dependencies
npm install

# Deploy to dev
serverless deploy --stage dev

# Deploy to production
serverless deploy --stage prod

# Deploy custom domain
serverless create_domain --stage prod
```

## 🧪 **Testing**

### **Health Check Testing**
```bash
# Test overall health
curl https://api.dev.com/health

# Test specific service health
curl https://api.dev.com/health?service=auth

# Test with detailed output
curl -s https://api.dev.com/health | jq '.data.services'
```

### **Configuration Testing**
```bash
# Get current configuration
curl https://api.dev.com/api/config

# Test routing
curl -X POST https://api.dev.com/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

### **Documentation Testing**
```bash
# Access API documentation
curl https://api.dev.com/docs

# Get OpenAPI spec
curl https://api.dev.com/docs/openapi.json

# View Swagger UI
open https://api.dev.com/docs/swagger
```

## 🔧 **Configuration Management**

### **Route Configuration**
```python
RouteConfig(
    path="/auth/login",
    method=HttpMethod.POST,
    service_name="auth",
    target_path="/auth/login",
    auth_required=False,
    rate_limit=100,  # requests per minute
    cache_ttl=None   # no caching
)
```

### **Service Configuration**
```python
ServiceEndpoint(
    service_name="auth",
    base_url="${AUTH_SERVICE_URL}",
    health_check_path="/health",
    timeout_seconds=30,
    retry_attempts=3
)
```

## 🐛 **Troubleshooting**

### **Common Issues**

**Service Routing Failures**
- Check service endpoint configuration
- Verify service health status
- Review CloudWatch logs for errors

**High Latency**
- Check backend service performance
- Review integration latency metrics
- Verify network connectivity

**Authentication Errors**
- Verify JWT token format
- Check token expiration
- Review auth service logs

### **Debug Commands**
```bash
# Check API Gateway status
aws apigateway get-rest-api --rest-api-id abc123def456

# View CloudWatch logs
aws logs filter-log-events \
  --log-group-name /aws/apigateway/agent-scl-dev \
  --start-time *************

# Check WAF metrics
aws wafv2 get-web-acl \
  --scope REGIONAL \
  --id abc123def456

# Test service connectivity
curl -I https://auth-service.dev.com/health
```

## 📈 **Performance Optimization**

### **Caching Strategy**
- **Static Content**: Cache API documentation and specs
- **Health Checks**: Cache health status for 30 seconds
- **Configuration**: Cache routing configuration for 5 minutes
- **Service Discovery**: Cache service endpoints for 10 minutes

### **Connection Pooling**
- **Keep-Alive**: Enable HTTP keep-alive for backend connections
- **Connection Limits**: Configure appropriate connection pool sizes
- **Timeout Tuning**: Optimize timeout values for different services

## 🔄 **Future Enhancements**

- [ ] Advanced routing with weighted traffic distribution
- [ ] API versioning support
- [ ] Request/response transformation
- [ ] Circuit breaker pattern implementation
- [ ] Advanced caching with Redis
- [ ] Real-time API analytics dashboard
- [ ] Custom authentication providers
- [ ] GraphQL federation support
- [ ] Multi-region deployment
- [ ] Blue-green deployment support
