#!/usr/bin/env python3
# infrastructure/api-gateway/src/handlers/config_manager.py
# API Gateway configuration manager handler

"""
API Gateway configuration manager handler.
Provides endpoints for viewing and updating API Gateway configuration.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException
from shared.validators import validate_required_fields

from ..services.config_service import ApiGatewayConfigService
from ..models.api_models import ApiGatewayConfig


@rate_limit(requests_per_minute=30)  # Moderate rate limiting for config operations
@measure_performance("api_gateway_config_manager")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    API Gateway configuration manager handler.
    
    GET /api/config - Get current configuration
    POST /api/config - Update configuration
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    http_method = event.get('httpMethod', 'GET')
    
    try:
        # Log API request
        log_api_request(event, "config_manager")
        
        lambda_logger.info("Processing API Gateway config request", extra={
            'request_id': request_id,
            'http_method': http_method
        })
        
        config_service = ApiGatewayConfigService()
        
        if http_method == 'GET':
            # Get current configuration
            response_data = _handle_get_config(config_service)
            
        elif http_method == 'POST':
            # Update configuration
            body = event.get('body', '{}')
            if isinstance(body, str):
                data = json.loads(body)
            else:
                data = body
            
            response_data = _handle_update_config(config_service, data)
            
        else:
            raise ValidationException(f"HTTP method {http_method} not supported")
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Configuration operation completed successfully"
        )
        
        # Log API response
        log_api_response(response, "config_manager")
        
        lambda_logger.info("API Gateway config request completed", extra={
            'request_id': request_id,
            'http_method': http_method
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Config manager validation failed", extra={
            'request_id': request_id,
            'http_method': http_method,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "config_manager")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Config manager platform error", extra={
            'request_id': request_id,
            'http_method': http_method,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Configuration operation failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "config_manager")
        return response
        
    except Exception as e:
        lambda_logger.error("Config manager unexpected error", extra={
            'request_id': request_id,
            'http_method': http_method,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "config_manager")
        return response


def _handle_get_config(config_service: ApiGatewayConfigService) -> Dict[str, Any]:
    """Handle GET configuration request."""
    try:
        # Get current configuration
        config = config_service.get_current_config()
        
        # Get API metrics
        metrics = config_service.get_api_metrics()
        
        return {
            'configuration': config.to_dict(),
            'metrics': metrics,
            'timestamp': lambda_logger.get_timestamp(),
            'summary': {
                'total_services': len(config.services),
                'total_routes': len(config.routes),
                'api_id': config.api_id,
                'stage': config.stage,
                'region': config.region
            }
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get configuration", extra={
            'error': str(e)
        })
        raise


def _handle_update_config(config_service: ApiGatewayConfigService, data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST configuration update request."""
    try:
        # Validate required fields
        validate_required_fields(data, ['configuration'])
        
        config_data = data['configuration']
        
        # Create configuration object
        config = ApiGatewayConfig(
            stage=config_data.get('stage'),
            region=config_data.get('region'),
            api_id=config_data.get('api_id'),
            domain_name=config_data.get('domain_name'),
            cors_enabled=config_data.get('cors_enabled', True),
            throttling_enabled=config_data.get('throttling_enabled', True),
            waf_enabled=config_data.get('waf_enabled', True)
        )
        
        # Update configuration
        update_result = config_service.update_config(config)
        
        # Get updated configuration
        updated_config = config_service.get_current_config()
        
        return {
            'configuration': updated_config.to_dict(),
            'update_result': update_result,
            'timestamp': lambda_logger.get_timestamp(),
            'message': 'Configuration updated successfully'
        }
        
    except Exception as e:
        lambda_logger.error("Failed to update configuration", extra={
            'error': str(e)
        })
        raise
