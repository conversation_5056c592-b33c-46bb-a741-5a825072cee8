#!/usr/bin/env python3
# infrastructure/api-gateway/src/handlers/docs_generator.py
# API documentation generator handler

"""
API documentation generator handler.
Generates and serves API documentation in various formats.
"""

import json
import yaml
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException

from ..services.config_service import ApiGatewayConfigService


@rate_limit(requests_per_minute=60)  # Higher limit for documentation access
@measure_performance("api_gateway_docs_generator")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    API documentation generator handler.
    
    GET /docs - Get API documentation index
    GET /docs/openapi.json - Get OpenAPI specification in JSON
    GET /docs/openapi.yaml - Get OpenAPI specification in YAML
    GET /docs/swagger - Get Swagger UI HTML
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    path = event.get('path', '/docs')
    
    try:
        # Log API request
        log_api_request(event, "docs_generator")
        
        lambda_logger.info("Processing documentation request", extra={
            'request_id': request_id,
            'path': path
        })
        
        config_service = ApiGatewayConfigService()
        
        # Route based on path
        if path == '/docs' or path == '/docs/':
            response_data = _handle_docs_index(config_service)
            content_type = 'application/json'
            
        elif path == '/docs/openapi.json':
            response_data = _handle_openapi_json(config_service)
            content_type = 'application/json'
            
        elif path == '/docs/openapi.yaml':
            response_data = _handle_openapi_yaml(config_service)
            content_type = 'application/yaml'
            
        elif path == '/docs/swagger':
            response_data = _handle_swagger_ui()
            content_type = 'text/html'
            
        else:
            raise ValidationException(f"Documentation path not found: {path}")
        
        # Create response based on content type
        if content_type == 'text/html':
            # Return HTML directly
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': content_type,
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                    'Access-Control-Allow-Methods': 'GET,OPTIONS'
                },
                'body': response_data
            }
        elif content_type == 'application/yaml':
            # Return YAML as plain text
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': content_type,
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                    'Access-Control-Allow-Methods': 'GET,OPTIONS'
                },
                'body': response_data
            }
        else:
            # Return JSON response
            response = APIResponse.success(
                data=response_data,
                message="Documentation retrieved successfully"
            )
            
            # Log API response
            log_api_response(response, "docs_generator")
            
            return response
        
    except ValidationException as e:
        lambda_logger.warning("Docs generator validation failed", extra={
            'request_id': request_id,
            'path': path,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Documentation not found",
            details={'error': str(e)},
            status_code=404
        )
        log_api_response(response, "docs_generator")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Docs generator platform error", extra={
            'request_id': request_id,
            'path': path,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Documentation generation failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "docs_generator")
        return response
        
    except Exception as e:
        lambda_logger.error("Docs generator unexpected error", extra={
            'request_id': request_id,
            'path': path,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "docs_generator")
        return response


def _handle_docs_index(config_service: ApiGatewayConfigService) -> Dict[str, Any]:
    """Handle documentation index request."""
    try:
        # Get API documentation
        docs = config_service.get_api_documentation()
        
        return {
            'title': 'Logistics AI Platform API Documentation',
            'description': 'Comprehensive API documentation for the logistics AI platform',
            'version': '1.0.0',
            'generated_at': docs['generated_at'],
            'api_id': docs['api_id'],
            'stage': docs['stage'],
            'total_endpoints': docs['total_endpoints'],
            'available_formats': [
                {
                    'format': 'OpenAPI JSON',
                    'url': '/docs/openapi.json',
                    'description': 'OpenAPI 3.0 specification in JSON format'
                },
                {
                    'format': 'OpenAPI YAML',
                    'url': '/docs/openapi.yaml',
                    'description': 'OpenAPI 3.0 specification in YAML format'
                },
                {
                    'format': 'Swagger UI',
                    'url': '/docs/swagger',
                    'description': 'Interactive API documentation with Swagger UI'
                }
            ],
            'services': _get_service_summary(docs['openapi_spec'])
        }
        
    except Exception as e:
        lambda_logger.error("Failed to generate docs index", extra={
            'error': str(e)
        })
        raise


def _handle_openapi_json(config_service: ApiGatewayConfigService) -> Dict[str, Any]:
    """Handle OpenAPI JSON specification request."""
    try:
        docs = config_service.get_api_documentation()
        return docs['openapi_spec']
        
    except Exception as e:
        lambda_logger.error("Failed to generate OpenAPI JSON", extra={
            'error': str(e)
        })
        raise


def _handle_openapi_yaml(config_service: ApiGatewayConfigService) -> str:
    """Handle OpenAPI YAML specification request."""
    try:
        docs = config_service.get_api_documentation()
        openapi_spec = docs['openapi_spec']
        
        # Convert to YAML
        yaml_content = yaml.dump(openapi_spec, default_flow_style=False, sort_keys=False)
        return yaml_content
        
    except Exception as e:
        lambda_logger.error("Failed to generate OpenAPI YAML", extra={
            'error': str(e)
        })
        raise


def _handle_swagger_ui() -> str:
    """Handle Swagger UI HTML request."""
    try:
        # Generate Swagger UI HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logistics AI Platform API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {{
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }}
        *, *:before, *:after {{
            box-sizing: inherit;
        }}
        body {{
            margin:0;
            background: #fafafa;
        }}
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {{
            const ui = SwaggerUIBundle({{
                url: '/docs/openapi.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                tryItOutEnabled: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                onComplete: function() {{
                    console.log('Swagger UI loaded successfully');
                }},
                onFailure: function(error) {{
                    console.error('Failed to load Swagger UI:', error);
                }}
            }});
        }};
    </script>
</body>
</html>
        """
        
        return html_content.strip()
        
    except Exception as e:
        lambda_logger.error("Failed to generate Swagger UI", extra={
            'error': str(e)
        })
        raise


def _get_service_summary(openapi_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Get summary of services from OpenAPI specification."""
    try:
        services = {}
        
        # Extract services from paths
        paths = openapi_spec.get('paths', {})
        for path, methods in paths.items():
            for method, spec in methods.items():
                if isinstance(spec, dict) and 'tags' in spec:
                    for tag in spec['tags']:
                        if tag not in services:
                            services[tag] = {
                                'name': tag,
                                'endpoints': 0,
                                'methods': set()
                            }
                        services[tag]['endpoints'] += 1
                        services[tag]['methods'].add(method.upper())
        
        # Convert to list and format
        service_list = []
        for service_name, service_data in services.items():
            service_list.append({
                'name': service_name,
                'endpoints': service_data['endpoints'],
                'methods': sorted(list(service_data['methods']))
            })
        
        return sorted(service_list, key=lambda x: x['name'])
        
    except Exception as e:
        lambda_logger.warning("Failed to generate service summary", extra={
            'error': str(e)
        })
        return []
