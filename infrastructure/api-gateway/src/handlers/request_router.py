#!/usr/bin/env python3
# infrastructure/api-gateway/src/handlers/request_router.py
# Request router handler

"""
Request router handler for API Gateway.
Routes requests to appropriate microservices based on configuration.
"""

import json
import requests
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, ExternalServiceException

from ..services.config_service import ApiGatewayConfigService
from ..models.api_models import HttpMethod


@rate_limit(requests_per_minute=1000)  # High limit for routing
@measure_performance("api_gateway_request_router")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Request router handler.
    
    Routes incoming requests to appropriate microservices.
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    http_method = event.get('httpMethod', 'GET')
    path = event.get('path', '/')
    
    try:
        # Log API request
        log_api_request(event, "request_router")
        
        lambda_logger.info("Processing route request", extra={
            'request_id': request_id,
            'http_method': http_method,
            'path': path
        })
        
        # Get routing configuration
        config_service = ApiGatewayConfigService()
        config = config_service.get_current_config()
        
        # Find matching route
        route = _find_matching_route(config.routes, http_method, path)
        if not route:
            raise ValidationException(f"No route found for {http_method} {path}")
        
        # Find target service
        service = _find_service(config.services, route.service_name)
        if not service:
            raise ValidationException(f"Service not found: {route.service_name}")
        
        # Route the request
        response_data = _route_request(event, service, route)
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Request routed successfully"
        )
        
        # Log API response
        log_api_response(response, "request_router")
        
        lambda_logger.info("Route request completed", extra={
            'request_id': request_id,
            'http_method': http_method,
            'path': path,
            'target_service': route.service_name
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Request router validation failed", extra={
            'request_id': request_id,
            'http_method': http_method,
            'path': path,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Route not found",
            details={'error': str(e)},
            status_code=404
        )
        log_api_response(response, "request_router")
        return response
        
    except ExternalServiceException as e:
        lambda_logger.error("Request router service error", extra={
            'request_id': request_id,
            'http_method': http_method,
            'path': path,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Service unavailable",
            details={'error': str(e)},
            status_code=503
        )
        log_api_response(response, "request_router")
        return response
        
    except Exception as e:
        lambda_logger.error("Request router unexpected error", extra={
            'request_id': request_id,
            'http_method': http_method,
            'path': path,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "request_router")
        return response


def _find_matching_route(routes, http_method: str, path: str):
    """Find matching route for the request."""
    try:
        method_enum = HttpMethod(http_method)
        
        for route in routes:
            if route.method == method_enum and _path_matches(route.path, path):
                return route
        
        return None
        
    except ValueError:
        # Invalid HTTP method
        return None


def _path_matches(route_path: str, request_path: str) -> bool:
    """Check if request path matches route path pattern."""
    try:
        # Simple path matching - in a real implementation, this would be more sophisticated
        # Handle path parameters like {user_id}
        
        route_parts = route_path.strip('/').split('/')
        request_parts = request_path.strip('/').split('/')
        
        if len(route_parts) != len(request_parts):
            return False
        
        for route_part, request_part in zip(route_parts, request_parts):
            # Check for path parameters
            if route_part.startswith('{') and route_part.endswith('}'):
                # This is a path parameter, it matches any value
                continue
            elif route_part != request_part:
                return False
        
        return True
        
    except Exception:
        return False


def _find_service(services, service_name: str):
    """Find service configuration by name."""
    for service in services:
        if service.service_name == service_name:
            return service
    return None


def _route_request(event: Dict[str, Any], service, route) -> Dict[str, Any]:
    """Route the request to the target service."""
    try:
        # Resolve environment variables in service URL
        import os
        import re
        
        def replace_env_var(match):
            env_var = match.group(1)
            return os.environ.get(env_var, match.group(0))
        
        base_url = re.sub(r'\$\{([^}]+)\}', replace_env_var, service.base_url)
        
        # Build target URL
        target_path = _build_target_path(route.target_path, event.get('path', '/'))
        target_url = f"{base_url.rstrip('/')}{target_path}"
        
        # Prepare request data
        method = event.get('httpMethod', 'GET')
        headers = _prepare_headers(event)
        query_params = event.get('queryStringParameters') or {}
        body = event.get('body')
        
        lambda_logger.info("Routing request to service", extra={
            'target_url': target_url,
            'method': method,
            'service': service.service_name
        })
        
        # Make the request
        response = requests.request(
            method=method,
            url=target_url,
            headers=headers,
            params=query_params,
            data=body,
            timeout=service.timeout_seconds
        )
        
        # Handle response
        try:
            response_data = response.json()
        except:
            response_data = {'message': response.text}
        
        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'body': response_data,
            'service': service.service_name,
            'target_url': target_url,
            'response_time_ms': response.elapsed.total_seconds() * 1000
        }
        
    except requests.exceptions.Timeout:
        raise ExternalServiceException(f"Service timeout: {service.service_name}")
    except requests.exceptions.ConnectionError:
        raise ExternalServiceException(f"Service connection error: {service.service_name}")
    except Exception as e:
        raise ExternalServiceException(f"Service request failed: {str(e)}")


def _build_target_path(target_path: str, request_path: str) -> str:
    """Build target path with path parameters."""
    try:
        # Simple path parameter substitution
        # In a real implementation, this would extract parameters from the request path
        # and substitute them in the target path
        
        # For now, just return the target path
        return target_path
        
    except Exception:
        return target_path


def _prepare_headers(event: Dict[str, Any]) -> Dict[str, str]:
    """Prepare headers for the target service request."""
    try:
        headers = {}
        
        # Get original headers
        original_headers = event.get('headers', {})
        
        # Forward important headers
        forward_headers = [
            'authorization',
            'content-type',
            'x-tenant-id',
            'x-user-id',
            'x-request-id'
        ]
        
        for header_name in forward_headers:
            # Check both lowercase and original case
            value = original_headers.get(header_name) or original_headers.get(header_name.title())
            if value:
                headers[header_name] = value
        
        # Add API Gateway specific headers
        headers['x-forwarded-by'] = 'api-gateway'
        headers['x-original-path'] = event.get('path', '/')
        
        return headers
        
    except Exception as e:
        lambda_logger.warning("Failed to prepare headers", extra={
            'error': str(e)
        })
        return {'content-type': 'application/json'}
