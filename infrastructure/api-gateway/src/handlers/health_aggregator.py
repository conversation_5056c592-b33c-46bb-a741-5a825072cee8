#!/usr/bin/env python3
# infrastructure/api-gateway/src/handlers/health_aggregator.py
# Health check aggregator handler

"""
Health check aggregator handler.
Aggregates health status from all microservices and provides overall system health.
"""

from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import PlatformException

from ..services.health_service import HealthAggregationService
from ..models.api_models import ServiceStatus


@rate_limit(requests_per_minute=60)  # Higher limit for health checks
@measure_performance("api_gateway_health_aggregator")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Health check aggregator handler.
    
    GET /health
    GET /health?service=auth
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "health_aggregator")
        
        # Extract query parameters
        query_params = event.get('queryStringParameters') or {}
        service_filter = query_params.get('service')
        
        lambda_logger.info("Processing health check request", extra={
            'request_id': request_id,
            'service_filter': service_filter
        })
        
        health_service = HealthAggregationService()
        
        if service_filter:
            # Get health for specific service
            response_data = _handle_service_health(health_service, service_filter)
        else:
            # Get aggregated health for all services
            response_data = _handle_aggregated_health(health_service)
        
        # Determine HTTP status code based on health
        status_code = _get_status_code_from_health(response_data)
        
        # Create response
        response = APIResponse.success(
            data=response_data,
            message="Health check completed"
        )
        
        # Override status code if unhealthy
        if status_code != 200:
            response['statusCode'] = status_code
        
        # Log API response
        log_api_response(response, "health_aggregator")
        
        lambda_logger.info("Health check request completed", extra={
            'request_id': request_id,
            'service_filter': service_filter,
            'status_code': status_code
        })
        
        return response
        
    except PlatformException as e:
        lambda_logger.error("Health aggregator platform error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Health check failed",
            details={'error': str(e)},
            status_code=503
        )
        log_api_response(response, "health_aggregator")
        return response
        
    except Exception as e:
        lambda_logger.error("Health aggregator unexpected error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Health check failed",
            details={'error': 'An unexpected error occurred'},
            status_code=503
        )
        log_api_response(response, "health_aggregator")
        return response


def _handle_aggregated_health(health_service: HealthAggregationService) -> Dict[str, Any]:
    """Handle aggregated health check for all services."""
    try:
        # Get health check results
        health_result = health_service.check_all_services_health()
        
        # Prepare response data
        response_data = health_result.to_dict()
        
        # Add additional metadata
        response_data['api_gateway'] = {
            'status': 'healthy',
            'timestamp': lambda_logger.get_timestamp(),
            'version': '1.0.0'
        }
        
        # Add summary statistics
        response_data['summary'] = {
            'overall_status': health_result.overall_status.value,
            'healthy_percentage': round((health_result.healthy_services / health_result.total_services * 100), 2) if health_result.total_services > 0 else 0,
            'response_time_avg': _calculate_average_response_time(health_result.services),
            'services_by_status': _group_services_by_status(health_result.services)
        }
        
        return response_data
        
    except Exception as e:
        lambda_logger.error("Failed to get aggregated health", extra={
            'error': str(e)
        })
        raise


def _handle_service_health(health_service: HealthAggregationService, service_name: str) -> Dict[str, Any]:
    """Handle health check for a specific service."""
    try:
        # Get service details
        service_details = health_service.get_service_details(service_name)
        
        if 'error' in service_details:
            return {
                'service_name': service_name,
                'status': 'not_found',
                'error': service_details['error'],
                'available_services': service_details.get('available_services', []),
                'timestamp': lambda_logger.get_timestamp()
            }
        
        return {
            'service_details': service_details,
            'timestamp': lambda_logger.get_timestamp()
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get service health", extra={
            'service_name': service_name,
            'error': str(e)
        })
        raise


def _get_status_code_from_health(response_data: Dict[str, Any]) -> int:
    """Determine HTTP status code based on health status."""
    try:
        # Check if this is a service-specific response
        if 'service_details' in response_data:
            health_status = response_data['service_details'].get('health_status', {}).get('status')
            if health_status == 'healthy':
                return 200
            elif health_status == 'degraded':
                return 200  # Still return 200 for degraded but functional
            else:
                return 503
        
        # Check overall status for aggregated response
        overall_status = response_data.get('overall_status')
        if overall_status == ServiceStatus.HEALTHY.value:
            return 200
        elif overall_status == ServiceStatus.DEGRADED.value:
            return 200  # Still return 200 for degraded but functional
        elif overall_status in [ServiceStatus.UNHEALTHY.value, ServiceStatus.UNKNOWN.value]:
            return 503
        else:
            return 200  # Default to 200
            
    except Exception as e:
        lambda_logger.warning("Failed to determine status code from health", extra={
            'error': str(e)
        })
        return 503  # Default to unhealthy if we can't determine


def _calculate_average_response_time(services: list) -> float:
    """Calculate average response time across services."""
    try:
        response_times = [
            service.get('response_time_ms', 0) 
            for service in services 
            if service.get('response_time_ms') is not None
        ]
        
        if not response_times:
            return 0.0
        
        return round(sum(response_times) / len(response_times), 2)
        
    except Exception as e:
        lambda_logger.warning("Failed to calculate average response time", extra={
            'error': str(e)
        })
        return 0.0


def _group_services_by_status(services: list) -> Dict[str, int]:
    """Group services by their health status."""
    try:
        status_counts = {
            'healthy': 0,
            'degraded': 0,
            'unhealthy': 0,
            'unknown': 0
        }
        
        for service in services:
            status = service.get('status', 'unknown')
            if status in status_counts:
                status_counts[status] += 1
            else:
                status_counts['unknown'] += 1
        
        return status_counts
        
    except Exception as e:
        lambda_logger.warning("Failed to group services by status", extra={
            'error': str(e)
        })
        return {'healthy': 0, 'degraded': 0, 'unhealthy': 0, 'unknown': 0}
