#!/usr/bin/env python3
# infrastructure/api-gateway/src/models/api_models.py
# API Gateway configuration models

"""
API Gateway configuration models.
Defines the data structures for API Gateway management and routing.
"""

from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class ServiceStatus(Enum):
    """Service status enumeration."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


class HttpMethod(Enum):
    """HTTP method enumeration."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    OPTIONS = "OPTIONS"
    HEAD = "HEAD"


@dataclass
class ServiceEndpoint:
    """Service endpoint configuration."""
    service_name: str
    base_url: str
    health_check_path: str = "/health"
    timeout_seconds: int = 30
    retry_attempts: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class RouteConfig:
    """API route configuration."""
    path: str
    method: HttpMethod
    service_name: str
    target_path: str
    auth_required: bool = True
    rate_limit: Optional[int] = None
    cache_ttl: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['method'] = self.method.value
        return data


@dataclass
class ServiceHealth:
    """Service health status."""
    service_name: str
    status: ServiceStatus
    response_time_ms: Optional[float]
    last_check: datetime
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['last_check'] = self.last_check.isoformat()
        return data


@dataclass
class ApiGatewayConfig:
    """API Gateway configuration."""
    stage: str
    region: str
    api_id: str
    domain_name: Optional[str] = None
    cors_enabled: bool = True
    throttling_enabled: bool = True
    waf_enabled: bool = True
    
    # Service endpoints
    services: List[ServiceEndpoint] = None
    
    # Route configurations
    routes: List[RouteConfig] = None
    
    def __post_init__(self):
        if self.services is None:
            self.services = []
        if self.routes is None:
            self.routes = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['services'] = [service.to_dict() for service in self.services]
        data['routes'] = [route.to_dict() for route in self.routes]
        return data


@dataclass
class HealthCheckResult:
    """Health check aggregation result."""
    overall_status: ServiceStatus
    timestamp: datetime
    services: List[ServiceHealth]
    total_services: int
    healthy_services: int
    unhealthy_services: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['overall_status'] = self.overall_status.value
        data['timestamp'] = self.timestamp.isoformat()
        data['services'] = [service.to_dict() for service in self.services]
        return data


# Default service configurations
DEFAULT_SERVICES = [
    ServiceEndpoint(
        service_name="auth",
        base_url="${AUTH_SERVICE_URL}",
        health_check_path="/health"
    ),
    ServiceEndpoint(
        service_name="tenant",
        base_url="${TENANT_SERVICE_URL}",
        health_check_path="/health"
    ),
    ServiceEndpoint(
        service_name="payment",
        base_url="${PAYMENT_SERVICE_URL}",
        health_check_path="/health"
    ),
    ServiceEndpoint(
        service_name="orchestrator",
        base_url="${ORCHESTRATOR_SERVICE_URL}",
        health_check_path="/health"
    ),
    ServiceEndpoint(
        service_name="setup",
        base_url="${SETUP_SERVICE_URL}",
        health_check_path="/health"
    ),
    ServiceEndpoint(
        service_name="jobs",
        base_url="${JOBS_SERVICE_URL}",
        health_check_path="/health"
    )
]

# Default route configurations
DEFAULT_ROUTES = [
    # Auth service routes
    RouteConfig("/auth/register", HttpMethod.POST, "auth", "/auth/register", auth_required=False),
    RouteConfig("/auth/login", HttpMethod.POST, "auth", "/auth/login", auth_required=False),
    RouteConfig("/auth/verify-email", HttpMethod.POST, "auth", "/auth/verify-email", auth_required=False),
    RouteConfig("/auth/refresh", HttpMethod.POST, "auth", "/auth/refresh", auth_required=False),
    RouteConfig("/auth/logout", HttpMethod.POST, "auth", "/auth/logout"),
    RouteConfig("/auth/profile", HttpMethod.GET, "auth", "/auth/profile"),
    RouteConfig("/auth/profile", HttpMethod.PUT, "auth", "/auth/profile"),
    
    # Tenant service routes
    RouteConfig("/tenant/register", HttpMethod.POST, "tenant", "/tenant/register", auth_required=False),
    RouteConfig("/tenant/profile", HttpMethod.GET, "tenant", "/tenant/profile"),
    RouteConfig("/tenant/profile", HttpMethod.PUT, "tenant", "/tenant/profile"),
    RouteConfig("/tenant/users", HttpMethod.GET, "tenant", "/tenant/users"),
    RouteConfig("/tenant/users", HttpMethod.POST, "tenant", "/tenant/users"),
    RouteConfig("/tenant/users/{user_id}", HttpMethod.GET, "tenant", "/tenant/users/{user_id}"),
    RouteConfig("/tenant/users/{user_id}", HttpMethod.PUT, "tenant", "/tenant/users/{user_id}"),
    RouteConfig("/tenant/users/{user_id}", HttpMethod.DELETE, "tenant", "/tenant/users/{user_id}"),
    
    # Payment service routes
    RouteConfig("/payment/plans", HttpMethod.GET, "payment", "/payment/plans", auth_required=False),
    RouteConfig("/payment/subscriptions", HttpMethod.GET, "payment", "/payment/subscriptions"),
    RouteConfig("/payment/subscriptions", HttpMethod.POST, "payment", "/payment/subscriptions"),
    RouteConfig("/payment/subscriptions/{subscription_id}", HttpMethod.GET, "payment", "/payment/subscriptions/{subscription_id}"),
    RouteConfig("/payment/subscriptions/{subscription_id}", HttpMethod.PUT, "payment", "/payment/subscriptions/{subscription_id}"),
    RouteConfig("/payment/subscriptions/{subscription_id}", HttpMethod.DELETE, "payment", "/payment/subscriptions/{subscription_id}"),
    RouteConfig("/payment/webhooks/stripe", HttpMethod.POST, "payment", "/payment/webhooks/stripe", auth_required=False),
    
    # Orchestrator service routes
    RouteConfig("/registration/complete", HttpMethod.POST, "orchestrator", "/registration/complete", auth_required=False),
    RouteConfig("/registration/verify", HttpMethod.POST, "orchestrator", "/registration/verify", auth_required=False),
    RouteConfig("/registration/payment", HttpMethod.POST, "orchestrator", "/registration/payment"),
    RouteConfig("/registration/status/{registration_id}", HttpMethod.GET, "orchestrator", "/registration/status/{registration_id}", auth_required=False),
    RouteConfig("/registration/{registration_id}", HttpMethod.DELETE, "orchestrator", "/registration/{registration_id}"),
    
    # Setup service routes
    RouteConfig("/setup/tenant/{tenant_id}", HttpMethod.POST, "setup", "/setup/tenant/{tenant_id}"),
    RouteConfig("/setup/tenant/{tenant_id}", HttpMethod.DELETE, "setup", "/setup/tenant/{tenant_id}"),
    RouteConfig("/setup/status/{tenant_id}", HttpMethod.GET, "setup", "/setup/status/{tenant_id}"),
    
    # Jobs service routes
    RouteConfig("/jobs/execute", HttpMethod.POST, "jobs", "/jobs/execute"),
    RouteConfig("/jobs/status", HttpMethod.GET, "jobs", "/jobs/status"),
]


def get_default_config(stage: str, region: str, api_id: str) -> ApiGatewayConfig:
    """Get default API Gateway configuration."""
    return ApiGatewayConfig(
        stage=stage,
        region=region,
        api_id=api_id,
        services=DEFAULT_SERVICES.copy(),
        routes=DEFAULT_ROUTES.copy()
    )
