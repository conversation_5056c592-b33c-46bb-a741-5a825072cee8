#!/usr/bin/env python3
# infrastructure/api-gateway/src/services/health_service.py
# Health check aggregation service

"""
Health check aggregation service.
Aggregates health status from all microservices and provides overall system health.
"""

import os
import asyncio
import aiohttp
import requests
from typing import Dict, Any, List
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from shared.logger import lambda_logger
from ..models.api_models import ServiceHealth, ServiceStatus, HealthCheckResult, ServiceEndpoint


class HealthAggregationService:
    """Service for aggregating health checks from all microservices."""
    
    def __init__(self):
        self.timeout = 10  # seconds
        self.max_workers = 6  # parallel health checks
        
        # Service endpoints from environment
        self.service_endpoints = self._get_service_endpoints()
    
    def check_all_services_health(self) -> HealthCheckResult:
        """Check health of all services and aggregate results."""
        try:
            lambda_logger.info("Starting health check aggregation", extra={
                'services_count': len(self.service_endpoints)
            })
            
            # Perform health checks in parallel
            service_healths = self._check_services_parallel()
            
            # Calculate overall status
            overall_status = self._calculate_overall_status(service_healths)
            
            # Count service statuses
            healthy_count = sum(1 for h in service_healths if h.status == ServiceStatus.HEALTHY)
            unhealthy_count = len(service_healths) - healthy_count
            
            result = HealthCheckResult(
                overall_status=overall_status,
                timestamp=datetime.utcnow(),
                services=service_healths,
                total_services=len(service_healths),
                healthy_services=healthy_count,
                unhealthy_services=unhealthy_count
            )
            
            lambda_logger.info("Health check aggregation completed", extra={
                'overall_status': overall_status.value,
                'healthy_services': healthy_count,
                'unhealthy_services': unhealthy_count,
                'total_services': len(service_healths)
            })
            
            return result
            
        except Exception as e:
            lambda_logger.error("Failed to aggregate health checks", extra={
                'error': str(e)
            })
            
            # Return degraded status with error
            return HealthCheckResult(
                overall_status=ServiceStatus.UNKNOWN,
                timestamp=datetime.utcnow(),
                services=[],
                total_services=0,
                healthy_services=0,
                unhealthy_services=0
            )
    
    def check_service_health(self, service_endpoint: ServiceEndpoint) -> ServiceHealth:
        """Check health of a single service."""
        start_time = datetime.utcnow()
        
        try:
            # Resolve environment variables in URL
            base_url = self._resolve_env_vars(service_endpoint.base_url)
            health_url = f"{base_url.rstrip('/')}{service_endpoint.health_check_path}"
            
            lambda_logger.info("Checking service health", extra={
                'service': service_endpoint.service_name,
                'url': health_url
            })
            
            # Make health check request
            response = requests.get(
                health_url,
                timeout=service_endpoint.timeout_seconds,
                headers={'User-Agent': 'API-Gateway-Health-Check'}
            )
            
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Determine status based on response
            if response.status_code == 200:
                status = ServiceStatus.HEALTHY
                error_message = None
                
                # Try to parse response for additional details
                try:
                    details = response.json()
                except:
                    details = {'status_code': response.status_code}
            
            elif response.status_code == 503:
                status = ServiceStatus.DEGRADED
                error_message = f"Service degraded (HTTP {response.status_code})"
                details = {'status_code': response.status_code}
            
            else:
                status = ServiceStatus.UNHEALTHY
                error_message = f"HTTP {response.status_code}: {response.text[:100]}"
                details = {'status_code': response.status_code}
            
            return ServiceHealth(
                service_name=service_endpoint.service_name,
                status=status,
                response_time_ms=response_time,
                last_check=datetime.utcnow(),
                error_message=error_message,
                details=details
            )
            
        except requests.exceptions.Timeout:
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            return ServiceHealth(
                service_name=service_endpoint.service_name,
                status=ServiceStatus.UNHEALTHY,
                response_time_ms=response_time,
                last_check=datetime.utcnow(),
                error_message="Request timeout",
                details={'error_type': 'timeout'}
            )
            
        except requests.exceptions.ConnectionError:
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            return ServiceHealth(
                service_name=service_endpoint.service_name,
                status=ServiceStatus.UNHEALTHY,
                response_time_ms=response_time,
                last_check=datetime.utcnow(),
                error_message="Connection error",
                details={'error_type': 'connection_error'}
            )
            
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            return ServiceHealth(
                service_name=service_endpoint.service_name,
                status=ServiceStatus.UNKNOWN,
                response_time_ms=response_time,
                last_check=datetime.utcnow(),
                error_message=str(e),
                details={'error_type': type(e).__name__}
            )
    
    def _check_services_parallel(self) -> List[ServiceHealth]:
        """Check all services in parallel."""
        service_healths = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit health check tasks
            future_to_service = {
                executor.submit(self.check_service_health, endpoint): endpoint
                for endpoint in self.service_endpoints
            }
            
            # Collect results
            for future in as_completed(future_to_service):
                try:
                    health = future.result()
                    service_healths.append(health)
                except Exception as e:
                    endpoint = future_to_service[future]
                    lambda_logger.error("Health check failed for service", extra={
                        'service': endpoint.service_name,
                        'error': str(e)
                    })
                    
                    # Add failed health check
                    service_healths.append(ServiceHealth(
                        service_name=endpoint.service_name,
                        status=ServiceStatus.UNKNOWN,
                        response_time_ms=None,
                        last_check=datetime.utcnow(),
                        error_message=str(e),
                        details={'error_type': 'health_check_failed'}
                    ))
        
        return service_healths
    
    def _calculate_overall_status(self, service_healths: List[ServiceHealth]) -> ServiceStatus:
        """Calculate overall system status based on individual service health."""
        if not service_healths:
            return ServiceStatus.UNKNOWN
        
        status_counts = {
            ServiceStatus.HEALTHY: 0,
            ServiceStatus.DEGRADED: 0,
            ServiceStatus.UNHEALTHY: 0,
            ServiceStatus.UNKNOWN: 0
        }
        
        for health in service_healths:
            status_counts[health.status] += 1
        
        total_services = len(service_healths)
        healthy_ratio = status_counts[ServiceStatus.HEALTHY] / total_services
        
        # Determine overall status based on healthy service ratio
        if healthy_ratio >= 0.9:  # 90% or more healthy
            return ServiceStatus.HEALTHY
        elif healthy_ratio >= 0.7:  # 70-89% healthy
            return ServiceStatus.DEGRADED
        elif status_counts[ServiceStatus.UNKNOWN] > total_services / 2:  # More than half unknown
            return ServiceStatus.UNKNOWN
        else:  # Less than 70% healthy
            return ServiceStatus.UNHEALTHY
    
    def _get_service_endpoints(self) -> List[ServiceEndpoint]:
        """Get service endpoints from environment variables."""
        endpoints = []
        
        service_mappings = {
            'auth': 'AUTH_SERVICE_URL',
            'tenant': 'TENANT_SERVICE_URL',
            'payment': 'PAYMENT_SERVICE_URL',
            'orchestrator': 'ORCHESTRATOR_SERVICE_URL',
            'setup': 'SETUP_SERVICE_URL',
            'jobs': 'JOBS_SERVICE_URL'
        }
        
        for service_name, env_var in service_mappings.items():
            base_url = os.environ.get(env_var)
            if base_url:
                endpoints.append(ServiceEndpoint(
                    service_name=service_name,
                    base_url=base_url,
                    health_check_path="/health",
                    timeout_seconds=self.timeout
                ))
            else:
                lambda_logger.warning("Service URL not configured", extra={
                    'service': service_name,
                    'env_var': env_var
                })
        
        return endpoints
    
    def _resolve_env_vars(self, url: str) -> str:
        """Resolve environment variables in URL."""
        # Simple environment variable resolution
        # In a real implementation, this might be more sophisticated
        import re
        
        def replace_env_var(match):
            env_var = match.group(1)
            return os.environ.get(env_var, match.group(0))
        
        # Replace ${VAR_NAME} patterns
        resolved_url = re.sub(r'\$\{([^}]+)\}', replace_env_var, url)
        
        return resolved_url
    
    def get_service_details(self, service_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific service."""
        try:
            # Find service endpoint
            endpoint = None
            for ep in self.service_endpoints:
                if ep.service_name == service_name:
                    endpoint = ep
                    break
            
            if not endpoint:
                return {
                    'error': f'Service {service_name} not found',
                    'available_services': [ep.service_name for ep in self.service_endpoints]
                }
            
            # Check service health
            health = self.check_service_health(endpoint)
            
            # Get additional service information
            base_url = self._resolve_env_vars(endpoint.base_url)
            
            return {
                'service_name': service_name,
                'base_url': base_url,
                'health_check_path': endpoint.health_check_path,
                'timeout_seconds': endpoint.timeout_seconds,
                'health_status': health.to_dict(),
                'last_checked': health.last_check.isoformat()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get service details", extra={
                'service_name': service_name,
                'error': str(e)
            })
            return {
                'error': str(e),
                'service_name': service_name
            }
