#!/usr/bin/env python3
# infrastructure/api-gateway/src/services/config_service.py
# API Gateway configuration service

"""
API Gateway configuration service.
Manages API Gateway configuration, routing, and service integration.
"""

import os
import boto3
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from shared.logger import lambda_logger
from shared.exceptions import ValidationException, ExternalServiceException
from ..models.api_models import (
    ApiGatewayConfig, ServiceEndpoint, RouteConfig, 
    get_default_config, HttpMethod
)


class ApiGatewayConfigService:
    """Service for managing API Gateway configuration."""
    
    def __init__(self):
        self.apigateway = boto3.client('apigateway')
        self.stage = os.environ.get('STAGE', 'dev')
        self.region = os.environ.get('REGION', 'us-east-1')
        self.api_id = os.environ.get('API_GATEWAY_ID')
        
        if not self.api_id:
            raise ValueError("API_GATEWAY_ID environment variable not set")
    
    def get_current_config(self) -> ApiGatewayConfig:
        """Get current API Gateway configuration."""
        try:
            lambda_logger.info("Getting current API Gateway configuration", extra={
                'api_id': self.api_id,
                'stage': self.stage
            })
            
            # Get API Gateway details
            api_details = self.apigateway.get_rest_api(restApiId=self.api_id)
            
            # Get stage details
            stage_details = self.apigateway.get_stage(
                restApiId=self.api_id,
                stageName=self.stage
            )
            
            # Create configuration object
            config = ApiGatewayConfig(
                stage=self.stage,
                region=self.region,
                api_id=self.api_id,
                domain_name=api_details.get('name'),
                services=self._get_service_endpoints(),
                routes=self._get_route_configurations()
            )
            
            lambda_logger.info("API Gateway configuration retrieved", extra={
                'api_id': self.api_id,
                'services_count': len(config.services),
                'routes_count': len(config.routes)
            })
            
            return config
            
        except Exception as e:
            lambda_logger.error("Failed to get API Gateway configuration", extra={
                'api_id': self.api_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Failed to get API Gateway configuration: {str(e)}")
    
    def update_config(self, config: ApiGatewayConfig) -> Dict[str, Any]:
        """Update API Gateway configuration."""
        try:
            lambda_logger.info("Updating API Gateway configuration", extra={
                'api_id': self.api_id,
                'services_count': len(config.services),
                'routes_count': len(config.routes)
            })
            
            # Validate configuration
            self._validate_config(config)
            
            # Update API Gateway settings
            update_result = {
                'api_updated': False,
                'stage_updated': False,
                'routes_updated': 0,
                'errors': []
            }
            
            # Update API description
            try:
                self.apigateway.update_rest_api(
                    restApiId=self.api_id,
                    patchOps=[
                        {
                            'op': 'replace',
                            'path': '/description',
                            'value': f"API Gateway for {config.stage} environment"
                        }
                    ]
                )
                update_result['api_updated'] = True
            except Exception as e:
                update_result['errors'].append(f"Failed to update API: {str(e)}")
            
            # Update stage configuration
            try:
                self.apigateway.update_stage(
                    restApiId=self.api_id,
                    stageName=self.stage,
                    patchOps=[
                        {
                            'op': 'replace',
                            'path': '/description',
                            'value': f"Stage for {config.stage} environment"
                        }
                    ]
                )
                update_result['stage_updated'] = True
            except Exception as e:
                update_result['errors'].append(f"Failed to update stage: {str(e)}")
            
            # Note: Route updates would typically be handled by individual services
            # This is more for configuration validation and monitoring
            
            lambda_logger.info("API Gateway configuration update completed", extra={
                'api_id': self.api_id,
                'update_result': update_result
            })
            
            return update_result
            
        except Exception as e:
            lambda_logger.error("Failed to update API Gateway configuration", extra={
                'api_id': self.api_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Failed to update API Gateway configuration: {str(e)}")
    
    def get_api_documentation(self) -> Dict[str, Any]:
        """Generate API documentation."""
        try:
            lambda_logger.info("Generating API documentation", extra={
                'api_id': self.api_id
            })
            
            config = self.get_current_config()
            
            # Generate OpenAPI specification
            openapi_spec = {
                "openapi": "3.0.0",
                "info": {
                    "title": "Logistics AI Platform API",
                    "version": "1.0.0",
                    "description": "Comprehensive API for the logistics AI platform",
                    "contact": {
                        "name": "Platform Team",
                        "email": "<EMAIL>"
                    }
                },
                "servers": [
                    {
                        "url": f"https://{self.api_id}.execute-api.{self.region}.amazonaws.com/{self.stage}",
                        "description": f"{self.stage.title()} environment"
                    }
                ],
                "paths": {},
                "components": {
                    "securitySchemes": {
                        "BearerAuth": {
                            "type": "http",
                            "scheme": "bearer",
                            "bearerFormat": "JWT"
                        }
                    }
                }
            }
            
            # Add paths from routes
            for route in config.routes:
                path = route.path
                method = route.method.value.lower()
                
                if path not in openapi_spec["paths"]:
                    openapi_spec["paths"][path] = {}
                
                openapi_spec["paths"][path][method] = {
                    "summary": f"{method.upper()} {path}",
                    "description": f"Proxies to {route.service_name} service",
                    "tags": [route.service_name],
                    "responses": {
                        "200": {
                            "description": "Successful response"
                        },
                        "400": {
                            "description": "Bad request"
                        },
                        "401": {
                            "description": "Unauthorized"
                        },
                        "500": {
                            "description": "Internal server error"
                        }
                    }
                }
                
                if route.auth_required:
                    openapi_spec["paths"][path][method]["security"] = [{"BearerAuth": []}]
            
            return {
                "openapi_spec": openapi_spec,
                "generated_at": datetime.utcnow().isoformat(),
                "api_id": self.api_id,
                "stage": self.stage,
                "total_endpoints": len(config.routes)
            }
            
        except Exception as e:
            lambda_logger.error("Failed to generate API documentation", extra={
                'api_id': self.api_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Failed to generate API documentation: {str(e)}")
    
    def _get_service_endpoints(self) -> List[ServiceEndpoint]:
        """Get service endpoint configurations from environment."""
        services = []
        
        service_mappings = {
            'auth': 'AUTH_SERVICE_URL',
            'tenant': 'TENANT_SERVICE_URL',
            'payment': 'PAYMENT_SERVICE_URL',
            'orchestrator': 'ORCHESTRATOR_SERVICE_URL',
            'setup': 'SETUP_SERVICE_URL',
            'jobs': 'JOBS_SERVICE_URL'
        }
        
        for service_name, env_var in service_mappings.items():
            base_url = os.environ.get(env_var)
            if base_url:
                services.append(ServiceEndpoint(
                    service_name=service_name,
                    base_url=base_url,
                    health_check_path="/health"
                ))
        
        return services
    
    def _get_route_configurations(self) -> List[RouteConfig]:
        """Get route configurations (would typically come from a database or config file)."""
        # For now, return default routes
        # In a real implementation, this might come from a database or configuration service
        from ..models.api_models import DEFAULT_ROUTES
        return DEFAULT_ROUTES.copy()
    
    def _validate_config(self, config: ApiGatewayConfig) -> None:
        """Validate API Gateway configuration."""
        if not config.api_id:
            raise ValidationException("API ID is required")
        
        if not config.stage:
            raise ValidationException("Stage is required")
        
        if not config.region:
            raise ValidationException("Region is required")
        
        # Validate services
        service_names = set()
        for service in config.services:
            if not service.service_name:
                raise ValidationException("Service name is required")
            
            if service.service_name in service_names:
                raise ValidationException(f"Duplicate service name: {service.service_name}")
            
            service_names.add(service.service_name)
            
            if not service.base_url:
                raise ValidationException(f"Base URL is required for service: {service.service_name}")
        
        # Validate routes
        route_keys = set()
        for route in config.routes:
            if not route.path:
                raise ValidationException("Route path is required")
            
            if not route.service_name:
                raise ValidationException(f"Service name is required for route: {route.path}")
            
            if route.service_name not in service_names:
                raise ValidationException(f"Unknown service for route {route.path}: {route.service_name}")
            
            route_key = f"{route.method.value}:{route.path}"
            if route_key in route_keys:
                raise ValidationException(f"Duplicate route: {route_key}")
            
            route_keys.add(route_key)
    
    def get_api_metrics(self) -> Dict[str, Any]:
        """Get API Gateway metrics."""
        try:
            cloudwatch = boto3.client('cloudwatch')
            
            # Get metrics for the last 24 hours
            from datetime import timedelta
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=24)
            
            metrics = {}
            
            # Get request count
            try:
                response = cloudwatch.get_metric_statistics(
                    Namespace='AWS/ApiGateway',
                    MetricName='Count',
                    Dimensions=[
                        {'Name': 'ApiName', 'Value': self.api_id},
                        {'Name': 'Stage', 'Value': self.stage}
                    ],
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,
                    Statistics=['Sum']
                )
                
                total_requests = sum(point['Sum'] for point in response['Datapoints'])
                metrics['total_requests'] = total_requests
                
            except Exception as e:
                metrics['total_requests'] = 0
                lambda_logger.warning("Failed to get request count metric", extra={'error': str(e)})
            
            # Get error count
            try:
                response = cloudwatch.get_metric_statistics(
                    Namespace='AWS/ApiGateway',
                    MetricName='4XXError',
                    Dimensions=[
                        {'Name': 'ApiName', 'Value': self.api_id},
                        {'Name': 'Stage', 'Value': self.stage}
                    ],
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,
                    Statistics=['Sum']
                )
                
                client_errors = sum(point['Sum'] for point in response['Datapoints'])
                metrics['client_errors'] = client_errors
                
            except Exception as e:
                metrics['client_errors'] = 0
                lambda_logger.warning("Failed to get 4XX error metric", extra={'error': str(e)})
            
            # Get server error count
            try:
                response = cloudwatch.get_metric_statistics(
                    Namespace='AWS/ApiGateway',
                    MetricName='5XXError',
                    Dimensions=[
                        {'Name': 'ApiName', 'Value': self.api_id},
                        {'Name': 'Stage', 'Value': self.stage}
                    ],
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,
                    Statistics=['Sum']
                )
                
                server_errors = sum(point['Sum'] for point in response['Datapoints'])
                metrics['server_errors'] = server_errors
                
            except Exception as e:
                metrics['server_errors'] = 0
                lambda_logger.warning("Failed to get 5XX error metric", extra={'error': str(e)})
            
            # Calculate error rate
            total_errors = metrics['client_errors'] + metrics['server_errors']
            error_rate = (total_errors / metrics['total_requests'] * 100) if metrics['total_requests'] > 0 else 0
            
            metrics['error_rate'] = round(error_rate, 2)
            metrics['time_range'] = '24 hours'
            metrics['start_time'] = start_time.isoformat()
            metrics['end_time'] = end_time.isoformat()
            
            return metrics
            
        except Exception as e:
            lambda_logger.error("Failed to get API metrics", extra={
                'api_id': self.api_id,
                'error': str(e)
            })
            return {
                'error': str(e),
                'total_requests': 0,
                'client_errors': 0,
                'server_errors': 0,
                'error_rate': 0
            }
