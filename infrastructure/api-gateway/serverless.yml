service: api-gateway

frameworkVersion: '3'

custom:
  projectName: agent-scl
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  
  # Domain configuration
  customDomain:
    domainName: ${self:custom.domainName.${self:custom.stage}, ''}
    certificateName: ${self:custom.certificateName.${self:custom.stage}, ''}
    createRoute53Record: true
    endpointType: 'regional'
    securityPolicy: tls_1_2
    apiType: rest
    autoDomain: false
  
  # Domain names per environment
  domainName:
    dev: api-dev.theplatform.com
    staging: api-staging.theplatform.com
    prod: api.theplatform.com
  
  # SSL certificates per environment
  certificateName:
    dev: '*.theplatform.com'
    staging: '*.theplatform.com'
    prod: '*.theplatform.com'
  
  # Shared layer configuration
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

provider:
  name: aws
  runtime: python3.11
  region: ${self:custom.region}
  stage: ${self:custom.stage}
  
  # API Gateway configuration
  apiGateway:
    restApiId: ${self:custom.restApiId}
    restApiRootResourceId: ${self:custom.restApiRootResourceId}
    description: "Centralized API Gateway for ${self:custom.projectName} platform"
    binaryMediaTypes:
      - 'multipart/form-data'
      - 'application/octet-stream'
      - 'image/*'
    
    # Request validation
    requestValidators:
      validateRequestBody:
        validateRequestBody: true
        validateRequestParameters: false
      validateRequestParameters:
        validateRequestBody: false
        validateRequestParameters: true
      validateAll:
        validateRequestBody: true
        validateRequestParameters: true
    
    # CORS configuration
    cors:
      origin: '*'
      headers:
        - Content-Type
        - X-Amz-Date
        - Authorization
        - X-Api-Key
        - X-Amz-Security-Token
        - X-Amz-User-Agent
        - X-Request-ID
        - X-Tenant-ID
      allowCredentials: false
      maxAge: 86400
  
  # Environment variables
  environment:
    STAGE: ${self:custom.stage}
    REGION: ${self:custom.region}
    PROJECT_NAME: ${self:custom.projectName}
    
    # Service endpoints (imported from other stacks)
    AUTH_SERVICE_URL: ${cf:auth-${self:custom.stage}.ServiceEndpoint}
    TENANT_SERVICE_URL: ${cf:tenant-${self:custom.stage}.ServiceEndpoint}
    PAYMENT_SERVICE_URL: ${cf:payment-${self:custom.stage}.ServiceEndpoint}
    ORCHESTRATOR_SERVICE_URL: ${cf:orchestrator-${self:custom.stage}.ServiceEndpoint}
    SETUP_SERVICE_URL: ${cf:setup-${self:custom.stage}.ServiceEndpoint}
    JOBS_SERVICE_URL: ${cf:jobs-${self:custom.stage}.ServiceEndpoint}
    
    # API Gateway configuration
    API_GATEWAY_ID: ${self:custom.restApiId}
    
  # IAM permissions
  iam:
    role:
      statements:
        # CloudWatch Logs
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: "*"
        
        # CloudWatch Metrics
        - Effect: Allow
          Action:
            - cloudwatch:PutMetricData
          Resource: "*"
        
        # API Gateway management
        - Effect: Allow
          Action:
            - apigateway:GET
            - apigateway:POST
            - apigateway:PUT
            - apigateway:DELETE
            - apigateway:PATCH
          Resource: 
            - arn:aws:apigateway:${self:custom.region}::/restapis/${self:custom.restApiId}/*

# Custom API Gateway resources
custom:
  # Import existing API Gateway (created separately)
  restApiId: ${cf:api-gateway-base-${self:custom.stage}.RestApiId}
  restApiRootResourceId: ${cf:api-gateway-base-${self:custom.stage}.RestApiRootResourceId}

# Lambda functions for API Gateway management
functions:
  # API Gateway configuration manager
  configManager:
    handler: src/handlers/config_manager.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /api/config
          method: get
          cors: true
      - http:
          path: /api/config
          method: post
          cors: true
  
  # Request router and proxy
  requestRouter:
    handler: src/handlers/request_router.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /api/route
          method: any
          cors: true
  
  # Health check aggregator
  healthAggregator:
    handler: src/handlers/health_aggregator.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /health
          method: get
          cors: true
  
  # API documentation generator
  docsGenerator:
    handler: src/handlers/docs_generator.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /docs
          method: get
          cors: true
      - http:
          path: /docs/{proxy+}
          method: get
          cors: true

# Resources
resources:
  Resources:
    # API Gateway base resources (if not imported)
    RestApi:
      Type: AWS::ApiGateway::RestApi
      Condition: CreateNewApi
      Properties:
        Name: ${self:custom.projectName}-api-${self:custom.stage}
        Description: "Centralized API Gateway for ${self:custom.projectName} platform"
        EndpointConfiguration:
          Types:
            - REGIONAL
        Policy:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal: '*'
              Action: 'execute-api:Invoke'
              Resource: '*'
        BinaryMediaTypes:
          - 'multipart/form-data'
          - 'application/octet-stream'
          - 'image/*'
    
    # API Gateway deployment
    ApiGatewayDeployment:
      Type: AWS::ApiGateway::Deployment
      Properties:
        RestApiId: ${self:custom.restApiId}
        StageName: ${self:custom.stage}
        StageDescription:
          Description: "API Gateway stage for ${self:custom.stage}"
          Variables:
            stage: ${self:custom.stage}
            project: ${self:custom.projectName}
          MethodSettings:
            - ResourcePath: "/*"
              HttpMethod: "*"
              LoggingLevel: INFO
              DataTraceEnabled: true
              MetricsEnabled: true
              ThrottlingBurstLimit: 1000
              ThrottlingRateLimit: 500
          AccessLogSetting:
            DestinationArn: !GetAtt ApiGatewayLogGroup.Arn
            Format: >
              {
                "requestId": "$context.requestId",
                "ip": "$context.identity.sourceIp",
                "caller": "$context.identity.caller",
                "user": "$context.identity.user",
                "requestTime": "$context.requestTime",
                "httpMethod": "$context.httpMethod",
                "resourcePath": "$context.resourcePath",
                "status": "$context.status",
                "protocol": "$context.protocol",
                "responseLength": "$context.responseLength",
                "responseTime": "$context.responseTime",
                "error": "$context.error.message",
                "integrationError": "$context.integration.error"
              }
    
    # CloudWatch Log Group for API Gateway
    ApiGatewayLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/apigateway/${self:custom.projectName}-${self:custom.stage}
        RetentionInDays: 14
    
    # WAF Web ACL for API Gateway protection
    ApiGatewayWebAcl:
      Type: AWS::WAFv2::WebACL
      Properties:
        Name: ${self:custom.projectName}-api-waf-${self:custom.stage}
        Scope: REGIONAL
        DefaultAction:
          Allow: {}
        Rules:
          # Rate limiting rule
          - Name: RateLimitRule
            Priority: 1
            Statement:
              RateBasedStatement:
                Limit: 2000
                AggregateKeyType: IP
            Action:
              Block: {}
            VisibilityConfig:
              SampledRequestsEnabled: true
              CloudWatchMetricsEnabled: true
              MetricName: RateLimitRule
          
          # AWS Managed Rules - Core Rule Set
          - Name: AWSManagedRulesCommonRuleSet
            Priority: 2
            OverrideAction:
              None: {}
            Statement:
              ManagedRuleGroupStatement:
                VendorName: AWS
                Name: AWSManagedRulesCommonRuleSet
            VisibilityConfig:
              SampledRequestsEnabled: true
              CloudWatchMetricsEnabled: true
              MetricName: CommonRuleSetMetric
        
        VisibilityConfig:
          SampledRequestsEnabled: true
          CloudWatchMetricsEnabled: true
          MetricName: ${self:custom.projectName}ApiWaf
    
    # Associate WAF with API Gateway
    ApiGatewayWebAclAssociation:
      Type: AWS::WAFv2::WebACLAssociation
      Properties:
        ResourceArn: !Sub 
          - arn:aws:apigateway:${AWS::Region}::/restapis/${RestApiId}/stages/${Stage}
          - RestApiId: ${self:custom.restApiId}
            Stage: ${self:custom.stage}
        WebACLArn: !GetAtt ApiGatewayWebAcl.Arn

  # Conditions
  Conditions:
    CreateNewApi: !Equals [${self:custom.restApiId}, '']

  # Outputs
  Outputs:
    RestApiId:
      Description: "API Gateway REST API ID"
      Value: ${self:custom.restApiId}
      Export:
        Name: sls-${self:custom.projectName}-api-gateway-${self:custom.stage}-RestApiId
    
    RestApiRootResourceId:
      Description: "API Gateway REST API Root Resource ID"
      Value: ${self:custom.restApiRootResourceId}
      Export:
        Name: sls-${self:custom.projectName}-api-gateway-${self:custom.stage}-RestApiRootResourceId
    
    ApiGatewayUrl:
      Description: "API Gateway URL"
      Value: !Sub 
        - https://${RestApiId}.execute-api.${AWS::Region}.amazonaws.com/${Stage}
        - RestApiId: ${self:custom.restApiId}
          Stage: ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-api-gateway-${self:custom.stage}-Url
    
    CustomDomainUrl:
      Description: "Custom Domain URL"
      Value: https://${self:custom.domainName.${self:custom.stage}}
      Condition: CreateNewApi
      Export:
        Name: sls-${self:custom.projectName}-api-gateway-${self:custom.stage}-CustomDomainUrl

plugins:
  - serverless-python-requirements
  - serverless-domain-manager

package:
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.pytest_cache/**'
    - '!tests/**'
    - '!*.md'
    - '!.env*'
