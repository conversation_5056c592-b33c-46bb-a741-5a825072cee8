# Testing Guide

## Overview

This guide covers the testing strategy, tools, and best practices for the Platform Multitenant Agents project. Our testing approach includes unit tests, integration tests, and infrastructure tests to ensure code quality and reliability.

## Testing Strategy

### Test Pyramid

```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/__________\ Unit Tests (Many)
```

- **Unit Tests (70%)**: Test individual functions and classes in isolation
- **Integration Tests (20%)**: Test component interactions and API endpoints
- **Infrastructure Tests (10%)**: Test AWS infrastructure and deployment

## Current Test Status

- **Total Tests**: 170
- **Passing Tests**: 86 (50.6%)
- **Test Coverage**: 53%
- **Target Coverage**: 80%

### Coverage by Module

| Module | Coverage | Status |
|--------|----------|--------|
| `src/auth/services/password_service.py` | 91% | ✅ Excellent |
| `src/shared/config.py` | 87% | ✅ Good |
| `src/shared/auth.py` | 77% | ✅ Good |
| `src/payment/models/plan.py` | 74% | ⚠️ Needs improvement |
| `src/shared/logger.py` | 69% | ⚠️ Needs improvement |
| `src/payment/models/subscription.py` | 68% | ⚠️ Needs improvement |
| `src/shared/validators.py` | 57% | ❌ Poor |
| `src/auth/models/tenant.py` | 54% | ❌ Poor |
| `src/shared/exceptions.py` | 48% | ❌ Poor |
| `src/auth/models/user.py` | 44% | ❌ Poor |

## Test Structure

```
tests/
├── unit/                         # Unit tests (isolated)
│   ├── auth/
│   │   ├── models/
│   │   │   ├── test_user.py
│   │   │   └── test_tenant.py
│   │   └── services/
│   │       └── test_password_service.py
│   ├── payment/
│   │   └── models/
│   │       └── test_plan.py
│   └── shared/
│       └── test_validators.py
├── integration/                  # Integration tests
│   ├── api/
│   │   ├── test_auth_endpoints.py
│   │   └── test_user_endpoints.py
│   └── database/
│       └── test_dynamodb_operations.py
└── infrastructure/               # Infrastructure tests
    ├── test_aws_connectivity.py
    ├── test_deployment.py
    └── test_terraform_validation.py
```

## Running Tests

### Quick Commands

```bash
# Run all tests
python scripts/run_tests.py

# Run specific test types
python scripts/run_tests.py --types unit
python scripts/run_tests.py --types integration
python scripts/run_tests.py --types infrastructure

# Run with coverage report
python scripts/run_tests.py --coverage

# Run tests with verbose output
python scripts/run_tests.py --verbose
```

### Detailed Commands

```bash
# Run unit tests only
pytest tests/unit/ -v

# Run integration tests only
pytest tests/integration/ -v

# Run infrastructure tests only
pytest tests/infrastructure/ -v

# Run specific test file
pytest tests/unit/auth/models/test_user.py -v

# Run specific test method
pytest tests/unit/auth/models/test_user.py::TestUser::test_user_creation -v

# Run with coverage
pytest tests/unit/ --cov=src --cov-report=html --cov-report=term-missing

# Run with debugging
pytest tests/unit/auth/models/test_user.py -v -s --pdb
```

## Unit Tests

Unit tests focus on testing individual functions and classes in isolation using mocks and stubs.

### Example Unit Test

```python
# tests/unit/auth/services/test_password_service.py
"""Unit tests for password service."""

import pytest
from unittest.mock import Mock, patch

from src.auth.services.password_service import PasswordService
from src.shared.exceptions import ValidationException


class TestPasswordService:
    """Unit tests for Password Service."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.password_service = PasswordService()
        self.test_password = "TestPassword123!"
    
    def test_hash_password(self):
        """Test password hashing."""
        hashed = self.password_service.hash_password(self.test_password)
        
        assert hashed is not None
        assert isinstance(hashed, str)
        assert len(hashed) > 50
        assert hashed != self.test_password
        assert hashed.startswith('$2b$')
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        hashed = self.password_service.hash_password(self.test_password)
        
        result = self.password_service.verify_password(self.test_password, hashed)
        
        assert result is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        hashed = self.password_service.hash_password(self.test_password)
        
        result = self.password_service.verify_password("WrongPassword123!", hashed)
        
        assert result is False
    
    def test_validate_password_strength_weak(self):
        """Test password strength validation with weak passwords."""
        weak_passwords = [
            "123",
            "password",
            "abc123",
            "12345678"
        ]
        
        for weak_password in weak_passwords:
            with pytest.raises(ValidationException):
                self.password_service.validate_password_strength(weak_password)
    
    @patch('src.auth.services.password_service.requests.get')
    def test_check_password_breach(self, mock_get):
        """Test password breach checking (mocked)."""
        # Mock API response for breached password
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "5E884898DA28047151D0E56F8DC6292773603D0D6AABBDD62A11EF721D1542D8:3"
        mock_get.return_value = mock_response
        
        # This should raise an exception for breached password
        with pytest.raises(ValidationException):
            self.password_service.check_password_breach("password")
```

### Unit Test Best Practices

1. **Test one thing at a time**: Each test should verify a single behavior
2. **Use descriptive names**: Test names should clearly describe what is being tested
3. **Follow AAA pattern**: Arrange, Act, Assert
4. **Mock external dependencies**: Use mocks for database, API calls, etc.
5. **Test edge cases**: Include boundary conditions and error scenarios
6. **Keep tests independent**: Tests should not depend on each other

## Integration Tests

Integration tests verify that different components work together correctly.

### Example Integration Test

```python
# tests/integration/api/test_auth_endpoints.py
"""Integration tests for authentication endpoints."""

import pytest
import json
from unittest.mock import patch

from src.shared.database import db_client
from tests.helpers.api_client import APIClient


class TestAuthEndpoints:
    """Integration tests for authentication API."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.api_client = APIClient()
        self.test_user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "name": "Test User",
            "company": "Test Company"
        }
    
    @patch('src.shared.database.db_client.put_item')
    @patch('src.shared.database.db_client.query')
    def test_register_user_success(self, mock_query, mock_put):
        """Test successful user registration."""
        # Mock database responses
        mock_query.return_value = {'Items': []}  # No existing user
        mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
        
        # Make API request
        response = self.api_client.post('/auth/register', self.test_user_data)
        
        # Verify response
        assert response.status_code == 201
        data = response.json()
        assert data['success'] is True
        assert 'user' in data['data']
        assert 'tenant' in data['data']
        assert 'tokens' in data['data']
        assert data['data']['user']['email'] == self.test_user_data['email']
    
    def test_register_user_invalid_email(self):
        """Test user registration with invalid email."""
        invalid_data = self.test_user_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        response = self.api_client.post('/auth/register', invalid_data)
        
        assert response.status_code == 400
        data = response.json()
        assert data['success'] is False
        assert 'VALIDATION_ERROR' in data['error']['code']
    
    @patch('src.shared.database.db_client.query')
    def test_login_success(self, mock_query):
        """Test successful user login."""
        # Mock user data in database
        mock_user_data = {
            'user_id': 'usr_123',
            'email': '<EMAIL>',
            'password_hash': '$2b$12$...',  # Hashed password
            'status': 'ACTIVE',
            'email_verified': True
        }
        mock_query.return_value = {'Items': [mock_user_data]}
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }
        
        with patch('src.auth.services.password_service.PasswordService.verify_password', return_value=True):
            response = self.api_client.post('/auth/login', login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        assert 'tokens' in data['data']
```

### Integration Test Best Practices

1. **Test realistic scenarios**: Use real data flows and interactions
2. **Mock external services**: Mock third-party APIs but not internal components
3. **Test error conditions**: Verify error handling and edge cases
4. **Use test databases**: Use separate test data that can be cleaned up
5. **Test authentication**: Verify security and authorization flows

## Infrastructure Tests

Infrastructure tests validate that AWS resources are properly configured and accessible.

### Example Infrastructure Test

```python
# tests/infrastructure/test_aws_connectivity.py
"""Infrastructure tests for AWS connectivity."""

import pytest
import boto3
from moto import mock_dynamodb, mock_s3

from src.shared.config import get_settings


class TestAWSConnectivity:
    """Test AWS service connectivity and configuration."""
    
    def test_aws_region_configuration(self):
        """Test AWS region configuration."""
        settings = get_settings()
        assert settings.aws_region is not None
        assert len(settings.aws_region) > 0
    
    @mock_dynamodb
    def test_dynamodb_connectivity(self):
        """Test DynamoDB connectivity."""
        # Create mock DynamoDB table
        dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
        table = dynamodb.create_table(
            TableName='test-table',
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Test table operations
        table.put_item(Item={'PK': 'TEST', 'SK': 'ITEM', 'data': 'test'})
        response = table.get_item(Key={'PK': 'TEST', 'SK': 'ITEM'})
        
        assert 'Item' in response
        assert response['Item']['data'] == 'test'
    
    @mock_s3
    def test_s3_connectivity(self):
        """Test S3 connectivity."""
        # Create mock S3 bucket
        s3 = boto3.client('s3', region_name='us-east-1')
        bucket_name = 'test-bucket'
        s3.create_bucket(Bucket=bucket_name)
        
        # Test bucket operations
        s3.put_object(Bucket=bucket_name, Key='test.txt', Body=b'test data')
        response = s3.get_object(Bucket=bucket_name, Key='test.txt')
        
        assert response['Body'].read() == b'test data'
```

## Test Configuration

### pytest Configuration

```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --strict-markers
    --disable-warnings
    --tb=short
markers =
    unit: Unit tests
    integration: Integration tests
    infrastructure: Infrastructure tests
    slow: Slow running tests
    aws: Tests that require AWS credentials
```

### Coverage Configuration

```ini
# .coveragerc
[run]
source = src
omit = 
    */tests/*
    */venv/*
    */__pycache__/*
    */migrations/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
```

## Test Data Management

### Fixtures

```python
# tests/conftest.py
"""Shared test fixtures."""

import pytest
import uuid
from unittest.mock import Mock

@pytest.fixture
def mock_db_client():
    """Mock database client."""
    return Mock()

@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        'user_id': str(uuid.uuid4()),
        'email': '<EMAIL>',
        'name': 'Test User',
        'tenant_id': str(uuid.uuid4())
    }

@pytest.fixture
def sample_tenant_data():
    """Sample tenant data for testing."""
    return {
        'tenant_id': str(uuid.uuid4()),
        'name': 'Test Company',
        'status': 'ACTIVE'
    }
```

### Test Helpers

```python
# tests/helpers/api_client.py
"""API client for integration tests."""

import requests
import json
from typing import Dict, Any


class APIClient:
    """Test API client."""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> requests.Response:
        """Make POST request."""
        url = f"{self.base_url}{endpoint}"
        return self.session.post(url, json=data)
    
    def get(self, endpoint: str, params: Dict[str, Any] = None) -> requests.Response:
        """Make GET request."""
        url = f"{self.base_url}{endpoint}"
        return self.session.get(url, params=params)
    
    def set_auth_token(self, token: str):
        """Set authentication token."""
        self.session.headers.update({'Authorization': f'Bearer {token}'})
```

## Continuous Integration

### GitHub Actions

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.12
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run unit tests
      run: pytest tests/unit/ --cov=src --cov-report=xml
    
    - name: Run integration tests
      run: pytest tests/integration/
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
      with:
        file: ./coverage.xml
```

## Performance Testing

### Load Testing

```python
# tests/performance/test_load.py
"""Load testing for API endpoints."""

import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor


async def load_test_endpoint(url: str, concurrent_requests: int = 10):
    """Load test an API endpoint."""
    async with aiohttp.ClientSession() as session:
        tasks = []
        start_time = time.time()
        
        for _ in range(concurrent_requests):
            task = session.get(url)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Analyze results
        success_count = sum(1 for r in responses if r.status == 200)
        total_time = end_time - start_time
        
        print(f"Requests: {concurrent_requests}")
        print(f"Successful: {success_count}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Requests/second: {concurrent_requests/total_time:.2f}")
```

## Troubleshooting Tests

### Common Issues

1. **Import errors**: Check PYTHONPATH and module structure
2. **Mock issues**: Verify mock paths and return values
3. **Database errors**: Ensure test database is properly set up
4. **Async issues**: Use proper async/await patterns
5. **Fixture conflicts**: Check fixture scopes and dependencies

### Debugging Tests

```bash
# Run with debugging
pytest tests/unit/auth/test_user.py -v -s --pdb

# Run with print statements
pytest tests/unit/auth/test_user.py -v -s

# Run single test with debugging
pytest tests/unit/auth/test_user.py::TestUser::test_creation -v -s --pdb
```

## Test Metrics and Reporting

### Coverage Reports

```bash
# Generate HTML coverage report
pytest tests/unit/ --cov=src --cov-report=html

# Generate terminal coverage report
pytest tests/unit/ --cov=src --cov-report=term-missing

# Generate XML coverage report (for CI)
pytest tests/unit/ --cov=src --cov-report=xml
```

### Test Reports

```bash
# Generate JUnit XML report
pytest tests/ --junitxml=test-results.xml

# Generate HTML test report
pytest tests/ --html=test-report.html --self-contained-html
```

## Next Steps

To improve test coverage to 80%:

1. **Add missing unit tests** for low-coverage modules
2. **Improve integration test coverage** for API endpoints
3. **Add end-to-end tests** for critical user flows
4. **Implement performance tests** for scalability validation
5. **Add security tests** for vulnerability assessment
