# Validation and Error Handling Migration Guide

## Overview

This guide explains how to migrate existing handlers to use the new unified validation and error handling system. The new system reduces boilerplate code by ~50-80% while providing consistent behavior across all services.

## New Features

### 1. Unified Decorators
- `@with_standard_handling()` - Complete handler wrapper with validation, auth, error handling
- `@with_validation_only()` - Simple validation decorator
- `@with_auth_only()` - Simple authentication decorator

### 2. Enhanced Validators
- Automatic sanitization (HTML, SQL injection protection)
- Consistent validation patterns
- Enhanced email, name, and string validation

### 3. Standardized Error Responses
- Consistent error codes across all services
- Pre-configured standard responses
- Automatic error logging and metrics

### 4. Sanitization Functions
- `sanitize_html()` - XSS protection
- `sanitize_sql()` - SQL injection protection
- `sanitize_string()` - General string sanitization

## Migration Examples

### Before: Traditional Handler (160+ lines)

```python
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    start_time = lambda_logger.get_current_timestamp()
    
    # Log API request
    log_api_request(lambda_logger, 'POST', '/user/profile', request_id=request_id)
    
    try:
        # Authentication
        auth_context = require_auth(event)
        
        # Validate request body
        body = event.get('body', '{}')
        validated_data = validate_request_body(body, UpdateProfileValidator)
        
        # Business logic
        user = User.get_by_id(auth_context.user_id, auth_context.tenant_id)
        if not user:
            return APIResponse.not_found(message="User not found")
        
        # Update user
        user.first_name = validated_data.get('first_name', user.first_name)
        user.last_name = validated_data.get('last_name', user.last_name)
        user.save()
        
        # Log successful response
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(lambda_logger, 'POST', '/user/profile', 200, duration_ms)
        
        return APIResponse.success(data=user.to_dict())
        
    except ValidationException as e:
        return APIResponse.validation_error(message=str(e))
    except AuthenticationException as e:
        return APIResponse.unauthorized(message=str(e))
    except Exception as e:
        lambda_logger.error("Unexpected error", extra={'error': str(e)})
        return APIResponse.internal_error()
```

### After: Unified Handler (40+ lines)

```python
from shared.decorators import with_standard_handling
from shared.responses import APIResponse, StandardResponses
from shared.validators import UpdateProfileValidator
from shared import User

@with_standard_handling(
    service_name="user",
    operation_name="update_profile",
    method="POST",
    validator=UpdateProfileValidator,
    auth_required=True
)
def handler(validated_data: Dict[str, Any]) -> Dict[str, Any]:
    """Update user profile - simplified with unified decorators."""
    
    # Extract validated data (already validated and sanitized)
    body = validated_data['body']
    auth_context = validated_data['auth']
    
    # Business logic only
    user = User.get_by_id(auth_context.user_id, auth_context.tenant_id)
    if not user:
        return StandardResponses.user_not_found()
    
    # Update user (data already sanitized)
    user.first_name = body.get('first_name', user.first_name)
    user.last_name = body.get('last_name', user.last_name)
    user.save()
    
    return APIResponse.success(
        data=user.to_dict(),
        message="Profile updated successfully"
    )
```

## Migration Steps

### Step 1: Update Imports

```python
# Add new imports
from shared.decorators import with_standard_handling
from shared.responses import StandardResponses, ErrorCodes
from shared.validators import EnhancedValidators
```

### Step 2: Replace Handler Decorator

```python
# Replace existing decorators with unified one
@with_standard_handling(
    service_name="your_service",
    operation_name="your_operation",
    method="POST",  # or GET, PUT, DELETE
    validator=YourValidator,  # or None for GET requests
    auth_required=True  # or False for public endpoints
)
def handler(validated_data: Dict[str, Any]) -> Dict[str, Any]:
    # Your business logic here
```

### Step 3: Update Handler Signature

```python
# Old signature
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:

# New signature
def handler(validated_data: Dict[str, Any]) -> Dict[str, Any]:
```

### Step 4: Extract Validated Data

```python
# Extract pre-validated data
body = validated_data.get('body', {})  # Validated request body
auth_context = validated_data.get('auth')  # Auth context if auth_required=True
path_params = validated_data.get('path_params', {})  # Path parameters
query_params = validated_data.get('query_params', {})  # Query parameters
event = validated_data['event']  # Original event for special cases
```

### Step 5: Use Standard Responses

```python
# Replace custom error responses with standard ones
return StandardResponses.user_not_found()
return StandardResponses.invalid_credentials()
return StandardResponses.insufficient_permissions()
return StandardResponses.validation_failed(errors)
```

### Step 6: Remove Boilerplate Code

Remove the following (handled automatically):
- CORS preflight handling
- Request/response logging
- Authentication logic
- Validation logic
- Error handling try/catch blocks
- Metrics collection

## Enhanced Validation Usage

### String Validation with Sanitization

```python
from shared.validators import EnhancedValidators

# Validate and sanitize name
clean_name = EnhancedValidators.validate_name_enhanced(
    name=user_input,
    field_name="First Name"
)

# Validate and sanitize email
clean_email = EnhancedValidators.validate_email_enhanced(user_email)

# Validate and sanitize description with HTML
clean_description = EnhancedValidators.validate_description_enhanced(
    description=user_description,
    max_length=500
)
```

### Manual Sanitization

```python
from shared.validators import sanitize_string, sanitize_html

# General string sanitization
clean_text = sanitize_string(
    text=user_input,
    max_length=100,
    allow_html=False
)

# HTML sanitization for rich content
clean_html = sanitize_html(user_html_content)
```

## Error Codes Reference

Use standardized error codes from `ErrorCodes` class:

```python
from shared.responses import ErrorCodes

# Authentication & Authorization
ErrorCodes.UNAUTHORIZED
ErrorCodes.FORBIDDEN
ErrorCodes.TOKEN_EXPIRED
ErrorCodes.ACCOUNT_LOCKED

# Validation
ErrorCodes.VALIDATION_FAILED
ErrorCodes.INVALID_EMAIL
ErrorCodes.FIELD_REQUIRED

# Resources
ErrorCodes.NOT_FOUND
ErrorCodes.ALREADY_EXISTS
ErrorCodes.CONFLICT

# Business Logic
ErrorCodes.INSUFFICIENT_PERMISSIONS
ErrorCodes.OPERATION_NOT_ALLOWED
```

## Benefits

1. **Reduced Code**: 50-80% less boilerplate code
2. **Consistency**: Same patterns across all handlers
3. **Security**: Automatic sanitization and validation
4. **Maintainability**: Centralized error handling and logging
5. **Performance**: Optimized validation and error handling
6. **Standards**: Consistent error codes and responses

## Next Steps

1. Start with new handlers using the unified approach
2. Gradually migrate existing handlers during maintenance
3. Update tests to work with new handler signatures
4. Monitor logs to ensure proper error handling

For questions or issues, refer to the shared layer documentation or contact the platform team.
