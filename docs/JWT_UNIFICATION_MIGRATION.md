# JWT Unification Migration Guide

## Overview

This guide explains how to migrate from fragmented JWT implementations to the new unified JWT manager. The unified system provides consistent JWT handling across all services while maintaining backward compatibility.

## Benefits of Unified JWT Manager

### 1. Consistency
- Single source of truth for JWT configuration
- Consistent token format across all services
- Unified error handling and logging

### 2. Security
- Multi-key support with automatic rotation
- Token blacklist for revocation
- Centralized secret management
- Automatic validation of token strength

### 3. Maintainability
- Reduced code duplication (60-80% less JWT code)
- Centralized configuration management
- Easier testing and debugging
- Single point for security updates

### 4. Features
- Access and refresh token types
- Automatic blacklist checking
- Multi-key validation with fallback
- Configurable expiry times
- AWS Secrets Manager integration

## Migration Examples

### Before: Fragmented JWT Implementation

```python
# Multiple different implementations across services

# In auth service - custom multi-key validation
def validate_token_with_multikey(token: str):
    config = get_jwt_config()
    # 50+ lines of validation logic...

# In user service - simple validation
payload = jwt_manager.decode_token(token)

# In admin service - manual validation
payload = jwt.decode(token, secret_key, algorithms=['HS256'])
```

### After: Unified JWT Implementation

```python
# Single unified approach across all services

from shared.jwt_manager import get_jwt_manager

# All services use the same pattern
jwt_manager = get_jwt_manager()
payload = jwt_manager.verify_token(token, token_type='access')
```

## Migration Steps

### Step 1: Update Imports

```python
# Old imports (various patterns)
from shared.auth import jwt_manager
import jwt
from serverless.shared.utils.jwt_utils import validate_token

# New unified import
from shared.jwt_manager import get_jwt_manager
```

### Step 2: Update Token Generation

```python
# Old patterns (inconsistent)
token = jwt_manager.create_access_token(user_id, tenant_id, email, role)
token = jwt_manager.generate_access_token(user_id, email, tenant_id, role)
token = generate_token(payload, expires_in_hours=24)

# New unified pattern
jwt_manager = get_jwt_manager()
token = jwt_manager.generate_access_token(user_id, email, tenant_id, role)
```

### Step 3: Update Token Validation

```python
# Old patterns (inconsistent)
payload = jwt_manager.decode_token(token)
payload = jwt_manager.verify_token(token, 'access')
payload = validate_token_with_multikey(token)

# New unified pattern
jwt_manager = get_jwt_manager()
payload = jwt_manager.verify_token(token, token_type='access')
```

### Step 4: Update Configuration

```python
# Old configuration (scattered)
JWT_SECRET=your-secret-key
# Multiple AWS Secrets Manager calls
# Different audience/issuer values

# New unified configuration
JWT_ALGORITHM=HS256
JWT_ISSUER=agent-scl-platform
JWT_AUDIENCE=agent-scl-api
JWT_ACCESS_TOKEN_EXPIRY=3600
JWT_REFRESH_TOKEN_EXPIRY=604800
JWT_MULTI_KEY_ENABLED=true
JWT_BLACKLIST_ENABLED=true
```

## Backward Compatibility

The unified JWT manager maintains backward compatibility:

```python
# Legacy methods still work
from shared.auth import jwt_manager

# These still work (aliased to unified manager)
token = jwt_manager.create_access_token(...)
payload = jwt_manager.decode_token(token)
```

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `JWT_ALGORITHM` | HS256 | JWT signing algorithm |
| `JWT_ISSUER` | agent-scl-platform | Token issuer |
| `JWT_AUDIENCE` | agent-scl-api | Token audience |
| `JWT_ACCESS_TOKEN_EXPIRY` | 3600 | Access token expiry (seconds) |
| `JWT_REFRESH_TOKEN_EXPIRY` | 604800 | Refresh token expiry (seconds) |
| `JWT_SECRET_SOURCE` | aws_secrets | Secret source (aws_secrets/environment) |
| `JWT_MULTI_KEY_ENABLED` | true | Enable multi-key support |
| `JWT_BLACKLIST_ENABLED` | true | Enable token blacklist |

### AWS Secrets Manager

For production, use AWS Secrets Manager with this structure:

```json
{
  "algorithm": "HS256",
  "issuer": "agent-scl-platform",
  "audience": "agent-scl-api",
  "keys": [
    {
      "kid": "key-2024-01",
      "secret_key": "your-strong-secret-key-here",
      "active": true,
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "kid": "key-2023-12",
      "secret_key": "previous-key-for-rotation",
      "active": false,
      "created_at": "2023-12-01T00:00:00Z"
    }
  ]
}
```

## Service-Specific Migration

### Auth Service

```python
# Before: Complex authorizer with custom logic
def validate_token_with_multikey(token: str):
    # 100+ lines of validation logic
    pass

# After: Simple authorizer using unified manager
def handler(event, context):
    jwt_manager = get_jwt_manager()
    payload = jwt_manager.verify_token(token, 'access')
    # Build authorization response
```

### User/Admin/Payment Services

```python
# Before: Manual token extraction and validation
auth_header = event.get('headers', {}).get('Authorization', '')
token = auth_header[7:]  # Remove 'Bearer '
payload = jwt_manager.decode_token(token)

# After: Use require_auth decorator (already updated)
@require_auth
def handler(event, context, auth_context):
    # auth_context already contains validated user info
```

## Testing

### Unit Tests

```python
import pytest
from shared.jwt_manager import get_jwt_manager

def test_token_generation():
    jwt_manager = get_jwt_manager()
    token = jwt_manager.generate_access_token(
        user_id="user123",
        email="<EMAIL>",
        tenant_id="tenant123",
        role="user"
    )
    assert token is not None

def test_token_validation():
    jwt_manager = get_jwt_manager()
    # Generate token
    token = jwt_manager.generate_access_token(...)
    
    # Validate token
    payload = jwt_manager.verify_token(token, 'access')
    assert payload['user_id'] == "user123"
```

### Integration Tests

```python
def test_authorizer_with_unified_jwt():
    # Test that authorizer works with unified JWT manager
    event = create_api_gateway_event_with_token()
    response = authorizer_handler(event, {})
    assert response['principalId'] is not None
```

## Troubleshooting

### Common Issues

1. **Token validation fails after migration**
   - Check audience/issuer configuration
   - Verify secret key is accessible
   - Ensure token type matches validation

2. **Multi-key validation not working**
   - Verify AWS Secrets Manager configuration
   - Check IAM permissions for Secrets Manager
   - Ensure JWT_MULTI_KEY_ENABLED=true

3. **Blacklist not working**
   - Verify JWT_BLACKLIST_ENABLED=true
   - Check token has 'jti' claim
   - Ensure blacklist cleanup is running

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('shared.jwt_manager').setLevel(logging.DEBUG)
```

## Performance Considerations

- JWT keys are cached for 5 minutes
- Blacklist cleanup runs every 5 minutes
- Multi-key validation tries active key first
- Configuration is cached per Lambda instance

## Security Best Practices

1. Use strong secret keys (>32 characters)
2. Enable multi-key support for production
3. Rotate keys regularly
4. Use AWS Secrets Manager for production
5. Enable token blacklist for logout functionality
6. Monitor JWT validation metrics

## Next Steps

1. Update imports in all services
2. Test token generation and validation
3. Update configuration for production
4. Monitor logs for any issues
5. Remove old JWT implementation files

For questions or issues, refer to the unified JWT manager documentation or contact the platform team.
