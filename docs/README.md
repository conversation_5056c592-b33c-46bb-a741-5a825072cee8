# 📚 Documentation - Agent SCL Platform

## 🎯 Introduction

Welcome to the official documentation for Agent SCL Platform, a multi-tenant SaaS solution for supply chain and logistics management built with serverless architecture on AWS.

## 📋 Documentation Index

### **🌐 API Documentation**
- [📖 API Reference](./api/API_REFERENCE.md) - Complete REST API documentation with examples
- [🔐 Authentication Guide](./api/authentication.md) - JWT authentication and authorization
- [🔗 Webhooks](./api/webhooks.md) - Webhook configuration and handling

### **🏗️ Architecture & Design**
- [🏛️ Architecture Overview](./ARCHITECTURE.md) - Complete system architecture with optimizations
- [🚀 Performance Optimizations](./PERFORMANCE_OPTIMIZATIONS.md) - Shared layer and performance improvements
- [📋 ADR-001: Dependency Injection](./architecture/ADR-001-dependency-injection-container.md)
- [📋 ADR-002: Layered Architecture](./architecture/ADR-002-layered-architecture-pattern.md)
- [📋 ADR-003: Decorator Pattern](./architecture/ADR-003-decorator-pattern-cross-cutting-concerns.md)

### **👨‍💻 For Developers**
- [🚀 Developer Setup Guide](./development/DEVELOPER_SETUP_GUIDE.md) - Complete development environment setup
- [🧪 Testing Strategy](./testing/README.md) - Testing approach and best practices
- [🔧 Development Standards](../project-context/development_standards.md) - Coding standards and practices

### **👥 For Users**
- [⚡ Quick Start Guide](./user-guides/QUICK_START_GUIDE.md) - Get started in 10 minutes
- [📖 User Guide](./user-guides/USER_GUIDE.md) - Complete platform user guide
- [❓ FAQ](./user-guides/FAQ.md) - Frequently asked questions

### **🔧 For Operations**
- [🚀 Deployment Runbook](./operations/DEPLOYMENT_RUNBOOK.md) - Complete deployment procedures with coordinated deployment
- [📊 Deployment Documentation](./DEPLOY_DOCUMENTATION.md) - Complete platform deployment status
- [📊 Monitoring Guide](./operations/monitoring.md) - System monitoring and alerting
- [🆘 Incident Response](./operations/incident-response.md) - Emergency procedures

## 🚀 **Quick Start**

### **Para Desarrolladores:**
```bash
# 1. Clonar repositorio
git clone <repository-url>
cd the-jungle-agents

# 2. Instalar dependencias
pip install -r requirements.txt
npm install -g serverless

# 3. Configurar AWS
aws configure

# 4. Validar configuración
python scripts/validate-dependencies.py
```

### **Para DevOps:**
```bash
# Despliegue completo automatizado
./scripts/validate-and-deploy.sh dev

# O despliegue manual paso a paso
serverless deploy --stage dev
cd serverless/services/auth && serverless deploy --stage dev
# ... continuar con otros servicios
```

## 🏗️ **Arquitectura General**

```
Frontend Apps → API Gateway → Microservices → DynamoDB/S3
     ↓              ↓            ↓              ↓
  React/Mobile   Rate Limit   Auth/Payment   Single Table
                 CORS/WAF     Tenant/User    Design
```

## 📱 **Servicios Principales**

- **🔒 Auth Service** - Autenticación JWT y autorización
- **💳 Payment Service** - Procesamiento de pagos con Stripe  
- **🏢 Tenant Service** - Gestión multi-tenant
- **👤 User Service** - Gestión de usuarios y perfiles

## 🔧 **Stack Tecnológico**

- **Backend**: Python 3.11 + AWS Lambda
- **Database**: DynamoDB (Single Table Design)
- **API**: AWS API Gateway REST
- **Infrastructure**: Serverless Framework v4
- **Storage**: S3 Multi-bucket
- **Security**: JWT + Secrets Manager + KMS
- **Monitoring**: CloudWatch + X-Ray

## ✅ **Estado del Proyecto**

**🎯 Fase Actual**: ✅ **COMPLETADO - INFRAESTRUCTURA SERVERLESS**
- ✅ Migración completa de Terraform a Serverless Framework
- ✅ Validación exitosa de todas las dependencias
- ✅ Documentación completa para desarrolladores y DevOps
- ✅ Scripts de automatización implementados
- ✅ Arquitectura lista para producción

**🚀 Próximo Paso**: Implementación de lógica de negocio

## 📞 **Soporte y Recursos**

### **🔍 Troubleshooting:**
1. **Validar dependencias**: `python scripts/validate-dependencies.py`
2. **Revisar logs**: `serverless logs -f function-name --stage dev --tail`
3. **Verificar exports**: `aws cloudformation list-exports | grep agent-scl-dev`

### **📚 Recursos Adicionales:**
- **Serverless Framework**: [Documentación oficial](https://www.serverless.com/framework/docs/)
- **AWS Lambda**: [Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- **DynamoDB**: [Single Table Design](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/bp-general-nosql-design.html)

### **🤝 Contribución:**
- **Issues**: Crear issue en el repositorio para bugs o mejoras
- **Pull Requests**: Seguir el flujo de trabajo definido en la guía de desarrolladores
- **Documentación**: Mantener documentación actualizada con cambios

---

**📅 Última actualización**: Enero 2024  
**📊 Estado**: ✅ **INFRAESTRUCTURA COMPLETADA**  
**🎯 Progreso General**: **100%** de Fase 1
