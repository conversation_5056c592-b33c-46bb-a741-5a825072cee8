# 🚀 FASE 3: CHAT EN TIEMPO REAL CON WEBSOCKETS

**Proyecto:** Agent SCL - Plataforma SaaS Logística Multitenant  
**Fase:** 3 de 4  
**Objetivo:** Implementar chat en tiempo real con WebSockets manteniendo integración N8N  
**Duración:** 2 semanas (10 días)  
**Fecha Inicio:** Agosto 2025  
**Estado:** 🟡 EN PLANIFICACIÓN

---

## 📊 CONTEXTO Y ESTADO PREVIO

### ✅ FASES COMPLETADAS:
- ✅ **FASE 1** - Corrección de configuración existente
- ✅ **FASE 2** - Agent Service y N8N integration (13 endpoints)

### 🏗️ INFRAESTRUCTURA DISPONIBLE:
- ✅ **Agent Service** - CRUD completo + webhooks
- ✅ **Auth Service** - JWT + multi-tenancy
- ✅ **Tenant/Payment Services** - Gestión completa
- ✅ **Events Service** - Auditoría robusta
- ✅ **Shared Layer** - Utilidades optimizadas
- ✅ **N8N Integration** - Webhooks bidireccionales

### 🔄 FLUJO ACTUAL (Asíncrono):
```
Usuario → Agent Service → N8N → Webhook → Agent Service → Usuario
```

---

## 🎯 OBJETIVOS FASE 3

### 🎯 OBJETIVO PRINCIPAL:
Implementar **chat en tiempo real** con WebSockets para comunicación instantánea entre usuarios y agentes, manteniendo la integración existente con N8N.

### 🎯 OBJETIVOS ESPECÍFICOS:
1. **WebSocket Infrastructure** - API Gateway WebSocket + Lambda
2. **Chat Service** - Gestión de mensajes en tiempo real
3. **Hybrid Integration** - WebSocket + N8N según tipo de mensaje
4. **Real-time Features** - Presence, typing, message status
5. **Performance & Scaling** - Soporte 1000+ conexiones concurrentes

### 🏗️ ARQUITECTURA OBJETIVO:
```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   Frontend      │ ←──────────────→ │  WebSocket      │
│   (React/Vue)   │                 │  Service        │
└─────────────────┘                 └─────────────────┘
                                            │
                                            ▼
┌─────────────────┐    HTTP/Events   ┌─────────────────┐
│   Agent         │ ←──────────────→ │  Chat           │
│   Service       │                 │  Service        │
└─────────────────┘                 └─────────────────┘
        │                                   │
        ▼                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│   N8N           │                 │  DynamoDB       │
│   Workflows     │                 │  (Messages +    │
└─────────────────┘                 │  Connections)   │
                                    └─────────────────┘
```

---

## 📅 CRONOGRAMA DETALLADO

### 🗓️ SEMANA 4: WEBSOCKET INFRASTRUCTURE

#### DÍA 1: WEBSOCKET SERVICE SETUP
**Tareas:**
- [ ] **T3.1.1** - Crear estructura WebSocket Service con serverless.yml
- [ ] **T3.1.2** - Configurar API Gateway WebSocket API
- [ ] **T3.1.3** - Implementar handlers connect/disconnect
- [ ] **T3.1.4** - Configurar DynamoDB para connection tracking
- [ ] **T3.1.5** - Setup básico de autenticación JWT

**Entregables:**
- WebSocket Service estructura completa
- API Gateway WebSocket configurado
- Connection management básico

#### DÍA 2: CHAT SERVICE CORE
**Tareas:**
- [ ] **T3.2.1** - Crear estructura Chat Service
- [ ] **T3.2.2** - Definir schema DynamoDB para mensajes en tiempo real
- [ ] **T3.2.3** - Implementar message persistence service
- [ ] **T3.2.4** - Crear modelos de chat message
- [ ] **T3.2.5** - Integración básica con Agent Service

**Entregables:**
- Chat Service estructura completa
- Schema DynamoDB extendido
- Message models definidos

#### DÍA 3: REAL-TIME MESSAGE FLOW
**Tareas:**
- [ ] **T3.3.1** - Implementar envío de mensajes via WebSocket
- [ ] **T3.3.2** - Configurar message routing por conversation
- [ ] **T3.3.3** - Implementar broadcast a usuarios conectados
- [ ] **T3.3.4** - Configurar message status (sent/delivered/read)
- [ ] **T3.3.5** - Testing básico de flujo de mensajes

**Entregables:**
- Flujo de mensajes en tiempo real funcional
- Message routing implementado
- Message status tracking

#### DÍA 4: CONNECTION MANAGEMENT
**Tareas:**
- [ ] **T3.4.1** - Implementar connection manager avanzado
- [ ] **T3.4.2** - Configurar presence system (online/offline)
- [ ] **T3.4.3** - Implementar reconnection handling
- [ ] **T3.4.4** - Gestión de múltiples conexiones por usuario
- [ ] **T3.4.5** - Implementar typing indicators

**Entregables:**
- Connection management robusto
- Presence system funcional
- Typing indicators activos

#### DÍA 5: WEBSOCKET AUTHENTICATION & SECURITY
**Tareas:**
- [ ] **T3.5.1** - Implementar autenticación JWT completa para WebSockets
- [ ] **T3.5.2** - Configurar aislamiento multi-tenant en WebSockets
- [ ] **T3.5.3** - Implementar rate limiting para WebSocket messages
- [ ] **T3.5.4** - Security testing y validación
- [ ] **T3.5.5** - Integration testing Semana 4

**Entregables:**
- Autenticación WebSocket completa
- Seguridad multi-tenant
- Testing de integración Semana 4

### 🗓️ SEMANA 5: ADVANCED FEATURES & OPTIMIZATION

#### DÍA 6: ADVANCED CHAT FEATURES
**Tareas:**
- [ ] **T3.6.1** - Implementar message history sync
- [ ] **T3.6.2** - Configurar file sharing en tiempo real
- [ ] **T3.6.3** - Implementar message reactions y status avanzado
- [ ] **T3.6.4** - Configurar message search y filtering
- [ ] **T3.6.5** - Implementar conversation archiving en tiempo real

**Entregables:**
- Message history sync funcional
- File sharing en tiempo real
- Features avanzadas de chat

#### DÍA 7: HYBRID N8N INTEGRATION
**Tareas:**
- [ ] **T3.7.1** - Configurar routing inteligente (WebSocket vs N8N)
- [ ] **T3.7.2** - Implementar fallback automático a webhooks
- [ ] **T3.7.3** - Sincronización de estados entre WebSocket y N8N
- [ ] **T3.7.4** - Testing de integración híbrida completa
- [ ] **T3.7.5** - Configurar criterios de routing automático

**Entregables:**
- Sistema híbrido WebSocket + N8N
- Routing inteligente funcional
- Fallback automático configurado

#### DÍA 8: PERFORMANCE & SCALING
**Tareas:**
- [ ] **T3.8.1** - Implementar connection pooling optimizado
- [ ] **T3.8.2** - Configurar auto-scaling para WebSocket Lambdas
- [ ] **T3.8.3** - Optimizar message delivery performance
- [ ] **T3.8.4** - Implementar message batching para eficiencia
- [ ] **T3.8.5** - Load testing con 1000+ conexiones

**Entregables:**
- Performance optimizado
- Auto-scaling configurado
- Load testing completado

#### DÍA 9: MONITORING & ANALYTICS
**Tareas:**
- [ ] **T3.9.1** - Configurar CloudWatch metrics para WebSockets
- [ ] **T3.9.2** - Implementar connection metrics y analytics
- [ ] **T3.9.3** - Configurar alertas de performance y errores
- [ ] **T3.9.4** - Crear dashboard de monitoreo en tiempo real
- [ ] **T3.9.5** - Integrar con Events Service para auditoría

**Entregables:**
- Monitoreo CloudWatch completo
- Dashboard de analytics
- Alertas configuradas

#### DÍA 10: FINAL INTEGRATION & TESTING
**Tareas:**
- [ ] **T3.10.1** - Testing end-to-end completo del sistema
- [ ] **T3.10.2** - Performance optimization final
- [ ] **T3.10.3** - Security audit completo
- [ ] **T3.10.4** - Documentation técnica completa
- [ ] **T3.10.5** - Preparación para Fase 4

**Entregables:**
- Sistema completamente funcional
- Documentation completa
- Preparación para integración end-to-end

---

## 🏗️ SERVICIOS A IMPLEMENTAR

### 1. WEBSOCKET SERVICE
```
services/websocket/
├── README.md
├── serverless.yml
├── requirements.txt
└── src/
    ├── handlers/
    │   ├── connect.py          # WebSocket connection handler
    │   ├── disconnect.py       # WebSocket disconnection handler
    │   ├── message.py          # Message handling via WebSocket
    │   ├── broadcast.py        # Message broadcasting
    │   └── auth.py             # WebSocket authentication
    ├── services/
    │   ├── connection_manager.py   # Connection state management
    │   ├── message_router.py       # Message routing logic
    │   └── presence_service.py     # User presence tracking
    ├── models/
    │   ├── connection.py       # Connection model
    │   └── websocket_message.py    # WebSocket message model
    └── utils/
        ├── auth.py             # WebSocket auth utilities
        ├── validation.py       # Message validation
        └── config.py           # WebSocket configuration
```

### 2. CHAT SERVICE
```
services/chat/
├── README.md
├── serverless.yml
├── requirements.txt
└── src/
    ├── handlers/
    │   ├── send_message.py     # Send real-time message
    │   ├── get_messages.py     # Get message history
    │   ├── message_status.py   # Update message status
    │   ├── typing_indicator.py # Typing indicators
    │   └── file_share.py       # File sharing in real-time
    ├── services/
    │   ├── message_service.py      # Core message operations
    │   ├── presence_service.py     # User presence management
    │   ├── notification_service.py # Push notifications
    │   └── hybrid_router.py        # WebSocket vs N8N routing
    ├── models/
    │   ├── chat_message.py     # Real-time chat message
    │   ├── conversation_state.py   # Conversation state
    │   └── user_presence.py    # User presence model
    └── utils/
        ├── message_utils.py    # Message utilities
        ├── routing_logic.py    # Hybrid routing logic
        └── performance.py      # Performance optimization
```

---

## 🔄 FLUJOS DE INTEGRACIÓN

### FLUJO TIEMPO REAL (WebSocket):
```
1. Usuario conecta → WebSocket Service → Auth validation
2. Usuario envía mensaje → Chat Service → Validation
3. Chat Service → Message persistence → DynamoDB
4. Chat Service → Broadcast → WebSocket Service → Usuario destino
5. Events Service ← Audit log ← Chat Service
```

### FLUJO HÍBRIDO (WebSocket + N8N):
```
1. Usuario envía mensaje complejo → Chat Service → Routing logic
2. Chat Service → Agent Service → N8N workflow
3. N8N procesa → Webhook → Agent Service → Chat Service
4. Chat Service → WebSocket broadcast → Usuario
5. Events Service ← Audit completo ← Multiple services
```

### CRITERIOS DE ROUTING:
- **WebSocket directo**: Mensajes texto simples, respuestas rápidas
- **N8N processing**: Documentos, análisis complejo, workflows específicos
- **Fallback automático**: Si WebSocket falla → N8N webhook

---

## 📊 INTEGRACIÓN CON INFRAESTRUCTURA EXISTENTE

### ✅ REUTILIZACIÓN DE SERVICIOS:
- **Agent Service**: Mantener todos los 13 endpoints existentes
- **Auth Service**: Extender para autenticación WebSocket
- **Events Service**: Auditoría de mensajes en tiempo real
- **Shared Layer**: Reutilizar todas las utilidades existentes
- **DynamoDB**: Extender schema actual para connections y real-time messages

### 🔧 EXTENSIONES NECESARIAS:

#### API Gateway:
- Agregar WebSocket API junto al HTTP API existente
- Configurar routes: $connect, $disconnect, $default
- Integrar con Lambda authorizer para JWT

#### Lambda Functions:
- 8 nuevas funciones para WebSocket Service
- 6 nuevas funciones para Chat Service
- Reutilizar shared layers existentes

#### DynamoDB:
- Nuevas tablas: `websocket-connections`, `real-time-messages`
- Extender tabla principal con GSI para real-time queries
- TTL para cleanup automático de connections

#### CloudWatch:
- Métricas específicas para WebSocket connections
- Alarmas para connection limits y performance
- Dashboard integrado con métricas existentes

---

## 🎯 CRITERIOS DE ACEPTACIÓN

### FUNCIONALIDAD CORE:
- [ ] Chat en tiempo real funcional con latencia < 100ms
- [ ] Integración híbrida WebSocket + N8N operativa
- [ ] Message persistence y sincronización completa
- [ ] Presence system (online/offline/typing) activo
- [ ] File sharing en tiempo real implementado
- [ ] Message status tracking (sent/delivered/read)

### PERFORMANCE:
- [ ] Soporte mínimo 1000 conexiones WebSocket concurrentes
- [ ] Latencia promedio < 100ms para mensajes
- [ ] 99.9% uptime para WebSocket connections
- [ ] Auto-scaling funcional bajo carga
- [ ] Throughput > 10,000 mensajes/minuto

### SEGURIDAD:
- [ ] Autenticación JWT completa en WebSockets
- [ ] Aislamiento multi-tenant estricto
- [ ] Rate limiting activo (100 msg/min por usuario)
- [ ] Auditoría completa integrada con Events Service
- [ ] Validación de input en todos los mensajes

### INTEGRACIÓN:
- [ ] Compatibilidad 100% con Agent Service existente
- [ ] Routing inteligente WebSocket vs N8N funcional
- [ ] Fallback automático a webhooks operativo
- [ ] Sincronización de estados entre servicios
- [ ] Events Service recibe todos los eventos de chat

### MONITOREO:
- [ ] CloudWatch metrics personalizadas activas
- [ ] Alertas configuradas para errores y performance
- [ ] Dashboard de monitoreo en tiempo real
- [ ] Logging estructurado en todos los componentes
- [ ] Health checks automáticos cada 5 minutos

---

## 📋 ENDPOINTS A IMPLEMENTAR

### WEBSOCKET SERVICE:
```
WebSocket API Gateway:
- $connect     → connect.handler      # Establecer conexión
- $disconnect  → disconnect.handler   # Cerrar conexión
- $default     → message.handler      # Manejar mensajes
- sendMessage  → broadcast.handler    # Broadcast a usuarios
```

### CHAT SERVICE:
```
HTTP Endpoints:
POST   /chat/messages                 # Enviar mensaje
GET    /chat/messages/{conversationId} # Obtener historial
PATCH  /chat/messages/{messageId}/status # Actualizar estado
POST   /chat/typing/{conversationId}   # Typing indicator
POST   /chat/files                    # Compartir archivo
GET    /chat/presence/{userId}        # Estado de presencia
```

---

## 🔧 CONFIGURACIÓN TÉCNICA

### WEBSOCKET API GATEWAY:
```yaml
WebSocketApi:
  Type: AWS::ApiGatewayV2::Api
  Properties:
    Name: ${self:custom.projectName}-websocket-${self:custom.stage}
    ProtocolType: WEBSOCKET
    RouteSelectionExpression: $request.body.action
```

### DYNAMODB TABLES:
```yaml
# Tabla para conexiones WebSocket
WebSocketConnections:
  TableName: websocket-connections-${self:custom.stage}
  AttributeDefinitions:
    - AttributeName: connectionId
      AttributeType: S
    - AttributeName: userId
      AttributeType: S
  KeySchema:
    - AttributeName: connectionId
      KeyType: HASH
  GlobalSecondaryIndexes:
    - IndexName: UserIndex
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
  TimeToLiveSpecification:
    AttributeName: ttl
    Enabled: true

# Tabla para mensajes en tiempo real
RealTimeMessages:
  TableName: realtime-messages-${self:custom.stage}
  AttributeDefinitions:
    - AttributeName: conversationId
      AttributeType: S
    - AttributeName: timestamp
      AttributeType: S
  KeySchema:
    - AttributeName: conversationId
      KeyType: HASH
    - AttributeName: timestamp
      KeyType: RANGE
```

### LAMBDA CONFIGURATION:
```yaml
# WebSocket functions
connect:
  timeout: 30
  memorySize: 256
  environment:
    CONNECTIONS_TABLE: ${self:custom.connectionsTable}

message:
  timeout: 30
  memorySize: 512
  environment:
    CONNECTIONS_TABLE: ${self:custom.connectionsTable}
    MESSAGES_TABLE: ${self:custom.messagesTable}
```

---

## 📈 MÉTRICAS Y MONITOREO

### CLOUDWATCH METRICS:
- **ConnectionCount** - Número de conexiones activas
- **MessageLatency** - Latencia de entrega de mensajes
- **MessageThroughput** - Mensajes por segundo
- **ErrorRate** - Tasa de errores en WebSocket
- **PresenceUpdates** - Actualizaciones de presencia por minuto

### ALARMAS:
- **HighConnectionCount** - > 800 conexiones (80% del límite)
- **HighMessageLatency** - > 200ms promedio
- **HighErrorRate** - > 5% errores en 5 minutos
- **WebSocketFailures** - Fallos de conexión > 10/minuto

---

## 🚀 PLAN DE DEPLOYMENT

### ORDEN DE DEPLOYMENT:
1. **WebSocket Service** - Infraestructura base
2. **Chat Service** - Lógica de negocio
3. **Agent Service Updates** - Integración híbrida
4. **Frontend Integration** - Cliente WebSocket

### TESTING STRATEGY:
1. **Unit Tests** - Cada handler y service
2. **Integration Tests** - Flujo completo WebSocket
3. **Load Tests** - 1000+ conexiones concurrentes
4. **Security Tests** - Penetration testing
5. **End-to-End Tests** - Flujo híbrido completo

---

## 📚 DOCUMENTACIÓN A GENERAR

- [ ] **WebSocket API Documentation** - Especificación completa
- [ ] **Chat Service API Documentation** - Endpoints y modelos
- [ ] **Integration Guide** - Cómo integrar frontend
- [ ] **Performance Tuning Guide** - Optimización y scaling
- [ ] **Troubleshooting Guide** - Resolución de problemas
- [ ] **Security Best Practices** - Guía de seguridad
- [ ] **Monitoring Playbook** - Guía de monitoreo

---

**ESTADO ACTUAL:** 🟡 PLANIFICACIÓN COMPLETA - LISTO PARA EJECUCIÓN
**PRÓXIMO PASO:** Ejecutar Tarea T3.1.1 - Crear estructura WebSocket Service

---

## 📋 TRACKING DE PROGRESO

### SEMANA 4 PROGRESS: 0/25 tareas completadas
- [ ] DÍA 1: 0/5 tareas
- [ ] DÍA 2: 0/5 tareas
- [ ] DÍA 3: 0/5 tareas
- [ ] DÍA 4: 0/5 tareas
- [ ] DÍA 5: 0/5 tareas

### SEMANA 5 PROGRESS: 0/25 tareas completadas
- [ ] DÍA 6: 0/5 tareas
- [ ] DÍA 7: 0/5 tareas
- [ ] DÍA 8: 0/5 tareas
- [ ] DÍA 9: 0/5 tareas
- [ ] DÍA 10: 0/5 tareas

**PROGRESO TOTAL: 0/50 tareas completadas (0%)**
