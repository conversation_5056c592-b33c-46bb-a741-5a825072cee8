# 🚀 Shared Layer Optimization Migration Guide

## 📋 Overview

This document provides a comprehensive guide for implementing shared layer optimizations in the Agent SCL Platform. It covers the migration process, validation procedures, and troubleshooting steps.

## 🎯 Migration Objectives

### **Performance Goals**
- **Function Size Reduction**: 90%+ reduction in Lambda deployment packages
- **Cold Start Improvement**: 2-5 second reduction in initialization time
- **Memory Optimization**: 20-30% reduction in memory usage
- **Cost Reduction**: 15-25% reduction in Lambda operational costs

### **Architecture Goals**
- **Centralized Dependencies**: Single shared layer for common libraries
- **Unified Authorization**: Centralized JWT authorizer across all services
- **Deployment Consistency**: Standardized deployment procedures
- **Operational Efficiency**: Simplified maintenance and updates

## 🔄 Migration Process

### **Phase 1: Pre-Migration Preparation**

#### **1.1 Backup Current State**
```bash
# Create backup branch
git checkout -b backup-pre-optimization
git push origin backup-pre-optimization

# Document current function sizes
aws lambda list-functions --region us-east-1 \
  --query 'Functions[?starts_with(FunctionName, `agent-scl`)].{Name:FunctionName,Size:CodeSize}' \
  --output table > pre-optimization-sizes.txt
```

#### **1.2 Validate Current Deployment**
```bash
# Test all service endpoints
for service in auth user admin payment tenant events security; do
  echo "Testing $service service..."
  # Add specific health check commands here
done
```

### **Phase 2: Dependency Isolation**

#### **2.1 Disable Shared Layer References**
```bash
# Run the disable script
python scripts/disable-shared-layer-references.py

# Verify changes
git diff --name-only | grep serverless.yml
```

#### **2.2 Disable JWT Authorizer References**
```bash
# Run the disable script
python scripts/disable-jwt-authorizer.py

# Verify changes
git status
```

#### **2.3 Deploy Services Without Dependencies**
```bash
# Deploy each service individually
services=("auth" "user" "admin" "payment" "tenant" "events" "security")

for service in "${services[@]}"; do
  echo "Deploying $service without dependencies..."
  cd services/$service
  serverless deploy --stage dev --region us-east-1
  if [ $? -eq 0 ]; then
    echo "✅ $service deployed successfully"
  else
    echo "❌ $service deployment failed"
    exit 1
  fi
  cd ../..
done
```

### **Phase 3: Shared Layer Deployment**

#### **3.1 Deploy Updated Shared Layer**
```bash
cd shared
serverless deploy --stage dev --region us-east-1

# Capture the layer ARN
LAYER_ARN=$(aws cloudformation describe-stacks \
  --stack-name agent-scl-shared-layer-dev \
  --region us-east-1 \
  --query 'Stacks[0].Outputs[?OutputKey==`SharedLayerLambdaLayerQualifiedArn`].OutputValue' \
  --output text)

echo "Shared Layer ARN: $LAYER_ARN"
cd ..
```

### **Phase 4: Optimization Activation**

#### **4.1 Re-enable Shared Layer References**
```bash
# Run the enable script
python scripts/enable-shared-layer-references.py

# Verify changes
git diff services/*/serverless.yml
```

#### **4.2 Re-enable JWT Authorizer References**
```bash
# Run the enable script
python scripts/enable-jwt-authorizer.py

# Verify changes
git diff services/*/serverless.yml
```

#### **4.3 Deploy Optimized Services**
```bash
# Deploy all services with optimizations
for service in "${services[@]}"; do
  echo "Deploying $service with optimizations..."
  cd services/$service
  serverless deploy --stage dev --region us-east-1
  if [ $? -eq 0 ]; then
    echo "✅ $service optimized successfully"
  else
    echo "❌ $service optimization failed"
    exit 1
  fi
  cd ../..
done
```

## ✅ Validation Procedures

### **Function Size Validation**
```bash
# Check optimized function sizes
aws lambda list-functions --region us-east-1 \
  --query 'Functions[?starts_with(FunctionName, `agent-scl`)].{Name:FunctionName,Size:CodeSize}' \
  --output table > post-optimization-sizes.txt

# Compare sizes
echo "Size comparison:"
paste pre-optimization-sizes.txt post-optimization-sizes.txt
```

### **Shared Layer Validation**
```bash
# Verify shared layer usage
sample_functions=(
  "agent-scl-auth-dev-login"
  "agent-scl-user-dev-getProfile"
  "agent-scl-admin-dev-systemAnalytics"
  "agent-scl-payment-dev-createSubscription"
  "agent-scl-tenant-dev-getTenants"
  "agent-scl-events-dev-tenantEvents"
  "agent-scl-security-dev-auditLog"
)

for func in "${sample_functions[@]}"; do
  echo "Checking $func..."
  aws lambda get-function --function-name $func --region us-east-1 \
    --query 'Configuration.Layers[].Arn' --output text
done
```

### **JWT Authorizer Validation**
```bash
# Test protected endpoints
curl -X GET https://5p54pp2np8.execute-api.us-east-1.amazonaws.com/dev/user/profile \
  -H "Authorization: Bearer VALID_JWT_TOKEN"

# Should return 401 without token
curl -X GET https://5p54pp2np8.execute-api.us-east-1.amazonaws.com/dev/user/profile
```

### **Performance Testing**
```bash
# Cold start testing
for i in {1..5}; do
  echo "Cold start test $i..."
  # Trigger function after 15 minutes of inactivity
  time curl -X GET https://5p54pp2np8.execute-api.us-east-1.amazonaws.com/dev/user/health
  sleep 900  # Wait 15 minutes for next test
done
```

## 🔧 Troubleshooting

### **Common Issues**

#### **Issue 1: Function Not Using Shared Layer**
```bash
# Symptoms: Function size not reduced
# Solution: Check layer reference in serverless.yml

# Verify layer configuration
grep -A 2 "layers:" services/*/serverless.yml

# Re-deploy specific service
cd services/problematic-service
serverless deploy --stage dev --region us-east-1
```

#### **Issue 2: JWT Authorizer Not Working**
```bash
# Symptoms: 500 errors on protected endpoints
# Solution: Verify authorizer configuration

# Check authorizer function
aws lambda get-function --function-name agent-scl-auth-dev-jwtAuthorizer \
  --region us-east-1

# Test authorizer directly
aws lambda invoke --function-name agent-scl-auth-dev-jwtAuthorizer \
  --region us-east-1 \
  --payload '{"authorizationToken":"Bearer test-token","methodArn":"arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/request"}' \
  response.json
```

#### **Issue 3: Deployment Failures**
```bash
# Symptoms: CloudFormation stack update failures
# Solution: Check for dependency conflicts

# Check stack events
aws cloudformation describe-stack-events \
  --stack-name agent-scl-service-dev \
  --region us-east-1 \
  --query 'StackEvents[?ResourceStatus==`CREATE_FAILED` || ResourceStatus==`UPDATE_FAILED`]'

# Force re-deployment
serverless deploy --stage dev --region us-east-1 --force
```

## 🔄 Rollback Procedures

### **Emergency Rollback**
```bash
# If optimization causes critical issues
git checkout backup-pre-optimization

# Deploy original configuration
for service in "${services[@]}"; do
  cd services/$service
  serverless deploy --stage dev --region us-east-1
  cd ../..
done
```

### **Partial Rollback**
```bash
# Rollback specific service
cd services/problematic-service

# Disable optimizations for this service only
sed -i 's/- ${self:custom.sharedLayerArn}/# - ${self:custom.sharedLayerArn}/' serverless.yml

# Re-deploy
serverless deploy --stage dev --region us-east-1
```

## 📊 Success Metrics

### **Performance Metrics**
- **Function Size**: Target 90%+ reduction
- **Cold Start**: Target 2-5 second improvement
- **Memory Usage**: Target 20-30% reduction
- **Error Rate**: Should remain < 0.1%

### **Operational Metrics**
- **Deployment Time**: Target 40-45% improvement
- **Cost Reduction**: Target 15-25% reduction
- **Maintenance Overhead**: Simplified dependency management

## 📝 Post-Migration Tasks

### **Documentation Updates**
- [ ] Update architecture diagrams
- [ ] Update deployment procedures
- [ ] Update troubleshooting guides
- [ ] Update performance baselines

### **Monitoring Setup**
- [ ] Configure performance dashboards
- [ ] Set up optimization alerts
- [ ] Update cost monitoring
- [ ] Schedule performance reviews

---

**Migration Completed**: 2024-08-10  
**Document Owner**: Platform Team  
**Next Review**: 2024-11-10
