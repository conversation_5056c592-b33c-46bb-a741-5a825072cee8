# 🌐 Agent SCL Platform - API Reference

## 📋 Overview

The Agent SCL Platform provides a comprehensive REST API for managing multi-tenant logistics operations. All APIs follow REST principles and return standardized JSON responses.

### **Base URLs**
- **Development**: `https://api-platform-dev.agentscl.com`
- **Staging**: `https://api-platform-staging.agentscl.com`
- **Production**: `https://api-platform.agentscl.com`

### **API Version**
Current API Version: `v1`

## 🔐 Authentication

### **Authentication Methods**
- **JWT Bearer Token**: Required for all authenticated endpoints
- **API Key**: For webhook endpoints (Stripe webhooks)

### **Required Headers**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Tenant-Id: <tenant_id>  # Required for tenant-specific operations
```

### **JWT Token Structure**
```json
{
  "user_id": "user_123",
  "tenant_id": "tenant_456",
  "email": "<EMAIL>",
  "roles": ["USER", "ADMIN"],
  "exp": **********,
  "iat": 1640908800
}
```

## 📊 Response Format

### **Success Response**
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "timestamp": "2024-08-05T10:30:00Z",
  "request_id": "req_123456789"
}
```

### **Error Response**
```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE",
  "details": {
    // Additional error details
  },
  "timestamp": "2024-08-05T10:30:00Z",
  "request_id": "req_123456789"
}
```

### **HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error
- `503` - Service Unavailable

## 🔑 Authentication Service

### **POST /auth/register**
Register a new tenant with master user.

**Authentication**: None required

**Request Body**:
```json
{
  "tenant_name": "Company Name",
  "user_name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phone": "+1234567890"
}
```

**Response (201)**:
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant_123",
    "user_id": "user_456",
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  },
  "message": "Registration successful"
}
```

### **POST /auth/login**
Authenticate user and get access tokens.

**Authentication**: None required

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_456",
    "tenant_id": "tenant_123",
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "roles": ["USER", "ADMIN"]
    }
  },
  "message": "Login successful"
}
```

### **POST /auth/refresh**
Refresh access token using refresh token.

**Authentication**: Refresh token required

**Request Body**:
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  },
  "message": "Token refreshed successfully"
}
```

### **POST /auth/logout**
Logout user and invalidate tokens.

**Authentication**: Bearer token required

**Request Body**: Empty

**Response (200)**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

## 💳 Payment Service

### **GET /payment/plans**
Get available subscription plans.

**Authentication**: Bearer token required

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "plan_id": "basic",
        "name": "Basic Plan",
        "description": "Essential features for small teams",
        "price": 29.99,
        "currency": "USD",
        "interval": "month",
        "features": [
          "Up to 5 users",
          "Basic analytics",
          "Email support"
        ]
      },
      {
        "plan_id": "standard",
        "name": "Standard Plan",
        "description": "Advanced features for growing businesses",
        "price": 79.99,
        "currency": "USD",
        "interval": "month",
        "features": [
          "Up to 25 users",
          "Advanced analytics",
          "Priority support",
          "API access"
        ]
      }
    ]
  }
}
```

### **POST /payment/subscription**
Create a new subscription.

**Authentication**: Bearer token required

**Request Body**:
```json
{
  "plan_id": "standard",
  "payment_method_id": "pm_1234567890"
}
```

**Response (201)**:
```json
{
  "success": true,
  "data": {
    "subscription_id": "sub_123456",
    "status": "active",
    "current_period_start": "2024-08-05T00:00:00Z",
    "current_period_end": "2024-09-05T00:00:00Z",
    "plan": {
      "plan_id": "standard",
      "name": "Standard Plan",
      "price": 79.99
    }
  },
  "message": "Subscription created successfully"
}
```

### **GET /payment/subscription**
Get current subscription details.

**Authentication**: Bearer token required

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "subscription_id": "sub_123456",
    "status": "active",
    "current_period_start": "2024-08-05T00:00:00Z",
    "current_period_end": "2024-09-05T00:00:00Z",
    "plan": {
      "plan_id": "standard",
      "name": "Standard Plan",
      "price": 79.99
    },
    "usage": {
      "users_count": 12,
      "users_limit": 25
    }
  }
}
```

## 👥 User Service

### **GET /user/profile**
Get current user profile.

**Authentication**: Bearer token required

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_456",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "roles": ["USER", "ADMIN"],
    "created_at": "2024-01-15T10:30:00Z",
    "last_login": "2024-08-05T09:15:00Z",
    "tenant": {
      "tenant_id": "tenant_123",
      "name": "Company Name"
    }
  }
}
```

### **PUT /user/profile**
Update user profile.

**Authentication**: Bearer token required

**Request Body**:
```json
{
  "name": "John Smith",
  "phone": "+1234567891"
}
```

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_456",
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1234567891",
    "updated_at": "2024-08-05T10:30:00Z"
  },
  "message": "Profile updated successfully"
}
```

## 🏢 Tenant Service

### **GET /tenant/info**
Get current tenant information.

**Authentication**: Bearer token required

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant_123",
    "name": "Company Name",
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z",
    "subscription": {
      "plan": "standard",
      "status": "active"
    },
    "settings": {
      "timezone": "UTC",
      "currency": "USD",
      "language": "en"
    }
  }
}
```

## 📊 Rate Limiting

### **Rate Limits by Endpoint**
| Endpoint | Requests/Minute | Requests/Hour |
|----------|-----------------|---------------|
| POST /auth/login | 5 | 50 |
| POST /auth/register | 3 | 10 |
| POST /auth/forgot-password | 3 | 10 |
| Authenticated endpoints | 60 | 1000 |
| GET endpoints | 120 | 2000 |

### **Rate Limit Headers**
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

## 🔍 Health Check

### **GET /health**
System health check endpoint.

**Authentication**: None required

**Response (200)**:
```json
{
  "status": "healthy",
  "timestamp": "2024-08-05T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "stripe": "healthy",
    "email": "healthy"
  }
}
```

## 🔧 Admin Service

### **GET /admin/metrics**
Get system metrics dashboard.

**Authentication**: Bearer token required (ADMIN role)

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "system_health": "healthy",
    "active_users": 1250,
    "total_tenants": 45,
    "api_requests_24h": 125000,
    "error_rate": 0.02
  }
}
```

## 🔒 Security Service

### **GET /security/audit-logs**
Get security audit logs.

**Authentication**: Bearer token required (ADMIN role)

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "timestamp": "2024-08-05T10:30:00Z",
        "event_type": "login_attempt",
        "user_id": "user_123",
        "ip_address": "***********",
        "status": "success"
      }
    ]
  }
}
```

## 📊 Events Service

### **GET /events/system-events**
Get system events.

**Authentication**: Bearer token required

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "event_id": "evt_123",
        "event_type": "user_registered",
        "timestamp": "2024-08-05T10:30:00Z",
        "data": {
          "user_id": "user_456",
          "tenant_id": "tenant_123"
        }
      }
    ]
  }
}
```

## 📚 Additional Resources

- [Authentication Guide](./authentication.md)
- [Error Handling](./error-handling.md)
- [Webhooks](./webhooks.md)
- [SDKs and Libraries](./sdks.md)
- [Postman Collection](./postman-collection.json)
