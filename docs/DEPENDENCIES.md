# 📋 **DOCUMENTACIÓN DE DEPENDENCIAS ENTRE SERVICIOS**

## 🏗️ **ARQUITECTURA GENERAL**

La plataforma Agent SCL está construida con una arquitectura de microservicios usando Serverless Framework, donde cada servicio tiene responsabilidades específicas y se comunica a través de recursos compartidos.

## 🔗 **MAPA DE DEPENDENCIAS**

### **1. INFRAESTRUCTURA PRINCIPAL** (`serverless.yml`)
**Rol**: Proveedor de recursos compartidos
**Ubicación**: `/serverless.yml`

#### **Recursos que Exporta:**
- `agent-scl-dev-DynamoDBTableName` - Tabla principal de datos
- `agent-scl-dev-PlatformDataBucketName` - Bucket de datos de la plataforma
- `agent-scl-dev-LambdaExecutionRoleArn` - Rol de ejecución para Lambdas
- `agent-scl-dev-JWTSecretArn` - Secreto para JWT
- `agent-scl-dev-DatabaseSecretArn` - Configuración de base de datos
- `agent-scl-dev-StripeSecretArn` - Configuración de Stripe
- `agent-scl-dev-ApplicationSecretArn` - Configuración de aplicación
- `agent-scl-dev-RestApiId` - ID de API Gateway
- `agent-scl-dev-RestApiRootResourceId` - Recurso raíz de API Gateway
- `agent-scl-dev-LambdaSecurityGroupId` - Security Group para Lambdas
- `agent-scl-dev-PrivateSubnetId1` - Subnet privada 1
- `agent-scl-dev-PrivateSubnetId2` - Subnet privada 2

#### **Dependencias**: Ninguna (es la base)

---

### **2. SERVICIO DE AUTENTICACIÓN** (`auth`)
**Rol**: Gestión de usuarios, autenticación y autorización
**Ubicación**: `/serverless/services/auth/`

#### **Recursos que Exporta:**
- `agent-scl-dev-JwtAuthorizerArn` - Autorizador JWT para otros servicios

#### **Dependencias de Infraestructura:**
```yaml
# DynamoDB
- Fn::ImportValue: agent-scl-dev-DynamoDBTableName

# S3
- Fn::ImportValue: agent-scl-dev-PlatformDataBucketName

# IAM
- Fn::ImportValue: agent-scl-dev-LambdaExecutionRoleArn

# Secrets Manager
- Fn::ImportValue: agent-scl-dev-JWTSecretArn
- Fn::ImportValue: agent-scl-dev-DatabaseSecretArn
- Fn::ImportValue: agent-scl-dev-ApplicationSecretArn

# API Gateway
- Fn::ImportValue: agent-scl-dev-RestApiId
- Fn::ImportValue: agent-scl-dev-RestApiRootResourceId

# VPC
- Fn::ImportValue: agent-scl-dev-LambdaSecurityGroupId
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId1
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId2
```

#### **Funciones Lambda:**
- `register` - Registro de usuarios
- `login` - Inicio de sesión
- `logout` - Cierre de sesión
- `refresh-token` - Renovación de tokens
- `verify-email` - Verificación de email
- `reset-password` - Restablecimiento de contraseña
- `jwt-authorizer` - Autorizador JWT (usado por otros servicios)

---

### **3. SERVICIO DE PAGOS** (`payment`)
**Rol**: Procesamiento de pagos y gestión de transacciones
**Ubicación**: `/serverless/services/payment/`

#### **Dependencias de Infraestructura:**
```yaml
# DynamoDB
- Fn::ImportValue: agent-scl-dev-DynamoDBTableName

# S3
- Fn::ImportValue: agent-scl-dev-PlatformDataBucketName

# IAM
- Fn::ImportValue: agent-scl-dev-LambdaExecutionRoleArn

# Secrets Manager
- Fn::ImportValue: agent-scl-dev-StripeSecretArn
- Fn::ImportValue: agent-scl-dev-DatabaseSecretArn

# API Gateway
- Fn::ImportValue: agent-scl-dev-RestApiId
- Fn::ImportValue: agent-scl-dev-RestApiRootResourceId

# VPC
- Fn::ImportValue: agent-scl-dev-LambdaSecurityGroupId
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId1
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId2
```

#### **Dependencias de Servicios:**
```yaml
# Autenticación
- Fn::ImportValue: agent-scl-dev-JwtAuthorizerArn  # Para proteger endpoints
```

#### **Funciones Lambda:**
- `create-payment-intent` - Crear intención de pago
- `confirm-payment` - Confirmar pago
- `webhook-handler` - Manejar webhooks de Stripe
- `get-payment-history` - Historial de pagos

---

### **4. SERVICIO DE TENANTS** (`tenant`)
**Rol**: Gestión de organizaciones y multi-tenancy
**Ubicación**: `/serverless/services/tenant/`

#### **Dependencias de Infraestructura:**
```yaml
# DynamoDB
- Fn::ImportValue: agent-scl-dev-DynamoDBTableName

# S3
- Fn::ImportValue: agent-scl-dev-PlatformDataBucketName

# IAM
- Fn::ImportValue: agent-scl-dev-LambdaExecutionRoleArn

# Secrets Manager
- Fn::ImportValue: agent-scl-dev-DatabaseSecretArn

# API Gateway
- Fn::ImportValue: agent-scl-dev-RestApiId
- Fn::ImportValue: agent-scl-dev-RestApiRootResourceId

# VPC
- Fn::ImportValue: agent-scl-dev-LambdaSecurityGroupId
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId1
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId2
```

#### **Dependencias de Servicios:**
```yaml
# Autenticación
- Fn::ImportValue: agent-scl-dev-JwtAuthorizerArn  # Para proteger endpoints
```

#### **Funciones Lambda:**
- `create-tenant` - Crear organización
- `get-tenant` - Obtener información de tenant
- `update-tenant` - Actualizar tenant
- `list-tenants` - Listar tenants
- `invite-user` - Invitar usuario a tenant

---

### **5. SERVICIO DE USUARIOS** (`user`)
**Rol**: Gestión de perfiles y datos de usuarios
**Ubicación**: `/serverless/services/user/`

#### **Dependencias de Infraestructura:**
```yaml
# DynamoDB
- Fn::ImportValue: agent-scl-dev-DynamoDBTableName

# S3
- Fn::ImportValue: agent-scl-dev-PlatformDataBucketName

# IAM
- Fn::ImportValue: agent-scl-dev-LambdaExecutionRoleArn

# Secrets Manager
- Fn::ImportValue: agent-scl-dev-DatabaseSecretArn

# API Gateway
- Fn::ImportValue: agent-scl-dev-RestApiId
- Fn::ImportValue: agent-scl-dev-RestApiRootResourceId

# VPC
- Fn::ImportValue: agent-scl-dev-LambdaSecurityGroupId
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId1
- Fn::ImportValue: agent-scl-dev-PrivateSubnetId2
```

#### **Dependencias de Servicios:**
```yaml
# Autenticación
- Fn::ImportValue: agent-scl-dev-JwtAuthorizerArn  # Para proteger endpoints
```

#### **Funciones Lambda:**
- `get-profile` - Obtener perfil de usuario
- `update-profile` - Actualizar perfil
- `upload-avatar` - Subir avatar
- `get-preferences` - Obtener preferencias
- `update-preferences` - Actualizar preferencias

---

## 🔄 **FLUJO DE DEPENDENCIAS**

### **Orden de Despliegue Recomendado:**
1. **Infraestructura Principal** (`serverless.yml`) - Debe desplegarse primero
2. **Servicio Auth** - Segundo, ya que otros servicios dependen de su autorizador
3. **Servicios Payment, Tenant, User** - Pueden desplegarse en paralelo

### **Dependencias Críticas:**
- Todos los servicios dependen de la infraestructura principal
- Los servicios `payment`, `tenant` y `user` dependen del autorizador JWT del servicio `auth`
- Ningún servicio puede funcionar sin acceso a DynamoDB y los secretos

---

## ⚠️ **CONSIDERACIONES IMPORTANTES**

### **Cambios que Afectan Múltiples Servicios:**
1. **Cambios en DynamoDB**: Afectan a todos los servicios
2. **Cambios en el autorizador JWT**: Afectan a payment, tenant y user
3. **Cambios en API Gateway**: Afectan a todos los servicios
4. **Cambios en VPC/Security Groups**: Afectan a todos los servicios

### **Estrategia de Rollback:**
- Mantener versiones de los exports de CloudFormation
- Probar cambios en entorno de desarrollo primero
- Desplegar cambios de infraestructura en horarios de bajo tráfico

### **Monitoreo de Dependencias:**
- Verificar que todos los exports estén disponibles antes del despliegue
- Monitorear logs de CloudFormation para errores de importación
- Implementar health checks que verifiquen la conectividad entre servicios

---

## 🛠️ **COMANDOS ÚTILES**

### **Verificar Exports Disponibles:**
```bash
aws cloudformation list-exports --region us-east-1 | grep agent-scl-dev
```

### **Validar Dependencias:**
```bash
# Validar infraestructura principal
serverless print --stage dev

# Validar cada servicio
cd serverless/services/auth && serverless print --stage dev
cd serverless/services/payment && serverless print --stage dev
cd serverless/services/tenant && serverless print --stage dev
cd serverless/services/user && serverless print --stage dev
```

### **Despliegue Ordenado:**
```bash
# Opción 1: Manual
# 1. Infraestructura
serverless deploy --stage dev

# 2. Auth
cd serverless/services/auth && serverless deploy --stage dev

# 3. Otros servicios (en paralelo)
cd serverless/services/payment && serverless deploy --stage dev &
cd serverless/services/tenant && serverless deploy --stage dev &
cd serverless/services/user && serverless deploy --stage dev &
wait

# Opción 2: Script automatizado
./scripts/validate-and-deploy.sh dev
```

---

## 📊 **MAPEO DE EXPORTS REALES**

### **Exports Disponibles vs Esperados:**
```yaml
# Infraestructura Principal
Disponible: agent-scl-dev-DynamoDBTableName ✅
Disponible: agent-scl-dev-PlatformDataBucketName ✅
Disponible: agent-scl-dev-LambdaExecutionRoleArn ✅
Disponible: agent-scl-dev-JWTSecretArn ✅
Disponible: agent-scl-dev-DatabaseSecretArn ✅
Disponible: agent-scl-dev-StripeSecretArn ✅
Disponible: agent-scl-dev-ApplicationSecretArn ✅

# API Gateway (nombres alternativos)
Disponible: agent-scl-dev-ApiGatewayRestApiId
Esperado:   agent-scl-dev-RestApiId
Disponible: agent-scl-dev-ApiGatewayRootResourceId
Esperado:   agent-scl-dev-RestApiRootResourceId

# VPC y Security Groups
Disponible: agent-scl-dev-LambdaSecurityGroupId ✅
Disponible: agent-scl-dev-PrivateSubnetId1 ✅
Disponible: agent-scl-dev-PrivateSubnetId2 ✅

# Servicio Auth
Disponible: agent-scl-dev-JwtAuthorizerArn ✅
```

### **Aliases de Compatibilidad:**
Los siguientes exports tienen aliases para mantener compatibilidad:
- `RestApiId` → `ApiGatewayRestApiId`
- `RestApiRootResourceId` → `ApiGatewayRootResourceId`
