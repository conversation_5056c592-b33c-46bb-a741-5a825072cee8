# ADR-002: Layered Architecture with Shared Lambda Layer

## Status
**ACCEPTED** - Implemented in Phase 1-4

## Context
The platform consists of multiple microservices that need to share common functionality (authentication, validation, database access, logging) while maintaining independence for deployment and scaling. Traditional approaches either duplicate code or create tight coupling between services.

## Decision
We implemented a **Layered Architecture** using AWS Lambda Layers for shared functionality:

### **Architecture Layers:**
1. **Shared Layer** (`shared/python/shared/`) - Common utilities, patterns, and infrastructure
2. **Service Layer** (`services/*/src/`) - Business logic specific to each service
3. **Handler Layer** (`services/*/src/handlers/`) - API endpoints and event handlers
4. **Infrastructure Layer** (Serverless Framework) - AWS resources and deployment

### **Shared Layer Contents:**
```
shared/python/shared/
├── auth.py                    # Authentication & authorization
├── database.py               # Database clients & patterns
├── validators.py             # Input validation
├── error_handler.py          # Standardized error handling
├── business_logic_decorator.py # Unified handler patterns
├── logger.py                 # Structured logging
├── responses.py              # Standardized API responses
├── exceptions.py             # Custom exception hierarchy
└── middleware/               # Cross-cutting concerns
```

### **Service Independence:**
- Each service has its own `serverless.yml`
- Independent deployment pipelines
- Service-specific business logic and models
- Shared layer referenced as Lambda Layer

### **Import Strategy:**
```python
# Shared functionality (via Lambda Layer)
from shared.auth import get_auth_context
from shared.database import db_client
from shared.business_logic_decorator import business_logic_handler

# Service-specific functionality (local)
from ..models.user import User
from ..services.user_service import UserService
```

## Consequences

### **Positive:**
- ✅ **Code Reuse**: Common functionality shared across all services
- ✅ **Consistency**: Standardized patterns for error handling, validation, logging
- ✅ **Independent Deployment**: Services can be deployed separately
- ✅ **Reduced Bundle Size**: Shared code not duplicated in each service
- ✅ **Maintainability**: Changes to shared functionality propagate automatically
- ✅ **Testing**: Shared layer can be tested independently

### **Negative:**
- ⚠️ **Layer Dependency**: Services depend on shared layer version
- ⚠️ **Deployment Coordination**: Shared layer changes require careful rollout
- ⚠️ **Debugging Complexity**: Stack traces span multiple layers

### **Design Principles Enforced:**
- **Separation of Concerns**: Clear boundaries between layers
- **DRY (Don't Repeat Yourself)**: Shared functionality centralized
- **Single Responsibility**: Each layer has specific purpose
- **Dependency Direction**: Services depend on shared layer, not vice versa

### **Deployment Strategy:**
1. Deploy shared layer first
2. Update service configurations to reference new layer version
3. Deploy services independently
4. Rollback strategy: revert to previous layer version

## Implementation Files
- `shared/python/shared/` - Shared layer implementation
- `serverless/shared/` - Shared infrastructure configuration
- `services/*/serverless.yml` - Service-specific configurations
- `docs/MODULAR_ARCHITECTURE.md` - Architecture documentation

## Metrics
- **Code Duplication**: Reduced from ~40% to <5%
- **Deployment Time**: Individual services: 2-3 minutes (vs 15+ minutes for monolith)
- **Bundle Size**: Average service bundle: 2MB (vs 15MB without layer)

## Related ADRs
- ADR-001: Dependency Injection Container
- ADR-003: Decorator Pattern for Cross-Cutting Concerns
- ADR-004: Repository Pattern Implementation

## Review Date
Next review: 2024-12-01 (6 months from implementation)
