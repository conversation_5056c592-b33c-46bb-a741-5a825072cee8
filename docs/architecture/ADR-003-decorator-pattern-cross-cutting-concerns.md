# ADR-003: Decorator Pattern for Cross-Cutting Concerns

## Status
**ACCEPTED** - Implemented in Phase 5

## Context
Cross-cutting concerns like authentication, validation, error handling, logging, and resilience patterns were being implemented inconsistently across handlers. This led to code duplication, inconsistent behavior, and maintenance overhead.

## Decision
We implemented a **Decorator Pattern** approach for all cross-cutting concerns with a unified business logic decorator:

### **Decorator Hierarchy:**
```python
@business_logic_handler(
    service_name="auth",
    operation_name="login",
    method="POST",
    path="/auth/login",
    require_auth=False,
    body_validator=LoginRequestValidator
)
def login_handler(event, context):
    # Pure business logic here
    validated_data = event['validated_body']
    return APIResponse.success(data=result)
```

### **Individual Decorators:**
- **`@with_error_handling`** - Standardized exception handling
- **`@with_validation`** - Request validation (body, path, query, headers)
- **`@auth_resilience`** - Circuit breaker, retry, rate limiting
- **`@authenticated_handler`** - Authentication required
- **`@public_handler`** - No authentication required
- **`@admin_handler`** - Admin role required

### **Unified Decorator Features:**
1. **Request/Response Logging** - Structured logging with context
2. **Authentication & Authorization** - JWT validation and role checking
3. **Request Validation** - Body, path parameters, query parameters, headers
4. **Error Handling** - Consistent exception handling and response formatting
5. **Performance Monitoring** - Duration tracking and metrics
6. **Security Logging** - Audit trails for sensitive operations

### **Implementation Pattern:**
```python
# Before (manual implementation)
def login_handler(event, context):
    try:
        # Manual auth check
        auth_context = get_auth_context(event)
        
        # Manual validation
        body = json.loads(event.get('body', '{}'))
        if not body.get('email'):
            return APIResponse.error("Email required", 400)
        
        # Business logic
        result = perform_login(body)
        
        # Manual logging
        lambda_logger.info("Login successful")
        
        return APIResponse.success(data=result)
        
    except Exception as e:
        lambda_logger.error(f"Login failed: {str(e)}")
        return APIResponse.error("Login failed", 500)

# After (decorator-based)
@business_logic_handler(
    service_name="auth",
    operation_name="login",
    body_validator=LoginRequestValidator,
    require_auth=False
)
def login_handler(event, context):
    # Pure business logic
    validated_data = event['validated_body']
    result = perform_login(validated_data)
    return APIResponse.success(data=result)
```

## Consequences

### **Positive:**
- ✅ **Consistency**: All handlers follow same patterns
- ✅ **Reduced Boilerplate**: 70% reduction in handler code
- ✅ **Maintainability**: Cross-cutting concerns centralized
- ✅ **Testability**: Business logic isolated from infrastructure concerns
- ✅ **Composability**: Decorators can be combined as needed
- ✅ **Standardization**: Uniform error handling, logging, validation

### **Negative:**
- ⚠️ **Learning Curve**: Developers need to understand decorator system
- ⚠️ **Debugging**: Stack traces include decorator layers
- ⚠️ **Magic Behavior**: Some functionality hidden behind decorators

### **Cross-Cutting Concerns Addressed:**
- **Authentication**: JWT validation, role checking
- **Authorization**: Permission validation, tenant isolation
- **Validation**: Request body, path parameters, query parameters
- **Error Handling**: Exception catching, response formatting
- **Logging**: Request/response logging, audit trails
- **Monitoring**: Performance metrics, duration tracking
- **Resilience**: Circuit breakers, retries, rate limiting

### **Performance Impact:**
- **Overhead**: ~2-5ms per request (acceptable for business logic)
- **Memory**: Minimal additional memory usage
- **Maintainability**: Significant improvement in code maintainability

## Implementation Files
- `shared/python/shared/business_logic_decorator.py` - Unified decorator
- `shared/python/shared/error_handler.py` - Error handling decorator
- `shared/python/shared/validation_manager.py` - Validation decorator
- `shared/python/shared/middleware/resilience_middleware.py` - Resilience decorators

## Usage Examples
```python
# Public endpoint with validation
@public_handler("auth", "register", body_validator=RegisterRequestValidator)
def register_handler(event, context): pass

# Authenticated endpoint with path parameters
@authenticated_handler("user", "get_profile", path_params=["user_id"])
def get_profile_handler(event, context): pass

# Admin endpoint with multiple validations
@admin_handler(
    "tenant", "create_tenant",
    body_validator=CreateTenantValidator,
    required_headers=["X-Tenant-Id"]
)
def create_tenant_handler(event, context): pass
```

## Related ADRs
- ADR-001: Dependency Injection Container
- ADR-002: Layered Architecture Pattern
- ADR-005: Standardized Error Handling Strategy

## Review Date
Next review: 2024-12-01 (6 months from implementation)
