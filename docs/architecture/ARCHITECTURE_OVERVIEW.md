# Agent SCL Platform - Architecture Overview

## 📋 Document Information
- **Version**: 1.0
- **Last Updated**: 2024-08-05
- **Status**: Current
- **Audience**: Development Team, Technical Leadership

## 🎯 Executive Summary

The Agent SCL Platform implements a **modern serverless microservices architecture** on AWS, designed for scalability, maintainability, and operational excellence. The architecture follows industry best practices and implements proven design patterns for enterprise-grade applications.

### **Key Architectural Principles:**
- **Microservices**: Independent, deployable services
- **Serverless-First**: AWS Lambda for compute, managed services for infrastructure
- **Multi-Tenant**: Secure tenant isolation at all layers
- **Event-Driven**: Asynchronous communication between services
- **API-First**: RESTful APIs with OpenAPI specifications

## 🏗️ High-Level Architecture

### **Service Decomposition:**
```
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│   Auth Service  │ Payment Service │ Tenant Service  │  User Service   │
├─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ • Authentication│ • Subscriptions │ • Provisioning │ • Profile Mgmt  │
│ • Authorization │ • Stripe Integ. │ • Configuration │ • Permissions   │
│ • JWT Management│ • Billing       │ • Multi-tenancy │ • User Roles    │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
                              │
                    ┌─────────────────┐
                    │  Shared Layer   │
                    │ • Common Utils  │
                    │ • Auth & Valid. │
                    │ • Error Handling│
                    │ • Database      │
                    └─────────────────┘
```

### **Technology Stack:**
- **Compute**: AWS Lambda (Python 3.11)
- **API Gateway**: AWS API Gateway with custom domain
- **Database**: Amazon DynamoDB with GSI
- **Authentication**: JWT with AWS Secrets Manager
- **Monitoring**: CloudWatch + X-Ray
- **Infrastructure**: Serverless Framework
- **Payment**: Stripe API integration

## 🎨 Design Patterns Implemented

### **1. Dependency Injection Container**
```python
# Service registration
container.register_singleton(IUserRepository, UserRepository)
container.register_scoped(IUserService, UserService)

# Automatic dependency resolution
@injectable(IEmailService, ServiceLifetime.SINGLETON)
class EmailService:
    def __init__(self, secrets_manager: ISecretsManager):
        self.secrets_manager = secrets_manager
```

**Benefits:**
- Loose coupling between components
- Easy unit testing with mocks
- Centralized service configuration
- Automatic lifecycle management

### **2. Repository Pattern**
```python
# Abstract interface
class IUserRepository(ABC):
    @abstractmethod
    async def get_by_id(self, user_id: str) -> Optional[User]:
        pass

# Concrete implementation
class UserRepository(IUserRepository):
    async def get_by_id(self, user_id: str) -> Optional[User]:
        # DynamoDB-specific implementation
        pass
```

**Benefits:**
- Data access abstraction
- Database technology independence
- Consistent query patterns
- Easy testing with mock repositories

### **3. Decorator Pattern for Cross-Cutting Concerns**
```python
@business_logic_handler(
    service_name="auth",
    operation_name="login",
    method="POST",
    path="/auth/login",
    body_validator=LoginRequestValidator,
    require_auth=False
)
def login_handler(event, context):
    # Pure business logic
    validated_data = event['validated_body']
    return perform_login(validated_data)
```

**Benefits:**
- Separation of concerns
- Consistent cross-cutting behavior
- Reduced boilerplate code
- Composable functionality

### **4. Strategy Pattern for Validation**
```python
# Validation strategies
class LoginRequestValidator:
    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        # Login-specific validation logic
        pass

# Unified validation manager
validation_manager.validate_request_body(event, LoginRequestValidator)
```

**Benefits:**
- Pluggable validation rules
- Consistent validation patterns
- Easy to extend and modify
- Reusable validation logic

### **5. Chain of Responsibility for Error Handling**
```python
# Error handling chain
ValidationException -> AuthenticationException -> PlatformException -> Exception

# Centralized error handler
error_handler.handle_exception(exception, context)
```

**Benefits:**
- Consistent error responses
- Proper error logging
- Graceful degradation
- Centralized error policies

## 🏛️ Layered Architecture

### **Layer Responsibilities:**

#### **1. Presentation Layer (Handlers)**
- HTTP request/response handling
- Input validation and sanitization
- Authentication and authorization
- Response formatting

#### **2. Business Logic Layer (Services)**
- Domain logic implementation
- Business rule enforcement
- Workflow orchestration
- Transaction management

#### **3. Data Access Layer (Repositories)**
- Data persistence abstraction
- Query optimization
- Data mapping and transformation
- Cache management

#### **4. Infrastructure Layer (Shared)**
- Cross-cutting concerns
- External service integration
- Configuration management
- Monitoring and logging

### **Dependency Flow:**
```
Handlers → Services → Repositories → Database
    ↓         ↓           ↓
  Shared ← Shared ← Shared
```

## 🔒 Security Architecture

### **Multi-Tenant Security:**
- **Tenant Isolation**: Row-level security in DynamoDB
- **JWT Tokens**: Signed with tenant-specific secrets
- **API Authorization**: Role-based access control (RBAC)
- **Data Encryption**: At rest and in transit

### **Authentication Flow:**
1. User provides credentials
2. Validate against tenant-specific user store
3. Generate JWT with tenant and user context
4. All subsequent requests validated against JWT
5. Tenant isolation enforced at data layer

## 📊 Data Architecture

### **DynamoDB Design:**
```
Primary Table: agent_scl_main
├── PK: TENANT#{tenant_id}#USER#{user_id}
├── SK: PROFILE
├── GSI1: EMAIL#{email}
└── GSI2: TENANT#{tenant_id}#ROLE#{role}

Subscription Table: agent_scl_subscriptions
├── PK: TENANT#{tenant_id}
├── SK: SUBSCRIPTION#{subscription_id}
└── GSI1: STRIPE_CUSTOMER#{customer_id}
```

### **Multi-Tenant Data Isolation:**
- All data includes tenant_id in partition key
- Global Secondary Indexes for cross-tenant queries (admin only)
- Automatic tenant filtering in all queries
- Tenant-specific encryption keys

## 🚀 Deployment Architecture

### **Infrastructure as Code:**
- **Serverless Framework**: Service definitions and AWS resources
- **Shared Infrastructure**: VPC, DynamoDB, Secrets Manager
- **Service-Specific**: Lambda functions, API Gateway routes
- **Environment Management**: Dev, staging, production configurations

### **Deployment Strategy:**
1. **Shared Layer**: Deploy common utilities first
2. **Infrastructure**: Deploy shared AWS resources
3. **Services**: Deploy each service independently
4. **Integration Tests**: Validate service interactions
5. **Monitoring**: Enable CloudWatch and X-Ray

## 📈 Scalability & Performance

### **Horizontal Scaling:**
- **Lambda Concurrency**: Auto-scaling based on demand
- **DynamoDB**: On-demand billing with burst capacity
- **API Gateway**: Built-in rate limiting and throttling
- **CDN**: CloudFront for static assets

### **Performance Optimizations:**
- **Connection Pooling**: Reuse database connections
- **Caching**: In-memory caching for frequently accessed data
- **Lazy Loading**: Load data only when needed
- **Batch Operations**: Bulk database operations where possible

## 🔍 Monitoring & Observability

### **Logging Strategy:**
- **Structured Logging**: JSON format with consistent fields
- **Correlation IDs**: Request tracing across services
- **Security Events**: Audit trail for sensitive operations
- **Performance Metrics**: Response times and error rates

### **Monitoring Tools:**
- **CloudWatch**: Metrics, logs, and alarms
- **X-Ray**: Distributed tracing
- **Custom Dashboards**: Business and technical metrics
- **Alerting**: Proactive issue detection

## 📚 Related Documentation

- [ADR-001: Dependency Injection Container](./ADR-001-dependency-injection-container.md)
- [ADR-002: Layered Architecture Pattern](./ADR-002-layered-architecture-pattern.md)
- [ADR-003: Decorator Pattern for Cross-Cutting Concerns](./ADR-003-decorator-pattern-cross-cutting-concerns.md)
- [Modular Architecture Guide](../MODULAR_ARCHITECTURE.md)
- [API Specifications](../../project-context/api_specifications.md)
- [Security Guidelines](../../project-context/security_multitenancy_guidelines.md)

## 🔄 Architecture Evolution

### **Current State (v1.0):**
- Microservices with shared layer
- Serverless compute model
- Multi-tenant data architecture
- Comprehensive design patterns

### **Future Considerations:**
- Event-driven architecture with EventBridge
- CQRS pattern for read/write separation
- Saga pattern for distributed transactions
- GraphQL API layer for complex queries

---

**Last Review**: 2024-08-05  
**Next Review**: 2024-12-01  
**Reviewers**: Development Team, Architecture Committee
