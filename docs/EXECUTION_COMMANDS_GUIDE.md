# 🚀 **GUÍA DE COMANDOS DE EJECUCIÓN**

## 📋 **COMANDOS POR FASE**

### **🔴 FASE 1: LIMPIEZA FINAL (30 min)**

#### **Comando Principal:**
```bash
python scripts/clean-duplicate-configs.py
```

#### **Comandos de Verificación:**
```bash
# Verificar limpieza exitosa
ls -la serverless/services/
# Resultado esperado: solo api-gateway, shared (si existen)

# Verificar estructura principal intacta
ls -la services/
# Resultado esperado: admin, auth, events, payment, security, tenant, user

# Validar configuraciones
grep -r "handler:" services/*/serverless.yml
# Resultado esperado: todos usan "src.handlers.*.handler"

# Verificar que no hay referencias a src/ inexistente
find serverless/ -name "*.yml" -exec grep -l "../../../src/" {} \;
# Resultado esperado: sin resultados
```

#### **Troubleshooting Fase 1:**
```bash
# Si el script falla, eliminar manualmente:
rm -rf serverless/services/auth
rm -rf serverless/services/payment
rm -rf serverless/services/tenant
rm -rf serverless/services/user
rm -rf serverless/services/admin
rm -rf serverless/services/security
rm -rf serverless/services/events

# Verificar que solo queden configuraciones válidas
ls serverless/services/
```

---

### **🟡 FASE 2: MIGRACIÓN DE TESTS (2-4 hrs)**

#### **2.1 Análisis Inicial:**
```bash
# Encontrar tests con imports incorrectos
find tests/ -name "*.py" -exec grep -l "from src\." {} \;
find tests/ -name "*.py" -exec grep -l "import src\." {} \;

# Contar archivos afectados
find tests/ -name "*.py" -exec grep -l "from src\." {} \; | wc -l
```

#### **2.2 Actualizar conftest.py:**
```bash
# Backup del archivo original
cp tests/conftest.py tests/conftest.py.backup

# Editar tests/conftest.py
cat > tests/conftest.py << 'EOF'
import sys
import pytest
from pathlib import Path
from unittest.mock import Mock, patch

# Configurar paths para nueva estructura
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "services" / "auth" / "src"))
sys.path.insert(0, str(project_root / "services" / "payment" / "src"))
sys.path.insert(0, str(project_root / "services" / "tenant" / "src"))
sys.path.insert(0, str(project_root / "services" / "user" / "src"))
sys.path.insert(0, str(project_root / "services" / "admin" / "src"))
sys.path.insert(0, str(project_root / "shared" / "python"))

# Fixtures globales
@pytest.fixture
def mock_db_client():
    with patch('shared.database.db_client') as mock:
        yield mock

@pytest.fixture
def mock_lambda_context():
    context = Mock()
    context.function_name = "test-function"
    context.aws_request_id = "test-request-id"
    return context

@pytest.fixture
def sample_event():
    return {
        'httpMethod': 'POST',
        'path': '/test',
        'headers': {'Content-Type': 'application/json'},
        'body': '{}',
        'requestContext': {'requestId': 'test-request'}
    }
EOF
```

#### **2.3 Migrar Tests Específicos:**
```bash
# Migrar test de auth models
sed -i 's/from src\.auth\.models\./from models\./g' tests/unit/test_auth_models.py
sed -i 's/from src\.shared\./from shared\./g' tests/unit/test_auth_models.py

# Migrar test de shared auth
sed -i 's/from src\.shared\./from shared\./g' tests/unit/test_shared_auth.py

# Migrar tests de integración
sed -i 's/from src\.auth\.handlers\./from handlers\./g' tests/integration/test_auth_flow.py
sed -i 's/from src\.shared\./from shared\./g' tests/integration/test_auth_flow.py
```

#### **2.4 Ejecutar Tests:**
```bash
# Tests unitarios
python -m pytest tests/unit/ -v --tb=short

# Tests de integración
python -m pytest tests/integration/ -v --tb=short

# Tests con coverage
python -m pytest tests/ --cov=services --cov=shared --cov-report=html --cov-report=term-missing
```

#### **Troubleshooting Fase 2:**
```bash
# Si hay errores de import, verificar paths:
python -c "
import sys
sys.path.insert(0, 'services/auth/src')
sys.path.insert(0, 'shared/python')
try:
    from models.user import User
    from shared.database import db_client
    print('✅ Imports funcionan correctamente')
except ImportError as e:
    print(f'❌ Error de import: {e}')
"

# Verificar estructura de archivos
find services/auth/src/models/ -name "*.py"
find shared/python/shared/ -name "*.py"
```

---

### **🟢 FASE 3: OPTIMIZACIÓN DEPENDENCIAS (1-2 hrs)**

#### **3.1 Crear requirements-dev.txt:**
```bash
cat > requirements-dev.txt << 'EOF'
# Development and testing dependencies
pytest==8.2.2
pytest-asyncio==0.23.7
pytest-cov==5.0.0
pytest-mock==3.14.0
moto==5.0.9
black==24.4.2
flake8==7.1.0
mypy==1.10.1
types-requests==2.32.0.20240622
EOF
```

#### **3.2 Limpiar requirements.txt:**
```bash
# Backup del archivo original
cp requirements.txt requirements.txt.backup

cat > requirements.txt << 'EOF'
# Core AWS dependencies
boto3==1.34.144
botocore==1.34.144

# Data validation
pydantic==2.7.4
pydantic-settings==2.3.4

# Authentication and security
PyJWT==2.8.0
cryptography==42.0.8
passlib==1.7.4
bcrypt==4.1.3

# Payment processing
stripe==12.0.0

# HTTP client
requests==2.32.3

# Data validation
email-validator==2.2.0

# Utilities
python-dotenv==1.0.1
python-dateutil==2.9.0.post0

# Logging and monitoring
structlog==24.2.0
aws-xray-sdk==2.12.1

# Lambda-specific
aws-lambda-powertools==2.38.0

# JSON processing
orjson==3.10.5

# Validation
jsonschema==4.22.0
EOF
```

#### **3.3 Actualizar Package.json:**
```bash
# Agregar nuevos scripts
npm pkg set scripts.install:dev="pip install -r requirements-dev.txt"
npm pkg set scripts.install:prod="pip install -r requirements.txt"
npm pkg set scripts.install:all="pip install -r requirements.txt -r requirements-dev.txt"
npm pkg set scripts.test:coverage="python -m pytest tests/ --cov=services --cov=shared --cov-report=html --cov-report=term-missing"
```

#### **3.4 Validar Instalación:**
```bash
# Crear entorno virtual limpio
python -m venv test_env
source test_env/bin/activate  # Linux/Mac
# test_env\Scripts\activate   # Windows

# Instalar solo producción
pip install -r requirements.txt

# Verificar que no hay dependencias de desarrollo
pip list | grep -E "(pytest|black|flake8|mypy)"
# Resultado esperado: sin resultados

deactivate
rm -rf test_env
```

---

### **🟡 FASE 4: VALIDACIÓN COMPLETA (1-2 hrs)**

#### **4.1 Validación de Estructura:**
```bash
python scripts/validate-project-structure.py
```

#### **4.2 Linting y Formateo:**
```bash
# Instalar dependencias de desarrollo
pip install -r requirements-dev.txt

# Ejecutar linting
python -m flake8 services/
# Resultado esperado: sin errores

# Ejecutar formateo
python -m black services/ --check
# Si hay archivos sin formatear:
python -m black services/

# Type checking
python -m mypy services/ --ignore-missing-imports
```

#### **4.3 Testing Completo:**
```bash
# Tests unitarios
python -m pytest tests/unit/ -v

# Tests de integración
python -m pytest tests/integration/ -v

# Coverage completo
python -m pytest tests/ --cov=services --cov=shared --cov-report=html --cov-fail-under=80

# Abrir reporte de coverage
open htmlcov/index.html  # Mac
# start htmlcov/index.html  # Windows
```

#### **4.4 Validación de Imports:**
```bash
# Verificar que no hay imports incorrectos
grep -r "from src\." services/
# Resultado esperado: sin resultados

grep -r "from \.\.\.shared\." services/
# Resultado esperado: sin resultados

# Verificar imports correctos
grep -r "from shared\." services/ | head -5
# Resultado esperado: múltiples líneas con imports correctos
```

---

### **🟢 FASE 5: DOCUMENTACIÓN Y DEPLOY (2-3 hrs)**

#### **5.1 Crear Documentación:**
```bash
# Crear guía de testing
cat > docs/TESTING_GUIDE.md << 'EOF'
# Testing Guide

## Estructura
- tests/unit/ - Tests unitarios
- tests/integration/ - Tests de integración

## Comandos
```bash
npm run test:unit        # Tests unitarios
npm run test:integration # Tests integración
npm run test:coverage   # Con coverage
```

## Configuración
- pytest.ini - Configuración principal
- conftest.py - Fixtures compartidas
EOF

# Actualizar developer guide
echo "
## Desarrollo Local
npm run install:dev  # Instalar deps desarrollo
npm run test        # Ejecutar tests
npm run lint        # Verificar código
" >> docs/DEVELOPER_GUIDE.md
```

#### **5.2 Validar Deployment:**
```bash
# Verificar configuraciones antes de deploy
serverless config validate --config services/auth/serverless.yml

# Deploy en dev (si está configurado)
npm run deploy:dev

# Verificar health checks (si están implementados)
curl -f https://api-dev.domain.com/auth/health || echo "Health check no disponible"
```

---

### **🔵 FASE 6: OPTIMIZACIONES FINALES (3-5 hrs)**

#### **6.1 Health Checks:**
```bash
# Implementar health check básico en cada servicio
# Ejemplo para auth service:
cat > services/auth/src/handlers/health.py << 'EOF'
def handler(event, context):
    return {
        'statusCode': 200,
        'body': {
            'status': 'healthy',
            'service': 'auth',
            'timestamp': time.time()
        }
    }
EOF
```

#### **6.2 Monitoring Setup:**
```bash
# Verificar CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/agent-scl"

# Crear dashboard básico (si AWS CLI está configurado)
aws cloudwatch put-dashboard --dashboard-name "agent-scl-dev" --dashboard-body file://monitoring/dashboard.json
```

---

## 🔧 **COMANDOS DE TROUBLESHOOTING GENERAL**

### **Verificar Estado del Proyecto:**
```bash
# Estructura general
tree -L 3 -I 'node_modules|__pycache__|*.pyc'

# Verificar imports en todos los archivos
find services/ -name "*.py" -exec grep -l "from src\." {} \;
find services/ -name "*.py" -exec grep -l "from \.\.\.shared\." {} \;

# Verificar configuraciones serverless
find services/ -name "serverless.yml" -exec grep -H "handler:" {} \;
```

### **Reset Completo (Si algo sale mal):**
```bash
# Restaurar desde backup (si existe)
cp tests/conftest.py.backup tests/conftest.py
cp requirements.txt.backup requirements.txt

# Limpiar entornos virtuales
rm -rf venv/ test_env/

# Reinstalar dependencias
pip install -r requirements.txt -r requirements-dev.txt
```

### **Validación Final:**
```bash
# Ejecutar todas las validaciones
python scripts/validate-project-structure.py
python -m flake8 services/
python -m pytest tests/ --cov=services --cov=shared
grep -r "from src\." services/ || echo "✅ No hay imports incorrectos"
```

**🎯 RESULTADO ESPERADO: PROYECTO 10/10 PRODUCTION-READY**
