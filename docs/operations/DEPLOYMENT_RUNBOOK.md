# 🚀 Deployment Runbook - Agent SCL Platform

## 📋 Overview

This runbook provides step-by-step procedures for deploying the Agent SCL Platform across different environments. It includes pre-deployment checks, deployment procedures, post-deployment validation, and rollback procedures.

## 🎯 Deployment Strategy

### **Environment Flow**
```
Development → Staging → Production
```

### **Deployment Types**
- **Full Deployment**: Complete infrastructure and application deployment
- **Service Deployment**: Individual microservice deployment
- **Coordinated Deployment**: Shared layer updates with dependency management
- **Hotfix Deployment**: Critical bug fixes
- **Rollback Deployment**: Revert to previous version

## 📋 Pre-Deployment Checklist

### **General Prerequisites**
- [ ] AWS CLI configured with appropriate credentials
- [ ] Serverless Framework installed (v3.x)
- [ ] Node.js 18+ installed
- [ ] Python 3.11+ installed
- [ ] Git repository access
- [ ] Environment variables configured

### **Environment-Specific Checks**

#### **Development Environment**
- [ ] Development AWS account access
- [ ] Test data prepared
- [ ] Development secrets configured
- [ ] Local testing completed

#### **Staging Environment**
- [ ] Staging AWS account access
- [ ] Production-like data (anonymized)
- [ ] Staging secrets configured
- [ ] Integration tests passed

#### **Production Environment**
- [ ] Production AWS account access
- [ ] Backup procedures verified
- [ ] Production secrets configured
- [ ] Change management approval
- [ ] Maintenance window scheduled
- [ ] Rollback plan prepared

## 🔧 Deployment Procedures

### **1. Full Platform Deployment**

#### **Step 1: Prepare Environment**
```bash
# Clone repository
git clone https://github.com/your-org/agent-scl-platform.git
cd agent-scl-platform

# Checkout specific version/tag
git checkout v1.0.0

# Install dependencies
npm install
pip install -r requirements.txt
```

#### **Step 2: Configure Environment**
```bash
# Set environment variables
export AWS_PROFILE=agent-scl-dev  # or staging/prod
export STAGE=dev  # or staging/prod
export AWS_REGION=us-east-1

# Verify AWS credentials
aws sts get-caller-identity
```

#### **Step 3: Deploy Shared Infrastructure**
```bash
# Deploy shared layer first
cd shared/python
serverless deploy --stage $STAGE --region $AWS_REGION

# Deploy shared infrastructure
cd ../../serverless/shared
serverless deploy --stage $STAGE --region $AWS_REGION
```

#### **Step 4: Deploy Microservices**
```bash
# Deploy services in order
SERVICES=("auth" "payment" "tenant" "user")

for service in "${SERVICES[@]}"; do
    echo "Deploying $service service..."
    cd ../../services/$service
    serverless deploy --stage $STAGE --region $AWS_REGION
    
    # Verify deployment
    if [ $? -eq 0 ]; then
        echo "✅ $service deployed successfully"
    else
        echo "❌ $service deployment failed"
        exit 1
    fi
done
```

#### **Step 5: Post-Deployment Validation**
```bash
# Run infrastructure validation
cd ../../scripts
python validate-infrastructure.py

# Run architecture validation
python validate-architecture.py

# Run health checks
curl -f https://api-platform-$STAGE.agentscl.com/health
```

### **2. Individual Service Deployment**

#### **Deploy Single Service**
```bash
# Navigate to service directory
cd services/auth  # or payment, tenant, user

# Deploy specific service
serverless deploy --stage $STAGE --region $AWS_REGION

# Deploy specific function (for code-only changes)
serverless deploy function --function login --stage $STAGE
```

#### **Verify Service Deployment**
```bash
# Check service health
curl -f https://api-platform-$STAGE.agentscl.com/health

# Test specific endpoints
curl -X POST https://api-platform-$STAGE.agentscl.com/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "test"}'
```

### **3. Hotfix Deployment**

#### **Emergency Hotfix Process**
```bash
# Create hotfix branch
git checkout -b hotfix/critical-fix

# Make necessary changes
# ... code changes ...

# Test locally
pytest tests/

# Deploy to staging first
export STAGE=staging
serverless deploy --stage $STAGE

# Validate fix
# ... validation steps ...

# Deploy to production
export STAGE=prod
serverless deploy --stage $STAGE

# Immediate validation
curl -f https://api-platform.agentscl.com/health
```

## 🔍 Post-Deployment Validation

### **Automated Validation**
```bash
#!/bin/bash
# scripts/post-deployment-validation.sh

STAGE=${1:-dev}
BASE_URL="https://api-platform-${STAGE}.agentscl.com"

echo "🔍 Starting post-deployment validation for $STAGE..."

# Health check
echo "1. Health check..."
curl -f "$BASE_URL/health" || exit 1

# Authentication test
echo "2. Authentication test..."
AUTH_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "test"}')

if echo "$AUTH_RESPONSE" | grep -q "access_token"; then
    echo "✅ Authentication working"
else
    echo "❌ Authentication failed"
    exit 1
fi

# Database connectivity
echo "3. Database connectivity..."
curl -f "$BASE_URL/user/profile" \
  -H "Authorization: Bearer test_token" || exit 1

echo "✅ All validations passed!"
```

### **Manual Validation Checklist**
- [ ] Health endpoints responding
- [ ] Authentication flow working
- [ ] Database queries executing
- [ ] External integrations functioning
- [ ] Monitoring and logging active
- [ ] Performance within acceptable limits

## 🔄 Rollback Procedures

### **Automatic Rollback Triggers**
- Health check failures
- Error rate > 5%
- Response time > 5 seconds
- Database connection failures

### **Manual Rollback Process**

#### **Service-Level Rollback**
```bash
# Get previous deployment
serverless deploy list --stage $STAGE

# Rollback to previous version
serverless rollback --timestamp TIMESTAMP --stage $STAGE

# Verify rollback
curl -f https://api-platform-$STAGE.agentscl.com/health
```

#### **Infrastructure Rollback**
```bash
# Rollback shared infrastructure
cd serverless/shared
serverless rollback --timestamp TIMESTAMP --stage $STAGE

# Rollback services
SERVICES=("auth" "payment" "tenant" "user")
for service in "${SERVICES[@]}"; do
    cd ../../services/$service
    serverless rollback --timestamp TIMESTAMP --stage $STAGE
done
```

### **Emergency Rollback**
```bash
#!/bin/bash
# scripts/emergency-rollback.sh

STAGE=${1:-prod}
TIMESTAMP=${2}

if [ -z "$TIMESTAMP" ]; then
    echo "Usage: $0 <stage> <timestamp>"
    exit 1
fi

echo "🚨 Emergency rollback for $STAGE to $TIMESTAMP"

# Rollback all services
SERVICES=("auth" "payment" "tenant" "user")
for service in "${SERVICES[@]}"; do
    echo "Rolling back $service..."
    cd services/$service
    serverless rollback --timestamp $TIMESTAMP --stage $STAGE
done

echo "✅ Emergency rollback completed"
```

## 📊 Monitoring and Alerting

### **Key Metrics to Monitor**
- **Health Check Status**: All services responding
- **Error Rate**: < 1% for normal operations
- **Response Time**: < 2 seconds average
- **Throughput**: Requests per second
- **Database Performance**: Query execution time

### **CloudWatch Alarms**
```yaml
# Example CloudWatch alarm configuration
HealthCheckAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: ${self:service}-${self:custom.stage}-health-check
    AlarmDescription: Health check failure
    MetricName: HealthCheckFailed
    Namespace: AWS/Lambda
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 2
    Threshold: 1
    ComparisonOperator: GreaterThanOrEqualToThreshold
```

### **Notification Channels**
- **Slack**: #alerts channel for immediate notifications
- **Email**: <EMAIL> for critical alerts
- **PagerDuty**: For production incidents requiring immediate response

## 🔧 Troubleshooting

### **Common Deployment Issues**

#### **Permission Errors**
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify IAM permissions
aws iam get-user
aws iam list-attached-user-policies --user-name YOUR_USER
```

#### **Resource Conflicts**
```bash
# Check existing resources
aws cloudformation describe-stacks --stack-name STACK_NAME

# Delete conflicting resources
aws cloudformation delete-stack --stack-name STACK_NAME
```

#### **Timeout Issues**
```bash
# Increase timeout in serverless.yml
timeout: 30  # seconds

# Check CloudWatch logs
aws logs describe-log-groups
aws logs get-log-events --log-group-name LOG_GROUP
```

### **Deployment Failure Recovery**

#### **Partial Deployment Failure**
1. Identify failed service/resource
2. Check CloudFormation stack status
3. Fix underlying issue
4. Retry deployment for failed component
5. Validate entire system

#### **Complete Deployment Failure**
1. Stop deployment process
2. Assess system state
3. Decide: fix forward or rollback
4. Execute chosen strategy
5. Validate system stability

## 📚 Additional Resources

### **Documentation**
- [Infrastructure as Code Guide](../project-context/infrastructure_as_code.md)
- [Architecture Overview](../docs/architecture/ARCHITECTURE_OVERVIEW.md)
- [API Reference](../docs/api/API_REFERENCE.md)

### **Tools and Scripts**
- `scripts/validate-infrastructure.py` - Infrastructure validation
- `scripts/validate-architecture.py` - Architecture validation
- `scripts/post-deployment-validation.sh` - Post-deployment checks
- `scripts/emergency-rollback.sh` - Emergency rollback procedure

### **Contacts**
- **Platform Team**: <EMAIL>
- **DevOps Lead**: <EMAIL>
- **On-Call Engineer**: +1-555-0123 (production issues)

---

## 🔄 **Coordinated Deployment Procedures**

### **Shared Layer Updates**

When updating the shared layer that affects multiple services, use the coordinated deployment strategy to avoid dependency conflicts:

#### **Phase 1: Disable Dependencies**
```bash
# Disable shared layer references
python scripts/disable-shared-layer-references.py

# Disable JWT authorizer references
python scripts/disable-jwt-authorizer.py
```

#### **Phase 2: Deploy Services Without Dependencies**
```bash
# Deploy all services without shared layer
for service in auth user admin payment tenant events security; do
  cd services/$service
  serverless deploy --stage $STAGE --region $REGION
  cd ../..
done
```

#### **Phase 3: Deploy Updated Shared Layer**
```bash
cd shared
serverless deploy --stage $STAGE --region $REGION
cd ..
```

#### **Phase 4: Restore Dependencies and Re-deploy**
```bash
# Re-enable shared layer references
python scripts/enable-shared-layer-references.py

# Re-enable JWT authorizer references
python scripts/enable-jwt-authorizer.py

# Re-deploy all services with optimizations
for service in auth user admin payment tenant events security; do
  cd services/$service
  serverless deploy --stage $STAGE --region $REGION
  cd ../..
done
```

#### **Validation Steps**
```bash
# Verify shared layer usage
aws lambda get-function --function-name agent-scl-user-dev-getProfile \
  --region us-east-1 --query 'Configuration.Layers[].Arn'

# Check function sizes (should be significantly reduced)
aws lambda get-function --function-name agent-scl-user-dev-getProfile \
  --region us-east-1 --query 'Configuration.CodeSize'
```

---

**Last Updated**: 2024-08-10
**Next Review**: 2024-11-10
**Document Owner**: Platform Team
