# Development Guide

## Getting Started

This guide covers the development setup, coding standards, and best practices for the Platform Multitenant Agents project.

## Prerequisites

### Required Software

- **Python 3.12+**: Main programming language
- **AWS CLI**: For AWS service interaction
- **Terraform 1.5+**: Infrastructure as Code
- **Git**: Version control
- **VS Code** (recommended): IDE with Python extensions

### AWS Setup

1. **Install AWS CLI**:
   ```bash
   # Windows
   msi installer from AWS website
   
   # macOS
   brew install awscli
   
   # Linux
   pip install awscli
   ```

2. **Configure AWS credentials**:
   ```bash
   aws configure
   # Enter your AWS Access Key ID, Secret Access Key, region, and output format
   ```

3. **Verify setup**:
   ```bash
   aws sts get-caller-identity
   ```

## Development Environment Setup

### 1. Clone Repository

```bash
git clone <repository-url>
cd the-jungle-agents
```

### 2. Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 3. Environment Variables

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
```

Required environment variables:

```env
# AWS Configuration
AWS_REGION=us-east-1
AWS_PROFILE=default

# Application Configuration
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# Database Configuration
DYNAMODB_TABLE_NAME=platform-dev

# Authentication
JWT_SECRET_KEY=your-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30

# Payment Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Configuration
FROM_EMAIL=<EMAIL>
REPLY_TO_EMAIL=<EMAIL>
```

### 4. Pre-commit Hooks

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

## Project Structure

```
agent-scl-platform/
├── src/                          # Source code
│   ├── auth/                     # Authentication service
│   │   ├── handlers/             # Lambda handlers
│   │   ├── models/               # Data models
│   │   └── services/             # Business logic
│   ├── payment/                  # Payment service
│   │   ├── handlers/             # Lambda handlers
│   │   ├── models/               # Data models
│   │   └── services/             # Business logic
│   └── shared/                   # Shared utilities
│       ├── auth.py               # JWT management
│       ├── database.py           # Database client
│       ├── exceptions.py         # Custom exceptions
│       ├── logger.py             # Logging utilities
│       ├── responses.py          # API response helpers
│       ├── secrets.py            # Secrets management
│       └── validators.py         # Input validation
├── terraform/                    # Infrastructure as Code
│   ├── modules/                  # Reusable modules
│   └── environments/             # Environment configs
├── tests/                        # Test suite
│   ├── unit/                     # Unit tests
│   ├── integration/              # Integration tests
│   └── infrastructure/           # Infrastructure tests
├── scripts/                      # Utility scripts
├── docs/                         # Documentation
└── requirements.txt              # Python dependencies
```

## Coding Standards

### Python Style Guide

We follow **PEP 8** with some modifications:

- **Line length**: 88 characters (Black default)
- **Imports**: Use absolute imports, group by standard/third-party/local
- **Docstrings**: Google style docstrings
- **Type hints**: Required for all public functions

### Code Formatting

```bash
# Format code with Black
black src/ tests/

# Sort imports with isort
isort src/ tests/

# Lint with flake8
flake8 src/ tests/

# Type checking with mypy
mypy src/
```

### Example Code Style

```python
# src/auth/services/user_service.py
"""User service for managing user operations."""

from typing import Optional, Dict, Any
from datetime import datetime

from ..models.user import User, UserStatus
from ...shared.exceptions import ValidationException
from ...shared.logger import logger


class UserService:
    """Service for user management operations."""
    
    def __init__(self, db_client) -> None:
        """Initialize user service.
        
        Args:
            db_client: Database client instance
        """
        self.db_client = db_client
    
    async def create_user(
        self,
        email: str,
        name: str,
        tenant_id: str,
        **kwargs
    ) -> User:
        """Create a new user.
        
        Args:
            email: User email address
            name: User full name
            tenant_id: Tenant identifier
            **kwargs: Additional user attributes
            
        Returns:
            Created user instance
            
        Raises:
            ValidationException: If input data is invalid
        """
        logger.info(f"Creating user: {email}")
        
        # Validation logic here
        if not email or "@" not in email:
            raise ValidationException("Invalid email address")
        
        # Business logic here
        user = User(
            email=email,
            name=name,
            tenant_id=tenant_id,
            status=UserStatus.PENDING_VERIFICATION,
            created_at=datetime.utcnow(),
            **kwargs
        )
        
        # Save to database
        await user.save()
        
        logger.info(f"User created successfully: {user.user_id}")
        return user
```

## Testing

### Test Structure

```
tests/
├── unit/                         # Unit tests
│   ├── auth/
│   │   ├── models/
│   │   └── services/
│   ├── payment/
│   └── shared/
├── integration/                  # Integration tests
│   ├── api/
│   └── database/
└── infrastructure/               # Infrastructure tests
    ├── test_aws_connectivity.py
    ├── test_deployment.py
    └── test_terraform_validation.py
```

### Running Tests

```bash
# Run all tests
python scripts/run_tests.py

# Run specific test types
python scripts/run_tests.py --types unit
python scripts/run_tests.py --types integration
python scripts/run_tests.py --types infrastructure

# Run with coverage
python scripts/run_tests.py --coverage

# Run specific test file
pytest tests/unit/auth/test_user_service.py -v

# Run with debugging
pytest tests/unit/auth/test_user_service.py -v -s --pdb
```

### Writing Tests

```python
# tests/unit/auth/services/test_user_service.py
"""Unit tests for user service."""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from src.auth.services.user_service import UserService
from src.auth.models.user import User, UserStatus
from src.shared.exceptions import ValidationException


class TestUserService:
    """Test cases for UserService."""
    
    @pytest.fixture
    def mock_db_client(self):
        """Mock database client."""
        return Mock()
    
    @pytest.fixture
    def user_service(self, mock_db_client):
        """User service instance."""
        return UserService(mock_db_client)
    
    def test_create_user_success(self, user_service):
        """Test successful user creation."""
        # Arrange
        email = "<EMAIL>"
        name = "Test User"
        tenant_id = "tenant_123"
        
        # Act
        with patch.object(User, 'save') as mock_save:
            user = user_service.create_user(email, name, tenant_id)
        
        # Assert
        assert user.email == email
        assert user.name == name
        assert user.tenant_id == tenant_id
        assert user.status == UserStatus.PENDING_VERIFICATION
        mock_save.assert_called_once()
    
    def test_create_user_invalid_email(self, user_service):
        """Test user creation with invalid email."""
        # Arrange
        email = "invalid-email"
        name = "Test User"
        tenant_id = "tenant_123"
        
        # Act & Assert
        with pytest.raises(ValidationException):
            user_service.create_user(email, name, tenant_id)
```

## Database Development

### DynamoDB Local Setup

```bash
# Install DynamoDB Local
python scripts/setup_local_db.py

# Start DynamoDB Local
python scripts/start_local_db.py

# Create tables
python scripts/create_tables.py

# Seed test data
python scripts/seed_data.py
```

### Database Operations

```python
# Example database operations
from src.shared.database import db_client

# Create item
await db_client.put_item(
    TableName="platform-dev",
    Item={
        "PK": "TENANT#123",
        "SK": "USER#456",
        "email": "<EMAIL>",
        "name": "Test User"
    }
)

# Get item
response = await db_client.get_item(
    TableName="platform-dev",
    Key={
        "PK": "TENANT#123",
        "SK": "USER#456"
    }
)

# Query items
response = await db_client.query(
    TableName="platform-dev",
    KeyConditionExpression="PK = :pk",
    ExpressionAttributeValues={
        ":pk": "TENANT#123"
    }
)
```

## API Development

### Creating New Endpoints

1. **Define handler function**:
   ```python
   # src/auth/handlers/user_handlers.py
   async def create_user_handler(event, context):
       """Create user endpoint handler."""
       try:
           # Parse request
           body = json.loads(event['body'])
           
           # Validate input
           user_data = CreateUserRequest(**body)
           
           # Business logic
           user = await user_service.create_user(**user_data.dict())
           
           # Return response
           return success_response(user.to_dict())
           
       except ValidationException as e:
           return error_response(str(e), 400)
       except Exception as e:
           logger.error(f"Unexpected error: {e}")
           return error_response("Internal server error", 500)
   ```

2. **Add route configuration**:
   ```yaml
   # serverless.yml
   functions:
     createUser:
       handler: src.auth.handlers.user_handlers.create_user_handler
       events:
         - http:
             path: users
             method: post
             cors: true
             authorizer: auth
   ```

3. **Add tests**:
   ```python
   # tests/integration/api/test_user_endpoints.py
   def test_create_user_endpoint():
       """Test create user API endpoint."""
       # Test implementation
   ```

## Debugging

### Local Development

```bash
# Run with debugger
python -m pdb scripts/dev_server.py

# Debug specific function
python -c "
import pdb; pdb.set_trace()
from src.auth.services.user_service import UserService
service = UserService()
"
```

### CloudWatch Logs

```bash
# View logs
aws logs describe-log-groups
aws logs get-log-events --log-group-name /aws/lambda/function-name

# Tail logs
aws logs tail /aws/lambda/function-name --follow
```

## Performance Optimization

### Database Optimization

- Use single-table design patterns
- Implement proper indexing strategies
- Use batch operations when possible
- Monitor DynamoDB metrics

### Lambda Optimization

- Minimize cold starts
- Use connection pooling
- Implement proper error handling
- Monitor execution metrics

## Security Best Practices

### Code Security

- Never commit secrets to version control
- Use environment variables for configuration
- Implement proper input validation
- Use parameterized queries
- Follow principle of least privilege

### AWS Security

- Use IAM roles instead of access keys
- Enable CloudTrail logging
- Use VPC for network isolation
- Encrypt data at rest and in transit
- Regular security audits

## Deployment

### Local Testing

```bash
# Test infrastructure
cd terraform/environments/dev
terraform plan

# Test application
python scripts/run_tests.py --types integration
```

### Staging Deployment

```bash
# Deploy infrastructure
cd terraform/environments/staging
terraform apply

# Deploy application
serverless deploy --stage staging
```

### Production Deployment

```bash
# Deploy infrastructure
cd terraform/environments/prod
terraform apply

# Deploy application
serverless deploy --stage prod
```

## Troubleshooting

### Common Issues

1. **Import errors**: Check PYTHONPATH and virtual environment
2. **AWS permissions**: Verify IAM roles and policies
3. **Database connection**: Check DynamoDB configuration
4. **Test failures**: Review test setup and mocking

### Getting Help

- **Documentation**: Check docs/ directory
- **Issues**: Create GitHub issue with details
- **Team Chat**: Use team communication channels
- **Code Review**: Request review from team members
