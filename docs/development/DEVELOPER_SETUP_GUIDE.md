# 👨‍💻 Developer Setup Guide - Agent SCL Platform

## 📋 Overview

This guide will help new developers set up their development environment for the Agent SCL Platform. Follow these steps to get your local development environment ready for contributing to the project.

## 🎯 Prerequisites

### **Required Software**
- **Python 3.11+**: Primary development language
- **Node.js 18+**: For Serverless Framework and tooling
- **Git**: Version control
- **AWS CLI**: AWS resource management
- **Docker**: For local testing (optional)

### **Required Accounts**
- **GitHub**: Access to the repository
- **AWS**: Development account access
- **Slack**: Team communication (optional)

## 🔧 Environment Setup

### **1. Install Required Software**

#### **Python 3.11+**
```bash
# macOS (using Homebrew)
brew install python@3.11

# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-pip python3.11-venv

# Windows (using Chocolatey)
choco install python --version=3.11.0

# Verify installation
python3.11 --version
```

#### **Node.js 18+**
```bash
# macOS (using Homebrew)
brew install node@18

# Ubuntu/Debian (using NodeSource)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Windows (using Chocolatey)
choco install nodejs --version=18.0.0

# Verify installation
node --version
npm --version
```

#### **AWS CLI**
```bash
# macOS/Linux
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Windows
# Download and run the AWS CLI MSI installer

# Verify installation
aws --version
```

#### **Serverless Framework**
```bash
# Install globally
npm install -g serverless@3

# Verify installation
serverless --version
```

### **2. Clone and Setup Repository**

#### **Clone Repository**
```bash
# Clone the repository
git clone https://github.com/your-org/agent-scl-platform.git
cd agent-scl-platform

# Create and switch to development branch
git checkout -b feature/your-feature-name
```

#### **Setup Python Environment**
```bash
# Create virtual environment
python3.11 -m venv venv

# Activate virtual environment
# macOS/Linux
source venv/bin/activate

# Windows
venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install development dependencies
pip install -r requirements-dev.txt
pip install -r requirements.txt
```

#### **Setup Node.js Dependencies**
```bash
# Install Node.js dependencies
npm install

# Install Serverless plugins
npm install -g serverless-python-requirements
npm install -g serverless-offline
```

### **3. Configure AWS Credentials**

#### **Setup AWS Profile**
```bash
# Configure AWS CLI
aws configure --profile agent-scl-dev

# Enter your credentials:
# AWS Access Key ID: YOUR_ACCESS_KEY
# AWS Secret Access Key: YOUR_SECRET_KEY
# Default region: us-east-1
# Default output format: json

# Set environment variable
export AWS_PROFILE=agent-scl-dev
```

#### **Verify AWS Access**
```bash
# Test AWS access
aws sts get-caller-identity

# Expected output:
# {
#     "UserId": "AIDACKCEVSQ6C2EXAMPLE",
#     "Account": "************",
#     "Arn": "arn:aws:iam::************:user/your-username"
# }
```

### **4. Environment Variables**

#### **Create .env File**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your values
nano .env
```

#### **Required Environment Variables**
```bash
# .env file content
# AWS Configuration
AWS_PROFILE=agent-scl-dev
AWS_REGION=us-east-1
STAGE=dev

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-for-development

# Database Configuration
DYNAMODB_TABLE_NAME=agent-scl-main-dev
DYNAMODB_REGION=us-east-1

# Stripe Configuration (use test keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_test_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration
SES_REGION=us-east-1
FROM_EMAIL=<EMAIL>

# Development Settings
LOG_LEVEL=DEBUG
DEBUG=true
```

## 🏗️ Project Structure

### **Understanding the Codebase**
```
agent-scl-platform/
├── docs/                          # Documentation
│   ├── api/                       # API documentation
│   ├── architecture/              # Architecture decisions
│   ├── development/               # Development guides
│   ├── operations/                # Operational runbooks
│   └── user-guides/               # User documentation
├── services/                      # Microservices
│   ├── auth/                      # Authentication service
│   ├── payment/                   # Payment service
│   ├── tenant/                    # Tenant management service
│   └── user/                      # User management service
├── shared/                        # Shared code
│   └── python/                    # Shared Python modules
│       └── shared/                # Shared utilities
├── serverless/                    # Infrastructure configuration
│   ├── shared/                    # Shared infrastructure
│   └── templates/                 # Service templates
├── tests/                         # Test suites
│   ├── unit/                      # Unit tests
│   ├── integration/               # Integration tests
│   └── fixtures/                  # Test fixtures
└── scripts/                       # Utility scripts
```

### **Key Directories**

#### **Services Structure**
```
services/auth/
├── src/
│   ├── handlers/                  # Lambda handlers
│   ├── services/                  # Business logic
│   ├── models/                    # Data models
│   └── utils/                     # Service utilities
├── tests/                         # Service-specific tests
├── serverless.yml                 # Service configuration
└── requirements.txt               # Service dependencies
```

#### **Shared Module Structure**
```
shared/python/shared/
├── auth.py                        # Authentication utilities
├── database.py                    # Database clients
├── validators.py                  # Input validation
├── error_handler.py               # Error handling
├── business_logic_decorator.py    # Handler decorators
├── logger.py                      # Structured logging
├── responses.py                   # API responses
└── exceptions.py                  # Custom exceptions
```

## 🧪 Development Workflow

### **1. Running Tests**

#### **Unit Tests**
```bash
# Run all unit tests
pytest tests/unit/ -v

# Run specific test file
pytest tests/unit/test_auth.py -v

# Run with coverage
pytest tests/unit/ --cov=shared --cov-report=html
```

#### **Integration Tests**
```bash
# Run integration tests
pytest tests/integration/ -v

# Run specific integration test
pytest tests/integration/test_auth_flow.py -v
```

#### **Test Coverage**
```bash
# Generate coverage report
pytest --cov=shared --cov=services --cov-report=html --cov-report=term

# View coverage report
open htmlcov/index.html  # macOS
xdg-open htmlcov/index.html  # Linux
```

### **2. Code Quality**

#### **Linting**
```bash
# Run flake8 linting
flake8 shared/ services/ tests/

# Run with specific configuration
flake8 --config=.flake8 shared/
```

#### **Code Formatting**
```bash
# Format code with black
black shared/ services/ tests/

# Check formatting without changes
black --check shared/ services/ tests/
```

#### **Type Checking (Optional)**
```bash
# Run mypy type checking
mypy shared/ services/
```

### **3. Local Development**

#### **Running Services Locally**
```bash
# Start specific service locally
cd services/auth
serverless offline --stage dev

# Service will be available at:
# http://localhost:3000/auth/login
# http://localhost:3000/auth/register
```

#### **Testing API Endpoints**
```bash
# Test registration endpoint
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_name": "Test Company",
    "user_name": "Test User",
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'

# Test login endpoint
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'
```

### **4. Database Development**

#### **Local DynamoDB (Optional)**
```bash
# Install DynamoDB Local
npm install -g dynamodb-local

# Start DynamoDB Local
dynamodb-local

# Configure local endpoint
export DYNAMODB_ENDPOINT=http://localhost:8000
```

#### **Database Operations**
```bash
# Create tables (development)
python scripts/create-dev-tables.py

# Seed test data
python scripts/seed-test-data.py

# Clear test data
python scripts/clear-test-data.py
```

## 🚀 Deployment

### **Development Deployment**
```bash
# Deploy to development environment
export STAGE=dev
export AWS_PROFILE=agent-scl-dev

# Deploy shared layer
cd shared/python
serverless deploy --stage dev

# Deploy specific service
cd ../../services/auth
serverless deploy --stage dev
```

### **Validation**
```bash
# Validate infrastructure
python scripts/validate-infrastructure.py

# Validate architecture
python scripts/validate-architecture.py

# Run post-deployment tests
pytest tests/integration/ --stage=dev
```

## 🔧 Troubleshooting

### **Common Issues**

#### **Python Environment Issues**
```bash
# Recreate virtual environment
deactivate
rm -rf venv
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements-dev.txt
```

#### **AWS Permission Issues**
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify IAM permissions
aws iam get-user
aws iam list-attached-user-policies --user-name YOUR_USER
```

#### **Serverless Deployment Issues**
```bash
# Clear Serverless cache
serverless remove --stage dev
serverless deploy --stage dev

# Check CloudFormation stack
aws cloudformation describe-stacks --stack-name STACK_NAME
```

### **Getting Help**

#### **Internal Resources**
- **Team Slack**: #development channel
- **Documentation**: This repository's docs/ folder
- **Code Reviews**: GitHub pull requests
- **Architecture Questions**: #architecture channel

#### **External Resources**
- **Serverless Framework**: [serverless.com/framework/docs](https://serverless.com/framework/docs)
- **AWS Documentation**: [docs.aws.amazon.com](https://docs.aws.amazon.com)
- **Python Best Practices**: [python.org/dev/peps](https://python.org/dev/peps)

## ✅ Development Checklist

### **Initial Setup**
- [ ] Python 3.11+ installed
- [ ] Node.js 18+ installed
- [ ] AWS CLI configured
- [ ] Serverless Framework installed
- [ ] Repository cloned
- [ ] Virtual environment created
- [ ] Dependencies installed
- [ ] Environment variables configured
- [ ] AWS credentials verified

### **Before First Commit**
- [ ] Tests passing locally
- [ ] Code formatted with black
- [ ] Linting passes
- [ ] Documentation updated
- [ ] Branch created from main
- [ ] Commit messages follow convention

### **Before Pull Request**
- [ ] All tests passing
- [ ] Code coverage maintained
- [ ] Documentation updated
- [ ] Architecture validation passes
- [ ] Integration tests pass
- [ ] Code review requested

## 🎉 Next Steps

Once your environment is set up:

1. **Read the Architecture Guide**: [docs/architecture/ARCHITECTURE_OVERVIEW.md](../architecture/ARCHITECTURE_OVERVIEW.md)
2. **Review API Documentation**: [docs/api/API_REFERENCE.md](../api/API_REFERENCE.md)
3. **Check Development Standards**: [project-context/development_standards.md](../../project-context/development_standards.md)
4. **Join Team Channels**: Get access to Slack channels and team resources
5. **Pick Your First Task**: Check the project board for beginner-friendly issues

---

**Welcome to the team!** 🎉

If you have any questions during setup, don't hesitate to reach out to the team in Slack or create an issue in the repository.
