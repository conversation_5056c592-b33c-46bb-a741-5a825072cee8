# 🔧 **Guía para DevOps - Agent SCL Platform**

## 🎯 **Introducción**

Esta guía proporciona información completa para equipos de DevOps responsables del despliegue, monitoreo y mantenimiento de la plataforma Agent SCL en AWS.

## 🏗️ **Arquitectura de Infraestructura**

### **Componentes Principales:**
- **🔧 Serverless Framework v4** - Orquestación de infraestructura
- **⚡ AWS Lambda** - Compute serverless
- **🗄️ DynamoDB** - Base de datos NoSQL
- **🌐 API Gateway** - Gestión de APIs
- **📦 S3** - Almacenamiento de objetos
- **🔐 Secrets Manager** - Gestión de secretos
- **🔑 KMS** - Encriptación
- **📊 CloudWatch** - Monitoreo y logs
- **🔍 X-Ray** - Trazabilidad distribuida
- **🛡️ WAF** - Protección web

### **Ambientes:**
- **dev** - Desarrollo y testing
- **staging** - Pre-producción
- **prod** - Producción

## 🚀 **Configuración Inicial**

### **Prerrequisitos AWS:**
```bash
# 1. Configurar AWS CLI
aws configure
# Access Key ID: [Tu Access Key]
# Secret Access Key: [Tu Secret Key]
# Default region: us-east-1
# Default output format: json

# 2. Verificar permisos
aws sts get-caller-identity

# 3. Verificar región
aws configure get region
```

### **Permisos IAM Requeridos:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "cloudformation:*",
        "lambda:*",
        "apigateway:*",
        "dynamodb:*",
        "s3:*",
        "iam:*",
        "logs:*",
        "secretsmanager:*",
        "kms:*",
        "ec2:*",
        "route53:*",
        "wafv2:*"
      ],
      "Resource": "*"
    }
  ]
}
```

### **Instalación de Herramientas:**
```bash
# Node.js y Serverless Framework
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
npm install -g serverless

# Python y dependencias
sudo apt-get install python3.11 python3-pip
pip install PyYAML boto3

# Verificar instalación
serverless --version
python3 --version
```

## 📋 **Proceso de Despliegue**

### **1. Validación Pre-Despliegue:**
```bash
# Ejecutar script de validación completa
python scripts/validate-dependencies.py

# Verificar sintaxis de todos los servicios
serverless print --stage dev
cd serverless/services/auth && serverless print --stage dev
cd serverless/services/payment && serverless print --stage dev
cd serverless/services/tenant && serverless print --stage dev
cd serverless/services/user && serverless print --stage dev
```

### **2. Despliegue Automatizado:**
```bash
# Despliegue completo con validaciones
./scripts/validate-and-deploy.sh dev

# Para staging
./scripts/validate-and-deploy.sh staging

# Para producción
./scripts/validate-and-deploy.sh prod
```

### **3. Despliegue Manual (Paso a Paso):**
```bash
# 1. Infraestructura principal (PRIMERO)
serverless deploy --stage dev --verbose

# 2. Servicio Auth (SEGUNDO - requerido por otros)
cd serverless/services/auth
serverless deploy --stage dev --verbose

# 3. Otros servicios (pueden ser en paralelo)
cd ../payment && serverless deploy --stage dev --verbose &
cd ../tenant && serverless deploy --stage dev --verbose &
cd ../user && serverless deploy --stage dev --verbose &
wait
```

### **4. Verificación Post-Despliegue:**
```bash
# Verificar exports de CloudFormation
aws cloudformation list-exports --region us-east-1 | grep agent-scl-dev

# Verificar funciones Lambda
aws lambda list-functions --region us-east-1 | grep agent-scl-dev

# Verificar API Gateway
aws apigateway get-rest-apis --region us-east-1

# Test de conectividad
curl -X GET https://api-platform-dev.agentscl.com/health
```

## 🔄 **CI/CD Pipeline**

### **GitHub Actions Workflow:**
```yaml
# .github/workflows/deploy.yml
name: Deploy Agent SCL Platform

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          pip install PyYAML boto3
          npm install -g serverless
          
      - name: Validate dependencies
        run: python scripts/validate-dependencies.py
        
  deploy-dev:
    needs: validate
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    environment: development
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Deploy to dev
        run: ./scripts/validate-and-deploy.sh dev
        
  deploy-staging:
    needs: validate
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
          aws-region: us-east-1
          
      - name: Deploy to staging
        run: ./scripts/validate-and-deploy.sh staging
        
  deploy-prod:
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v3
      
      - name: Manual approval required
        uses: trstringer/manual-approval@v1
        with:
          secret: ${{ github.TOKEN }}
          approvers: devops-team
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: us-east-1
          
      - name: Deploy to production
        run: ./scripts/validate-and-deploy.sh prod
```

### **GitLab CI/CD:**
```yaml
# .gitlab-ci.yml
stages:
  - validate
  - deploy-dev
  - deploy-staging
  - deploy-prod

variables:
  AWS_DEFAULT_REGION: us-east-1

validate:
  stage: validate
  image: node:18
  before_script:
    - apt-get update && apt-get install -y python3 python3-pip
    - pip3 install PyYAML boto3
    - npm install -g serverless
  script:
    - python3 scripts/validate-dependencies.py
  only:
    - merge_requests
    - main
    - develop

deploy-dev:
  stage: deploy-dev
  image: node:18
  before_script:
    - apt-get update && apt-get install -y python3 python3-pip
    - pip3 install PyYAML boto3
    - npm install -g serverless
  script:
    - ./scripts/validate-and-deploy.sh dev
  environment:
    name: development
    url: https://api-platform-dev.agentscl.com
  only:
    - develop

deploy-staging:
  stage: deploy-staging
  image: node:18
  before_script:
    - apt-get update && apt-get install -y python3 python3-pip
    - pip3 install PyYAML boto3
    - npm install -g serverless
  script:
    - ./scripts/validate-and-deploy.sh staging
  environment:
    name: staging
    url: https://api-platform-staging.agentscl.com
  only:
    - main

deploy-prod:
  stage: deploy-prod
  image: node:18
  when: manual
  before_script:
    - apt-get update && apt-get install -y python3 python3-pip
    - pip3 install PyYAML boto3
    - npm install -g serverless
  script:
    - ./scripts/validate-and-deploy.sh prod
  environment:
    name: production
    url: https://api-platform.agentscl.com
  only:
    - main
```

## 📊 **Monitoreo y Observabilidad**

### **CloudWatch Dashboards:**
```bash
# Crear dashboard personalizado
aws cloudwatch put-dashboard \
  --dashboard-name "agent-scl-dev-platform" \
  --dashboard-body file://monitoring/dashboard.json
```

### **Métricas Clave:**
- **Lambda Duration** - Tiempo de ejecución
- **Lambda Errors** - Errores de función
- **Lambda Throttles** - Limitaciones
- **API Gateway 4XX/5XX** - Errores de API
- **API Gateway Latency** - Latencia de respuesta
- **DynamoDB Throttles** - Limitaciones de DB
- **DynamoDB Errors** - Errores de DB

### **Alertas Críticas:**
```yaml
# CloudWatch Alarms
LambdaErrors:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: agent-scl-dev-lambda-errors
    MetricName: Errors
    Namespace: AWS/Lambda
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 2
    Threshold: 5
    ComparisonOperator: GreaterThanThreshold
    AlarmActions:
      - !Ref AlertsTopic

ApiGateway5xxErrors:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: agent-scl-dev-api-5xx-errors
    MetricName: 5XXError
    Namespace: AWS/ApiGateway
    Statistic: Sum
    Period: 300
    EvaluationPeriods: 1
    Threshold: 3
    ComparisonOperator: GreaterThanThreshold
```

### **Logs Centralizados:**
```bash
# Buscar errores en todos los servicios
aws logs filter-log-events \
  --log-group-name /aws/lambda/agent-scl-dev-auth-login \
  --filter-pattern "ERROR" \
  --start-time $(date -d '1 hour ago' +%s)000

# Exportar logs a S3 para análisis
aws logs create-export-task \
  --log-group-name /aws/lambda/agent-scl-dev-auth-login \
  --from $(date -d '1 day ago' +%s)000 \
  --to $(date +%s)000 \
  --destination agent-scl-logs-dev
```

## 🔐 **Seguridad y Compliance**

### **Gestión de Secretos:**
```bash
# Rotar secretos JWT
aws secretsmanager rotate-secret \
  --secret-id agent-scl/dev/jwt \
  --rotation-lambda-arn arn:aws:lambda:us-east-1:account:function:rotate-jwt

# Auditar acceso a secretos
aws logs filter-log-events \
  --log-group-name /aws/secretsmanager/agent-scl \
  --filter-pattern "GetSecretValue"
```

### **Encriptación:**
- **En tránsito**: TLS 1.2+ en API Gateway
- **En reposo**: KMS para DynamoDB, S3, Secrets Manager
- **Aplicación**: Hashing de contraseñas con bcrypt

### **Compliance:**
```bash
# Verificar configuración de seguridad
aws s3api get-bucket-encryption --bucket agent-scl-data-dev
aws dynamodb describe-table --table-name agent-scl-main-dev | grep SSEDescription
aws kms describe-key --key-id alias/agent-scl-dev-app
```

## 🚨 **Disaster Recovery**

### **Backups Automatizados:**
```yaml
# DynamoDB Point-in-Time Recovery
PointInTimeRecoverySpecification:
  PointInTimeRecoveryEnabled: true

# S3 Versioning y Lifecycle
VersioningConfiguration:
  Status: Enabled
LifecycleConfiguration:
  Rules:
    - Id: BackupRetention
      Status: Enabled
      Transitions:
        - TransitionInDays: 30
          StorageClass: GLACIER
```

### **Procedimiento de Recuperación:**
```bash
# 1. Restaurar DynamoDB desde backup
aws dynamodb restore-table-from-backup \
  --target-table-name agent-scl-main-dev-restored \
  --backup-arn arn:aws:dynamodb:us-east-1:account:table/agent-scl-main-dev/backup/backup-id

# 2. Restaurar S3 desde versión anterior
aws s3api restore-object \
  --bucket agent-scl-data-dev \
  --key important-file.json \
  --version-id version-id

# 3. Re-desplegar servicios si es necesario
./scripts/validate-and-deploy.sh dev
```

## 🔧 **Troubleshooting**

### **Problemas Comunes:**

1. **Error: "Stack does not exist"**
   ```bash
   # Verificar que el stack existe
   aws cloudformation describe-stacks --stack-name agent-scl-infrastructure-dev
   
   # Si no existe, desplegar infraestructura primero
   serverless deploy --stage dev
   ```

2. **Error: "Export does not exist"**
   ```bash
   # Verificar exports disponibles
   aws cloudformation list-exports | grep agent-scl-dev
   
   # Verificar dependencias
   python scripts/validate-dependencies.py
   ```

3. **Lambda timeout**
   ```bash
   # Aumentar timeout en serverless.yml
   timeout: 30  # segundos
   
   # O optimizar código para mejor performance
   ```

4. **DynamoDB throttling**
   ```bash
   # Verificar métricas de capacidad
   aws cloudwatch get-metric-statistics \
     --namespace AWS/DynamoDB \
     --metric-name ConsumedReadCapacityUnits \
     --dimensions Name=TableName,Value=agent-scl-main-dev
   
   # Considerar cambiar a On-Demand billing
   ```

### **Comandos de Diagnóstico:**
```bash
# Estado general del stack
aws cloudformation describe-stacks --stack-name agent-scl-infrastructure-dev

# Logs de CloudFormation
aws cloudformation describe-stack-events --stack-name agent-scl-infrastructure-dev

# Estado de funciones Lambda
aws lambda list-functions | grep agent-scl-dev

# Métricas de API Gateway
aws apigateway get-usage --usage-plan-id plan-id --key-id key-id
```

## 📈 **Optimización de Costos**

### **Estrategias:**
- **Lambda**: Optimizar memoria y timeout
- **DynamoDB**: Usar On-Demand para cargas variables
- **S3**: Lifecycle policies para archivos antiguos
- **CloudWatch**: Retención de logs apropiada
- **API Gateway**: Caching para endpoints frecuentes

### **Monitoreo de Costos:**
```bash
# Configurar alertas de billing
aws budgets create-budget \
  --account-id account-id \
  --budget file://budget.json \
  --notifications-with-subscribers file://notifications.json
```

## 🔄 **Mantenimiento**

### **Tareas Regulares:**
- **Diario**: Revisar alertas y métricas
- **Semanal**: Analizar logs de errores
- **Mensual**: Revisar costos y optimizaciones
- **Trimestral**: Actualizar dependencias y parches de seguridad

### **Actualizaciones:**
```bash
# Actualizar Serverless Framework
npm update -g serverless

# Actualizar dependencias Python
pip install --upgrade -r requirements.txt

# Actualizar plugins de Serverless
npm update
```
