# 🚀 Quick Start Guide - Agent SCL Platform

## 📋 Overview

This guide will help you get started with the Agent SCL Platform in under 10 minutes. You'll learn how to register, set up your account, and start using the platform's core features.

## 🎯 What You'll Accomplish

By the end of this guide, you will have:
- ✅ Created your tenant account
- ✅ Set up your user profile
- ✅ Configured a subscription plan
- ✅ Understood the platform's main features
- ✅ Made your first API call

## 📋 Prerequisites

Before you begin, ensure you have:
- A valid email address
- A credit card for subscription (free trial available)
- Basic understanding of REST APIs (for developers)

## 🔧 Step 1: Account Registration

### **1.1 Register Your Tenant**

Visit the registration endpoint or use our web interface:

**API Method:**
```bash
curl -X POST https://api-platform.agentscl.com/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_name": "Your Company Name",
    "user_name": "Your Full Name",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "phone": "+**********"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant_abc123",
    "user_id": "user_def456",
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  },
  "message": "Registration successful"
}
```

### **1.2 Save Your Credentials**

**Important**: Save these values securely:
- `tenant_id`: Your unique tenant identifier
- `access_token`: For API authentication
- `refresh_token`: For token renewal

## 🔐 Step 2: Authentication Setup

### **2.1 Test Your Authentication**

```bash
curl -X GET https://api-platform.agentscl.com/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "X-Tenant-Id: YOUR_TENANT_ID"
```

### **2.2 Set Up Environment Variables**

Create a `.env` file for your project:
```bash
# Agent SCL Platform Configuration
API_BASE_URL=https://api-platform.agentscl.com
TENANT_ID=your_tenant_id_here
ACCESS_TOKEN=your_access_token_here
```

## 💳 Step 3: Choose Your Subscription Plan

### **3.1 View Available Plans**

```bash
curl -X GET https://api-platform.agentscl.com/payment/plans \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### **3.2 Available Plans**

| Plan | Price | Users | Features |
|------|-------|-------|----------|
| **Basic** | $29.99/month | Up to 5 | Basic analytics, Email support |
| **Standard** | $79.99/month | Up to 25 | Advanced analytics, Priority support, API access |
| **Enterprise** | $199.99/month | Unlimited | Custom integrations, Dedicated support |

### **3.3 Subscribe to a Plan**

```bash
curl -X POST https://api-platform.agentscl.com/payment/subscription \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "plan_id": "standard",
    "payment_method_id": "pm_your_stripe_payment_method"
  }'
```

## 👥 Step 4: User Management

### **4.1 Update Your Profile**

```bash
curl -X PUT https://api-platform.agentscl.com/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Name",
    "phone": "+**********"
  }'
```

### **4.2 Invite Team Members (Standard+ Plans)**

```bash
curl -X POST https://api-platform.agentscl.com/user/bulk-invite \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "invitations": [
      {
        "email": "<EMAIL>",
        "name": "Team Member",
        "role": "USER"
      }
    ]
  }'
```

## 🏢 Step 5: Tenant Configuration

### **5.1 Configure Tenant Settings**

```bash
curl -X PUT https://api-platform.agentscl.com/tenant/settings \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "timezone": "America/New_York",
    "currency": "USD",
    "language": "en"
  }'
```

### **5.2 View Tenant Information**

```bash
curl -X GET https://api-platform.agentscl.com/tenant/info \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🔍 Step 6: Health Check & Monitoring

### **6.1 Check System Health**

```bash
curl -X GET https://api-platform.agentscl.com/health
```

### **6.2 Monitor Your Usage**

```bash
curl -X GET https://api-platform.agentscl.com/payment/subscription \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📚 Step 7: Next Steps

### **7.1 Explore Advanced Features**

Now that you're set up, explore these advanced features:

1. **API Integration**: [API Reference Guide](../api/API_REFERENCE.md)
2. **Webhooks**: [Webhook Configuration](../api/webhooks.md)
3. **Analytics**: [Analytics Dashboard](../user-guides/analytics.md)
4. **Security**: [Security Best Practices](../user-guides/security.md)

### **7.2 Development Resources**

- [Developer Documentation](../development/README.md)
- [SDK Libraries](../api/sdks.md)
- [Code Examples](../examples/)
- [Postman Collection](../api/postman-collection.json)

## 🆘 Getting Help

### **Support Channels**

- **Documentation**: [docs.agentscl.com](https://docs.agentscl.com)
- **Email Support**: <EMAIL>
- **Priority Support**: Available for Standard+ plans
- **Community Forum**: [community.agentscl.com](https://community.agentscl.com)

### **Common Issues**

#### **Authentication Errors**
```json
{
  "success": false,
  "message": "Invalid or expired token",
  "error_code": "AUTH_TOKEN_EXPIRED"
}
```
**Solution**: Refresh your token using the `/auth/refresh` endpoint.

#### **Rate Limiting**
```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "error_code": "RATE_LIMIT_EXCEEDED"
}
```
**Solution**: Wait for the rate limit to reset or upgrade your plan.

#### **Subscription Issues**
```json
{
  "success": false,
  "message": "Subscription required",
  "error_code": "SUBSCRIPTION_REQUIRED"
}
```
**Solution**: Subscribe to a plan or check your payment method.

## ✅ Checklist

Use this checklist to ensure you've completed all setup steps:

- [ ] Account registered successfully
- [ ] Access token obtained and tested
- [ ] Subscription plan selected and activated
- [ ] User profile updated
- [ ] Tenant settings configured
- [ ] Team members invited (if applicable)
- [ ] API integration tested
- [ ] Health check verified

## 🎉 Congratulations!

You've successfully set up your Agent SCL Platform account! You're now ready to start using the platform's powerful logistics and supply chain management features.

**Next recommended actions:**
1. Explore the [User Guide](./USER_GUIDE.md) for detailed feature explanations
2. Check out [API Examples](../examples/) for common use cases
3. Join our [Community Forum](https://community.agentscl.com) for tips and best practices

---

**Need help?** Contact our support <NAME_EMAIL> or visit our [Help Center](https://help.agentscl.com).
