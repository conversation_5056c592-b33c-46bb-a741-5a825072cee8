# 📖 User Guide - Agent SCL Platform

## 📋 Table of Contents

1. [Platform Overview](#platform-overview)
2. [Account Management](#account-management)
3. [User Management](#user-management)
4. [Subscription Management](#subscription-management)
5. [Security & Authentication](#security--authentication)
6. [API Usage](#api-usage)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

## 🌟 Platform Overview

### **What is Agent SCL Platform?**

Agent SCL Platform is a comprehensive multi-tenant SaaS solution designed for logistics and supply chain management. It provides:

- **Multi-tenant Architecture**: Secure isolation between organizations
- **User Management**: Role-based access control and team collaboration
- **Subscription Management**: Flexible billing and plan management
- **API-First Design**: Complete REST API for integrations
- **Enterprise Security**: JWT authentication, encryption, and audit trails

### **Key Features**

#### **🔐 Authentication & Security**
- JWT-based authentication with refresh tokens
- Centralized JWT authorizer for consistent security
- Role-based access control (RBAC)
- Multi-factor authentication support
- Audit logging and security monitoring

#### **👥 User Management**
- User profiles and preferences
- Team invitation and management
- Role assignment and permissions
- Bulk user operations

#### **🏢 Tenant Management**
- Organization settings and configuration
- Multi-tenant data isolation
- Custom branding and themes
- Usage analytics and reporting

#### **💳 Subscription Management**
- Multiple subscription plans
- Stripe integration for payments
- Usage tracking and billing
- Plan upgrades and downgrades

## 👤 Account Management

### **Creating Your Account**

1. **Registration Process**:
   - Visit the registration page or use the API
   - Provide company name, user details, and contact information
   - Verify your email address
   - Set up your initial password

2. **Account Verification**:
   - Check your email for verification link
   - Click the verification link to activate your account
   - Complete your profile setup

### **Profile Management**

#### **Updating Your Profile**
```bash
# Update user profile
curl -X PUT https://5p54pp2np8.execute-api.us-east-1.amazonaws.com/dev/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Name",
    "phone": "+**********",
    "preferences": {
      "timezone": "America/New_York",
      "language": "en",
      "notifications": {
        "email": true,
        "sms": false
      }
    }
  }'
```

#### **Changing Your Password**
```bash
# Change password
curl -X POST https://5p54pp2np8.execute-api.us-east-1.amazonaws.com/dev/user/change-password \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "current_password": "CurrentPassword123!",
    "new_password": "NewPassword123!",
    "confirm_password": "NewPassword123!"
  }'
```

### **Account Security**

#### **Two-Factor Authentication (2FA)**
1. Enable 2FA in your account settings
2. Scan the QR code with your authenticator app
3. Enter the verification code to confirm setup
4. Save your backup codes securely

#### **Session Management**
- Monitor active sessions in your account settings
- Revoke suspicious or old sessions
- Set session timeout preferences

## 👥 User Management

### **User Roles and Permissions**

#### **Available Roles**
- **MASTER**: Full administrative access to the tenant
- **ADMIN**: Administrative access with some restrictions
- **USER**: Standard user access
- **VIEWER**: Read-only access

#### **Permission Matrix**
| Action | MASTER | ADMIN | USER | VIEWER |
|--------|--------|-------|------|--------|
| Manage Users | ✅ | ✅ | ❌ | ❌ |
| Manage Billing | ✅ | ❌ | ❌ | ❌ |
| View Analytics | ✅ | ✅ | ✅ | ✅ |
| Manage Settings | ✅ | ✅ | ❌ | ❌ |
| API Access | ✅ | ✅ | ✅ | ❌ |

### **Inviting Team Members**

#### **Single User Invitation**
```bash
curl -X POST https://api-platform.agentscl.com/user/invite \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "New User",
    "role": "USER",
    "message": "Welcome to our team!"
  }'
```

#### **Bulk User Invitation**
```bash
curl -X POST https://api-platform.agentscl.com/user/bulk-invite \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "invitations": [
      {
        "email": "<EMAIL>",
        "name": "User One",
        "role": "USER"
      },
      {
        "email": "<EMAIL>",
        "name": "Admin User",
        "role": "ADMIN"
      }
    ]
  }'
```

### **Managing Existing Users**

#### **Updating User Roles**
```bash
curl -X PUT https://api-platform.agentscl.com/user/{user_id}/role \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "ADMIN"
  }'
```

#### **Deactivating Users**
```bash
curl -X POST https://api-platform.agentscl.com/user/{user_id}/deactivate \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "User left the company"
  }'
```

## 💳 Subscription Management

### **Available Plans**

#### **Basic Plan - $29.99/month**
- Up to 5 users
- Basic analytics and reporting
- Email support
- Standard API rate limits

#### **Standard Plan - $79.99/month**
- Up to 25 users
- Advanced analytics and dashboards
- Priority email support
- Higher API rate limits
- Webhook support

#### **Enterprise Plan - $199.99/month**
- Unlimited users
- Custom integrations
- Dedicated support manager
- Premium API rate limits
- Custom SLA

### **Managing Your Subscription**

#### **Viewing Current Subscription**
```bash
curl -X GET https://api-platform.agentscl.com/payment/subscription \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Upgrading Your Plan**
```bash
curl -X PUT https://api-platform.agentscl.com/payment/subscription \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "plan_id": "standard"
  }'
```

#### **Updating Payment Method**
```bash
curl -X PUT https://api-platform.agentscl.com/payment/payment-method \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "pm_new_payment_method"
  }'
```

### **Billing and Invoices**

#### **Viewing Billing History**
```bash
curl -X GET https://api-platform.agentscl.com/payment/invoices \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Downloading Invoices**
```bash
curl -X GET https://api-platform.agentscl.com/payment/invoice/{invoice_id}/download \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔐 Security & Authentication

### **Authentication Flow**

1. **Login**: Obtain access and refresh tokens
2. **API Calls**: Use access token in Authorization header
3. **Token Refresh**: Use refresh token when access token expires
4. **Logout**: Invalidate tokens when done

### **Token Management**

#### **Refreshing Tokens**
```bash
curl -X POST https://api-platform.agentscl.com/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "YOUR_REFRESH_TOKEN"
  }'
```

#### **Validating Tokens**
```bash
curl -X POST https://api-platform.agentscl.com/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **Security Best Practices**

1. **Token Security**:
   - Store tokens securely (never in localStorage for web apps)
   - Use HTTPS for all API calls
   - Implement token rotation
   - Set appropriate token expiration times

2. **Password Security**:
   - Use strong, unique passwords
   - Enable two-factor authentication
   - Regularly update passwords
   - Don't share credentials

3. **API Security**:
   - Validate all input data
   - Implement rate limiting
   - Monitor for suspicious activity
   - Use proper error handling

## 🔌 API Usage

### **Making API Calls**

#### **Basic Request Structure**
```bash
curl -X {METHOD} https://api-platform.agentscl.com/{endpoint} \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: YOUR_TENANT_ID" \
  -d '{request_body}'
```

#### **Handling Responses**
All API responses follow this structure:
```json
{
  "success": true|false,
  "data": {},
  "message": "Description",
  "timestamp": "ISO 8601 datetime",
  "request_id": "unique_request_id"
}
```

### **Rate Limiting**

#### **Understanding Rate Limits**
- Each endpoint has specific rate limits
- Limits are per user, per minute/hour
- Headers indicate current limit status

#### **Rate Limit Headers**
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

#### **Handling Rate Limits**
```python
import time
import requests

def make_api_call_with_retry(url, headers, data=None):
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 429:
        # Rate limited, wait and retry
        reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
        wait_time = reset_time - int(time.time())
        time.sleep(max(wait_time, 1))
        return make_api_call_with_retry(url, headers, data)
    
    return response
```

## 🔧 Troubleshooting

### **Common Issues**

#### **Authentication Errors**
- **Invalid Token**: Check token format and expiration
- **Expired Token**: Use refresh token to get new access token
- **Missing Headers**: Ensure Authorization and Content-Type headers are set

#### **Permission Errors**
- **Insufficient Permissions**: Check user role and permissions
- **Tenant Access**: Verify X-Tenant-Id header is correct
- **Resource Access**: Ensure user has access to specific resources

#### **Rate Limiting**
- **Too Many Requests**: Implement exponential backoff
- **Plan Limits**: Consider upgrading to higher plan
- **Burst Limits**: Spread requests over time

### **Error Codes Reference**

| Code | Description | Solution |
|------|-------------|----------|
| AUTH_TOKEN_EXPIRED | Access token has expired | Refresh token |
| AUTH_TOKEN_INVALID | Token format is invalid | Re-authenticate |
| INSUFFICIENT_PERMISSIONS | User lacks required permissions | Check user role |
| RATE_LIMIT_EXCEEDED | Too many requests | Wait and retry |
| SUBSCRIPTION_REQUIRED | Feature requires subscription | Upgrade plan |
| VALIDATION_ERROR | Request data is invalid | Check request format |

## 💡 Best Practices

### **API Integration**

1. **Error Handling**:
   - Always check response status
   - Implement proper error handling
   - Log errors for debugging
   - Provide user-friendly error messages

2. **Performance**:
   - Cache responses when appropriate
   - Use pagination for large datasets
   - Implement request batching
   - Monitor API performance

3. **Security**:
   - Validate all input data
   - Use HTTPS for all requests
   - Implement proper authentication
   - Monitor for security issues

### **User Management**

1. **Onboarding**:
   - Provide clear invitation emails
   - Include setup instructions
   - Offer training resources
   - Monitor user adoption

2. **Access Control**:
   - Follow principle of least privilege
   - Regularly review user permissions
   - Remove inactive users
   - Audit access logs

### **Subscription Management**

1. **Plan Selection**:
   - Start with appropriate plan size
   - Monitor usage regularly
   - Plan for growth
   - Consider annual billing for savings

2. **Cost Optimization**:
   - Remove inactive users
   - Monitor API usage
   - Optimize integration efficiency
   - Review plan features regularly

---

**Need additional help?** Contact our support <NAME_EMAIL> or visit our [Help Center](https://help.agentscl.com).
