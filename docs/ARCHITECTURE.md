# 🏗️ **Arquitectura del Sistema - Agent SCL Platform**

## 🎯 **Visión General**

Agent SCL Platform es una plataforma SaaS multi-tenant construida con arquitectura serverless en AWS, diseñada para gestión de cadena de suministro y logística. La plataforma utiliza microservicios desacoplados que se comunican a través de APIs REST y eventos.

## 🔧 **Principios Arquitectónicos**

### **1. Serverless-First**
- **Sin gestión de servidores**: AWS Lambda para compute
- **Escalabilidad automática**: Basada en demanda
- **Pago por uso**: Optimización de costos
- **Alta disponibilidad**: Multi-AZ por defecto
- **Shared Layer Optimizado**: Dependencias comunes centralizadas para reducir cold starts

### **2. Microservicios**
- **Separación de responsabilidades**: Cada servicio tiene un dominio específico
- **Despliegue independiente**: Servicios pueden desplegarse por separado
- **Tecnologías heterogéneas**: Flexibilidad en stack tecnológico
- **Tolerancia a fallos**: Aislamiento de errores

### **3. Event-Driven**
- **Comunicación asíncrona**: Eventos para operaciones no críticas
- **Desacoplamiento**: Servicios no dependen directamente entre sí
- **Escalabilidad**: Procesamiento paralelo de eventos
- **Auditabilidad**: Trazabilidad completa de eventos

### **4. Multi-Tenant**
- **Aislamiento de datos**: Separación lógica por tenant
- **Recursos compartidos**: Optimización de costos
- **Configuración por tenant**: Personalización flexible
- **Escalabilidad horizontal**: Soporte para miles de tenants

## 🏛️ **Arquitectura de Alto Nivel**

```
┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND APPLICATIONS                    │
├─────────────────────────────────────────────────────────────┤
│  Web App (React)  │  Mobile App  │  Admin Dashboard        │
└─────────────────────┬───────────────┬─────────────────────────┘
                      │               │
┌─────────────────────┴───────────────┴─────────────────────────┐
│                     API GATEWAY                             │
├─────────────────────────────────────────────────────────────┤
│  • Rate Limiting    • Authentication  • Request Validation  │
│  • CORS            • Caching          • Response Transform  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   MICROSERVICES                            │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ Auth Service│Payment Svc  │Tenant Svc   │ User Service    │
│             │             │             │                 │
│ • Login     │ • Stripe    │ • Orgs      │ • Profiles      │
│ • Register  │ • Billing   │ • Settings  │ • Preferences   │
│ • JWT       │ • Invoices  │ • Users     │ • Avatars       │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                 SHARED LAYER (OPTIMIZED)                   │
├─────────────────────────────────────────────────────────────┤
│ • Common Dependencies (boto3, requests, etc.)              │
│ • Shared Utilities & Helpers                               │
│ • Performance Optimization (99.21 kB)                      │
│ • Cold Start Reduction (~95% function size reduction)      │
└─────────────────────────────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   DATA LAYER                               │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│  DynamoDB   │     S3      │  Secrets    │   CloudWatch    │
│             │             │  Manager    │                 │
│ • Main Table│ • Files     │ • JWT Keys  │ • Logs          │
│ • Sessions  │ • Avatars   │ • DB Config │ • Metrics       │
│ • Rate Limit│ • Backups   │ • API Keys  │ • Traces        │
└─────────────┴─────────────┴─────────────┴─────────────────┘
```

## 🔄 **Flujo de Datos**

### **1. Autenticación:**
```
User → API Gateway → Auth Service → DynamoDB → JWT Token → User
```

### **2. Operación Autenticada:**
```
User → API Gateway → JWT Authorizer → Service → DynamoDB → Response
```

### **3. Procesamiento de Pagos:**
```
User → Payment Service → Stripe API → Webhook → Event Processing → DynamoDB
```

## 🗄️ **Modelo de Datos**

### **Single Table Design (DynamoDB)**

```
Table: agent-scl-main-{stage}

PK (Partition Key) | SK (Sort Key)     | GSI1PK        | GSI1SK        | Type
-------------------|-------------------|---------------|---------------|--------
TENANT#123         | TENANT#123        | -             | -             | Tenant
USER#456           | USER#456          | TENANT#123    | USER#456      | User
PAYMENT#789        | PAYMENT#789       | TENANT#123    | PAYMENT#789   | Payment
SESSION#abc        | SESSION#abc       | USER#456      | SESSION#abc   | Session
```

### **Patrones de Acceso:**
1. **Get Tenant**: `PK = TENANT#{tenant_id}`
2. **Get User**: `PK = USER#{user_id}`
3. **Get Users by Tenant**: `GSI1PK = TENANT#{tenant_id}, GSI1SK begins_with USER#`
4. **Get Payments by Tenant**: `GSI1PK = TENANT#{tenant_id}, GSI1SK begins_with PAYMENT#`
5. **Get User Sessions**: `GSI1PK = USER#{user_id}, GSI1SK begins_with SESSION#`

## 🔐 **Seguridad**

### **Autenticación y Autorización:**
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│ API Gateway │───▶│JWT Authorizer│
└─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
                           ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │  Service    │    │ Secrets Mgr │
                   └─────────────┘    └─────────────┘
```

### **Capas de Seguridad:**
1. **Transport**: TLS 1.2+ (HTTPS)
2. **API Gateway**: WAF, Rate Limiting, CORS
3. **Authentication**: JWT tokens con RS256
4. **Authorization**: Role-based access control
5. **Data**: Encryption at rest (KMS) y in transit
6. **Network**: VPC, Security Groups, NACLs

### **Gestión de Secretos:**
- **JWT Keys**: AWS Secrets Manager
- **Database Config**: AWS Secrets Manager  
- **Third-party APIs**: AWS Secrets Manager
- **Encryption**: AWS KMS con customer-managed keys

## 🚀 **Escalabilidad**

### **Horizontal Scaling:**
- **Lambda**: Auto-scaling basado en concurrencia
- **DynamoDB**: On-demand billing mode
- **API Gateway**: Manejo automático de tráfico
- **S3**: Escalabilidad ilimitada

### **Performance Optimization:**
- **Lambda**: Warm-up strategies, provisioned concurrency
- **DynamoDB**: Single table design, efficient queries
- **API Gateway**: Response caching, compression
- **CloudFront**: CDN para assets estáticos

### **Límites y Quotas:**
```yaml
Lambda:
  Concurrent Executions: 1000 (default)
  Memory: 128MB - 10GB
  Timeout: 15 minutes max
  
DynamoDB:
  Read/Write Capacity: On-demand (auto-scaling)
  Item Size: 400KB max
  Query Result: 1MB max
  
API Gateway:
  Requests per second: 10,000 (default)
  Payload size: 10MB max
  Timeout: 29 seconds max
```

## 🔄 **Patrones de Integración**

### **1. Synchronous (Request-Response):**
```
Client → API Gateway → Lambda → DynamoDB → Response
```
- **Uso**: Operaciones CRUD, consultas en tiempo real
- **Ventajas**: Respuesta inmediata, simple
- **Desventajas**: Acoplamiento, timeouts

### **2. Asynchronous (Event-Driven):**
```
Lambda → SQS → Lambda → DynamoDB
Lambda → SNS → Multiple Lambdas
```
- **Uso**: Procesamiento de pagos, notificaciones
- **Ventajas**: Desacoplamiento, tolerancia a fallos
- **Desventajas**: Eventual consistency, complejidad

### **3. Streaming:**
```
DynamoDB Streams → Lambda → Processing
```
- **Uso**: Auditoría, replicación, analytics
- **Ventajas**: Real-time, ordenado
- **Desventajas**: Complejidad, costos

## 🏗️ **Infraestructura como Código**

### **Serverless Framework:**
```yaml
# Estructura de archivos
serverless.yml                 # Infraestructura principal
serverless/
├── services/                  # Microservicios
│   ├── auth/serverless.yml
│   ├── payment/serverless.yml
│   └── ...
├── resources/                 # Recursos AWS
│   ├── dynamodb.yml
│   ├── s3.yml
│   └── ...
└── shared/                    # Configuraciones compartidas
    └── variables.yml
```

### **Deployment Pipeline:**
```
Code → GitHub → GitHub Actions → AWS CloudFormation → Live
```

1. **Validation**: Syntax, dependencies, security
2. **Testing**: Unit, integration, e2e
3. **Staging**: Deploy to staging environment
4. **Production**: Manual approval + deploy

## 📊 **Observabilidad**

### **Logging:**
```
Application Logs → CloudWatch Logs → Log Insights → Dashboards
```

### **Metrics:**
```
Lambda Metrics → CloudWatch Metrics → Alarms → SNS → Notifications
```

### **Tracing:**
```
X-Ray Traces → Service Map → Performance Analysis
```

### **Monitoring Stack:**
- **Logs**: CloudWatch Logs + Log Insights
- **Metrics**: CloudWatch Metrics + Custom Metrics
- **Tracing**: AWS X-Ray
- **Alerting**: CloudWatch Alarms + SNS
- **Dashboards**: CloudWatch Dashboards

## 🔄 **Disaster Recovery**

### **Backup Strategy:**
```
DynamoDB → Point-in-Time Recovery (35 days)
S3 → Versioning + Cross-Region Replication
Secrets → Automatic rotation + backup
Code → Git repositories + artifacts
```

### **Recovery Procedures:**
1. **RTO (Recovery Time Objective)**: < 4 hours
2. **RPO (Recovery Point Objective)**: < 1 hour
3. **Multi-Region**: Primary (us-east-1), DR (us-west-2)
4. **Automated Failover**: Route 53 health checks

## 🔮 **Evolución Futura**

### **Próximas Mejoras:**
1. **GraphQL API**: Complementar REST APIs
2. **Event Sourcing**: Para auditoría completa
3. **CQRS**: Separar read/write models
4. **Machine Learning**: Analytics predictivos
5. **Multi-Region**: Despliegue global

### **Consideraciones Técnicas:**
- **Microservices → Nano-services**: Granularidad más fina
- **Serverless → Edge Computing**: Latencia ultra-baja
- **SQL → NoSQL → NewSQL**: Evolución de datos

---

## 🚀 **Optimizaciones Implementadas**

### **Shared Layer Architecture**

La plataforma implementa un **Shared Layer optimizado** que centraliza dependencias comunes y reduce significativamente los cold starts:

#### **Características del Shared Layer:**
- **ARN**: `arn:aws:lambda:us-east-1:485950502364:layer:agent-scl-shared-dev-fixed:7`
- **Tamaño**: 99.21 kB (optimizado)
- **Dependencias**: boto3, requests, jwt, bcrypt, y utilidades comunes
- **Compatibilidad**: Python 3.11, arquitectura x86_64

#### **Beneficios de Performance:**
- **Reducción de tamaño**: 95-99% reducción en tamaño de funciones
- **Cold Start**: Reducción de 2-5 segundos en inicialización
- **Memoria**: Menor uso de memoria por función
- **Costos**: Reducción en costos de almacenamiento y transferencia

#### **Funciones Optimizadas:**
| **Servicio** | **Tamaño Antes** | **Tamaño Después** | **Reducción** |
|--------------|------------------|-------------------|---------------|
| User Service | 922 kB | 27 kB | 96.7% |
| Admin Service | 922 kB | 19 kB | 97.9% |
| Payment Service | 922 kB | 72 kB | 92.2% |
| Tenant Service | 922 kB | 46 kB | 95.0% |
| Events Service | 922 kB | 14 kB | 98.5% |
| Security Service | 922 kB | 8.3 kB | 99.1% |

### **JWT Authorizer Centralizado**

#### **Implementación:**
- **Función**: `agent-scl-auth-dev-jwtAuthorizer`
- **Tipo**: Request Authorizer
- **Integración**: 175+ referencias en todos los servicios
- **Caching**: Optimizado para reutilización de tokens

#### **Beneficios:**
- **Consistencia**: Autorización unificada en toda la plataforma
- **Performance**: Caching de validaciones JWT
- **Mantenimiento**: Lógica centralizada de autorización
- **Seguridad**: Validación consistente de tokens

### **Estrategia de Deploy Coordinado**

Para implementar estas optimizaciones sin downtime, se desarrolló una estrategia de deploy coordinado:

1. **Fase 1**: Comentar dependencias en todos los servicios
2. **Fase 2**: Deploy servicios sin dependencias
3. **Fase 3**: Deploy shared layer actualizado
4. **Fase 4**: Restaurar referencias y re-deploy con optimizaciones

#### **Scripts de Automatización:**
- `scripts/disable-shared-layer-references.py`
- `scripts/disable-jwt-authorizer.py`
- `scripts/enable-shared-layer-references.py`
- `scripts/enable-jwt-authorizer.py`
- **REST → GraphQL → gRPC**: Evolución de APIs

## 📚 **Referencias**

- [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)
- [Serverless Application Lens](https://docs.aws.amazon.com/wellarchitected/latest/serverless-applications-lens/)
- [DynamoDB Best Practices](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/best-practices.html)
- [Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
