"""
Integration test for complete system flow validation.
Tests the entire user journey from registration to payment.
"""

import pytest
import uuid
from decimal import Decimal
from unittest.mock import patch, MagicMock
from moto import mock_aws

from models.user import User, UserRole, UserStatus
from models.tenant import Tenant, TenantStatus
from models.subscription import Subscription, SubscriptionStatus, BillingInterval
from models.plan import Plan, PlanType, PlanStatus
from shared.exceptions import ValidationException, AuthorizationException


@mock_aws
class TestCompleteSystemFlow:
    """Test complete system integration flows."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        # Test data
        self.tenant_id = str(uuid.uuid4())
        self.user_id = str(uuid.uuid4())
        self.plan_id = str(uuid.uuid4())
        self.subscription_id = str(uuid.uuid4())
        
        # Create test tenant
        self.tenant_data = {
            'tenant_id': self.tenant_id,
            'name': 'Test Company',
            'email': '<EMAIL>',
            'status': TenantStatus.ACTIVE,
            'settings': {
                'max_users': 10,
                'features': ['basic_features']
            }
        }
        
        # Create test user
        self.user_data = {
            'user_id': self.user_id,
            'tenant_id': self.tenant_id,
            'email': '<EMAIL>',
            'name': 'Test User',
            'role': UserRole.MEMBER,
            'status': UserStatus.ACTIVE,
            'email_verified': True
        }
        
        # Create test plan
        self.plan_data = {
            'plan_id': self.plan_id,
            'name': 'Basic Plan',
            'plan_type': PlanType.BASIC,
            'monthly_price': Decimal('29.99'),
            'yearly_price': Decimal('299.99'),
            'status': PlanStatus.ACTIVE,
            'features': {
                'max_users': 10,
                'max_storage_gb': 100,
                'api_calls': 1000
            }
        }
        
        # Create test subscription
        self.subscription_data = {
            'subscription_id': self.subscription_id,
            'tenant_id': self.tenant_id,
            'plan_id': self.plan_id,
            'status': SubscriptionStatus.ACTIVE,
            'billing_interval': BillingInterval.MONTHLY,
            'amount': Decimal('29.99'),
            'stripe_subscription_id': 'sub_test_123'
        }
    
    def test_complete_user_registration_flow(self):
        """Test complete user registration and tenant creation flow."""
        # 1. Create tenant
        tenant = Tenant(**self.tenant_data)
        assert tenant.tenant_id == self.tenant_id
        assert tenant.name == 'Test Company'
        assert tenant.status == TenantStatus.ACTIVE
        
        # 2. Create user in tenant
        user = User(**self.user_data)
        assert user.user_id == self.user_id
        assert user.tenant_id == self.tenant_id
        assert user.email == '<EMAIL>'
        assert user.status == UserStatus.ACTIVE
        
        # 3. Validate user can access tenant
        assert user.is_member()
        assert user.can_login()
        
        # 4. Validate tenant-user relationship
        assert user.tenant_id == tenant.tenant_id
    
    def test_complete_payment_flow(self):
        """Test complete payment subscription flow."""
        # 1. Create plan
        plan = Plan(**self.plan_data)
        assert plan.plan_id == self.plan_id
        assert plan.name == 'Basic Plan'
        assert plan.monthly_price == Decimal('29.99')
        assert plan.is_active()
        
        # 2. Create subscription
        subscription = Subscription(**self.subscription_data)
        assert subscription.subscription_id == self.subscription_id
        assert subscription.tenant_id == self.tenant_id
        assert subscription.plan_id == self.plan_id
        assert subscription.status == SubscriptionStatus.ACTIVE
        
        # 3. Validate subscription-plan relationship
        assert subscription.plan_id == plan.plan_id
        assert subscription.amount == plan.monthly_price
        
        # 4. Test subscription operations
        assert subscription.is_active()
        assert subscription.can_access_features()
        
        # 5. Test plan price calculation
        yearly_discount = plan.calculate_yearly_discount()
        assert yearly_discount is not None
        assert yearly_discount > 0
    
    def test_multi_tenant_isolation(self):
        """Test multi-tenant data isolation."""
        # Create second tenant
        tenant2_id = str(uuid.uuid4())
        tenant2_data = self.tenant_data.copy()
        tenant2_data['tenant_id'] = tenant2_id
        tenant2_data['name'] = 'Another Company'
        tenant2_data['email'] = '<EMAIL>'
        
        tenant1 = Tenant(**self.tenant_data)
        tenant2 = Tenant(**tenant2_data)
        
        # Create users in different tenants
        user1 = User(**self.user_data)
        
        user2_data = self.user_data.copy()
        user2_data['user_id'] = str(uuid.uuid4())
        user2_data['tenant_id'] = tenant2_id
        user2_data['email'] = '<EMAIL>'
        user2 = User(**user2_data)
        
        # Validate isolation
        assert user1.tenant_id != user2.tenant_id
        assert user1.tenant_id == tenant1.tenant_id
        assert user2.tenant_id == tenant2.tenant_id
        
        # Validate users can't access other tenants
        assert user1.tenant_id != tenant2.tenant_id
        assert user2.tenant_id != tenant1.tenant_id
    
    def test_subscription_lifecycle(self):
        """Test complete subscription lifecycle."""
        subscription = Subscription(**self.subscription_data)

        # 1. Active subscription
        assert subscription.is_active()
        assert subscription.status == SubscriptionStatus.ACTIVE

        # 2. Cancel subscription (with mock)
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            subscription.cancel()
            assert subscription.status == SubscriptionStatus.CANCELLED
            assert hasattr(subscription, 'cancelled_at')
            assert not subscription.is_active()

        # 3. Reactivate subscription (with mock)
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            subscription.reactivate()
            assert subscription.status == SubscriptionStatus.ACTIVE
            assert hasattr(subscription, 'reactivated_at')
            assert subscription.is_active()

        # 4. Suspend subscription (with mock)
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            subscription.suspend()
            assert subscription.status == SubscriptionStatus.SUSPENDED
            assert hasattr(subscription, 'suspended_at')
            assert not subscription.is_active()
    
    def test_plan_management(self):
        """Test plan management operations."""
        plan = Plan(**self.plan_data)
        
        # 1. Active plan
        assert plan.is_active()
        assert plan.status == PlanStatus.ACTIVE
        
        # 2. Get price for different intervals
        monthly_price = plan.get_price_for_interval(BillingInterval.MONTHLY)
        yearly_price = plan.get_price_for_interval(BillingInterval.YEARLY)
        
        assert monthly_price == Decimal('29.99')
        assert yearly_price == Decimal('299.99')
        
        # 3. Update features
        new_features = {
            'max_users': 20,
            'max_storage_gb': 200,
            'api_calls': 2000,
            'new_feature': True
        }
        
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            plan.update_features(new_features)
            
            assert plan.features['max_users'] == 20
            assert plan.features['new_feature'] is True
        
        # 4. Deactivate plan
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            plan.deactivate()
            
            assert plan.status == PlanStatus.INACTIVE
            assert not plan.is_active()
    
    def test_user_permissions_and_roles(self):
        """Test user permissions and role management."""
        # Master user
        master_data = self.user_data.copy()
        master_data['role'] = UserRole.MASTER
        master_user = User(**master_data)
        
        # Member user
        member_user = User(**self.user_data)
        
        # Test master permissions
        assert master_user.is_master()
        assert master_user.can_manage_users()
        assert master_user.can_manage_billing()
        
        # Test member permissions
        assert member_user.is_member()
        assert not member_user.can_manage_users()
        assert not member_user.can_manage_billing()
        
        # Test common permissions
        assert master_user.can_login()
        assert member_user.can_login()
    
    def test_validation_and_error_handling(self):
        """Test validation and error handling across modules."""
        # Test invalid plan data (negative price)
        invalid_plan_data = self.plan_data.copy()
        invalid_plan_data['monthly_price'] = Decimal('-10.00')

        with pytest.raises(ValidationException):
            Plan(**invalid_plan_data)

        # Test invalid plan type
        invalid_plan_data2 = self.plan_data.copy()
        invalid_plan_data2['plan_type'] = 'INVALID_TYPE'

        with pytest.raises((ValidationException, ValueError)):
            Plan(**invalid_plan_data2)

        # Test that valid data works
        valid_plan = Plan(**self.plan_data)
        assert valid_plan.plan_id == self.plan_id

        valid_subscription = Subscription(**self.subscription_data)
        assert valid_subscription.subscription_id == self.subscription_id
