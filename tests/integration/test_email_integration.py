"""
Integration tests for Email Service with AWS SES.
Tests end-to-end email functionality including SES templates and configuration.
"""

import pytest
import json
import boto3
import asyncio
from unittest.mock import patch, MagicMock
from moto import mock_aws

from services.email_service import EmailService
from shared.exceptions import EmailException

# Integration tests use asyncio.run() for async functions

def run_async(coro):
    """Helper function to run async coroutines in sync tests."""
    return asyncio.run(coro)


@pytest.mark.integration
class TestEmailSESIntegration:
    """Integration tests for EmailService with SES."""
    
    @pytest.fixture(autouse=True)
    def setup(self, aws_credentials, mock_environment):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        self.test_domain = "agentscl.com"
        self.project_name = "agent-scl"
        self.environment = "test"
        
    @mock_aws
    def test_ses_client_initialization(self):
        """Test SES client initializes correctly."""
        service = EmailService()
        
        # Verify SES client is created
        assert service.ses_client is not None
        assert service.from_email == "<EMAIL>"
        assert service.reply_to_email == "<EMAIL>"
        
    @mock_aws
    def test_ses_template_name_generation(self):
        """Test SES template name generation."""
        service = EmailService()
        
        # Test template name generation
        verification_template = service._get_ses_template_name('verification')
        assert verification_template == "agent-scl-test-email-verification"
        
        welcome_template = service._get_ses_template_name('welcome')
        assert welcome_template == "agent-scl-test-welcome"
        
        password_reset_template = service._get_ses_template_name('password_reset')
        assert password_reset_template == "agent-scl-test-password-reset"
        
        invitation_template = service._get_ses_template_name('invitation')
        assert invitation_template == "agent-scl-test-invitation"
        
    @mock_aws
    def test_configuration_set_name_generation(self):
        """Test configuration set name generation."""
        service = EmailService()
        
        config_set = service._get_configuration_set_name()
        assert config_set == "agent-scl-test-config-set"
        
    @mock_aws
    def test_send_templated_email_integration(self):
        """Test sending templated email with SES integration."""
        # Setup SES mock
        ses_client = boto3.client('ses', region_name='us-east-1')
        
        # Verify email addresses for testing
        ses_client.verify_email_identity(EmailAddress=self.test_email)
        ses_client.verify_email_identity(EmailAddress="<EMAIL>")
        
        # Create SES template for testing
        template_name = "agent-scl-test-email-verification"
        ses_client.create_template(
            Template={
                'TemplateName': template_name,
                'SubjectPart': 'Verify your email for {{company_name}}',
                'HtmlPart': '<h1>Hi {{user_name}}</h1><p>Code: {{verification_code}}</p>',
                'TextPart': 'Hi {{user_name}}, Code: {{verification_code}}'
            }
        )
        
        # Test email sending
        service = EmailService()

        async def run_test():
            return await service.send_email(
                to_email=self.test_email,
                template='verification',
                template_data={
                    'user_name': 'Test User',
                    'verification_code': '123456',
                    'company_name': 'Agent SCL'
                }
            )

        result = asyncio.run(run_test())
        
        # Verify result
        assert result['success'] is True
        assert 'message_id' in result
        
    @mock_aws
    def test_email_validation_integration(self):
        """Test email validation in integration context."""
        service = EmailService()
        
        # Test invalid email
        with pytest.raises(EmailException) as exc_info:
            run_async(service.send_email(
                to_email="invalid-email",
                template='verification',
                template_data={
                    'user_name': 'Test User',
                    'verification_code': '123456',
                    'company_name': 'Agent SCL'
                }
            ))
        
        assert "Invalid email address" in str(exc_info.value)
        
    @mock_aws
    async def test_template_data_validation_integration(self):
        """Test template data validation in integration context."""
        service = EmailService()
        
        # Test missing required data
        with pytest.raises(EmailException) as exc_info:
            await service.send_email(
                to_email=self.test_email,
                template='verification',
                template_data={
                    'user_name': 'Test User'
                    # Missing verification_code and company_name
                }
            )
        
        assert "Missing required template data" in str(exc_info.value)
        
    @mock_aws
    async def test_all_template_types_integration(self):
        """Test all template types in integration context."""
        # Setup SES mock
        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress=self.test_email)
        ses_client.verify_email_identity(EmailAddress="<EMAIL>")
        
        # Create all templates
        templates = [
            ('agent-scl-test-email-verification', 'verification'),
            ('agent-scl-test-welcome', 'welcome'),
            ('agent-scl-test-password-reset', 'password_reset'),
            ('agent-scl-test-invitation', 'invitation')
        ]
        
        for template_name, template_type in templates:
            ses_client.create_template(
                Template={
                    'TemplateName': template_name,
                    'SubjectPart': f'Test {template_type} for {{{{company_name}}}}',
                    'HtmlPart': f'<h1>Test {template_type}</h1>',
                    'TextPart': f'Test {template_type}'
                }
            )
        
        service = EmailService()
        
        # Test verification email
        result = await service.send_email(
            to_email=self.test_email,
            template='verification',
            template_data={
                'user_name': 'Test User',
                'verification_code': '123456',
                'company_name': 'Agent SCL'
            }
        )
        assert result['success'] is True
        
        # Test welcome email
        result = await service.send_email(
            to_email=self.test_email,
            template='welcome',
            template_data={
                'user_name': 'Test User',
                'company_name': 'Agent SCL',
                'login_url': 'https://app.agentscl.com/login'
            }
        )
        assert result['success'] is True
        
        # Test password reset email
        result = await service.send_email(
            to_email=self.test_email,
            template='password_reset',
            template_data={
                'user_name': 'Test User',
                'reset_code': 'reset123',
                'company_name': 'Agent SCL'
            }
        )
        assert result['success'] is True
        
        # Test invitation email
        result = await service.send_email(
            to_email=self.test_email,
            template='invitation',
            template_data={
                'invitee_name': 'New User',
                'inviter_name': 'Admin User',
                'company_name': 'Agent SCL',
                'invitation_url': 'https://app.agentscl.com/accept/xyz'
            }
        )
        assert result['success'] is True


@pytest.mark.integration
class TestEmailServiceConvenienceMethods:
    """Integration tests for EmailService convenience methods."""
    
    @pytest.fixture(autouse=True)
    def setup(self, aws_credentials, mock_environment):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        
    @mock_aws
    async def test_send_verification_email_integration(self):
        """Test send_verification_email convenience method integration."""
        # Setup SES mock
        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress=self.test_email)
        ses_client.verify_email_identity(EmailAddress="<EMAIL>")
        
        # Create template
        ses_client.create_template(
            Template={
                'TemplateName': 'agent-scl-test-email-verification',
                'SubjectPart': 'Verify your email for {{company_name}}',
                'HtmlPart': '<h1>Hi {{user_name}}</h1><p>Code: {{verification_code}}</p>',
                'TextPart': 'Hi {{user_name}}, Code: {{verification_code}}'
            }
        )
        
        service = EmailService()
        
        # Test new parameter format
        result = await service.send_verification_email(
            to_email=self.test_email,
            user_name='Test User',
            verification_code='123456',
            company_name='Agent SCL'
        )
        
        assert result['success'] is True
        assert 'message_id' in result
        
        # Test legacy parameter format
        result = await service.send_verification_email(
            email=self.test_email,
            name='Test User',
            token='123456',
            tenant_name='Agent SCL'
        )
        
        assert result['success'] is True
        assert 'message_id' in result


@pytest.mark.integration
class TestEmailServiceErrorHandling:
    """Integration tests for EmailService error handling."""
    
    @pytest.fixture(autouse=True)
    def setup(self, aws_credentials, mock_environment):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        
    @mock_aws
    async def test_template_not_found_error(self):
        """Test handling when SES template doesn't exist."""
        service = EmailService()
        
        # Try to send email with non-existent template
        with pytest.raises(EmailException) as exc_info:
            await service.send_email(
                to_email=self.test_email,
                template='verification',
                template_data={
                    'user_name': 'Test User',
                    'verification_code': '123456',
                    'company_name': 'Agent SCL'
                }
            )
        
        assert "Failed to send email" in str(exc_info.value)
        
    @mock_aws
    async def test_unverified_email_error(self):
        """Test handling when email address is not verified in SES."""
        # Setup SES without verifying the email
        ses_client = boto3.client('ses', region_name='us-east-1')
        
        # Create template but don't verify email
        ses_client.create_template(
            Template={
                'TemplateName': 'agent-scl-test-email-verification',
                'SubjectPart': 'Test',
                'HtmlPart': '<h1>Test</h1>',
                'TextPart': 'Test'
            }
        )
        
        service = EmailService()
        
        # Try to send email to unverified address
        with pytest.raises(EmailException) as exc_info:
            await service.send_email(
                to_email="<EMAIL>",
                template='verification',
                template_data={
                    'user_name': 'Test User',
                    'verification_code': '123456',
                    'company_name': 'Agent SCL'
                }
            )
        
        assert "Failed to send email" in str(exc_info.value)
