#!/usr/bin/env python3
# tests/integration/test_registration_flow.py
# Integration tests for complete registration flow

"""
Integration tests for the complete registration flow.
Tests the end-to-end process from registration initiation to tenant setup.
"""

import pytest
import json
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Import handlers for testing
from services.orchestrator.src.handlers.complete_registration import handler as complete_registration_handler
from services.orchestrator.src.handlers.verify_registration import handler as verify_registration_handler
from services.orchestrator.src.handlers.process_payment import handler as process_payment_handler
from services.orchestrator.src.handlers.get_status import handler as get_status_handler


@pytest.mark.integration
class TestRegistrationFlow:
    """Test complete registration flow integration."""
    
    def test_complete_registration_flow_success(self, dynamodb_table, s3_bucket, 
                                              mock_stripe, mock_ses, api_event, 
                                              lambda_context, test_helpers):
        """Test successful complete registration flow."""
        
        # Step 1: Complete registration
        registration_data = {
            'email': '<EMAIL>',
            'company_name': 'Test Company Inc.',
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'plan_id': 'plan_pro',
            'password': 'SecurePassword123!',
            'terms_accepted': True
        }
        
        event = api_event(
            method='POST',
            path='/registration/complete',
            body=registration_data
        )
        
        response = complete_registration_handler(event, lambda_context)
        
        # Verify registration creation
        body = test_helpers.assert_response_success(response, 201)
        registration_id = body['data']['registration_id']
        tenant_id = body['data']['tenant_id']
        user_id = body['data']['user_id']
        
        assert registration_id
        assert tenant_id
        assert user_id
        assert body['data']['state'] == 'EMAIL_SENT'
        
        # Step 2: Verify email
        verification_data = {
            'registration_id': registration_id,
            'verification_code': '123456'  # Mock code
        }
        
        # Mock verification code validation
        with patch('services.orchestrator.src.services.registration_service.RegistrationService._validate_verification_code', return_value=True):
            event = api_event(
                method='POST',
                path='/registration/verify',
                body=verification_data
            )
            
            response = verify_registration_handler(event, lambda_context)
            
            # Verify email verification
            body = test_helpers.assert_response_success(response)
            assert body['data']['state'] == 'EMAIL_VERIFIED'
        
        # Step 3: Process payment
        payment_data = {
            'registration_id': registration_id,
            'payment_method_id': 'pm_test_123',
            'billing_address': {
                'line1': '123 Test St',
                'city': 'Test City',
                'state': 'TS',
                'postal_code': '12345',
                'country': 'US'
            }
        }
        
        event = api_event(
            method='POST',
            path='/registration/payment',
            body=payment_data
        )
        
        response = process_payment_handler(event, lambda_context)
        
        # Verify payment processing
        body = test_helpers.assert_response_success(response)
        assert body['data']['state'] == 'PAYMENT_COMPLETED'
        assert body['data']['subscription_id']
        
        # Step 4: Check final status
        event = api_event(
            method='GET',
            path=f'/registration/status/{registration_id}'
        )
        
        response = get_status_handler(event, lambda_context)
        
        # Verify final status
        body = test_helpers.assert_response_success(response)
        assert body['data']['state'] == 'COMPLETED'
        assert body['data']['tenant_setup_status'] == 'IN_PROGRESS'
    
    def test_registration_flow_with_invalid_email(self, api_event, lambda_context, test_helpers):
        """Test registration flow with invalid email."""
        
        registration_data = {
            'email': 'invalid-email',
            'company_name': 'Test Company Inc.',
            'first_name': 'John',
            'last_name': 'Doe',
            'plan_id': 'plan_pro',
            'password': 'SecurePassword123!',
            'terms_accepted': True
        }
        
        event = api_event(
            method='POST',
            path='/registration/complete',
            body=registration_data
        )
        
        response = complete_registration_handler(event, lambda_context)
        
        # Verify validation error
        test_helpers.assert_response_error(response, 422)
    
    def test_registration_flow_with_duplicate_email(self, dynamodb_table, api_event, 
                                                  lambda_context, test_helpers):
        """Test registration flow with duplicate email."""
        
        # Create existing tenant with same email
        test_helpers.create_test_item(
            dynamodb_table,
            pk='TENANT#tenant_existing_123',
            sk='PROFILE',
            entity_type='tenant',
            email='<EMAIL>',
            status='active'
        )
        
        registration_data = {
            'email': '<EMAIL>',
            'company_name': 'Test Company Inc.',
            'first_name': 'John',
            'last_name': 'Doe',
            'plan_id': 'plan_pro',
            'password': 'SecurePassword123!',
            'terms_accepted': True
        }
        
        event = api_event(
            method='POST',
            path='/registration/complete',
            body=registration_data
        )
        
        response = complete_registration_handler(event, lambda_context)
        
        # Verify conflict error
        test_helpers.assert_response_error(response, 409)
    
    def test_registration_verification_with_invalid_code(self, dynamodb_table, api_event, 
                                                       lambda_context, test_helpers):
        """Test registration verification with invalid code."""
        
        # Create registration record
        registration_id = test_helpers.generate_test_id('reg')
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'REGISTRATION#{registration_id}',
            sk='DETAILS',
            entity_type='registration',
            registration_id=registration_id,
            state='EMAIL_SENT',
            email='<EMAIL>',
            verification_code='123456',
            verification_attempts=0
        )
        
        verification_data = {
            'registration_id': registration_id,
            'verification_code': 'wrong_code'
        }
        
        event = api_event(
            method='POST',
            path='/registration/verify',
            body=verification_data
        )
        
        response = verify_registration_handler(event, lambda_context)
        
        # Verify validation error
        test_helpers.assert_response_error(response, 422)
    
    def test_registration_payment_with_invalid_payment_method(self, dynamodb_table, 
                                                            mock_stripe, api_event, 
                                                            lambda_context, test_helpers):
        """Test registration payment with invalid payment method."""
        
        # Create verified registration record
        registration_id = test_helpers.generate_test_id('reg')
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'REGISTRATION#{registration_id}',
            sk='DETAILS',
            entity_type='registration',
            registration_id=registration_id,
            state='EMAIL_VERIFIED',
            email='<EMAIL>',
            plan_id='plan_pro'
        )
        
        # Mock Stripe payment failure
        import stripe
        mock_stripe['subscription'].create.side_effect = stripe.error.CardError(
            'Your card was declined.',
            'card_declined',
            'card_declined'
        )
        
        payment_data = {
            'registration_id': registration_id,
            'payment_method_id': 'pm_invalid_123',
            'billing_address': {
                'line1': '123 Test St',
                'city': 'Test City',
                'state': 'TS',
                'postal_code': '12345',
                'country': 'US'
            }
        }
        
        event = api_event(
            method='POST',
            path='/registration/payment',
            body=payment_data
        )
        
        response = process_payment_handler(event, lambda_context)
        
        # Verify payment error
        test_helpers.assert_response_error(response, 422)
    
    def test_registration_status_not_found(self, api_event, lambda_context, test_helpers):
        """Test registration status for non-existent registration."""
        
        event = api_event(
            method='GET',
            path='/registration/status/non_existent_reg'
        )
        
        response = get_status_handler(event, lambda_context)
        
        # Verify not found error
        test_helpers.assert_response_error(response, 404)
    
    @pytest.mark.slow
    def test_registration_flow_timeout_handling(self, dynamodb_table, api_event, 
                                               lambda_context, test_helpers):
        """Test registration flow timeout handling."""
        
        # Create expired registration record
        registration_id = test_helpers.generate_test_id('reg')
        expired_time = datetime.utcnow() - timedelta(hours=25)  # Expired
        
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'REGISTRATION#{registration_id}',
            sk='DETAILS',
            entity_type='registration',
            registration_id=registration_id,
            state='EMAIL_SENT',
            email='<EMAIL>',
            created_at=expired_time.isoformat(),
            expires_at=expired_time.isoformat()
        )
        
        verification_data = {
            'registration_id': registration_id,
            'verification_code': '123456'
        }
        
        event = api_event(
            method='POST',
            path='/registration/verify',
            body=verification_data
        )
        
        response = verify_registration_handler(event, lambda_context)
        
        # Verify timeout error
        test_helpers.assert_response_error(response, 410)  # Gone
    
    def test_registration_flow_concurrent_requests(self, dynamodb_table, mock_stripe, 
                                                  api_event, lambda_context, test_helpers):
        """Test registration flow with concurrent requests."""
        
        # This test would simulate concurrent registration attempts
        # with the same email to test race condition handling
        
        registration_data = {
            'email': '<EMAIL>',
            'company_name': 'Concurrent Test Company',
            'first_name': 'Jane',
            'last_name': 'Doe',
            'plan_id': 'plan_starter',
            'password': 'SecurePassword123!',
            'terms_accepted': True
        }
        
        event1 = api_event(
            method='POST',
            path='/registration/complete',
            body=registration_data
        )
        
        event2 = api_event(
            method='POST',
            path='/registration/complete',
            body=registration_data
        )
        
        # Execute both requests
        response1 = complete_registration_handler(event1, lambda_context)
        response2 = complete_registration_handler(event2, lambda_context)
        
        # One should succeed, one should fail with conflict
        responses = [response1, response2]
        success_count = sum(1 for r in responses if r['statusCode'] == 201)
        conflict_count = sum(1 for r in responses if r['statusCode'] == 409)
        
        assert success_count == 1
        assert conflict_count == 1
