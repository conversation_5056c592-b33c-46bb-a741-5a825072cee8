# tests/integration/test_auth_flow.py
# Implementado según "Development Standards" y "Testing Guidelines"

"""
Integration tests for authentication flow.
"""

import pytest
import json
from unittest.mock import patch, MagicMock

from handlers import register, login, refresh, verify_email
from models.tenant import Tenant
from models.user import User


class TestAuthenticationFlow:
    """Integration tests for complete authentication flow."""
    
    def test_complete_registration_flow(self, dynamodb_mock, ses_mock, mock_environment):
        """Test complete user registration flow."""
        # Mock email service
        with patch('src.auth.services.email_service.EmailService.send_verification_email') as mock_email:
            mock_email.return_value = True
            
            # Prepare registration request
            event = {
                "httpMethod": "POST",
                "path": "/auth/register",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "tenant_name": "Test Company",
                    "user_name": "<PERSON> Do<PERSON>",
                    "email": "<EMAIL>",
                    "password": "SecurePassword123!",
                    "phone": "+1234567890"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
            
            context = MagicMock()
            
            # Execute registration
            response = register.handler(event, context)
            
            # Verify response
            assert response["statusCode"] == 201
            
            body = json.loads(response["body"])
            assert body["success"] is True
            assert "tenant" in body["data"]
            assert "user" in body["data"]
            assert body["data"]["tenant"]["name"] == "Test Company"
            assert body["data"]["user"]["email"] == "<EMAIL>"
            assert body["data"]["user"]["role"] == "MASTER"
            assert body["data"]["user"]["email_verified"] is False
            
            # Verify email was sent
            mock_email.assert_called_once()
    
    def test_registration_with_weak_password(self, dynamodb_mock, mock_environment):
        """Test registration with weak password."""
        event = {
            "httpMethod": "POST",
            "path": "/auth/register",
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({
                "tenant_name": "Test Company",
                "user_name": "John Doe",
                "email": "<EMAIL>",
                "password": "weak",  # Weak password
                "phone": "+1234567890"
            }),
            "requestContext": {
                "requestId": "test-request-123",
                "identity": {"sourceIp": "127.0.0.1"}
            }
        }
        
        context = MagicMock()
        
        # Execute registration
        response = register.handler(event, context)
        
        # Verify response
        assert response["statusCode"] == 422
        
        body = json.loads(response["body"])
        assert body["success"] is False
        assert body["error_code"] == "WEAK_PASSWORD"
        assert "validation_errors" in body
    
    def test_registration_duplicate_email(self, dynamodb_mock, test_helpers, mock_environment):
        """Test registration with duplicate email."""
        # Create existing tenant and user
        test_helpers.create_test_tenant(dynamodb_mock)
        test_helpers.create_test_user(dynamodb_mock)
        
        with patch('src.auth.models.user.User.get_by_email') as mock_get_user:
            # Mock existing user
            existing_user = User(
                user_id="existing-user",
                tenant_id="existing-tenant",
                email="<EMAIL>",
                name="Existing User"
            )
            mock_get_user.return_value = existing_user
            
            event = {
                "httpMethod": "POST",
                "path": "/auth/register",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "tenant_name": "Test Company",
                    "user_name": "John Doe",
                    "email": "<EMAIL>",  # Duplicate email
                    "password": "SecurePassword123!",
                    "phone": "+1234567890"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
            
            context = MagicMock()
            
            # Execute registration
            response = register.handler(event, context)
            
            # Verify response
            assert response["statusCode"] == 409
            
            body = json.loads(response["body"])
            assert body["success"] is False
            assert body["error_code"] == "RESOURCE_CONFLICT"
    
    def test_login_flow_success(self, dynamodb_mock, test_helpers, mock_environment):
        """Test successful login flow."""
        # Create test tenant and user
        tenant_data = test_helpers.create_test_tenant(dynamodb_mock)
        user_data = test_helpers.create_test_user(dynamodb_mock)
        
        # Add required fields for authentication
        user_data.update({
            "password_hash": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.e",  # "password123"
            "email_verified": True,
            "login_attempts": 0
        })
        
        with patch('src.auth.models.user.User.get_by_email') as mock_get_user, \
             patch('src.auth.models.tenant.Tenant.get_by_id') as mock_get_tenant, \
             patch('src.auth.handlers.login._find_user_by_email') as mock_find_user:
            
            # Mock user and tenant retrieval
            user = User(**user_data)
            tenant = Tenant(**tenant_data)
            
            mock_find_user.return_value = user
            mock_get_tenant.return_value = tenant
            
            # Mock authentication methods
            with patch.object(user, 'authenticate') as mock_auth, \
                 patch.object(user, '_reset_login_attempts'), \
                 patch.object(user, '_update_last_login'):
                
                mock_auth.return_value = True
                
                event = {
                    "httpMethod": "POST",
                    "path": "/auth/login",
                    "headers": {"Content-Type": "application/json"},
                    "body": json.dumps({
                        "email": "<EMAIL>",
                        "password": "password123"
                    }),
                    "requestContext": {
                        "requestId": "test-request-123",
                        "identity": {"sourceIp": "127.0.0.1"}
                    }
                }
                
                context = MagicMock()
                
                # Execute login
                response = login.handler(event, context)
                
                # Verify response
                assert response["statusCode"] == 200
                
                body = json.loads(response["body"])
                assert body["success"] is True
                assert "access_token" in body["data"]
                assert "refresh_token" in body["data"]
                assert body["data"]["token_type"] == "Bearer"
                assert "user" in body["data"]
                assert "tenant" in body["data"]
    
    def test_login_invalid_credentials(self, mock_environment):
        """Test login with invalid credentials."""
        with patch('src.auth.handlers.login._find_user_by_email') as mock_find_user:
            mock_find_user.return_value = None  # User not found
            
            event = {
                "httpMethod": "POST",
                "path": "/auth/login",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "email": "<EMAIL>",
                    "password": "password123"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
            
            context = MagicMock()
            
            # Execute login
            response = login.handler(event, context)
            
            # Verify response
            assert response["statusCode"] == 401
            
            body = json.loads(response["body"])
            assert body["success"] is False
            assert body["error_code"] == "INVALID_CREDENTIALS"
    
    def test_token_refresh_flow(self, mock_environment):
        """Test token refresh flow."""
        with patch('src.shared.auth.jwt_manager.verify_token') as mock_verify, \
             patch('src.auth.models.user.User.get_by_id') as mock_get_user, \
             patch('src.auth.models.tenant.Tenant.get_by_id') as mock_get_tenant:
            
            # Mock token verification
            mock_verify.return_value = {
                "sub": "user-123",
                "tenant_id": "tenant-123",
                "type": "refresh"
            }
            
            # Mock user and tenant
            user = User(
                user_id="user-123",
                tenant_id="tenant-123",
                email="<EMAIL>",
                name="Test User",
                status="ACTIVE"
            )
            tenant = Tenant(
                tenant_id="tenant-123",
                name="Test Company",
                status="ACTIVE"
            )
            
            mock_get_user.return_value = user
            mock_get_tenant.return_value = tenant
            
            event = {
                "httpMethod": "POST",
                "path": "/auth/refresh",
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "refresh_token": "valid_refresh_token"
                }),
                "requestContext": {
                    "requestId": "test-request-123",
                    "identity": {"sourceIp": "127.0.0.1"}
                }
            }
            
            context = MagicMock()
            
            # Execute refresh
            response = refresh.handler(event, context)
            
            # Verify response
            assert response["statusCode"] == 200
            
            body = json.loads(response["body"])
            assert body["success"] is True
            assert "access_token" in body["data"]
            assert "refresh_token" in body["data"]
    
    def test_email_verification_flow(self, dynamodb_mock, test_helpers, mock_environment):
        """Test email verification flow."""
        # Create test user with verification token
        user_data = test_helpers.create_test_user(dynamodb_mock)
        user_data.update({
            "email_verification_token": "valid_token_123",
            "email_verification_expires": int(time.time()) + 3600,  # 1 hour from now
            "email_verified": False
        })
        
        with patch('src.auth.handlers.verify_email._find_user_by_verification_token') as mock_find_user, \
             patch('src.auth.models.tenant.Tenant.get_by_id') as mock_get_tenant, \
             patch('src.auth.services.email_service.EmailService.send_welcome_email') as mock_welcome:
            
            # Mock user and tenant
            user = User(**user_data)
            tenant = Tenant(
                tenant_id="test-tenant-123",
                name="Test Company",
                status="ACTIVE"
            )
            
            mock_find_user.return_value = user
            mock_get_tenant.return_value = tenant
            mock_welcome.return_value = True
            
            # Mock verification method
            with patch.object(user, 'verify_email') as mock_verify:
                mock_verify.return_value = True
                
                event = {
                    "httpMethod": "POST",
                    "path": "/auth/verify-email",
                    "headers": {"Content-Type": "application/json"},
                    "body": json.dumps({
                        "token": "valid_token_123"
                    }),
                    "requestContext": {
                        "requestId": "test-request-123",
                        "identity": {"sourceIp": "127.0.0.1"}
                    }
                }
                
                context = MagicMock()
                
                # Execute verification
                response = verify_email.handler(event, context)
                
                # Verify response
                assert response["statusCode"] == 200
                
                body = json.loads(response["body"])
                assert body["success"] is True
                assert body["data"]["user"]["email_verified"] is True
                
                # Verify welcome email was sent
                mock_welcome.assert_called_once()
