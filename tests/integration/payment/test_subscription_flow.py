# tests/integration/payment/test_subscription_flow.py
# Implementado según "Testing Guidelines"

"""
Integration tests for subscription flow.
Tests the complete subscription lifecycle.
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import Mock, patch

from handlers.create_subscription import handler as create_subscription_handler
from handlers.get_subscription import handler as get_subscription_handler
from handlers.cancel_subscription import handler as cancel_subscription_handler
from handlers.list_plans import handler as list_plans_handler
from models.plan import Plan, PlanType
from models.subscription import Subscription, SubscriptionStatus, BillingInterval
from shared.auth import AuthContext


class TestSubscriptionFlow:
    """Integration tests for subscription flow."""
    
    @pytest.fixture
    def auth_context(self):
        """Create a mock auth context."""
        context = Mock(spec=AuthContext)
        context.tenant_id = "tenant-123"
        context.user_id = "user-456"
        context.email = "<EMAIL>"
        context.role = "MASTER"
        context.is_master.return_value = True
        context.is_member.return_value = False
        return context
    
    @pytest.fixture
    def basic_plan(self):
        """Create a basic plan for testing."""
        return Plan(
            plan_id="plan-basic",
            name="Basic Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99"),
            yearly_price=Decimal("299.99"),
            features=["basic_chat", "email_support"],
            limits={"max_users": 5, "storage_gb": 5}
        )
    
    @pytest.fixture
    def lambda_event(self):
        """Create a basic Lambda event."""
        return {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {
                'Content-Type': 'application/json'
            },
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
    
    @patch('src.payment.models.plan.db_client')
    @patch('src.payment.models.subscription.db_client')
    @patch('src.auth.models.tenant.db_client')
    def test_list_plans_flow(self, mock_tenant_db, mock_sub_db, mock_plan_db, lambda_event):
        """Test listing available plans."""
        # Setup
        lambda_event['httpMethod'] = 'GET'
        lambda_event['path'] = '/payment/plans'
        lambda_event['body'] = None
        
        # Mock plan data
        mock_plan_db.query.return_value = {
            'Items': [
                {
                    'plan_id': 'plan-free',
                    'name': 'Free Plan',
                    'plan_type': 'FREE',
                    'status': 'ACTIVE',
                    'monthly_price': '0.00',
                    'yearly_price': '0.00',
                    'features': ['basic_chat'],
                    'limits': {'max_users': 1},
                    'trial_days': 0,
                    'created_at': 1234567890,
                    'updated_at': 1234567890
                },
                {
                    'plan_id': 'plan-basic',
                    'name': 'Basic Plan',
                    'plan_type': 'BASIC',
                    'status': 'ACTIVE',
                    'monthly_price': '29.99',
                    'yearly_price': '299.99',
                    'features': ['basic_chat', 'email_support'],
                    'limits': {'max_users': 5, 'storage_gb': 5},
                    'trial_days': 14,
                    'created_at': 1234567890,
                    'updated_at': 1234567890
                }
            ]
        }
        
        # Execute
        response = list_plans_handler(lambda_event, {})
        
        # Verify
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert 'data' in body
        assert 'plans' in body['data']
        assert len(body['data']['plans']) == 2
        
        # Verify plan data
        plans = body['data']['plans']
        free_plan = next(p for p in plans if p['plan_type'] == 'FREE')
        basic_plan = next(p for p in plans if p['plan_type'] == 'BASIC')
        
        assert free_plan['monthly_price'] == '0.00'
        assert basic_plan['monthly_price'] == '29.99'
    
    @patch('src.payment.services.stripe_service.stripe_service')
    @patch('src.payment.models.plan.db_client')
    @patch('src.payment.models.subscription.db_client')
    @patch('src.auth.models.tenant.db_client')
    def test_create_subscription_flow(self, mock_tenant_db, mock_sub_db, mock_plan_db, mock_stripe, auth_context, basic_plan, lambda_event):
        """Test creating a subscription."""
        # Setup
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'plan_id': 'plan-basic',
            'billing_interval': 'MONTHLY'
        })
        
        # Mock plan lookup
        mock_plan_db.get_item.return_value = {
            'plan_id': 'plan-basic',
            'name': 'Basic Plan',
            'plan_type': 'BASIC',
            'status': 'ACTIVE',
            'monthly_price': '29.99',
            'yearly_price': '299.99',
            'features': ['basic_chat', 'email_support'],
            'limits': {'max_users': 5, 'storage_gb': 5},
            'trial_days': 14,
            'stripe_monthly_price_id': 'price_123',
            'created_at': 1234567890,
            'updated_at': 1234567890
        }
        
        # Mock tenant lookup
        mock_tenant_db.get_item.return_value = {
            'tenant_id': 'tenant-123',
            'name': 'Test Tenant',
            'master_user_email': '<EMAIL>',
            'status': 'ACTIVE',
            'plan': 'FREE',
            'created_at': 1234567890,
            'updated_at': 1234567890
        }
        
        # Mock existing subscription check
        mock_sub_db.query.return_value = {'Items': []}
        
        # Mock Stripe service
        mock_stripe.create_customer.return_value = {
            'customer_id': 'cus_123',
            'email': '<EMAIL>',
            'name': 'Test Tenant'
        }
        
        mock_stripe.create_subscription.return_value = {
            'subscription_id': 'sub_123',
            'status': 'trialing',
            'current_period_start': 1234567890,
            'current_period_end': 1234567890 + (30 * 24 * 60 * 60),
            'trial_end': 1234567890 + (14 * 24 * 60 * 60),
            'client_secret': 'pi_123_secret_456'
        }
        
        # Execute
        response = create_subscription_handler(lambda_event, {})
        
        # Verify
        assert response['statusCode'] == 201
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert 'data' in body
        assert 'subscription' in body['data']
        assert 'plan' in body['data']
        assert 'stripe_client_secret' in body['data']
        
        # Verify subscription data
        subscription = body['data']['subscription']
        assert subscription['tenant_id'] == 'tenant-123'
        assert subscription['plan_id'] == 'plan-basic'
        assert subscription['status'] == 'TRIAL'
        assert subscription['billing_interval'] == 'MONTHLY'
        assert subscription['amount'] == '29.99'
        
        # Verify Stripe calls
        mock_stripe.create_customer.assert_called_once()
        mock_stripe.create_subscription.assert_called_once()
        
        # Verify database calls
        mock_sub_db.put_item.assert_called_once()
        mock_tenant_db.update_item.assert_called_once()
    
    @patch('src.payment.models.subscription.db_client')
    @patch('src.payment.models.plan.db_client')
    def test_get_subscription_flow(self, mock_plan_db, mock_sub_db, auth_context, lambda_event):
        """Test getting current subscription."""
        # Setup
        lambda_event['httpMethod'] = 'GET'
        lambda_event['path'] = '/payment/subscription'
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = None
        
        # Mock subscription lookup
        mock_sub_db.query.return_value = {
            'Items': [{
                'subscription_id': 'sub-123',
                'tenant_id': 'tenant-123',
                'plan_id': 'plan-basic',
                'status': 'ACTIVE',
                'billing_interval': 'MONTHLY',
                'amount': '29.99',
                'currency': 'USD',
                'current_period_start': 1234567890,
                'current_period_end': 1234567890 + (30 * 24 * 60 * 60),
                'created_at': 1234567890,
                'updated_at': 1234567890
            }]
        }
        
        # Mock plan lookup
        mock_plan_db.get_item.return_value = {
            'plan_id': 'plan-basic',
            'name': 'Basic Plan',
            'plan_type': 'BASIC',
            'status': 'ACTIVE',
            'monthly_price': '29.99',
            'yearly_price': '299.99',
            'features': ['basic_chat', 'email_support'],
            'limits': {'max_users': 5, 'storage_gb': 5},
            'trial_days': 14,
            'created_at': 1234567890,
            'updated_at': 1234567890
        }
        
        # Execute
        response = get_subscription_handler(lambda_event, {})
        
        # Verify
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert 'data' in body
        assert body['data']['has_subscription'] is True
        assert 'subscription' in body['data']
        assert 'plan' in body['data']
        
        # Verify subscription data
        subscription = body['data']['subscription']
        assert subscription['subscription_id'] == 'sub-123'
        assert subscription['status'] == 'ACTIVE'
        assert subscription['is_active'] is True
    
    @patch('src.payment.services.stripe_service.stripe_service')
    @patch('src.payment.models.subscription.db_client')
    def test_cancel_subscription_flow(self, mock_sub_db, mock_stripe, auth_context, lambda_event):
        """Test cancelling a subscription."""
        # Setup
        lambda_event['httpMethod'] = 'POST'
        lambda_event['path'] = '/payment/subscription/cancel'
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'reason': 'No longer needed',
            'at_period_end': True
        })
        
        # Mock subscription lookup
        mock_sub_db.query.return_value = {
            'Items': [{
                'subscription_id': 'sub-123',
                'tenant_id': 'tenant-123',
                'plan_id': 'plan-basic',
                'status': 'ACTIVE',
                'billing_interval': 'MONTHLY',
                'amount': '29.99',
                'stripe_subscription_id': 'sub_stripe_123',
                'current_period_start': 1234567890,
                'current_period_end': 1234567890 + (30 * 24 * 60 * 60),
                'created_at': 1234567890,
                'updated_at': 1234567890
            }]
        }
        
        # Mock Stripe cancellation
        mock_stripe.cancel_subscription.return_value = {
            'subscription_id': 'sub_stripe_123',
            'status': 'active',
            'canceled_at': None,
            'cancel_at_period_end': True
        }
        
        # Execute
        response = cancel_subscription_handler(lambda_event, {})
        
        # Verify
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert 'data' in body
        assert body['data']['cancelled_at_period_end'] is True
        
        # Verify Stripe call
        mock_stripe.cancel_subscription.assert_called_once_with(
            'sub_stripe_123',
            at_period_end=True
        )
        
        # Verify database update
        mock_sub_db.update_item.assert_called_once()
    
    def test_subscription_flow_unauthorized_user(self, lambda_event):
        """Test subscription operations with unauthorized user."""
        # Setup unauthorized context
        auth_context = Mock(spec=AuthContext)
        auth_context.tenant_id = "tenant-123"
        auth_context.user_id = "user-456"
        auth_context.role = "MEMBER"
        auth_context.is_master.return_value = False
        auth_context.is_member.return_value = True
        
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'plan_id': 'plan-basic',
            'billing_interval': 'MONTHLY'
        })
        
        # Execute
        response = create_subscription_handler(lambda_event, {})
        
        # Verify
        assert response['statusCode'] == 403
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'error' in body
        assert 'authorization' in body['error']['message'].lower()
    
    def test_subscription_flow_invalid_plan(self, auth_context, lambda_event):
        """Test subscription creation with invalid plan."""
        # Setup
        lambda_event['auth_context'] = auth_context
        lambda_event['body'] = json.dumps({
            'plan_id': 'invalid-plan',
            'billing_interval': 'MONTHLY'
        })
        
        # Mock plan lookup to return None
        with patch('src.payment.models.plan.db_client') as mock_plan_db:
            mock_plan_db.get_item.return_value = None
            
            # Execute
            response = create_subscription_handler(lambda_event, {})
            
            # Verify
            assert response['statusCode'] == 404
            
            body = json.loads(response['body'])
            assert body['success'] is False
            assert 'error' in body
            assert 'not found' in body['error']['message'].lower()
