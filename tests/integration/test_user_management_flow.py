#!/usr/bin/env python3
# tests/integration/test_user_management_flow.py
# Integration tests for user management flow

"""
Integration tests for user management functionality.
Tests CRUD operations for users within tenants with proper authorization.
"""

import pytest
import json
from datetime import datetime

# Import handlers for testing
from services.tenant.src.handlers.get_user import handler as get_user_handler
from services.tenant.src.handlers.update_user import handler as update_user_handler
from services.tenant.src.handlers.delete_user import handler as delete_user_handler
from services.tenant.src.handlers.list_users import handler as list_users_handler


@pytest.mark.integration
class TestUserManagementFlow:
    """Test user management flow integration."""
    
    def test_get_user_as_self(self, dynamodb_table, jwt_token, api_event, 
                             lambda_context, test_helpers):
        """Test getting user information as the user themselves."""
        
        # Create user record
        user_id = 'user_test_123'
        tenant_id = 'tenant_test_123'
        
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{user_id}',
            entity_type='user',
            user_id=user_id,
            tenant_id=tenant_id,
            email='<EMAIL>',
            first_name='John',
            last_name='Doe',
            role='user',
            status='active',
            created_at=datetime.utcnow().isoformat()
        )
        
        event = api_event(
            method='GET',
            path=f'/tenant/users/{user_id}',
            path_params={'user_id': user_id},
            auth_token=jwt_token
        )
        
        response = get_user_handler(event, lambda_context)
        
        # Verify successful response
        body = test_helpers.assert_response_success(response)
        assert body['data']['user_id'] == user_id
        assert body['data']['email'] == '<EMAIL>'
        assert body['data']['role'] == 'user'
    
    def test_get_user_as_admin(self, dynamodb_table, admin_jwt_token, api_event, 
                              lambda_context, test_helpers):
        """Test getting user information as admin."""
        
        # Create target user record
        target_user_id = 'user_target_123'
        tenant_id = 'tenant_test_123'
        
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{target_user_id}',
            entity_type='user',
            user_id=target_user_id,
            tenant_id=tenant_id,
            email='<EMAIL>',
            first_name='Jane',
            last_name='Smith',
            role='user',
            status='active',
            created_at=datetime.utcnow().isoformat()
        )
        
        event = api_event(
            method='GET',
            path=f'/tenant/users/{target_user_id}',
            path_params={'user_id': target_user_id},
            auth_token=admin_jwt_token
        )
        
        response = get_user_handler(event, lambda_context)
        
        # Verify successful response
        body = test_helpers.assert_response_success(response)
        assert body['data']['user_id'] == target_user_id
        assert body['data']['email'] == '<EMAIL>'
    
    def test_get_user_unauthorized(self, dynamodb_table, jwt_token, api_event, 
                                  lambda_context, test_helpers):
        """Test getting user information without authorization."""
        
        # Create another user record
        other_user_id = 'user_other_123'
        tenant_id = 'tenant_test_123'
        
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{other_user_id}',
            entity_type='user',
            user_id=other_user_id,
            tenant_id=tenant_id,
            email='<EMAIL>',
            role='user',
            status='active'
        )
        
        event = api_event(
            method='GET',
            path=f'/tenant/users/{other_user_id}',
            path_params={'user_id': other_user_id},
            auth_token=jwt_token  # Regular user trying to access another user
        )
        
        response = get_user_handler(event, lambda_context)
        
        # Verify authorization error
        test_helpers.assert_response_error(response, 403)
    
    def test_update_user_self_profile(self, dynamodb_table, jwt_token, api_event, 
                                     lambda_context, test_helpers):
        """Test updating own user profile."""
        
        user_id = 'user_test_123'
        tenant_id = 'tenant_test_123'
        
        # Create user record
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{user_id}',
            entity_type='user',
            user_id=user_id,
            tenant_id=tenant_id,
            email='<EMAIL>',
            first_name='John',
            last_name='Doe',
            role='user',
            status='active'
        )
        
        update_data = {
            'first_name': 'Johnny',
            'last_name': 'Doe-Smith',
            'email': '<EMAIL>'
        }
        
        event = api_event(
            method='PUT',
            path=f'/tenant/users/{user_id}',
            path_params={'user_id': user_id},
            body=update_data,
            auth_token=jwt_token
        )
        
        response = update_user_handler(event, lambda_context)
        
        # Verify successful update
        body = test_helpers.assert_response_success(response)
        assert body['data']['first_name'] == 'Johnny'
        assert body['data']['last_name'] == 'Doe-Smith'
        assert body['data']['email'] == '<EMAIL>'
    
    def test_update_user_role_as_admin(self, dynamodb_table, admin_jwt_token, api_event, 
                                      lambda_context, test_helpers):
        """Test updating user role as admin."""
        
        target_user_id = 'user_target_123'
        tenant_id = 'tenant_test_123'
        
        # Create target user record
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{target_user_id}',
            entity_type='user',
            user_id=target_user_id,
            tenant_id=tenant_id,
            email='<EMAIL>',
            role='user',
            status='active'
        )
        
        update_data = {
            'role': 'admin',
            'permissions': ['read', 'write', 'admin', 'invite_users']
        }
        
        event = api_event(
            method='PUT',
            path=f'/tenant/users/{target_user_id}',
            path_params={'user_id': target_user_id},
            body=update_data,
            auth_token=admin_jwt_token
        )
        
        response = update_user_handler(event, lambda_context)
        
        # Verify successful update
        body = test_helpers.assert_response_success(response)
        assert body['data']['role'] == 'admin'
        assert 'admin' in body['data']['permissions']
    
    def test_update_user_role_unauthorized(self, dynamodb_table, jwt_token, api_event, 
                                         lambda_context, test_helpers):
        """Test updating user role without authorization."""
        
        user_id = 'user_test_123'
        tenant_id = 'tenant_test_123'
        
        # Create user record
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{user_id}',
            entity_type='user',
            user_id=user_id,
            tenant_id=tenant_id,
            role='user',
            status='active'
        )
        
        update_data = {
            'role': 'admin'  # Regular user trying to promote themselves
        }
        
        event = api_event(
            method='PUT',
            path=f'/tenant/users/{user_id}',
            path_params={'user_id': user_id},
            body=update_data,
            auth_token=jwt_token
        )
        
        response = update_user_handler(event, lambda_context)
        
        # Verify authorization error
        test_helpers.assert_response_error(response, 403)
    
    def test_delete_user_as_admin(self, dynamodb_table, admin_jwt_token, api_event, 
                                 lambda_context, test_helpers):
        """Test deleting user as admin."""
        
        target_user_id = 'user_target_123'
        tenant_id = 'tenant_test_123'
        
        # Create target user record
        test_helpers.create_test_item(
            dynamodb_table,
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{target_user_id}',
            entity_type='user',
            user_id=target_user_id,
            tenant_id=tenant_id,
            email='<EMAIL>',
            role='user',
            status='active'
        )
        
        event = api_event(
            method='DELETE',
            path=f'/tenant/users/{target_user_id}',
            path_params={'user_id': target_user_id},
            auth_token=admin_jwt_token
        )
        
        response = delete_user_handler(event, lambda_context)
        
        # Verify successful deletion
        body = test_helpers.assert_response_success(response)
        assert body['data']['user_id'] == target_user_id
        assert body['data']['deletion_type'] == 'soft'
        assert body['data']['can_be_restored'] is True
    
    def test_delete_user_self_forbidden(self, dynamodb_table, jwt_token, api_event, 
                                       lambda_context, test_helpers):
        """Test that users cannot delete themselves."""
        
        user_id = 'user_test_123'
        
        event = api_event(
            method='DELETE',
            path=f'/tenant/users/{user_id}',
            path_params={'user_id': user_id},
            auth_token=jwt_token
        )
        
        response = delete_user_handler(event, lambda_context)
        
        # Verify authorization error
        test_helpers.assert_response_error(response, 403)
    
    def test_delete_user_unauthorized(self, dynamodb_table, jwt_token, api_event, 
                                     lambda_context, test_helpers):
        """Test deleting user without authorization."""
        
        target_user_id = 'user_target_123'
        
        event = api_event(
            method='DELETE',
            path=f'/tenant/users/{target_user_id}',
            path_params={'user_id': target_user_id},
            auth_token=jwt_token  # Regular user trying to delete another user
        )
        
        response = delete_user_handler(event, lambda_context)
        
        # Verify authorization error
        test_helpers.assert_response_error(response, 403)
    
    def test_list_users_as_admin(self, dynamodb_table, admin_jwt_token, api_event, 
                                lambda_context, test_helpers):
        """Test listing users as admin."""
        
        tenant_id = 'tenant_test_123'
        
        # Create multiple user records
        users = [
            {'user_id': 'user_1', 'email': '<EMAIL>', 'role': 'user'},
            {'user_id': 'user_2', 'email': '<EMAIL>', 'role': 'user'},
            {'user_id': 'admin_1', 'email': '<EMAIL>', 'role': 'admin'}
        ]
        
        for user in users:
            test_helpers.create_test_item(
                dynamodb_table,
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user["user_id"]}',
                entity_type='user',
                tenant_id=tenant_id,
                status='active',
                **user
            )
        
        event = api_event(
            method='GET',
            path='/tenant/users',
            auth_token=admin_jwt_token
        )
        
        response = list_users_handler(event, lambda_context)
        
        # Verify successful response
        body = test_helpers.assert_response_success(response)
        assert len(body['data']['users']) >= 3
        assert body['data']['total_count'] >= 3
    
    def test_user_not_found(self, jwt_token, api_event, lambda_context, test_helpers):
        """Test accessing non-existent user."""
        
        event = api_event(
            method='GET',
            path='/tenant/users/non_existent_user',
            path_params={'user_id': 'non_existent_user'},
            auth_token=jwt_token
        )
        
        response = get_user_handler(event, lambda_context)
        
        # Verify not found error
        test_helpers.assert_response_error(response, 404)
