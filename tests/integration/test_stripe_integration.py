# tests/integration/test_stripe_integration.py
# Tests de integración para Stripe

"""
Integration tests for Stripe services.
Tests the complete flow of Stripe operations.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import stripe

from services.stripe_client import stripe_client
from services.customer_service import customer_service
from services.subscription_service import subscription_service
from services.webhook_service import webhook_service
from models.subscription import BillingInterval
from models.tenant import Tenant


class TestStripeIntegration:
    """Integration tests for Stripe services."""
    
    @pytest.fixture
    def mock_tenant(self):
        """Create a mock tenant for testing."""
        tenant = Mock(spec=Tenant)
        tenant.tenant_id = "tenant_123"
        tenant.name = "Test Tenant"
        tenant.slug = "test-tenant"
        tenant.billing_email = "<EMAIL>"
        tenant.is_active.return_value = True
        tenant.save = AsyncMock()
        return tenant
    
    @pytest.fixture
    def mock_plan(self):
        """Create a mock plan for testing."""
        plan = Mock()
        plan.plan_id = "plan_123"
        plan.name = "Basic Plan"
        plan.stripe_monthly_price_id = "price_monthly_123"
        plan.stripe_yearly_price_id = "price_yearly_123"
        plan.is_active.return_value = True
        return plan
    
    @patch('src.payment.services.stripe_client.stripe')
    async def test_complete_subscription_flow(self, mock_stripe, mock_tenant, mock_plan):
        """Test complete subscription creation flow."""
        # Mock Stripe responses
        mock_stripe_customer = Mock()
        mock_stripe_customer.id = "cus_test123"
        mock_stripe_customer.email = "<EMAIL>"
        mock_stripe_customer.name = "Test Tenant"
        mock_stripe_customer.phone = None
        mock_stripe_customer.created = 1640995200
        mock_stripe_customer.metadata = {"tenant_id": "tenant_123"}
        mock_stripe_customer.default_source = None
        mock_stripe_customer.invoice_prefix = None
        mock_stripe_customer.currency = None
        mock_stripe_customer.balance = 0
        mock_stripe_customer.delinquent = False
        
        mock_stripe_subscription = Mock()
        mock_stripe_subscription.id = "sub_test123"
        mock_stripe_subscription.status = "active"
        mock_stripe_subscription.customer = "cus_test123"
        mock_stripe_subscription.current_period_start = 1640995200
        mock_stripe_subscription.current_period_end = 1643673600
        mock_stripe_subscription.trial_start = None
        mock_stripe_subscription.trial_end = None
        mock_stripe_subscription.cancel_at_period_end = False
        mock_stripe_subscription.canceled_at = None
        mock_stripe_subscription.created = 1640995200
        mock_stripe_subscription.metadata = {"tenant_id": "tenant_123"}
        mock_stripe_subscription.items = Mock()
        mock_stripe_subscription.items.data = [
            Mock(
                id="si_test123",
                price=Mock(
                    id="price_monthly_123",
                    unit_amount=2000,
                    currency="usd",
                    recurring=Mock(interval="month")
                )
            )
        ]
        
        mock_stripe.Customer.create.return_value = mock_stripe_customer
        mock_stripe.Subscription.create.return_value = mock_stripe_subscription
        
        # Mock database operations
        with patch('src.payment.models.customer.Customer.save', new_callable=AsyncMock) as mock_customer_save, \
             patch('src.payment.models.subscription.Subscription.save', new_callable=AsyncMock) as mock_subscription_save, \
             patch('src.payment.services.customer_service.Customer.get_by_tenant_id', new_callable=AsyncMock) as mock_get_customer, \
             patch('src.payment.services.subscription_service.Subscription.get_active_by_tenant_id', new_callable=AsyncMock) as mock_get_subscription, \
             patch('src.payment.services.subscription_service.Tenant.get_by_id', new_callable=AsyncMock) as mock_get_tenant, \
             patch('src.payment.services.subscription_service.Plan.get_by_id', new_callable=AsyncMock) as mock_get_plan:
            
            # Setup mocks
            mock_get_customer.return_value = None  # No existing customer
            mock_get_subscription.return_value = None  # No existing subscription
            mock_get_tenant.return_value = mock_tenant
            mock_get_plan.return_value = mock_plan
            
            # Create subscription
            subscription = await subscription_service.create_subscription(
                tenant_id="tenant_123",
                plan_id="plan_123",
                billing_interval=BillingInterval.MONTHLY,
                payment_method_id="pm_test123"
            )
            
            # Verify Stripe calls
            mock_stripe.Customer.create.assert_called_once()
            mock_stripe.Subscription.create.assert_called_once()
            
            # Verify customer creation
            customer_call_args = mock_stripe.Customer.create.call_args[1]
            assert customer_call_args["email"] == "<EMAIL>"
            assert customer_call_args["name"] == "Test Tenant"
            assert customer_call_args["metadata"]["tenant_id"] == "tenant_123"
            
            # Verify subscription creation
            subscription_call_args = mock_stripe.Subscription.create.call_args[1]
            assert subscription_call_args["customer"] == "cus_test123"
            assert subscription_call_args["items"][0]["price"] == "price_monthly_123"
            assert subscription_call_args["default_payment_method"] == "pm_test123"
            
            # Verify local objects
            assert subscription.subscription_id == "sub_test123"
            assert subscription.tenant_id == "tenant_123"
            assert subscription.plan_id == "plan_123"
            assert subscription.billing_interval == BillingInterval.MONTHLY
            
            # Verify database saves
            mock_customer_save.assert_called_once()
            mock_subscription_save.assert_called_once()
    
    @patch('src.payment.services.stripe_client.stripe')
    async def test_webhook_subscription_updated_flow(self, mock_stripe):
        """Test webhook processing for subscription updates."""
        # Mock webhook event
        webhook_event = {
            "id": "evt_test123",
            "type": "customer.subscription.updated",
            "data": {
                "object": {
                    "id": "sub_test123",
                    "status": "past_due",
                    "current_period_start": 1640995200,
                    "current_period_end": 1643673600,
                    "cancel_at_period_end": False,
                    "canceled_at": None
                }
            }
        }
        
        # Mock existing subscription
        mock_subscription = Mock()
        mock_subscription.subscription_id = "subscription_123"
        mock_subscription.save = Mock()
        
        mock_stripe.Webhook.construct_event.return_value = webhook_event
        
        with patch('src.payment.services.webhook_service.Subscription.get_by_stripe_subscription_id') as mock_get_subscription:
            mock_get_subscription.return_value = mock_subscription
            
            # Process webhook
            result = webhook_service.process_webhook(
                payload='{"id": "evt_test123"}',
                signature="t=123,v1=abc",
                endpoint_secret="whsec_test"
            )
            
            # Verify webhook processing
            assert result["success"] is True
            assert result["event_id"] == "evt_test123"
            assert result["event_type"] == "customer.subscription.updated"
            
            # Verify subscription update
            mock_get_subscription.assert_called_once_with("sub_test123")
            mock_subscription.save.assert_called_once()
    
    @patch('src.payment.services.stripe_client.stripe')
    async def test_subscription_cancellation_flow(self, mock_stripe):
        """Test subscription cancellation flow."""
        # Mock existing subscription
        mock_subscription = Mock()
        mock_subscription.subscription_id = "sub_test123"
        mock_subscription.stripe_subscription_id = "sub_test123"
        
        # Mock Stripe cancellation response
        mock_cancelled_subscription = Mock()
        mock_cancelled_subscription.id = "sub_test123"
        mock_cancelled_subscription.status = "canceled"
        mock_cancelled_subscription.cancel_at_period_end = True
        mock_cancelled_subscription.canceled_at = 1640995200
        
        mock_stripe.Subscription.modify.return_value = mock_cancelled_subscription
        
        with patch('src.payment.services.subscription_service.Subscription.get_by_id', new_callable=AsyncMock) as mock_get_subscription, \
             patch('src.payment.models.subscription.Subscription.save', new_callable=AsyncMock) as mock_save:
            
            mock_get_subscription.return_value = mock_subscription
            
            # Cancel subscription
            result = await subscription_service.cancel_subscription(
                subscription_id="sub_test123",
                at_period_end=True,
                cancellation_reason="Customer request"
            )
            
            # Verify Stripe call
            mock_stripe.Subscription.modify.assert_called_once_with(
                "sub_test123",
                cancel_at_period_end=True
            )
            
            # Verify local update
            mock_save.assert_called_once()
    
    @patch('src.payment.services.stripe_client.stripe')
    async def test_customer_sync_flow(self, mock_stripe):
        """Test customer synchronization with Stripe."""
        # Mock existing customer
        mock_customer = Mock()
        mock_customer.customer_id = "customer_123"
        mock_customer.stripe_customer_id = "cus_test123"
        
        # Mock Stripe customer data
        mock_stripe_customer = Mock()
        mock_stripe_customer.id = "cus_test123"
        mock_stripe_customer.email = "<EMAIL>"
        mock_stripe_customer.name = "Updated Name"
        mock_stripe_customer.phone = "+1234567890"
        
        mock_stripe.Customer.retrieve.return_value = mock_stripe_customer
        
        with patch('src.payment.services.customer_service.Customer.get_by_id', new_callable=AsyncMock) as mock_get_customer, \
             patch('src.payment.models.customer.Customer.save', new_callable=AsyncMock) as mock_save:
            
            mock_get_customer.return_value = mock_customer
            
            # Sync customer
            result = await customer_service.sync_customer_with_stripe("customer_123")
            
            # Verify Stripe call
            mock_stripe.Customer.retrieve.assert_called_once_with("cus_test123")
            
            # Verify local update
            assert mock_customer.email == "<EMAIL>"
            assert mock_customer.name == "Updated Name"
            assert mock_customer.phone == "+1234567890"
            mock_save.assert_called_once()
    
    def test_error_handling_integration(self):
        """Test error handling across services."""
        # Test Stripe error propagation
        with patch('src.payment.services.stripe_client.stripe.Customer.create') as mock_create:
            mock_create.side_effect = stripe.error.CardError(
                message="Card declined",
                param="card",
                code="card_declined"
            )
            
            from shared.exceptions import PaymentException
            
            with pytest.raises(PaymentException) as exc_info:
                stripe_client.create_customer(
                    email="<EMAIL>",
                    name="Test Customer",
                    tenant_id="tenant_123"
                )
            
            assert exc_info.value.error_code == "CARD_DECLINED"
            assert "Payment failed" in str(exc_info.value)
    
    @patch('src.payment.services.stripe_client.stripe')
    def test_webhook_signature_verification(self, mock_stripe):
        """Test webhook signature verification."""
        # Test valid signature
        mock_event = {"id": "evt_test123", "type": "customer.created"}
        mock_stripe.Webhook.construct_event.return_value = mock_event
        
        result = webhook_service.process_webhook(
            payload='{"id": "evt_test123"}',
            signature="t=123,v1=valid_signature",
            endpoint_secret="whsec_test"
        )
        
        assert result["success"] is True
        
        # Test invalid signature
        mock_stripe.Webhook.construct_event.side_effect = stripe.error.SignatureVerificationError(
            message="Invalid signature",
            sig_header="invalid"
        )
        
        from shared.exceptions import ValidationException
        
        with pytest.raises(ValidationException) as exc_info:
            webhook_service.process_webhook(
                payload='{"id": "evt_test123"}',
                signature="t=123,v1=invalid_signature",
                endpoint_secret="whsec_test"
            )
        
        assert "Invalid webhook signature" in str(exc_info.value)
