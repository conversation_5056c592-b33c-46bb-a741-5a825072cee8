# tests/unit/auth/services/test_email_service.py

"""
Unit tests for EmailService.
Tests email sending, template rendering, and error handling.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from botocore.exceptions import ClientError

from services.email_service import EmailService
from shared.exceptions import EmailException


class TestEmailService:
    """Test cases for EmailService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.email_service = EmailService()
    
    @patch('src.auth.services.email_service.boto3.client')
    def test_init_creates_ses_client(self, mock_boto3_client):
        """Test EmailService initialization creates SES client."""
        service = EmailService()
        # Verify SES client was created (with or without region)
        mock_boto3_client.assert_called_once()
        call_args = mock_boto3_client.call_args
        assert call_args[0][0] == 'ses'  # First positional arg should be 'ses'
        assert service.ses_client is not None
    
    def test_render_template_verification_email(self):
        """Test rendering verification email template."""
        result = self.email_service._render_template(
            'verification',
            {
                'user_name': '<PERSON>',
                'verification_code': '123456',
                'company_name': 'Test Company'
            }
        )
        
        assert 'John Doe' in result['subject']
        assert 'John Doe' in result['html_body']
        assert '123456' in result['html_body']
        assert 'Test Company' in result['html_body']
        assert 'verification' in result['subject'].lower()
    
    def test_render_template_welcome_email(self):
        """Test rendering welcome email template."""
        result = self.email_service._render_template(
            'welcome',
            {
                'user_name': 'Jane Smith',
                'company_name': 'Test Corp',
                'login_url': 'https://app.test.com/login'
            }
        )
        
        assert 'Jane Smith' in result['html_body']
        assert 'Test Corp' in result['html_body']
        assert 'https://app.test.com/login' in result['html_body']
        assert 'welcome' in result['subject'].lower()
    
    def test_render_template_password_reset(self):
        """Test rendering password reset email template."""
        result = self.email_service._render_template(
            'password_reset',
            {
                'user_name': 'Bob Wilson',
                'reset_code': 'ABC123',
                'company_name': 'Test Inc'
            }
        )
        
        assert 'Bob Wilson' in result['html_body']
        assert 'ABC123' in result['html_body']
        assert 'Test Inc' in result['html_body']
        assert 'password' in result['subject'].lower()
    
    def test_render_template_invitation(self):
        """Test rendering invitation email template."""
        result = self.email_service._render_template(
            'invitation',
            {
                'invitee_name': 'Alice Johnson',
                'inviter_name': 'John Admin',
                'company_name': 'Test LLC',
                'invitation_url': 'https://app.test.com/accept/xyz'
            }
        )
        
        assert 'Alice Johnson' in result['html_body']
        assert 'John Admin' in result['html_body']
        assert 'Test LLC' in result['html_body']
        assert 'https://app.test.com/accept/xyz' in result['html_body']
        assert 'invitation' in result['subject'].lower()
    
    def test_render_template_invalid_template(self):
        """Test rendering with invalid template raises exception."""
        with pytest.raises(EmailException) as exc_info:
            self.email_service._render_template('invalid_template', {})
        
        assert 'Unknown email template' in str(exc_info.value)
    
    @pytest.mark.asyncio
    @patch('src.auth.services.email_service.boto3.client')
    async def test_send_email_success(self, mock_boto3_client):
        """Test successful email sending."""
        # Setup mock
        mock_ses = Mock()
        mock_ses.send_templated_email.return_value = {
            'MessageId': 'test-message-id-123'
        }
        mock_boto3_client.return_value = mock_ses
        
        # Create service instance
        service = EmailService()
        
        # Test email sending
        result = await service.send_email(
            to_email='<EMAIL>',
            template='verification',
            template_data={
                'user_name': 'Test User',
                'verification_code': '123456',
                'company_name': 'Test Company'
            }
        )
        
        # Verify result
        assert result['success'] is True
        assert result['message_id'] == 'test-message-id-123'

        # Verify SES call
        mock_ses.send_templated_email.assert_called_once()
        call_args = mock_ses.send_templated_email.call_args[1]
        assert call_args['Destination']['ToAddresses'] == ['<EMAIL>']
        assert 'agent-scl-test-email-verification' in call_args['Template']
    
    @pytest.mark.asyncio
    @patch('src.auth.services.email_service.boto3.client')
    async def test_send_email_ses_error(self, mock_boto3_client):
        """Test email sending with SES error."""
        # Setup mock to raise ClientError
        mock_ses = Mock()
        mock_ses.send_templated_email.side_effect = ClientError(
            error_response={
                'Error': {
                    'Code': 'MessageRejected',
                    'Message': 'Email address not verified'
                }
            },
            operation_name='SendEmail'
        )
        mock_boto3_client.return_value = mock_ses
        
        # Create service instance
        service = EmailService()
        
        # Test email sending
        with pytest.raises(EmailException) as exc_info:
            await service.send_email(
                to_email='<EMAIL>',
                template='verification',
                template_data={
                    'user_name': 'Test User',
                    'verification_code': '123456',
                    'company_name': 'Test Company'
                }
            )
        
        assert 'Failed to send email' in str(exc_info.value)
        assert 'MessageRejected' in str(exc_info.value)
    
    @pytest.mark.asyncio
    @patch('src.auth.services.email_service.boto3.client')
    async def test_send_verification_email(self, mock_boto3_client):
        """Test sending verification email convenience method."""
        # Setup mock
        mock_ses = Mock()
        mock_ses.send_templated_email.return_value = {'MessageId': 'test-id'}
        mock_boto3_client.return_value = mock_ses
        
        service = EmailService()
        
        result = await service.send_verification_email(
            to_email='<EMAIL>',
            user_name='Test User',
            verification_code='123456',
            company_name='Test Company'
        )
        
        assert result['success'] is True
        mock_ses.send_templated_email.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.auth.services.email_service.boto3.client')
    async def test_send_welcome_email(self, mock_boto3_client):
        """Test sending welcome email convenience method."""
        # Setup mock
        mock_ses = Mock()
        mock_ses.send_templated_email.return_value = {'MessageId': 'test-id'}
        mock_boto3_client.return_value = mock_ses
        
        service = EmailService()
        
        result = await service.send_welcome_email(
            to_email='<EMAIL>',
            user_name='Test User',
            company_name='Test Company',
            login_url='https://app.test.com/login'
        )
        
        assert result['success'] is True
        mock_ses.send_templated_email.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.auth.services.email_service.boto3.client')
    async def test_send_password_reset_email(self, mock_boto3_client):
        """Test sending password reset email convenience method."""
        # Setup mock
        mock_ses = Mock()
        mock_ses.send_templated_email.return_value = {'MessageId': 'test-id'}
        mock_boto3_client.return_value = mock_ses
        
        service = EmailService()
        
        result = await service.send_password_reset_email(
            to_email='<EMAIL>',
            user_name='Test User',
            reset_code='ABC123',
            company_name='Test Company'
        )
        
        assert result['success'] is True
        mock_ses.send_templated_email.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.auth.services.email_service.boto3.client')
    async def test_send_invitation_email(self, mock_boto3_client):
        """Test sending invitation email convenience method."""
        # Setup mock
        mock_ses = Mock()
        mock_ses.send_templated_email.return_value = {'MessageId': 'test-id'}
        mock_boto3_client.return_value = mock_ses
        
        service = EmailService()
        
        result = await service.send_invitation_email(
            to_email='<EMAIL>',
            invitee_name='New User',
            inviter_name='Admin User',
            company_name='Test Company',
            invitation_url='https://app.test.com/accept/xyz'
        )
        
        assert result['success'] is True
        mock_ses.send_templated_email.assert_called_once()
    
    def test_validate_email_address_valid(self):
        """Test email address validation with valid emails."""
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            assert self.email_service._validate_email_address(email) is True
    
    def test_validate_email_address_invalid(self):
        """Test email address validation with invalid emails."""
        invalid_emails = [
            'invalid-email',
            '@domain.com',
            'user@',
            '<EMAIL>',
            'user@domain',
            ''
        ]
        
        for email in invalid_emails:
            assert self.email_service._validate_email_address(email) is False
    
    @pytest.mark.asyncio
    async def test_send_email_invalid_email_address(self):
        """Test sending email with invalid email address."""
        with pytest.raises(EmailException) as exc_info:
            await self.email_service.send_email(
                to_email='invalid-email',
                template='verification',
                template_data={'user_name': 'Test'}
            )
        
        assert 'Invalid email address' in str(exc_info.value)
    
    def test_get_template_data_keys_verification(self):
        """Test getting required template data keys for verification."""
        keys = self.email_service._get_template_data_keys('verification')
        expected_keys = {'user_name', 'verification_code', 'company_name'}
        assert set(keys) == expected_keys
    
    def test_get_template_data_keys_welcome(self):
        """Test getting required template data keys for welcome."""
        keys = self.email_service._get_template_data_keys('welcome')
        expected_keys = {'user_name', 'company_name', 'login_url'}
        assert set(keys) == expected_keys
    
    @pytest.mark.asyncio
    async def test_send_email_missing_template_data(self):
        """Test sending email with missing template data."""
        with pytest.raises(EmailException) as exc_info:
            await self.email_service.send_email(
                to_email='<EMAIL>',
                template='verification',
                template_data={'user_name': 'Test'}  # Missing verification_code and company_name
            )
        
        assert 'Missing required template data' in str(exc_info.value)
