# tests/unit/auth/services/test_password_service.py
# Implementado según "Testing Guidelines" - Unit Tests

"""
Unit tests for Password Service.
Tests password hashing, verification, and validation.
"""

import pytest
from unittest.mock import Mock, patch

from services.password_service import PasswordService
from shared.exceptions import ValidationException


class TestPasswordService:
    """Unit tests for Password Service."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.password_service = PasswordService()
        self.test_password = "TestPassword123!"
        self.weak_passwords = [
            "123",
            "password",
            "abc123",
            "12345678",
            "Password",
            "password123",
            "PASSWORD123"
        ]
        self.strong_passwords = [
            "TestPassword123!",
            "MySecure@Pass2024",
            "Complex#Password99",
            "Strong$Pass123",
            "Secure&Password456"
        ]
    
    def test_hash_password(self):
        """Test password hashing."""
        hashed = self.password_service.hash_password(self.test_password)
        
        assert hashed is not None
        assert isinstance(hashed, str)
        assert len(hashed) > 50  # bcrypt hashes are typically 60 characters
        assert hashed != self.test_password  # Should not be plain text
        assert hashed.startswith('$2b$')  # bcrypt format
    
    def test_hash_password_different_salts(self):
        """Test that same password produces different hashes (due to salt)."""
        hash1 = self.password_service.hash_password(self.test_password)
        hash2 = self.password_service.hash_password(self.test_password)
        
        assert hash1 != hash2  # Different salts should produce different hashes
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        hashed = self.password_service.hash_password(self.test_password)
        
        result = self.password_service.verify_password(self.test_password, hashed)
        
        assert result is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        hashed = self.password_service.hash_password(self.test_password)
        
        result = self.password_service.verify_password("WrongPassword123!", hashed)
        
        assert result is False
    
    def test_verify_password_case_sensitive(self):
        """Test that password verification is case sensitive."""
        hashed = self.password_service.hash_password(self.test_password)
        
        # Test different cases
        test_cases = [
            "testpassword123!",  # All lowercase
            "TESTPASSWORD123!",  # All uppercase
            "TestPassword123!",  # Original (should work)
            "testPassword123!"   # Different case
        ]
        
        for i, password in enumerate(test_cases):
            result = self.password_service.verify_password(password, hashed)
            if i == 2:  # Original password
                assert result is True
            else:
                assert result is False
    
    def test_validate_password_strength_weak(self):
        """Test password strength validation with weak passwords."""
        for weak_password in self.weak_passwords:
            with pytest.raises(ValidationException) as exc_info:
                self.password_service.validate_password_strength(weak_password)
            
            assert "password" in str(exc_info.value).lower()
    
    def test_validate_password_strength_strong(self):
        """Test password strength validation with strong passwords."""
        for strong_password in self.strong_passwords:
            # Should not raise exception
            self.password_service.validate_password_strength(strong_password)
    
    def test_validate_password_length_requirements(self):
        """Test password length requirements."""
        # Too short
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("Abc1!")
        
        # Too long
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("A" * 129 + "bc1!")
        
        # Just right
        self.password_service.validate_password_strength("TestPassword123!")
    
    def test_validate_password_character_requirements(self):
        """Test password character requirements."""
        # Missing uppercase
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("testpassword123!")
        
        # Missing lowercase
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("TESTPASSWORD123!")
        
        # Missing digit
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("TestPassword!")
        
        # Missing special character
        with pytest.raises(ValidationException):
            self.password_service.validate_password_strength("TestPassword123")
        
        # All requirements met
        self.password_service.validate_password_strength("TestPassword123!")
    
    def test_validate_password_common_patterns(self):
        """Test validation against common password patterns."""
        common_patterns = [
            "Password123!",  # Common word + numbers + special
            "Qwerty123!",    # Keyboard pattern
            "Admin123!",     # Common admin password
            "Welcome123!",   # Common welcome password
            "Test123!"       # Common test password
        ]
        
        for pattern in common_patterns:
            with pytest.raises(ValidationException):
                self.password_service.validate_password_strength(pattern)
    
    def test_validate_password_sequential_characters(self):
        """Test validation against sequential characters."""
        sequential_passwords = [
            "Abc123456!",    # Sequential letters and numbers
            "Test12345!",    # Sequential numbers
            "TestAbcd1!",    # Sequential letters
            "Pass098765!"    # Reverse sequential numbers
        ]
        
        for password in sequential_passwords:
            with pytest.raises(ValidationException):
                self.password_service.validate_password_strength(password)
    
    def test_validate_password_repeated_characters(self):
        """Test validation against repeated characters."""
        repeated_passwords = [
            "Tesssst123!",   # Repeated 's'
            "Passsword1!",   # Repeated 's'
            "Test1111!",     # Repeated '1'
            "Testtt123!"     # Repeated 't'
        ]
        
        for password in repeated_passwords:
            with pytest.raises(ValidationException):
                self.password_service.validate_password_strength(password)
    
    def test_generate_secure_password(self):
        """Test secure password generation."""
        password = self.password_service.generate_secure_password()
        
        assert password is not None
        assert isinstance(password, str)
        assert len(password) >= 12  # Minimum length
        
        # Should pass strength validation
        self.password_service.validate_password_strength(password)
    
    def test_generate_secure_password_custom_length(self):
        """Test secure password generation with custom length."""
        lengths = [12, 16, 20, 24]
        
        for length in lengths:
            password = self.password_service.generate_secure_password(length)
            
            assert len(password) == length
            self.password_service.validate_password_strength(password)
    
    def test_generate_secure_password_uniqueness(self):
        """Test that generated passwords are unique."""
        passwords = set()
        
        for _ in range(100):
            password = self.password_service.generate_secure_password()
            passwords.add(password)
        
        # All passwords should be unique
        assert len(passwords) == 100
    
    def test_check_password_breach(self):
        """Test password breach checking (mocked)."""
        with patch('src.auth.services.password_service.requests.get') as mock_get:
            # Mock API response for breached password
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = "5E884898DA28047151D0E56F8DC6292773603D0D6AABBDD62A11EF721D1542D8:3"
            mock_get.return_value = mock_response
            
            # This should raise an exception for breached password
            with pytest.raises(ValidationException):
                self.password_service.check_password_breach("password")
    
    def test_check_password_no_breach(self):
        """Test password breach checking for clean password (mocked)."""
        with patch('src.auth.services.password_service.requests.get') as mock_get:
            # Mock API response for clean password
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = "OTHER_HASH:1\nANOTHER_HASH:2"
            mock_get.return_value = mock_response
            
            # This should not raise an exception
            self.password_service.check_password_breach("UniqueSecurePassword123!")
    
    def test_hash_password_empty_string(self):
        """Test hashing empty password."""
        with pytest.raises(ValidationException):
            self.password_service.hash_password("")
    
    def test_hash_password_none(self):
        """Test hashing None password."""
        with pytest.raises(ValidationException):
            self.password_service.hash_password(None)
    
    def test_verify_password_empty_hash(self):
        """Test verifying password with empty hash."""
        result = self.password_service.verify_password(self.test_password, "")
        assert result is False
    
    def test_verify_password_invalid_hash(self):
        """Test verifying password with invalid hash format."""
        result = self.password_service.verify_password(self.test_password, "invalid_hash")
        assert result is False
    
    def test_password_entropy_calculation(self):
        """Test password entropy calculation."""
        test_cases = [
            ("abc", 15.51),      # Low entropy
            ("Abc123", 35.41),   # Medium entropy
            ("TestPassword123!", 95.27)  # High entropy
        ]
        
        for password, expected_min_entropy in test_cases:
            entropy = self.password_service.calculate_password_entropy(password)
            assert entropy >= expected_min_entropy * 0.9  # Allow 10% variance
    
    def test_password_strength_score(self):
        """Test password strength scoring."""
        test_cases = [
            ("123", 0),          # Very weak
            ("password", 1),     # Weak
            ("Password1", 2),    # Fair
            ("Password123", 3),  # Good
            ("TestPassword123!", 4)  # Strong
        ]
        
        for password, expected_min_score in test_cases:
            score = self.password_service.get_password_strength_score(password)
            assert score >= expected_min_score
