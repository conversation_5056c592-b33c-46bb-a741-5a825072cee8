# tests/unit/auth/models/test_tenant.py
# Implementado según "Testing Guidelines" - Unit Tests

"""
Unit tests for Tenant model.
Tests tenant creation, validation, and business logic.
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from moto import mock_aws

from models.tenant import Tenant, TenantStatus
from shared.exceptions import ValidationException


class TestTenant:
    """Unit tests for Tenant model."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.tenant_id = str(uuid.uuid4())
        self.tenant_data = {
            'tenant_id': self.tenant_id,
            'name': 'Test Company',
            'status': TenantStatus.ACTIVE,
            'billing_email': '<EMAIL>',
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }
    
    def test_tenant_creation(self):
        """Test tenant creation with valid data."""
        tenant = Tenant(
            tenant_id=self.tenant_id,
            name='Test Company',
            status=TenantStatus.ACTIVE
        )

        assert tenant.name == 'Test Company'
        assert tenant.status == TenantStatus.ACTIVE
        assert tenant.tenant_id == self.tenant_id

    def test_tenant_to_dict(self):
        """Test tenant serialization to dictionary."""
        tenant = Tenant(
            tenant_id=self.tenant_id,
            name='Test Company',
            status=TenantStatus.ACTIVE
        )

        tenant_dict = tenant.to_dict()

        assert tenant_dict['tenant_id'] == self.tenant_id
        assert tenant_dict['name'] == 'Test Company'
        assert tenant_dict['status'] == TenantStatus.ACTIVE.value

    def test_tenant_is_active(self):
        """Test tenant active status check."""
        tenant = Tenant(
            tenant_id=self.tenant_id,
            name='Test Company',
            status=TenantStatus.ACTIVE
        )

        assert tenant.is_active() is True

        tenant.status = TenantStatus.INACTIVE
        assert tenant.is_active() is False
        """Test tenant creation with default values."""
        minimal_data = {
            'name': 'Test Company'
        }
        
        tenant = Tenant(**minimal_data)
        
        assert tenant.status == TenantStatus.PENDING_VERIFICATION  # Default status
        assert tenant.settings is not None
        assert tenant.settings['timezone'] == 'UTC'  # Default timezone
        assert tenant.subscription_id is None
        assert tenant.billing_email is None
    
    def test_tenant_validation_invalid_name(self):
        """Test tenant validation with invalid name."""
        invalid_names = [
            '',
            'a',  # Too short
            'a' * 101,  # Too long
            '   ',  # Only whitespace
            'Test@Company!'  # Invalid characters
        ]
        
        for invalid_name in invalid_names:
            tenant_data = self.tenant_data.copy()
            tenant_data['name'] = invalid_name
            
            with pytest.raises(ValidationException):
                Tenant(**tenant_data)
    
    def test_tenant_validation_valid_name(self):
        """Test tenant validation with valid names."""
        valid_names = [
            'Test Company',
            'ABC Corp',
            'Company-123',
            'Test Company Inc.',
            'My Startup'
        ]
        
        for valid_name in valid_names:
            tenant_data = self.tenant_data.copy()
            tenant_data['name'] = valid_name
            
            tenant = Tenant(**tenant_data)
            assert tenant.name == valid_name
    
    def test_tenant_validation_invalid_billing_email(self):
        """Test tenant validation with invalid billing email."""
        invalid_emails = [
            'invalid-email',
            'test@',
            '@example.com',
            '<EMAIL>'
        ]
        
        for invalid_email in invalid_emails:
            tenant_data = self.tenant_data.copy()
            tenant_data['billing_email'] = invalid_email
            
            with pytest.raises(ValidationException):
                Tenant(**tenant_data)
    
    def test_tenant_validation_valid_billing_email(self):
        """Test tenant validation with valid billing email."""
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for valid_email in valid_emails:
            tenant_data = self.tenant_data.copy()
            tenant_data['billing_email'] = valid_email
            
            tenant = Tenant(**tenant_data)
            assert tenant.billing_email == valid_email
    
    def test_tenant_to_dict(self):
        """Test tenant serialization to dictionary."""
        tenant = Tenant(**self.tenant_data)
        tenant_dict = tenant.to_dict()
        
        assert tenant_dict['tenant_id'] == tenant.tenant_id
        assert tenant_dict['name'] == 'Test Company'
        assert tenant_dict['status'] == TenantStatus.ACTIVE.value
        assert tenant_dict['settings'] == tenant.settings
        assert 'created_at' in tenant_dict
        assert 'updated_at' in tenant_dict
    
    def test_tenant_activate(self):
        """Test tenant activation."""
        tenant = Tenant(**self.tenant_data)
        tenant.status = TenantStatus.PENDING_VERIFICATION
        
        tenant.activate()
        
        assert tenant.status == TenantStatus.ACTIVE
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_suspend(self):
        """Test tenant suspension."""
        tenant = Tenant(**self.tenant_data)
        tenant.status = TenantStatus.ACTIVE
        
        tenant.suspend()
        
        assert tenant.status == TenantStatus.SUSPENDED
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_deactivate(self):
        """Test tenant deactivation."""
        tenant = Tenant(**self.tenant_data)
        tenant.status = TenantStatus.ACTIVE
        
        tenant.deactivate()
        
        assert tenant.status == TenantStatus.INACTIVE
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_is_active(self):
        """Test tenant active status check."""
        tenant = Tenant(**self.tenant_data)
        
        # Test different statuses
        tenant.status = TenantStatus.ACTIVE
        assert tenant.is_active() is True
        
        tenant.status = TenantStatus.INACTIVE
        assert tenant.is_active() is False
        
        tenant.status = TenantStatus.SUSPENDED
        assert tenant.is_active() is False
        
        tenant.status = TenantStatus.PENDING_VERIFICATION
        assert tenant.is_active() is False
    
    def test_tenant_update_settings(self):
        """Test updating tenant settings."""
        tenant = Tenant(**self.tenant_data)
        
        new_settings = {
            'timezone': 'America/New_York',
            'date_format': 'MM/DD/YYYY',
            'currency': 'EUR',
            'language': 'en'
        }
        
        tenant.update_settings(new_settings)
        
        assert tenant.settings['timezone'] == 'America/New_York'
        assert tenant.settings['date_format'] == 'MM/DD/YYYY'
        assert tenant.settings['currency'] == 'EUR'
        assert tenant.settings['language'] == 'en'
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_update_settings_partial(self):
        """Test updating tenant settings partially."""
        tenant = Tenant(**self.tenant_data)
        original_timezone = tenant.settings['timezone']
        
        partial_settings = {
            'currency': 'EUR'
        }
        
        tenant.update_settings(partial_settings)
        
        # Updated setting
        assert tenant.settings['currency'] == 'EUR'
        
        # Unchanged settings
        assert tenant.settings['timezone'] == original_timezone
        # Only check for date_format if it was originally set
        if 'date_format' in tenant.settings:
            assert tenant.settings['date_format'] == 'YYYY-MM-DD'
    
    def test_tenant_set_subscription(self):
        """Test setting tenant subscription."""
        tenant = Tenant(**self.tenant_data)
        subscription_id = 'sub_test123'
        
        tenant.set_subscription(subscription_id)
        
        assert tenant.subscription_id == subscription_id
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_remove_subscription(self):
        """Test removing tenant subscription."""
        tenant = Tenant(**self.tenant_data)
        tenant.subscription_id = 'sub_test123'
        
        tenant.remove_subscription()
        
        assert tenant.subscription_id is None
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_has_subscription(self):
        """Test checking if tenant has subscription."""
        tenant = Tenant(**self.tenant_data)
        
        # No subscription
        assert tenant.has_subscription() is False
        
        # With subscription
        tenant.subscription_id = 'sub_test123'
        assert tenant.has_subscription() is True
    
    @mock_aws
    def test_tenant_save(self):
        """Test tenant save to database."""
        tenant = Tenant(**self.tenant_data)
        
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            
            result = tenant.save()
            
            assert result is True
            mock_put.assert_called_once()
            
            # Verify the item structure
            call_args = mock_put.call_args[0]  # Positional arguments
            item = call_args[0]  # First positional argument is the item
            
            assert item['PK'] == f"TENANT#{tenant.tenant_id}"
            assert item['SK'] == 'METADATA'
            assert item['tenant_id'] == tenant.tenant_id
            assert item['name'] == tenant.name
            assert item['status'] == tenant.status.value
    
    @mock_aws
    def test_tenant_get_by_id(self):
        """Test getting tenant by ID."""
        tenant_id = str(uuid.uuid4())
        
        mock_item = {
            'PK': f"TENANT#{tenant_id}",
            'SK': 'METADATA',
            'tenant_id': tenant_id,
            'name': 'Test Company',
            'status': TenantStatus.ACTIVE.value,
            'settings': {'timezone': 'UTC'},
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }
        
        with patch('src.shared.database.db_client.get_item') as mock_get:
            mock_get.return_value = mock_item
            
            tenant = Tenant.get_by_id(tenant_id)
            
            assert tenant is not None
            assert tenant.tenant_id == tenant_id
            assert tenant.name == 'Test Company'
            assert tenant.status == TenantStatus.ACTIVE
    
    @mock_aws
    def test_tenant_get_by_name(self):
        """Test getting tenant by name."""
        mock_items = [{
            'tenant_id': str(uuid.uuid4()),
            'name': 'Test Company',
            'status': TenantStatus.ACTIVE.value,
            'settings': {'timezone': 'UTC'},
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }]
        
        with patch('src.shared.database.db_client.query') as mock_query:
            mock_query.return_value = {'Items': mock_items}
            
            tenant = Tenant.get_by_name('Test Company')
            
            assert tenant is not None
            assert tenant.name == 'Test Company'
    
    @mock_aws
    def test_tenant_get_by_name_not_found(self):
        """Test getting tenant by name when not found."""
        with patch('src.shared.database.db_client.query') as mock_query:
            mock_query.return_value = {'Items': []}
            
            tenant = Tenant.get_by_name('Nonexistent Company')
            
            assert tenant is None
    
    def test_tenant_slug_generation(self):
        """Test tenant slug generation from name."""
        test_cases = [
            ('Test Company', 'test-company'),
            ('ABC Corp Inc.', 'abc-corp-inc'),
            ('My-Startup LLC', 'my-startup-llc'),
            ('Company   123', 'company-123'),
            ('Special & Characters', 'special-characters')
        ]
        
        for name, expected_slug in test_cases:
            tenant_data = self.tenant_data.copy()
            tenant_data['name'] = name
            
            tenant = Tenant(**tenant_data)
            assert tenant.slug == expected_slug
