# tests/unit/auth/models/test_user.py
# Implementado según "Testing Guidelines" - Unit Tests

"""
Unit tests for User model.
Tests user creation, validation, and business logic.
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from moto import mock_aws

from models.user import User, UserStatus, UserRole
from shared.exceptions import ValidationException


class TestUser:
    """Unit tests for User model."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.tenant_id = str(uuid.uuid4())
        self.user_id = str(uuid.uuid4())
        self.user_data = {
            'user_id': self.user_id,
            'tenant_id': self.tenant_id,
            'email': '<EMAIL>',
            'name': 'Test User',
            'status': UserStatus.PENDING_VERIFICATION,
            'role': UserRole.MASTER,
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }

    def test_user_creation(self):
        """Test user creation with valid data."""
        user = User(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            email='<EMAIL>',
            name='Test User',
            role=UserRole.MASTER,
            status=UserStatus.PENDING_VERIFICATION
        )

        assert user.tenant_id == self.tenant_id
        assert user.email == '<EMAIL>'
        assert user.name == 'Test User'
        assert user.role == UserRole.MASTER
        assert user.status == UserStatus.PENDING_VERIFICATION
        assert user.user_id == self.user_id

    def test_user_to_dict(self):
        """Test user serialization to dictionary."""
        user = User(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            email='<EMAIL>',
            name='Test User',
            role=UserRole.MASTER,
            status=UserStatus.ACTIVE
        )

        user_dict = user.to_dict()

        assert user_dict['user_id'] == self.user_id
        assert user_dict['tenant_id'] == self.tenant_id
        assert user_dict['email'] == '<EMAIL>'
        assert user_dict['name'] == 'Test User'
        assert user_dict['role'] == UserRole.MASTER.value
        assert user_dict['status'] == UserStatus.ACTIVE.value

    def test_user_is_active(self):
        """Test user active status check."""
        user = User(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            email='<EMAIL>',
            name='Test User',
            role=UserRole.MASTER,
            status=UserStatus.ACTIVE
        )

        assert user.is_active() is True

        user.status = UserStatus.INACTIVE
        assert user.is_active() is False

    def test_user_role_permissions(self):
        """Test user role permission checks."""
        user = User(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            email='<EMAIL>',
            name='Test User',
            role=UserRole.MASTER,
            status=UserStatus.ACTIVE
        )

        # Test MASTER role
        assert user.is_master() is True
        assert user.is_member() is False
        assert user.can_manage_users() is True
        assert user.can_manage_billing() is True

        # Test MEMBER role
        user.role = UserRole.MEMBER
        assert user.is_master() is False
        assert user.is_member() is True
        assert user.can_manage_users() is False
        assert user.can_manage_billing() is False
        """Test user validation with invalid name."""
        invalid_names = [
            '',
            'a',  # Too short
            'a' * 101,  # Too long
            '123',  # Only numbers
            'Test@User'  # Invalid characters
        ]
        
        for invalid_name in invalid_names:
            user_data = self.user_data.copy()
            user_data['name'] = invalid_name
            
            with pytest.raises(ValidationException):
                User(**user_data)
    
    def test_user_validation_invalid_phone(self):
        """Test user validation with invalid phone."""
        invalid_phones = [
            '123',  # Too short
            'abc123',  # Contains letters
            '************',  # Invalid format
            '+1234567890123456'  # Too long
        ]
        
        for invalid_phone in invalid_phones:
            user_data = self.user_data.copy()
            user_data['phone'] = invalid_phone
            
            with pytest.raises(ValidationException):
                User(**user_data)
    
    def test_user_validation_valid_phone(self):
        """Test user validation with valid phone."""
        valid_phones = [
            '+1234567890',
            '+12345678901',
            '+123456789012345'
        ]
        
        for valid_phone in valid_phones:
            user_data = self.user_data.copy()
            user_data['phone'] = valid_phone
            
            user = User(**user_data)
            assert user.phone == valid_phone
    
    def test_user_to_dict(self):
        """Test user serialization to dictionary."""
        user = User(**self.user_data)
        user_dict = user.to_dict()
        
        assert user_dict['user_id'] == user.user_id
        assert user_dict['tenant_id'] == self.tenant_id
        assert user_dict['email'] == '<EMAIL>'
        assert user_dict['name'] == 'Test User'
        assert user_dict['role'] == UserRole.MASTER.value
        assert user_dict['status'] == UserStatus.PENDING_VERIFICATION.value
        assert user_dict['email_verified'] is False
        assert 'password_hash' not in user_dict  # Should not be exposed
        assert 'created_at' in user_dict
        assert 'updated_at' in user_dict
    
    def test_user_to_dict_exclude_sensitive(self):
        """Test user serialization excludes sensitive data."""
        user = User(**self.user_data)
        user_dict = user.to_dict()
        
        sensitive_fields = [
            'password_hash',
            'verification_token',
            'reset_token',
            'reset_token_expires_at'
        ]
        
        for field in sensitive_fields:
            assert field not in user_dict
    
    def test_user_update_last_login(self):
        """Test updating user last login timestamp."""
        user = User(**self.user_data)
        
        # Initially no last login
        assert user.last_login_at is None
        
        # Update last login with mocked time
        with patch('src.auth.models.user.time.time') as mock_time:
            mock_timestamp = 1234567890
            mock_time.return_value = mock_timestamp

            user.update_last_login()

            assert user.last_login_at == mock_timestamp
            assert user.updated_at == mock_timestamp
    
    def test_user_verify_email(self):
        """Test email verification."""
        user = User(**self.user_data)
        
        # Initially not verified
        assert user.email_verified is False
        assert user.status == UserStatus.PENDING_VERIFICATION
        
        # Verify email
        user.verify_email()
        
        assert user.email_verified is True
        assert user.status == UserStatus.ACTIVE
        assert user.email_verified_at is not None
        assert isinstance(user.email_verified_at, datetime)
    
    def test_user_activate(self):
        """Test user activation."""
        user = User(**self.user_data)
        user.status = UserStatus.INACTIVE
        
        user.activate()
        
        assert user.status == UserStatus.ACTIVE
        assert isinstance(user.updated_at, int)
    
    def test_user_deactivate(self):
        """Test user deactivation."""
        user = User(**self.user_data)
        user.status = UserStatus.ACTIVE
        
        user.deactivate()
        
        assert user.status == UserStatus.INACTIVE
        assert isinstance(user.updated_at, int)
    
    def test_user_suspend(self):
        """Test user suspension."""
        user = User(**self.user_data)
        user.status = UserStatus.ACTIVE
        
        user.suspend()
        
        assert user.status == UserStatus.SUSPENDED
        assert isinstance(user.updated_at, int)
    
    def test_user_is_active(self):
        """Test user active status check."""
        user = User(**self.user_data)
        
        # Test different statuses
        user.status = UserStatus.ACTIVE
        assert user.is_active() is True
        
        user.status = UserStatus.INACTIVE
        assert user.is_active() is False
        
        user.status = UserStatus.SUSPENDED
        assert user.is_active() is False
        
        user.status = UserStatus.PENDING_VERIFICATION
        assert user.is_active() is False
    
    def test_user_can_login(self):
        """Test user login capability check."""
        user = User(**self.user_data)
        
        # Active and verified user can login
        user.status = UserStatus.ACTIVE
        user.email_verified = True
        assert user.can_login() is True
        
        # Inactive user cannot login
        user.status = UserStatus.INACTIVE
        assert user.can_login() is False
        
        # Suspended user cannot login
        user.status = UserStatus.SUSPENDED
        assert user.can_login() is False
        
        # Unverified user cannot login
        user.status = UserStatus.ACTIVE
        user.email_verified = False
        assert user.can_login() is False
    
    def test_user_role_permissions(self):
        """Test user role permission checks."""
        user = User(**self.user_data)
        
        # Test MASTER role
        user.role = UserRole.MASTER
        user.status = UserStatus.ACTIVE  # Activate user for permissions
        assert user.is_master() is True
        assert user.is_member() is False
        assert user.can_manage_users() is True
        assert user.can_manage_billing() is True
        
        # Test MEMBER role
        user.role = UserRole.MEMBER
        assert user.is_master() is False
        assert user.is_member() is True
        assert user.can_manage_users() is False
        assert user.can_manage_billing() is False
    
    @mock_aws
    def test_user_save(self):
        """Test user save to database."""
        user = User(**self.user_data)
        
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            
            result = user.save()
            
            assert result is True
            mock_put.assert_called_once()
            
            # Verify the item structure
            call_args = mock_put.call_args[0]  # Positional arguments
            item = call_args[0]  # First positional argument is the item

            assert item['PK'] == f"USER#{user.user_id}"
            assert item['SK'] == 'METADATA'
            assert item['user_id'] == user.user_id
            assert item['tenant_id'] == user.tenant_id
            assert item['email'] == user.email
    
    @mock_aws
    def test_user_get_by_id(self):
        """Test getting user by ID."""
        user_id = str(uuid.uuid4())
        
        mock_item = {
            'PK': f"USER#{user_id}",
            'SK': 'METADATA',
            'user_id': user_id,
            'tenant_id': self.tenant_id,
            'email': '<EMAIL>',
            'name': 'Test User',
            'role': UserRole.MASTER.value,
            'status': UserStatus.ACTIVE.value,
            'email_verified': True,
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }

        with patch('src.shared.database.db_client.get_item') as mock_get:
            mock_get.return_value = mock_item
            
            user = User.get_by_id(user_id, self.tenant_id)
            
            assert user is not None
            assert user.user_id == user_id
            assert user.tenant_id == self.tenant_id
            assert user.email == '<EMAIL>'
            assert user.role == UserRole.MASTER
            assert user.status == UserStatus.ACTIVE
    
    @mock_aws
    def test_user_get_by_email(self):
        """Test getting user by email."""
        mock_items = [{
            'user_id': str(uuid.uuid4()),
            'tenant_id': self.tenant_id,
            'email': '<EMAIL>',
            'name': 'Test User',
            'role': UserRole.MASTER.value,
            'status': UserStatus.ACTIVE.value,
            'email_verified': True,
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }]
        
        with patch('src.shared.database.db_client.query') as mock_query:
            mock_query.return_value = {'Items': mock_items}
            
            user = User.get_by_email('<EMAIL>', self.tenant_id)
            
            assert user is not None
            assert user.email == '<EMAIL>'
            assert user.tenant_id == self.tenant_id
    
    @mock_aws
    def test_user_get_by_email_not_found(self):
        """Test getting user by email when not found."""
        with patch('src.shared.database.db_client.query') as mock_query:
            mock_query.return_value = {'Items': []}
            
            user = User.get_by_email('<EMAIL>', self.tenant_id)
            
            assert user is None
