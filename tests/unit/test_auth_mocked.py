#!/usr/bin/env python3
# tests/unit/test_auth_mocked.py
# Auth tests with proper mocking to avoid AWS dependencies

"""
Unit tests for shared authentication utilities with proper mocking.
"""

import pytest
import jwt
import time
from unittest.mock import patch, MagicMock

from shared.auth import <PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON>TManager, AuthContext
from shared.exceptions import (
    InvalidTokenException,
    TokenExpiredException,
    WeakPasswordException
)

class TestJWTManagerMocked:
    """Test cases for JWTManager with mocked AWS dependencies."""
    
    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_create_access_token_mocked(self, mock_get_secret):
        """Test access token creation with mocked secret."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"
        
        jwt_manager = JWTManager()
        
        token = jwt_manager.generate_access_token(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            role="MEMBER"
        )
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 50  # JWT tokens are long
    
    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_create_refresh_token_mocked(self, mock_get_secret):
        """Test refresh token creation with mocked secret."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"

        jwt_manager = JWTManager()

        token = jwt_manager.generate_refresh_token(
            user_id="user-123",
            tenant_id="tenant-123"
        )

        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 50

    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_verify_token_valid_mocked(self, mock_get_secret):
        """Test token verification with valid token."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"

        jwt_manager = JWTManager()

        # Create token
        token = jwt_manager.generate_access_token(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            role="MEMBER"
        )

        # Verify token
        payload = jwt_manager.verify_token(token, 'access')

        assert payload is not None
        assert payload["user_id"] == "user-123"
        assert payload["tenant_id"] == "tenant-123"
        assert payload["email"] == "<EMAIL>"
        assert payload["role"] == "MEMBER"

    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_verify_token_invalid_mocked(self, mock_get_secret):
        """Test token verification with invalid token."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"

        jwt_manager = JWTManager()

        with pytest.raises(InvalidTokenException):
            jwt_manager.verify_token("invalid-token", 'access')

    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_extract_user_context_mocked(self, mock_get_secret):
        """Test user context extraction from token."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"

        jwt_manager = JWTManager()

        # Create token
        token = jwt_manager.generate_access_token(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            role="MEMBER"
        )

        # Extract context
        context = jwt_manager.extract_user_context(token)

        assert context is not None
        assert isinstance(context, AuthContext)
        assert context.user_id == "user-123"
        assert context.tenant_id == "tenant-123"
        assert context.email == "<EMAIL>"
        assert context.role == "MEMBER"

    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_extract_user_context_invalid_token(self, mock_get_secret):
        """Test user context extraction with invalid token."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"

        jwt_manager = JWTManager()

        # Extract context from invalid token
        context = jwt_manager.extract_user_context("invalid-token")

        assert context is None

class TestAuthContextMocked:
    """Test cases for AuthContext."""
    
    def test_auth_context_initialization(self):
        """Test AuthContext initialization."""
        context = AuthContext(
            user_id="user-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MEMBER",
            token_payload={"jti": "token-123"}
        )
        
        assert context.user_id == "user-123"
        assert context.tenant_id == "tenant-123"
        assert context.email == "<EMAIL>"
        assert context.role == "MEMBER"
        assert context.is_authenticated is True
    
    def test_has_role_member(self):
        """Test role checking for MEMBER."""
        context = AuthContext(
            user_id="user-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MEMBER",
            token_payload={"jti": "token-123"}
        )

        assert context.has_role("MEMBER") is True
        assert context.has_role("MASTER") is False  # MEMBER doesn't have MASTER permissions

    def test_has_role_master(self):
        """Test role checking for MASTER."""
        context = AuthContext(
            user_id="user-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MASTER",
            token_payload={"jti": "token-123"}
        )

        assert context.has_role("MEMBER") is True  # MASTER has MEMBER permissions
        assert context.has_role("MASTER") is True
    
    def test_can_access_tenant(self):
        """Test tenant access checking."""
        context = AuthContext(
            user_id="user-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MEMBER",
            token_payload={"jti": "token-123"}
        )

        assert context.can_access_tenant("tenant-123") is True
        assert context.can_access_tenant("other-tenant") is False

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
