# tests/unit/agent/test_hybrid_router.py
# Unit tests for agent hybrid router

"""
Unit tests for the agent hybrid router service.
Tests message routing to different agents (Feedo, Forecaster).
"""

import pytest
import requests
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from services.agent.src.services.hybrid_router import HybridRouter
from shared.exceptions import DatabaseException


class TestHybridRouter:
    """Test cases for HybridRouter."""
    
    @pytest.fixture
    def router(self):
        """Create a HybridRouter instance for testing."""
        with patch('services.agent.src.services.hybrid_router.get_agent_config'):
            with patch('services.agent.src.services.hybrid_router.DynamoDBClient'):
                return HybridRouter()
    
    @pytest.fixture
    def mock_agent_config(self):
        """Mock agent configuration."""
        return {
            'agent_id': 'feedo',
            'name': 'Feedo Agent',
            'status': 'active',
            'webhook_url': 'https://n8n.example.com/webhook/feedo',
            'document_webhook_url': 'https://n8n.example.com/webhook/feedo/docs',
            'bearer_token': 'test-token-123',
            'input_parameters': {
                'api_version': '1.0',
                'format': 'json'
            }
        }
    
    @pytest.fixture
    def mock_message_data(self):
        """Mock message data."""
        return {
            'message_id': 'msg-123',
            'content': 'I need help with logistics planning',
            'message_type': 'text',
            'sender_id': 'user-456',
            'timestamp': datetime.utcnow().isoformat()
        }
    
    @pytest.fixture
    def mock_conversation_context(self):
        """Mock conversation context."""
        return {
            'conversation_id': 'conv-789',
            'tenant_id': 'tenant-123',
            'user_context': {
                'user_id': 'user-456',
                'user_name': 'John Doe'
            }
        }
    
    def test_determine_agent_routing_strategy_feedo_keywords(self, router):
        """Test routing strategy determination for Feedo keywords."""
        message_content = "I need help with logistics and shipping"
        
        strategy = router.determine_agent_routing_strategy(
            conversation_id="conv-123",
            message_content=message_content,
            user_context={},
            tenant_id="tenant-456"
        )
        
        assert strategy == 'feedo'
    
    def test_determine_agent_routing_strategy_forecaster_keywords(self, router):
        """Test routing strategy determination for Forecaster keywords."""
        message_content = "I need demand forecasting and analytics"
        
        strategy = router.determine_agent_routing_strategy(
            conversation_id="conv-123",
            message_content=message_content,
            user_context={},
            tenant_id="tenant-456"
        )
        
        assert strategy == 'forecaster'
    
    def test_determine_agent_routing_strategy_human_escalation(self, router):
        """Test routing strategy determination for human escalation."""
        message_content = "I need to speak with a human agent"
        
        strategy = router.determine_agent_routing_strategy(
            conversation_id="conv-123",
            message_content=message_content,
            user_context={},
            tenant_id="tenant-456"
        )
        
        assert strategy == 'human'
    
    def test_determine_agent_routing_strategy_default_feedo(self, router):
        """Test default routing strategy is Feedo."""
        message_content = "Hello, how are you?"
        
        strategy = router.determine_agent_routing_strategy(
            conversation_id="conv-123",
            message_content=message_content,
            user_context={},
            tenant_id="tenant-456"
        )
        
        assert strategy == 'feedo'
    
    @patch('services.agent.src.services.hybrid_router.requests.post')
    def test_route_to_feedo_success(self, mock_post, router, mock_agent_config, 
                                   mock_message_data, mock_conversation_context):
        """Test successful routing to Feedo agent."""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'status': 'success',
            'response': 'I can help you with logistics planning'
        }
        mock_post.return_value = mock_response
        
        # Execute
        success, result, error = router._route_to_feedo(
            mock_message_data,
            mock_agent_config,
            mock_conversation_context
        )
        
        # Verify
        assert success is True
        assert result['agent_type'] == 'feedo'
        assert result['routing_method'] == 'webhook'
        assert error is None
        
        # Verify HTTP call
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == 'https://n8n.example.com/webhook/feedo'
        assert call_args[1]['headers']['Authorization'] == 'Bearer test-token-123'
    
    @patch('services.agent.src.services.hybrid_router.requests.post')
    def test_route_to_feedo_with_document(self, mock_post, router, mock_agent_config, 
                                         mock_conversation_context):
        """Test routing to Feedo agent with document."""
        # Mock document message
        document_message = {
            'message_id': 'msg-123',
            'content': 'Please analyze this document',
            'message_type': 'document',
            'attachments': [{'filename': 'logistics_data.pdf'}]
        }
        
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'status': 'success'}
        mock_post.return_value = mock_response
        
        # Execute
        success, result, error = router._route_to_feedo(
            document_message,
            mock_agent_config,
            mock_conversation_context
        )
        
        # Verify
        assert success is True
        
        # Verify document webhook URL was used
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == 'https://n8n.example.com/webhook/feedo/docs'
    
    @patch('services.agent.src.services.hybrid_router.requests.post')
    def test_route_to_feedo_webhook_failure(self, mock_post, router, mock_agent_config, 
                                           mock_message_data, mock_conversation_context):
        """Test handling of webhook failure."""
        # Mock failed HTTP response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = 'Internal Server Error'
        mock_post.return_value = mock_response
        
        # Execute
        success, result, error = router._route_to_feedo(
            mock_message_data,
            mock_agent_config,
            mock_conversation_context
        )
        
        # Verify
        assert success is False
        assert result == {}
        assert 'webhook failed: 500' in error
    
    @patch('services.agent.src.services.hybrid_router.requests.post')
    def test_route_to_feedo_connection_error(self, mock_post, router, mock_agent_config, 
                                            mock_message_data, mock_conversation_context):
        """Test handling of connection error."""
        # Mock connection error
        mock_post.side_effect = requests.ConnectionError("Connection failed")
        
        # Execute
        success, result, error = router._route_to_feedo(
            mock_message_data,
            mock_agent_config,
            mock_conversation_context
        )
        
        # Verify
        assert success is False
        assert result == {}
        assert 'Connection failed' in error
    
    def test_route_to_feedo_missing_webhook_url(self, router, mock_message_data, 
                                               mock_conversation_context):
        """Test handling of missing webhook URL."""
        # Agent config without webhook URL
        agent_config = {
            'agent_id': 'feedo',
            'status': 'active'
            # Missing webhook_url
        }
        
        # Execute
        success, result, error = router._route_to_feedo(
            mock_message_data,
            agent_config,
            mock_conversation_context
        )
        
        # Verify
        assert success is False
        assert result == {}
        assert 'webhook URL not configured' in error
    
    @patch('services.agent.src.services.hybrid_router.requests.post')
    def test_route_to_forecaster_success(self, mock_post, router, mock_conversation_context):
        """Test successful routing to Forecaster agent."""
        # Mock Forecaster agent config
        forecaster_config = {
            'agent_id': 'forecaster',
            'status': 'active',
            'forecaster_webhook_url': 'https://n8n.example.com/webhook/forecaster',
            'bearer_token': 'forecaster-token-456'
        }
        
        # Mock message data
        message_data = {
            'message_id': 'msg-123',
            'content': 'I need demand forecasting',
            'message_type': 'text'
        }
        
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'status': 'success',
            'forecast_data': {'demand': 1000}
        }
        mock_post.return_value = mock_response
        
        # Execute
        success, result, error = router._route_to_forecaster(
            message_data,
            forecaster_config,
            mock_conversation_context
        )
        
        # Verify
        assert success is True
        assert result['agent_type'] == 'forecaster'
        assert result['routing_method'] == 'webhook'
        assert error is None
        
        # Verify HTTP call
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == 'https://n8n.example.com/webhook/forecaster'
    
    def test_get_agent_config_validates_status(self, router):
        """Test that agent config validation checks status."""
        with patch.object(router.db_client, 'get_item') as mock_get:
            # Mock inactive agent
            mock_get.return_value = {
                'agent_id': 'feedo',
                'status': 'inactive',
                'webhook_url': 'https://example.com/webhook'
            }
            
            # Execute
            result = router._get_agent_config('feedo', 'tenant-123')
            
            # Verify
            assert result is None
    
    def test_get_agent_config_validates_webhook_url(self, router):
        """Test that agent config validation checks webhook URL."""
        with patch.object(router.db_client, 'get_item') as mock_get:
            # Mock agent without webhook URL
            mock_get.return_value = {
                'agent_id': 'feedo',
                'status': 'active',
                'webhook_url': ''  # Empty webhook URL
            }
            
            # Execute
            result = router._get_agent_config('feedo', 'tenant-123')
            
            # Verify
            assert result is None
    
    def test_get_agent_config_success(self, router):
        """Test successful agent config retrieval."""
        with patch.object(router.db_client, 'get_item') as mock_get:
            # Mock valid agent
            mock_agent = {
                'agent_id': 'feedo',
                'status': 'active',
                'webhook_url': 'https://example.com/webhook'
            }
            mock_get.return_value = mock_agent
            
            # Execute
            result = router._get_agent_config('feedo', 'tenant-123')
            
            # Verify
            assert result == mock_agent


if __name__ == '__main__':
    pytest.main([__file__])
