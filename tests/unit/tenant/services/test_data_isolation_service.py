# tests/unit/tenant/services/test_data_isolation_service.py

"""
Unit tests for DataIsolationService.
Tests tenant data isolation, security validation, and access control.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from services.data_isolation_service import TenantDataIsolationService
from shared.auth import AuthContext
from shared.exceptions import (
    SecurityException,
    ValidationException,
    AuthorizationException
)


class TestDataIsolationService:
    """Test cases for TenantDataIsolationService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.isolation_service = TenantDataIsolationService()
        self.auth_context = AuthContext(
            user_id='user_123',
            tenant_id='tenant_123',
            email='<EMAIL>',
            role='MEMBER',
            token_id='token_123'
        )
    
    def test_validate_tenant_access_valid(self):
        """Test tenant access validation with valid context."""
        # Should not raise exception
        self.isolation_service.validate_tenant_access(
            self.auth_context,
            'tenant_123'
        )
    
    def test_validate_tenant_access_invalid_tenant(self):
        """Test tenant access validation with invalid tenant."""
        with pytest.raises(AuthorizationException) as exc_info:
            self.isolation_service.validate_tenant_access(
                self.auth_context,
                'different_tenant_456'
            )
        
        assert 'Access denied: User cannot access data from different tenant' in str(exc_info.value)
    
    def test_validate_tenant_access_no_context(self):
        """Test tenant access validation with no auth context."""
        with pytest.raises(AuthorizationException) as exc_info:
            self.isolation_service.validate_tenant_access(
                None,
                'tenant_123'
            )
        
        assert 'Authentication context required' in str(exc_info.value)
    
    def test_enforce_data_isolation_valid_key(self):
        """Test data isolation enforcement with valid tenant key."""
        data_key = {
            'PK': 'TENANT#tenant_123',
            'SK': 'USER#user_456'
        }
        
        # Should not raise exception
        self.isolation_service.enforce_data_isolation(
            self.auth_context,
            data_key
        )
    
    def test_enforce_data_isolation_invalid_tenant_in_pk(self):
        """Test data isolation enforcement with wrong tenant in PK."""
        data_key = {
            'PK': 'TENANT#different_tenant_456',
            'SK': 'USER#user_456'
        }
        
        with pytest.raises(SecurityException) as exc_info:
            self.isolation_service.enforce_data_isolation(
                self.auth_context,
                data_key
            )
        
        assert 'Data isolation violation' in str(exc_info.value)
    
    def test_enforce_data_isolation_no_tenant_in_pk(self):
        """Test data isolation enforcement with no tenant in PK."""
        data_key = {
            'PK': 'GLOBAL#config',
            'SK': 'SETTING#theme'
        }
        
        # Global data should be allowed
        self.isolation_service.enforce_data_isolation(
            self.auth_context,
            data_key
        )
    
    def test_enforce_data_isolation_invalid_key_format(self):
        """Test data isolation enforcement with invalid key format."""
        data_key = {
            'PK': 'invalid_format',
            'SK': 'USER#user_456'
        }
        
        with pytest.raises(ValidationException) as exc_info:
            self.isolation_service.enforce_data_isolation(
                self.auth_context,
                data_key
            )
        
        assert 'Invalid data key format' in str(exc_info.value)
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_query_success(self, mock_db_client):
        """Test secure query execution with proper isolation."""
        # Setup mock
        mock_db_client.query.return_value = {
            'Items': [
                {
                    'PK': 'TENANT#tenant_123',
                    'SK': 'USER#user_456',
                    'user_id': 'user_456',
                    'name': 'Test User'
                }
            ]
        }
        
        # Execute
        result = self.isolation_service.secure_query(
            auth_context=self.auth_context,
            pk='TENANT#tenant_123',
            sk_condition='begins_with(SK, :sk)',
            expression_attribute_values={':sk': 'USER#'}
        )
        
        # Verify
        assert len(result['Items']) == 1
        assert result['Items'][0]['user_id'] == 'user_456'
        
        # Verify query was called with tenant isolation
        mock_db_client.query.assert_called_once()
        call_args = mock_db_client.query.call_args[0]
        assert 'TENANT#tenant_123' in call_args[0]
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_query_cross_tenant_data_filtered(self, mock_db_client):
        """Test secure query filters out cross-tenant data."""
        # Setup mock with mixed tenant data
        mock_db_client.query.return_value = {
            'Items': [
                {
                    'PK': 'TENANT#tenant_123',
                    'SK': 'USER#user_456',
                    'user_id': 'user_456',
                    'name': 'Valid User'
                },
                {
                    'PK': 'TENANT#different_tenant_789',
                    'SK': 'USER#user_789',
                    'user_id': 'user_789',
                    'name': 'Invalid User'
                }
            ]
        }
        
        # Execute
        result = self.isolation_service.secure_query(
            auth_context=self.auth_context,
            pk='TENANT#tenant_123',
            sk_condition='begins_with(SK, :sk)',
            expression_attribute_values={':sk': 'USER#'}
        )
        
        # Verify only valid tenant data is returned
        assert len(result['Items']) == 1
        assert result['Items'][0]['user_id'] == 'user_456'
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_get_item_success(self, mock_db_client):
        """Test secure get item with proper isolation."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'PK': 'TENANT#tenant_123',
            'SK': 'USER#user_456',
            'user_id': 'user_456',
            'name': 'Test User'
        }
        
        # Execute
        result = self.isolation_service.secure_get_item(
            auth_context=self.auth_context,
            pk='TENANT#tenant_123',
            sk='USER#user_456'
        )
        
        # Verify
        assert result['user_id'] == 'user_456'
        assert result['name'] == 'Test User'
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_get_item_cross_tenant_access_denied(self, mock_db_client):
        """Test secure get item denies cross-tenant access."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'PK': 'TENANT#different_tenant_789',
            'SK': 'USER#user_456',
            'user_id': 'user_456',
            'name': 'Test User'
        }
        
        # Execute and verify exception
        with pytest.raises(SecurityException) as exc_info:
            self.isolation_service.secure_get_item(
                auth_context=self.auth_context,
                pk='TENANT#different_tenant_789',
                sk='USER#user_456'
            )
        
        assert 'Cross-tenant data access denied' in str(exc_info.value)
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_put_item_success(self, mock_db_client):
        """Test secure put item with proper isolation."""
        # Setup mock
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        item_data = {
            'PK': 'TENANT#tenant_123',
            'SK': 'USER#user_456',
            'user_id': 'user_456',
            'name': 'Test User'
        }
        
        # Execute
        result = self.isolation_service.secure_put_item(
            auth_context=self.auth_context,
            item=item_data
        )
        
        # Verify
        assert result['success'] is True
        
        # Verify item was enriched with tenant metadata
        mock_db_client.put_item.assert_called_once()
        call_args = mock_db_client.put_item.call_args[0]
        saved_item = call_args[0]
        assert saved_item['tenant_id'] == 'tenant_123'
        assert 'created_at' in saved_item
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_put_item_cross_tenant_denied(self, mock_db_client):
        """Test secure put item denies cross-tenant writes."""
        item_data = {
            'PK': 'TENANT#different_tenant_789',
            'SK': 'USER#user_456',
            'user_id': 'user_456',
            'name': 'Test User'
        }
        
        # Execute and verify exception
        with pytest.raises(SecurityException) as exc_info:
            self.isolation_service.secure_put_item(
                auth_context=self.auth_context,
                item=item_data
            )
        
        assert 'Cross-tenant data write denied' in str(exc_info.value)
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_delete_item_success(self, mock_db_client):
        """Test secure delete item with proper isolation."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'PK': 'TENANT#tenant_123',
            'SK': 'USER#user_456',
            'user_id': 'user_456'
        }
        mock_db_client.delete_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        # Execute
        result = self.isolation_service.secure_delete_item(
            auth_context=self.auth_context,
            pk='TENANT#tenant_123',
            sk='USER#user_456'
        )
        
        # Verify
        assert result['success'] is True
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_secure_delete_item_cross_tenant_denied(self, mock_db_client):
        """Test secure delete item denies cross-tenant deletes."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'PK': 'TENANT#different_tenant_789',
            'SK': 'USER#user_456',
            'user_id': 'user_456'
        }
        
        # Execute and verify exception
        with pytest.raises(SecurityException) as exc_info:
            self.isolation_service.secure_delete_item(
                auth_context=self.auth_context,
                pk='TENANT#different_tenant_789',
                sk='USER#user_456'
            )
        
        assert 'Cross-tenant data delete denied' in str(exc_info.value)
    
    def test_extract_tenant_from_key_valid(self):
        """Test tenant extraction from valid key."""
        tenant_id = self.isolation_service._extract_tenant_from_key('TENANT#tenant_123')
        assert tenant_id == 'tenant_123'
    
    def test_extract_tenant_from_key_global(self):
        """Test tenant extraction from global key."""
        tenant_id = self.isolation_service._extract_tenant_from_key('GLOBAL#config')
        assert tenant_id is None
    
    def test_extract_tenant_from_key_invalid_format(self):
        """Test tenant extraction from invalid key format."""
        tenant_id = self.isolation_service._extract_tenant_from_key('invalid_format')
        assert tenant_id is None
    
    def test_enrich_item_with_tenant_metadata(self):
        """Test item enrichment with tenant metadata."""
        item = {
            'PK': 'TENANT#tenant_123',
            'SK': 'USER#user_456',
            'user_id': 'user_456'
        }
        
        enriched_item = self.isolation_service._enrich_item_with_tenant_metadata(
            item,
            self.auth_context
        )
        
        assert enriched_item['tenant_id'] == 'tenant_123'
        assert enriched_item['created_by'] == 'user_123'
        assert 'created_at' in enriched_item
        assert 'updated_at' in enriched_item
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_audit_data_access(self, mock_db_client):
        """Test data access auditing."""
        # Setup mock
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        # Execute
        self.isolation_service._audit_data_access(
            auth_context=self.auth_context,
            operation='READ',
            resource_key='TENANT#tenant_123#USER#user_456',
            success=True
        )
        
        # Verify audit log was created
        mock_db_client.put_item.assert_called_once()
        call_args = mock_db_client.put_item.call_args[0]
        audit_item = call_args[0]
        
        assert audit_item['PK'].startswith('AUDIT#')
        assert audit_item['tenant_id'] == 'tenant_123'
        assert audit_item['user_id'] == 'user_123'
        assert audit_item['operation'] == 'READ'
        assert audit_item['success'] is True
    
    def test_validate_data_key_format_valid(self):
        """Test data key format validation with valid key."""
        data_key = {
            'PK': 'TENANT#tenant_123',
            'SK': 'USER#user_456'
        }
        
        # Should not raise exception
        self.isolation_service._validate_data_key_format(data_key)
    
    def test_validate_data_key_format_missing_pk(self):
        """Test data key format validation with missing PK."""
        data_key = {
            'SK': 'USER#user_456'
        }
        
        with pytest.raises(ValidationException) as exc_info:
            self.isolation_service._validate_data_key_format(data_key)
        
        assert 'Missing required key' in str(exc_info.value)
    
    def test_validate_data_key_format_missing_sk(self):
        """Test data key format validation with missing SK."""
        data_key = {
            'PK': 'TENANT#tenant_123'
        }
        
        with pytest.raises(ValidationException) as exc_info:
            self.isolation_service._validate_data_key_format(data_key)
        
        assert 'Missing required key' in str(exc_info.value)
    
    @patch('src.tenant.services.data_isolation_service.db_client')
    def test_get_tenant_data_summary(self, mock_db_client):
        """Test getting tenant data summary."""
        # Setup mock
        mock_db_client.get_item.side_effect = [
            {  # Usage data
                'usage_metrics': {'conversations': 150, 'storage_mb': 500},
                'limits': {'max_conversations': 1000, 'max_storage_gb': 10}
            },
            {  # Config data
                'plan_config': {'plan_type': 'BASIC', 'features': ['chat', 'analytics']}
            }
        ]
        
        # Execute
        result = self.isolation_service.get_tenant_data_summary(self.auth_context)
        
        # Verify
        assert result['tenant_id'] == 'tenant_123'
        assert result['data_isolation_status'] == 'active'
        assert 'usage_metrics' in result
        assert 'limits' in result
        assert 'plan_config' in result
        assert result['usage_metrics']['conversations'] == 150
    
    def test_check_tenant_isolation_compliance_success(self):
        """Test tenant isolation compliance check success."""
        test_data = [
            {
                'PK': 'TENANT#tenant_123',
                'SK': 'USER#user_456',
                'tenant_id': 'tenant_123'
            },
            {
                'PK': 'TENANT#tenant_123',
                'SK': 'CONFIG#settings',
                'tenant_id': 'tenant_123'
            }
        ]
        
        # Execute
        result = self.isolation_service.check_tenant_isolation_compliance(
            self.auth_context,
            test_data
        )
        
        # Verify
        assert result['compliant'] is True
        assert result['violations'] == 0
        assert len(result['valid_items']) == 2
    
    def test_check_tenant_isolation_compliance_violations(self):
        """Test tenant isolation compliance check with violations."""
        test_data = [
            {
                'PK': 'TENANT#tenant_123',
                'SK': 'USER#user_456',
                'tenant_id': 'tenant_123'
            },
            {
                'PK': 'TENANT#different_tenant_789',
                'SK': 'USER#user_789',
                'tenant_id': 'different_tenant_789'
            }
        ]
        
        # Execute
        result = self.isolation_service.check_tenant_isolation_compliance(
            self.auth_context,
            test_data
        )
        
        # Verify
        assert result['compliant'] is False
        assert result['violations'] == 1
        assert len(result['valid_items']) == 1
        assert len(result['violation_details']) == 1
