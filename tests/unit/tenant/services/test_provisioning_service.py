# tests/unit/tenant/services/test_provisioning_service.py

"""
Unit tests for TenantProvisioningService.
Tests tenant creation, configuration, and provisioning logic.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from services.provisioning_service import TenantProvisioningService
from models.tenant import Tenant, TenantStatus
from models.plan import Plan
from shared.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    TenantException
)


class TestTenantProvisioningService:
    """Test cases for TenantProvisioningService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.provisioning_service = TenantProvisioningService()
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_create_tenant_success(self, mock_db_client):
        """Test successful tenant creation."""
        # Setup mocks
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        mock_db_client.get_item.return_value = None  # Tenant doesn't exist
        
        # Test data
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
        
        # Execute
        result = await self.provisioning_service.create_tenant(**tenant_data)
        
        # Verify
        assert result['success'] is True
        assert 'tenant_id' in result
        assert result['tenant']['name'] == 'Test Company'
        assert result['tenant']['slug'] == 'test-company'
        assert result['tenant']['status'] == TenantStatus.ACTIVE.value
        
        # Verify database calls
        mock_db_client.put_item.assert_called()
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_create_tenant_duplicate_slug(self, mock_db_client):
        """Test tenant creation with duplicate slug."""
        # Setup mock to return existing tenant
        mock_db_client.get_item.return_value = {
            'tenant_id': 'existing_tenant_123',
            'slug': 'test-company'
        }
        
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
        
        # Execute and verify exception
        with pytest.raises(ValidationException) as exc_info:
            await self.provisioning_service.create_tenant(**tenant_data)
        
        assert 'Tenant slug already exists' in str(exc_info.value)
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_create_tenant_invalid_plan(self, mock_db_client):
        """Test tenant creation with invalid plan."""
        # Setup mocks
        mock_db_client.get_item.side_effect = [
            None,  # Tenant doesn't exist
            None   # Plan doesn't exist
        ]
        
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'invalid_plan_123'
        }
        
        # Execute and verify exception
        with pytest.raises(ValidationException) as exc_info:
            await self.provisioning_service.create_tenant(**tenant_data)
        
        assert 'Invalid plan ID' in str(exc_info.value)
    
    def test_generate_tenant_slug_basic(self):
        """Test basic tenant slug generation."""
        slug = self.provisioning_service._generate_tenant_slug('Test Company')
        assert slug == 'test-company'
    
    def test_generate_tenant_slug_special_chars(self):
        """Test tenant slug generation with special characters."""
        slug = self.provisioning_service._generate_tenant_slug('Test & Company, Inc.')
        assert slug == 'test-company-inc'
    
    def test_generate_tenant_slug_numbers(self):
        """Test tenant slug generation with numbers."""
        slug = self.provisioning_service._generate_tenant_slug('Company 123 LLC')
        assert slug == 'company-123-llc'
    
    def test_generate_tenant_slug_unicode(self):
        """Test tenant slug generation with unicode characters."""
        slug = self.provisioning_service._generate_tenant_slug('Compañía Española')
        assert slug == 'compania-espanola'
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_provision_tenant_resources_success(self, mock_db_client):
        """Test successful tenant resource provisioning."""
        # Setup mock
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        tenant = Tenant(
            tenant_id='tenant_123',
            name='Test Company',
            slug='test-company',
            admin_email='<EMAIL>'
        )
        
        # Execute
        result = await self.provisioning_service._provision_tenant_resources(tenant)
        
        # Verify
        assert result['success'] is True
        assert 'resources_created' in result
        assert len(result['resources_created']) > 0
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_setup_tenant_configuration_success(self, mock_db_client):
        """Test successful tenant configuration setup."""
        # Setup mock
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        tenant = Tenant(
            tenant_id='tenant_123',
            name='Test Company',
            slug='test-company'
        )
        
        plan = Plan(
            plan_id='plan_123',
            name='Basic Plan',
            monthly_price=29.99
        )
        
        # Execute
        result = await self.provisioning_service._setup_tenant_configuration(tenant, plan)
        
        # Verify
        assert result['success'] is True
        assert 'configuration' in result
        assert result['configuration']['plan_id'] == 'plan_123'
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_create_admin_user_success(self, mock_db_client):
        """Test successful admin user creation."""
        # Setup mock
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        tenant = Tenant(
            tenant_id='tenant_123',
            name='Test Company',
            slug='test-company'
        )
        
        # Execute
        result = await self.provisioning_service._create_admin_user(
            tenant=tenant,
            admin_email='<EMAIL>',
            admin_name='Admin User'
        )
        
        # Verify
        assert result['success'] is True
        assert 'user_id' in result
        assert result['user']['email'] == '<EMAIL>'
        assert result['user']['name'] == 'Admin User'
        assert result['user']['role'] == 'MASTER'
    
    def test_validate_tenant_data_valid(self):
        """Test tenant data validation with valid data."""
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
        
        # Should not raise exception
        self.provisioning_service._validate_tenant_data(tenant_data)
    
    def test_validate_tenant_data_missing_name(self):
        """Test tenant data validation with missing name."""
        tenant_data = {
            'slug': 'test-company',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
        
        with pytest.raises(ValidationException) as exc_info:
            self.provisioning_service._validate_tenant_data(tenant_data)
        
        assert 'Tenant name is required' in str(exc_info.value)
    
    def test_validate_tenant_data_invalid_email(self):
        """Test tenant data validation with invalid email."""
        tenant_data = {
            'name': 'Test Company',
            'slug': 'test-company',
            'admin_email': 'invalid-email',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
        
        with pytest.raises(ValidationException) as exc_info:
            self.provisioning_service._validate_tenant_data(tenant_data)
        
        assert 'Invalid admin email format' in str(exc_info.value)
    
    def test_validate_tenant_data_invalid_slug(self):
        """Test tenant data validation with invalid slug."""
        tenant_data = {
            'name': 'Test Company',
            'slug': 'invalid slug with spaces',
            'admin_email': '<EMAIL>',
            'admin_name': 'Admin User',
            'plan_id': 'plan_basic_123'
        }
        
        with pytest.raises(ValidationException) as exc_info:
            self.provisioning_service._validate_tenant_data(tenant_data)
        
        assert 'Invalid tenant slug format' in str(exc_info.value)
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_suspend_tenant_success(self, mock_db_client):
        """Test successful tenant suspension."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'tenant_id': 'tenant_123',
            'name': 'Test Company',
            'status': TenantStatus.ACTIVE.value
        }
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        # Execute
        result = await self.provisioning_service.suspend_tenant(
            tenant_id='tenant_123',
            reason='Payment failure'
        )
        
        # Verify
        assert result['success'] is True
        assert result['tenant']['status'] == TenantStatus.SUSPENDED.value
        assert result['suspension_reason'] == 'Payment failure'
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_reactivate_tenant_success(self, mock_db_client):
        """Test successful tenant reactivation."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'tenant_id': 'tenant_123',
            'name': 'Test Company',
            'status': TenantStatus.SUSPENDED.value
        }
        mock_db_client.put_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        # Execute
        result = await self.provisioning_service.reactivate_tenant('tenant_123')
        
        # Verify
        assert result['success'] is True
        assert result['tenant']['status'] == TenantStatus.ACTIVE.value
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_get_tenant_status_success(self, mock_db_client):
        """Test successful tenant status retrieval."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'tenant_id': 'tenant_123',
            'name': 'Test Company',
            'status': TenantStatus.ACTIVE.value,
            'created_at': datetime.utcnow().isoformat()
        }
        
        # Execute
        result = await self.provisioning_service.get_tenant_status('tenant_123')
        
        # Verify
        assert result['tenant_id'] == 'tenant_123'
        assert result['status'] == TenantStatus.ACTIVE.value
        assert 'created_at' in result
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_get_tenant_status_not_found(self, mock_db_client):
        """Test tenant status retrieval for non-existent tenant."""
        # Setup mock
        mock_db_client.get_item.return_value = None
        
        # Execute and verify exception
        with pytest.raises(ResourceNotFoundException) as exc_info:
            await self.provisioning_service.get_tenant_status('nonexistent_tenant')
        
        assert 'Tenant not found' in str(exc_info.value)
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_delete_tenant_success(self, mock_db_client):
        """Test successful tenant deletion."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'tenant_id': 'tenant_123',
            'name': 'Test Company',
            'status': TenantStatus.SUSPENDED.value
        }
        mock_db_client.delete_item.return_value = {"ResponseMetadata": {"HTTPStatusCode": 200}}
        
        # Execute
        result = await self.provisioning_service.delete_tenant('tenant_123')
        
        # Verify
        assert result['success'] is True
        assert result['tenant_id'] == 'tenant_123'
        assert 'deleted_at' in result
    
    @patch('src.tenant.services.provisioning_service.db_client')
    async def test_delete_tenant_active_tenant(self, mock_db_client):
        """Test tenant deletion for active tenant (should fail)."""
        # Setup mock
        mock_db_client.get_item.return_value = {
            'tenant_id': 'tenant_123',
            'name': 'Test Company',
            'status': TenantStatus.ACTIVE.value
        }
        
        # Execute and verify exception
        with pytest.raises(TenantException) as exc_info:
            await self.provisioning_service.delete_tenant('tenant_123')
        
        assert 'Cannot delete active tenant' in str(exc_info.value)
    
    def test_generate_tenant_id(self):
        """Test tenant ID generation."""
        tenant_id = self.provisioning_service._generate_tenant_id()
        
        assert tenant_id.startswith('tenant_')
        assert len(tenant_id) > 10  # Should be reasonably long
        
        # Generate multiple IDs to ensure uniqueness
        ids = [self.provisioning_service._generate_tenant_id() for _ in range(10)]
        assert len(set(ids)) == 10  # All should be unique
