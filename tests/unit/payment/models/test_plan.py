# tests/unit/payment/models/test_plan.py
# Implementado según "Testing Guidelines" - Unit Tests

"""
Unit tests for Plan model.
Tests plan creation, validation, and business logic.
"""

import pytest
import uuid
from datetime import datetime
from decimal import Decimal
from unittest.mock import Mock, patch
from moto import mock_aws

from models.plan import Plan, PlanType, PlanStatus
from models.subscription import BillingInterval
from shared.exceptions import ValidationException, BusinessLogicException


class TestPlan:
    """Unit tests for Plan model."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.plan_id = str(uuid.uuid4())

        # Plan test data
        self.plan_data = {
            'plan_id': self.plan_id,
            'name': 'Basic Plan',
            'plan_type': PlanType.BASIC,
            'monthly_price': Decimal('29.99'),
            'yearly_price': Decimal('299.99'),
            'status': PlanStatus.ACTIVE,
            'features': {
                'max_users': 5,
                'max_projects': 10,
                'storage_gb': 100,
                'api_calls': 1000
            },
            'max_users': 10,
            'max_storage_gb': 100,
            'max_api_calls': 1000,
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': int(datetime.utcnow().timestamp())
        }
    
    def test_plan_creation(self):
        """Test plan creation with valid data."""
        plan = Plan(
            plan_id=self.plan_id,
            name='Basic Plan',
            plan_type=PlanType.BASIC,
            monthly_price=Decimal('29.99'),
            yearly_price=Decimal('299.99'),
            status=PlanStatus.ACTIVE
        )

        assert plan.name == 'Basic Plan'
        assert plan.plan_type == PlanType.BASIC
        assert plan.monthly_price == Decimal('29.99')
        assert plan.yearly_price == Decimal('299.99')
        assert plan.status == PlanStatus.ACTIVE
        assert plan.plan_id == self.plan_id
    
    def test_plan_to_dict(self):
        """Test plan serialization to dictionary."""
        plan = Plan(
            plan_id=self.plan_id,
            name='Basic Plan',
            plan_type=PlanType.BASIC,
            monthly_price=Decimal('29.99'),
            status=PlanStatus.ACTIVE
        )

        plan_dict = plan.to_dict()

        assert plan_dict['plan_id'] == self.plan_id
        assert plan_dict['name'] == 'Basic Plan'
        assert plan_dict['plan_type'] == PlanType.BASIC.value
        assert plan_dict['status'] == PlanStatus.ACTIVE.value

    def test_plan_is_active(self):
        """Test checking if plan is active."""
        plan = Plan(
            plan_id=self.plan_id,
            name='Basic Plan',
            plan_type=PlanType.BASIC,
            status=PlanStatus.ACTIVE
        )

        assert plan.is_active() is True

        plan.status = PlanStatus.INACTIVE
        assert plan.is_active() is False
        """Test plan validation with invalid name."""
        invalid_names = [
            '',
            'a',  # Too short
            'a' * 101,  # Too long
            '   ',  # Only whitespace
        ]
        
        for invalid_name in invalid_names:
            plan_data = self.plan_data.copy()
            plan_data['name'] = invalid_name
            
            with pytest.raises(ValidationException):
                Plan(**plan_data)
    
    def test_plan_validation_invalid_price(self):
        """Test plan validation with invalid prices."""
        invalid_prices = [
            Decimal('-1.00'),  # Negative price
            Decimal('0.00'),   # Zero price (for paid plans)
            Decimal('10000.00')  # Too expensive
        ]
        
        for invalid_price in invalid_prices:
            plan_data = self.plan_data.copy()
            plan_data['monthly_price'] = invalid_price
            
            with pytest.raises(ValidationException):
                Plan(**plan_data)
    
    def test_plan_validation_free_plan(self):
        """Test validation for free plan."""
        free_plan_data = {
            'name': 'Free Plan',
            'plan_type': PlanType.FREE,
            'monthly_price': Decimal('0.00'),
            'features': {
                'max_users': 1,
                'max_projects': 3
            }
        }
        
        # Free plan with zero price should be valid
        plan = Plan(**free_plan_data)
        assert plan.monthly_price == Decimal('0.00')
        assert plan.plan_type == PlanType.FREE
    
    def test_plan_to_dict(self):
        """Test plan serialization to dictionary."""
        plan = Plan(**self.plan_data)
        plan_dict = plan.to_dict()
        
        assert plan_dict['plan_id'] == plan.plan_id
        assert plan_dict['name'] == 'Basic Plan'
        assert plan_dict['plan_type'] == PlanType.BASIC.value
        assert plan_dict['monthly_price'] == '29.99'  # Decimal as string
        assert plan_dict['yearly_price'] == '299.99'
        assert plan_dict['features'] == plan.features
        assert plan_dict['status'] == PlanStatus.ACTIVE.value
        assert 'created_at' in plan_dict
        assert 'updated_at' in plan_dict
    
    def test_plan_is_free(self):
        """Test checking if plan is free."""
        # Free plan
        free_plan = Plan(
            name='Free Plan',
            plan_type=PlanType.FREE,
            monthly_price=Decimal('0.00')
        )
        assert free_plan.is_free() is True
        
        # Paid plan
        paid_plan = Plan(**self.plan_data)
        assert paid_plan.is_free() is False
    
    def test_plan_is_active(self):
        """Test checking if plan is active."""
        plan = Plan(**self.plan_data)
        
        # Active plan
        plan.status = PlanStatus.ACTIVE
        assert plan.is_active() is True
        
        # Inactive plan
        plan.status = PlanStatus.INACTIVE
        assert plan.is_active() is False
        
        # Deprecated plan
        plan.status = PlanStatus.DEPRECATED
        assert plan.is_active() is False
    
    def test_plan_calculate_yearly_discount(self):
        """Test calculating yearly discount percentage."""
        plan = Plan(**self.plan_data)
        
        # Monthly: $29.99, Yearly: $299.99
        # Annual equivalent of monthly: $29.99 * 12 = $359.88
        # Discount: ($359.88 - $299.99) / $359.88 = 16.64%
        
        discount = plan.calculate_yearly_discount()
        assert discount is not None
        assert 16.0 <= discount <= 17.0  # Approximately 16.64%
    
    def test_plan_calculate_yearly_discount_no_yearly_price(self):
        """Test calculating yearly discount when no yearly price is set."""
        plan_data = self.plan_data.copy()
        plan_data['yearly_price'] = None
        
        plan = Plan(**plan_data)
        discount = plan.calculate_yearly_discount()
        
        assert discount is None
    
    def test_plan_get_price_for_interval(self):
        """Test getting price for billing interval."""
        plan = Plan(**self.plan_data)
        
        monthly_price = plan.get_price_for_interval(BillingInterval.MONTHLY)
        yearly_price = plan.get_price_for_interval(BillingInterval.YEARLY)
        
        assert monthly_price == Decimal('29.99')
        assert yearly_price == Decimal('299.99')
    
    def test_plan_get_price_for_interval_no_yearly(self):
        """Test getting price when yearly price is not available."""
        plan_data = self.plan_data.copy()
        plan_data['yearly_price'] = None
        
        plan = Plan(**plan_data)
        
        monthly_price = plan.get_price_for_interval(BillingInterval.MONTHLY)
        yearly_price = plan.get_price_for_interval(BillingInterval.YEARLY)
        
        assert monthly_price == Decimal('29.99')
        assert yearly_price is None
    
    @mock_aws
    @mock_aws
    def test_plan_update_features(self):
        """Test updating plan features."""
        plan = Plan(**self.plan_data)

        new_features = {
            'max_users': 10,
            'max_projects': 20,
            'new_feature': True
        }

        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}

            plan.update_features(new_features)

            assert plan.features['max_users'] == 10
            assert plan.features['max_projects'] == 20
            assert plan.features['new_feature'] is True
            assert plan.features['storage_gb'] == 100  # Unchanged
            assert isinstance(plan.updated_at, int)
    
    @mock_aws
    def test_plan_activate(self):
        """Test plan activation."""
        plan = Plan(**self.plan_data)
        plan.status = PlanStatus.INACTIVE

        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}

            plan.activate()

            assert plan.status == PlanStatus.ACTIVE
            assert isinstance(plan.updated_at, int)
    
    @mock_aws
    def test_plan_deactivate(self):
        """Test plan deactivation."""
        plan = Plan(**self.plan_data)
        plan.status = PlanStatus.ACTIVE

        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}

            plan.deactivate()

            assert plan.status == PlanStatus.INACTIVE
            assert isinstance(plan.updated_at, int)
    
    @mock_aws
    def test_plan_deprecate(self):
        """Test plan deprecation."""
        plan = Plan(**self.plan_data)
        plan.status = PlanStatus.ACTIVE

        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}

            plan.deprecate()

            assert plan.status == PlanStatus.DEPRECATED
            assert isinstance(plan.updated_at, int)
    
    @mock_aws
    def test_plan_save(self):
        """Test plan save to database."""
        plan = Plan(**self.plan_data)
        
        with patch('src.shared.database.db_client.put_item') as mock_put:
            mock_put.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
            
            result = plan.save()
            
            assert result is True
            mock_put.assert_called_once()
            
            # Verify the item structure
            call_args = mock_put.call_args[0]  # Positional arguments
            item = call_args[0]  # First argument is the plan_data
            
            assert item['PK'] == f"PLAN#{plan.plan_id}"
            assert item['SK'] == 'METADATA'
            assert item['GSI1PK'] == f'PLAN_TYPE#{plan.plan_type.value}'
            assert item['GSI1SK'] == f"PLAN#{plan.plan_id}"
            assert item['entity_type'] == 'PLAN'
    
    @mock_aws
    def test_plan_get_by_id(self):
        """Test getting plan by ID."""
        plan_id = str(uuid.uuid4())
        
        mock_item = {
            'PK': f"PLAN#{plan_id}",
            'SK': f"PLAN#{plan_id}",
            'plan_id': plan_id,
            'name': 'Basic Plan',
            'plan_type': PlanType.BASIC.value,
            'monthly_price': '29.99',
            'yearly_price': '299.99',
            'status': PlanStatus.ACTIVE.value,
            'features': {'max_users': 5},
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        with patch('src.shared.database.db_client.get_item') as mock_get:
            mock_get.return_value = mock_item
            
            plan = Plan.get_by_id(plan_id)
            
            assert plan is not None
            assert plan.plan_id == plan_id
            assert plan.name == 'Basic Plan'
            assert plan.plan_type == PlanType.BASIC
            assert plan.monthly_price == Decimal('29.99')
    
    @mock_aws
    def test_plan_get_all_active(self):
        """Test getting all active plans."""
        mock_items = [
            {
                'plan_id': str(uuid.uuid4()),
                'name': 'Basic Plan',
                'plan_type': PlanType.BASIC.value,
                'monthly_price': '29.99',
                'status': PlanStatus.ACTIVE.value,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            },
            {
                'plan_id': str(uuid.uuid4()),
                'name': 'Pro Plan',
                'plan_type': PlanType.PROFESSIONAL.value,
                'monthly_price': '99.99',
                'status': PlanStatus.ACTIVE.value,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }
        ]
        
        with patch('src.shared.database.db_client.query') as mock_query:
            mock_query.return_value = {'Items': mock_items}
            
            plans = Plan.get_all_active()
            
            assert len(plans) == 2
            assert all(plan.status == PlanStatus.ACTIVE for plan in plans)
    
    def test_plan_create_default_plans(self):
        """Test creating default plans."""
        with patch.object(Plan, 'save') as mock_save:
            mock_save.return_value = True
            
            plans = Plan.create_default_plans()
            
            assert len(plans) == 4  # FREE, BASIC, PROFESSIONAL, ENTERPRISE
            
            # Verify plan types
            plan_types = [plan.plan_type for plan in plans]
            assert PlanType.FREE in plan_types
            assert PlanType.BASIC in plan_types
            assert PlanType.PROFESSIONAL in plan_types
            assert PlanType.ENTERPRISE in plan_types
            
            # Verify free plan has zero price
            free_plan = next(plan for plan in plans if plan.plan_type == PlanType.FREE)
            assert free_plan.monthly_price == Decimal('0.00')
            
            # Verify other plans have positive prices
            paid_plans = [plan for plan in plans if plan.plan_type != PlanType.FREE]
            assert all(plan.monthly_price > Decimal('0.00') for plan in paid_plans)
    
    def test_plan_comparison(self):
        """Test plan comparison methods."""
        basic_plan = Plan(
            name='Basic',
            plan_type=PlanType.BASIC,
            monthly_price=Decimal('29.99')
        )
        
        pro_plan = Plan(
            name='Professional',
            plan_type=PlanType.PROFESSIONAL,
            monthly_price=Decimal('99.99')
        )
        
        # Basic plan should be less expensive than Pro
        assert basic_plan.monthly_price < pro_plan.monthly_price
        
        # Plan types should have proper ordering
        assert basic_plan.plan_type.value < pro_plan.plan_type.value
