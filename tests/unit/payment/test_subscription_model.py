# tests/unit/payment/test_subscription_model.py
# Implementado según "Testing Guidelines"

"""
Unit tests for Subscription model.
"""

import pytest
import time
from decimal import Decimal
from unittest.mock import Mock, patch

from models.subscription import (
    Subscription,
    SubscriptionStatus,
    BillingInterval
)


class TestSubscription:
    """Test cases for Subscription model."""
    
    def test_subscription_creation(self):
        """Test subscription creation with default values."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            amount=Decimal("29.99")
        )
        
        assert subscription.tenant_id == "tenant-123"
        assert subscription.plan_id == "plan-456"
        assert subscription.amount == Decimal("29.99")
        assert subscription.status == SubscriptionStatus.TRIAL
        assert subscription.billing_interval == BillingInterval.MONTHLY
        assert subscription.currency == "USD"
        assert subscription.subscription_id is not None
        assert subscription.created_at is not None
        assert subscription.updated_at is not None
    
    def test_subscription_creation_with_custom_values(self):
        """Test subscription creation with custom values."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE,
            billing_interval=BillingInterval.YEARLY,
            amount=Decimal("299.99"),
            currency="EUR"
        )
        
        assert subscription.status == SubscriptionStatus.ACTIVE
        assert subscription.billing_interval == BillingInterval.YEARLY
        assert subscription.amount == Decimal("299.99")
        assert subscription.currency == "EUR"
    
    def test_subscription_status_from_string(self):
        """Test subscription status conversion from string."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status="ACTIVE"
        )
        
        assert subscription.status == SubscriptionStatus.ACTIVE
    
    def test_subscription_billing_interval_from_string(self):
        """Test billing interval conversion from string."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            billing_interval="YEARLY"
        )
        
        assert subscription.billing_interval == BillingInterval.YEARLY
    
    def test_is_active(self):
        """Test is_active method."""
        # Trial subscription should be active
        trial_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.TRIAL
        )
        assert trial_subscription.is_active() is True
        
        # Active subscription should be active
        active_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE
        )
        assert active_subscription.is_active() is True
        
        # Cancelled subscription should not be active
        cancelled_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.CANCELLED
        )
        assert cancelled_subscription.is_active() is False
    
    def test_is_trial(self):
        """Test is_trial method."""
        trial_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.TRIAL
        )
        assert trial_subscription.is_trial() is True
        
        active_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE
        )
        assert active_subscription.is_trial() is False
    
    def test_is_trial_expired(self):
        """Test is_trial_expired method."""
        current_time = int(time.time())
        
        # Trial not expired
        trial_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.TRIAL,
            trial_ends_at=current_time + 86400  # 1 day from now
        )
        assert trial_subscription.is_trial_expired() is False
        
        # Trial expired
        expired_trial_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.TRIAL,
            trial_ends_at=current_time - 86400  # 1 day ago
        )
        assert expired_trial_subscription.is_trial_expired() is True
        
        # Not a trial
        active_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE
        )
        assert active_subscription.is_trial_expired() is False
    
    def test_days_until_trial_end(self):
        """Test days_until_trial_end method."""
        current_time = int(time.time())
        
        # Trial with 5 days remaining
        trial_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.TRIAL,
            trial_ends_at=current_time + (5 * 86400)  # 5 days from now
        )
        days_remaining = trial_subscription.days_until_trial_end()
        assert days_remaining == 5
        
        # Expired trial
        expired_trial_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.TRIAL,
            trial_ends_at=current_time - 86400  # 1 day ago
        )
        assert expired_trial_subscription.days_until_trial_end() == 0
        
        # Not a trial
        active_subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE
        )
        assert active_subscription.days_until_trial_end() == 0
    
    @patch('src.payment.models.subscription.db_client')
    def test_save(self, mock_db_client):
        """Test subscription save method."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            amount=Decimal("29.99")
        )
        
        subscription.save()
        
        # Verify db_client.put_item was called
        mock_db_client.put_item.assert_called_once()
        
        # Verify the data structure
        call_args = mock_db_client.put_item.call_args
        subscription_data = call_args[0][0]
        tenant_id = call_args[0][1]
        
        assert tenant_id == "tenant-123"
        assert subscription_data['PK'] == f'TENANT#{subscription.tenant_id}'
        assert subscription_data['SK'] == f'SUBSCRIPTION#{subscription.subscription_id}'
        assert subscription_data['entity_type'] == 'SUBSCRIPTION'
        assert subscription_data['tenant_id'] == "tenant-123"
        assert subscription_data['plan_id'] == "plan-456"
        assert subscription_data['amount'] == "29.99"
        assert subscription_data['status'] == SubscriptionStatus.TRIAL.value
    
    @patch('src.payment.models.subscription.db_client')
    def test_update(self, mock_db_client):
        """Test subscription update method."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456"
        )
        
        original_updated_at = subscription.updated_at
        
        # Mock time.time to return a different value
        with patch('time.time', return_value=original_updated_at + 100):
            subscription.update(status=SubscriptionStatus.ACTIVE, amount=Decimal("99.99"))
        
        # Verify the subscription was updated
        assert subscription.status == SubscriptionStatus.ACTIVE
        assert subscription.amount == Decimal("99.99")
        assert subscription.updated_at > original_updated_at
        
        # Verify save was called
        mock_db_client.put_item.assert_called_once()
    
    def test_to_dict(self):
        """Test subscription to_dict method."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            amount=Decimal("29.99"),
            stripe_subscription_id="sub_123"
        )
        
        # Test without sensitive data
        data = subscription.to_dict()
        
        assert data['tenant_id'] == "tenant-123"
        assert data['plan_id'] == "plan-456"
        assert data['amount'] == "29.99"
        assert data['status'] == SubscriptionStatus.TRIAL.value
        assert data['is_active'] is True
        assert data['is_trial'] is True
        assert 'stripe_subscription_id' not in data
        
        # Test with sensitive data
        sensitive_data = subscription.to_dict(include_sensitive=True)
        
        assert sensitive_data['stripe_subscription_id'] == "sub_123"
    
    @patch('src.payment.models.subscription.db_client')
    def test_cancel(self, mock_db_client):
        """Test subscription cancel method."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE
        )
        
        subscription.cancel("User requested cancellation")
        
        # Verify the subscription was cancelled
        assert subscription.status == SubscriptionStatus.CANCELLED
        assert hasattr(subscription, 'cancelled_at')
        assert hasattr(subscription, 'cancellation_reason')
        
        # Verify save was called
        mock_db_client.put_item.assert_called_once()
    
    @patch('src.payment.models.subscription.db_client')
    def test_suspend(self, mock_db_client):
        """Test subscription suspend method."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.ACTIVE
        )
        
        subscription.suspend("Payment failed")
        
        # Verify the subscription was suspended
        assert subscription.status == SubscriptionStatus.SUSPENDED
        assert hasattr(subscription, 'suspended_at')
        assert hasattr(subscription, 'suspension_reason')
        
        # Verify save was called
        mock_db_client.put_item.assert_called_once()
    
    @patch('src.payment.models.subscription.db_client')
    def test_reactivate(self, mock_db_client):
        """Test subscription reactivate method."""
        subscription = Subscription(
            tenant_id="tenant-123",
            plan_id="plan-456",
            status=SubscriptionStatus.SUSPENDED
        )
        
        subscription.reactivate()
        
        # Verify the subscription was reactivated
        assert subscription.status == SubscriptionStatus.ACTIVE
        assert hasattr(subscription, 'reactivated_at')
        
        # Verify save was called
        mock_db_client.put_item.assert_called_once()
