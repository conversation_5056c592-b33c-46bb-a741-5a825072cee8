# tests/unit/payment/services/test_webhook_service.py
# Tests para Webhook Service

"""
Unit tests for webhook service.
Tests webhook event processing and error handling.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from services.webhook_service import WebhookService
from shared.exceptions import ValidationException, PaymentException


class TestWebhookService:
    """Test cases for WebhookService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.webhook_service = WebhookService()
    
    def test_process_webhook_success(self):
        """Test successful webhook processing."""
        # Mock webhook event
        mock_event = {
            "id": "evt_test123",
            "type": "customer.created",
            "data": {
                "object": {
                    "id": "cus_test123",
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                }
            }
        }
        
        with patch.object(self.webhook_service.stripe_client, 'construct_webhook_event', return_value=mock_event):
            # Process webhook
            result = self.webhook_service.process_webhook(
                payload='{"id": "evt_test123"}',
                signature="t=123,v1=abc",
                endpoint_secret="whsec_test"
            )
        
            # Verify result
            assert result["success"] is True
            assert result["event_id"] == "evt_test123"
            assert result["event_type"] == "customer.created"
            assert "processed_at" in result
    
    def test_process_webhook_unhandled_event(self):
        """Test processing of unhandled webhook event."""
        mock_event = {
            "id": "evt_test123",
            "type": "unknown.event",
            "data": {"object": {}}
        }

        with patch.object(self.webhook_service.stripe_client, 'construct_webhook_event', return_value=mock_event):
            result = self.webhook_service.process_webhook(
                payload='{"id": "evt_test123"}',
                signature="t=123,v1=abc",
                endpoint_secret="whsec_test"
            )

            assert result["success"] is True
            assert result["result"] == "unhandled_event_type"
    
    def test_process_webhook_validation_error(self):
        """Test webhook processing with validation error."""
        with patch.object(self.webhook_service.stripe_client, 'construct_webhook_event', side_effect=ValidationException("Invalid signature")):
            with pytest.raises(ValidationException):
                self.webhook_service.process_webhook(
                    payload='{"id": "evt_test123"}',
                    signature="invalid",
                    endpoint_secret="whsec_test"
                )
    
    def test_handle_customer_created(self):
        """Test handling of customer.created event."""
        event = {
            "id": "evt_test123",
            "type": "customer.created",
            "data": {
                "object": {
                    "id": "cus_test123",
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                }
            }
        }
        
        result = self.webhook_service._handle_customer_created(event)
        
        assert result["action"] == "logged"
        assert result["customer_id"] == "cus_test123"
    
    @patch('src.payment.services.webhook_service.Customer')
    def test_handle_customer_updated_found(self, mock_customer_class):
        """Test handling of customer.updated event when customer is found."""
        # Mock customer
        mock_customer = Mock()
        mock_customer.customer_id = "customer_123"
        mock_customer.email = "<EMAIL>"
        mock_customer.name = "Old Name"
        mock_customer.phone = None
        mock_customer.stripe_data = {}
        mock_customer.save = Mock()
        
        mock_customer_class.get_by_stripe_customer_id.return_value = mock_customer
        
        event = {
            "data": {
                "object": {
                    "id": "cus_test123",
                    "email": "<EMAIL>",
                    "name": "New Name",
                    "phone": "+1234567890"
                }
            }
        }
        
        result = self.webhook_service._handle_customer_updated(event)
        
        assert result["action"] == "synced"
        assert result["customer_id"] == "customer_123"
        assert mock_customer.email == "<EMAIL>"
        assert mock_customer.name == "New Name"
        assert mock_customer.phone == "+1234567890"
        mock_customer.save.assert_called_once()
    
    @patch('src.payment.services.webhook_service.Customer')
    def test_handle_customer_updated_not_found(self, mock_customer_class):
        """Test handling of customer.updated event when customer is not found."""
        mock_customer_class.get_by_stripe_customer_id.return_value = None
        
        event = {
            "data": {
                "object": {
                    "id": "cus_test123",
                    "email": "<EMAIL>"
                }
            }
        }
        
        result = self.webhook_service._handle_customer_updated(event)
        
        assert result["action"] == "not_found"
        assert result["stripe_customer_id"] == "cus_test123"
    
    @patch('src.payment.services.webhook_service.Customer')
    def test_handle_customer_deleted(self, mock_customer_class):
        """Test handling of customer.deleted event."""
        mock_customer = Mock()
        mock_customer.customer_id = "customer_123"
        mock_customer.deactivate = Mock()
        mock_customer.save = Mock()
        
        mock_customer_class.get_by_stripe_customer_id.return_value = mock_customer
        
        event = {
            "data": {
                "object": {
                    "id": "cus_test123"
                }
            }
        }
        
        result = self.webhook_service._handle_customer_deleted(event)
        
        assert result["action"] == "deactivated"
        assert result["customer_id"] == "customer_123"
        mock_customer.deactivate.assert_called_once()
        mock_customer.save.assert_called_once()
    
    def test_handle_subscription_created(self):
        """Test handling of customer.subscription.created event."""
        event = {
            "data": {
                "object": {
                    "id": "sub_test123",
                    "status": "active"
                }
            }
        }
        
        result = self.webhook_service._handle_subscription_created(event)
        
        assert result["action"] == "logged"
        assert result["subscription_id"] == "sub_test123"
    
    @patch('src.payment.services.webhook_service.Subscription')
    def test_handle_subscription_updated(self, mock_subscription_class):
        """Test handling of customer.subscription.updated event."""
        mock_subscription = Mock()
        mock_subscription.subscription_id = "subscription_123"
        mock_subscription.save = Mock()
        
        mock_subscription_class.get_by_stripe_subscription_id.return_value = mock_subscription
        
        event = {
            "data": {
                "object": {
                    "id": "sub_test123",
                    "status": "active",
                    "current_period_start": 1640995200,
                    "current_period_end": 1643673600,
                    "cancel_at_period_end": False,
                    "canceled_at": None
                }
            }
        }
        
        result = self.webhook_service._handle_subscription_updated(event)
        
        assert result["action"] == "synced"
        assert result["subscription_id"] == "subscription_123"
        mock_subscription.save.assert_called_once()
    
    @patch('src.payment.services.webhook_service.Subscription')
    def test_handle_subscription_deleted(self, mock_subscription_class):
        """Test handling of customer.subscription.deleted event."""
        mock_subscription = Mock()
        mock_subscription.subscription_id = "subscription_123"
        mock_subscription.save = Mock()
        
        mock_subscription_class.get_by_stripe_subscription_id.return_value = mock_subscription
        
        event = {
            "data": {
                "object": {
                    "id": "sub_test123",
                    "status": "canceled"
                }
            }
        }
        
        result = self.webhook_service._handle_subscription_deleted(event)
        
        assert result["action"] == "cancelled"
        assert result["subscription_id"] == "subscription_123"
        mock_subscription.save.assert_called_once()
    
    @patch('src.payment.services.webhook_service.Subscription')
    def test_handle_subscription_trial_will_end(self, mock_subscription_class):
        """Test handling of customer.subscription.trial_will_end event."""
        mock_subscription = Mock()
        mock_subscription.subscription_id = "subscription_123"
        
        mock_subscription_class.get_by_stripe_subscription_id.return_value = mock_subscription
        
        event = {
            "data": {
                "object": {
                    "id": "sub_test123",
                    "trial_end": 1640995200
                }
            }
        }
        
        result = self.webhook_service._handle_subscription_trial_will_end(event)
        
        assert result["action"] == "notification_sent"
        assert result["subscription_id"] == "subscription_123"
    
    def test_handle_invoice_created(self):
        """Test handling of invoice.created event."""
        event = {
            "data": {
                "object": {
                    "id": "in_test123",
                    "amount_due": 2000
                }
            }
        }
        
        result = self.webhook_service._handle_invoice_created(event)
        
        assert result["action"] == "logged"
        assert result["invoice_id"] == "in_test123"
    
    def test_handle_invoice_payment_succeeded(self):
        """Test handling of invoice.payment_succeeded event."""
        event = {
            "data": {
                "object": {
                    "id": "in_test123",
                    "amount_paid": 2000
                }
            }
        }
        
        result = self.webhook_service._handle_invoice_payment_succeeded(event)
        
        assert result["action"] == "payment_processed"
        assert result["invoice_id"] == "in_test123"
    
    def test_handle_invoice_payment_failed(self):
        """Test handling of invoice.payment_failed event."""
        event = {
            "data": {
                "object": {
                    "id": "in_test123",
                    "amount_due": 2000
                }
            }
        }
        
        result = self.webhook_service._handle_invoice_payment_failed(event)
        
        assert result["action"] == "payment_failed_processed"
        assert result["invoice_id"] == "in_test123"
    
    def test_handle_payment_intent_succeeded(self):
        """Test handling of payment_intent.succeeded event."""
        event = {
            "data": {
                "object": {
                    "id": "pi_test123",
                    "amount": 2000
                }
            }
        }
        
        result = self.webhook_service._handle_payment_intent_succeeded(event)
        
        assert result["action"] == "logged"
        assert result["payment_intent_id"] == "pi_test123"
    
    def test_handle_payment_intent_failed(self):
        """Test handling of payment_intent.payment_failed event."""
        event = {
            "data": {
                "object": {
                    "id": "pi_test123",
                    "last_payment_error": {
                        "message": "Your card was declined."
                    }
                }
            }
        }
        
        result = self.webhook_service._handle_payment_intent_failed(event)
        
        assert result["action"] == "logged"
        assert result["payment_intent_id"] == "pi_test123"
    
    def test_map_stripe_status(self):
        """Test mapping of Stripe status to local status."""
        from models.subscription import SubscriptionStatus
        
        # Test all status mappings
        assert self.webhook_service._map_stripe_status("active") == SubscriptionStatus.ACTIVE
        assert self.webhook_service._map_stripe_status("trialing") == SubscriptionStatus.TRIALING
        assert self.webhook_service._map_stripe_status("past_due") == SubscriptionStatus.PAST_DUE
        assert self.webhook_service._map_stripe_status("canceled") == SubscriptionStatus.CANCELLED
        assert self.webhook_service._map_stripe_status("unpaid") == SubscriptionStatus.UNPAID
        assert self.webhook_service._map_stripe_status("incomplete") == SubscriptionStatus.INCOMPLETE
        assert self.webhook_service._map_stripe_status("incomplete_expired") == SubscriptionStatus.INCOMPLETE_EXPIRED
        
        # Test unknown status
        assert self.webhook_service._map_stripe_status("unknown") == SubscriptionStatus.INCOMPLETE
