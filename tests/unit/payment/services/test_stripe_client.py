# tests/unit/payment/services/test_stripe_client.py
# Tests para Stripe Client Service

"""
Unit tests for Stripe client service.
Tests Stripe API integration and error handling.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import stripe

from services.stripe_client import StripeClientService
from shared.exceptions import PaymentException, ValidationException


class TestStripeClientService:
    """Test cases for StripeClientService."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.stripe_client = StripeClientService()
    
    @patch('src.payment.services.stripe_client.stripe')
    def test_configure_stripe_test_environment(self, mock_stripe):
        """Test Stripe configuration for test environment."""
        with patch('src.payment.services.stripe_client.get_settings') as mock_settings:
            mock_settings.return_value.environment = "test"
            mock_settings.return_value.stripe_test_secret_key = "sk_test_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
            
            client = StripeClientService()
            
            assert mock_stripe.api_key == "sk_test_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
            assert mock_stripe.api_version == "2025-06-30.basil"
            assert mock_stripe.max_network_retries == 3
    
    @patch('src.payment.services.stripe_client.stripe')
    def test_configure_stripe_production_environment(self, mock_stripe):
        """Test Stripe configuration for production environment."""
        with patch('src.payment.services.stripe_client.get_settings') as mock_settings:
            mock_settings.return_value.environment = "production"
            mock_settings.return_value.stripe_live_secret_key = "sk_live_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
            
            client = StripeClientService()
            
            assert mock_stripe.api_key == "sk_live_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
    
    @patch('src.payment.services.stripe_client.stripe.Customer.create')
    def test_create_customer_success(self, mock_create):
        """Test successful customer creation."""
        # Mock Stripe customer response
        mock_customer = Mock()
        mock_customer.id = "cus_test123"
        mock_customer.email = "<EMAIL>"
        mock_customer.name = "Test Customer"
        mock_create.return_value = mock_customer
        
        # Create customer
        result = self.stripe_client.create_customer(
            email="<EMAIL>",
            name="Test Customer",
            tenant_id="tenant_123"
        )
        
        # Verify result
        assert result == mock_customer
        mock_create.assert_called_once()
        
        # Verify call arguments
        call_args = mock_create.call_args[1]
        assert call_args["email"] == "<EMAIL>"
        assert call_args["name"] == "Test Customer"
        assert call_args["metadata"]["tenant_id"] == "tenant_123"
    
    @patch('src.payment.services.stripe_client.stripe.Customer.create')
    def test_create_customer_with_phone(self, mock_create):
        """Test customer creation with phone number."""
        mock_customer = Mock()
        mock_customer.id = "cus_test123"
        mock_create.return_value = mock_customer
        
        result = self.stripe_client.create_customer(
            email="<EMAIL>",
            name="Test Customer",
            tenant_id="tenant_123",
            phone="+1234567890"
        )
        
        call_args = mock_create.call_args[1]
        assert call_args["phone"] == "+1234567890"
    
    @patch('src.payment.services.stripe_client.stripe.Customer.create')
    def test_create_customer_stripe_error(self, mock_create):
        """Test customer creation with Stripe error."""
        mock_create.side_effect = stripe.error.CardError(
            message="Your card was declined.",
            param="card",
            code="card_declined"
        )
        
        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client.create_customer(
                email="<EMAIL>",
                name="Test Customer",
                tenant_id="tenant_123"
            )
        
        assert "Payment failed" in str(exc_info.value)
        assert exc_info.value.error_code == "CARD_DECLINED"
    
    @patch('src.payment.services.stripe_client.stripe.Customer.retrieve')
    def test_get_customer_success(self, mock_retrieve):
        """Test successful customer retrieval."""
        mock_customer = Mock()
        mock_customer.id = "cus_test123"
        mock_retrieve.return_value = mock_customer
        
        result = self.stripe_client.get_customer("cus_test123")
        
        assert result == mock_customer
        mock_retrieve.assert_called_once_with("cus_test123")
    
    @patch('src.payment.services.stripe_client.stripe.Customer.retrieve')
    def test_get_customer_not_found(self, mock_retrieve):
        """Test customer retrieval when customer not found."""
        mock_retrieve.side_effect = stripe.error.InvalidRequestError(
            message="No such customer: cus_invalid",
            param="id"
        )
        
        with pytest.raises(ValidationException):
            self.stripe_client.get_customer("cus_invalid")
    
    @patch('src.payment.services.stripe_client.stripe.Subscription.create')
    def test_create_subscription_success(self, mock_create):
        """Test successful subscription creation."""
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_subscription.status = "active"
        mock_subscription.current_period_start = 1640995200
        mock_subscription.current_period_end = 1643673600
        mock_subscription.trial_start = None
        mock_subscription.trial_end = None
        mock_subscription.cancel_at_period_end = False
        mock_create.return_value = mock_subscription
        
        result = self.stripe_client.create_subscription(
            customer_id="cus_test123",
            price_id="price_test123",
            payment_method_id="pm_test123"
        )
        
        assert result == mock_subscription
        mock_create.assert_called_once()
        
        call_args = mock_create.call_args[1]
        assert call_args["customer"] == "cus_test123"
        assert call_args["items"][0]["price"] == "price_test123"
    
    @patch('src.payment.services.stripe_client.stripe.Subscription.create')
    def test_create_subscription_with_trial(self, mock_create):
        """Test subscription creation with trial period."""
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_create.return_value = mock_subscription
        
        result = self.stripe_client.create_subscription(
            customer_id="cus_test123",
            price_id="price_test123",
            trial_period_days=14
        )
        
        call_args = mock_create.call_args[1]
        assert call_args["trial_period_days"] == 14
    
    @patch('src.payment.services.stripe_client.stripe.Subscription.retrieve')
    def test_get_subscription_success(self, mock_retrieve):
        """Test successful subscription retrieval."""
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_retrieve.return_value = mock_subscription
        
        result = self.stripe_client.get_subscription("sub_test123")
        
        assert result == mock_subscription
        mock_retrieve.assert_called_once_with(
            "sub_test123",
            expand=["latest_invoice", "customer", "default_payment_method"]
        )
    
    @patch('src.payment.services.stripe_client.stripe.Subscription.modify')
    def test_update_subscription_success(self, mock_modify):
        """Test successful subscription update."""
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_modify.return_value = mock_subscription
        
        result = self.stripe_client.update_subscription(
            "sub_test123",
            metadata={"updated": "true"}
        )
        
        assert result == mock_subscription
        mock_modify.assert_called_once_with(
            "sub_test123",
            metadata={"updated": "true"}
        )
    
    @patch('src.payment.services.stripe_client.stripe.Subscription.modify')
    def test_cancel_subscription_at_period_end(self, mock_modify):
        """Test subscription cancellation at period end."""
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_modify.return_value = mock_subscription
        
        result = self.stripe_client.cancel_subscription(
            "sub_test123",
            at_period_end=True
        )
        
        assert result == mock_subscription
        mock_modify.assert_called_once_with(
            "sub_test123",
            cancel_at_period_end=True
        )
    
    @patch('src.payment.services.stripe_client.stripe.Subscription.cancel')
    def test_cancel_subscription_immediately(self, mock_cancel):
        """Test immediate subscription cancellation."""
        mock_subscription = Mock()
        mock_subscription.id = "sub_test123"
        mock_cancel.return_value = mock_subscription
        
        result = self.stripe_client.cancel_subscription(
            "sub_test123",
            at_period_end=False
        )
        
        assert result == mock_subscription
        mock_cancel.assert_called_once_with("sub_test123")
    
    @patch('src.payment.services.stripe_client.stripe.Product.create')
    def test_create_product_success(self, mock_create):
        """Test successful product creation."""
        mock_product = Mock()
        mock_product.id = "prod_test123"
        mock_create.return_value = mock_product
        
        result = self.stripe_client.create_product(
            name="Test Product",
            description="A test product"
        )
        
        assert result == mock_product
        mock_create.assert_called_once()
        
        call_args = mock_create.call_args[1]
        assert call_args["name"] == "Test Product"
        assert call_args["description"] == "A test product"
        assert call_args["type"] == "service"
    
    @patch('src.payment.services.stripe_client.stripe.Price.create')
    def test_create_price_success(self, mock_create):
        """Test successful price creation."""
        mock_price = Mock()
        mock_price.id = "price_test123"
        mock_create.return_value = mock_price
        
        result = self.stripe_client.create_price(
            product_id="prod_test123",
            unit_amount=2000,
            currency="usd",
            recurring_interval="month"
        )
        
        assert result == mock_price
        mock_create.assert_called_once()
        
        call_args = mock_create.call_args[1]
        assert call_args["product"] == "prod_test123"
        assert call_args["unit_amount"] == 2000
        assert call_args["currency"] == "usd"
        assert call_args["recurring"]["interval"] == "month"
    
    @patch('src.payment.services.stripe_client.stripe.Webhook.construct_event')
    def test_construct_webhook_event_success(self, mock_construct):
        """Test successful webhook event construction."""
        mock_event = {"id": "evt_test123", "type": "customer.created"}
        mock_construct.return_value = mock_event
        
        result = self.stripe_client.construct_webhook_event(
            payload='{"id": "evt_test123"}',
            signature="t=123,v1=abc",
            endpoint_secret="whsec_test"
        )
        
        assert result == mock_event
        mock_construct.assert_called_once_with(
            '{"id": "evt_test123"}',
            "t=123,v1=abc",
            "whsec_test",
            300
        )
    
    @patch('src.payment.services.stripe_client.stripe.Webhook.construct_event')
    def test_construct_webhook_event_invalid_payload(self, mock_construct):
        """Test webhook event construction with invalid payload."""
        mock_construct.side_effect = ValueError("Invalid payload")
        
        with pytest.raises(ValidationException) as exc_info:
            self.stripe_client.construct_webhook_event(
                payload="invalid",
                signature="t=123,v1=abc",
                endpoint_secret="whsec_test"
            )
        
        assert "Invalid webhook payload" in str(exc_info.value)
    
    @patch('src.payment.services.stripe_client.stripe.Webhook.construct_event')
    def test_construct_webhook_event_invalid_signature(self, mock_construct):
        """Test webhook event construction with invalid signature."""
        mock_construct.side_effect = stripe.error.SignatureVerificationError(
            message="Invalid signature",
            sig_header="invalid"
        )
        
        with pytest.raises(ValidationException) as exc_info:
            self.stripe_client.construct_webhook_event(
                payload='{"id": "evt_test123"}',
                signature="invalid",
                endpoint_secret="whsec_test"
            )
        
        assert "Invalid webhook signature" in str(exc_info.value)
    
    def test_handle_stripe_error_card_error(self):
        """Test handling of Stripe card error."""
        error = stripe.error.CardError(
            message="Your card was declined.",
            param="card",
            code="card_declined"
        )
        
        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client._handle_stripe_error(error)
        
        assert exc_info.value.error_code == "CARD_DECLINED"
        assert "Payment failed" in str(exc_info.value)
    
    def test_handle_stripe_error_rate_limit(self):
        """Test handling of Stripe rate limit error."""
        error = stripe.error.RateLimitError(message="Too many requests")
        
        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client._handle_stripe_error(error)
        
        assert exc_info.value.error_code == "RATE_LIMIT_ERROR"
    
    def test_handle_stripe_error_invalid_request(self):
        """Test handling of Stripe invalid request error."""
        error = stripe.error.InvalidRequestError(
            message="Invalid parameter",
            param="email"
        )
        
        with pytest.raises(ValidationException) as exc_info:
            self.stripe_client._handle_stripe_error(error)
        
        assert "Invalid payment request" in str(exc_info.value)
    
    def test_handle_stripe_error_authentication(self):
        """Test handling of Stripe authentication error."""
        error = stripe.error.AuthenticationError(message="Invalid API key")
        
        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client._handle_stripe_error(error)
        
        assert exc_info.value.error_code == "AUTHENTICATION_ERROR"


# Additional tests for edge cases and error scenarios
class TestStripeClientErrorHandling:
    """Test error handling scenarios for StripeClientService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.stripe_client = StripeClientService()

    def test_handle_stripe_error_connection_error(self):
        """Test handling of Stripe connection error."""
        error = stripe.error.APIConnectionError(message="Network error")

        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client._handle_stripe_error(error)

        assert exc_info.value.error_code == "CONNECTION_ERROR"

    def test_handle_stripe_error_generic(self):
        """Test handling of generic Stripe error."""
        error = stripe.error.StripeError(message="Generic error")

        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client._handle_stripe_error(error)

        assert exc_info.value.error_code == "STRIPE_ERROR"

    def test_handle_unknown_error(self):
        """Test handling of unknown error."""
        error = Exception("Unknown error")

        with pytest.raises(PaymentException) as exc_info:
            self.stripe_client._handle_stripe_error(error)

        assert exc_info.value.error_code == "UNKNOWN_ERROR"
