# tests/unit/payment/test_plan_model.py
# Implementado según "Testing Guidelines"

"""
Unit tests for Plan model.
"""

import pytest
from decimal import Decimal
from unittest.mock import Mock, patch

from models.plan import (
    Plan,
    PlanType,
    PlanStatus
)


class TestPlan:
    """Test cases for Plan model."""
    
    def test_plan_creation(self):
        """Test plan creation with default values."""
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99"),
            yearly_price=Decimal("299.99")
        )
        
        assert plan.name == "Test Plan"
        assert plan.plan_type == PlanType.BASIC
        assert plan.monthly_price == Decimal("29.99")
        assert plan.yearly_price == Decimal("299.99")
        assert plan.status == PlanStatus.ACTIVE
        assert plan.currency == "USD"
        assert plan.features == []
        assert plan.limits == {}
        assert plan.trial_days == 30
        assert plan.plan_id is not None
        assert plan.created_at is not None
        assert plan.updated_at is not None
    
    def test_plan_creation_with_custom_values(self):
        """Test plan creation with custom values."""
        features = ["feature1", "feature2"]
        limits = {"max_users": 10, "storage_gb": 5}
        
        plan = Plan(
            name="Custom Plan",
            plan_type=PlanType.PROFESSIONAL,
            status=PlanStatus.INACTIVE,
            monthly_price=Decimal("99.99"),
            yearly_price=Decimal("999.99"),
            currency="EUR",
            features=features,
            limits=limits,
            trial_days=14,
            description="A custom plan for testing"
        )
        
        assert plan.name == "Custom Plan"
        assert plan.plan_type == PlanType.PROFESSIONAL
        assert plan.status == PlanStatus.INACTIVE
        assert plan.currency == "EUR"
        assert plan.features == features
        assert plan.limits == limits
        assert plan.trial_days == 14
        assert plan.description == "A custom plan for testing"
    
    def test_plan_type_from_string(self):
        """Test plan type conversion from string."""
        plan = Plan(
            name="Test Plan",
            plan_type="ENTERPRISE",
            monthly_price=Decimal("299.99")
        )
        
        assert plan.plan_type == PlanType.ENTERPRISE
    
    def test_plan_status_from_string(self):
        """Test plan status conversion from string."""
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            status="DEPRECATED",
            monthly_price=Decimal("29.99")
        )
        
        assert plan.status == PlanStatus.DEPRECATED
    
    def test_is_active(self):
        """Test is_active method."""
        active_plan = Plan(
            name="Active Plan",
            plan_type=PlanType.BASIC,
            status=PlanStatus.ACTIVE,
            monthly_price=Decimal("29.99")
        )
        assert active_plan.is_active() is True
        
        inactive_plan = Plan(
            name="Inactive Plan",
            plan_type=PlanType.BASIC,
            status=PlanStatus.INACTIVE,
            monthly_price=Decimal("29.99")
        )
        assert inactive_plan.is_active() is False
    
    def test_is_free(self):
        """Test is_free method."""
        free_plan = Plan(
            name="Free Plan",
            plan_type=PlanType.FREE,
            monthly_price=Decimal("0.00")
        )
        assert free_plan.is_free() is True
        
        paid_plan = Plan(
            name="Paid Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99")
        )
        assert paid_plan.is_free() is False
    
    def test_get_price(self):
        """Test get_price method."""
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99"),
            yearly_price=Decimal("299.99")
        )
        
        assert plan.get_price("MONTHLY") == Decimal("29.99")
        assert plan.get_price("monthly") == Decimal("29.99")
        assert plan.get_price("YEARLY") == Decimal("299.99")
        assert plan.get_price("yearly") == Decimal("299.99")
        assert plan.get_price("UNKNOWN") == Decimal("29.99")  # Defaults to monthly
    
    def test_has_feature(self):
        """Test has_feature method."""
        features = ["basic_chat", "email_support", "analytics"]
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99"),
            features=features
        )
        
        assert plan.has_feature("basic_chat") is True
        assert plan.has_feature("email_support") is True
        assert plan.has_feature("premium_support") is False
    
    def test_get_limit(self):
        """Test get_limit method."""
        limits = {"max_users": 10, "storage_gb": 5, "api_calls": 1000}
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99"),
            limits=limits
        )
        
        assert plan.get_limit("max_users") == 10
        assert plan.get_limit("storage_gb") == 5
        assert plan.get_limit("nonexistent") is None
    
    @patch('src.payment.models.plan.db_client')
    def test_save(self, mock_db_client):
        """Test plan save method."""
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99")
        )
        
        plan.save()
        
        # Verify db_client.put_item was called
        mock_db_client.put_item.assert_called_once()
        
        # Verify the data structure
        call_args = mock_db_client.put_item.call_args
        plan_data = call_args[0][0]
        tenant_id = call_args[0][1]
        
        assert tenant_id == 'global'
        assert plan_data['PK'] == f'PLAN#{plan.plan_id}'
        assert plan_data['SK'] == 'METADATA'
        assert plan_data['entity_type'] == 'PLAN'
        assert plan_data['name'] == "Test Plan"
        assert plan_data['plan_type'] == PlanType.BASIC.value
        assert plan_data['monthly_price'] == "29.99"
    
    @patch('src.payment.models.plan.db_client')
    def test_update(self, mock_db_client):
        """Test plan update method."""
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99")
        )
        
        original_updated_at = plan.updated_at
        
        # Mock time.time to return a different value
        with patch('time.time', return_value=original_updated_at + 100):
            plan.update(
                name="Updated Plan",
                monthly_price=Decimal("39.99"),
                status=PlanStatus.DEPRECATED
            )
        
        # Verify the plan was updated
        assert plan.name == "Updated Plan"
        assert plan.monthly_price == Decimal("39.99")
        assert plan.status == PlanStatus.DEPRECATED
        assert plan.updated_at > original_updated_at
        
        # Verify save was called
        mock_db_client.put_item.assert_called_once()
    
    def test_to_dict(self):
        """Test plan to_dict method."""
        plan = Plan(
            name="Test Plan",
            plan_type=PlanType.BASIC,
            monthly_price=Decimal("29.99"),
            yearly_price=Decimal("299.99"),
            features=["basic_chat", "email_support"],
            limits={"max_users": 10},
            stripe_monthly_price_id="price_123"
        )
        
        # Test without sensitive data
        data = plan.to_dict()
        
        assert data['name'] == "Test Plan"
        assert data['plan_type'] == PlanType.BASIC.value
        assert data['monthly_price'] == "29.99"
        assert data['yearly_price'] == "299.99"
        assert data['features'] == ["basic_chat", "email_support"]
        assert data['limits'] == {"max_users": 10}
        assert data['is_active'] is True
        assert data['is_free'] is False
        assert 'stripe_monthly_price_id' not in data
        
        # Test with sensitive data
        sensitive_data = plan.to_dict(include_sensitive=True)
        
        assert sensitive_data['stripe_monthly_price_id'] == "price_123"
    
    @patch('src.payment.models.plan.db_client')
    def test_create_default_plans(self, mock_db_client):
        """Test create_default_plans class method."""
        plans = Plan.create_default_plans()
        
        # Verify 4 plans were created
        assert len(plans) == 4
        
        # Verify plan types
        plan_types = [plan.plan_type for plan in plans]
        assert PlanType.FREE in plan_types
        assert PlanType.BASIC in plan_types
        assert PlanType.PROFESSIONAL in plan_types
        assert PlanType.ENTERPRISE in plan_types
        
        # Verify FREE plan
        free_plan = next(plan for plan in plans if plan.plan_type == PlanType.FREE)
        assert free_plan.monthly_price == Decimal("0.00")
        assert free_plan.yearly_price == Decimal("0.00")
        assert free_plan.trial_days == 0
        
        # Verify BASIC plan
        basic_plan = next(plan for plan in plans if plan.plan_type == PlanType.BASIC)
        assert basic_plan.monthly_price == Decimal("29.00")
        assert basic_plan.yearly_price == Decimal("290.00")
        assert basic_plan.trial_days == 14
        
        # Verify save was called for each plan
        assert mock_db_client.put_item.call_count == 4
