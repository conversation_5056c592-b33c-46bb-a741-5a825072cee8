# tests/unit/test_auth_models.py
# Implementado según "Development Standards" y "Testing Guidelines"

"""
Unit tests for authentication models.
"""

import pytest
import time
from unittest.mock import patch, MagicMock

# Import from auth service models specifically
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'services', 'auth', 'src'))

from models.tenant import Tenant, TenantStatus, TenantPlan
from models.user import User, UserRole, UserStatus
from shared.exceptions import ResourceConflictException, InvalidCredentialsException


class TestTenantModel:
    """Test cases for Tenant model."""
    
    def test_tenant_initialization(self):
        """Test tenant object initialization."""
        tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            status=TenantStatus.ACTIVE,
            plan=TenantPlan.FREE
        )
        
        assert tenant.tenant_id == "test-123"
        assert tenant.name == "Test Company"
        assert tenant.status == TenantStatus.ACTIVE
        assert tenant.plan == TenantPlan.FREE
        assert isinstance(tenant.created_at, int)
        assert isinstance(tenant.updated_at, int)
    
    def test_tenant_is_active(self):
        """Test tenant active status check."""
        # Active tenant
        active_tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            status=TenantStatus.ACTIVE
        )
        assert active_tenant.is_active() is True
        
        # Trial tenant
        trial_tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            status=TenantStatus.TRIAL
        )
        assert trial_tenant.is_active() is True
        
        # Inactive tenant
        inactive_tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            status=TenantStatus.INACTIVE
        )
        assert inactive_tenant.is_active() is False
    
    def test_tenant_trial_expired(self):
        """Test trial expiration check."""
        current_time = int(time.time())
        
        # Not expired trial
        tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            status=TenantStatus.TRIAL,
            trial_ends_at=current_time + 3600  # 1 hour from now
        )
        assert tenant.is_trial_expired() is False
        
        # Expired trial
        tenant.trial_ends_at = current_time - 3600  # 1 hour ago
        assert tenant.is_trial_expired() is True
        
        # Non-trial tenant
        tenant.status = TenantStatus.ACTIVE
        assert tenant.is_trial_expired() is False
    
    def test_tenant_can_add_user(self):
        """Test user addition capability."""
        tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            user_count=2,
            max_users=5
        )
        assert tenant.can_add_user() is True
        
        # At limit
        tenant.user_count = 5
        assert tenant.can_add_user() is False
        
        # Over limit
        tenant.user_count = 6
        assert tenant.can_add_user() is False
    
    def test_get_max_users_for_plan(self):
        """Test max users calculation for different plans."""
        assert Tenant._get_max_users_for_plan(TenantPlan.FREE) == 1
        assert Tenant._get_max_users_for_plan(TenantPlan.BASIC) == 5
        assert Tenant._get_max_users_for_plan(TenantPlan.PROFESSIONAL) == 25
        assert Tenant._get_max_users_for_plan(TenantPlan.ENTERPRISE) == 1000
    
    def test_get_features_for_plan(self):
        """Test features for different plans."""
        free_features = Tenant._get_features_for_plan(TenantPlan.FREE)
        assert "basic_chat" in free_features
        assert "single_agent" in free_features
        
        enterprise_features = Tenant._get_features_for_plan(TenantPlan.ENTERPRISE)
        assert "sso" in enterprise_features
        assert "priority_support" in enterprise_features
    
    def test_tenant_to_dict(self):
        """Test tenant serialization to dictionary."""
        tenant = Tenant(
            tenant_id="test-123",
            name="Test Company",
            status=TenantStatus.ACTIVE,
            plan=TenantPlan.FREE,
            user_count=2,
            max_users=5
        )
        
        data = tenant.to_dict()
        
        assert data["tenant_id"] == "test-123"
        assert data["name"] == "Test Company"
        assert data["status"] == "ACTIVE"
        assert data["plan"] == "FREE"
        assert data["user_count"] == 2
        assert data["max_users"] == 5
        assert data["is_active"] is True
        assert isinstance(data["is_trial_expired"], bool)


class TestUserModel:
    """Test cases for User model."""
    
    def test_user_initialization(self):
        """Test user object initialization."""
        user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            role=UserRole.MEMBER,
            status=UserStatus.ACTIVE
        )
        
        assert user.user_id == "user-123"
        assert user.tenant_id == "tenant-123"
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.role == UserRole.MEMBER
        assert user.status == UserStatus.ACTIVE
        assert isinstance(user.created_at, int)
        assert isinstance(user.updated_at, int)
    
    def test_user_is_active(self):
        """Test user active status check."""
        # Active user
        active_user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            status=UserStatus.ACTIVE
        )
        assert active_user.is_active() is True
        
        # Inactive user
        inactive_user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            status=UserStatus.INACTIVE
        )
        assert inactive_user.is_active() is False
    
    def test_user_is_master(self):
        """Test master role check."""
        # Master user
        master_user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            role=UserRole.MASTER
        )
        assert master_user.is_master() is True
        
        # Member user
        member_user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            role=UserRole.MEMBER
        )
        assert member_user.is_master() is False
    
    def test_user_is_locked(self):
        """Test account lock status."""
        current_time = int(time.time())
        
        # Not locked user
        user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            login_attempts=3
        )
        assert user.is_locked() is False
        
        # Locked user (5+ attempts within 15 minutes)
        user.login_attempts = 5
        user.last_failed_login = current_time - 600  # 10 minutes ago
        assert user.is_locked() is True
        
        # Lock expired (more than 15 minutes ago)
        user.last_failed_login = current_time - 1200  # 20 minutes ago
        assert user.is_locked() is False
    
    @patch('src.shared.auth.password_manager.verify_password')
    def test_user_authenticate_success(self, mock_verify):
        """Test successful user authentication."""
        mock_verify.return_value = True
        
        user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            role=UserRole.MASTER,  # Master users don't need email verification
            status=UserStatus.ACTIVE,
            password_hash="hashed_password",
            email_verified=True,
            login_attempts=0
        )
        
        with patch.object(user, '_reset_login_attempts') as mock_reset, \
             patch.object(user, '_update_last_login') as mock_update:
            
            result = user.authenticate("correct_password")
            
            assert result is True
            mock_verify.assert_called_once_with("correct_password", "hashed_password")
            mock_reset.assert_called_once()
            mock_update.assert_called_once()
    
    @patch('src.shared.auth.password_manager.verify_password')
    def test_user_authenticate_invalid_password(self, mock_verify):
        """Test authentication with invalid password."""
        mock_verify.return_value = False
        
        user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            role=UserRole.MASTER,
            status=UserStatus.ACTIVE,
            password_hash="hashed_password",
            email_verified=True,
            login_attempts=0
        )
        
        with patch.object(user, '_increment_login_attempts') as mock_increment:
            with pytest.raises(InvalidCredentialsException):
                user.authenticate("wrong_password")
            
            mock_verify.assert_called_once_with("wrong_password", "hashed_password")
            mock_increment.assert_called_once()
    
    def test_user_to_dict(self):
        """Test user serialization to dictionary."""
        user = User(
            user_id="user-123",
            tenant_id="tenant-123",
            email="<EMAIL>",
            name="Test User",
            role=UserRole.MEMBER,
            status=UserStatus.ACTIVE,
            phone="+1234567890",
            email_verified=True
        )
        
        data = user.to_dict()
        
        assert data["user_id"] == "user-123"
        assert data["tenant_id"] == "tenant-123"
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "Test User"
        assert data["role"] == "MEMBER"
        assert data["status"] == "ACTIVE"
        assert data["phone"] == "+1234567890"
        assert data["email_verified"] is True
        assert data["is_active"] is True
        assert data["is_master"] is False
        assert isinstance(data["is_locked"], bool)
        
        # Test without sensitive data
        assert "password_hash" not in data
        assert "login_attempts" not in data
        
        # Test with sensitive data
        sensitive_data = user.to_dict(include_sensitive=True)
        assert "login_attempts" in sensitive_data
