#!/usr/bin/env python3
# tests/unit/test_auth_working.py
# Working auth tests

"""
Working unit tests for shared authentication utilities.
"""

import pytest

class TestPasswordManagerWorking:
    """Test cases for PasswordManager."""
    
    def test_hash_password(self):
        """Test password hashing."""
        from shared.auth import PasswordManager
        
        pm = PasswordManager()
        password = "TestPassword123!"
        
        # Test hashing
        hashed = pm.hash_password(password)
        assert hashed is not None
        assert hashed != password
        assert len(hashed) > 20  # bcrypt hashes are long
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        from shared.auth import PasswordManager
        
        pm = PasswordManager()
        password = "TestPassword123!"
        hashed = pm.hash_password(password)
        
        # Test verification
        assert pm.verify_password(password, hashed) is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        from shared.auth import PasswordManager
        
        pm = PasswordManager()
        password = "TestPassword123!"
        hashed = pm.hash_password(password)
        
        # Test verification with wrong password
        assert pm.verify_password("WrongPassword", hashed) is False

class TestJWTManagerWorking:
    """Test cases for JWTManager."""
    
    def test_create_access_token(self):
        """Test access token creation."""
        from shared.auth import JWTManager
        
        jwt_manager = JWTManager()
        
        token = jwt_manager.generate_access_token(
            user_id="test-user-123",
            email="<EMAIL>",
            tenant_id="test-tenant-123",
            role="member"
        )
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 50  # JWT tokens are long
    
    def test_verify_token_valid(self):
        """Test token verification with valid token."""
        from shared.auth import JWTManager
        
        jwt_manager = JWTManager()
        
        # Create token
        token = jwt_manager.generate_access_token(
            user_id="test-user-123",
            email="<EMAIL>",
            tenant_id="test-tenant-123",
            role="member"
        )
        
        # Verify token
        payload = jwt_manager.verify_token(token)
        
        assert payload is not None
        assert payload["user_id"] == "test-user-123"
        assert payload["email"] == "<EMAIL>"
        assert payload["tenant_id"] == "test-tenant-123"
        assert payload["role"] == "member"

class TestAuthContextWorking:
    """Test cases for AuthContext."""
    
    def test_auth_context_initialization(self):
        """Test AuthContext initialization."""
        from shared.auth import AuthContext
        
        context = AuthContext(
            user_id="test-user-123",
            email="<EMAIL>",
            tenant_id="test-tenant-123",
            role="member",
            token_payload={"test": "data"}
        )
        
        assert context.user_id == "test-user-123"
        assert context.email == "<EMAIL>"
        assert context.tenant_id == "test-tenant-123"
        assert context.role == "member"
        assert context.token_payload == {"test": "data"}
    
    def test_has_role(self):
        """Test role checking."""
        from shared.auth import AuthContext
        
        context = AuthContext(
            user_id="test-user-123",
            email="<EMAIL>",
            tenant_id="test-tenant-123",
            role="admin",
            token_payload={}
        )
        
        assert context.has_role("admin") is True
        assert context.has_role("member") is True  # Admin should have member permissions too
