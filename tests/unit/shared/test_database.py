# tests/unit/shared/test_database.py
# Unit tests for shared database layer

"""
Unit tests for the shared database layer.
Tests the multi-tenant DynamoDB client functionality.
"""

import pytest
import boto3
from moto import mock_dynamodb
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal

from shared.database import <PERSON><PERSON><PERSON><PERSON>
from shared.exceptions import DatabaseException


class TestDynamoDBClient:
    """Test cases for DynamoDBClient."""
    
    @pytest.fixture
    def mock_table_name(self):
        """Mock table name for testing."""
        return "test-table"
    
    @pytest.fixture
    def db_client(self, mock_table_name):
        """Create a DynamoDBClient instance for testing."""
        return DynamoDBClient(table_name=mock_table_name)
    
    @mock_dynamodb
    def test_init_creates_dynamodb_resource(self, db_client):
        """Test that initialization creates DynamoDB resource."""
        assert db_client.dynamodb is not None
        assert db_client.table_name == "test-table"
    
    def test_add_tenant_context_with_valid_tenant_id(self, db_client):
        """Test adding tenant context to partition key."""
        result = db_client._add_tenant_context("USER#123", "tenant-456")
        assert result == "TENANT#tenant-456#USER#123"
    
    def test_add_tenant_context_with_empty_tenant_id_raises_exception(self, db_client):
        """Test that empty tenant ID raises DatabaseException."""
        with pytest.raises(DatabaseException, match="Tenant ID is required"):
            db_client._add_tenant_context("USER#123", "")
    
    def test_add_tenant_context_with_none_tenant_id_raises_exception(self, db_client):
        """Test that None tenant ID raises DatabaseException."""
        with pytest.raises(DatabaseException, match="Tenant ID is required"):
            db_client._add_tenant_context("USER#123", None)
    
    @mock_dynamodb
    def test_put_item_success(self, db_client):
        """Test successful item insertion."""
        # Create mock table
        dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
        table = dynamodb.create_table(
            TableName='test-table',
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Mock the table
        db_client.table = table
        
        # Test data
        item = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'age': 25
        }
        
        # Execute
        result = db_client.put_item(
            pk="USER#123",
            sk="PROFILE",
            item=item,
            tenant_id="tenant-456"
        )
        
        # Verify
        assert result is True
    
    @mock_dynamodb
    def test_get_item_success(self, db_client):
        """Test successful item retrieval."""
        # Create mock table
        dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
        table = dynamodb.create_table(
            TableName='test-table',
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Mock the table
        db_client.table = table
        
        # Insert test data
        test_item = {
            'PK': 'TENANT#tenant-456#USER#123',
            'SK': 'PROFILE',
            'name': 'Test User',
            'email': '<EMAIL>'
        }
        table.put_item(Item=test_item)
        
        # Execute
        result = db_client.get_item(
            pk="USER#123",
            sk="PROFILE",
            tenant_id="tenant-456"
        )
        
        # Verify
        assert result is not None
        assert result['name'] == 'Test User'
        assert result['email'] == '<EMAIL>'
    
    @mock_dynamodb
    def test_get_item_not_found_returns_none(self, db_client):
        """Test that non-existent item returns None."""
        # Create mock table
        dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
        table = dynamodb.create_table(
            TableName='test-table',
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Mock the table
        db_client.table = table
        
        # Execute
        result = db_client.get_item(
            pk="USER#nonexistent",
            sk="PROFILE",
            tenant_id="tenant-456"
        )
        
        # Verify
        assert result is None
    
    @mock_dynamodb
    def test_update_item_success(self, db_client):
        """Test successful item update."""
        # Create mock table
        dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
        table = dynamodb.create_table(
            TableName='test-table',
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Mock the table
        db_client.table = table
        
        # Insert initial data
        initial_item = {
            'PK': 'TENANT#tenant-456#USER#123',
            'SK': 'PROFILE',
            'name': 'Test User',
            'email': '<EMAIL>'
        }
        table.put_item(Item=initial_item)
        
        # Update data
        updates = {
            'name': 'Updated User',
            'age': 30
        }
        
        # Execute
        result = db_client.update_item(
            pk="USER#123",
            sk="PROFILE",
            updates=updates,
            tenant_id="tenant-456"
        )
        
        # Verify
        assert result is True
        
        # Verify the update
        updated_item = db_client.get_item(
            pk="USER#123",
            sk="PROFILE",
            tenant_id="tenant-456"
        )
        assert updated_item['name'] == 'Updated User'
        assert updated_item['age'] == 30
    
    @mock_dynamodb
    def test_delete_item_success(self, db_client):
        """Test successful item deletion."""
        # Create mock table
        dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
        table = dynamodb.create_table(
            TableName='test-table',
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Mock the table
        db_client.table = table
        
        # Insert test data
        test_item = {
            'PK': 'TENANT#tenant-456#USER#123',
            'SK': 'PROFILE',
            'name': 'Test User'
        }
        table.put_item(Item=test_item)
        
        # Execute
        result = db_client.delete_item(
            pk="USER#123",
            sk="PROFILE",
            tenant_id="tenant-456"
        )
        
        # Verify
        assert result is True
        
        # Verify deletion
        deleted_item = db_client.get_item(
            pk="USER#123",
            sk="PROFILE",
            tenant_id="tenant-456"
        )
        assert deleted_item is None
    
    def test_query_with_tenant_isolation(self, db_client):
        """Test that queries are properly isolated by tenant."""
        with patch.object(db_client.table, 'query') as mock_query:
            mock_query.return_value = {'Items': []}
            
            # Execute query
            db_client.query(
                pk="USER#123",
                tenant_id="tenant-456"
            )
            
            # Verify tenant context was added
            mock_query.assert_called_once()
            call_args = mock_query.call_args[1]
            assert call_args['KeyConditionExpression'].values[0].value == 'TENANT#tenant-456#USER#123'
    
    def test_batch_operations_maintain_tenant_isolation(self, db_client):
        """Test that batch operations maintain tenant isolation."""
        items = [
            {'pk': 'USER#1', 'sk': 'PROFILE', 'data': {'name': 'User 1'}},
            {'pk': 'USER#2', 'sk': 'PROFILE', 'data': {'name': 'User 2'}}
        ]
        
        with patch.object(db_client.table, 'batch_writer') as mock_batch:
            mock_context = MagicMock()
            mock_batch.return_value.__enter__.return_value = mock_context
            
            # Execute batch put
            result = db_client.batch_put_items(items, tenant_id="tenant-456")
            
            # Verify tenant context was added to all items
            assert result is True
            assert mock_context.put_item.call_count == 2
            
            # Check that tenant context was added
            for call in mock_context.put_item.call_args_list:
                item = call[1]['Item']
                assert item['PK'].startswith('TENANT#tenant-456#')


if __name__ == '__main__':
    pytest.main([__file__])
