# tests/unit/shared/test_validators.py
# Implementado según "Testing Guidelines" - Unit Tests

"""
Unit tests for shared validators.
Tests validation functions and Pydantic models.
"""

import pytest
import sys
import os
from datetime import datetime
from decimal import Decimal
from pydantic import ValidationError
from pathlib import Path

# Add shared path explicitly
project_root = Path(__file__).parent.parent.parent.parent
shared_path = str(project_root / "shared" / "python")
if shared_path not in sys.path:
    sys.path.insert(0, shared_path)

from shared.validators import (
    validate_email_address, validate_uuid, validate_tenant_name,
    RegisterRequestValidator, LoginRequestValidator, CreateSubscriptionRequestValidator
)
from shared.exceptions import ValidationException


class TestValidators:
    """Unit tests for shared validators."""
    
    def test_validate_email_valid(self):
        """Test email validation with valid emails."""
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]

        for email in valid_emails:
            result = validate_email_address(email, check_deliverability=False)
            assert '@' in result
    
    def test_validate_email_invalid(self):
        """Test email validation with invalid emails."""
        invalid_emails = [
            'invalid-email',
            'test@',
            '@example.com',
            'test@example',
            'test@.com'
        ]

        for email in invalid_emails:
            with pytest.raises(Exception):  # Could be ValidationException or other
                validate_email_address(email)
    
    def test_validate_uuid_valid(self):
        """Test UUID validation with valid UUIDs."""
        import uuid
        valid_uuid = str(uuid.uuid4())

        result = validate_uuid(valid_uuid)
        assert result == valid_uuid

    def test_validate_uuid_invalid(self):
        """Test UUID validation with invalid UUIDs."""
        invalid_uuids = [
            'invalid-uuid',
            '123',
            '',
            'not-a-uuid-at-all'
        ]

        for invalid_uuid in invalid_uuids:
            with pytest.raises(ValidationException):
                validate_uuid(invalid_uuid)
    
    def test_validate_tenant_name_valid(self):
        """Test tenant name validation with valid names."""
        valid_names = [
            'Test Company',
            'ABC Corp',
            'Company-123',
            'Test Company Inc.',
            'My Startup'
        ]

        for name in valid_names:
            result = validate_tenant_name(name)
            assert len(result) > 0

    def test_validate_tenant_name_invalid(self):
        """Test tenant name validation with invalid names."""
        invalid_names = [
            '',                 # Empty
            '   ',             # Only whitespace
        ]

        for name in invalid_names:
            with pytest.raises(ValidationException):
                validate_tenant_name(name)
    """Unit tests for RegisterUserRequest validator."""
    
    def test_register_user_request_valid(self):
        """Test valid user registration request."""
        valid_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'user_name': 'Test User',
            'tenant_name': 'Test Company'
        }
        
        request = RegisterUserRequest(**valid_data)

        assert request.email == '<EMAIL>'
        assert request.password == 'TestPassword123!'
        assert request.user_name == 'Test User'
        assert request.tenant_name == 'Test Company'
        assert request.phone is None
    
    def test_register_user_request_with_phone(self):
        """Test user registration request with phone."""
        valid_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'user_name': 'Test User',
            'tenant_name': 'Test Company',
            'phone': '+1234567890'
        }
        
        request = RegisterUserRequest(**valid_data)
        assert request.phone == '+1234567890'
    
    def test_register_user_request_invalid_email(self):
        """Test user registration with invalid email."""
        invalid_data = {
            'email': 'invalid-email',
            'password': 'TestPassword123!',
            'user_name': 'Test User',
            'tenant_name': 'Test Company'
        }
        
        with pytest.raises(ValidationError):
            RegisterUserRequest(**invalid_data)
    
    def test_register_user_request_invalid_password(self):
        """Test user registration with invalid password."""
        invalid_data = {
            'email': '<EMAIL>',
            'password': 'weak',
            'user_name': 'Test User',
            'tenant_name': 'Test Company'
        }
        
        with pytest.raises(ValidationError):
            RegisterUserRequest(**invalid_data)
    
    def test_register_user_request_missing_fields(self):
        """Test user registration with missing required fields."""
        # Missing email
        with pytest.raises(ValidationError):
            RegisterUserRequest(
                password='TestPassword123!',
                user_name='Test User',
                tenant_name='Test Company'
            )
        
        # Missing password
        with pytest.raises(ValidationError):
            RegisterUserRequest(
                email='<EMAIL>',
                name='Test User',
                company='Test Company'
            )
        
        # Missing name
        with pytest.raises(ValidationError):
            RegisterUserRequest(
                email='<EMAIL>',
                password='TestPassword123!',
                company='Test Company'
            )
        
        # Missing company
        with pytest.raises(ValidationError):
            RegisterUserRequest(
                email='<EMAIL>',
                password='TestPassword123!',
                name='Test User'
            )


class TestLoginRequest:
    """Unit tests for LoginRequest validator."""
    
    def test_login_request_valid(self):
        """Test valid login request."""
        valid_data = {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        }
        
        request = LoginRequest(**valid_data)
        
        assert request.email == '<EMAIL>'
        assert request.password == 'TestPassword123!'
    
    def test_login_request_invalid_email(self):
        """Test login request with invalid email."""
        invalid_data = {
            'email': 'invalid-email',
            'password': 'TestPassword123!'
        }
        
        with pytest.raises(ValidationError):
            LoginRequest(**invalid_data)
    
    def test_login_request_missing_fields(self):
        """Test login request with missing fields."""
        # Missing email
        with pytest.raises(ValidationError):
            LoginRequest(password='TestPassword123!')
        
        # Missing password
        with pytest.raises(ValidationError):
            LoginRequest(email='<EMAIL>')


class TestCreateSubscriptionRequest:
    """Unit tests for CreateSubscriptionRequest validator."""
    
    def test_create_subscription_request_valid(self):
        """Test valid subscription creation request."""
        valid_data = {
            'plan_id': 'plan_basic_123',
            'billing_interval': 'MONTHLY'
        }
        
        request = CreateSubscriptionRequest(**valid_data)
        
        assert request.plan_id == 'plan_basic_123'
        assert request.billing_interval == 'MONTHLY'
        assert request.payment_method_id is None
    
    def test_create_subscription_request_with_payment_method(self):
        """Test subscription creation with payment method."""
        valid_data = {
            'plan_id': 'plan_basic_123',
            'billing_interval': 'YEARLY',
            'payment_method_id': 'pm_test123'
        }
        
        request = CreateSubscriptionRequest(**valid_data)
        assert request.payment_method_id == 'pm_test123'
        assert request.billing_interval == 'YEARLY'
    
    def test_create_subscription_request_invalid_billing_interval(self):
        """Test subscription creation with invalid billing interval."""
        invalid_data = {
            'plan_id': 'plan_basic_123',
            'billing_interval': 'WEEKLY'  # Invalid
        }
        
        with pytest.raises(ValidationError):
            CreateSubscriptionRequest(**invalid_data)
    
    def test_create_subscription_request_missing_fields(self):
        """Test subscription creation with missing fields."""
        # Missing plan_id
        with pytest.raises(ValidationError):
            CreateSubscriptionRequest(billing_interval='MONTHLY')
        
        # Missing billing_interval
        with pytest.raises(ValidationError):
            CreateSubscriptionRequest(plan_id='plan_basic_123')


class TestUpdateUserRequest:
    """Unit tests for UpdateUserRequest validator."""
    
    def test_update_user_request_valid(self):
        """Test valid user update request."""
        valid_data = {
            'name': 'Updated Name',
            'phone': '+1234567890'
        }
        
        request = UpdateUserRequest(**valid_data)
        
        assert request.name == 'Updated Name'
        assert request.phone == '+1234567890'
        assert request.role is None
        assert request.status is None
    
    def test_update_user_request_role_and_status(self):
        """Test user update with role and status."""
        valid_data = {
            'name': 'Updated Name',
            'role': 'MEMBER',
            'status': 'ACTIVE'
        }
        
        request = UpdateUserRequest(**valid_data)
        
        assert request.role == 'MEMBER'
        assert request.status == 'ACTIVE'
    
    def test_update_user_request_invalid_role(self):
        """Test user update with invalid role."""
        invalid_data = {
            'name': 'Updated Name',
            'role': 'INVALID_ROLE'
        }
        
        with pytest.raises(ValidationError):
            UpdateUserRequest(**invalid_data)
    
    def test_update_user_request_invalid_status(self):
        """Test user update with invalid status."""
        invalid_data = {
            'name': 'Updated Name',
            'status': 'INVALID_STATUS'
        }
        
        with pytest.raises(ValidationError):
            UpdateUserRequest(**invalid_data)
    
    def test_update_user_request_empty(self):
        """Test user update with no fields (should be valid)."""
        request = UpdateUserRequest()
        
        assert request.name is None
        assert request.phone is None
        assert request.role is None
        assert request.status is None
