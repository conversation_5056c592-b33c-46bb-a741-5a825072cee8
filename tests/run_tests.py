#!/usr/bin/env python3
# tests/run_tests.py
# Automated test runner for the logistics AI platform

"""
Automated test runner for the logistics AI platform.
Executes different test suites with proper reporting and coverage analysis.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """Test runner for the logistics AI platform."""
    
    def __init__(self):
        self.project_root = project_root
        self.tests_dir = self.project_root / "tests"
        self.results = {}
    
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run unit tests."""
        print("🧪 Running Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.tests_dir / "unit"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "--cov=services",
            "--cov=shared",
            "--cov=infrastructure",
            "--cov-report=term-missing",
            "--cov-report=html:tests/coverage/unit",
            "-m", "unit"
        ]
        
        return self._run_pytest_command(cmd, "unit")
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run integration tests."""
        print("🔗 Running Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.tests_dir / "integration"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "-m", "integration"
        ]
        
        return self._run_pytest_command(cmd, "integration")
    
    def run_e2e_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run end-to-end tests."""
        print("🌐 Running End-to-End Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.tests_dir / "e2e"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "-m", "e2e"
        ]
        
        return self._run_pytest_command(cmd, "e2e")
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run performance tests."""
        print("⚡ Running Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.tests_dir),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "-m", "performance"
        ]
        
        return self._run_pytest_command(cmd, "performance")
    
    def run_all_tests(self, verbose: bool = False, skip_slow: bool = False) -> Dict[str, Any]:
        """Run all test suites."""
        print("🚀 Running All Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.tests_dir),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=20",
            "--cov=services",
            "--cov=shared",
            "--cov=infrastructure",
            "--cov-report=term-missing",
            "--cov-report=html:tests/coverage/all",
            "--cov-report=xml:tests/coverage/coverage.xml"
        ]
        
        if skip_slow:
            cmd.extend(["-m", "not slow"])
        
        return self._run_pytest_command(cmd, "all")
    
    def run_specific_test(self, test_path: str, verbose: bool = False) -> Dict[str, Any]:
        """Run a specific test file or function."""
        print(f"🎯 Running Specific Test: {test_path}")
        
        cmd = [
            "python", "-m", "pytest",
            test_path,
            "-v" if verbose else "-q",
            "--tb=short"
        ]
        
        return self._run_pytest_command(cmd, "specific")
    
    def _run_pytest_command(self, cmd: List[str], test_type: str) -> Dict[str, Any]:
        """Run a pytest command and capture results."""
        start_time = time.time()
        
        try:
            # Set environment variables for testing
            env = os.environ.copy()
            env.update({
                'PYTHONPATH': str(self.project_root),
                'AWS_DEFAULT_REGION': 'us-east-1',
                'STAGE': 'test'
            })
            
            result = subprocess.run(
                cmd,
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                env=env
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Parse pytest output for test counts
            output_lines = result.stdout.split('\n')
            test_summary = self._parse_pytest_output(output_lines)
            
            test_result = {
                'test_type': test_type,
                'success': result.returncode == 0,
                'duration': duration,
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                **test_summary
            }
            
            self.results[test_type] = test_result
            
            # Print summary
            if test_result['success']:
                print(f"✅ {test_type.title()} tests passed in {duration:.2f}s")
                if test_summary.get('passed', 0) > 0:
                    print(f"   Passed: {test_summary['passed']}")
                if test_summary.get('failed', 0) > 0:
                    print(f"   Failed: {test_summary['failed']}")
                if test_summary.get('skipped', 0) > 0:
                    print(f"   Skipped: {test_summary['skipped']}")
            else:
                print(f"❌ {test_type.title()} tests failed in {duration:.2f}s")
                print(f"   Return code: {result.returncode}")
                if result.stderr:
                    print(f"   Error: {result.stderr[:200]}...")
            
            return test_result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            test_result = {
                'test_type': test_type,
                'success': False,
                'duration': duration,
                'error': str(e),
                'passed': 0,
                'failed': 0,
                'skipped': 0
            }
            
            self.results[test_type] = test_result
            print(f"❌ {test_type.title()} tests failed with exception: {e}")
            
            return test_result
    
    def _parse_pytest_output(self, output_lines: List[str]) -> Dict[str, int]:
        """Parse pytest output to extract test counts."""
        summary = {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': 0}
        
        for line in output_lines:
            line = line.strip()
            
            # Look for summary line like "5 passed, 2 failed, 1 skipped in 10.23s"
            if ' passed' in line or ' failed' in line or ' skipped' in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed' and i > 0:
                        try:
                            summary['passed'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'failed' and i > 0:
                        try:
                            summary['failed'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'skipped' and i > 0:
                        try:
                            summary['skipped'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'error' and i > 0:
                        try:
                            summary['errors'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
        
        return summary
    
    def generate_report(self) -> None:
        """Generate a comprehensive test report."""
        print("\n" + "="*60)
        print("📊 TEST EXECUTION REPORT")
        print("="*60)
        
        total_duration = 0
        total_passed = 0
        total_failed = 0
        total_skipped = 0
        
        for test_type, result in self.results.items():
            print(f"\n{test_type.upper()} TESTS:")
            print(f"  Status: {'✅ PASSED' if result['success'] else '❌ FAILED'}")
            print(f"  Duration: {result['duration']:.2f}s")
            print(f"  Passed: {result.get('passed', 0)}")
            print(f"  Failed: {result.get('failed', 0)}")
            print(f"  Skipped: {result.get('skipped', 0)}")
            
            total_duration += result['duration']
            total_passed += result.get('passed', 0)
            total_failed += result.get('failed', 0)
            total_skipped += result.get('skipped', 0)
        
        print(f"\nOVERALL SUMMARY:")
        print(f"  Total Duration: {total_duration:.2f}s")
        print(f"  Total Passed: {total_passed}")
        print(f"  Total Failed: {total_failed}")
        print(f"  Total Skipped: {total_skipped}")
        
        success_rate = (total_passed / (total_passed + total_failed) * 100) if (total_passed + total_failed) > 0 else 0
        print(f"  Success Rate: {success_rate:.1f}%")
        
        overall_success = all(result['success'] for result in self.results.values())
        print(f"  Overall Status: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
        
        print("="*60)


def main():
    """Main function for test runner."""
    parser = argparse.ArgumentParser(description="Run tests for the logistics AI platform")
    parser.add_argument("--type", choices=["unit", "integration", "e2e", "performance", "all"], 
                       default="all", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--skip-slow", action="store_true", help="Skip slow tests")
    parser.add_argument("--test", help="Run specific test file or function")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    print("🧪 Logistics AI Platform Test Runner")
    print("="*50)
    
    if args.test:
        runner.run_specific_test(args.test, args.verbose)
    elif args.type == "unit":
        runner.run_unit_tests(args.verbose)
    elif args.type == "integration":
        runner.run_integration_tests(args.verbose)
    elif args.type == "e2e":
        runner.run_e2e_tests(args.verbose)
    elif args.type == "performance":
        runner.run_performance_tests(args.verbose)
    elif args.type == "all":
        runner.run_all_tests(args.verbose, args.skip_slow)
    
    runner.generate_report()
    
    # Exit with error code if any tests failed
    overall_success = all(result['success'] for result in runner.results.values())
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()
