#!/usr/bin/env python3
# tests/test_business_logic_improvements.py
# Test business logic improvements implemented in Phase 5

"""
Test business logic improvements:
1. Standardized error handling
2. Unified validation system
3. Structured logging
4. Business logic decorator
"""

import os
import json
import pytest
from unittest.mock import patch, MagicMock
from typing import Dict, Any

# Set JWT secret before importing auth modules
os.environ['JWT_SECRET'] = 'test-jwt-secret-for-testing-purposes-very-long-and-secure'

from shared.error_handler import ErrorHandler, with_error_handling
from shared.validation_manager import ValidationManager, with_validation
from shared.business_logic_decorator import business_logic_handler
from shared.exceptions import ValidationException, AuthenticationException
from shared.validators import LoginRequestValidator


class TestBusinessLogicImprovements:
    """Test business logic improvements implemented in Phase 5."""
    
    def test_error_handler_validation_exception(self):
        """Test error handler with validation exception."""
        error_handler = ErrorHandler("test-service", "test-operation")
        
        exception = ValidationException(
            "Test validation error",
            error_code="TEST_VALIDATION_ERROR"
        )
        
        response = error_handler.handle_exception(exception, request_id="test-123")
        
        assert response["statusCode"] == 422
        assert "Test validation error" in response["body"]
    
    def test_error_handler_authentication_exception(self):
        """Test error handler with authentication exception."""
        error_handler = ErrorHandler("test-service", "test-operation")
        
        exception = AuthenticationException(
            "Test auth error",
            error_code="TEST_AUTH_ERROR"
        )
        
        response = error_handler.handle_exception(exception, request_id="test-123")
        
        assert response["statusCode"] == 401
        assert "Test auth error" in response["body"]
    
    def test_error_handler_unexpected_exception(self):
        """Test error handler with unexpected exception."""
        error_handler = ErrorHandler("test-service", "test-operation")
        
        exception = RuntimeError("Unexpected error")
        
        response = error_handler.handle_exception(exception, request_id="test-123")
        
        assert response["statusCode"] == 500
        assert "unexpected error" in response["body"].lower()
    
    def test_validation_manager_valid_request(self):
        """Test validation manager with valid request."""
        validation_manager = ValidationManager("test-service")
        
        event = {
            "body": json.dumps({
                "email": "<EMAIL>",
                "password": "testpassword"
            })
        }
        
        result = validation_manager.validate_request_body(event, LoginRequestValidator)
        
        assert result is not None
        assert result["email"] == "<EMAIL>"
        assert result["password"] == "testpassword"
    
    def test_validation_manager_invalid_json(self):
        """Test validation manager with invalid JSON."""
        validation_manager = ValidationManager("test-service")
        
        event = {
            "body": "invalid json"
        }
        
        with pytest.raises(ValidationException) as exc_info:
            validation_manager.validate_request_body(event, LoginRequestValidator)
        
        assert "Invalid JSON format" in str(exc_info.value)
    
    def test_validation_manager_missing_body(self):
        """Test validation manager with missing body."""
        validation_manager = ValidationManager("test-service")
        
        event = {}
        
        with pytest.raises(ValidationException) as exc_info:
            validation_manager.validate_request_body(event, LoginRequestValidator)
        
        assert "Request body is required" in str(exc_info.value)
    
    def test_validation_manager_path_parameters(self):
        """Test validation manager path parameter validation."""
        validation_manager = ValidationManager("test-service")
        
        event = {
            "pathParameters": {
                "user_id": "123",
                "tenant_id": "456"
            }
        }
        
        result = validation_manager.validate_path_parameters(
            event, 
            required_params=["user_id", "tenant_id"]
        )
        
        assert result["user_id"] == "123"
        assert result["tenant_id"] == "456"
    
    def test_validation_manager_missing_path_parameter(self):
        """Test validation manager with missing path parameter."""
        validation_manager = ValidationManager("test-service")
        
        event = {
            "pathParameters": {
                "user_id": "123"
            }
        }
        
        with pytest.raises(ValidationException) as exc_info:
            validation_manager.validate_path_parameters(
                event, 
                required_params=["user_id", "tenant_id"]
            )
        
        assert "Missing required path parameter: tenant_id" in str(exc_info.value)
    
    def test_validation_manager_query_parameters(self):
        """Test validation manager query parameter validation."""
        validation_manager = ValidationManager("test-service")
        
        event = {
            "queryStringParameters": {
                "page": "1",
                "limit": "10"
            }
        }
        
        result = validation_manager.validate_query_parameters(
            event, 
            allowed_params=["page", "limit", "sort"]
        )
        
        assert result["page"] == "1"
        assert result["limit"] == "10"
    
    def test_validation_manager_unknown_query_parameter(self):
        """Test validation manager with unknown query parameter."""
        validation_manager = ValidationManager("test-service")
        
        event = {
            "queryStringParameters": {
                "page": "1",
                "unknown_param": "value"
            }
        }
        
        with pytest.raises(ValidationException) as exc_info:
            validation_manager.validate_query_parameters(
                event, 
                allowed_params=["page", "limit"]
            )
        
        assert "Unknown query parameter: unknown_param" in str(exc_info.value)
    
    def test_with_error_handling_decorator(self):
        """Test with_error_handling decorator."""
        
        @with_error_handling("test-service", "test-operation")
        def test_handler(event, context):
            if event.get("should_fail"):
                raise ValidationException("Test error")
            return {"statusCode": 200, "body": "success"}
        
        # Test successful execution
        result = test_handler({"should_fail": False}, None)
        assert result["statusCode"] == 200
        
        # Test error handling
        result = test_handler({"should_fail": True}, None)
        assert result["statusCode"] == 422
    
    def test_with_validation_decorator(self):
        """Test with_validation decorator."""
        
        @with_validation(
            service_name="test-service",
            body_validator=LoginRequestValidator
        )
        def test_handler(event, context):
            validated_data = event["validated_body"]
            return {
                "statusCode": 200, 
                "body": json.dumps({"email": validated_data["email"]})
            }
        
        # Test with valid data
        event = {
            "body": json.dumps({
                "email": "<EMAIL>",
                "password": "testpassword"
            })
        }
        
        result = test_handler(event, None)
        assert result["statusCode"] == 200
        
        response_body = json.loads(result["body"])
        assert response_body["email"] == "<EMAIL>"
    
    @patch('shared.auth.get_auth_context')
    def test_business_logic_decorator_public(self, mock_get_auth_context):
        """Test business logic decorator for public endpoint."""
        
        @business_logic_handler(
            service_name="test-service",
            operation_name="test-operation",
            require_auth=False
        )
        def test_handler(event, context):
            return {"statusCode": 200, "body": "success"}
        
        result = test_handler({}, None)
        assert result["statusCode"] == 200
        
        # Should not call auth context for public endpoints
        mock_get_auth_context.assert_not_called()
    
    @patch('shared.auth.get_auth_context')
    def test_business_logic_decorator_authenticated(self, mock_get_auth_context):
        """Test business logic decorator for authenticated endpoint."""
        
        # Mock auth context
        mock_auth_context = MagicMock()
        mock_auth_context.user_id = "user-123"
        mock_auth_context.tenant_id = "tenant-123"
        mock_auth_context.has_role.return_value = True
        mock_get_auth_context.return_value = mock_auth_context
        
        @business_logic_handler(
            service_name="test-service",
            operation_name="test-operation",
            require_auth=True
        )
        def test_handler(event, context):
            auth_context = event["auth_context"]
            return {
                "statusCode": 200, 
                "body": json.dumps({"user_id": auth_context.user_id})
            }
        
        result = test_handler({}, None)
        assert result["statusCode"] == 200
        
        response_body = json.loads(result["body"])
        assert response_body["user_id"] == "user-123"
    
    @patch('shared.auth.get_auth_context')
    def test_business_logic_decorator_no_auth(self, mock_get_auth_context):
        """Test business logic decorator when authentication fails."""
        
        mock_get_auth_context.return_value = None
        
        @business_logic_handler(
            service_name="test-service",
            operation_name="test-operation",
            require_auth=True
        )
        def test_handler(event, context):
            return {"statusCode": 200, "body": "success"}
        
        result = test_handler({}, None)
        assert result["statusCode"] == 403  # Authorization error
    
    def test_business_logic_decorator_with_validation(self):
        """Test business logic decorator with validation."""
        
        @business_logic_handler(
            service_name="test-service",
            operation_name="test-operation",
            require_auth=False,
            body_validator=LoginRequestValidator
        )
        def test_handler(event, context):
            validated_data = event["validated_body"]
            return {
                "statusCode": 200,
                "body": json.dumps({"email": validated_data["email"]})
            }
        
        # Test with valid data
        event = {
            "body": json.dumps({
                "email": "<EMAIL>",
                "password": "testpassword"
            })
        }
        
        result = test_handler(event, None)
        assert result["statusCode"] == 200
        
        # Test with invalid data
        event = {
            "body": json.dumps({
                "email": "invalid-email"
            })
        }
        
        result = test_handler(event, None)
        assert result["statusCode"] == 422  # Validation error


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
