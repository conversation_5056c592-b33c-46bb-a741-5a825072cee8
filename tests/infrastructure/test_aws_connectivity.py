# tests/infrastructure/test_aws_connectivity.py
# Implementado según "Testing Guidelines" y Fase 1 Plan

"""
AWS connectivity and service configuration tests.
Validates that all AWS services are properly configured and accessible.
"""

import pytest
import boto3
import os
from typing import Dict, Any
from moto import mock_aws
import json

# Add path for shared layer
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'python'))

from shared.config import get_settings
from shared.database import db_client
from shared.secrets import get_integration_credentials
from shared.exceptions import ConfigurationException


class TestAWSConnectivity:
    """Test AWS service connectivity and configuration."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.settings = get_settings()
        self.aws_region = os.getenv('AWS_REGION', 'us-east-1')
    
    def test_aws_region_configuration(self):
        """Test AWS region is properly configured."""
        assert self.aws_region is not None
        assert len(self.aws_region) > 0
        
        # Validate region format
        valid_regions = [
            'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2',
            'eu-west-1', 'eu-west-2', 'eu-central-1',
            'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1'
        ]
        
        assert self.aws_region in valid_regions, f"Invalid AWS region: {self.aws_region}"
    
    @mock_aws
    def test_dynamodb_connectivity(self):
        """Test DynamoDB connectivity and basic operations."""
        # Create mock table
        dynamodb = boto3.resource('dynamodb', region_name=self.aws_region)
        table_name = os.getenv('DYNAMODB_TABLE_NAME', 'platform-test')
        
        table = dynamodb.create_table(
            TableName=table_name,
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'}
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        table.wait_until_exists()
        
        # Test basic CRUD operations
        test_item = {
            'PK': 'TEST#connectivity',
            'SK': 'METADATA',
            'test_field': 'connectivity_test',
            'created_at': 1234567890
        }
        
        # Test PUT
        table.put_item(Item=test_item)
        
        # Test GET
        response = table.get_item(
            Key={'PK': 'TEST#connectivity', 'SK': 'METADATA'}
        )
        
        assert 'Item' in response
        assert response['Item']['test_field'] == 'connectivity_test'
        
        # Test UPDATE
        table.update_item(
            Key={'PK': 'TEST#connectivity', 'SK': 'METADATA'},
            UpdateExpression='SET test_field = :val',
            ExpressionAttributeValues={':val': 'updated_value'}
        )
        
        # Test DELETE
        table.delete_item(
            Key={'PK': 'TEST#connectivity', 'SK': 'METADATA'}
        )
        
        # Verify deletion
        response = table.get_item(
            Key={'PK': 'TEST#connectivity', 'SK': 'METADATA'}
        )
        
        assert 'Item' not in response
    
    @mock_aws
    def test_s3_connectivity(self):
        """Test S3 connectivity and basic operations."""
        s3_client = boto3.client('s3', region_name=self.aws_region)
        bucket_name = f"test-bucket-{self.settings.environment}"
        
        # Create test bucket
        if self.aws_region == 'us-east-1':
            s3_client.create_bucket(Bucket=bucket_name)
        else:
            s3_client.create_bucket(
                Bucket=bucket_name,
                CreateBucketConfiguration={'LocationConstraint': self.aws_region}
            )
        
        # Test PUT object
        test_key = 'test-folder/test-file.txt'
        test_content = b'Test content for S3 connectivity'
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=test_key,
            Body=test_content,
            ContentType='text/plain'
        )
        
        # Test GET object
        response = s3_client.get_object(
            Bucket=bucket_name,
            Key=test_key
        )
        
        assert response['Body'].read() == test_content
        assert response['ContentType'] == 'text/plain'
        
        # Test LIST objects
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix='test-folder/'
        )
        
        assert 'Contents' in response
        assert len(response['Contents']) == 1
        assert response['Contents'][0]['Key'] == test_key
        
        # Test DELETE object
        s3_client.delete_object(
            Bucket=bucket_name,
            Key=test_key
        )
        
        # Verify deletion
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix='test-folder/'
        )
        
        assert 'Contents' not in response
    
    @mock_aws
    def test_secrets_manager_connectivity(self):
        """Test AWS Secrets Manager connectivity."""
        secrets_client = boto3.client('secretsmanager', region_name=self.aws_region)
        
        # Create test secret
        secret_name = f"test-secret-{self.settings.environment}"
        secret_value = {
            'test_key': 'test_value',
            'api_key': 'test_api_key_123'
        }
        
        secrets_client.create_secret(
            Name=secret_name,
            SecretString=json.dumps(secret_value)
        )
        
        # Test GET secret
        response = secrets_client.get_secret_value(SecretId=secret_name)
        
        assert 'SecretString' in response
        retrieved_secret = json.loads(response['SecretString'])
        assert retrieved_secret['test_key'] == 'test_value'
        assert retrieved_secret['api_key'] == 'test_api_key_123'
        
        # Test UPDATE secret
        updated_value = {
            'test_key': 'updated_value',
            'api_key': 'updated_api_key_456'
        }
        
        secrets_client.update_secret(
            SecretId=secret_name,
            SecretString=json.dumps(updated_value)
        )
        
        # Verify update
        response = secrets_client.get_secret_value(SecretId=secret_name)
        retrieved_secret = json.loads(response['SecretString'])
        assert retrieved_secret['test_key'] == 'updated_value'
    
    def test_database_client_configuration(self):
        """Test database client configuration."""
        # Test that db_client is properly configured
        assert db_client is not None
        
        # Test configuration attributes
        assert hasattr(db_client, 'dynamodb')
        assert hasattr(db_client, 'table')
        assert hasattr(db_client, 'config')

        # Test that table name is set
        assert db_client.config["table_name"] is not None
        assert len(db_client.config["table_name"]) > 0
    
    def test_settings_configuration(self):
        """Test application settings configuration."""
        settings = get_settings()
        
        # Test required settings
        assert settings.project_name is not None
        assert settings.environment is not None
        assert settings.aws_region is not None
        
        # Test environment-specific settings
        valid_environments = ['dev', 'staging', 'prod', 'test']
        assert settings.environment in valid_environments
        
        # Test project name format
        assert len(settings.project_name) > 0
        assert settings.project_name.replace('-', '').replace('_', '').isalnum()
    
    def test_logging_configuration(self):
        """Test logging configuration."""
        from shared.logger import logger, lambda_logger
        
        # Test logger instances exist
        assert logger is not None
        assert lambda_logger is not None
        
        # Test basic logging functionality
        try:
            logger.info("Test log message")
            lambda_logger.info("Test lambda log message")
        except Exception as e:
            pytest.fail(f"Logging configuration failed: {str(e)}")
    
    def test_exception_handling_configuration(self):
        """Test exception handling configuration."""
        from shared.exceptions import (
            PlatformException,
            ValidationException,
            AuthenticationException,
            AuthorizationException,
            ResourceNotFoundException,
            PaymentException
        )
        
        # Test exception classes can be instantiated
        exceptions_to_test = [
            (ValidationException, "Test validation error"),
            (AuthenticationException, "Test auth error"),
            (AuthorizationException, "Test authorization error"),
            (ResourceNotFoundException, "Test not found error"),
            (PaymentException, "Test payment error")
        ]
        
        for exception_class, message in exceptions_to_test:
            try:
                raise exception_class(message)
            except exception_class as e:
                assert str(e) == message
                assert hasattr(e, 'status_code')
                assert hasattr(e, 'error_code')
    
    def test_response_handling_configuration(self):
        """Test API response handling configuration."""
        from shared.responses import APIResponse
        
        # Test success response
        response = APIResponse.success(data={'test': 'data'})
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['test'] == 'data'
        
        # Test error response
        response = APIResponse.error(message="Test error", status_code=400)
        assert response['statusCode'] == 400
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert body['message'] == "Test error"
        
        # Test validation error response
        validation_errors = [{"field": "email", "message": "Invalid email"}]
        response = APIResponse.validation_error(
            message="Validation failed",
            validation_errors=validation_errors
        )
        assert response['statusCode'] == 422
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert body['validation_errors'] == validation_errors
    
    def test_cors_configuration(self):
        """Test CORS configuration."""
        from shared.responses import handle_cors_preflight
        
        response = handle_cors_preflight()
        
        # Test CORS headers
        required_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers',
            'Access-Control-Max-Age'
        ]
        
        for header in required_headers:
            assert header in response['headers']
        
        # Test specific CORS values
        assert response['headers']['Access-Control-Allow-Origin'] == '*'
        assert 'POST' in response['headers']['Access-Control-Allow-Methods']
        assert 'GET' in response['headers']['Access-Control-Allow-Methods']
        assert 'Authorization' in response['headers']['Access-Control-Allow-Headers']
        assert 'Content-Type' in response['headers']['Access-Control-Allow-Headers']


class TestEnvironmentSpecificConfiguration:
    """Test environment-specific configuration."""
    
    def test_development_environment_config(self):
        """Test development environment specific configuration."""
        if os.getenv('ENVIRONMENT') == 'dev':
            settings = get_settings()
            
            # Development should have debug enabled
            assert settings.debug_mode is True
            
            # Development should have relaxed CORS
            assert settings.cors_origins == ['*']
    
    def test_staging_environment_config(self):
        """Test staging environment specific configuration."""
        if os.getenv('ENVIRONMENT') == 'staging':
            settings = get_settings()
            
            # Staging should have debug disabled
            assert settings.debug_mode is False
            
            # Staging should have specific CORS origins
            assert '*' not in settings.cors_origins
    
    def test_production_environment_config(self):
        """Test production environment specific configuration."""
        if os.getenv('ENVIRONMENT') == 'prod':
            settings = get_settings()
            
            # Production should have debug disabled
            assert settings.debug_mode is False
            
            # Production should have strict CORS
            assert '*' not in settings.cors_origins
            
            # Production should have enhanced security
            assert settings.jwt_expiry_minutes <= 60  # Max 1 hour tokens
