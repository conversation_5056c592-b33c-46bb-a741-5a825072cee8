# tests/infrastructure/test_deployment.py
# Tests de infraestructura para Serverless Framework - Post migración

"""
Serverless infrastructure deployment tests.
Validates that all AWS resources are properly deployed via Serverless Framework.
"""

import pytest
import boto3
import os
from typing import Dict, Any
from moto import mock_aws
from unittest.mock import Mock, patch
import json

# Add path for shared layer
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'python'))

from shared.config import get_settings
from shared.database import db_client
from shared.exceptions import ConfigurationException


class TestServerlessDeployment:
    """Test cases for Serverless Framework deployment validation."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        # Set required environment variables for tests
        os.environ.setdefault('AWS_REGION', 'us-east-1')
        os.environ.setdefault('PROJECT_NAME', 'platform')
        os.environ.setdefault('DYNAMODB_TABLE_NAME', 'platform-main-test')

        self.settings = get_settings()
        self.aws_region = os.getenv('AWS_REGION', 'us-east-1')
        
    def test_environment_variables_configured(self):
        """Test that all required environment variables are set."""
        required_vars = [
            'AWS_REGION',
            'ENVIRONMENT',
            'PROJECT_NAME',
            'DYNAMODB_TABLE_NAME'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        assert not missing_vars, f"Missing required environment variables: {missing_vars}"
    
    def test_aws_credentials_configured(self):
        """Test that AWS credentials are properly configured."""
        try:
            # Test AWS credentials by creating a client
            sts_client = boto3.client('sts', region_name=self.aws_region)
            identity = sts_client.get_caller_identity()
            
            assert 'Account' in identity
            assert 'UserId' in identity
            assert identity['Account'] is not None
            
        except Exception as e:
            pytest.fail(f"AWS credentials not properly configured: {str(e)}")
    
    @mock_aws
    def test_dynamodb_table_configuration(self):
        """Test DynamoDB table exists and is properly configured."""
        # Create mock DynamoDB table for testing
        dynamodb = boto3.resource('dynamodb', region_name=self.aws_region)
        
        table_name = os.getenv('DYNAMODB_TABLE_NAME', 'platform-dev')
        
        # Create table with expected schema
        table = dynamodb.create_table(
            TableName=table_name,
            KeySchema=[
                {'AttributeName': 'PK', 'KeyType': 'HASH'},
                {'AttributeName': 'SK', 'KeyType': 'RANGE'}
            ],
            AttributeDefinitions=[
                {'AttributeName': 'PK', 'AttributeType': 'S'},
                {'AttributeName': 'SK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI1PK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI1SK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI2PK', 'AttributeType': 'S'},
                {'AttributeName': 'GSI2SK', 'AttributeType': 'S'}
            ],
            GlobalSecondaryIndexes=[
                {
                    'IndexName': 'GSI1',
                    'KeySchema': [
                        {'AttributeName': 'GSI1PK', 'KeyType': 'HASH'},
                        {'AttributeName': 'GSI1SK', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'}
                },
                {
                    'IndexName': 'GSI2',
                    'KeySchema': [
                        {'AttributeName': 'GSI2PK', 'KeyType': 'HASH'},
                        {'AttributeName': 'GSI2SK', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'}
                }
            ],
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Wait for table to be created
        table.wait_until_exists()
        
        # Validate table configuration
        table_description = table.meta.client.describe_table(TableName=table_name)
        table_info = table_description['Table']
        
        # Validate key schema
        assert len(table_info['KeySchema']) == 2
        assert table_info['KeySchema'][0]['AttributeName'] == 'PK'
        assert table_info['KeySchema'][1]['AttributeName'] == 'SK'
        
        # Validate GSI configuration
        assert len(table_info['GlobalSecondaryIndexes']) == 2
        
        gsi_names = [gsi['IndexName'] for gsi in table_info['GlobalSecondaryIndexes']]
        assert 'GSI1' in gsi_names
        assert 'GSI2' in gsi_names
        
        # Test database client connectivity
        try:
            # Test basic operations
            test_item = {
                'PK': 'TEST#123',
                'SK': 'METADATA',
                'test_field': 'test_value',
                'created_at': 1234567890
            }
            
            # This would normally use db_client, but we'll test the structure
            table.put_item(Item=test_item)
            
            response = table.get_item(
                Key={'PK': 'TEST#123', 'SK': 'METADATA'}
            )
            
            assert 'Item' in response
            assert response['Item']['test_field'] == 'test_value'
            
        except Exception as e:
            pytest.fail(f"DynamoDB operations failed: {str(e)}")
    
    @mock_aws
    def test_s3_bucket_configuration(self):
        """Test S3 bucket exists and is properly configured."""
        s3_client = boto3.client('s3', region_name=self.aws_region)
        
        bucket_name = f"{self.settings.project_name}-{self.settings.environment}-storage"
        
        # Create mock bucket
        if self.aws_region == 'us-east-1':
            s3_client.create_bucket(Bucket=bucket_name)
        else:
            s3_client.create_bucket(
                Bucket=bucket_name,
                CreateBucketConfiguration={'LocationConstraint': self.aws_region}
            )
        
        # Test bucket exists
        response = s3_client.list_buckets()
        bucket_names = [bucket['Name'] for bucket in response['Buckets']]
        assert bucket_name in bucket_names
        
        # Test bucket policies (mock validation)
        try:
            # Test basic S3 operations
            test_key = 'test-tenant/test-file.txt'
            test_content = b'test content'
            
            s3_client.put_object(
                Bucket=bucket_name,
                Key=test_key,
                Body=test_content
            )
            
            response = s3_client.get_object(
                Bucket=bucket_name,
                Key=test_key
            )
            
            assert response['Body'].read() == test_content
            
        except Exception as e:
            pytest.fail(f"S3 operations failed: {str(e)}")
    
    def test_secrets_manager_configuration(self):
        """Test AWS Secrets Manager configuration."""
        try:
            secrets_client = boto3.client('secretsmanager', region_name=self.aws_region)
            
            # Test that we can access secrets manager
            # This is a basic connectivity test
            response = secrets_client.list_secrets(MaxResults=1)
            assert 'SecretList' in response
            
        except Exception as e:
            # In test environment, this might not be available
            # We'll mark as expected failure for now
            pytest.skip(f"Secrets Manager not available in test environment: {str(e)}")
    
    def test_ses_configuration(self):
        """Test AWS SES configuration."""
        try:
            ses_client = boto3.client('ses', region_name=self.aws_region)
            
            # Test SES connectivity
            response = ses_client.get_send_quota()
            assert 'Max24HourSend' in response
            assert 'MaxSendRate' in response
            
        except Exception as e:
            # In test environment, this might not be available
            pytest.skip(f"SES not available in test environment: {str(e)}")
    
    def test_lambda_function_configuration(self):
        """Test Lambda function configuration (mock)."""
        # This would test actual Lambda functions in real deployment
        # For now, we'll test the handler imports work correctly

        try:
            # Mock Stripe credentials to avoid initialization errors
            with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
                mock_secrets.return_value = {
                    'publishable_key': 'pk_test_123',
                    'secret_key': 'sk_test_123',
                    'webhook_secret': 'whsec_test_123'
                }

                # Test auth handlers can be imported
                # Add path for auth service
                import sys
                import os
                sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'services', 'auth', 'src'))

                from handlers import register, login, refresh, verify_email
                from handlers import forgot_password, reset_password, authorizer

                # Test that handlers have the required function
                assert hasattr(register, 'handler')
                assert hasattr(login, 'handler')
                assert hasattr(refresh, 'handler')
                assert hasattr(verify_email, 'handler')

        except ImportError as e:
            pytest.fail(f"Handler imports failed: {str(e)}")
    
    def test_api_gateway_configuration(self):
        """Test API Gateway configuration (mock)."""
        # This would test actual API Gateway in real deployment
        # For now, we'll test the handler structure
        
        expected_endpoints = [
            # Auth endpoints
            ('POST', '/auth/register'),
            ('POST', '/auth/login'),
            ('POST', '/auth/refresh'),
            ('POST', '/auth/verify-email'),
            ('POST', '/auth/forgot-password'),
            ('POST', '/auth/reset-password'),
            
            # Payment endpoints
            ('POST', '/payment/subscriptions'),
            ('GET', '/payment/subscription'),
            ('POST', '/payment/subscription/cancel'),
            ('GET', '/payment/plans'),
            ('POST', '/payment/webhooks/stripe')
        ]
        
        # Validate that we have handlers for all expected endpoints
        for method, path in expected_endpoints:
            # This is a structural test - in real deployment would test actual API
            assert True  # Placeholder for actual API Gateway tests
    
    def test_cloudwatch_configuration(self):
        """Test CloudWatch logging and monitoring configuration."""
        try:
            cloudwatch = boto3.client('cloudwatch', region_name=self.aws_region)
            logs_client = boto3.client('logs', region_name=self.aws_region)
            
            # Test CloudWatch connectivity
            response = cloudwatch.list_metrics(MaxRecords=1)
            assert 'Metrics' in response
            
            # Test CloudWatch Logs connectivity
            response = logs_client.describe_log_groups(limit=1)
            assert 'logGroups' in response
            
        except Exception as e:
            pytest.skip(f"CloudWatch not available in test environment: {str(e)}")
    
    def test_iam_roles_configuration(self):
        """Test IAM roles and policies configuration."""
        try:
            iam_client = boto3.client('iam', region_name=self.aws_region)
            
            # Test IAM connectivity
            response = iam_client.list_roles(MaxItems=1)
            assert 'Roles' in response
            
        except Exception as e:
            pytest.skip(f"IAM not available in test environment: {str(e)}")
    
    def test_kms_configuration(self):
        """Test KMS key configuration."""
        try:
            kms_client = boto3.client('kms', region_name=self.aws_region)
            
            # Test KMS connectivity
            response = kms_client.list_keys(Limit=1)
            assert 'Keys' in response
            
        except Exception as e:
            pytest.skip(f"KMS not available in test environment: {str(e)}")
    
    def test_terraform_outputs_validation(self):
        """Test that Terraform outputs are properly configured."""
        # This would validate actual Terraform outputs in real deployment
        # For now, we'll test the expected structure
        
        expected_outputs = [
            'dynamodb_table_name',
            's3_bucket_name',
            'api_gateway_url',
            'kms_key_arn'
        ]
        
        # In real deployment, would read from terraform output
        # For now, validate environment variables exist
        for output in expected_outputs:
            env_var = output.upper()
            # This is a placeholder - in real deployment would validate actual outputs
            assert True  # Placeholder for actual Terraform output validation


class TestDeploymentValidation:
    """Test deployment validation and health checks."""
    
    def test_health_check_endpoints(self):
        """Test that health check endpoints are accessible."""
        # This would test actual health check endpoints
        # For now, we'll test the handler structure exists
        
        try:
            # Test that we can import and call basic handlers
            from shared.responses import APIResponse
            
            # Test basic response structure
            response = APIResponse.success(data={'status': 'healthy'})
            assert response['statusCode'] == 200
            
            response_body = json.loads(response['body'])
            assert response_body['success'] is True
            assert response_body['data']['status'] == 'healthy'
            
        except Exception as e:
            pytest.fail(f"Health check validation failed: {str(e)}")
    
    def test_cors_configuration(self):
        """Test CORS configuration."""
        from shared.responses import handle_cors_preflight
        
        # Test CORS preflight response
        response = handle_cors_preflight()
        
        assert response['statusCode'] == 200
        assert 'Access-Control-Allow-Origin' in response['headers']
        assert 'Access-Control-Allow-Methods' in response['headers']
        assert 'Access-Control-Allow-Headers' in response['headers']
    
    def test_error_handling_configuration(self):
        """Test error handling configuration."""
        from shared.exceptions import PlatformException, ValidationException
        from shared.responses import APIResponse
        
        # Test exception handling
        try:
            raise ValidationException(
                "Test validation error",
                validation_errors=[{"field": "test", "message": "test error"}]
            )
        except ValidationException as e:
            response = APIResponse.validation_error(
                message=e.message,
                validation_errors=e.validation_errors
            )
            
            assert response['statusCode'] == 422
            response_body = json.loads(response['body'])
            assert response_body['success'] is False
            assert 'validation_errors' in response_body
