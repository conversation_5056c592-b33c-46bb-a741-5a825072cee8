#!/usr/bin/env python3
# tests/test_comprehensive_auth.py
# Comprehensive auth tests combining all functionality

"""
Comprehensive authentication tests covering all major functionality.
"""

import pytest
from unittest.mock import patch

from shared.auth import Pass<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthContext
from shared.exceptions import (
    InvalidTokenException,
    TokenExpiredException,
    WeakPasswordException
)

class TestComprehensiveAuth:
    """Comprehensive authentication tests."""
    
    def test_password_manager_complete_workflow(self):
        """Test complete password manager workflow."""
        pm = PasswordManager()
        
        # Test password generation
        password = pm.generate_secure_password(length=16)
        assert len(password) == 16
        assert isinstance(password, str)
        
        # Test token generation
        token = pm.generate_secure_token(length=32)
        assert len(token) > 30  # Base64 encoding may vary slightly
        assert isinstance(token, str)
        
        # Test password hashing and verification
        test_password = "TestPassword123!"
        hashed = pm.hash_password(test_password)
        assert pm.verify_password(test_password, hashed) is True
        assert pm.verify_password("wrong_password", hashed) is False
        
        # Test password strength validation
        strong_password = "StrongP@ssw0rd123!"
        is_strong, errors = pm.validate_password_strength(strong_password)
        assert is_strong is True
        assert len(errors) == 0
        
        weak_password = "weak"
        is_weak, weak_errors = pm.validate_password_strength(weak_password)
        assert is_weak is False
        assert len(weak_errors) > 0
    
    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_jwt_manager_complete_workflow(self, mock_get_secret):
        """Test complete JWT manager workflow."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"
        
        jwt_manager = JWTManager()
        
        # Test access token generation
        access_token = jwt_manager.generate_access_token(
            user_id="user-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MEMBER"
        )
        assert access_token is not None
        assert isinstance(access_token, str)
        
        # Test refresh token generation
        refresh_token = jwt_manager.generate_refresh_token(
            user_id="user-123",
            tenant_id="tenant-123"
        )
        assert refresh_token is not None
        assert isinstance(refresh_token, str)
        
        # Test token verification
        payload = jwt_manager.verify_token(access_token, 'access')
        assert payload["user_id"] == "user-123"
        assert payload["email"] == "<EMAIL>"
        assert payload["tenant_id"] == "tenant-123"
        assert payload["role"] == "MEMBER"
        
        # Test user context extraction
        context = jwt_manager.extract_user_context(access_token)
        assert isinstance(context, AuthContext)
        assert context.user_id == "user-123"
        assert context.email == "<EMAIL>"
        assert context.tenant_id == "tenant-123"
        assert context.role == "MEMBER"
        
        # Test invalid token handling
        invalid_context = jwt_manager.extract_user_context("invalid-token")
        assert invalid_context is None
        
        with pytest.raises(InvalidTokenException):
            jwt_manager.verify_token("invalid-token", 'access')
    
    def test_auth_context_complete_functionality(self):
        """Test complete AuthContext functionality."""
        # Test MEMBER role
        member_context = AuthContext(
            user_id="member-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MEMBER",
            token_payload={"jti": "token-123"}
        )
        
        assert member_context.user_id == "member-123"
        assert member_context.email == "<EMAIL>"
        assert member_context.tenant_id == "tenant-123"
        assert member_context.role == "MEMBER"
        assert member_context.is_authenticated is True
        
        # Test role hierarchy for MEMBER
        assert member_context.has_role("MEMBER") is True
        assert member_context.has_role("MASTER") is False
        
        # Test tenant access
        assert member_context.can_access_tenant("tenant-123") is True
        assert member_context.can_access_tenant("other-tenant") is False
        
        # Test MASTER role
        master_context = AuthContext(
            user_id="master-123",
            email="<EMAIL>",
            tenant_id="tenant-123",
            role="MASTER",
            token_payload={"jti": "token-456"}
        )
        
        # Test role hierarchy for MASTER
        assert master_context.has_role("MEMBER") is True  # MASTER has MEMBER permissions
        assert master_context.has_role("MASTER") is True
    
    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_token_refresh_workflow(self, mock_get_secret):
        """Test token refresh workflow."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"
        
        jwt_manager = JWTManager()
        
        # Generate initial refresh token
        refresh_token = jwt_manager.generate_refresh_token(
            user_id="user-123",
            tenant_id="tenant-123"
        )
        
        # Test refresh token workflow
        new_access_token, new_refresh_token = jwt_manager.refresh_access_token(refresh_token)
        
        assert new_access_token is not None
        assert new_refresh_token is not None
        assert isinstance(new_access_token, str)
        assert isinstance(new_refresh_token, str)
        
        # Verify new access token works
        payload = jwt_manager.verify_token(new_access_token, 'access')
        assert payload["user_id"] == "user-123"
        assert payload["tenant_id"] == "tenant-123"
    
    def test_error_handling(self):
        """Test error handling scenarios."""
        pm = PasswordManager()

        # Test weak password validation
        is_weak, errors = pm.validate_password_strength("weak")
        assert is_weak is False
        assert len(errors) > 0
    
    @patch('shared.auth.JWTManager._get_jwt_secret')
    def test_edge_cases(self, mock_get_secret):
        """Test edge cases and boundary conditions."""
        mock_get_secret.return_value = "test-secret-key-for-testing-purposes"
        
        jwt_manager = JWTManager()
        pm = PasswordManager()
        
        # Test empty password (should work but be weak)
        empty_hash = pm.hash_password("a")  # Minimum valid password
        assert pm.verify_password("a", empty_hash) is True
        
        # Test very long passwords
        long_password = "a" * 1000
        hashed_long = pm.hash_password(long_password)
        assert pm.verify_password(long_password, hashed_long) is True
        
        # Test special characters in passwords
        special_password = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        hashed_special = pm.hash_password(special_password)
        assert pm.verify_password(special_password, hashed_special) is True

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
