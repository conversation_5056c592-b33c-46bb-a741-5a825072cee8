# 🧪 Testing Suite - Logistics AI Platform

## 📋 **Overview**

Comprehensive testing suite for the logistics AI platform, covering unit tests, integration tests, end-to-end tests, and performance tests. The suite validates all critical flows from registration to platform usage.

## 🏗️ **Test Structure**

```
tests/
├── conftest.py              # Shared fixtures and configuration
├── run_tests.py             # Automated test runner
├── unit/                    # Unit tests for individual components
├── integration/             # Integration tests for service interactions
├── e2e/                     # End-to-end tests for complete flows
├── performance/             # Performance and load tests
├── fixtures/                # Test data and mock fixtures
└── coverage/                # Coverage reports
```

## 🚀 **Quick Start**

### **Run All Tests**
```bash
# Run complete test suite
python tests/run_tests.py

# Run with verbose output
python tests/run_tests.py --verbose

# Skip slow tests
python tests/run_tests.py --skip-slow
```

### **Run Specific Test Types**
```bash
# Unit tests only
python tests/run_tests.py --type unit

# Integration tests only
python tests/run_tests.py --type integration

# End-to-end tests only
python tests/run_tests.py --type e2e

# Performance tests only
python tests/run_tests.py --type performance
```

### **Run Specific Tests**
```bash
# Run specific test file
python tests/run_tests.py --test tests/integration/test_registration_flow.py

# Run specific test function
python tests/run_tests.py --test tests/integration/test_registration_flow.py::TestRegistrationFlow::test_complete_registration_flow_success
```

## 🧪 **Test Categories**

### **Unit Tests**
Test individual functions and classes in isolation.

**Coverage:**
- Service layer logic
- Utility functions
- Data models
- Validation functions
- Error handling

**Example:**
```python
def test_email_validation():
    assert validate_email("<EMAIL>") == True
    assert validate_email("invalid-email") == False
```

### **Integration Tests**
Test interactions between services and components.

**Coverage:**
- Service-to-service communication
- Database operations
- External API integrations
- Authentication flows
- Authorization checks

**Example:**
```python
def test_registration_flow_success(dynamodb_table, mock_stripe):
    # Test complete registration process
    response = complete_registration_handler(event, context)
    assert response['statusCode'] == 201
```

### **End-to-End Tests**
Test complete user journeys from start to finish.

**Coverage:**
- Complete registration flow
- User management workflows
- Payment processing
- Tenant setup
- Multi-tenant isolation

**Example:**
```python
def test_complete_user_journey_success():
    # Registration → Verification → Payment → Setup → Usage
    # Tests the entire user onboarding process
```

### **Performance Tests**
Test system performance under load.

**Coverage:**
- Response time benchmarks
- Concurrent request handling
- Resource utilization
- Scalability limits

**Example:**
```python
def test_system_performance_under_load():
    # Simulate 100 concurrent registrations
    # Verify response times < 2 seconds
    # Verify success rate > 95%
```

## 🔧 **Test Configuration**

### **Environment Variables**
```bash
# Test environment
STAGE=test
REGION=us-east-1
PROJECT_NAME=agent-scl

# Test databases
DYNAMODB_TABLE=agent-scl-test-table
REGISTRATIONS_TABLE=agent-scl-test-table
SUBSCRIPTIONS_TABLE=agent-scl-test-table

# Test credentials
JWT_SECRET=test_jwt_secret_key_for_testing_only
STRIPE_SECRET_KEY=sk_test_123456789
```

### **Pytest Markers**
Use markers to run specific test categories:

```bash
# Run only authentication tests
pytest -m auth

# Run only payment tests
pytest -m payment

# Run only fast tests (exclude slow)
pytest -m "not slow"

# Run integration tests for tenant service
pytest -m "integration and tenant"
```

### **Available Markers**
- `unit` - Unit tests
- `integration` - Integration tests
- `e2e` - End-to-end tests
- `performance` - Performance tests
- `slow` - Slow running tests
- `auth` - Authentication tests
- `payment` - Payment tests
- `tenant` - Tenant management tests
- `registration` - Registration flow tests
- `setup` - Setup service tests
- `jobs` - Scheduled jobs tests
- `api_gateway` - API Gateway tests

## 📊 **Coverage Reports**

### **Generate Coverage Reports**
```bash
# HTML coverage report
pytest --cov=services --cov=shared --cov-report=html

# Terminal coverage report
pytest --cov=services --cov=shared --cov-report=term-missing

# XML coverage report (for CI/CD)
pytest --cov=services --cov=shared --cov-report=xml
```

### **Coverage Targets**
- **Overall Coverage**: > 80%
- **Critical Paths**: > 95%
- **Business Logic**: > 90%
- **Error Handling**: > 85%

## 🔍 **Test Data & Fixtures**

### **Shared Fixtures**
Available in `conftest.py`:

- `dynamodb_table` - Mock DynamoDB table
- `s3_bucket` - Mock S3 bucket
- `jwt_token` - Valid JWT token for testing
- `admin_jwt_token` - Admin JWT token
- `api_event` - API Gateway event generator
- `lambda_context` - Mock Lambda context
- `mock_stripe` - Mocked Stripe API
- `mock_ses` - Mocked SES email service

### **Test Data**
Sample data generators:

- `sample_tenant_data()` - Tenant test data
- `sample_user_data()` - User test data
- `sample_registration_data()` - Registration test data
- `sample_subscription_data()` - Subscription test data

### **Test Helpers**
Utility functions:

- `test_helpers.assert_response_success()` - Assert API success
- `test_helpers.assert_response_error()` - Assert API error
- `test_helpers.create_test_item()` - Create DynamoDB test item
- `test_helpers.generate_test_id()` - Generate unique test IDs

## 🚨 **Debugging Tests**

### **Run Tests with Debug Output**
```bash
# Verbose output with full tracebacks
pytest -vvv --tb=long

# Show print statements
pytest -s

# Drop into debugger on failure
pytest --pdb

# Debug specific test
pytest --pdb tests/integration/test_registration_flow.py::test_specific_function
```

### **Common Issues**

**AWS Credentials Error**
```bash
# Solution: Use mocked AWS services
export AWS_ACCESS_KEY_ID=testing
export AWS_SECRET_ACCESS_KEY=testing
```

**Import Errors**
```bash
# Solution: Add project root to PYTHONPATH
export PYTHONPATH=/path/to/project:$PYTHONPATH
```

**Database Connection Issues**
```bash
# Solution: Ensure moto mocks are active
# Check that @mock_dynamodb decorator is applied
```

## 🔄 **Continuous Integration**

### **GitHub Actions Example**
```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements-test.txt
      
      - name: Run tests
        run: |
          python tests/run_tests.py --type all
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: tests/coverage/coverage.xml
```

### **Pre-commit Hooks**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: pytest-fast
        name: pytest-fast
        entry: python tests/run_tests.py --skip-slow
        language: system
        pass_filenames: false
        always_run: true
```

## 📈 **Performance Benchmarks**

### **Target Metrics**
- **Registration Flow**: < 2 seconds end-to-end
- **User Authentication**: < 500ms
- **API Response Time**: < 1 second (95th percentile)
- **Database Queries**: < 100ms average
- **Concurrent Users**: Support 100+ simultaneous registrations

### **Load Testing**
```bash
# Run performance tests
python tests/run_tests.py --type performance

# Custom load test
pytest tests/performance/ -v --durations=0
```

## 🛠️ **Writing New Tests**

### **Test Naming Convention**
```python
# File: test_<feature>_<type>.py
# Class: Test<Feature><Type>
# Method: test_<scenario>_<expected_outcome>

class TestUserManagement:
    def test_create_user_success(self):
        # Test successful user creation
        pass
    
    def test_create_user_duplicate_email_fails(self):
        # Test duplicate email validation
        pass
```

### **Test Structure**
```python
def test_feature_scenario(self, fixtures, helpers):
    # Arrange - Set up test data
    test_data = {...}
    
    # Act - Execute the functionality
    result = function_under_test(test_data)
    
    # Assert - Verify the outcome
    assert result['success'] == True
    helpers.assert_response_success(result)
```

### **Best Practices**
1. **Isolation**: Each test should be independent
2. **Clarity**: Test names should describe the scenario
3. **Coverage**: Test both success and failure paths
4. **Performance**: Keep tests fast and focused
5. **Maintainability**: Use fixtures and helpers for common setup

## 📝 **Test Reports**

### **Automated Reports**
The test runner generates comprehensive reports including:

- Test execution summary
- Coverage analysis
- Performance metrics
- Failed test details
- Duration analysis

### **Example Report**
```
📊 TEST EXECUTION REPORT
============================================================

UNIT TESTS:
  Status: ✅ PASSED
  Duration: 15.23s
  Passed: 45
  Failed: 0
  Skipped: 2

INTEGRATION TESTS:
  Status: ✅ PASSED
  Duration: 32.45s
  Passed: 23
  Failed: 0
  Skipped: 1

OVERALL SUMMARY:
  Total Duration: 47.68s
  Total Passed: 68
  Total Failed: 0
  Total Skipped: 3
  Success Rate: 100.0%
  Overall Status: ✅ ALL TESTS PASSED
============================================================
```
