#!/usr/bin/env python3
# tests/test_infrastructure_improvements.py
# Test infrastructure improvements implemented in Phase 4

"""
Test infrastructure improvements:
1. Health check functionality
2. Standardized configurations
3. Monitoring capabilities
4. Deployment validation
"""

import os
import json
import pytest
from unittest.mock import patch, MagicMock
from pathlib import Path

# Set JWT secret before importing auth modules
os.environ['JWT_SECRET'] = 'test-jwt-secret-for-testing-purposes-very-long-and-secure'

from shared.health import HealthCheck, HealthStatus, create_health_handler


class TestInfrastructureImprovements:
    """Test infrastructure improvements implemented in Phase 4."""
    
    def test_health_check_basic_functionality(self):
        """Test basic health check functionality."""
        health_checker = HealthCheck("test-service")
        
        # Test basic health
        health_data = health_checker.get_basic_health()
        
        assert health_data["service"] == "test-service"
        assert health_data["status"] == HealthStatus.HEALTHY
        assert "timestamp" in health_data
        assert "uptime_seconds" in health_data
        assert "environment" in health_data
        assert "region" in health_data
        assert "version" in health_data
    
    @patch('shared.database.db_client')
    def test_database_health_check_success(self, mock_db_client):
        """Test successful database health check."""
        # Mock successful database response
        mock_db_client.dynamodb.describe_table.return_value = {
            'Table': {'TableStatus': 'ACTIVE'}
        }

        health_checker = HealthCheck("test-service")
        health_checker.settings.dynamodb_table = "test-table"

        db_health = health_checker.check_database_health()

        assert db_health["component"] == "database"
        assert db_health["status"] == HealthStatus.HEALTHY
        assert "response_time_ms" in db_health
        assert db_health["table_status"] == "ACTIVE"
    
    @patch('shared.database.db_client')
    def test_database_health_check_failure(self, mock_db_client):
        """Test database health check failure."""
        # Mock database error
        mock_db_client.dynamodb.describe_table.side_effect = Exception("Connection failed")

        health_checker = HealthCheck("test-service")
        health_checker.settings.dynamodb_table = "test-table"

        db_health = health_checker.check_database_health()

        assert db_health["component"] == "database"
        assert db_health["status"] == HealthStatus.UNHEALTHY
        assert "error" in db_health
    
    @patch('shared.secrets_manager.secrets_client')
    def test_secrets_health_check_success(self, mock_secrets_client):
        """Test successful secrets manager health check."""
        # Mock successful secrets response
        mock_secrets_client.list_secrets.return_value = {"SecretList": []}

        health_checker = HealthCheck("test-service")

        secrets_health = health_checker.check_secrets_health()

        assert secrets_health["component"] == "secrets_manager"
        assert secrets_health["status"] == HealthStatus.HEALTHY
        assert "response_time_ms" in secrets_health
    
    @patch('psutil.virtual_memory')
    def test_memory_health_check_healthy(self, mock_virtual_memory):
        """Test memory health check when healthy."""
        # Mock healthy memory usage (60%)
        mock_memory = MagicMock()
        mock_memory.percent = 60.0
        mock_memory.available = 1024 * 1024 * 1024  # 1GB
        mock_memory.total = 2048 * 1024 * 1024      # 2GB
        mock_virtual_memory.return_value = mock_memory

        health_checker = HealthCheck("test-service")

        memory_health = health_checker.check_memory_usage()

        assert memory_health["component"] == "memory"
        assert memory_health["status"] == HealthStatus.HEALTHY
        assert memory_health["usage_percent"] == 60.0
    
    @patch('psutil.virtual_memory')
    def test_memory_health_check_degraded(self, mock_virtual_memory):
        """Test memory health check when degraded."""
        # Mock degraded memory usage (80%)
        mock_memory = MagicMock()
        mock_memory.percent = 80.0
        mock_memory.available = 512 * 1024 * 1024   # 512MB
        mock_memory.total = 2048 * 1024 * 1024      # 2GB
        mock_virtual_memory.return_value = mock_memory

        health_checker = HealthCheck("test-service")

        memory_health = health_checker.check_memory_usage()

        assert memory_health["component"] == "memory"
        assert memory_health["status"] == HealthStatus.DEGRADED
        assert memory_health["usage_percent"] == 80.0
    
    @patch('psutil.virtual_memory')
    def test_memory_health_check_unhealthy(self, mock_virtual_memory):
        """Test memory health check when unhealthy."""
        # Mock unhealthy memory usage (90%)
        mock_memory = MagicMock()
        mock_memory.percent = 90.0
        mock_memory.available = 256 * 1024 * 1024   # 256MB
        mock_memory.total = 2048 * 1024 * 1024      # 2GB
        mock_virtual_memory.return_value = mock_memory

        health_checker = HealthCheck("test-service")

        memory_health = health_checker.check_memory_usage()

        assert memory_health["component"] == "memory"
        assert memory_health["status"] == HealthStatus.UNHEALTHY
        assert memory_health["usage_percent"] == 90.0
    
    def test_comprehensive_health_check_all_healthy(self):
        """Test comprehensive health check when all dependencies are healthy."""
        with patch.object(HealthCheck, 'check_database_health') as mock_db, \
             patch.object(HealthCheck, 'check_secrets_health') as mock_secrets, \
             patch.object(HealthCheck, 'check_memory_usage') as mock_memory:
            
            # Mock all dependencies as healthy
            mock_db.return_value = {"component": "database", "status": HealthStatus.HEALTHY}
            mock_secrets.return_value = {"component": "secrets_manager", "status": HealthStatus.HEALTHY}
            mock_memory.return_value = {"component": "memory", "status": HealthStatus.HEALTHY}
            
            health_checker = HealthCheck("test-service")
            health_data = health_checker.get_comprehensive_health()
            
            assert health_data["status"] == HealthStatus.HEALTHY
            assert len(health_data["dependencies"]) == 3
            assert "message" not in health_data  # No issues to report
    
    def test_comprehensive_health_check_with_unhealthy_dependency(self):
        """Test comprehensive health check with unhealthy dependency."""
        with patch.object(HealthCheck, 'check_database_health') as mock_db, \
             patch.object(HealthCheck, 'check_secrets_health') as mock_secrets, \
             patch.object(HealthCheck, 'check_memory_usage') as mock_memory:
            
            # Mock one dependency as unhealthy
            mock_db.return_value = {"component": "database", "status": HealthStatus.UNHEALTHY}
            mock_secrets.return_value = {"component": "secrets_manager", "status": HealthStatus.HEALTHY}
            mock_memory.return_value = {"component": "memory", "status": HealthStatus.HEALTHY}
            
            health_checker = HealthCheck("test-service")
            health_data = health_checker.get_comprehensive_health()
            
            assert health_data["status"] == HealthStatus.UNHEALTHY
            assert "1 dependencies unhealthy" in health_data["message"]
    
    def test_health_handler_creation(self):
        """Test health handler creation."""
        handler = create_health_handler("test-service")
        
        assert callable(handler)
        
        # Test basic health check
        event = {"queryStringParameters": None}
        response = handler(event, None)
        
        assert response["statusCode"] == 200
        assert "Content-Type" in response["headers"]
        
        # Parse response body
        body = json.loads(response["body"])
        assert body["service"] == "test-service"
        assert body["status"] == HealthStatus.HEALTHY
    
    def test_health_handler_deep_check(self):
        """Test health handler with deep check."""
        with patch.object(HealthCheck, 'get_comprehensive_health') as mock_comprehensive:
            mock_comprehensive.return_value = {
                "service": "test-service",
                "status": HealthStatus.HEALTHY,
                "dependencies": []
            }
            
            handler = create_health_handler("test-service")
            
            # Test deep health check
            event = {"queryStringParameters": {"deep": "true"}}
            response = handler(event, None)
            
            assert response["statusCode"] == 200
            mock_comprehensive.assert_called_once()
    
    def test_health_handler_unhealthy_response(self):
        """Test health handler with unhealthy status."""
        with patch.object(HealthCheck, 'get_basic_health') as mock_basic:
            mock_basic.return_value = {
                "service": "test-service",
                "status": HealthStatus.UNHEALTHY,
                "error": "Service unavailable"
            }
            
            handler = create_health_handler("test-service")
            
            event = {"queryStringParameters": None}
            response = handler(event, None)
            
            assert response["statusCode"] == 503
    
    def test_health_handler_error_handling(self):
        """Test health handler error handling."""
        with patch.object(HealthCheck, 'get_basic_health') as mock_basic:
            mock_basic.side_effect = Exception("Unexpected error")
            
            handler = create_health_handler("test-service")
            
            event = {"queryStringParameters": None}
            response = handler(event, None)
            
            assert response["statusCode"] == 503
            body = json.loads(response["body"])
            assert body["status"] == HealthStatus.UNHEALTHY
            assert "error" in body
    
    def test_serverless_template_exists(self):
        """Test that serverless template exists and is valid."""
        template_path = Path("serverless/templates/service-template.yml")
        
        assert template_path.exists(), "Service template should exist"
        
        # Read and validate basic structure
        with open(template_path, 'r') as f:
            content = f.read()
        
        # Check for key sections
        assert "service:" in content
        assert "provider:" in content
        assert "custom:" in content
        assert "functions:" in content
        assert "resources:" in content
    
    def test_shared_variables_configuration(self):
        """Test shared variables configuration."""
        variables_path = Path("serverless/shared/variables.yml")

        assert variables_path.exists(), "Shared variables should exist"

        # Read and validate basic structure with UTF-8 encoding
        with open(variables_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for key configurations
        assert "projectName:" in content
        assert "stages:" in content
        assert "lambda:" in content


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
