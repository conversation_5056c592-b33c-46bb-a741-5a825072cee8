# tests/performance/test_api_performance.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Performance Testing

"""
Performance tests for API endpoints.
Tests response times, throughput, and resource usage.
"""

import pytest
import json
import time
import statistics
import concurrent.futures
from unittest.mock import Mock, patch
from moto import mock_aws

# Add paths for services
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'services', 'auth', 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'services', 'payment', 'src'))

from handlers.login import handler as login_handler
from handlers.register import handler as register_handler
from handlers.list_plans import handler as list_plans_handler
from handlers.get_subscription import handler as get_subscription_handler


class TestAPIPerformance:
    """Performance tests for API endpoints."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        # SLA thresholds (Phase 1 requirements)
        self.sla_thresholds = {
            'p50': 200,   # 50th percentile < 200ms
            'p95': 500,   # 95th percentile < 500ms
            'p99': 1000,  # 99th percentile < 1000ms
            'max': 2000   # Maximum response time < 2000ms
        }
        self.concurrent_users = 10
        self.test_iterations = 50
    
    def _measure_response_time(self, handler_func, event):
        """Measure response time for a handler function."""
        start_time = time.time()
        response = handler_func(event, {})
        end_time = time.time()
        
        response_time_ms = (end_time - start_time) * 1000
        return response_time_ms, response

    def _calculate_percentiles(self, response_times):
        """Calculate performance percentiles."""
        if not response_times:
            return {}

        sorted_times = sorted(response_times)
        return {
            'min': min(sorted_times),
            'max': max(sorted_times),
            'mean': statistics.mean(sorted_times),
            'median': statistics.median(sorted_times),
            'p50': sorted_times[int(0.50 * len(sorted_times))],
            'p90': sorted_times[int(0.90 * len(sorted_times))],
            'p95': sorted_times[int(0.95 * len(sorted_times))],
            'p99': sorted_times[int(0.99 * len(sorted_times))] if len(sorted_times) >= 100 else max(sorted_times)
        }

    def _assert_sla_compliance(self, percentiles, test_name):
        """Assert that performance meets SLA requirements."""
        failures = []

        for metric, threshold in self.sla_thresholds.items():
            if metric in percentiles and percentiles[metric] > threshold:
                failures.append(f"{metric}: {percentiles[metric]:.2f}ms > {threshold}ms")

        if failures:
            failure_msg = f"{test_name} SLA violations:\n" + "\n".join(failures)
            pytest.fail(failure_msg)

        # Print performance results
        print(f"\n{test_name} Performance Results:")
        for metric, value in percentiles.items():
            print(f"  {metric}: {value:.2f}ms")

    def _print_performance_summary(self, test_name, percentiles, success_rate=None):
        """Print detailed performance summary."""
        print(f"\n{'='*50}")
        print(f"PERFORMANCE SUMMARY: {test_name}")
        print(f"{'='*50}")
        print(f"Mean Response Time: {percentiles['mean']:.2f}ms")
        print(f"Median (P50): {percentiles['p50']:.2f}ms")
        print(f"P90: {percentiles['p90']:.2f}ms")
        print(f"P95: {percentiles['p95']:.2f}ms")
        print(f"P99: {percentiles['p99']:.2f}ms")
        print(f"Max: {percentiles['max']:.2f}ms")
        print(f"Min: {percentiles['min']:.2f}ms")
        if success_rate is not None:
            print(f"Success Rate: {success_rate:.1f}%")

        # SLA compliance check
        sla_violations = []
        for metric, threshold in self.sla_thresholds.items():
            if metric in percentiles:
                status = "✅ PASS" if percentiles[metric] <= threshold else "❌ FAIL"
                sla_violations.append(percentiles[metric] > threshold)
                print(f"SLA {metric.upper()}: {percentiles[metric]:.2f}ms <= {threshold}ms {status}")

        overall_status = "✅ PASS" if not any(sla_violations) else "❌ FAIL"
        print(f"Overall SLA Compliance: {overall_status}")
        print(f"{'='*50}")
    
    def _create_test_event(self, method='GET', path='/', body=None):
        """Create a test event for API calls."""
        return {
            'httpMethod': method,
            'path': path,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps(body) if body else None,
            'requestContext': {
                'requestId': f'perf-test-{int(time.time() * 1000)}',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
    
    @mock_aws
    def test_login_performance(self):
        """Test login endpoint performance."""
        
        # Create test user first
        register_event = self._create_test_event('POST', '/auth/register', {
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'name': 'Performance Test',
            'company': 'Test Company'
        })
        
        with patch('src.auth.services.email_service.email_service.send_verification_email'):
            register_handler(register_event, {})
        
        # Test login performance
        login_event = self._create_test_event('POST', '/auth/login', {
            'email': '<EMAIL>',
            'password': 'TestPassword123!'
        })
        
        response_times = []
        
        for i in range(self.test_iterations):
            response_time, response = self._measure_response_time(login_handler, login_event)
            response_times.append(response_time)
            
            # Verify response is successful
            assert response['statusCode'] in [200, 401, 403]  # 401/403 for unverified user
        
        # Analyze performance metrics
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        
        print(f"\nLogin Performance Metrics:")
        print(f"Average response time: {avg_response_time:.2f}ms")
        print(f"Max response time: {max_response_time:.2f}ms")
        print(f"Min response time: {min_response_time:.2f}ms")
        print(f"95th percentile: {p95_response_time:.2f}ms")
        
        # Performance assertions
        assert avg_response_time < self.max_response_time, f"Average response time {avg_response_time:.2f}ms exceeds {self.max_response_time}ms"
        assert p95_response_time < self.max_response_time * 1.5, f"95th percentile {p95_response_time:.2f}ms exceeds threshold"
    
    @mock_aws
    def test_list_plans_performance(self):
        """Test list plans endpoint performance."""
        
        # Create test plans
        from models.plan import Plan
        Plan.create_default_plans()
        
        list_plans_event = self._create_test_event('GET', '/payment/plans')
        
        response_times = []
        
        for i in range(self.test_iterations):
            response_time, response = self._measure_response_time(list_plans_handler, list_plans_event)
            response_times.append(response_time)
            
            # Verify response is successful
            assert response['statusCode'] == 200
        
        # Analyze performance metrics
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        
        print(f"\nList Plans Performance Metrics:")
        print(f"Average response time: {avg_response_time:.2f}ms")
        print(f"Max response time: {max_response_time:.2f}ms")
        print(f"95th percentile: {p95_response_time:.2f}ms")
        
        # Performance assertions
        assert avg_response_time < self.max_response_time, f"Average response time {avg_response_time:.2f}ms exceeds {self.max_response_time}ms"
        assert p95_response_time < self.max_response_time * 1.5, f"95th percentile {p95_response_time:.2f}ms exceeds threshold"
    
    @mock_aws
    def test_concurrent_requests_performance(self):
        """Test performance under concurrent load."""
        
        # Create test plans
        from models.plan import Plan
        Plan.create_default_plans()
        
        list_plans_event = self._create_test_event('GET', '/payment/plans')
        
        def make_request():
            return self._measure_response_time(list_plans_handler, list_plans_event)
        
        # Execute concurrent requests
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.concurrent_users) as executor:
            futures = [executor.submit(make_request) for _ in range(self.concurrent_users)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        
        # Analyze results
        response_times = [result[0] for result in results]
        responses = [result[1] for result in results]
        
        # Verify all responses are successful
        for response in responses:
            assert response['statusCode'] == 200
        
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        throughput = len(results) / (total_time / 1000)  # requests per second
        
        print(f"\nConcurrent Load Performance Metrics:")
        print(f"Concurrent users: {self.concurrent_users}")
        print(f"Total time: {total_time:.2f}ms")
        print(f"Average response time: {avg_response_time:.2f}ms")
        print(f"Max response time: {max_response_time:.2f}ms")
        print(f"Throughput: {throughput:.2f} requests/second")
        
        # Performance assertions
        assert avg_response_time < self.max_response_time * 2, f"Concurrent average response time {avg_response_time:.2f}ms too high"
        assert throughput > 5, f"Throughput {throughput:.2f} req/s too low"
    
    @mock_aws
    def test_memory_usage_performance(self):
        """Test memory usage during operations."""
        import psutil
        import os
        
        # Get current process
        process = psutil.Process(os.getpid())
        
        # Measure initial memory
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create test plans
        from models.plan import Plan
        Plan.create_default_plans()
        
        list_plans_event = self._create_test_event('GET', '/payment/plans')
        
        # Execute multiple requests
        for i in range(100):
            list_plans_handler(list_plans_event, {})
        
        # Measure final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"\nMemory Usage Metrics:")
        print(f"Initial memory: {initial_memory:.2f}MB")
        print(f"Final memory: {final_memory:.2f}MB")
        print(f"Memory increase: {memory_increase:.2f}MB")
        
        # Memory assertions
        assert memory_increase < 50, f"Memory increase {memory_increase:.2f}MB too high"
    
    @mock_aws
    def test_database_query_performance(self):
        """Test database query performance."""
        
        # Create multiple plans to test query performance
        from models.plan import Plan
        plans = Plan.create_default_plans()
        
        # Measure query performance
        query_times = []
        
        for i in range(20):
            start_time = time.time()
            
            # Simulate database queries
            for plan in plans:
                Plan.get_by_id(plan.plan_id)
            
            end_time = time.time()
            query_time = (end_time - start_time) * 1000
            query_times.append(query_time)
        
        avg_query_time = statistics.mean(query_times)
        max_query_time = max(query_times)
        
        print(f"\nDatabase Query Performance Metrics:")
        print(f"Average query time: {avg_query_time:.2f}ms")
        print(f"Max query time: {max_query_time:.2f}ms")
        print(f"Queries per test: {len(plans)}")
        
        # Query performance assertions
        assert avg_query_time < 100, f"Average query time {avg_query_time:.2f}ms too high"
        assert max_query_time < 200, f"Max query time {max_query_time:.2f}ms too high"
    
    @mock_aws
    def test_json_serialization_performance(self):
        """Test JSON serialization performance."""
        
        # Create test data
        from models.plan import Plan
        plans = Plan.create_default_plans()
        
        # Test serialization performance
        serialization_times = []
        
        for i in range(100):
            start_time = time.time()
            
            # Serialize plans to JSON
            plans_data = [plan.to_dict() for plan in plans]
            json_data = json.dumps(plans_data)
            
            end_time = time.time()
            serialization_time = (end_time - start_time) * 1000
            serialization_times.append(serialization_time)
        
        avg_serialization_time = statistics.mean(serialization_times)
        max_serialization_time = max(serialization_times)
        
        print(f"\nJSON Serialization Performance Metrics:")
        print(f"Average serialization time: {avg_serialization_time:.2f}ms")
        print(f"Max serialization time: {max_serialization_time:.2f}ms")
        print(f"Objects per test: {len(plans)}")
        
        # Serialization performance assertions
        assert avg_serialization_time < 10, f"Average serialization time {avg_serialization_time:.2f}ms too high"
        assert max_serialization_time < 20, f"Max serialization time {max_serialization_time:.2f}ms too high"
    
    @mock_aws
    def test_error_handling_performance(self):
        """Test performance when handling errors."""
        
        # Test invalid login performance
        invalid_login_event = self._create_test_event('POST', '/auth/login', {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        
        error_response_times = []
        
        for i in range(20):
            response_time, response = self._measure_response_time(login_handler, invalid_login_event)
            error_response_times.append(response_time)
            
            # Verify error response
            assert response['statusCode'] == 401
        
        avg_error_time = statistics.mean(error_response_times)
        max_error_time = max(error_response_times)
        
        print(f"\nError Handling Performance Metrics:")
        print(f"Average error response time: {avg_error_time:.2f}ms")
        print(f"Max error response time: {max_error_time:.2f}ms")
        
        # Error handling should be fast
        assert avg_error_time < self.max_response_time, f"Error handling too slow: {avg_error_time:.2f}ms"
        assert max_error_time < self.max_response_time * 1.5, f"Max error time too high: {max_error_time:.2f}ms"
