# tests/conftest.py
# Implementado según "Development Standards" y "Testing Guidelines"

"""
Pytest configuration and shared fixtures for all tests.
"""

import os
import sys
import json
import pytest
import pytest_asyncio
import boto3
from moto import mock_aws
from unittest.mock import patch
from pathlib import Path

# Add paths for new structure - CRITICAL FIX
project_root = Path(__file__).parent.parent
print(f"🔧 CONFTEST: Adding paths from {project_root}")

# Add shared path FIRST and ensure it's at the beginning
shared_path = str(project_root / "shared" / "python")
if shared_path in sys.path:
    sys.path.remove(shared_path)
sys.path.insert(0, shared_path)
print(f"🔧 CONFTEST: Added shared path: {shared_path}")

# Add service paths
service_names = ["auth", "payment", "tenant", "user", "admin", "events", "security"]
for service_name in service_names:
    service_path = str(project_root / "services" / service_name / "src")
    if Path(service_path).exists():
        if service_path in sys.path:
            sys.path.remove(service_path)
        sys.path.insert(0, service_path)
        print(f"🔧 CONFTEST: Added {service_name} service path: {service_path}")

# Set JWT secret for testing before importing auth modules
import os
os.environ.setdefault('JWT_SECRET', 'test-jwt-secret-for-testing-purposes-very-long-and-secure')

# Verify imports work
try:
    import shared.auth
    print("🔧 CONFTEST: ✅ shared.auth import verification successful")
except ImportError as e:
    print(f"🔧 CONFTEST: ❌ shared.auth import verification failed: {e}")

# Configure pytest-asyncio
pytest_plugins = ('pytest_asyncio',)

# Set test environment
os.environ["TESTING"] = "true"
os.environ["ENVIRONMENT"] = "test"
os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
os.environ["DYNAMODB_TABLE"] = "platform-main-test"
os.environ["S3_BUCKET"] = "platform-data-test"

# Stripe test configuration
os.environ["STRIPE_TEST_SECRET_KEY"] = "sk_test_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
os.environ["STRIPE_LIVE_SECRET_KEY"] = "sk_live_test_key_for_testing"
os.environ["STRIPE_TEST_PUBLISHABLE_KEY"] = "pk_test_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
os.environ["STRIPE_LIVE_PUBLISHABLE_KEY"] = "pk_live_test_key_for_testing"
os.environ["STRIPE_WEBHOOK_SECRET"] = "whsec_test_webhook_secret_for_testing"
os.environ["STRIPE_WEBHOOK_TOLERANCE"] = "300"


@pytest.fixture(scope="session")
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"


@pytest.fixture(scope="function")
def dynamodb_mock(aws_credentials):
    """Mock DynamoDB service."""
    with mock_aws():
        # Create DynamoDB resource
        dynamodb = boto3.resource("dynamodb", region_name="us-east-1")
        
        # Create test table
        table = dynamodb.create_table(
            TableName="platform-main-test",
            KeySchema=[
                {"AttributeName": "PK", "KeyType": "HASH"},
                {"AttributeName": "SK", "KeyType": "RANGE"}
            ],
            AttributeDefinitions=[
                {"AttributeName": "PK", "AttributeType": "S"},
                {"AttributeName": "SK", "AttributeType": "S"},
                {"AttributeName": "GSI1PK", "AttributeType": "S"},
                {"AttributeName": "GSI1SK", "AttributeType": "S"},
                {"AttributeName": "GSI2PK", "AttributeType": "S"},
                {"AttributeName": "GSI2SK", "AttributeType": "S"},
                {"AttributeName": "GSI3PK", "AttributeType": "S"},
                {"AttributeName": "GSI3SK", "AttributeType": "S"}
            ],
            BillingMode="PAY_PER_REQUEST",
            GlobalSecondaryIndexes=[
                {
                    "IndexName": "GSI1",
                    "KeySchema": [
                        {"AttributeName": "GSI1PK", "KeyType": "HASH"},
                        {"AttributeName": "GSI1SK", "KeyType": "RANGE"}
                    ],
                    "Projection": {"ProjectionType": "ALL"}
                },
                {
                    "IndexName": "GSI2",
                    "KeySchema": [
                        {"AttributeName": "GSI2PK", "KeyType": "HASH"},
                        {"AttributeName": "GSI2SK", "KeyType": "RANGE"}
                    ],
                    "Projection": {"ProjectionType": "ALL"}
                },
                {
                    "IndexName": "GSI3",
                    "KeySchema": [
                        {"AttributeName": "GSI3PK", "KeyType": "HASH"},
                        {"AttributeName": "GSI3SK", "KeyType": "RANGE"}
                    ],
                    "Projection": {"ProjectionType": "ALL"}
                }
            ]
        )
        
        # Wait for table to be created
        table.wait_until_exists()
        
        yield dynamodb


@pytest.fixture(scope="function")
def s3_mock(aws_credentials):
    """Mock S3 service."""
    with mock_aws():
        # Create S3 resource
        s3 = boto3.resource("s3", region_name="us-east-1")
        
        # Create test bucket
        bucket = s3.create_bucket(Bucket="platform-data-test")
        
        yield s3


@pytest.fixture(scope="function")
def ses_mock(aws_credentials):
    """Mock SES service."""
    with mock_aws():
        # Create SES client
        ses = boto3.client("ses", region_name="us-east-1")
        
        # Verify test email addresses
        ses.verify_email_identity(EmailAddress="<EMAIL>")
        ses.verify_email_identity(EmailAddress="<EMAIL>")
        
        yield ses


@pytest.fixture(scope="function")
def secrets_mock(aws_credentials):
    """Mock Secrets Manager service."""
    with mock_aws():
        # Create Secrets Manager client
        secrets = boto3.client("secretsmanager", region_name="us-east-1")

        # Create JWT keys secret (required by JWT manager)
        jwt_keys = {
            "private_key": "-----BEGIN RSA PRIVATE KEY-----\nMIIEpAIBAAKCAQEA4f5wg5l2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEjWT2btYK\n...\n-----END RSA PRIVATE KEY-----",
            "public_key": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41\n...\n-----END PUBLIC KEY-----",
            "algorithm": "RS256"
        }

        secrets.create_secret(
            Name="platform/test/jwt-keys",
            SecretString=json.dumps(jwt_keys)
        )

        # Create Stripe credentials secret
        stripe_credentials = {
            "secret_key": "sk_test_123456789",
            "publishable_key": "pk_test_123456789",
            "webhook_secret": "whsec_test_123456789"
        }

        secrets.create_secret(
            Name="platform/test/stripe-credentials",
            SecretString=json.dumps(stripe_credentials)
        )

        # Create PayU credentials secret
        payu_credentials = {
            "api_key": "test_api_key",
            "merchant_id": "test_merchant",
            "account_id": "test_account"
        }

        secrets.create_secret(
            Name="platform/test/payu-credentials",
            SecretString=json.dumps(payu_credentials)
        )

        # Create n8n credentials secret
        n8n_credentials = {
            "webhook_url": "https://test.n8n.io/webhook",
            "api_key": "test_n8n_api_key"
        }

        secrets.create_secret(
            Name="platform/test/n8n-credentials",
            SecretString=json.dumps(n8n_credentials)
        )

        yield secrets


@pytest.fixture(scope="function")
def mock_environment():
    """Mock environment variables for testing."""
    test_env = {
        "PROJECT_NAME": "platform",
        "ENVIRONMENT": "test",
        "AWS_REGION": "us-east-1",
        "DYNAMODB_TABLE": "platform-main-test",
        "S3_BUCKET": "platform-data-test",
        "LOG_LEVEL": "DEBUG",
        "JWT_ISSUER": "platform-auth",
        "JWT_AUDIENCE": "platform-api",
        "JWT_ALGORITHM": "RS256",
        "FROM_EMAIL": "<EMAIL>",
        "REPLY_TO_EMAIL": "<EMAIL>"
    }
    
    with patch.dict(os.environ, test_env):
        yield test_env


@pytest.fixture(scope="function")
def complete_aws_mock(aws_credentials, secrets_mock, dynamodb_mock, s3_mock):
    """Complete AWS mock setup with all services."""
    # This fixture combines all AWS mocks for comprehensive testing
    yield {
        "secrets": secrets_mock,
        "dynamodb": dynamodb_mock,
        "s3": s3_mock
    }


@pytest.fixture
def sample_tenant_data():
    """Sample tenant data for testing."""
    return {
        "tenant_id": "test-tenant-123",
        "name": "Test Company",
        "status": "ACTIVE",
        "plan": "FREE",
        "master_user_email": "<EMAIL>",
        "created_at": 1642694400,  # 2022-01-20
        "updated_at": 1642694400,
        "user_count": 1,
        "max_users": 5,
        "features": ["basic_chat", "single_agent"]
    }


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "user_id": "test-user-456",
        "tenant_id": "test-tenant-123",
        "email": "<EMAIL>",
        "name": "Test User",
        "role": "MEMBER",
        "status": "ACTIVE",
        "password_hash": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.e",  # "password123"
        "created_at": 1642694400,
        "updated_at": 1642694400,
        "email_verified": True,
        "login_attempts": 0
    }


@pytest.fixture
def sample_jwt_payload():
    """Sample JWT payload for testing."""
    return {
        "sub": "test-user-456",
        "tenant_id": "test-tenant-123",
        "email": "<EMAIL>",
        "role": "MEMBER",
        "type": "access",
        "iss": "platform-auth",
        "aud": "platform-api",
        "exp": 1642698000,  # 1 hour later
        "iat": 1642694400,
        "jti": "test-token-id"
    }


@pytest.fixture
def api_gateway_event():
    """Sample API Gateway event for testing."""
    return {
        "httpMethod": "POST",
        "path": "/auth/login",
        "headers": {
            "Content-Type": "application/json",
            "User-Agent": "test-agent"
        },
        "body": '{"email": "<EMAIL>", "password": "password123"}',
        "requestContext": {
            "requestId": "test-request-123",
            "identity": {
                "sourceIp": "127.0.0.1"
            }
        },
        "queryStringParameters": None,
        "pathParameters": None
    }


@pytest.fixture
def lambda_context():
    """Mock Lambda context for testing."""
    class MockContext:
        def __init__(self):
            self.function_name = "test-function"
            self.function_version = "$LATEST"
            self.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:test-function"
            self.memory_limit_in_mb = 256
            self.remaining_time_in_millis = lambda: 30000
            self.aws_request_id = "test-request-id"
            self.log_group_name = "/aws/lambda/test-function"
            self.log_stream_name = "2022/01/20/[$LATEST]test-stream"
    
    return MockContext()


# Test utilities
class TestHelpers:
    """Helper methods for testing."""
    
    @staticmethod
    def create_test_tenant(dynamodb, tenant_data=None):
        """Create a test tenant in DynamoDB."""
        if tenant_data is None:
            tenant_data = {
                "PK": "TENANT#test-tenant-123",
                "SK": "METADATA",
                "tenant_id": "test-tenant-123",
                "name": "Test Company",
                "status": "ACTIVE",
                "plan": "FREE"
            }
        
        table = dynamodb.Table("platform-main-test")
        table.put_item(Item=tenant_data)
        return tenant_data
    
    @staticmethod
    def create_test_user(dynamodb, user_data=None):
        """Create a test user in DynamoDB."""
        if user_data is None:
            user_data = {
                "PK": "USER#test-user-456",
                "SK": "METADATA",
                "GSI1PK": "TENANT#test-tenant-123",
                "GSI1SK": "USER#<EMAIL>",
                "user_id": "test-user-456",
                "tenant_id": "test-tenant-123",
                "email": "<EMAIL>",
                "name": "Test User",
                "role": "MEMBER",
                "status": "ACTIVE"
            }
        
        table = dynamodb.Table("platform-main-test")
        table.put_item(Item=user_data)
        return user_data


@pytest.fixture
def test_helpers():
    """Test helper methods."""
    return TestHelpers


# Updated fixtures for new structure
@pytest.fixture
def mock_db_client():
    """Mock database client for new structure."""
    with patch('shared.database.db_client') as mock:
        yield mock


@pytest.fixture
def sample_user():
    """Sample user using new structure."""
    try:
        from models.user import User
        return User.create(
            tenant_id="test-tenant",
            email="<EMAIL>",
            name="Test User"
        )
    except ImportError:
        # Fallback for when models aren't available
        return {
            "user_id": "test-user-456",
            "tenant_id": "test-tenant-123",
            "email": "<EMAIL>",
            "name": "Test User",
            "role": "MEMBER"
        }


@pytest.fixture
def sample_tenant():
    """Sample tenant using new structure."""
    try:
        from models.tenant import Tenant
        return Tenant.create(
            name="Test Company",
            master_user_email="<EMAIL>"
        )
    except ImportError:
        # Fallback for when models aren't available
        return {
            "tenant_id": "test-tenant-123",
            "name": "Test Company",
            "status": "ACTIVE",
            "plan": "FREE"
        }
