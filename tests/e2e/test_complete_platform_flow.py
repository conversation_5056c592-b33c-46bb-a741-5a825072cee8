#!/usr/bin/env python3
# tests/e2e/test_complete_platform_flow.py
# End-to-end tests for complete platform flow

"""
End-to-end tests for the complete platform flow.
Tests the entire user journey from registration to platform usage.
"""

import pytest
import json
import time
from datetime import datetime, timedelta
from unittest.mock import patch

# Import all necessary handlers
from services.orchestrator.src.handlers.complete_registration import handler as complete_registration_handler
from services.orchestrator.src.handlers.verify_registration import handler as verify_registration_handler
from services.orchestrator.src.handlers.process_payment import handler as process_payment_handler
from services.payment.src.handlers.webhook import handler as stripe_webhook_handler
from services.setup.src.handlers.setup_tenant import handler as setup_tenant_handler
from services.tenant.src.handlers.get_profile import handler as get_tenant_profile_handler
from services.tenant.src.handlers.invite_user import handler as invite_user_handler
from services.tenant.src.handlers.list_users import handler as list_users_handler
from infrastructure.api_gateway.src.handlers.health_aggregator import handler as health_aggregator_handler


@pytest.mark.e2e
class TestCompletePlatformFlow:
    """Test complete platform flow end-to-end."""
    
    @pytest.mark.slow
    def test_complete_user_journey_success(self, dynamodb_table, s3_bucket, 
                                         mock_stripe, mock_ses, api_event, 
                                         lambda_context, test_helpers):
        """Test complete user journey from registration to platform usage."""
        
        # Phase 1: Registration
        print("🚀 Phase 1: Starting registration...")
        
        registration_data = {
            'email': '<EMAIL>',
            'company_name': 'New Logistics Company',
            'first_name': 'Sarah',
            'last_name': 'Johnson',
            'plan_id': 'plan_pro',
            'password': 'SecurePassword123!',
            'terms_accepted': True
        }
        
        event = api_event(
            method='POST',
            path='/registration/complete',
            body=registration_data
        )
        
        response = complete_registration_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response, 201)
        
        registration_id = body['data']['registration_id']
        tenant_id = body['data']['tenant_id']
        user_id = body['data']['user_id']
        
        print(f"✅ Registration created: {registration_id}")
        
        # Phase 2: Email Verification
        print("📧 Phase 2: Email verification...")
        
        with patch('services.orchestrator.src.services.registration_service.RegistrationService._validate_verification_code', return_value=True):
            verification_data = {
                'registration_id': registration_id,
                'verification_code': '123456'
            }
            
            event = api_event(
                method='POST',
                path='/registration/verify',
                body=verification_data
            )
            
            response = verify_registration_handler(event, lambda_context)
            body = test_helpers.assert_response_success(response)
            
            assert body['data']['state'] == 'EMAIL_VERIFIED'
            print("✅ Email verified successfully")
        
        # Phase 3: Payment Processing
        print("💳 Phase 3: Payment processing...")
        
        payment_data = {
            'registration_id': registration_id,
            'payment_method_id': 'pm_test_123',
            'billing_address': {
                'line1': '123 Business Ave',
                'city': 'Business City',
                'state': 'BC',
                'postal_code': '12345',
                'country': 'US'
            }
        }
        
        event = api_event(
            method='POST',
            path='/registration/payment',
            body=payment_data
        )
        
        response = process_payment_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        subscription_id = body['data']['subscription_id']
        assert body['data']['state'] == 'PAYMENT_COMPLETED'
        print(f"✅ Payment processed: {subscription_id}")
        
        # Phase 4: Stripe Webhook Processing
        print("🔗 Phase 4: Webhook processing...")
        
        webhook_event = {
            'type': 'payment_intent.succeeded',
            'data': {
                'object': {
                    'id': 'pi_test_123',
                    'status': 'succeeded',
                    'metadata': {
                        'registration_id': registration_id,
                        'tenant_id': tenant_id
                    }
                }
            }
        }
        
        event = api_event(
            method='POST',
            path='/payment/webhooks/stripe',
            body=webhook_event,
            headers={'stripe-signature': 'test_signature'}
        )
        
        with patch('stripe.Webhook.construct_event', return_value=webhook_event):
            response = stripe_webhook_handler(event, lambda_context)
            test_helpers.assert_response_success(response)
            print("✅ Webhook processed successfully")
        
        # Phase 5: Tenant Setup
        print("🏗️ Phase 5: Tenant setup...")
        
        setup_data = {
            'plan_id': 'plan_pro',
            'configuration': {
                'enable_advanced_features': True
            }
        }
        
        event = api_event(
            method='POST',
            path=f'/setup/tenant/{tenant_id}',
            path_params={'tenant_id': tenant_id},
            body=setup_data
        )
        
        response = setup_tenant_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        job_id = body['data']['job_id']
        assert body['data']['status'] == 'IN_PROGRESS'
        print(f"✅ Tenant setup initiated: {job_id}")
        
        # Phase 6: Tenant Profile Access
        print("👤 Phase 6: Tenant profile access...")
        
        # Create JWT token for the new user
        import jwt
        token_payload = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'email': '<EMAIL>',
            'role': 'owner',
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        
        jwt_token = jwt.encode(token_payload, 'test_jwt_secret_key_for_testing_only', algorithm='HS256')
        
        event = api_event(
            method='GET',
            path='/tenant/profile',
            auth_token=jwt_token
        )
        
        response = get_tenant_profile_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        assert body['data']['tenant_id'] == tenant_id
        assert body['data']['company_name'] == 'New Logistics Company'
        print("✅ Tenant profile accessed successfully")
        
        # Phase 7: User Invitation
        print("👥 Phase 7: User invitation...")
        
        invitation_data = {
            'email': '<EMAIL>',
            'first_name': 'Mike',
            'last_name': 'Smith',
            'role': 'user',
            'permissions': ['read', 'write']
        }
        
        event = api_event(
            method='POST',
            path='/tenant/invite',
            body=invitation_data,
            auth_token=jwt_token
        )
        
        response = invite_user_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        invitation_id = body['data']['invitation_id']
        print(f"✅ User invited: {invitation_id}")
        
        # Phase 8: User List Access
        print("📋 Phase 8: User list access...")
        
        event = api_event(
            method='GET',
            path='/tenant/users',
            auth_token=jwt_token
        )
        
        response = list_users_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        assert body['data']['total_count'] >= 1
        assert any(user['user_id'] == user_id for user in body['data']['users'])
        print("✅ User list accessed successfully")
        
        # Phase 9: Health Check
        print("🏥 Phase 9: System health check...")
        
        event = api_event(
            method='GET',
            path='/health'
        )
        
        response = health_aggregator_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        assert body['data']['overall_status'] in ['healthy', 'degraded']
        print(f"✅ System health: {body['data']['overall_status']}")
        
        print("🎉 Complete user journey test passed!")
        
        return {
            'registration_id': registration_id,
            'tenant_id': tenant_id,
            'user_id': user_id,
            'subscription_id': subscription_id,
            'job_id': job_id,
            'invitation_id': invitation_id
        }
    
    @pytest.mark.slow
    def test_multi_tenant_isolation(self, dynamodb_table, s3_bucket, mock_stripe, 
                                   mock_ses, api_event, lambda_context, test_helpers):
        """Test that tenants are properly isolated from each other."""
        
        # Create two separate tenants
        tenants = []
        
        for i in range(2):
            registration_data = {
                'email': f'ceo{i}@company{i}.com',
                'company_name': f'Company {i}',
                'first_name': f'CEO{i}',
                'last_name': 'Smith',
                'plan_id': 'plan_starter',
                'password': 'SecurePassword123!',
                'terms_accepted': True
            }
            
            event = api_event(
                method='POST',
                path='/registration/complete',
                body=registration_data
            )
            
            response = complete_registration_handler(event, lambda_context)
            body = test_helpers.assert_response_success(response, 201)
            
            tenants.append({
                'tenant_id': body['data']['tenant_id'],
                'user_id': body['data']['user_id'],
                'email': registration_data['email']
            })
        
        # Create JWT tokens for both tenants
        tokens = []
        for tenant in tenants:
            token_payload = {
                'user_id': tenant['user_id'],
                'tenant_id': tenant['tenant_id'],
                'email': tenant['email'],
                'role': 'owner',
                'exp': datetime.utcnow() + timedelta(hours=1)
            }
            
            import jwt
            token = jwt.encode(token_payload, 'test_jwt_secret_key_for_testing_only', algorithm='HS256')
            tokens.append(token)
        
        # Test that tenant 1 cannot access tenant 2's data
        event = api_event(
            method='GET',
            path='/tenant/profile',
            auth_token=tokens[0]  # Tenant 1's token
        )
        
        response = get_tenant_profile_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        # Should only see tenant 1's data
        assert body['data']['tenant_id'] == tenants[0]['tenant_id']
        assert body['data']['company_name'] == 'Company 0'
        
        # Test that tenant 2 cannot access tenant 1's data
        event = api_event(
            method='GET',
            path='/tenant/profile',
            auth_token=tokens[1]  # Tenant 2's token
        )
        
        response = get_tenant_profile_handler(event, lambda_context)
        body = test_helpers.assert_response_success(response)
        
        # Should only see tenant 2's data
        assert body['data']['tenant_id'] == tenants[1]['tenant_id']
        assert body['data']['company_name'] == 'Company 1'
        
        print("✅ Multi-tenant isolation verified")
    
    def test_error_handling_and_recovery(self, dynamodb_table, api_event, 
                                       lambda_context, test_helpers):
        """Test error handling and recovery mechanisms."""
        
        # Test 1: Invalid registration data
        invalid_registration_data = {
            'email': 'invalid-email',
            'company_name': '',
            'plan_id': 'invalid_plan'
        }
        
        event = api_event(
            method='POST',
            path='/registration/complete',
            body=invalid_registration_data
        )
        
        response = complete_registration_handler(event, lambda_context)
        test_helpers.assert_response_error(response, 422)
        
        # Test 2: Non-existent resource access
        event = api_event(
            method='GET',
            path='/registration/status/non_existent'
        )
        
        from services.orchestrator.src.handlers.get_status import handler as get_status_handler
        response = get_status_handler(event, lambda_context)
        test_helpers.assert_response_error(response, 404)
        
        # Test 3: Unauthorized access
        invalid_token = 'invalid.jwt.token'
        
        event = api_event(
            method='GET',
            path='/tenant/profile',
            auth_token=invalid_token
        )
        
        response = get_tenant_profile_handler(event, lambda_context)
        test_helpers.assert_response_error(response, 401)
        
        print("✅ Error handling verified")
    
    @pytest.mark.performance
    def test_system_performance_under_load(self, dynamodb_table, mock_stripe, 
                                         api_event, lambda_context, test_helpers):
        """Test system performance under simulated load."""
        
        import concurrent.futures
        import time
        
        def create_registration(index):
            registration_data = {
                'email': f'load_test_{index}@example.com',
                'company_name': f'Load Test Company {index}',
                'first_name': f'User{index}',
                'last_name': 'Test',
                'plan_id': 'plan_starter',
                'password': 'SecurePassword123!',
                'terms_accepted': True
            }
            
            event = api_event(
                method='POST',
                path='/registration/complete',
                body=registration_data
            )
            
            start_time = time.time()
            response = complete_registration_handler(event, lambda_context)
            end_time = time.time()
            
            return {
                'index': index,
                'response': response,
                'duration': end_time - start_time,
                'success': response['statusCode'] == 201
            }
        
        # Simulate concurrent registrations
        num_concurrent = 10
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_registration, i) for i in range(num_concurrent)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Analyze results
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        success_rate = len(successful_requests) / len(results) * 100
        avg_response_time = sum(r['duration'] for r in successful_requests) / len(successful_requests) if successful_requests else 0
        
        print(f"📊 Performance Results:")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Average Response Time: {avg_response_time:.3f}s")
        print(f"   Successful Requests: {len(successful_requests)}")
        print(f"   Failed Requests: {len(failed_requests)}")
        
        # Performance assertions
        assert success_rate >= 90, f"Success rate too low: {success_rate}%"
        assert avg_response_time <= 5.0, f"Average response time too high: {avg_response_time}s"
        
        print("✅ Performance test passed")
