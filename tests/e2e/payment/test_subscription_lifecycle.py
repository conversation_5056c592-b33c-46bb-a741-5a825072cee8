# tests/e2e/payment/test_subscription_lifecycle.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Tarea 4.8

"""
End-to-end tests for subscription lifecycle.
Tests the complete subscription process from creation to cancellation.
"""

import pytest
import json
import time
from unittest.mock import Mock, patch
from moto import mock_aws
from decimal import Decimal

# Add paths for services
import sys
import os

from handlers.register import handler as register_handler
from handlers.verify_email import handler as verify_email_handler
from handlers.login import handler as login_handler
from handlers.list_plans import handler as list_plans_handler
from handlers.create_subscription import handler as create_subscription_handler
from handlers.get_subscription import handler as get_subscription_handler
from handlers.cancel_subscription import handler as cancel_subscription_handler
from models.plan import Plan, PlanType, PlanStatus
from models.subscription import Subscription, SubscriptionStatus, BillingInterval


class TestSubscriptionLifecycle:
    """End-to-end tests for subscription lifecycle."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        self.test_password = "TestPassword123!"
        self.test_name = "Test User"
        self.test_company = "Test Company"
    
    def _create_verified_tenant_with_auth(self):
        """Helper method to create a verified tenant with auth tokens."""
        # Register user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            register_response = register_handler(register_event, {})
            verification_token = mock_email.call_args[1]['verification_token']
        
        # Verify email
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({'token': verification_token}),
            'requestContext': {
                'requestId': 'test-verify-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        verify_response = verify_email_handler(verify_event, {})
        
        # Login to get tokens
        login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password
            }),
            'requestContext': {
                'requestId': 'test-login-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        login_response = login_handler(login_event, {})
        
        verify_data = json.loads(verify_response['body'])['data']
        login_data = json.loads(login_response['body'])['data']
        
        # Create auth context mock
        auth_context = Mock()
        auth_context.tenant_id = verify_data['tenant']['tenant_id']
        auth_context.user_id = verify_data['user']['user_id']
        auth_context.email = self.test_email
        auth_context.role = 'MASTER'
        auth_context.is_master.return_value = True
        auth_context.is_member.return_value = False
        
        return {
            'auth_context': auth_context,
            'access_token': login_data['tokens']['access_token'],
            'tenant_id': verify_data['tenant']['tenant_id'],
            'user_id': verify_data['user']['user_id']
        }
    
    def _create_test_plans(self):
        """Helper method to create test plans."""
        plans = Plan.create_default_plans()
        return plans
    
    @mock_aws
    def test_complete_subscription_lifecycle(self):
        """Test complete subscription lifecycle from creation to cancellation."""
        
        # Setup authenticated tenant
        tenant_data = self._create_verified_tenant_with_auth()
        
        # Create test plans
        plans = self._create_test_plans()
        basic_plan = next(plan for plan in plans if plan.plan_type == PlanType.BASIC)
        
        # Step 1: List available plans
        list_plans_event = {
            'httpMethod': 'GET',
            'path': '/payment/plans',
            'headers': {'Content-Type': 'application/json'},
            'requestContext': {
                'requestId': 'test-list-plans-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        plans_response = list_plans_handler(list_plans_event, {})
        
        # Verify plans response
        assert plans_response['statusCode'] == 200
        
        plans_body = json.loads(plans_response['body'])
        assert plans_body['success'] is True
        assert 'plans' in plans_body['data']
        assert len(plans_body['data']['plans']) >= 4  # FREE, BASIC, PROFESSIONAL, ENTERPRISE
        
        # Step 2: Create subscription
        create_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': basic_plan.plan_id,
                'billing_interval': 'MONTHLY'
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-create-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
            # Mock Stripe responses
            mock_stripe.create_customer.return_value = {
                'customer_id': 'cus_test123',
                'email': self.test_email,
                'name': self.test_company
            }
            
            mock_stripe.create_subscription.return_value = {
                'subscription_id': 'sub_test123',
                'status': 'trialing',
                'current_period_start': int(time.time()),
                'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
                'trial_end': int(time.time()) + (14 * 24 * 60 * 60),
                'client_secret': None
            }
            
            create_response = create_subscription_handler(create_subscription_event, {})
        
        # Verify subscription creation
        assert create_response['statusCode'] == 201
        
        create_body = json.loads(create_response['body'])
        assert create_body['success'] is True
        assert 'subscription' in create_body['data']
        assert 'plan' in create_body['data']
        
        subscription_data = create_body['data']['subscription']
        assert subscription_data['tenant_id'] == tenant_data['tenant_id']
        assert subscription_data['plan_id'] == basic_plan.plan_id
        assert subscription_data['status'] == SubscriptionStatus.TRIAL.value
        assert subscription_data['billing_interval'] == BillingInterval.MONTHLY.value
        
        # Step 3: Get subscription
        get_subscription_event = {
            'httpMethod': 'GET',
            'path': '/payment/subscription',
            'headers': {'Content-Type': 'application/json'},
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-get-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        get_response = get_subscription_handler(get_subscription_event, {})
        
        # Verify get subscription
        assert get_response['statusCode'] == 200
        
        get_body = json.loads(get_response['body'])
        assert get_body['success'] is True
        assert get_body['data']['has_subscription'] is True
        assert 'subscription' in get_body['data']
        assert 'plan' in get_body['data']
        
        retrieved_subscription = get_body['data']['subscription']
        assert retrieved_subscription['subscription_id'] == subscription_data['subscription_id']
        assert retrieved_subscription['status'] == SubscriptionStatus.TRIAL.value
        
        # Step 4: Cancel subscription
        cancel_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscription/cancel',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'reason': 'Testing cancellation',
                'at_period_end': True
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-cancel-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
            mock_stripe.cancel_subscription.return_value = {
                'subscription_id': 'sub_test123',
                'status': 'active',
                'canceled_at': None,
                'cancel_at_period_end': True
            }
            
            cancel_response = cancel_subscription_handler(cancel_subscription_event, {})
        
        # Verify cancellation
        assert cancel_response['statusCode'] == 200
        
        cancel_body = json.loads(cancel_response['body'])
        assert cancel_body['success'] is True
        assert cancel_body['data']['cancelled_at_period_end'] is True
        
        # Step 5: Verify subscription is marked for cancellation
        final_get_response = get_subscription_handler(get_subscription_event, {})
        
        assert final_get_response['statusCode'] == 200
        
        final_body = json.loads(final_get_response['body'])
        final_subscription = final_body['data']['subscription']
        
        # Should still be active but marked for cancellation
        assert final_subscription['status'] in [SubscriptionStatus.TRIAL.value, SubscriptionStatus.ACTIVE.value]
        # In real implementation, would check cancel_at_period_end flag
    
    @mock_aws
    def test_subscription_plan_upgrade(self):
        """Test subscription plan upgrade flow."""
        
        # Setup authenticated tenant
        tenant_data = self._create_verified_tenant_with_auth()
        
        # Create test plans
        plans = self._create_test_plans()
        basic_plan = next(plan for plan in plans if plan.plan_type == PlanType.BASIC)
        pro_plan = next(plan for plan in plans if plan.plan_type == PlanType.PROFESSIONAL)
        
        # Create initial subscription (BASIC)
        create_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': basic_plan.plan_id,
                'billing_interval': 'MONTHLY'
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-create-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
            mock_stripe.create_customer.return_value = {
                'customer_id': 'cus_test123',
                'email': self.test_email,
                'name': self.test_company
            }
            
            mock_stripe.create_subscription.return_value = {
                'subscription_id': 'sub_test123',
                'status': 'trialing',
                'current_period_start': int(time.time()),
                'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
                'trial_end': int(time.time()) + (14 * 24 * 60 * 60),
                'client_secret': None
            }
            
            create_response = create_subscription_handler(create_subscription_event, {})
        
        assert create_response['statusCode'] == 201
        
        # Upgrade to PROFESSIONAL plan
        upgrade_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': pro_plan.plan_id,
                'billing_interval': 'MONTHLY'
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-upgrade-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
            mock_stripe.update_subscription.return_value = {
                'subscription_id': 'sub_test123',
                'status': 'active',
                'current_period_start': int(time.time()),
                'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
                'client_secret': None
            }
            
            # In real implementation, this would be an update operation
            # For now, we'll test that the service handles existing subscriptions
            upgrade_response = create_subscription_handler(upgrade_event, {})
        
        # Should handle existing subscription (either update or error)
        assert upgrade_response['statusCode'] in [200, 201, 409]
    
    @mock_aws
    def test_subscription_without_payment_method(self):
        """Test subscription creation without payment method (trial only)."""
        
        # Setup authenticated tenant
        tenant_data = self._create_verified_tenant_with_auth()
        
        # Create test plans
        plans = self._create_test_plans()
        basic_plan = next(plan for plan in plans if plan.plan_type == PlanType.BASIC)
        
        # Create subscription without payment method
        create_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': basic_plan.plan_id,
                'billing_interval': 'MONTHLY'
                # No payment_method_id provided
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-create-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
            mock_stripe.create_customer.return_value = {
                'customer_id': 'cus_test123',
                'email': self.test_email,
                'name': self.test_company
            }
            
            mock_stripe.create_subscription.return_value = {
                'subscription_id': 'sub_test123',
                'status': 'trialing',
                'current_period_start': int(time.time()),
                'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
                'trial_end': int(time.time()) + (14 * 24 * 60 * 60),
                'client_secret': 'pi_test123_secret_456'  # Requires payment setup
            }
            
            create_response = create_subscription_handler(create_subscription_event, {})
        
        # Should succeed but require payment setup
        assert create_response['statusCode'] == 201
        
        create_body = json.loads(create_response['body'])
        assert create_body['success'] is True
        assert 'stripe_client_secret' in create_body['data']
        assert 'Complete payment setup' in create_body['data']['next_steps'][0]
    
    @mock_aws
    def test_subscription_with_invalid_plan(self):
        """Test subscription creation with invalid plan ID."""
        
        # Setup authenticated tenant
        tenant_data = self._create_verified_tenant_with_auth()
        
        # Try to create subscription with invalid plan
        create_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': 'invalid-plan-id',
                'billing_interval': 'MONTHLY'
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-create-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = create_subscription_handler(create_subscription_event, {})
        
        # Should return error for invalid plan
        assert response['statusCode'] == 404
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'not found' in body['error']['message'].lower()
    
    @mock_aws
    def test_subscription_unauthorized_user(self):
        """Test subscription operations with unauthorized user."""
        
        # Create auth context for MEMBER user (not MASTER)
        auth_context = Mock()
        auth_context.tenant_id = 'tenant-123'
        auth_context.user_id = 'user-456'
        auth_context.email = '<EMAIL>'
        auth_context.role = 'MEMBER'
        auth_context.is_master.return_value = False
        auth_context.is_member.return_value = True
        
        # Try to create subscription as MEMBER
        create_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscriptions',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'plan_id': 'plan-basic',
                'billing_interval': 'MONTHLY'
            }),
            'auth_context': auth_context,
            'requestContext': {
                'requestId': 'test-create-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = create_subscription_handler(create_subscription_event, {})
        
        # Should return authorization error
        assert response['statusCode'] == 403
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'authorization' in body['error']['message'].lower()
    
    @mock_aws
    def test_get_subscription_without_subscription(self):
        """Test getting subscription when none exists."""
        
        # Setup authenticated tenant
        tenant_data = self._create_verified_tenant_with_auth()
        
        # Try to get subscription when none exists
        get_subscription_event = {
            'httpMethod': 'GET',
            'path': '/payment/subscription',
            'headers': {'Content-Type': 'application/json'},
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-get-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = get_subscription_handler(get_subscription_event, {})
        
        # Should return success but no subscription
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['has_subscription'] is False
        assert body['data']['subscription'] is None
        assert body['data']['plan'] is None
    
    @mock_aws
    def test_cancel_nonexistent_subscription(self):
        """Test cancelling subscription when none exists."""
        
        # Setup authenticated tenant
        tenant_data = self._create_verified_tenant_with_auth()
        
        # Try to cancel subscription when none exists
        cancel_subscription_event = {
            'httpMethod': 'POST',
            'path': '/payment/subscription/cancel',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'reason': 'Testing cancellation',
                'at_period_end': True
            }),
            'auth_context': tenant_data['auth_context'],
            'requestContext': {
                'requestId': 'test-cancel-sub-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = cancel_subscription_handler(cancel_subscription_event, {})
        
        # Should return error for no subscription
        assert response['statusCode'] == 404
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'not found' in body['error']['message'].lower() or 'no subscription' in body['error']['message'].lower()
