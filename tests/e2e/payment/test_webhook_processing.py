# tests/e2e/payment/test_webhook_processing.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Tarea 4.8

"""
End-to-end tests for Stripe webhook processing.
Tests webhook event handling and subscription state synchronization.
"""

import pytest
import json
import time
import hmac
import hashlib
from unittest.mock import Mock, patch
from moto import mock_aws

# Add path for payment service
import sys
import os

from handlers.stripe_webhook import handler as webhook_handler
from models.subscription import Subscription, SubscriptionStatus


class TestWebhookProcessing:
    """End-to-end tests for webhook processing."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.webhook_secret = "whsec_test123"
        self.stripe_subscription_id = "sub_test123"
        self.tenant_id = "tenant-123"
    
    def _generate_stripe_signature(self, payload: str, timestamp: int = None) -> str:
        """Generate a valid Stripe webhook signature."""
        if timestamp is None:
            timestamp = int(time.time())
        
        # Create the signed payload
        signed_payload = f"{timestamp}.{payload}"
        
        # Generate signature
        signature = hmac.new(
            self.webhook_secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return f"t={timestamp},v1={signature}"
    
    def _create_webhook_event(self, event_type: str, event_data: dict) -> dict:
        """Create a webhook event with proper structure."""
        webhook_payload = {
            'id': f'evt_test_{int(time.time())}',
            'object': 'event',
            'type': event_type,
            'created': int(time.time()),
            'data': {
                'object': event_data
            },
            'livemode': False,
            'pending_webhooks': 1,
            'request': {
                'id': None,
                'idempotency_key': None
            }
        }
        
        payload_str = json.dumps(webhook_payload)
        signature = self._generate_stripe_signature(payload_str)
        
        return {
            'httpMethod': 'POST',
            'path': '/payment/webhooks/stripe',
            'headers': {
                'Content-Type': 'application/json',
                'stripe-signature': signature
            },
            'body': payload_str,
            'requestContext': {
                'requestId': f'webhook-{int(time.time())}',
                'identity': {'sourceIp': '**************'}  # Stripe IP
            }
        }
    
    @mock_aws
    def test_subscription_created_webhook(self):
        """Test processing of subscription.created webhook."""
        
        # Create subscription data
        subscription_data = {
            'id': self.stripe_subscription_id,
            'object': 'subscription',
            'status': 'trialing',
            'customer': 'cus_test123',
            'current_period_start': int(time.time()),
            'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
            'trial_end': int(time.time()) + (14 * 24 * 60 * 60),
            'cancel_at_period_end': False,
            'items': {
                'data': [{
                    'price': {
                        'id': 'price_test123',
                        'unit_amount': 2999
                    }
                }]
            }
        }
        
        # Create webhook event
        webhook_event = self._create_webhook_event('customer.subscription.created', subscription_data)
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
                
                response = webhook_handler(webhook_event, {})
        
        # Verify webhook processing
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['processed'] is True
        assert body['data']['action'] == 'subscription_activated'
    
    @mock_aws
    def test_subscription_updated_webhook(self):
        """Test processing of subscription.updated webhook."""
        
        # First create a subscription in our system
        subscription = Subscription(
            tenant_id=self.tenant_id,
            plan_id='plan-basic',
            stripe_subscription_id=self.stripe_subscription_id,
            status=SubscriptionStatus.TRIAL
        )
        subscription.save()
        
        # Create updated subscription data
        subscription_data = {
            'id': self.stripe_subscription_id,
            'object': 'subscription',
            'status': 'active',
            'customer': 'cus_test123',
            'current_period_start': int(time.time()),
            'current_period_end': int(time.time()) + (30 * 24 * 60 * 60),
            'trial_end': None,
            'cancel_at_period_end': False,
            'items': {
                'data': [{
                    'price': {
                        'id': 'price_test123',
                        'unit_amount': 2999
                    }
                }]
            }
        }
        
        # Create webhook event
        webhook_event = self._create_webhook_event('customer.subscription.updated', subscription_data)
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
                
                response = webhook_handler(webhook_event, {})
        
        # Verify webhook processing
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['processed'] is True
        assert body['data']['action'] == 'subscription_updated'
    
    @mock_aws
    def test_subscription_deleted_webhook(self):
        """Test processing of subscription.deleted webhook."""
        
        # First create a subscription in our system
        subscription = Subscription(
            tenant_id=self.tenant_id,
            plan_id='plan-basic',
            stripe_subscription_id=self.stripe_subscription_id,
            status=SubscriptionStatus.ACTIVE
        )
        subscription.save()
        
        # Create deleted subscription data
        subscription_data = {
            'id': self.stripe_subscription_id,
            'object': 'subscription',
            'status': 'canceled',
            'customer': 'cus_test123',
            'current_period_start': int(time.time()) - (30 * 24 * 60 * 60),
            'current_period_end': int(time.time()),
            'canceled_at': int(time.time()),
            'cancel_at_period_end': False,
            'items': {
                'data': [{
                    'price': {
                        'id': 'price_test123',
                        'unit_amount': 2999
                    }
                }]
            }
        }
        
        # Create webhook event
        webhook_event = self._create_webhook_event('customer.subscription.deleted', subscription_data)
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
                
                response = webhook_handler(webhook_event, {})
        
        # Verify webhook processing
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['processed'] is True
        assert body['data']['action'] == 'subscription_cancelled'
    
    @mock_aws
    def test_payment_succeeded_webhook(self):
        """Test processing of invoice.payment_succeeded webhook."""
        
        # First create a subscription in our system
        subscription = Subscription(
            tenant_id=self.tenant_id,
            plan_id='plan-basic',
            stripe_subscription_id=self.stripe_subscription_id,
            status=SubscriptionStatus.PAST_DUE
        )
        subscription.save()
        
        # Create payment succeeded data
        invoice_data = {
            'id': 'in_test123',
            'object': 'invoice',
            'subscription': self.stripe_subscription_id,
            'status': 'paid',
            'amount_paid': 2999,
            'currency': 'usd',
            'customer': 'cus_test123',
            'period_start': int(time.time()),
            'period_end': int(time.time()) + (30 * 24 * 60 * 60)
        }
        
        # Create webhook event
        webhook_event = self._create_webhook_event('invoice.payment_succeeded', invoice_data)
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
                
                response = webhook_handler(webhook_event, {})
        
        # Verify webhook processing
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['processed'] is True
        assert body['data']['action'] == 'payment_succeeded'
    
    @mock_aws
    def test_payment_failed_webhook(self):
        """Test processing of invoice.payment_failed webhook."""
        
        # First create a subscription in our system
        subscription = Subscription(
            tenant_id=self.tenant_id,
            plan_id='plan-basic',
            stripe_subscription_id=self.stripe_subscription_id,
            status=SubscriptionStatus.ACTIVE
        )
        subscription.save()
        
        # Create payment failed data
        invoice_data = {
            'id': 'in_test123',
            'object': 'invoice',
            'subscription': self.stripe_subscription_id,
            'status': 'open',
            'amount_due': 2999,
            'currency': 'usd',
            'customer': 'cus_test123',
            'period_start': int(time.time()),
            'period_end': int(time.time()) + (30 * 24 * 60 * 60),
            'attempt_count': 1
        }
        
        # Create webhook event
        webhook_event = self._create_webhook_event('invoice.payment_failed', invoice_data)
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
                
                response = webhook_handler(webhook_event, {})
        
        # Verify webhook processing
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['processed'] is True
        assert body['data']['action'] == 'payment_failed'
    
    @mock_aws
    def test_webhook_invalid_signature(self):
        """Test webhook with invalid signature."""
        
        # Create subscription data
        subscription_data = {
            'id': self.stripe_subscription_id,
            'object': 'subscription',
            'status': 'active'
        }
        
        # Create webhook event with invalid signature
        webhook_payload = {
            'id': f'evt_test_{int(time.time())}',
            'type': 'customer.subscription.created',
            'data': {'object': subscription_data}
        }
        
        webhook_event = {
            'httpMethod': 'POST',
            'path': '/payment/webhooks/stripe',
            'headers': {
                'Content-Type': 'application/json',
                'stripe-signature': 'invalid-signature'
            },
            'body': json.dumps(webhook_payload),
            'requestContext': {
                'requestId': f'webhook-{int(time.time())}',
                'identity': {'sourceIp': '**************'}
            }
        }
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.side_effect = Exception("Invalid signature")
                
                response = webhook_handler(webhook_event, {})
        
        # Should return error for invalid signature
        assert response['statusCode'] == 400
        
        body = json.loads(response['body'])
        assert body['success'] is False
    
    @mock_aws
    def test_webhook_missing_signature(self):
        """Test webhook with missing signature header."""
        
        # Create webhook event without signature
        webhook_event = {
            'httpMethod': 'POST',
            'path': '/payment/webhooks/stripe',
            'headers': {
                'Content-Type': 'application/json'
                # Missing stripe-signature header
            },
            'body': json.dumps({'test': 'data'}),
            'requestContext': {
                'requestId': f'webhook-{int(time.time())}',
                'identity': {'sourceIp': '**************'}
            }
        }
        
        response = webhook_handler(webhook_event, {})
        
        # Should return error for missing signature
        assert response['statusCode'] == 422
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'validation_errors' in body['error']
    
    @mock_aws
    def test_webhook_unhandled_event_type(self):
        """Test webhook with unhandled event type."""
        
        # Create unhandled event data
        event_data = {
            'id': 'test_object_123',
            'object': 'test_object'
        }
        
        # Create webhook event
        webhook_event = self._create_webhook_event('test.unhandled_event', event_data)
        
        with patch('src.shared.secrets.get_integration_credentials') as mock_secrets:
            mock_secrets.return_value = {'webhook_secret': self.webhook_secret}
            
            with patch('src.payment.services.stripe_service.stripe_service') as mock_stripe:
                mock_stripe.verify_webhook_signature.return_value = json.loads(webhook_event['body'])
                
                response = webhook_handler(webhook_event, {})
        
        # Should still return success but indicate unhandled
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert body['data']['processed'] is False
        assert 'Unhandled event type' in body['data']['reason']
    
    @mock_aws
    def test_webhook_cors_preflight(self):
        """Test webhook CORS preflight request."""
        
        # Create OPTIONS request
        options_event = {
            'httpMethod': 'OPTIONS',
            'path': '/payment/webhooks/stripe',
            'headers': {
                'Origin': 'https://dashboard.stripe.com',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'stripe-signature'
            },
            'requestContext': {
                'requestId': f'options-{int(time.time())}',
                'identity': {'sourceIp': '**************'}
            }
        }
        
        response = webhook_handler(options_event, {})
        
        # Should return CORS headers
        assert response['statusCode'] == 200
        assert 'Access-Control-Allow-Origin' in response['headers']
        assert 'Access-Control-Allow-Methods' in response['headers']
        assert 'Access-Control-Allow-Headers' in response['headers']
