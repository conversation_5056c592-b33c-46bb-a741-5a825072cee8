# tests/e2e/auth/test_registration_flow.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Tarea 2.8

"""
End-to-end tests for user registration flow.
Tests the complete registration process from start to finish.
"""

import pytest
import json
import time
from unittest.mock import Mock, patch
from moto import mock_aws

from handlers.register import handler as register_handler
from handlers.verify_email import handler as verify_email_handler
from models.user import User, UserStatus
from models.tenant import Tenant, TenantStatus
from shared.auth import AuthContext


class TestRegistrationFlow:
    """End-to-end tests for user registration flow."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        self.test_password = "TestPassword123!"
        self.test_name = "Test User"
        self.test_company = "Test Company"
    
    @mock_aws
    def test_complete_registration_flow(self):
        """Test complete registration flow from start to verification."""
        
        # Step 1: Register new user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            
            response = register_handler(register_event, {})
            
            # Verify registration response
            assert response['statusCode'] == 201
            
            body = json.loads(response['body'])
            assert body['success'] is True
            assert 'user' in body['data']
            assert 'tenant' in body['data']
            
            user_data = body['data']['user']
            tenant_data = body['data']['tenant']
            
            # Verify user data
            assert user_data['email'] == self.test_email
            assert user_data['name'] == self.test_name
            assert user_data['status'] == UserStatus.PENDING_VERIFICATION.value
            assert user_data['role'] == 'MASTER'
            
            # Verify tenant data
            assert tenant_data['name'] == self.test_company
            assert tenant_data['status'] == TenantStatus.PENDING_VERIFICATION.value
            
            # Verify email was sent
            mock_email.assert_called_once()
            
            # Extract verification token from email call
            email_call_args = mock_email.call_args
            verification_token = email_call_args[1]['verification_token']
            
            assert verification_token is not None
            assert len(verification_token) > 20
        
        # Step 2: Verify email
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'token': verification_token
            }),
            'requestContext': {
                'requestId': 'test-verify-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        verify_response = verify_email_handler(verify_event, {})
        
        # Verify email verification response
        assert verify_response['statusCode'] == 200
        
        verify_body = json.loads(verify_response['body'])
        assert verify_body['success'] is True
        assert 'user' in verify_body['data']
        assert 'tenant' in verify_body['data']
        
        verified_user = verify_body['data']['user']
        verified_tenant = verify_body['data']['tenant']
        
        # Verify user is now active
        assert verified_user['status'] == UserStatus.ACTIVE.value
        assert verified_user['email_verified'] is True
        
        # Verify tenant is now active
        assert verified_tenant['status'] == TenantStatus.ACTIVE.value
    
    @mock_aws
    def test_registration_with_duplicate_email(self):
        """Test registration with duplicate email address."""
        
        # First registration
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            
            # First registration should succeed
            response1 = register_handler(register_event, {})
            assert response1['statusCode'] == 201
            
            # Second registration with same email should fail
            response2 = register_handler(register_event, {})
            assert response2['statusCode'] == 409
            
            body = json.loads(response2['body'])
            assert body['success'] is False
            assert 'already exists' in body['error']['message'].lower()
    
    @mock_aws
    def test_registration_with_invalid_data(self):
        """Test registration with various invalid data scenarios."""
        
        test_cases = [
            # Invalid email
            {
                'data': {
                    'email': 'invalid-email',
                    'password': self.test_password,
                    'name': self.test_name,
                    'company': self.test_company
                },
                'expected_error': 'email'
            },
            # Weak password
            {
                'data': {
                    'email': self.test_email,
                    'password': '123',
                    'name': self.test_name,
                    'company': self.test_company
                },
                'expected_error': 'password'
            },
            # Missing name
            {
                'data': {
                    'email': self.test_email,
                    'password': self.test_password,
                    'company': self.test_company
                },
                'expected_error': 'name'
            },
            # Missing company
            {
                'data': {
                    'email': self.test_email,
                    'password': self.test_password,
                    'name': self.test_name
                },
                'expected_error': 'company'
            }
        ]
        
        for test_case in test_cases:
            register_event = {
                'httpMethod': 'POST',
                'path': '/auth/register',
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps(test_case['data']),
                'requestContext': {
                    'requestId': 'test-request-123',
                    'identity': {
                        'sourceIp': '127.0.0.1'
                    }
                }
            }
            
            response = register_handler(register_event, {})
            
            # Should return validation error
            assert response['statusCode'] == 422
            
            body = json.loads(response['body'])
            assert body['success'] is False
            assert 'validation_errors' in body['error']
            
            # Check that the expected field is in validation errors
            validation_errors = body['error']['validation_errors']
            error_fields = [error['field'] for error in validation_errors]
            assert test_case['expected_error'] in error_fields
    
    @mock_aws
    def test_email_verification_with_invalid_token(self):
        """Test email verification with invalid token."""
        
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'token': 'invalid-token-123'
            }),
            'requestContext': {
                'requestId': 'test-verify-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        response = verify_email_handler(verify_event, {})
        
        # Should return error for invalid token
        assert response['statusCode'] == 400
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'invalid' in body['error']['message'].lower()
    
    @mock_aws
    def test_email_verification_with_expired_token(self):
        """Test email verification with expired token."""
        
        # First register a user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            
            response = register_handler(register_event, {})
            assert response['statusCode'] == 201
            
            # Get the verification token
            email_call_args = mock_email.call_args
            verification_token = email_call_args[1]['verification_token']
        
        # Mock time to simulate token expiration
        with patch('time.time', return_value=time.time() + 25 * 60 * 60):  # 25 hours later
            verify_event = {
                'httpMethod': 'POST',
                'path': '/auth/verify-email',
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'token': verification_token
                }),
                'requestContext': {
                    'requestId': 'test-verify-123',
                    'identity': {
                        'sourceIp': '127.0.0.1'
                    }
                }
            }
            
            response = verify_email_handler(verify_event, {})
            
            # Should return error for expired token
            assert response['statusCode'] == 400
            
            body = json.loads(response['body'])
            assert body['success'] is False
            assert 'expired' in body['error']['message'].lower()
    
    @mock_aws
    def test_registration_tenant_isolation(self):
        """Test that tenant isolation works correctly during registration."""
        
        # Register first user/tenant
        register_event1 = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': '<EMAIL>',
                'password': self.test_password,
                'name': 'User One',
                'company': 'Company One'
            }),
            'requestContext': {
                'requestId': 'test-request-1',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        # Register second user/tenant
        register_event2 = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': '<EMAIL>',
                'password': self.test_password,
                'name': 'User Two',
                'company': 'Company Two'
            }),
            'requestContext': {
                'requestId': 'test-request-2',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            
            # Register both users
            response1 = register_handler(register_event1, {})
            response2 = register_handler(register_event2, {})
            
            assert response1['statusCode'] == 201
            assert response2['statusCode'] == 201
            
            # Extract tenant IDs
            body1 = json.loads(response1['body'])
            body2 = json.loads(response2['body'])
            
            tenant1_id = body1['data']['tenant']['tenant_id']
            tenant2_id = body2['data']['tenant']['tenant_id']
            
            # Verify tenants are different
            assert tenant1_id != tenant2_id
            
            # Verify users belong to different tenants
            user1_tenant = body1['data']['user']['tenant_id']
            user2_tenant = body2['data']['user']['tenant_id']
            
            assert user1_tenant == tenant1_id
            assert user2_tenant == tenant2_id
            assert user1_tenant != user2_tenant
    
    @mock_aws
    def test_registration_rate_limiting(self):
        """Test rate limiting during registration."""
        
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {
                    'sourceIp': '127.0.0.1'
                }
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            
            # First registration should succeed
            response1 = register_handler(register_event, {})
            assert response1['statusCode'] == 201
            
            # Immediate second registration from same IP should be rate limited
            # (This would be handled by API Gateway or WAF in real deployment)
            # For now, we'll test the application-level rate limiting
            
            # Change email to avoid duplicate error
            register_event['body'] = json.dumps({
                'email': '<EMAIL>',
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            })
            
            # This should still succeed as we don't have strict rate limiting implemented yet
            # In production, this would be handled by WAF/API Gateway
            response2 = register_handler(register_event, {})
            # For now, we expect this to succeed
            assert response2['statusCode'] in [201, 429]  # Either success or rate limited
