# tests/e2e/auth/test_password_reset_flow.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Tarea 2.8

"""
End-to-end tests for password reset flow.
Tests the complete password reset process from request to completion.
"""

import pytest
import json
import time
from unittest.mock import Mock, patch
from moto import mock_aws

# Add path for auth service
import sys
import os

from handlers.register import handler as register_handler
from handlers.verify_email import handler as verify_email_handler
from handlers.login import handler as login_handler
from handlers.forgot_password import handler as forgot_password_handler
from handlers.reset_password import handler as reset_password_handler
from models.user import User, UserStatus


class TestPasswordResetFlow:
    """End-to-end tests for password reset flow."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.test_email = "<EMAIL>"
        self.test_password = "TestPassword123!"
        self.new_password = "NewPassword456!"
        self.test_name = "Test User"
        self.test_company = "Test Company"
    
    def _create_verified_user(self):
        """Helper method to create a verified user."""
        # Register user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            register_response = register_handler(register_event, {})
            verification_token = mock_email.call_args[1]['verification_token']
        
        # Verify email
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({'token': verification_token}),
            'requestContext': {
                'requestId': 'test-verify-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        verify_response = verify_email_handler(verify_event, {})
        return json.loads(verify_response['body'])['data']
    
    @mock_aws
    def test_complete_password_reset_flow(self):
        """Test complete password reset flow from request to completion."""
        
        # Create verified user
        user_data = self._create_verified_user()
        
        # Step 1: Request password reset
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:
            mock_email.return_value = True
            
            forgot_response = forgot_password_handler(forgot_event, {})
            
            # Verify forgot password response
            assert forgot_response['statusCode'] == 200
            
            forgot_body = json.loads(forgot_response['body'])
            assert forgot_body['success'] is True
            assert 'message' in forgot_body['data']
            
            # Verify email was sent
            mock_email.assert_called_once()
            
            # Extract reset token from email call
            email_call_args = mock_email.call_args
            reset_token = email_call_args[1]['reset_token']
            
            assert reset_token is not None
            assert len(reset_token) > 20
        
        # Step 2: Reset password using token
        reset_event = {
            'httpMethod': 'POST',
            'path': '/auth/reset-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'token': reset_token,
                'new_password': self.new_password
            }),
            'requestContext': {
                'requestId': 'test-reset-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        reset_response = reset_password_handler(reset_event, {})
        
        # Verify reset response
        assert reset_response['statusCode'] == 200
        
        reset_body = json.loads(reset_response['body'])
        assert reset_body['success'] is True
        assert 'message' in reset_body['data']
        
        # Step 3: Verify old password no longer works
        old_login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password  # Old password
            }),
            'requestContext': {
                'requestId': 'test-login-old-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        old_login_response = login_handler(old_login_event, {})
        assert old_login_response['statusCode'] == 401  # Should fail
        
        # Step 4: Verify new password works
        new_login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.new_password  # New password
            }),
            'requestContext': {
                'requestId': 'test-login-new-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        new_login_response = login_handler(new_login_event, {})
        assert new_login_response['statusCode'] == 200  # Should succeed
        
        new_login_body = json.loads(new_login_response['body'])
        assert new_login_body['success'] is True
        assert 'tokens' in new_login_body['data']
    
    @mock_aws
    def test_forgot_password_with_nonexistent_email(self):
        """Test forgot password with email that doesn't exist."""
        
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': '<EMAIL>'
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:
            mock_email.return_value = True
            
            response = forgot_password_handler(forgot_event, {})
            
            # Should still return success for security (don't reveal if email exists)
            assert response['statusCode'] == 200
            
            body = json.loads(response['body'])
            assert body['success'] is True
            
            # But email should not be sent
            mock_email.assert_not_called()
    
    @mock_aws
    def test_forgot_password_with_unverified_user(self):
        """Test forgot password with unverified user."""
        
        # Register user but don't verify
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_verify_email:
            mock_verify_email.return_value = True
            register_handler(register_event, {})
        
        # Try to reset password
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_reset_email:
            mock_reset_email.return_value = True
            
            response = forgot_password_handler(forgot_event, {})
            
            # Should return success but not send email for unverified users
            assert response['statusCode'] == 200
            
            body = json.loads(response['body'])
            assert body['success'] is True
            
            # Email should not be sent for unverified users
            mock_reset_email.assert_not_called()
    
    @mock_aws
    def test_reset_password_with_invalid_token(self):
        """Test password reset with invalid token."""
        
        reset_event = {
            'httpMethod': 'POST',
            'path': '/auth/reset-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'token': 'invalid-reset-token-123',
                'new_password': self.new_password
            }),
            'requestContext': {
                'requestId': 'test-reset-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = reset_password_handler(reset_event, {})
        
        # Should return error for invalid token
        assert response['statusCode'] == 400
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'invalid' in body['error']['message'].lower()
    
    @mock_aws
    def test_reset_password_with_expired_token(self):
        """Test password reset with expired token."""
        
        # Create verified user
        self._create_verified_user()
        
        # Request password reset
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:
            mock_email.return_value = True
            
            forgot_response = forgot_password_handler(forgot_event, {})
            assert forgot_response['statusCode'] == 200
            
            # Get reset token
            reset_token = mock_email.call_args[1]['reset_token']
        
        # Mock time to simulate token expiration (2 hours later)
        with patch('time.time', return_value=time.time() + 2 * 60 * 60 + 1):
            reset_event = {
                'httpMethod': 'POST',
                'path': '/auth/reset-password',
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps({
                    'token': reset_token,
                    'new_password': self.new_password
                }),
                'requestContext': {
                    'requestId': 'test-reset-123',
                    'identity': {'sourceIp': '127.0.0.1'}
                }
            }
            
            response = reset_password_handler(reset_event, {})
            
            # Should return error for expired token
            assert response['statusCode'] == 400
            
            body = json.loads(response['body'])
            assert body['success'] is False
            assert 'expired' in body['error']['message'].lower()
    
    @mock_aws
    def test_reset_password_with_weak_password(self):
        """Test password reset with weak new password."""
        
        # Create verified user
        self._create_verified_user()
        
        # Request password reset
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:
            mock_email.return_value = True
            
            forgot_response = forgot_password_handler(forgot_event, {})
            assert forgot_response['statusCode'] == 200
            
            # Get reset token
            reset_token = mock_email.call_args[1]['reset_token']
        
        # Try to reset with weak password
        weak_passwords = ['123', 'password', 'abc123', '12345678']
        
        for weak_password in weak_passwords:
            reset_event = {
                'httpMethod': 'POST',
                'path': '/auth/reset-password',
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps({
                    'token': reset_token,
                    'new_password': weak_password
                }),
                'requestContext': {
                    'requestId': 'test-reset-123',
                    'identity': {'sourceIp': '127.0.0.1'}
                }
            }
            
            response = reset_password_handler(reset_event, {})
            
            # Should return validation error for weak password
            assert response['statusCode'] == 422
            
            body = json.loads(response['body'])
            assert body['success'] is False
            assert 'validation_errors' in body['error']
    
    @mock_aws
    def test_reset_password_token_single_use(self):
        """Test that reset tokens can only be used once."""
        
        # Create verified user
        self._create_verified_user()
        
        # Request password reset
        forgot_event = {
            'httpMethod': 'POST',
            'path': '/auth/forgot-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email
            }),
            'requestContext': {
                'requestId': 'test-forgot-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:
            mock_email.return_value = True
            
            forgot_response = forgot_password_handler(forgot_event, {})
            assert forgot_response['statusCode'] == 200
            
            # Get reset token
            reset_token = mock_email.call_args[1]['reset_token']
        
        # First reset should succeed
        reset_event = {
            'httpMethod': 'POST',
            'path': '/auth/reset-password',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'token': reset_token,
                'new_password': self.new_password
            }),
            'requestContext': {
                'requestId': 'test-reset-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        first_response = reset_password_handler(reset_event, {})
        assert first_response['statusCode'] == 200
        
        # Second reset with same token should fail
        reset_event['body'] = json.dumps({
            'token': reset_token,
            'new_password': 'AnotherPassword789!'
        })
        
        second_response = reset_password_handler(reset_event, {})
        assert second_response['statusCode'] == 400
        
        body = json.loads(second_response['body'])
        assert body['success'] is False
        assert 'invalid' in body['error']['message'].lower() or 'used' in body['error']['message'].lower()
    
    @mock_aws
    def test_forgot_password_rate_limiting(self):
        """Test rate limiting for forgot password requests."""
        
        # Create verified user
        self._create_verified_user()
        
        # Make multiple forgot password requests
        responses = []
        
        for i in range(5):
            forgot_event = {
                'httpMethod': 'POST',
                'path': '/auth/forgot-password',
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps({
                    'email': self.test_email
                }),
                'requestContext': {
                    'requestId': f'test-forgot-{i}',
                    'identity': {'sourceIp': '127.0.0.1'}
                }
            }
            
            with patch('src.auth.services.email_service.email_service.send_password_reset_email') as mock_email:
                mock_email.return_value = True
                
                response = forgot_password_handler(forgot_event, {})
                responses.append(response['statusCode'])
        
        # First few requests should succeed
        for status_code in responses[:2]:
            assert status_code == 200
        
        # Later requests might be rate limited (if implemented)
        # For now, they should still succeed as rate limiting is not strictly implemented
        for status_code in responses[2:]:
            assert status_code in [200, 429]
