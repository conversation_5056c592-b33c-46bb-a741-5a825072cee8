# tests/e2e/auth/test_login_flow.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Tarea 2.8

"""
End-to-end tests for user login flow.
Tests the complete login process including token refresh.
"""

import pytest
import json
import time
from unittest.mock import Mock, patch
from moto import mock_aws

# Add path for auth service
import sys
import os

from handlers.register import handler as register_handler
from handlers.verify_email import handler as verify_email_handler
from handlers.login import handler as login_handler
from handlers.refresh import handler as refresh_handler
from models.user import User, UserStatus
from shared.auth import jwt_manager


class TestLoginFlow:
    """End-to-end tests for user login flow."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment with verified user."""
        self.test_email = "<EMAIL>"
        self.test_password = "TestPassword123!"
        self.test_name = "Test User"
        self.test_company = "Test Company"
        
        # We'll create a verified user in each test that needs it
    
    def _create_verified_user(self):
        """Helper method to create a verified user."""
        # Register user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            register_response = register_handler(register_event, {})
            
            # Get verification token
            verification_token = mock_email.call_args[1]['verification_token']
        
        # Verify email
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({'token': verification_token}),
            'requestContext': {
                'requestId': 'test-verify-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        verify_response = verify_email_handler(verify_event, {})
        
        return json.loads(register_response['body'])['data'], json.loads(verify_response['body'])['data']
    
    @mock_aws
    def test_successful_login_flow(self):
        """Test successful login with verified user."""
        
        # Create verified user
        register_data, verify_data = self._create_verified_user()
        
        # Login
        login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password
            }),
            'requestContext': {
                'requestId': 'test-login-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = login_handler(login_event, {})
        
        # Verify login response
        assert response['statusCode'] == 200
        
        body = json.loads(response['body'])
        assert body['success'] is True
        assert 'tokens' in body['data']
        assert 'user' in body['data']
        assert 'tenant' in body['data']
        
        tokens = body['data']['tokens']
        user_data = body['data']['user']
        
        # Verify tokens
        assert 'access_token' in tokens
        assert 'refresh_token' in tokens
        assert tokens['token_type'] == 'Bearer'
        assert tokens['expires_in'] > 0
        
        # Verify user data
        assert user_data['email'] == self.test_email
        assert user_data['name'] == self.test_name
        assert user_data['status'] == UserStatus.ACTIVE.value
        assert user_data['email_verified'] is True
        
        # Verify token is valid
        access_token = tokens['access_token']
        decoded_token = jwt_manager.decode_token(access_token)
        
        assert decoded_token is not None
        assert decoded_token['email'] == self.test_email
        assert decoded_token['tenant_id'] == user_data['tenant_id']
    
    @mock_aws
    def test_login_with_unverified_user(self):
        """Test login attempt with unverified user."""
        
        # Register user but don't verify
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password,
                'name': self.test_name,
                'company': self.test_company
            }),
            'requestContext': {
                'requestId': 'test-request-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            register_handler(register_event, {})
        
        # Attempt login
        login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password
            }),
            'requestContext': {
                'requestId': 'test-login-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = login_handler(login_event, {})
        
        # Should fail for unverified user
        assert response['statusCode'] == 403
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'verification' in body['error']['message'].lower()
    
    @mock_aws
    def test_login_with_invalid_credentials(self):
        """Test login with invalid credentials."""
        
        # Create verified user
        self._create_verified_user()
        
        # Test cases for invalid credentials
        test_cases = [
            # Wrong password
            {
                'email': self.test_email,
                'password': 'WrongPassword123!',
                'expected_error': 'credentials'
            },
            # Wrong email
            {
                'email': '<EMAIL>',
                'password': self.test_password,
                'expected_error': 'credentials'
            },
            # Empty password
            {
                'email': self.test_email,
                'password': '',
                'expected_error': 'password'
            },
            # Invalid email format
            {
                'email': 'invalid-email',
                'password': self.test_password,
                'expected_error': 'email'
            }
        ]
        
        for test_case in test_cases:
            login_event = {
                'httpMethod': 'POST',
                'path': '/auth/login',
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps({
                    'email': test_case['email'],
                    'password': test_case['password']
                }),
                'requestContext': {
                    'requestId': 'test-login-123',
                    'identity': {'sourceIp': '127.0.0.1'}
                }
            }
            
            response = login_handler(login_event, {})
            
            # Should return error
            assert response['statusCode'] in [400, 401, 422]
            
            body = json.loads(response['body'])
            assert body['success'] is False
    
    @mock_aws
    def test_token_refresh_flow(self):
        """Test token refresh functionality."""
        
        # Create verified user and login
        self._create_verified_user()
        
        login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': self.test_email,
                'password': self.test_password
            }),
            'requestContext': {
                'requestId': 'test-login-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        login_response = login_handler(login_event, {})
        assert login_response['statusCode'] == 200
        
        login_body = json.loads(login_response['body'])
        refresh_token = login_body['data']['tokens']['refresh_token']
        
        # Use refresh token to get new access token
        refresh_event = {
            'httpMethod': 'POST',
            'path': '/auth/refresh',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'refresh_token': refresh_token
            }),
            'requestContext': {
                'requestId': 'test-refresh-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        refresh_response = refresh_handler(refresh_event, {})
        
        # Verify refresh response
        assert refresh_response['statusCode'] == 200
        
        refresh_body = json.loads(refresh_response['body'])
        assert refresh_body['success'] is True
        assert 'tokens' in refresh_body['data']
        
        new_tokens = refresh_body['data']['tokens']
        
        # Verify new tokens
        assert 'access_token' in new_tokens
        assert 'refresh_token' in new_tokens
        assert new_tokens['token_type'] == 'Bearer'
        
        # Verify new access token is different and valid
        new_access_token = new_tokens['access_token']
        original_access_token = login_body['data']['tokens']['access_token']
        
        assert new_access_token != original_access_token
        
        decoded_new_token = jwt_manager.decode_token(new_access_token)
        assert decoded_new_token is not None
        assert decoded_new_token['email'] == self.test_email
    
    @mock_aws
    def test_refresh_with_invalid_token(self):
        """Test refresh with invalid refresh token."""
        
        refresh_event = {
            'httpMethod': 'POST',
            'path': '/auth/refresh',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'refresh_token': 'invalid-refresh-token'
            }),
            'requestContext': {
                'requestId': 'test-refresh-123',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        response = refresh_handler(refresh_event, {})
        
        # Should return error for invalid token
        assert response['statusCode'] == 401
        
        body = json.loads(response['body'])
        assert body['success'] is False
        assert 'invalid' in body['error']['message'].lower()
    
    @mock_aws
    def test_login_rate_limiting(self):
        """Test login rate limiting for failed attempts."""
        
        # Create verified user
        self._create_verified_user()
        
        # Simulate multiple failed login attempts
        failed_attempts = []
        
        for i in range(6):  # Attempt 6 failed logins
            login_event = {
                'httpMethod': 'POST',
                'path': '/auth/login',
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps({
                    'email': self.test_email,
                    'password': 'WrongPassword123!'
                }),
                'requestContext': {
                    'requestId': f'test-login-{i}',
                    'identity': {'sourceIp': '127.0.0.1'}
                }
            }
            
            response = login_handler(login_event, {})
            failed_attempts.append(response['statusCode'])
        
        # First few attempts should return 401 (unauthorized)
        # Later attempts might be rate limited (429) if implemented
        for status_code in failed_attempts[:3]:
            assert status_code == 401
        
        # After multiple failures, should still return 401 or 429
        for status_code in failed_attempts[3:]:
            assert status_code in [401, 429]
    
    @mock_aws
    def test_concurrent_login_sessions(self):
        """Test multiple concurrent login sessions for same user."""
        
        # Create verified user
        self._create_verified_user()
        
        # Login from multiple "devices" (different request IDs)
        sessions = []
        
        for i in range(3):
            login_event = {
                'httpMethod': 'POST',
                'path': '/auth/login',
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps({
                    'email': self.test_email,
                    'password': self.test_password
                }),
                'requestContext': {
                    'requestId': f'test-login-device-{i}',
                    'identity': {'sourceIp': f'127.0.0.{i+1}'}
                }
            }
            
            response = login_handler(login_event, {})
            assert response['statusCode'] == 200
            
            body = json.loads(response['body'])
            sessions.append(body['data']['tokens'])
        
        # Verify all sessions have different tokens
        access_tokens = [session['access_token'] for session in sessions]
        refresh_tokens = [session['refresh_token'] for session in sessions]
        
        # All access tokens should be different
        assert len(set(access_tokens)) == 3
        
        # All refresh tokens should be different
        assert len(set(refresh_tokens)) == 3
        
        # All tokens should be valid
        for access_token in access_tokens:
            decoded = jwt_manager.decode_token(access_token)
            assert decoded is not None
            assert decoded['email'] == self.test_email
