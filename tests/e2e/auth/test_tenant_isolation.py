# tests/e2e/auth/test_tenant_isolation.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Tarea 2.8

"""
End-to-end tests for tenant isolation.
Tests that multi-tenancy works correctly and data is properly isolated.
"""

import pytest
import json
import time
from unittest.mock import Mock, patch
from moto import mock_aws

from handlers.register import handler as register_handler
from handlers.verify_email import handler as verify_email_handler
from handlers.login import handler as login_handler
from handlers.authorizer import handler as authorizer_handler
from models.user import User, UserStatus
from models.tenant import Tenant, TenantStatus
from shared.auth import jwt_manager


class TestTenantIsolation:
    """End-to-end tests for tenant isolation."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment with multiple tenants."""
        self.tenant1_data = {
            'email': '<EMAIL>',
            'password': 'Password123!',
            'name': 'User One',
            'company': 'Company One'
        }
        
        self.tenant2_data = {
            'email': '<EMAIL>',
            'password': 'Password456!',
            'name': 'User Two',
            'company': 'Company Two'
        }
    
    def _create_verified_tenant(self, tenant_data):
        """Helper method to create a verified tenant and user."""
        # Register user
        register_event = {
            'httpMethod': 'POST',
            'path': '/auth/register',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps(tenant_data),
            'requestContext': {
                'requestId': f'test-request-{tenant_data["email"]}',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        with patch('src.auth.services.email_service.email_service.send_verification_email') as mock_email:
            mock_email.return_value = True
            register_response = register_handler(register_event, {})
            verification_token = mock_email.call_args[1]['verification_token']
        
        # Verify email
        verify_event = {
            'httpMethod': 'POST',
            'path': '/auth/verify-email',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({'token': verification_token}),
            'requestContext': {
                'requestId': f'test-verify-{tenant_data["email"]}',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        verify_response = verify_email_handler(verify_event, {})
        
        # Login to get tokens
        login_event = {
            'httpMethod': 'POST',
            'path': '/auth/login',
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'email': tenant_data['email'],
                'password': tenant_data['password']
            }),
            'requestContext': {
                'requestId': f'test-login-{tenant_data["email"]}',
                'identity': {'sourceIp': '127.0.0.1'}
            }
        }
        
        login_response = login_handler(login_event, {})
        
        register_data = json.loads(register_response['body'])['data']
        verify_data = json.loads(verify_response['body'])['data']
        login_data = json.loads(login_response['body'])['data']
        
        return {
            'register': register_data,
            'verify': verify_data,
            'login': login_data,
            'tenant_id': verify_data['tenant']['tenant_id'],
            'user_id': verify_data['user']['user_id'],
            'access_token': login_data['tokens']['access_token']
        }
    
    @mock_aws
    def test_tenant_data_isolation(self):
        """Test that tenant data is properly isolated."""
        
        # Create two separate tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Verify tenants are different
        assert tenant1['tenant_id'] != tenant2['tenant_id']
        assert tenant1['user_id'] != tenant2['user_id']
        
        # Verify tenant data
        tenant1_info = tenant1['verify']['tenant']
        tenant2_info = tenant2['verify']['tenant']
        
        assert tenant1_info['name'] == self.tenant1_data['company']
        assert tenant2_info['name'] == self.tenant2_data['company']
        assert tenant1_info['tenant_id'] != tenant2_info['tenant_id']
        
        # Verify user data
        user1_info = tenant1['verify']['user']
        user2_info = tenant2['verify']['user']
        
        assert user1_info['email'] == self.tenant1_data['email']
        assert user2_info['email'] == self.tenant2_data['email']
        assert user1_info['tenant_id'] == tenant1['tenant_id']
        assert user2_info['tenant_id'] == tenant2['tenant_id']
        assert user1_info['tenant_id'] != user2_info['tenant_id']
    
    @mock_aws
    def test_token_tenant_isolation(self):
        """Test that JWT tokens contain correct tenant information."""
        
        # Create two tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Decode tokens
        token1_data = jwt_manager.decode_token(tenant1['access_token'])
        token2_data = jwt_manager.decode_token(tenant2['access_token'])
        
        # Verify token data
        assert token1_data['tenant_id'] == tenant1['tenant_id']
        assert token2_data['tenant_id'] == tenant2['tenant_id']
        assert token1_data['tenant_id'] != token2_data['tenant_id']
        
        assert token1_data['email'] == self.tenant1_data['email']
        assert token2_data['email'] == self.tenant2_data['email']
        
        assert token1_data['user_id'] == tenant1['user_id']
        assert token2_data['user_id'] == tenant2['user_id']
        assert token1_data['user_id'] != token2_data['user_id']
    
    @mock_aws
    def test_authorizer_tenant_isolation(self):
        """Test that API Gateway authorizer enforces tenant isolation."""
        
        # Create two tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Test authorizer with tenant1 token
        auth_event1 = {
            'type': 'TOKEN',
            'authorizationToken': f'Bearer {tenant1["access_token"]}',
            'methodArn': 'arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/resource'
        }
        
        auth_response1 = authorizer_handler(auth_event1, {})
        
        # Verify authorization response
        assert auth_response1['principalId'] == tenant1['user_id']
        
        # Extract context
        context1 = auth_response1['context']
        assert context1['tenant_id'] == tenant1['tenant_id']
        assert context1['user_id'] == tenant1['user_id']
        assert context1['email'] == self.tenant1_data['email']
        assert context1['role'] == 'MASTER'
        
        # Test authorizer with tenant2 token
        auth_event2 = {
            'type': 'TOKEN',
            'authorizationToken': f'Bearer {tenant2["access_token"]}',
            'methodArn': 'arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/resource'
        }
        
        auth_response2 = authorizer_handler(auth_event2, {})
        
        # Verify authorization response
        assert auth_response2['principalId'] == tenant2['user_id']
        
        # Extract context
        context2 = auth_response2['context']
        assert context2['tenant_id'] == tenant2['tenant_id']
        assert context2['user_id'] == tenant2['user_id']
        assert context2['email'] == self.tenant2_data['email']
        assert context2['role'] == 'MASTER'
        
        # Verify contexts are different
        assert context1['tenant_id'] != context2['tenant_id']
        assert context1['user_id'] != context2['user_id']
        assert context1['email'] != context2['email']
    
    @mock_aws
    def test_cross_tenant_token_rejection(self):
        """Test that tokens from one tenant cannot access another tenant's resources."""
        
        # Create two tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Try to use tenant1's token to access tenant2's data
        # This would be tested in actual API endpoints, but we can test the token validation
        
        token1_data = jwt_manager.decode_token(tenant1['access_token'])
        token2_data = jwt_manager.decode_token(tenant2['access_token'])
        
        # Verify that tokens contain different tenant IDs
        assert token1_data['tenant_id'] != token2_data['tenant_id']
        
        # In a real API call, the handler would check:
        # if auth_context.tenant_id != requested_tenant_id:
        #     raise AuthorizationException("Access denied")
        
        # Simulate this check
        def simulate_api_call(token, requested_tenant_id):
            token_data = jwt_manager.decode_token(token)
            if token_data['tenant_id'] != requested_tenant_id:
                return {'statusCode': 403, 'error': 'Access denied'}
            return {'statusCode': 200, 'data': 'success'}
        
        # Tenant1 accessing their own data - should succeed
        result1 = simulate_api_call(tenant1['access_token'], tenant1['tenant_id'])
        assert result1['statusCode'] == 200
        
        # Tenant1 trying to access tenant2's data - should fail
        result2 = simulate_api_call(tenant1['access_token'], tenant2['tenant_id'])
        assert result2['statusCode'] == 403
        
        # Tenant2 accessing their own data - should succeed
        result3 = simulate_api_call(tenant2['access_token'], tenant2['tenant_id'])
        assert result3['statusCode'] == 200
        
        # Tenant2 trying to access tenant1's data - should fail
        result4 = simulate_api_call(tenant2['access_token'], tenant1['tenant_id'])
        assert result4['statusCode'] == 403
    
    @mock_aws
    def test_database_tenant_isolation(self):
        """Test that database queries are properly scoped to tenant."""
        
        # Create two tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Test user lookup by tenant
        user1 = User.get_by_email(self.tenant1_data['email'], tenant1['tenant_id'])
        user2 = User.get_by_email(self.tenant2_data['email'], tenant2['tenant_id'])
        
        # Verify users exist in their respective tenants
        assert user1 is not None
        assert user2 is not None
        assert user1.tenant_id == tenant1['tenant_id']
        assert user2.tenant_id == tenant2['tenant_id']
        
        # Test cross-tenant user lookup should fail
        user1_in_tenant2 = User.get_by_email(self.tenant1_data['email'], tenant2['tenant_id'])
        user2_in_tenant1 = User.get_by_email(self.tenant2_data['email'], tenant1['tenant_id'])
        
        assert user1_in_tenant2 is None
        assert user2_in_tenant1 is None
        
        # Test tenant lookup
        tenant1_obj = Tenant.get_by_id(tenant1['tenant_id'])
        tenant2_obj = Tenant.get_by_id(tenant2['tenant_id'])
        
        assert tenant1_obj is not None
        assert tenant2_obj is not None
        assert tenant1_obj.tenant_id != tenant2_obj.tenant_id
        assert tenant1_obj.name == self.tenant1_data['company']
        assert tenant2_obj.name == self.tenant2_data['company']
    
    @mock_aws
    def test_tenant_user_roles_isolation(self):
        """Test that user roles are properly isolated per tenant."""
        
        # Create two tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Both users should be MASTER in their respective tenants
        token1_data = jwt_manager.decode_token(tenant1['access_token'])
        token2_data = jwt_manager.decode_token(tenant2['access_token'])
        
        assert token1_data['role'] == 'MASTER'
        assert token2_data['role'] == 'MASTER'
        
        # Verify role is scoped to tenant
        assert token1_data['tenant_id'] == tenant1['tenant_id']
        assert token2_data['tenant_id'] == tenant2['tenant_id']
        
        # In a real scenario, we would test that:
        # - MASTER role in tenant1 cannot manage users in tenant2
        # - MASTER role in tenant2 cannot manage users in tenant1
        
        def simulate_user_management(token, target_tenant_id):
            token_data = jwt_manager.decode_token(token)
            if token_data['tenant_id'] != target_tenant_id:
                return {'statusCode': 403, 'error': 'Cannot manage users in different tenant'}
            if token_data['role'] != 'MASTER':
                return {'statusCode': 403, 'error': 'Insufficient permissions'}
            return {'statusCode': 200, 'data': 'User management allowed'}
        
        # Tenant1 MASTER managing tenant1 users - should succeed
        result1 = simulate_user_management(tenant1['access_token'], tenant1['tenant_id'])
        assert result1['statusCode'] == 200
        
        # Tenant1 MASTER trying to manage tenant2 users - should fail
        result2 = simulate_user_management(tenant1['access_token'], tenant2['tenant_id'])
        assert result2['statusCode'] == 403
        
        # Tenant2 MASTER managing tenant2 users - should succeed
        result3 = simulate_user_management(tenant2['access_token'], tenant2['tenant_id'])
        assert result3['statusCode'] == 200
        
        # Tenant2 MASTER trying to manage tenant1 users - should fail
        result4 = simulate_user_management(tenant2['access_token'], tenant1['tenant_id'])
        assert result4['statusCode'] == 403
    
    @mock_aws
    def test_tenant_resource_naming_isolation(self):
        """Test that tenant resources are properly named and isolated."""
        
        # Create two tenants
        tenant1 = self._create_verified_tenant(self.tenant1_data)
        tenant2 = self._create_verified_tenant(self.tenant2_data)
        
        # Verify tenant IDs are UUIDs and different
        import re
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        
        assert re.match(uuid_pattern, tenant1['tenant_id'])
        assert re.match(uuid_pattern, tenant2['tenant_id'])
        assert tenant1['tenant_id'] != tenant2['tenant_id']
        
        # Verify user IDs are UUIDs and different
        assert re.match(uuid_pattern, tenant1['user_id'])
        assert re.match(uuid_pattern, tenant2['user_id'])
        assert tenant1['user_id'] != tenant2['user_id']
        
        # In a real deployment, we would also test:
        # - S3 bucket prefixes: tenant1/{tenant_id}/files vs tenant2/{tenant_id}/files
        # - DynamoDB partition keys: TENANT#{tenant_id}
        # - CloudWatch log groups: /aws/lambda/platform-{env}-{tenant_id}
        
        # Simulate S3 key generation
        def generate_s3_key(tenant_id, filename):
            return f"tenant-data/{tenant_id}/{filename}"
        
        s3_key1 = generate_s3_key(tenant1['tenant_id'], 'document.pdf')
        s3_key2 = generate_s3_key(tenant2['tenant_id'], 'document.pdf')
        
        assert tenant1['tenant_id'] in s3_key1
        assert tenant2['tenant_id'] in s3_key2
        assert s3_key1 != s3_key2
        
        # Simulate DynamoDB partition key generation
        def generate_partition_key(tenant_id, entity_type, entity_id):
            return f"TENANT#{tenant_id}#{entity_type}#{entity_id}"
        
        pk1 = generate_partition_key(tenant1['tenant_id'], 'USER', tenant1['user_id'])
        pk2 = generate_partition_key(tenant2['tenant_id'], 'USER', tenant2['user_id'])
        
        assert tenant1['tenant_id'] in pk1
        assert tenant2['tenant_id'] in pk2
        assert pk1 != pk2
