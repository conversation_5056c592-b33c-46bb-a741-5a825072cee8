#!/usr/bin/env python3
# tests/test_auth_simple.py
# Simple test for auth functionality

"""
Simple test to verify auth imports and basic functionality.
"""

import pytest

def test_auth_import():
    """Test that auth modules can be imported."""
    from shared.auth import <PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON>TManager, AuthContext
    
    # Test basic instantiation
    pm = PasswordManager()
    assert pm is not None
    
    jwt_manager = JWTManager()
    assert jwt_manager is not None

def test_password_hashing():
    """Test basic password hashing."""
    from shared.auth import PasswordManager
    
    pm = PasswordManager()
    password = "TestPassword123!"
    
    # Test hashing
    hashed = pm.hash_password(password)
    assert hashed is not None
    assert hashed != password
    
    # Test verification
    assert pm.verify_password(password, hashed) is True
    assert pm.verify_password("wrong_password", hashed) is False

if __name__ == "__main__":
    test_auth_import()
    test_password_hashing()
    print("✅ All simple auth tests passed!")
