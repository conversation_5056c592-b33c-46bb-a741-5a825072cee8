#!/usr/bin/env python3
# tests/test_security_improvements.py
# Test security improvements implemented in Phase 3

"""
Test security improvements:
1. Standardized bcrypt implementation
2. Secure fallback handling
3. JWT blacklist functionality
"""

import os
import pytest
from unittest.mock import patch, MagicMock

# Set JWT secret before importing auth modules
os.environ['JWT_SECRET'] = 'test-jwt-secret-for-testing-purposes-very-long-and-secure'

from shared.auth import PasswordManager, get_jwt_manager
from shared.exceptions import InvalidTokenException

class TestSecurityImprovements:
    """Test security improvements implemented in Phase 3."""
    
    def test_password_manager_bcrypt_configuration(self):
        """Test that PasswordManager uses secure bcrypt configuration."""
        pm = PasswordManager()
        
        # Test that bcrypt context is properly configured
        assert 'bcrypt' in pm.pwd_context.schemes()
        
        # Test password hashing produces bcrypt format
        password = "TestPassword123!"
        hashed = pm.hash_password(password)
        
        # bcrypt hashes start with $2b$ and are 60 characters
        assert hashed.startswith('$2b$')
        assert len(hashed) == 60
        
        # Verify password works
        assert pm.verify_password(password, hashed) is True
        assert pm.verify_password("wrong_password", hashed) is False
    
    def test_jwt_manager_secure_fallback(self):
        """Test JWT manager secure fallback behavior."""
        # Test with secure environment variable
        with patch.dict(os.environ, {'JWT_SECRET': 'secure-jwt-secret-with-more-than-32-characters'}):
            jwt_manager = get_jwt_manager()
            assert jwt_manager is not None
    
    def test_jwt_manager_rejects_weak_secrets(self):
        """Test JWT manager rejects weak secrets."""
        # Reset global JWT manager
        import shared.auth
        shared.auth._jwt_manager = None

        # Test with weak secret
        with patch.dict(os.environ, {'JWT_SECRET': 'weak'}):
            with pytest.raises(RuntimeError, match="JWT secret is too weak"):
                get_jwt_manager()
    
    def test_jwt_manager_requires_secret(self):
        """Test JWT manager requires some form of secret."""
        # Reset global JWT manager
        import shared.auth
        shared.auth._jwt_manager = None

        # Test with no secret
        with patch.dict(os.environ, {}, clear=True):
            with patch('shared.secrets_manager.secrets_client') as mock_secrets:
                mock_secrets.get_secret.side_effect = Exception("No secret")
                with pytest.raises(RuntimeError, match="JWT secret not available"):
                    get_jwt_manager()
    
    @patch('shared.auth.get_jwt_manager')
    def test_blacklist_functionality(self, mock_get_jwt_manager):
        """Test JWT blacklist functionality."""
        # Mock JWT manager
        mock_jwt_manager = MagicMock()
        mock_get_jwt_manager.return_value = mock_jwt_manager
        
        # Test token blacklist checking
        mock_jwt_manager._is_token_blacklisted.return_value = True
        mock_jwt_manager.verify_token.side_effect = InvalidTokenException("Token has been revoked")
        
        # This should raise InvalidTokenException for blacklisted token
        with pytest.raises(InvalidTokenException, match="Token has been revoked"):
            mock_jwt_manager.verify_token("blacklisted_token")
    
    def test_password_service_uses_shared_implementation(self):
        """Test that password service uses shared implementation."""
        from src.services.password_service import PasswordService
        
        ps = PasswordService()
        
        # Test that it has shared password manager
        assert hasattr(ps, 'shared_password_manager')
        assert ps.shared_password_manager is not None
        
        # Test password hashing produces bcrypt format
        password = "TestPassword123!"
        hashed = ps.hash_password(password)
        
        # Should use bcrypt format
        assert hashed.startswith('$2b$')
        assert len(hashed) == 60
        
        # Verify password works
        assert ps.verify_password(password, hashed) is True
    
    def test_admin_service_uses_secure_hashing(self):
        """Test that admin service uses secure hashing."""
        from services.admin_user_service import AdminUserService
        
        admin_service = AdminUserService()
        
        # Test password hashing
        password = "AdminPassword123!"
        hashed = admin_service._hash_password(password)
        
        # Should use bcrypt format (not SHA256)
        assert hashed.startswith('$2b$')
        assert len(hashed) == 60
        assert 'sha256' not in hashed.lower()
    
    def test_no_hardcoded_fallback_keys(self):
        """Test that no hardcoded fallback keys are present."""
        # Reset global JWT manager
        import shared.auth
        shared.auth._jwt_manager = None

        # Test auth.py doesn't have hardcoded fallback
        with patch.dict(os.environ, {}, clear=True):
            with patch('shared.secrets_manager.secrets_client') as mock_secrets:
                mock_secrets.get_secret.side_effect = Exception("No secret")

                # Should raise error, not use hardcoded fallback
                with pytest.raises(RuntimeError):
                    get_jwt_manager()
    
    def test_secure_token_generation(self):
        """Test secure token generation."""
        pm = PasswordManager()
        
        # Test secure password generation
        password = pm.generate_secure_password(16)
        assert len(password) == 16
        assert isinstance(password, str)
        
        # Test secure token generation
        token = pm.generate_secure_token(32)
        assert len(token) > 30  # Base64 encoding
        assert isinstance(token, str)
        
        # Tokens should be different each time
        token2 = pm.generate_secure_token(32)
        assert token != token2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
