# Script para instalar la capa shared localmente para desarrollo en Windows
# Simula el comportamiento de AWS Lambda Layers en desarrollo local

Write-Host "🔧 Installing Agent SCL Shared Layer for Local Development" -ForegroundColor Cyan
Write-Host "==========================================================" -ForegroundColor Cyan

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "shared\python")) {
    Write-Host "❌ Error: shared\python directory not found." -ForegroundColor Red
    Write-Host "   Make sure you're running this script from the project root." -ForegroundColor Red
    exit 1
}

# Verificar que Python está disponible
$pythonCmd = $null
if (Get-Command python -ErrorAction SilentlyContinue) {
    $pythonCmd = "python"
} elseif (Get-Command python3 -ErrorAction SilentlyContinue) {
    $pythonCmd = "python3"
} elseif (Get-Command py -ErrorAction SilentlyContinue) {
    $pythonCmd = "py"
} else {
    Write-Host "❌ Error: Python is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Verificar que pip está disponible
$pipCmd = $null
if (Get-Command pip -ErrorAction SilentlyContinue) {
    $pipCmd = "pip"
} elseif (Get-Command pip3 -ErrorAction SilentlyContinue) {
    $pipCmd = "pip3"
} else {
    Write-Host "❌ Error: pip is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host "📦 Using Python: $pythonCmd" -ForegroundColor Green
Write-Host "📦 Using pip: $pipCmd" -ForegroundColor Green
Write-Host ""

# Opción 1: Instalación editable (recomendada)
Write-Host "🚀 Installing shared layer as editable package..." -ForegroundColor Yellow
Write-Host "   This allows changes in shared\ to be reflected immediately" -ForegroundColor Yellow
Write-Host ""

Set-Location "shared\python"

# Instalar en modo editable
& $pipCmd install -e .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Shared layer installed successfully as editable package!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 What this means:" -ForegroundColor Cyan
    Write-Host "   - You can import 'shared' from anywhere in your Python environment" -ForegroundColor White
    Write-Host "   - Changes in shared\ are reflected immediately (no reinstall needed)" -ForegroundColor White
    Write-Host "   - Works exactly like AWS Lambda Layers in local development" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Test the installation:" -ForegroundColor Cyan
    Write-Host "   python -c `"from shared.models import UserRole; print('✅ Import successful!')`"" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 To uninstall later:" -ForegroundColor Cyan
    Write-Host "   pip uninstall agent-scl-shared" -ForegroundColor White
} else {
    Write-Host "❌ Failed to install shared layer" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Alternative installation methods:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Method 2 - Add to PYTHONPATH:" -ForegroundColor Cyan
    $currentPath = Get-Location
    Write-Host "   `$env:PYTHONPATH = `"`$env:PYTHONPATH;$currentPath`"" -ForegroundColor White
    Write-Host ""
    Write-Host "Method 3 - Manual sys.path (in your code):" -ForegroundColor Cyan
    Write-Host "   import sys" -ForegroundColor White
    Write-Host "   sys.path.append('$currentPath')" -ForegroundColor White
    Set-Location "..\..\"
    exit 1
}

Set-Location "..\..\"

Write-Host ""
Write-Host "🎉 Setup complete! You can now use 'from shared import ...' in your services." -ForegroundColor Green
Write-Host ""
Write-Host "💡 Pro tip: If you're using a virtual environment, make sure it's activated" -ForegroundColor Yellow
Write-Host "   before running your serverless offline services." -ForegroundColor Yellow
