# Terraform
*.tfstate
*.tfstate.*
*.tfvars
*.tfplan
.terraform/
.terraform.lock.hcl

# Environment variables
.env*
!.env.example

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# AWS
.aws/
*.pem

# Serverless
.serverless/
.serverless_plugins/

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.cache/

# Backup files
*.bak
*.backup

# Local configuration
config/local.json
secrets.json

# Documentation builds
docs/_build/
