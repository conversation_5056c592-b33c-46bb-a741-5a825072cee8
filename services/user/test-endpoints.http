# User Service - Local Testing Endpoints
# Use with REST Client extension in VS Code or similar HTTP client

### Variables
@baseUrl = http://localhost:3001
@authToken = Bearer your-jwt-token-here
@userId = user-123
@tenantId = tenant-123

### 1. Get User Profile
GET {{baseUrl}}/user/profile
Authorization: {{authToken}}
Content-Type: application/json

### 2. Update User Profile
PUT {{baseUrl}}/user/profile
Authorization: {{authToken}}
Content-Type: application/json

{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone_number": "+1234567890",
  "timezone": "America/New_York",
  "language": "en"
}

### 3. Change Password
POST {{baseUrl}}/user/change-password
Authorization: {{authToken}}
Content-Type: application/json

{
  "current_password": "currentPassword123!",
  "new_password": "newPassword123!"
}

### 4. Bulk Invite Users
POST {{baseUrl}}/user/bulk-invite
Authorization: {{authToken}}
Content-Type: application/json

{
  "invitations": [
    {
      "email": "<EMAIL>",
      "first_name": "User",
      "last_name": "One",
      "role": "MEMBER"
    },
    {
      "email": "<EMAIL>",
      "first_name": "User",
      "last_name": "Two",
      "role": "VIEWER"
    }
  ],
  "send_emails": true,
  "custom_message": "Welcome to our platform!"
}

### 5. Deactivate User
POST {{baseUrl}}/user/{{userId}}/deactivate
Authorization: {{authToken}}
Content-Type: application/json

{
  "reason": "User requested account deactivation"
}

### 6. Update User Role
PUT {{baseUrl}}/user/{{userId}}/role
Authorization: {{authToken}}
Content-Type: application/json

{
  "role": "ADMIN"
}

### 7. Export Users
GET {{baseUrl}}/user/export?format=csv&include_inactive=false
Authorization: {{authToken}}
Content-Type: application/json

### 8. Reset User Password (Admin)
POST {{baseUrl}}/user/reset-password
Authorization: {{authToken}}
Content-Type: application/json

{
  "user_id": "{{userId}}",
  "new_password": "NewSecurePassword123!",
  "reason": "user_forgot_password",
  "notify_user": true
}

### 9. Impersonate User (Master only)
POST {{baseUrl}}/user/impersonate
Authorization: {{authToken}}
Content-Type: application/json

{
  "target_user_id": "{{userId}}",
  "reason": "customer_support_request",
  "duration_minutes": 30
}

### 10. Health Check (if available)
GET {{baseUrl}}/health
Content-Type: application/json
