#!/bin/bash

# User Service - Local Development Script
# Ejecuta el servicio user en modo offline para desarrollo local

echo "🚀 Starting User Service in Local Development Mode..."

# Verificar que estamos en el directorio correcto
if [ ! -f "serverless.yml" ]; then
    echo "❌ Error: serverless.yml not found. Make sure you're in the services/user directory"
    exit 1
fi

# Verificar que serverless-offline está instalado
if ! npm list serverless-offline > /dev/null 2>&1; then
    echo "📦 Installing serverless-offline..."
    npm install --save-dev serverless-offline
fi

# Configurar variables de entorno para desarrollo local
export PROJECT_NAME=agent-scl
export ENVIRONMENT=dev
export REGION=us-east-1
export STAGE=dev

# DynamoDB Local (opcional - descomenta si usas DynamoDB local)
# export DYNAMODB_ENDPOINT=http://localhost:8000

# JWT Secret para desarrollo (cambiar por uno real)
export JWT_SECRET=dev-jwt-secret-key-change-in-production

echo "📋 Environment Variables:"
echo "  PROJECT_NAME: $PROJECT_NAME"
echo "  ENVIRONMENT: $ENVIRONMENT"
echo "  REGION: $REGION"
echo "  STAGE: $STAGE"

echo ""
echo "🌐 Starting serverless offline..."
echo "📍 User Service will be available at: http://localhost:3001"
echo ""
echo "📚 Available Endpoints:"
echo "  GET    http://localhost:3001/user/profile"
echo "  PUT    http://localhost:3001/user/profile"
echo "  POST   http://localhost:3001/user/change-password"
echo "  POST   http://localhost:3001/user/bulk-invite"
echo "  POST   http://localhost:3001/user/{userId}/deactivate"
echo "  PUT    http://localhost:3001/user/{userId}/role"
echo "  GET    http://localhost:3001/user/export"
echo "  POST   http://localhost:3001/user/reset-password"
echo "  POST   http://localhost:3001/user/impersonate"
echo ""
echo "🛑 Press Ctrl+C to stop the service"
echo ""

# Ejecutar serverless offline
serverless offline start --stage dev --region us-east-1
