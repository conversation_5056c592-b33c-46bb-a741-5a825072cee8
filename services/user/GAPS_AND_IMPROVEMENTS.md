# 👤 User Service - Gaps and Improvements

## 📊 **Current Status: 8.4/10 - MUY BUENO**

### **Completitud:** 85% funcional, gaps menores identificados

---

## 🎯 **Gaps Identificados**

### **1. MINOR GAPS (15%)**

#### **1.1 Missing User Profile Endpoints**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Feature completeness

**Current State:**
- User model exists in shared layer
- Missing profile management endpoints
- Limited user preferences handling

**Required Endpoints:**

```python
# src/handlers/user_profile.py
"""User profile management handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@user_resilience("user_profile")
@measure_performance("user_profile")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage user profile.
    
    GET /user/profile
    PUT /user/profile
    POST /user/profile/avatar
    DELETE /user/profile/avatar
    """
    pass

# src/handlers/user_preferences.py
"""User preferences handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@user_resilience("user_preferences")
@measure_performance("user_preferences")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage user preferences.
    
    GET /user/preferences
    PUT /user/preferences
    POST /user/preferences/reset
    """
    pass

# src/handlers/user_activity.py
"""User activity tracking handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@user_resilience("user_activity")
@measure_performance("user_activity")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Track and view user activity.
    
    GET /user/activity
    GET /user/activity/sessions
    POST /user/activity/track
    """
    pass

# src/handlers/user_notifications.py
"""User notifications handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@user_resilience("user_notifications")
@measure_performance("user_notifications")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage user notifications.
    
    GET /user/notifications
    PUT /user/notifications/{notification_id}/read
    DELETE /user/notifications/{notification_id}
    POST /user/notifications/mark-all-read
    """
    pass

# src/handlers/user_security.py
"""User security settings handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@user_resilience("user_security")
@measure_performance("user_security")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage user security settings.
    
    GET /user/security
    PUT /user/security/two-factor
    POST /user/security/sessions/revoke
    GET /user/security/login-history
    """
    pass
```

#### **1.2 Real Service Implementations**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Production readiness

**Current State:**
- Some services use mock implementations
- Missing real integrations
- Limited error handling

**Required Implementation:**

```python
# src/services/real_user_profile_service.py
"""Real user profile service implementation."""

import boto3
import json
from typing import Dict, Any, Optional
from shared.database import db_client
from shared.logger import lambda_logger
from shared.exceptions import UserException, ValidationException

class RealUserProfileService:
    """Real user profile service."""
    
    def __init__(self):
        self.db = db_client
        self.s3_client = boto3.client('s3')
        self.avatar_bucket = os.environ.get('AVATAR_BUCKET')
    
    async def get_user_profile(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Get complete user profile."""
        try:
            # Get user record
            user_record = await self.db.get_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                tenant_id=tenant_id
            )
            
            if not user_record:
                raise UserException(f"User {user_id} not found")
            
            # Get user preferences
            preferences = await self._get_user_preferences(user_id, tenant_id)
            
            # Get user activity summary
            activity_summary = await self._get_activity_summary(user_id, tenant_id)
            
            profile = {
                'user_id': user_record.get('user_id'),
                'email': user_record.get('email'),
                'first_name': user_record.get('first_name'),
                'last_name': user_record.get('last_name'),
                'role': user_record.get('role'),
                'phone_number': user_record.get('phone_number'),
                'timezone': user_record.get('timezone'),
                'language': user_record.get('language'),
                'avatar_url': user_record.get('avatar_url'),
                'created_at': user_record.get('created_at'),
                'last_login_at': user_record.get('last_login_at'),
                'email_verified': user_record.get('email_verified', False),
                'two_factor_enabled': user_record.get('two_factor_enabled', False),
                'preferences': preferences,
                'activity_summary': activity_summary
            }
            
            return profile
            
        except Exception as e:
            lambda_logger.error(f"Failed to get user profile: {str(e)}")
            raise UserException(f"Failed to get user profile: {str(e)}")
    
    async def update_user_profile(
        self,
        user_id: str,
        tenant_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update user profile."""
        try:
            # Validate update data
            allowed_fields = [
                'first_name', 'last_name', 'phone_number', 
                'timezone', 'language'
            ]
            
            update_expression_parts = []
            expression_attribute_values = {}
            
            for field, value in update_data.items():
                if field in allowed_fields and value is not None:
                    update_expression_parts.append(f'{field} = :{field}')
                    expression_attribute_values[f':{field}'] = value
            
            if not update_expression_parts:
                raise ValidationException("No valid fields to update")
            
            # Add updated timestamp
            update_expression_parts.append('updated_at = :updated_at')
            expression_attribute_values[':updated_at'] = int(time.time())
            
            update_expression = 'SET ' + ', '.join(update_expression_parts)
            
            # Update user record
            await self.db.update_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                update_expression=update_expression,
                expression_attribute_values=expression_attribute_values,
                tenant_id=tenant_id
            )
            
            # Get updated profile
            updated_profile = await self.get_user_profile(user_id, tenant_id)
            
            lambda_logger.info(f"User profile updated for {user_id}")
            return updated_profile
            
        except Exception as e:
            lambda_logger.error(f"Failed to update user profile: {str(e)}")
            raise UserException(f"Failed to update user profile: {str(e)}")
    
    async def upload_avatar(
        self,
        user_id: str,
        tenant_id: str,
        avatar_data: bytes,
        content_type: str
    ) -> str:
        """Upload user avatar to S3."""
        try:
            # Generate avatar key
            file_extension = content_type.split('/')[-1]
            avatar_key = f"avatars/{tenant_id}/{user_id}.{file_extension}"
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.avatar_bucket,
                Key=avatar_key,
                Body=avatar_data,
                ContentType=content_type,
                ACL='public-read'
            )
            
            # Generate avatar URL
            avatar_url = f"https://{self.avatar_bucket}.s3.amazonaws.com/{avatar_key}"
            
            # Update user record with avatar URL
            await self.db.update_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                update_expression='SET avatar_url = :avatar_url, updated_at = :updated_at',
                expression_attribute_values={
                    ':avatar_url': avatar_url,
                    ':updated_at': int(time.time())
                },
                tenant_id=tenant_id
            )
            
            lambda_logger.info(f"Avatar uploaded for user {user_id}")
            return avatar_url
            
        except Exception as e:
            lambda_logger.error(f"Failed to upload avatar: {str(e)}")
            raise UserException(f"Failed to upload avatar: {str(e)}")
    
    async def _get_user_preferences(
        self, 
        user_id: str, 
        tenant_id: str
    ) -> Dict[str, Any]:
        """Get user preferences."""
        try:
            preferences_record = await self.db.get_item(
                pk=f'USER#{user_id}',
                sk='PREFERENCES',
                tenant_id=tenant_id
            )
            
            if preferences_record:
                return preferences_record.get('preferences', {})
            
            # Return default preferences
            return {
                'notifications': {
                    'email_notifications': True,
                    'push_notifications': True,
                    'sms_notifications': False
                },
                'ui': {
                    'theme': 'light',
                    'language': 'en',
                    'timezone': 'UTC'
                },
                'privacy': {
                    'profile_visibility': 'team',
                    'activity_tracking': True
                }
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to get user preferences: {str(e)}")
            return {}
    
    async def _get_activity_summary(
        self, 
        user_id: str, 
        tenant_id: str
    ) -> Dict[str, Any]:
        """Get user activity summary."""
        try:
            # Get recent activity records
            activity_records = await self.db.query(
                pk=f'USER#{user_id}',
                sk_prefix='ACTIVITY#',
                tenant_id=tenant_id,
                limit=100
            )
            
            # Calculate summary
            total_sessions = len([r for r in activity_records if r.get('activity_type') == 'login'])
            last_activity = max([r.get('timestamp', 0) for r in activity_records]) if activity_records else 0
            
            return {
                'total_sessions': total_sessions,
                'last_activity': last_activity,
                'recent_activities': activity_records[:10]  # Last 10 activities
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to get activity summary: {str(e)}")
            return {}
```

**Serverless.yml Updates Required:**
```yaml
functions:
  # Existing functions...
  
  userProfile:
    handler: src/handlers/user_profile.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/profile
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /user/profile
          method: put
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
  
  userPreferences:
    handler: src/handlers/user_preferences.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/preferences
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /user/preferences
          method: put
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
```
