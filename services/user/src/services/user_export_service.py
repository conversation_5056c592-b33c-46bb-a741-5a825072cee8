# src/user/services/user_export_service.py
# Implementado según "User Management Guidelines"

"""
User export service.
Manages user data exports and reports.
"""

import time
import uuid
from typing import Dict, Any

from shared.database import db_client
from shared.logger import lambda_logger


class UserExportService:
    """Service for managing user exports."""
    
    def __init__(self):
        self.db_client = db_client
    
    async def export_users(
        self,
        tenant_id: str,
        format_type: str = 'csv',
        include_inactive: bool = False
    ) -> Dict[str, Any]:
        """Export users data."""
        try:
            export_id = str(uuid.uuid4())
            current_time = int(time.time())
            
            # Get users for export
            users = self.db_client.query(
                f'TENANT#{tenant_id}',
                sk_prefix='USER#',
                tenant_id=tenant_id
            )
            
            if not include_inactive:
                users = [u for u in users if u.get('is_active', True)]
            
            # Generate download URL (would be actual S3 URL in production)
            download_url = f"https://exports.yourplatform.com/users/{export_id}/download.{format_type}"
            
            lambda_logger.info("Created user export", extra={
                'tenant_id': tenant_id,
                'export_id': export_id,
                'user_count': len(users),
                'format': format_type
            })
            
            return {
                'export_id': export_id,
                'download_url': download_url,
                'user_count': len(users),
                'expires_at': current_time + 86400  # 24 hours
            }
            
        except Exception as e:
            lambda_logger.error("Failed to export users", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise


# Global service instance
user_export_service = UserExportService()
