# src/user/services/bulk_user_service.py
# Implementado según "User Management Guidelines"

"""
Bulk user service.
Manages bulk user operations like invitations and imports.
"""

import time
from typing import List, Dict, Any

from shared.database import db_client
from shared.logger import lambda_logger
from ...tenant.services.user_management_service import user_management_service


class BulkUserService:
    """Service for bulk user operations."""
    
    def __init__(self):
        self.db_client = db_client
        self.user_management_service = user_management_service
    
    async def process_bulk_invitations(
        self,
        tenant_id: str,
        inviter_user_id: str,
        invitations: List[Dict[str, Any]],
        send_emails: bool = True,
        custom_message: str = None
    ) -> Dict[str, Any]:
        """Process bulk user invitations."""
        try:
            successful = []
            failed = []
            duplicates_skipped = 0
            emails_sent = 0
            
            for invitation in invitations:
                try:
                    email = invitation['email'].lower().strip()
                    role = invitation['role']
                    first_name = invitation.get('first_name', '')
                    last_name = invitation.get('last_name', '')
                    
                    # Check if user already exists
                    existing_user = self.user_management_service.get_user_by_email(tenant_id, email)
                    if existing_user:
                        duplicates_skipped += 1
                        failed.append({
                            'email': email,
                            'reason': 'User already exists',
                            'error_code': 'DUPLICATE_USER'
                        })
                        continue
                    
                    # Create invitation
                    invitation_result = self.user_management_service.invite_user(
                        tenant_id=tenant_id,
                        inviter_user_id=inviter_user_id,
                        email=email,
                        role=role,
                        first_name=first_name,
                        last_name=last_name,
                        send_email=send_emails,
                        custom_message=custom_message
                    )
                    
                    successful.append({
                        'email': email,
                        'invitation_id': invitation_result.get('invitation_id'),
                        'role': role,
                        'status': 'invited'
                    })
                    
                    if send_emails:
                        emails_sent += 1
                        
                except Exception as e:
                    failed.append({
                        'email': invitation.get('email', 'unknown'),
                        'reason': str(e),
                        'error_code': 'INVITATION_FAILED'
                    })
            
            lambda_logger.info("Processed bulk invitations", extra={
                'tenant_id': tenant_id,
                'total': len(invitations),
                'successful': len(successful),
                'failed': len(failed),
                'duplicates_skipped': duplicates_skipped
            })
            
            return {
                'successful': successful,
                'failed': failed,
                'duplicates_skipped': duplicates_skipped,
                'emails_sent': emails_sent
            }
            
        except Exception as e:
            lambda_logger.error("Failed to process bulk invitations", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise


# Global service instance
bulk_user_service = BulkUserService()
