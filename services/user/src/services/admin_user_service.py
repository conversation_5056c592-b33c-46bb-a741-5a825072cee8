# src/user/services/admin_user_service.py
# Implementado según "User Management Guidelines"

"""
Admin user service.
Manages administrative user operations.
"""

import time
import hashlib
from typing import Dict, Any

from shared.database import db_client
from shared.logger import lambda_logger
from shared.email import email_service
from ...auth.services.token_blacklist_service import token_blacklist_service


class AdminUserService:
    """Service for administrative user operations."""
    
    def __init__(self):
        self.db_client = db_client
        self.email_service = email_service
    
    async def reset_user_password(
        self,
        target_user: Any,
        new_password: str,
        reset_by_user_id: str,
        reason: str,
        notify_user: bool = True,
        client_ip: str = 'unknown'
    ) -> Dict[str, Any]:
        """Reset user password administratively."""
        try:
            current_time = int(time.time())
            
            # Hash new password
            password_hash = self._hash_password(new_password)
            
            # Update user password
            target_user.password_hash = password_hash
            target_user.password_changed_at = current_time
            target_user.password_reset_required = True
            target_user.updated_at = current_time
            target_user.save()
            
            # Invalidate all user sessions
            token_blacklist_service.revoke_all_user_tokens(
                user_id=target_user.user_id,
                tenant_id=target_user.tenant_id
            )
            
            # Send notification email if requested
            notification_sent = False
            if notify_user:
                notification_sent = await self._send_password_reset_notification(
                    target_user, reset_by_user_id, reason
                )
            
            # Log the reset
            reset_log = {
                'PK': f'USER#{target_user.user_id}',
                'SK': f'PASSWORD_RESET#{current_time}',
                'entity_type': 'password_reset_log',
                'user_id': target_user.user_id,
                'tenant_id': target_user.tenant_id,
                'reset_by': reset_by_user_id,
                'reset_at': current_time,
                'reason': reason,
                'client_ip': client_ip,
                'notification_sent': notification_sent
            }
            
            self.db_client.put_item(reset_log, target_user.tenant_id)
            
            lambda_logger.info("Admin password reset completed", extra={
                'target_user_id': target_user.user_id,
                'reset_by': reset_by_user_id,
                'tenant_id': target_user.tenant_id,
                'reason': reason
            })
            
            return {
                'reset_at': current_time,
                'notification_sent': notification_sent,
                'password_change_required': True
            }
            
        except Exception as e:
            lambda_logger.error("Failed to reset user password", extra={
                'target_user_id': target_user.user_id if target_user else None,
                'reset_by': reset_by_user_id,
                'error': str(e)
            })
            raise
    
    async def _send_password_reset_notification(
        self,
        target_user: Any,
        reset_by_user_id: str,
        reason: str
    ) -> bool:
        """Send password reset notification email."""
        try:
            subject = "Your password has been reset"
            
            html_content = f"""
            <h2>Password Reset Notification</h2>
            <p>Hello {target_user.first_name or target_user.email},</p>
            <p>Your password has been reset by an administrator.</p>
            <p><strong>Reason:</strong> {reason}</p>
            <p><strong>Reset by:</strong> Administrator</p>
            <p><strong>Next steps:</strong></p>
            <ul>
                <li>You will be required to change your password on next login</li>
                <li>All your current sessions have been invalidated</li>
                <li>Please log in with your new password</li>
            </ul>
            <p>If you did not request this change, please contact your administrator immediately.</p>
            """
            
            text_content = f"""
            Password Reset Notification
            
            Hello {target_user.first_name or target_user.email},
            
            Your password has been reset by an administrator.
            
            Reason: {reason}
            Reset by: Administrator
            
            Next steps:
            - You will be required to change your password on next login
            - All your current sessions have been invalidated
            - Please log in with your new password
            
            If you did not request this change, please contact your administrator immediately.
            """
            
            return await self.email_service.send_email(
                to_email=target_user.email,
                subject=subject,
                html_content=html_content,
                text_content=text_content,
                email_type="password_reset"
            )
            
        except Exception as e:
            lambda_logger.error("Failed to send password reset notification", extra={
                'target_user_id': target_user.user_id,
                'error': str(e)
            })
            return False
    
    def _hash_password(self, password: str) -> str:
        """Hash password using secure bcrypt algorithm."""
        from shared.auth import PasswordManager

        # Use standardized secure password manager
        password_manager = PasswordManager()
        return password_manager.hash_password(password)


# Global service instance
admin_user_service = AdminUserService()
