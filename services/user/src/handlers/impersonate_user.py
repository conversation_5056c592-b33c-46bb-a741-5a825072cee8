# src/user/handlers/impersonate_user.py
# Implementado según "Security Guidelines" y "User Management"

"""
Impersonate user handler.
Handles admin impersonation of users for support purposes.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body, ImpersonateUserRequestValidator
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    ResourceNotFoundException,
    AuthorizationException
)
from shared.logger import audit_log, lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth, jwt_manager
from ..models.user import User


@require_auth
@rate_limit(requests_per_minute=5)  # Very restrictive for impersonation
@user_resilience("impersonate_user")
@measure_performance("user_impersonate")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Impersonate a user for support purposes.
    
    POST /user/impersonate
    {
        "target_user_id": "user_123",
        "reason": "customer_support_request",
        "duration_minutes": 30
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/user/impersonate'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Validate request body
        body = event.get('body', '{}')
        validated_data = validate_request_body(body, ImpersonateUserRequestValidator)
        
        target_user_id = validated_data['target_user_id']
        reason = validated_data['reason']
        duration_minutes = validated_data.get('duration_minutes', 30)
        
        lambda_logger.info("Processing user impersonation request", extra={
            'tenant_id': auth_context.tenant_id,
            'admin_user_id': auth_context.user_id,
            'target_user_id': target_user_id,
            'reason': reason,
            'duration_minutes': duration_minutes
        })
        
        # Check permissions (only MASTER can impersonate)
        if auth_context.role != 'MASTER':
            log_security_event(
                lambda_logger,
                "unauthorized_impersonation_attempt",
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                target_user_id=target_user_id,
                ip_address=client_ip,
                reason="insufficient_permissions"
            )
            raise AuthorizationException("Only tenant masters can impersonate users")
        
        # Validate duration (max 2 hours for security)
        if duration_minutes > 120:
            raise ValidationException("Impersonation duration cannot exceed 120 minutes")
        
        # Get target user
        target_user = User.get_by_id(target_user_id, auth_context.tenant_id)
        if not target_user:
            raise ResourceNotFoundException("Target user not found")
        
        # Cannot impersonate another MASTER
        if target_user.role == 'MASTER':
            log_security_event(
                lambda_logger,
                "attempted_master_impersonation",
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                target_user_id=target_user_id,
                ip_address=client_ip
            )
            raise AuthorizationException("Cannot impersonate another master user")
        
        # Check if user is active
        if not target_user.is_active:
            raise ValidationException("Cannot impersonate inactive user")
        
        # Generate impersonation token
        impersonation_payload = {
            'user_id': target_user.user_id,
            'tenant_id': target_user.tenant_id,
            'role': target_user.role,
            'email': target_user.email,
            'first_name': target_user.first_name,
            'last_name': target_user.last_name,
            'is_impersonation': True,
            'impersonator_user_id': auth_context.user_id,
            'impersonation_reason': reason,
            'impersonation_started_at': lambda_logger.get_current_timestamp()
        }
        
        # Create impersonation token with shorter expiry
        impersonation_token = jwt_manager.create_token(
            impersonation_payload,
            expires_in_minutes=duration_minutes
        )
        
        # Log security event
        log_security_event(
            lambda_logger,
            "user_impersonation_started",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            target_user_id=target_user_id,
            ip_address=client_ip,
            reason=reason,
            duration_minutes=duration_minutes
        )
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/impersonate",
            method="POST",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        # Prepare response data
        response_data = {
            'impersonation_token': impersonation_token,
            'target_user': {
                'user_id': target_user.user_id,
                'email': target_user.email,
                'first_name': target_user.first_name,
                'last_name': target_user.last_name,
                'role': target_user.role
            },
            'impersonation_details': {
                'impersonator_user_id': auth_context.user_id,
                'reason': reason,
                'duration_minutes': duration_minutes,
                'expires_at': lambda_logger.get_current_timestamp() + (duration_minutes * 60),
                'started_at': lambda_logger.get_current_timestamp()
            },
            'security_notice': 'This is an impersonation session. All actions will be logged and attributed to the impersonator.'
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/user/impersonate',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message=f"Successfully started impersonation of user {target_user.email}"
        )
        
    except ValidationException as e:
        lambda_logger.warning("Impersonation validation error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=400,
            error_code="VALIDATION_ERROR"
        )
        
    except AuthorizationException as e:
        lambda_logger.warning("Impersonation authorization error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=403,
            error_code="AUTHORIZATION_ERROR"
        )
        
    except ResourceNotFoundException as e:
        lambda_logger.warning("Impersonation resource not found", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=404,
            error_code="RESOURCE_NOT_FOUND"
        )
        
    except Exception as e:
        lambda_logger.error("Impersonation error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error during user impersonation",
            status_code=500,
            error_code="IMPERSONATION_ERROR"
        )
