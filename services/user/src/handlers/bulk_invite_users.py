# src/user/handlers/bulk_invite_users.py
# Implementado según "API Design Guidelines" y "User Management"

"""
Bulk invite users handler.
Handles inviting multiple users to a tenant.
"""

import json
from typing import Any, Dict, List
from shared.python.shared.
from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body, BulkInviteUsersRequestValidator
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    ResourceNotFoundException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth
from ..services.bulk_user_service import bulk_user_service


@require_auth
@rate_limit(requests_per_minute=5)  # Very restrictive for bulk operations
@user_resilience("bulk_invite_users")
@measure_performance("user_bulk_invite")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Bulk invite users to tenant.
    
    POST /user/bulk-invite
    {
        "invitations": [
            {
                "email": "<EMAIL>",
                "role": "USER",
                "first_name": "<PERSON>",
                "last_name": "Doe"
            },
            {
                "email": "<EMAIL>", 
                "role": "ADMIN",
                "first_name": "Jane",
                "last_name": "Smith"
            }
        ],
        "send_emails": true,
        "custom_message": "Welcome to our team!"
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/user/bulk-invite'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Validate request body
        body = event.get('body', '{}')
        validated_data = validate_request_body(body, BulkInviteUsersRequestValidator)
        
        invitations = validated_data['invitations']
        send_emails = validated_data.get('send_emails', True)
        custom_message = validated_data.get('custom_message')
        
        lambda_logger.info("Processing bulk user invitations", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'invitation_count': len(invitations),
            'send_emails': send_emails
        })
        
        # Check permissions (MASTER and ADMIN can invite users)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning("Insufficient permissions for bulk invite", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            raise ValidationException("Insufficient permissions to invite users")
        
        # Validate invitation count limits
        if len(invitations) > 50:  # Reasonable limit
            raise ValidationException("Cannot invite more than 50 users at once")
        
        # Process bulk invitations
        import asyncio
        results = asyncio.run(bulk_user_service.process_bulk_invitations(
            tenant_id=auth_context.tenant_id,
            inviter_user_id=auth_context.user_id,
            invitations=invitations,
            send_emails=send_emails,
            custom_message=custom_message
        ))
        
        # Prepare response data
        # Log security event for bulk invitation
        log_security_event(
            lambda_logger,
            "bulk_users_invited",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            invitation_count=len(invitations),
            successful_count=len(results['successful']),
            failed_count=len(results['failed'])
        )

        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/bulk-invite",
            method="POST",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        response_data = {
            'total_invitations': len(invitations),
            'successful_invitations': len(results['successful']),
            'failed_invitations': len(results['failed']),
            'successful': results['successful'],
            'failed': results['failed'],
            'summary': {
                'success_rate': (len(results['successful']) / len(invitations)) * 100,
                'emails_sent': results['emails_sent'] if send_emails else 0,
                'duplicates_skipped': results['duplicates_skipped']
            },
            'processed_at': lambda_logger.get_current_timestamp()
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/user/bulk-invite',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message=f"Processed {len(invitations)} invitations. {len(results['successful'])} successful, {len(results['failed'])} failed."
        )
        
    except ValidationException as e:
        lambda_logger.warning("Bulk invite validation error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=400,
            error_code="VALIDATION_ERROR"
        )
        
    except Exception as e:
        lambda_logger.error("Bulk invite error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error during bulk invitation",
            status_code=500,
            error_code="BULK_INVITE_ERROR"
        )
