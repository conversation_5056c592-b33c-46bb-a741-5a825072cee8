# src/user/handlers/export_users.py
# Implementado según "API Design Guidelines" y "User Management"

"""
Export users handler.
Handles exporting user lists and data.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import ValidationException
from shared.logger import lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth
from ..services.user_export_service import user_export_service


@require_auth
@rate_limit(requests_per_minute=5)  # Very restrictive for data export
@user_resilience("export_users")
@measure_performance("user_export")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Export users data."""

    # GET/POST requests for export don't need body validation
    
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    log_api_request(lambda_logger, 'POST', '/user/export', request_id=request_id,
                   tenant_id=auth_context.tenant_id, user_id=auth_context.user_id)
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        if auth_context.role not in ['MASTER', 'ADMIN']:
            raise ValidationException("Insufficient permissions to export users")
        
        query_params = event.get('queryStringParameters') or {}
        format_type = query_params.get('format', 'csv')
        include_inactive = query_params.get('include_inactive', 'false').lower() == 'true'
        
        # Generate export
        import asyncio
        export_result = asyncio.run(user_export_service.export_users(
            tenant_id=auth_context.tenant_id,
            format_type=format_type,
            include_inactive=include_inactive
        ))
        
        # Log security event for data export
        log_security_event(
            lambda_logger,
            "users_exported",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            user_count=export_result['user_count'],
            format=format_type,
            include_inactive=include_inactive
        )

        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/export",
            method="POST",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        response_data = {
            'export_id': export_result['export_id'],
            'download_url': export_result['download_url'],
            'user_count': export_result['user_count'],
            'format': format_type,
            'expires_at': export_result['expires_at']
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(lambda_logger, 'POST', '/user/export', 200, duration_ms,
                        tenant_id=auth_context.tenant_id, user_id=auth_context.user_id, request_id=request_id)

        return APIResponse.success(data=response_data, message="User export generated successfully")
        
    except Exception as e:
        lambda_logger.error("Export users error", extra={'error': str(e), 'tenant_id': auth_context.tenant_id})
        return APIResponse.error(message="Internal server error during user export", status_code=500, error_code="EXPORT_ERROR")
