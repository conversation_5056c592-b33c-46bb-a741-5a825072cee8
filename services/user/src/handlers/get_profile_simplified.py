# src/user/handlers/get_profile_simplified.py
# Example of simplified handler using unified decorators

"""
Simplified get profile handler demonstrating the new unified approach.
This shows how a simple GET handler can be dramatically simplified.
"""

from typing import Any, Dict

# Imports from shared layer (via Lambda Layer)
from shared.responses import APIResponse, StandardResponses
from shared.decorators import with_standard_handling
from shared.logger import audit_log, lambda_logger
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared import User


@rate_limit(requests_per_minute=100)
@user_resilience("get_profile")
@with_standard_handling(
    service_name="user",
    operation_name="get_profile",
    method="GET",
    validator=None,  # No body validation needed for GET
    auth_required=True
)
def handler(validated_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Simplified get profile handler using unified decorators.
    
    All authentication, error handling, logging, and CORS is handled automatically.
    This handler only contains business logic.
    """
    
    # Extract auth context (already validated by decorator)
    auth_context = validated_data['auth']
    event = validated_data['event']
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    # Get user profile
    user = User.get_by_id(auth_context.user_id, auth_context.tenant_id)
    
    if not user:
        return StandardResponses.user_not_found()
    
    # Verify tenant access (extra security check)
    if user.tenant_id != auth_context.tenant_id:
        return StandardResponses.insufficient_permissions()
    
    # Prepare response data (exclude sensitive information)
    user_data = user.to_dict()
    user_data.pop('password_hash', None)
    user_data.pop('email_verification_token', None)
    
    response_data = {
        'user': user_data,
        'profile_completion': _calculate_profile_completion(user),
        'last_updated': user.updated_at
    }
    
    # Log successful access
    audit_log(
        lambda_logger,
        "user_profile_accessed",
        "User profile retrieved successfully",
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id,
        request_id=request_id
    )
    
    return APIResponse.success(
        data=response_data,
        message="User profile retrieved successfully"
    )


def _calculate_profile_completion(user: User) -> int:
    """Calculate profile completion percentage."""
    total_fields = 8
    completed_fields = 0
    
    if user.first_name:
        completed_fields += 1
    if user.last_name:
        completed_fields += 1
    if user.email:
        completed_fields += 1
    if user.phone_number:
        completed_fields += 1
    if user.profile_picture_url:
        completed_fields += 1
    if user.timezone:
        completed_fields += 1
    if user.language:
        completed_fields += 1
    if user.email_verified:
        completed_fields += 1
    
    return int((completed_fields / total_fields) * 100)


# Note: This simplified handler demonstrates the dramatic reduction in code:
# 
# Before: ~160 lines with manual auth, validation, error handling, logging
# After: ~80 lines with only business logic
# 
# Benefits:
# - Automatic authentication and authorization
# - Consistent error handling with standard responses
# - Automatic logging and audit trails
# - CORS handling
# - Reduced boilerplate code by ~50%
# - Consistent patterns across all handlers
# - Better error messages with standard codes
