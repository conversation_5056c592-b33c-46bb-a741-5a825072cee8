# src/user/handlers/reset_user_password.py
# Implementado según "Security Guidelines" y "User Management"

"""
Reset user password handler.
Handles admin-initiated password resets for users.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body, ResetUserPasswordRequestValidator
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    ResourceNotFoundException,
    AuthorizationException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth
from shared import User
from ..services.admin_user_service import admin_user_service


@require_auth
@rate_limit(requests_per_minute=10)  # Restrictive for admin password resets
@user_resilience("reset_user_password")
@measure_performance("user_reset_password")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Admin reset user password.
    
    POST /user/reset-password
    {
        "user_id": "user_123",
        "new_password": "NewSecurePassword123!",
        "reason": "user_forgot_password",
        "notify_user": true
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
    user_agent = event.get('headers', {}).get('User-Agent', 'unknown')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/user/reset-password'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Validate request body
        body = event.get('body', '{}')
        validated_data = validate_request_body(body, ResetUserPasswordRequestValidator)
        
        target_user_id = validated_data['user_id']
        new_password = validated_data['new_password']
        reason = validated_data['reason']
        notify_user = validated_data.get('notify_user', True)
        
        lambda_logger.info("Processing admin password reset", extra={
            'tenant_id': auth_context.tenant_id,
            'admin_user_id': auth_context.user_id,
            'target_user_id': target_user_id,
            'reason': reason,
            'notify_user': notify_user
        })
        
        # Check permissions (only MASTER and ADMIN can reset passwords)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            log_security_event(
                lambda_logger,
                "unauthorized_password_reset_attempt",
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                target_user_id=target_user_id,
                ip_address=client_ip,
                user_agent=user_agent,
                reason="insufficient_permissions"
            )
            raise AuthorizationException("Only tenant masters and admins can reset user passwords")
        
        # Get target user
        target_user = User.get_by_id(target_user_id, auth_context.tenant_id)
        if not target_user:
            raise ResourceNotFoundException("User not found")
        
        # Verify user belongs to same tenant
        if target_user.tenant_id != auth_context.tenant_id:
            log_security_event(
                lambda_logger,
                "cross_tenant_password_reset_attempt",
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                target_user_id=target_user_id,
                target_tenant_id=target_user.tenant_id,
                ip_address=client_ip
            )
            raise AuthorizationException("Cannot reset password for users in other tenants")
        
        # Prevent ADMIN from resetting MASTER password
        if auth_context.role == 'ADMIN' and target_user.role == 'MASTER':
            log_security_event(
                lambda_logger,
                "admin_attempted_master_password_reset",
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                target_user_id=target_user_id,
                ip_address=client_ip
            )
            raise AuthorizationException("Admins cannot reset master user passwords")
        
        # Prevent self password reset through this endpoint
        if target_user_id == auth_context.user_id:
            raise ValidationException("Use the change password endpoint to update your own password")
        
        # Reset password
        import asyncio
        reset_result = asyncio.run(admin_user_service.reset_user_password(
            target_user=target_user,
            new_password=new_password,
            reset_by_user_id=auth_context.user_id,
            reason=reason,
            notify_user=notify_user,
            client_ip=client_ip
        ))
        
        # Log security event
        log_security_event(
            lambda_logger,
            "admin_password_reset_completed",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            target_user_id=target_user_id,
            ip_address=client_ip,
            user_agent=user_agent,
            reason=reason,
            notify_user=notify_user
        )
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/reset-password",
            method="POST",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        # Prepare response data (without sensitive information)
        response_data = {
            'user_id': target_user_id,
            'user_email': target_user.email,
            'password_reset': True,
            'reset_by': auth_context.user_id,
            'reset_at': reset_result['reset_at'],
            'reason': reason,
            'notification_sent': reset_result['notification_sent'] if notify_user else False,
            'security_actions': {
                'all_sessions_invalidated': True,
                'password_change_required': reset_result.get('password_change_required', False)
            }
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/user/reset-password',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message=f"Password reset successfully for user {target_user.email}"
        )
        
    except ValidationException as e:
        lambda_logger.warning("Password reset validation error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=400,
            error_code="VALIDATION_ERROR"
        )
        
    except AuthorizationException as e:
        lambda_logger.warning("Password reset authorization error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=403,
            error_code="AUTHORIZATION_ERROR"
        )
        
    except ResourceNotFoundException as e:
        lambda_logger.warning("Password reset resource not found", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=404,
            error_code="RESOURCE_NOT_FOUND"
        )
        
    except Exception as e:
        lambda_logger.error("Password reset error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error during password reset",
            status_code=500,
            error_code="PASSWORD_RESET_ERROR"
        )
