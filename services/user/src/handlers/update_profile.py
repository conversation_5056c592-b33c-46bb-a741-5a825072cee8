# src/user/handlers/update_profile.py
# Implementado según "API Specifications" y "Multi-tenancy Guidelines"

"""
Update user profile handler.
Handles updating user profile information.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body, UserProfileUpdateValidator
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ValidationException,
    ResourceNotFoundException
)
from shared.logger import audit_log, lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth
from shared import User
from shared.auth import get_auth_context
from shared.auth import jwt_manager


@require_auth
@rate_limit(requests_per_minute=30)  # Reasonable limit for profile updates
@user_resilience("update_profile")
@measure_performance("user_update_profile")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update user profile information.
    
    PUT /user/profile
    {
        "name": "Updated Name",
        "phone": "+1234567890"
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', '/user/profile'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Extract JWT token from authorization header
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            lambda_logger.warning("Missing or invalid authorization header")
            return APIResponse.error(
                message="Authorization header required",
                status_code=401
            )
        
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Decode and validate JWT token
        try:
            payload = jwt_manager.decode_token(token)
            tenant_id = payload.get('tenant_id')
            user_id = payload.get('user_id')
            
            if not tenant_id or not user_id:
                raise AuthorizationException("Invalid token payload")
                
        except Exception as e:
            lambda_logger.warning(f"Invalid JWT token: {str(e)}")
            return APIResponse.error(
                message="Invalid or expired token",
                status_code=401
            )
        
        # Get authentication context
        auth_context = get_auth_context(event)

        # Validate tenant access
        if not auth_context or not auth_context.tenant_id:
            return APIResponse.error("Tenant access required", status_code=403)

        # Validate permissions
        if not auth_context.has_role("MASTER"):
            return APIResponse.error("Insufficient permissions", status_code=403)

        # Validate request body
        try:
            request_data = validate_request_body(event, UserProfileUpdateValidator)
        except ValidationException as e:
            lambda_logger.warning(f"Invalid request data: {str(e)}")
            return APIResponse.error(
                message=str(e),
                status_code=400
            )
        
        lambda_logger.info(f"Updating profile for user_id: {user_id}")
        
        # Get user
        user = User.get_by_id(user_id)
        
        if not user:
            lambda_logger.warning(f"User not found: {user_id}")
            return APIResponse.error(
                message="User not found",
                status_code=404
            )
        
        # Verify tenant access
        if user.tenant_id != tenant_id:
            lambda_logger.warning(f"User {user_id} does not belong to tenant {tenant_id}")
            return APIResponse.error(
                message="Access denied",
                status_code=403
            )
        
        # Update user profile fields
        if 'name' in request_data:
            user.name = request_data['name']
        
        if 'phone' in request_data:
            user.phone = request_data['phone']
        
        # Save updated user
        success = user.save()
        
        if not success:
            lambda_logger.error(f"Failed to save user profile for user_id: {user_id}")
            return APIResponse.error(
                message="Failed to update user profile",
                status_code=500
            )
        
        # Prepare response data (exclude sensitive information)
        user_data = user.to_dict()
        user_data.pop('password_hash', None)
        user_data.pop('email_verification_token', None)
        user_data.pop('password_reset_token', None)
        
        response_data = {
            'user': user_data
        }
        
        # Log security event for profile update
        log_security_event(
            lambda_logger,
            "user_profile_updated",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/profile",
            method="PUT",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/user/profile',
            200,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="User profile updated successfully"
        )
        
    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/user/profile',
            e.status_code,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )
        
    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/user/profile',
            500,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        lambda_logger.error("Unexpected error during user profile update", extra={
            'error': str(e),
            'user_id': user_id if 'user_id' in locals() else 'unknown',
            'request_id': request_id
        })
        
        return APIResponse.internal_server_error(
            message="An unexpected error occurred while updating user profile"
        )
