# src/user/handlers/change_password.py
# Implementado según "API Specifications" y "Security Guidelines"

"""
Change password handler.
Handles user password change requests.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body, ChangePasswordValidator
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ValidationException,
    InvalidCredentialsException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth, password_manager
from shared import User
from shared.auth import get_auth_context
from shared.auth import jwt_manager


@require_auth
@rate_limit(requests_per_minute=10)  # Restrictive for password changes
@user_resilience("change_password")
@measure_performance("user_change_password")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Change user password.
    
    POST /user/change-password
    {
        "current_password": "current_password",
        "new_password": "new_password"
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/user/change-password'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Extract JWT token from authorization header
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            lambda_logger.warning("Missing or invalid authorization header")
            return APIResponse.error(
                message="Authorization header required",
                status_code=401
            )
        
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Decode and validate JWT token
        try:
            payload = jwt_manager.decode_token(token)
            tenant_id = payload.get('tenant_id')
            user_id = payload.get('user_id')
            
            if not tenant_id or not user_id:
                raise AuthorizationException("Invalid token payload")
                
        except Exception as e:
            lambda_logger.warning(f"Invalid JWT token: {str(e)}")
            return APIResponse.error(
                message="Invalid or expired token",
                status_code=401
            )
        
        # Get authentication context
        auth_context = get_auth_context(event)

        # Validate tenant access
        if not auth_context or not auth_context.tenant_id:
            return APIResponse.error("Tenant access required", status_code=403)

        # Validate request body
        try:
            request_data = validate_request_body(event, ChangePasswordValidator)
        except ValidationException as e:
            lambda_logger.warning(f"Invalid request data: {str(e)}")
            return APIResponse.error(
                message=str(e),
                status_code=400
            )
        
        current_password = request_data['current_password']
        new_password = request_data['new_password']
        
        lambda_logger.info(f"Processing password change for user_id: {user_id}")
        
        # Get user
        user = User.get_by_id(user_id)
        
        if not user:
            lambda_logger.warning(f"User not found: {user_id}")
            return APIResponse.error(
                message="User not found",
                status_code=404
            )
        
        # Verify tenant access
        if user.tenant_id != tenant_id:
            lambda_logger.warning(f"User {user_id} does not belong to tenant {tenant_id}")
            return APIResponse.error(
                message="Access denied",
                status_code=403
            )
        
        # Verify current password
        if not password_manager.verify_password(current_password, user.password_hash):
            log_security_event(
                lambda_logger,
                'PASSWORD_CHANGE_FAILED',
                user_id=user_id,
                tenant_id=tenant_id,
                client_ip=client_ip,
                reason='Invalid current password'
            )
            
            return APIResponse.error(
                message="Current password is incorrect",
                status_code=400
            )
        
        # Hash new password
        new_password_hash = password_manager.hash_password(new_password)
        
        # Update user password
        user.password_hash = new_password_hash
        user.password_changed_at = lambda_logger.get_current_timestamp()
        
        # Save updated user
        success = user.save()
        
        if not success:
            lambda_logger.error(f"Failed to save password change for user_id: {user_id}")
            return APIResponse.error(
                message="Failed to change password",
                status_code=500
            )
        
        # Log security event
        log_security_event(
            lambda_logger,
            'PASSWORD_CHANGED',
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip
        )
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/change-password",
            method="POST",
            status_code=200,
            tenant_id=tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/user/change-password',
            200,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.success(
            data={'password_changed_at': user.password_changed_at},
            message="Password changed successfully"
        )
        
    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/user/change-password',
            e.status_code,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )
        
    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/user/change-password',
            500,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        lambda_logger.error("Unexpected error during password change", extra={
            'error': str(e),
            'user_id': user_id if 'user_id' in locals() else 'unknown',
            'request_id': request_id
        })
        
        return APIResponse.internal_server_error(
            message="An unexpected error occurred while changing password"
        )
