# src/user/handlers/update_role.py
# Implementado según "API Specifications" y "Multi-tenancy Guidelines"

"""
Update user role handler.
Handles updating user roles (admin only).
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body, UpdateUserRequestValidator
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ValidationException,
    ResourceNotFoundException
)
from shared.logger import audit_log, lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth
from shared import User, UserRole
from shared.auth import get_auth_context
from shared.auth import jwt_manager


@require_auth
@rate_limit(requests_per_minute=10)  # Restrictive for role changes
@user_resilience("update_role")
@measure_performance("user_update_role")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update user role (admin only).
    
    PUT /user/{user_id}/role
    {
        "role": "ADMIN"
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', '/user/{user_id}/role'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Extract JWT token from authorization header
        auth_header = event.get('headers', {}).get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            lambda_logger.warning("Missing or invalid authorization header")
            return APIResponse.error(
                message="Authorization header required",
                status_code=401
            )
        
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Decode and validate JWT token
        try:
            payload = jwt_manager.decode_token(token)
            tenant_id = payload.get('tenant_id')
            admin_user_id = payload.get('user_id')
            admin_role = payload.get('role')
            
            if not tenant_id or not admin_user_id:
                raise AuthorizationException("Invalid token payload")
                
        except Exception as e:
            lambda_logger.warning(f"Invalid JWT token: {str(e)}")
            return APIResponse.error(
                message="Invalid or expired token",
                status_code=401
            )
        
        # Check admin permissions
        if admin_role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning(f"Insufficient permissions for user {admin_user_id} with role {admin_role}")
            return APIResponse.error(
                message="Insufficient permissions to update user roles",
                status_code=403
            )
        
        # Extract target user_id from path parameters
        path_parameters = event.get('pathParameters') or {}
        target_user_id = path_parameters.get('user_id')
        
        if not target_user_id:
            lambda_logger.warning("Missing user_id in path parameters")
            return APIResponse.error(
                message="User ID is required",
                status_code=400
            )
        
        # Get authentication context
        auth_context = get_auth_context(event)

        # Validate tenant access
        if not auth_context or not auth_context.tenant_id:
            return APIResponse.error("Tenant access required", status_code=403)

        # Validate permissions
        if not auth_context.has_role("MASTER"):
            return APIResponse.error("Insufficient permissions", status_code=403)

        # Validate request body
        try:
            request_data = validate_request_body(event, UpdateUserRequestValidator)
        except ValidationException as e:
            lambda_logger.warning(f"Invalid request data: {str(e)}")
            return APIResponse.error(
                message=str(e),
                status_code=400
            )
        
        new_role = request_data.get('role')
        if not new_role:
            return APIResponse.error(
                message="Role is required",
                status_code=400
            )
        
        lambda_logger.info(f"Updating role for user_id: {target_user_id} to role: {new_role}")
        
        # Get target user
        target_user = User.get_by_id(target_user_id)
        
        if not target_user:
            lambda_logger.warning(f"Target user not found: {target_user_id}")
            return APIResponse.error(
                message="User not found",
                status_code=404
            )
        
        # Verify tenant access
        if target_user.tenant_id != tenant_id:
            lambda_logger.warning(f"User {target_user_id} does not belong to tenant {tenant_id}")
            return APIResponse.error(
                message="Access denied",
                status_code=403
            )
        
        # Prevent self-role modification for MASTER users
        if target_user_id == admin_user_id and admin_role == 'MASTER':
            lambda_logger.warning(f"MASTER user {admin_user_id} attempted to modify own role")
            return APIResponse.error(
                message="Cannot modify your own role as MASTER user",
                status_code=403
            )
        
        # Prevent non-MASTER users from creating MASTER users
        if new_role == 'MASTER' and admin_role != 'MASTER':
            lambda_logger.warning(f"Non-MASTER user {admin_user_id} attempted to create MASTER user")
            return APIResponse.error(
                message="Only MASTER users can assign MASTER role",
                status_code=403
            )
        
        # Update user role
        old_role = target_user.role
        target_user.role = UserRole(new_role)
        
        # Save updated user
        success = target_user.save()
        
        if not success:
            lambda_logger.error(f"Failed to save role update for user_id: {target_user_id}")
            return APIResponse.error(
                message="Failed to update user role",
                status_code=500
            )
        
        # Log security event
        log_security_event(
            lambda_logger,
            'USER_ROLE_UPDATED',
            user_id=target_user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            details={
                'old_role': old_role.value if hasattr(old_role, 'value') else str(old_role),
                'new_role': new_role,
                'updated_by': admin_user_id
            }
        )
        
        # Prepare response data (exclude sensitive information)
        user_data = target_user.to_dict()
        user_data.pop('password_hash', None)
        user_data.pop('email_verification_token', None)
        user_data.pop('password_reset_token', None)
        
        response_data = {
            'user': user_data,
            'role_updated_by': admin_user_id
        }
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint=f"/user/{target_user_id}/role",
            method="PUT",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            f'/user/{target_user_id}/role',
            200,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="User role updated successfully"
        )
        
    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            f'/user/{target_user_id if "target_user_id" in locals() else "unknown"}/role',
            e.status_code,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )
        
    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            f'/user/{target_user_id if "target_user_id" in locals() else "unknown"}/role',
            500,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        lambda_logger.error("Unexpected error during user role update", extra={
            'error': str(e),
            'target_user_id': target_user_id if 'target_user_id' in locals() else 'unknown',
            'admin_user_id': admin_user_id if 'admin_user_id' in locals() else 'unknown',
            'request_id': request_id
        })
        
        return APIResponse.internal_server_error(
            message="An unexpected error occurred while updating user role"
        )
