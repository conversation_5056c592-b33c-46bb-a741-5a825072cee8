# src/user/handlers/get_profile.py
# Implementado según "API Specifications" y "Multi-tenancy Guidelines"

"""
Get user profile handler.
Handles retrieving user profile information.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import user_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ResourceNotFoundException
)
from shared.logger import audit_log, lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from shared.middleware.resilience_middleware import rate_limit
from shared import User


@require_auth
@rate_limit(requests_per_minute=120)  # High limit for profile access
@user_resilience("get_profile")
@measure_performance("user_get_profile")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get user profile information.

    GET /user/profile
    """

    # GET requests don't need body validation
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/user/profile'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        lambda_logger.info("Getting user profile", extra={
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        # Get user information
        user = User.get_by_id(auth_context.user_id, auth_context.tenant_id)

        if not user:
            lambda_logger.warning("User not found", extra={
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException("User not found")
        
        # Verify tenant access (already verified by get_by_id with tenant_id)
        # This check is redundant but kept for extra security
        if user.tenant_id != auth_context.tenant_id:
            lambda_logger.warning("User does not belong to tenant", extra={
                'user_id': auth_context.user_id,
                'user_tenant_id': user.tenant_id,
                'auth_tenant_id': auth_context.tenant_id
            })
            raise AuthorizationException("Access denied")
        
        # Prepare response data (exclude sensitive information)
        user_data = user.to_dict()
        user_data.pop('password_hash', None)
        user_data.pop('email_verification_token', None)
        user_data.pop('password_reset_token', None)
        
        response_data = {
            'user': user_data,
            'permissions': {
                'can_change_password': True,
                'can_update_profile': True,
                'can_manage_users': user.role in ['MASTER', 'ADMIN']
            }
        }
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/user/profile",
            method="GET",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/user/profile',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="User profile retrieved successfully"
        )
        
    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/user/profile',
            e.status_code,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )
        
    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/user/profile',
            500,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )
        
        lambda_logger.error("Unexpected error during user profile retrieval", extra={
            'error': str(e),
            'user_id': auth_context.user_id if auth_context else 'unknown',
            'request_id': request_id
        })
        
        return APIResponse.internal_server_error(
            message="An unexpected error occurred while retrieving user profile"
        )
