# services/user/serverless.yml
# User service configuration

service: agent-scl-user

# Custom configuration
custom:
  serviceName: user
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
  # Python requirements optimization
  pythonRequirements:
    dockerizePip: false
    slim: true
    strip: false
    useDownloadCache: true
    useStaticCache: true
    fileName: requirements.txt  # Solo dependencias de producciÃ³n

# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    
  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"
    - Effect: Allow
      Action:
        - ses:SendEmail
        - ses:SendRawEmail
      Resource: "*"

# Functions
functions:
  # User profile management
  getProfile:
    handler: src.handlers.get_profile.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/profile
          method: get
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_get_profile
      tracing: Active

  updateProfile:
    handler: src.handlers.update_profile.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/profile
          method: put
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_update_profile
      tracing: Active

  changePassword:
    handler: src.handlers.change_password.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/change-password
          method: post
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_change_password
      tracing: Active

  # Admin user management
  bulkInviteUsers:
    handler: src.handlers.bulk_invite_users.handler
    timeout: 60
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/bulk-invite
          method: post
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_bulk_invite
      tracing: Active

  deactivateUser:
    handler: src.handlers.deactivate_user.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/{userId}/deactivate
          method: post
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_deactivate
      tracing: Active

  updateRole:
    handler: src.handlers.update_role.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/{userId}/role
          method: put
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_update_role
      tracing: Active

  exportUsers:
    handler: src.handlers.export_users.handler
    timeout: 60
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /user/export
          method: get
          cors: true
  authorizer:
    name: jwtAuthorizer
    type: request
    arn:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      environment:
        FUNCTION_NAME: user_export
      tracing: Active

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'

# Resources
resources:
  Description: User service for Agent SCL platform
