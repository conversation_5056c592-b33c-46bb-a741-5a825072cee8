service: agent-scl-jobs

custom:
  serviceName: jobs
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}

  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName

    # Service endpoints
    ORCHESTRATOR_SERVICE_URL:
      Fn::ImportValue: sls-${self:custom.projectName}-orchestrator-${self:custom.stage}-ServiceEndpoint
    PAYMENT_SERVICE_URL:
      Fn::ImportValue: sls-${self:custom.projectName}-payment-${self:custom.stage}-ServiceEndpoint
    TENANT_SERVICE_URL:
      Fn::ImportValue: sls-${self:custom.projectName}-tenant-${self:custom.stage}-ServiceEndpoint

    # Jobs-specific tables
    REGISTRATIONS_TABLE: ${self:custom.projectName}-registrations-${self:custom.stage}
    SUBSCRIPTIONS_TABLE: ${self:custom.projectName}-main-${self:custom.stage}

    # Email configuration
    EMAIL_FROM: <EMAIL>

  # IAM role statements
  iamRoleStatements:
    # Main DynamoDB table permissions
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"

    # Jobs-specific table permissions
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-registrations-${self:custom.stage}
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-registrations-${self:custom.stage}/index/*
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-main-${self:custom.stage}
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-main-${self:custom.stage}/index/*
        
    # SES permissions for email notifications
    - Effect: Allow
      Action:
        - ses:SendEmail
        - ses:SendRawEmail
      Resource: "*"
    
    # CloudWatch Logs
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    
    # CloudWatch Metrics
    - Effect: Allow
      Action:
        - cloudwatch:PutMetricData
      Resource: "*"

# Lambda functions
functions:
  # Scheduled cleanup jobs
  registrationCleanup:
    handler: src/handlers/registration_cleanup.handler
    timeout: 300  # 5 minutes
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - schedule:
          rate: rate(1 hour)  # Run every hour
          enabled: true
          input:
            job_type: "registration_cleanup"
            max_age_hours: 1
            batch_size: 100
  
  paymentValidation:
    handler: src/handlers/payment_validation.handler
    timeout: 300  # 5 minutes
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - schedule:
          rate: rate(1 day)  # Run daily at midnight UTC
          enabled: true
          input:
            job_type: "payment_validation"
            check_overdue: true
            send_notifications: true
  
  paymentReminders:
    handler: src/handlers/payment_reminders.handler
    timeout: 300  # 5 minutes
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - schedule:
          rate: rate(1 day)  # Run daily
          enabled: true
          input:
            job_type: "payment_reminders"
            reminder_days: [7, 3, 1]  # Days before due date
            overdue_days: [1, 7, 14]  # Days after due date
  
  # Manual job execution endpoint
  executeJob:
    handler: src/handlers/job_executor.handler
    timeout: 300  # 5 minutes
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /jobs/execute
          method: post
          cors: true
  
  # Job status and monitoring
  jobStatus:
    handler: src/handlers/job_status.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /jobs/status
          method: get
          cors: true
  
  # Health check
  health:
    handler: src/handlers/health.handler
    timeout: 10
    memorySize: 128
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /health
          method: get
          cors: true

# Resources
resources:
  # Outputs
  Outputs:
    ServiceEndpoint:
      Description: "Jobs Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
            - "/jobs"
      Export:
        Name: sls-${self:custom.projectName}-jobs-${self:custom.stage}-ServiceEndpoint

plugins:
  - serverless-python-requirements

package:
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.pytest_cache/**'
    - '!tests/**'
    - '!*.md'
    - '!.env*'
