# ⏰ **Jobs Service - Agent-SCL Platform**

## **🚀 Descripción General**

El Jobs Service es el motor de tareas programadas del ecosistema **agent-scl**. Proporciona infraestructura completa para ejecución de jobs programados, limpieza automática de datos, validación de pagos, envío de notificaciones, mantenimiento del sistema y ejecución manual para testing y emergencias.

### **✨ Características Principales**
- ⏰ **Jobs Programados**: Ejecución automática basada en CloudWatch Events
- 🧹 **Limpieza Automática**: Cleanup de registros abandonados y datos expirados
- 💳 **Validación de Pagos**: Integración con Stripe para validar suscripciones
- 📧 **Recordatorios Inteligentes**: Sistema de notificaciones programadas
- 📊 **Batch Processing**: Procesamiento optimizado en lotes
- ⚡ **Ejecución Manual**: Jobs bajo demanda para testing y emergencias
- 📈 **Métricas Avanzadas**: Tracking detallado de performance y resultados
- 🔄 **Resilencia**: Manejo robusto de errores y reintentos

---

## **📋 Tabla de Contenidos**
1. [Instalación y Setup](#instalación-y-setup)
2. [Configuración](#configuración)
3. [Jobs Disponibles](#jobs-disponibles)
4. [API Reference](#api-reference)
5. [Scheduling](#scheduling)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)

---

## **🛠️ Instalación y Setup**

### **Prerrequisitos**
```bash
# Servicios requeridos (deben estar desplegados)
✅ agent-scl-shared-layer-dev
✅ agent-scl-orchestrator-dev
✅ agent-scl-auth-dev
✅ agent-scl-tenant-dev
✅ agent-scl-payment-dev

# Herramientas de desarrollo
- Python 3.11+
- Serverless Framework 3.x
- AWS CLI configurado
- Node.js 18+ (para Serverless)
```

### **Instalación Local**
```bash
# 1. Clonar y navegar al directorio
cd services/jobs

# 2. Instalar dependencias Python
pip install -r requirements.txt

# 3. Instalar Serverless plugins
npm install

# 4. Configurar variables de entorno
export STAGE=dev
export REGION=us-east-1
```

### **Configuración de Servicios Externos**
```bash
# Stripe API Key (para payment validation)
aws ssm put-parameter \
  --name "/agent-scl/dev/stripe-api-key" \
  --value "sk_test_..." \
  --type "SecureString"

# Verificar SES para emails
aws ses get-identity-verification-attributes --identities <EMAIL>

# Verificar tabla DynamoDB
aws dynamodb describe-table --table-name "agent-scl-dev"
```

---

## **⏰ Jobs Disponibles**

### **🧹 Registration Cleanup Job**
**Frecuencia**: Cada hora
**Propósito**: Limpieza de registros abandonados y expirados

```yaml
Configuración:
  max_age_hours: 1          # Edad máxima de registros
  batch_size: 100           # Tamaño de lote de procesamiento
  dry_run: false            # Modo de prueba sin cambios
  cleanup_resources: true   # Limpiar recursos asociados

Acciones:
  1. Buscar registros expirados en estados intermedios
  2. Validar que realmente están abandonados
  3. Limpiar recursos de tenant y usuario asociados
  4. Eliminar registros de DynamoDB
  5. Generar reporte de limpieza y métricas
```

### **💳 Payment Validation Job**
**Frecuencia**: Diario a medianoche UTC
**Propósito**: Validación de pagos y suscripciones con Stripe

```yaml
Configuración:
  check_overdue: true       # Verificar cuentas vencidas
  send_notifications: true  # Enviar notificaciones
  grace_period_days: 3      # Período de gracia
  batch_size: 50           # Tamaño de lote

Acciones:
  1. Consultar todas las suscripciones activas
  2. Validar estado actual con Stripe API
  3. Identificar pagos vencidos o fallidos
  4. Actualizar estados en DynamoDB
  5. Enviar notificaciones de problemas de pago
```

### **📧 Payment Reminders Job**
**Frecuencia**: Diario a medianoche UTC
**Propósito**: Recordatorios de pago programados

```yaml
Configuración:
  reminder_days: [7, 3, 1]  # Días antes del vencimiento
  overdue_days: [1, 7, 14]  # Días después del vencimiento
  batch_size: 50           # Tamaño de lote
  prevent_spam: true       # Prevenir spam de recordatorios

Acciones:
  1. Buscar suscripciones próximas a vencer
  2. Verificar historial de recordatorios enviados
  3. Enviar recordatorios según cronograma
  4. Tracking de envíos para prevenir spam
  5. Generar reporte de efectividad
```

---

## **⚙️ Configuración**

### **Variables de Entorno**
```yaml
# Core Configuration
STAGE: dev                           # Ambiente de deployment
REGION: us-east-1                   # Región AWS
PROJECT_NAME: agent-scl             # Nombre del proyecto
DYNAMODB_TABLE: agent-scl-dev       # Tabla DynamoDB unificada

# Service URLs
ORCHESTRATOR_SERVICE_URL: https://api.agent-scl.com/dev
AUTH_SERVICE_URL: https://api.agent-scl.com/dev
TENANT_SERVICE_URL: https://api.agent-scl.com/dev
PAYMENT_SERVICE_URL: https://api.agent-scl.com/dev

# Email Configuration
EMAIL_FROM: <EMAIL>
SES_REGION: us-east-1

# Job Configuration
REGISTRATION_CLEANUP_MAX_AGE_HOURS: 1
PAYMENT_VALIDATION_BATCH_SIZE: 50
PAYMENT_REMINDERS_BATCH_SIZE: 50
JOB_EXECUTION_TTL_DAYS: 30

# External Services
STRIPE_API_KEY: ${ssm:/agent-scl/dev/stripe-api-key}
```

### **CloudWatch Events Scheduling**
```yaml
# Registration Cleanup (Hourly)
RegistrationCleanupSchedule:
  ScheduleExpression: "rate(1 hour)"
  State: ENABLED

# Payment Validation (Daily at midnight UTC)
PaymentValidationSchedule:
  ScheduleExpression: "cron(0 0 * * ? *)"
  State: ENABLED

# Payment Reminders (Daily at midnight UTC)
PaymentRemindersSchedule:
  ScheduleExpression: "cron(0 0 * * ? *)"
  State: ENABLED
```

---

## **📡 API Reference**

### **Manual Job Execution**

#### **Execute Job**
```http
POST /jobs/execute
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
    "job_type": "REGISTRATION_CLEANUP",
    "configuration": {
        "max_age_hours": 2,
        "batch_size": 50,
        "dry_run": true
    }
}

Response:
{
    "success": true,
    "data": {
        "execution_id": "exec-abc123def456",
        "job_type": "REGISTRATION_CLEANUP",
        "status": "STARTED",
        "started_at": "2024-01-15T10:30:00Z",
        "configuration": {
            "max_age_hours": 2,
            "batch_size": 50,
            "dry_run": true
        }
    }
}
```

#### **Get Job Status**
```http
GET /jobs/status/{executionId}
Authorization: Bearer JWT_TOKEN

Response:
{
    "success": true,
    "data": {
        "execution_id": "exec-abc123def456",
        "job_type": "REGISTRATION_CLEANUP",
        "status": "COMPLETED",
        "started_at": "2024-01-15T10:30:00Z",
        "completed_at": "2024-01-15T10:35:00Z",
        "items_processed": 45,
        "items_succeeded": 43,
        "items_failed": 2,
        "execution_time_seconds": 300,
        "error_details": null
    }
}
```

#### **Health Check**
```http
GET /jobs/health

Response:
{
    "status": "healthy",
    "jobs": {
        "registration_cleanup": {
            "last_execution": "2024-01-15T09:00:00Z",
            "status": "COMPLETED",
            "next_execution": "2024-01-15T10:00:00Z"
        },
        "payment_validation": {
            "last_execution": "2024-01-15T00:00:00Z",
            "status": "COMPLETED",
            "next_execution": "2024-01-16T00:00:00Z"
        }
    },
    "external_services": {
        "stripe_api": "healthy",
        "ses": "healthy",
        "dynamodb": "healthy"
    }
}
```

### **Job Types Disponibles**
```yaml
REGISTRATION_CLEANUP:
  description: "Limpieza de registros abandonados"
  frequency: "Hourly"

PAYMENT_VALIDATION:
  description: "Validación de pagos con Stripe"
  frequency: "Daily"

PAYMENT_REMINDERS:
  description: "Recordatorios de pago programados"
  frequency: "Daily"
```

---

## **📅 Scheduling**

### **Cronograma de Jobs**
```mermaid
gantt
    title Jobs Service - Cronograma Diario
    dateFormat HH:mm
    axisFormat %H:%M

    section Hourly Jobs
    Registration Cleanup    :active, cleanup, 00:00, 24:00

    section Daily Jobs
    Payment Validation      :milestone, validation, 00:00, 00:30
    Payment Reminders       :milestone, reminders, 00:30, 01:00
    Metrics Aggregation     :milestone, metrics, 01:00, 01:15
```

### **Configuración de Triggers**
- **Registration Cleanup**: `rate(1 hour)` - Cada hora
- **Payment Validation**: `cron(0 0 * * ? *)` - Diario a medianoche UTC
- **Payment Reminders**: `cron(0 0 * * ? *)` - Diario a medianoche UTC
- **Metrics Aggregation**: `cron(0 1 * * ? *)` - Diario a la 1:00 AM UTC

## 🔧 **API Endpoints**

### **POST /jobs/execute**
Execute jobs manually for testing or emergency situations.

**Request:**
```json
{
  "job_type": "REGISTRATION_CLEANUP",
  "configuration": {
    "max_age_hours": 2,
    "batch_size": 50,
    "dry_run": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "registration_cleanup_abc123",
    "job_type": "REGISTRATION_CLEANUP",
    "success": true,
    "items_processed": 25,
    "items_succeeded": 25,
    "items_failed": 0,
    "duration_seconds": 12.5,
    "message": "Cleanup completed: 25 succeeded, 0 failed"
  }
}
```

### **GET /jobs/status**
Get job execution status and statistics.

**Query Parameters:**
- `job_type`: Filter by specific job type
- `hours`: Time range in hours (default: 24)

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "time_range_hours": 24,
      "total_executions": 48,
      "total_successes": 47,
      "total_failures": 1,
      "overall_success_rate": 97.92
    },
    "job_types": {
      "REGISTRATION_CLEANUP": {
        "executions": 24,
        "successes": 24,
        "failures": 0,
        "success_rate": 100.0,
        "avg_duration_seconds": 8.5
      }
    }
  }
}
```

### **GET /health**
Service health check with job execution diagnostics.

## 📊 **Monitoring**

### **CloudWatch Metrics**
- `{JobType}_Executions` - Number of job executions
- `{JobType}_Failures` - Number of failed executions
- `{JobType}_Duration` - Execution duration
- `{JobType}_ItemsProcessed` - Items processed per execution
- `{JobType}_SuccessRate` - Success rate percentage

### **Alarms**
- High failure rate (>20% in 1 hour)
- Long execution times (>5 minutes)
- No recent executions (>25 hours for daily jobs)
- Email delivery failures

### **Dashboards**
- Job execution trends
- Success/failure rates
- Performance metrics
- System health overview

## 🔒 **Security**

### **Rate Limiting**
- Manual execution: 10 requests/minute
- Status checks: 30 requests/minute
- Health checks: 60 requests/minute

### **Data Protection**
- Secure email handling
- Audit logging for all operations
- Encrypted data in transit and at rest
- Minimal data retention

## 🚀 **Deployment**

### **Environment Variables**
```bash
# Service endpoints
ORCHESTRATOR_SERVICE_URL=https://api.platform.com/orchestrator
PAYMENT_SERVICE_URL=https://api.platform.com/payment
TENANT_SERVICE_URL=https://api.platform.com/tenant

# DynamoDB tables
REGISTRATIONS_TABLE=agent-scl-registrations-dev
SUBSCRIPTIONS_TABLE=agent-scl-main-dev

# Email
EMAIL_FROM=<EMAIL>

# AWS
STAGE=dev
REGION=us-east-1
```

### **Deploy Commands**
```bash
# Install dependencies
npm install

# Deploy to dev
serverless deploy --stage dev

# Deploy to production
serverless deploy --stage prod
```

## 🧪 **Testing**

### **Unit Tests**
```bash
# Run unit tests
pytest tests/unit/

# Test specific job service
pytest tests/unit/test_registration_cleanup_service.py
```

### **Integration Tests**
```bash
# Run integration tests
pytest tests/integration/

# Test job execution flow
pytest tests/integration/test_job_execution.py
```

### **Manual Testing**
```bash
# Execute job manually
curl -X POST https://api.dev.com/jobs/execute \
  -H "Content-Type: application/json" \
  -d '{
    "job_type": "REGISTRATION_CLEANUP",
    "configuration": {"dry_run": true}
  }'

# Check job status
curl https://api.dev.com/jobs/status

# Check service health
curl https://api.dev.com/jobs/health
```

## 📝 **Email Templates**

### **Payment Reminder (Due Soon)**
```html
<h2>Payment Reminder</h2>
<p>Your subscription payment is due in {days} day(s).</p>
<p>Please ensure your payment method is up to date.</p>
```

### **Payment Overdue**
```html
<h2 style="color: #d32f2f;">Payment Overdue Notice</h2>
<p>Your subscription payment is overdue by {days} day(s).</p>
<p>Please update your payment method immediately.</p>
```

## 🐛 **Troubleshooting**

### **Common Issues**

**Jobs Not Executing**
- Check CloudWatch Events rules are enabled
- Verify IAM permissions for Lambda execution
- Review CloudWatch Logs for errors

**Email Notifications Not Sending**
- Check SES configuration and limits
- Verify email addresses are verified in SES
- Review bounce and complaint rates

**High Failure Rates**
- Check external service availability
- Review error logs for patterns
- Verify database connectivity

### **Debug Commands**
```bash
# Check service health
curl https://api.dev.com/jobs/health

# View CloudWatch logs
aws logs filter-log-events \
  --log-group-name /aws/lambda/jobs-dev-registrationCleanup \
  --start-time 1692000000000

# Check CloudWatch metrics
aws cloudwatch get-metric-statistics \
  --namespace Jobs/Scheduled \
  --metric-name REGISTRATION_CLEANUP_Executions \
  --start-time 2023-08-14T00:00:00Z \
  --end-time 2023-08-15T00:00:00Z \
  --period 3600 \
  --statistics Sum

# Check scheduled events
aws events list-rules --name-prefix jobs-dev
```

## 📈 **Performance**

### **Optimization Strategies**
- **Batch Processing**: Process items in configurable batches
- **Parallel Execution**: Use concurrent processing where possible
- **Efficient Queries**: Optimize DynamoDB queries with proper indexing
- **Caching**: Cache frequently accessed data
- **Monitoring**: Track performance metrics and optimize bottlenecks

### **Scaling Considerations**
- **Lambda Concurrency**: Configure reserved concurrency for critical jobs
- **DynamoDB Capacity**: Monitor and adjust read/write capacity
- **SES Limits**: Monitor sending quotas and rates
- **Error Handling**: Implement exponential backoff for retries

## 🔄 **Future Enhancements**

- [ ] Advanced job scheduling with cron expressions
- [ ] Job dependency management and workflows
- [ ] Real-time job monitoring dashboard
- [ ] Custom notification channels (Slack, SMS)
- [ ] Job result persistence and history
- [ ] Advanced retry mechanisms with dead letter queues
- [ ] Multi-region job execution
- [ ] Job performance analytics and optimization recommendations

---

## **📚 Documentación Adicional**

- 📖 [Documentación Técnica Completa](./TECHNICAL_DOCUMENTATION.md)
- 🔧 [Guía de Configuración de Jobs](./docs/job-configuration.md)
- 🚀 [Guía de Deployment](./docs/deployment.md)
- 🐛 [Troubleshooting Avanzado](./docs/troubleshooting.md)

---

## **📝 Changelog**

### **v1.0.0 (2024-08-29) - ✅ DEPLOYED**
- ✅ Jobs programados con CloudWatch Events
- ✅ Registration cleanup automático
- ✅ Payment validation con Stripe
- ✅ Payment reminders inteligentes
- ✅ Ejecución manual de jobs
- ✅ Batch processing optimizado
- ✅ Métricas y logging detallados
- ✅ Manejo robusto de errores

### **Próximas Versiones**
- 🔄 Machine Learning para optimización de batch sizes
- 🔄 Advanced scheduling dinámico
- 🔄 Multi-region job distribution
- 🔄 Real-time job monitoring dashboard

---

**📝 Documento actualizado**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
**🔗 Repositorio**: [agent-scl/services/jobs](https://github.com/agent-scl/services/jobs)
