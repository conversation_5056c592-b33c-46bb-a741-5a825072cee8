sequenceDiagram
    participant CWE as ⏰ CloudWatch Events
    participant PVH as 💳 Payment Validation Handler
    participant PVS as 💳 Payment Validation Service
    participant PRH as 📧 Payment Reminders Handler
    participant PRS as 📧 Payment Reminders Service
    participant BJS as 🔧 Base Job Service
    participant DB as 🗄️ DynamoDB
    participant PS as 💳 Payment Service
    participant Stripe as 💳 Stripe API
    participant SES as 📧 AWS SES
    participant CW as 📊 CloudWatch

    Note over CWE: Daily trigger at midnight UTC
    
    rect rgb(245, 255, 245)
        Note over PVH: Payment Validation Job
        CWE->>PVH: Trigger payment validation job
        PVH->>BJS: create_job_execution(PAYMENT_VALIDATION)
        BJS->>DB: Store job execution record
        
        PVH->>PVS: execute_job_logic(execution)
        PVS->>DB: Query active subscriptions
        Note over DB: Query all active subscriptions<br/>batch_size: 50
        DB-->>PVS: List of active subscriptions
        
        loop For each subscription batch
            loop For each subscription
                PVS->>PS: get_subscription_details(subscription_id)
                PS-->>PVS: Subscription details
                
                PVS->>Stripe: retrieve_subscription(stripe_subscription_id)
                Stripe-->>PVS: Stripe subscription status
                
                PVS->>PVS: Compare local vs Stripe status
                
                alt Payment is overdue
                    PVS->>DB: Update subscription status to OVERDUE
                    PVS->>SES: Send overdue notification
                    Note over SES: Template: payment_overdue<br/>Recipient: tenant admin
                    SES-->>PVS: Email sent
                    PVS->>DB: Track notification sent
                else Payment is current
                    PVS->>DB: Update last_validated timestamp
                end
                
                PVS->>PVS: Update processing counters
            end
            
            PVS->>BJS: update_job_progress(execution_id, batch_results)
            BJS->>CW: Send progress metrics
        end
        
        PVS-->>PVH: Validation completed
        PVH->>BJS: complete_job_execution(execution_id, results)
        BJS->>CW: Send completion metrics
    end
    
    rect rgb(255, 255, 245)
        Note over PRH: Payment Reminders Job (runs after validation)
        CWE->>PRH: Trigger payment reminders job
        PRH->>BJS: create_job_execution(PAYMENT_REMINDERS)
        BJS->>DB: Store job execution record
        
        PRH->>PRS: execute_job_logic(execution)
        PRS->>DB: Query subscriptions needing reminders
        Note over DB: Find subscriptions 7, 3, 1 days before due<br/>and 1, 7, 14 days overdue
        DB-->>PRS: List of subscriptions needing reminders
        
        loop For each subscription needing reminder
            PRS->>DB: Check reminder history
            Note over DB: Query last reminder sent<br/>prevent spam
            DB-->>PRS: Last reminder details
            
            PRS->>PRS: Determine reminder type needed
            Note over PRS: Types: pre_due_7, pre_due_3, pre_due_1<br/>overdue_1, overdue_7, overdue_14
            
            alt Reminder needed
                PRS->>SES: Send payment reminder
                Note over SES: Template based on reminder type<br/>Personalized content
                SES-->>PRS: Email sent successfully
                
                PRS->>DB: Track reminder sent
                Note over DB: Store reminder history<br/>prevent duplicate sends
                
                PRS->>PRS: Update success counter
            else Reminder already sent recently
                PRS->>PRS: Skip reminder (prevent spam)
                PRS->>PRS: Update skipped counter
            end
        end
        
        PRS->>PRS: Generate reminder summary
        Note over PRS: reminders_sent: 23<br/>reminders_skipped: 5<br/>email_failures: 1
        
        PRS-->>PRH: Reminders completed
        PRH->>BJS: complete_job_execution(execution_id, results)
        BJS->>CW: Send completion metrics
        Note over CW: Custom metrics:<br/>- jobs.reminders.sent<br/>- jobs.payments.validated<br/>- jobs.notifications.delivered
    end
    
    rect rgb(255, 245, 245)
        Note over PVS: Error handling scenarios
        
        alt Stripe API error
            PVS->>PVS: Handle Stripe API failure
            PVS->>BJS: log_job_warning(execution_id, "stripe_api_error")
            PVS->>CW: Send error metrics
        end
        
        alt Email delivery failure
            PRS->>PRS: Handle SES delivery failure
            PRS->>BJS: log_job_warning(execution_id, "email_delivery_failed")
            PRS->>DB: Mark email as failed for retry
        end
    end
    
    Note over CWE,CW: Payment validation and reminders completed