#!/usr/bin/env python3
# services/jobs/src/services/payment_validation_service.py
# Payment validation service

"""
Payment validation service for checking payment status and handling overdue accounts.
Validates subscription payments and triggers appropriate actions.
"""

import os
import boto3
from typing import Dict, Any, List
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

from shared.logger import lambda_logger
from .base_job_service import BaseJobService
from ..models.job_models import JobType, JobResult, JobExecution, PaymentValidationConfig


class PaymentValidationService(BaseJobService):
    """Service for validating payment status and handling overdue accounts."""
    
    def __init__(self):
        super().__init__()
        self.subscriptions_table_name = os.environ.get('SUBSCRIPTIONS_TABLE')
        if not self.subscriptions_table_name:
            raise ValueError("SUBSCRIPTIONS_TABLE environment variable not set")
        
        self.subscriptions_table = self.dynamodb.Table(self.subscriptions_table_name)
    
    def get_job_type(self) -> JobType:
        """Get the job type."""
        return JobType.PAYMENT_VALIDATION
    
    def execute_job_logic(self, execution: JobExecution) -> JobResult:
        """Execute payment validation logic."""
        try:
            config = PaymentValidationConfig(**execution.configuration)
            
            execution.add_log_entry("INFO", "Starting payment validation", {
                'check_overdue': config.check_overdue,
                'send_notifications': config.send_notifications,
                'grace_period_days': config.grace_period_days,
                'batch_size': config.batch_size
            })
            
            # Get active subscriptions
            active_subscriptions = self._get_active_subscriptions()
            
            execution.add_log_entry("INFO", f"Found {len(active_subscriptions)} active subscriptions")
            
            # Filter subscriptions that need validation
            subscriptions_to_validate = self._filter_subscriptions_for_validation(
                active_subscriptions, config
            )
            
            execution.add_log_entry("INFO", f"Found {len(subscriptions_to_validate)} subscriptions to validate")
            
            # Process in batches
            total_processed = 0
            total_succeeded = 0
            total_failed = 0
            overdue_count = 0
            notifications_sent = 0
            
            for i in range(0, len(subscriptions_to_validate), config.batch_size):
                batch = subscriptions_to_validate[i:i + config.batch_size]
                
                batch_results = self._process_validation_batch(batch, config)
                
                total_processed += len(batch)
                total_succeeded += batch_results['succeeded']
                total_failed += batch_results['failed']
                overdue_count += batch_results['overdue_count']
                notifications_sent += batch_results['notifications_sent']
                
                execution.add_log_entry("INFO", f"Processed batch {i//config.batch_size + 1}", {
                    'batch_size': len(batch),
                    'succeeded': batch_results['succeeded'],
                    'failed': batch_results['failed'],
                    'overdue_count': batch_results['overdue_count'],
                    'notifications_sent': batch_results['notifications_sent']
                })
                
                # Update execution progress
                execution.items_processed = total_processed
                execution.items_succeeded = total_succeeded
                execution.items_failed = total_failed
            
            # Prepare result
            success = total_failed == 0
            message = f"Validation completed: {total_succeeded} succeeded, {total_failed} failed, {overdue_count} overdue found"
            
            return JobResult(
                success=success,
                execution_id=execution.execution_id,
                job_type=self.get_job_type(),
                items_processed=total_processed,
                items_succeeded=total_succeeded,
                items_failed=total_failed,
                duration_seconds=execution.get_duration_seconds(),
                message=message,
                details={
                    'total_subscriptions': len(active_subscriptions),
                    'validated_subscriptions': len(subscriptions_to_validate),
                    'overdue_count': overdue_count,
                    'notifications_sent': notifications_sent,
                    'grace_period_days': config.grace_period_days
                }
            )
            
        except Exception as e:
            execution.add_log_entry("ERROR", f"Payment validation failed: {str(e)}")
            raise
    
    def _get_active_subscriptions(self) -> List[Dict[str, Any]]:
        """Get all active subscriptions from DynamoDB."""
        try:
            # In a real implementation, this would query the subscriptions table
            # For now, we'll simulate with a scan (not efficient for production)
            response = self.subscriptions_table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('entity_type').eq('subscription') &
                                boto3.dynamodb.conditions.Attr('status').is_in(['active', 'past_due'])
            )
            
            return response.get('Items', [])
            
        except ClientError as e:
            lambda_logger.error("Failed to get active subscriptions", extra={
                'error': str(e)
            })
            raise
    
    def _filter_subscriptions_for_validation(self, subscriptions: List[Dict[str, Any]], 
                                           config: PaymentValidationConfig) -> List[Dict[str, Any]]:
        """Filter subscriptions that need validation."""
        filtered = []
        
        for subscription in subscriptions:
            try:
                # Check if subscription needs validation
                if self._subscription_needs_validation(subscription, config):
                    filtered.append(subscription)
                    
            except Exception as e:
                lambda_logger.warning("Failed to check subscription for validation", extra={
                    'subscription_id': subscription.get('subscription_id'),
                    'error': str(e)
                })
        
        return filtered
    
    def _subscription_needs_validation(self, subscription: Dict[str, Any], 
                                     config: PaymentValidationConfig) -> bool:
        """Check if a subscription needs validation."""
        try:
            status = subscription.get('status')
            current_period_end = subscription.get('current_period_end')
            
            # Always validate past_due subscriptions
            if status == 'past_due':
                return True
            
            # Check if subscription is approaching due date
            if current_period_end and config.check_overdue:
                try:
                    period_end = datetime.fromisoformat(current_period_end)
                    days_until_due = (period_end - datetime.utcnow()).days
                    
                    # Validate if within grace period or overdue
                    if days_until_due <= config.grace_period_days:
                        return True
                        
                except ValueError:
                    lambda_logger.warning("Invalid period_end format", extra={
                        'subscription_id': subscription.get('subscription_id'),
                        'current_period_end': current_period_end
                    })
            
            return False
            
        except Exception as e:
            lambda_logger.error("Failed to check if subscription needs validation", extra={
                'subscription_id': subscription.get('subscription_id'),
                'error': str(e)
            })
            return False
    
    def _process_validation_batch(self, batch: List[Dict[str, Any]], 
                                config: PaymentValidationConfig) -> Dict[str, int]:
        """Process a batch of subscriptions for validation."""
        succeeded = 0
        failed = 0
        overdue_count = 0
        notifications_sent = 0
        
        for subscription in batch:
            try:
                subscription_id = subscription.get('subscription_id')
                tenant_id = subscription.get('tenant_id')
                status = subscription.get('status')
                
                lambda_logger.info("Validating subscription", extra={
                    'subscription_id': subscription_id,
                    'tenant_id': tenant_id,
                    'status': status
                })
                
                # Validate payment status with Stripe
                validation_result = self._validate_subscription_with_stripe(subscription)
                
                # Handle validation result
                if validation_result['is_overdue']:
                    overdue_count += 1
                    
                    # Send notification if configured
                    if config.send_notifications:
                        self._send_overdue_notification(subscription, validation_result)
                        notifications_sent += 1
                    
                    # Update subscription status if needed
                    self._update_subscription_status(subscription, validation_result)
                
                succeeded += 1
                
            except Exception as e:
                lambda_logger.error("Failed to validate subscription", extra={
                    'subscription_id': subscription.get('subscription_id'),
                    'error': str(e)
                })
                failed += 1
        
        return {
            'succeeded': succeeded,
            'failed': failed,
            'overdue_count': overdue_count,
            'notifications_sent': notifications_sent
        }
    
    def _validate_subscription_with_stripe(self, subscription: Dict[str, Any]) -> Dict[str, Any]:
        """Validate subscription status with Stripe."""
        try:
            subscription_id = subscription.get('subscription_id')
            
            # In a real implementation, this would call Stripe API
            # For now, we'll simulate the validation
            
            current_period_end = subscription.get('current_period_end')
            status = subscription.get('status')
            
            is_overdue = False
            days_overdue = 0
            
            if current_period_end:
                try:
                    period_end = datetime.fromisoformat(current_period_end)
                    days_overdue = (datetime.utcnow() - period_end).days
                    is_overdue = days_overdue > 0
                except ValueError:
                    pass
            
            # Simulate Stripe validation result
            return {
                'subscription_id': subscription_id,
                'stripe_status': status,
                'is_overdue': is_overdue,
                'days_overdue': max(0, days_overdue),
                'current_period_end': current_period_end,
                'validation_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to validate with Stripe", extra={
                'subscription_id': subscription.get('subscription_id'),
                'error': str(e)
            })
            raise
    
    def _send_overdue_notification(self, subscription: Dict[str, Any], 
                                 validation_result: Dict[str, Any]) -> None:
        """Send overdue payment notification."""
        try:
            tenant_id = subscription.get('tenant_id')
            subscription_id = subscription.get('subscription_id')
            days_overdue = validation_result.get('days_overdue', 0)
            
            # Get tenant information for email
            tenant_info = self._get_tenant_info(tenant_id)
            if not tenant_info:
                lambda_logger.warning("Cannot send notification - tenant not found", extra={
                    'tenant_id': tenant_id,
                    'subscription_id': subscription_id
                })
                return
            
            # Prepare email content
            subject = f"Payment Overdue - Action Required"
            
            body_html = f"""
            <html>
            <body>
                <h2>Payment Overdue Notice</h2>
                <p>Dear {tenant_info.get('company_name', 'Valued Customer')},</p>
                
                <p>Your subscription payment is overdue by {days_overdue} day(s).</p>
                
                <p><strong>Subscription Details:</strong></p>
                <ul>
                    <li>Subscription ID: {subscription_id}</li>
                    <li>Days Overdue: {days_overdue}</li>
                    <li>Status: {subscription.get('status', 'Unknown')}</li>
                </ul>
                
                <p>Please update your payment method or contact our support team to avoid service interruption.</p>
                
                <p>Best regards,<br>The Platform Team</p>
            </body>
            </html>
            """
            
            # Send email
            self.send_email_notification(
                recipient=tenant_info.get('email', ''),
                subject=subject,
                body_html=body_html
            )
            
            lambda_logger.info("Overdue notification sent", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'days_overdue': days_overdue
            })
            
        except Exception as e:
            lambda_logger.error("Failed to send overdue notification", extra={
                'tenant_id': subscription.get('tenant_id'),
                'subscription_id': subscription.get('subscription_id'),
                'error': str(e)
            })
    
    def _update_subscription_status(self, subscription: Dict[str, Any], 
                                  validation_result: Dict[str, Any]) -> None:
        """Update subscription status based on validation result."""
        try:
            subscription_id = subscription.get('subscription_id')
            tenant_id = subscription.get('tenant_id')
            
            # Update subscription record with validation results
            update_data = {
                'last_validation': validation_result['validation_timestamp'],
                'stripe_status': validation_result['stripe_status'],
                'days_overdue': validation_result['days_overdue']
            }
            
            # In a real implementation, this would update the subscription record
            lambda_logger.info("Subscription status updated", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'update_data': update_data
            })
            
        except Exception as e:
            lambda_logger.error("Failed to update subscription status", extra={
                'subscription_id': subscription.get('subscription_id'),
                'error': str(e)
            })
    
    def _get_tenant_info(self, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get tenant information for notifications."""
        try:
            # In a real implementation, this would call the Tenant Service
            # For now, return mock data
            return {
                'tenant_id': tenant_id,
                'company_name': 'Test Company',
                'email': '<EMAIL>'
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant info", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
