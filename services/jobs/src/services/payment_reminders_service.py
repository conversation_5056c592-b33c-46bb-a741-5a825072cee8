#!/usr/bin/env python3
# services/jobs/src/services/payment_reminders_service.py
# Payment reminders service

"""
Payment reminders service for sending payment due and overdue notifications.
Sends timely reminders to prevent payment failures and service interruptions.
"""

import os
import boto3
from typing import Dict, Any, List
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

from shared.logger import lambda_logger
from .base_job_service import BaseJobService
from ..models.job_models import JobType, JobResult, JobExecution, PaymentRemindersConfig


class PaymentRemindersService(BaseJobService):
    """Service for sending payment reminders."""
    
    def __init__(self):
        super().__init__()
        self.subscriptions_table_name = os.environ.get('SUBSCRIPTIONS_TABLE')
        if not self.subscriptions_table_name:
            raise ValueError("SUBSCRIPTIONS_TABLE environment variable not set")
        
        self.subscriptions_table = self.dynamodb.Table(self.subscriptions_table_name)
    
    def get_job_type(self) -> JobType:
        """Get the job type."""
        return JobType.PAYMENT_REMINDERS
    
    def execute_job_logic(self, execution: JobExecution) -> JobResult:
        """Execute payment reminders logic."""
        try:
            config = PaymentRemindersConfig(**execution.configuration)
            
            execution.add_log_entry("INFO", "Starting payment reminders", {
                'reminder_days': config.reminder_days,
                'overdue_days': config.overdue_days,
                'batch_size': config.batch_size
            })
            
            # Get subscriptions needing reminders
            reminder_subscriptions = self._get_subscriptions_needing_reminders(config)
            
            execution.add_log_entry("INFO", f"Found {len(reminder_subscriptions)} subscriptions needing reminders")
            
            # Process in batches
            total_processed = 0
            total_succeeded = 0
            total_failed = 0
            reminders_sent = 0
            
            for i in range(0, len(reminder_subscriptions), config.batch_size):
                batch = reminder_subscriptions[i:i + config.batch_size]
                
                batch_results = self._process_reminders_batch(batch, config)
                
                total_processed += len(batch)
                total_succeeded += batch_results['succeeded']
                total_failed += batch_results['failed']
                reminders_sent += batch_results['reminders_sent']
                
                execution.add_log_entry("INFO", f"Processed batch {i//config.batch_size + 1}", {
                    'batch_size': len(batch),
                    'succeeded': batch_results['succeeded'],
                    'failed': batch_results['failed'],
                    'reminders_sent': batch_results['reminders_sent']
                })
                
                # Update execution progress
                execution.items_processed = total_processed
                execution.items_succeeded = total_succeeded
                execution.items_failed = total_failed
            
            # Prepare result
            success = total_failed == 0
            message = f"Reminders completed: {reminders_sent} reminders sent, {total_failed} failed"
            
            return JobResult(
                success=success,
                execution_id=execution.execution_id,
                job_type=self.get_job_type(),
                items_processed=total_processed,
                items_succeeded=total_succeeded,
                items_failed=total_failed,
                duration_seconds=execution.get_duration_seconds(),
                message=message,
                details={
                    'reminders_sent': reminders_sent,
                    'reminder_days': config.reminder_days,
                    'overdue_days': config.overdue_days
                }
            )
            
        except Exception as e:
            execution.add_log_entry("ERROR", f"Payment reminders failed: {str(e)}")
            raise
    
    def _get_subscriptions_needing_reminders(self, config: PaymentRemindersConfig) -> List[Dict[str, Any]]:
        """Get subscriptions that need payment reminders."""
        try:
            # Get active subscriptions
            response = self.subscriptions_table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('entity_type').eq('subscription') &
                                boto3.dynamodb.conditions.Attr('status').is_in(['active', 'past_due'])
            )
            
            all_subscriptions = response.get('Items', [])
            
            # Filter subscriptions that need reminders
            needing_reminders = []
            
            for subscription in all_subscriptions:
                reminder_type = self._get_reminder_type(subscription, config)
                if reminder_type:
                    subscription['reminder_type'] = reminder_type
                    needing_reminders.append(subscription)
            
            return needing_reminders
            
        except ClientError as e:
            lambda_logger.error("Failed to get subscriptions needing reminders", extra={
                'error': str(e)
            })
            raise
    
    def _get_reminder_type(self, subscription: Dict[str, Any], 
                          config: PaymentRemindersConfig) -> Optional[str]:
        """Determine if subscription needs a reminder and what type."""
        try:
            current_period_end = subscription.get('current_period_end')
            status = subscription.get('status')
            last_reminder_sent = subscription.get('last_reminder_sent')
            
            if not current_period_end:
                return None
            
            try:
                period_end = datetime.fromisoformat(current_period_end)
                now = datetime.utcnow()
                days_until_due = (period_end - now).days
                days_overdue = (now - period_end).days
                
                # Check if we already sent a reminder recently (within 24 hours)
                if last_reminder_sent:
                    last_reminder = datetime.fromisoformat(last_reminder_sent)
                    hours_since_last = (now - last_reminder).total_seconds() / 3600
                    if hours_since_last < 24:
                        return None  # Don't spam with reminders
                
                # Check for upcoming due date reminders
                if days_until_due >= 0 and days_until_due in config.reminder_days:
                    return f"due_in_{days_until_due}_days"
                
                # Check for overdue reminders
                if days_overdue > 0 and days_overdue in config.overdue_days:
                    return f"overdue_{days_overdue}_days"
                
                return None
                
            except ValueError:
                lambda_logger.warning("Invalid period_end format", extra={
                    'subscription_id': subscription.get('subscription_id'),
                    'current_period_end': current_period_end
                })
                return None
                
        except Exception as e:
            lambda_logger.error("Failed to determine reminder type", extra={
                'subscription_id': subscription.get('subscription_id'),
                'error': str(e)
            })
            return None
    
    def _process_reminders_batch(self, batch: List[Dict[str, Any]], 
                               config: PaymentRemindersConfig) -> Dict[str, int]:
        """Process a batch of subscriptions for reminders."""
        succeeded = 0
        failed = 0
        reminders_sent = 0
        
        for subscription in batch:
            try:
                subscription_id = subscription.get('subscription_id')
                tenant_id = subscription.get('tenant_id')
                reminder_type = subscription.get('reminder_type')
                
                lambda_logger.info("Processing payment reminder", extra={
                    'subscription_id': subscription_id,
                    'tenant_id': tenant_id,
                    'reminder_type': reminder_type
                })
                
                # Send reminder
                reminder_sent = self._send_payment_reminder(subscription, reminder_type)
                
                if reminder_sent:
                    reminders_sent += 1
                    
                    # Update last reminder sent timestamp
                    self._update_last_reminder_sent(subscription)
                
                succeeded += 1
                
            except Exception as e:
                lambda_logger.error("Failed to process payment reminder", extra={
                    'subscription_id': subscription.get('subscription_id'),
                    'error': str(e)
                })
                failed += 1
        
        return {
            'succeeded': succeeded,
            'failed': failed,
            'reminders_sent': reminders_sent
        }
    
    def _send_payment_reminder(self, subscription: Dict[str, Any], 
                             reminder_type: str) -> bool:
        """Send payment reminder email."""
        try:
            tenant_id = subscription.get('tenant_id')
            subscription_id = subscription.get('subscription_id')
            current_period_end = subscription.get('current_period_end')
            
            # Get tenant information
            tenant_info = self._get_tenant_info(tenant_id)
            if not tenant_info:
                lambda_logger.warning("Cannot send reminder - tenant not found", extra={
                    'tenant_id': tenant_id,
                    'subscription_id': subscription_id
                })
                return False
            
            # Prepare email content based on reminder type
            subject, body_html = self._prepare_reminder_email(
                subscription, tenant_info, reminder_type
            )
            
            # Send email
            self.send_email_notification(
                recipient=tenant_info.get('email', ''),
                subject=subject,
                body_html=body_html
            )
            
            lambda_logger.info("Payment reminder sent", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'reminder_type': reminder_type
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to send payment reminder", extra={
                'tenant_id': subscription.get('tenant_id'),
                'subscription_id': subscription.get('subscription_id'),
                'reminder_type': reminder_type,
                'error': str(e)
            })
            return False
    
    def _prepare_reminder_email(self, subscription: Dict[str, Any], 
                              tenant_info: Dict[str, Any], 
                              reminder_type: str) -> tuple[str, str]:
        """Prepare email subject and body for reminder."""
        company_name = tenant_info.get('company_name', 'Valued Customer')
        subscription_id = subscription.get('subscription_id')
        current_period_end = subscription.get('current_period_end')
        
        # Parse reminder type
        if reminder_type.startswith('due_in_'):
            days = reminder_type.split('_')[2]
            subject = f"Payment Due in {days} Day(s) - Action Required"
            urgency = "upcoming"
        elif reminder_type.startswith('overdue_'):
            days = reminder_type.split('_')[1]
            subject = f"Payment Overdue - Immediate Action Required"
            urgency = "overdue"
        else:
            subject = "Payment Reminder"
            urgency = "general"
        
        # Format due date
        due_date = "Unknown"
        if current_period_end:
            try:
                period_end = datetime.fromisoformat(current_period_end)
                due_date = period_end.strftime("%B %d, %Y")
            except ValueError:
                pass
        
        # Prepare HTML body
        if urgency == "upcoming":
            body_html = f"""
            <html>
            <body>
                <h2>Payment Reminder</h2>
                <p>Dear {company_name},</p>
                
                <p>This is a friendly reminder that your subscription payment is due in {days} day(s).</p>
                
                <p><strong>Payment Details:</strong></p>
                <ul>
                    <li>Subscription ID: {subscription_id}</li>
                    <li>Due Date: {due_date}</li>
                    <li>Days Until Due: {days}</li>
                </ul>
                
                <p>Please ensure your payment method is up to date to avoid any service interruption.</p>
                
                <p>Best regards,<br>The Platform Team</p>
            </body>
            </html>
            """
        else:  # overdue
            body_html = f"""
            <html>
            <body>
                <h2 style="color: #d32f2f;">Payment Overdue Notice</h2>
                <p>Dear {company_name},</p>
                
                <p><strong>Your subscription payment is overdue and requires immediate attention.</strong></p>
                
                <p><strong>Payment Details:</strong></p>
                <ul>
                    <li>Subscription ID: {subscription_id}</li>
                    <li>Due Date: {due_date}</li>
                    <li>Days Overdue: {days}</li>
                </ul>
                
                <p style="color: #d32f2f;"><strong>Action Required:</strong> Please update your payment method immediately to avoid service suspension.</p>
                
                <p>If you have any questions, please contact our support team.</p>
                
                <p>Best regards,<br>The Platform Team</p>
            </body>
            </html>
            """
        
        return subject, body_html
    
    def _update_last_reminder_sent(self, subscription: Dict[str, Any]) -> None:
        """Update the last reminder sent timestamp."""
        try:
            subscription_id = subscription.get('subscription_id')
            tenant_id = subscription.get('tenant_id')
            
            # In a real implementation, this would update the subscription record
            lambda_logger.info("Last reminder timestamp updated", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            lambda_logger.error("Failed to update last reminder sent", extra={
                'subscription_id': subscription.get('subscription_id'),
                'error': str(e)
            })
    
    def _get_tenant_info(self, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get tenant information for notifications."""
        try:
            # In a real implementation, this would call the Tenant Service
            # For now, return mock data
            return {
                'tenant_id': tenant_id,
                'company_name': 'Test Company',
                'email': '<EMAIL>'
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant info", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
