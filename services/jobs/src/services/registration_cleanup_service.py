#!/usr/bin/env python3
# services/jobs/src/services/registration_cleanup_service.py
# Registration cleanup service

"""
Registration cleanup service for removing abandoned registrations.
Handles cleanup of expired and abandoned registration records.
"""

import os
import boto3
from typing import Dict, Any, List
from datetime import datetime, timedelta
from botocore.exceptions import Client<PERSON>rror

from shared.logger import lambda_logger
from .base_job_service import BaseJobService
from ..models.job_models import JobType, JobResult, JobExecution, RegistrationCleanupConfig


class RegistrationCleanupService(BaseJobService):
    """Service for cleaning up abandoned registrations."""
    
    def __init__(self):
        super().__init__()
        self.registrations_table_name = os.environ.get('REGISTRATIONS_TABLE')
        if not self.registrations_table_name:
            raise ValueError("REGISTRATIONS_TABLE environment variable not set")
        
        self.registrations_table = self.dynamodb.Table(self.registrations_table_name)
    
    def get_job_type(self) -> JobType:
        """Get the job type."""
        return JobType.REGISTRATION_CLEANUP
    
    def execute_job_logic(self, execution: JobExecution) -> JobResult:
        """Execute registration cleanup logic."""
        try:
            config = RegistrationCleanupConfig(**execution.configuration)
            
            execution.add_log_entry("INFO", "Starting registration cleanup", {
                'max_age_hours': config.max_age_hours,
                'batch_size': config.batch_size,
                'dry_run': config.dry_run
            })
            
            # Find expired registrations
            expired_registrations = self._find_expired_registrations(config.max_age_hours)
            
            execution.add_log_entry("INFO", f"Found {len(expired_registrations)} expired registrations")
            
            # Find abandoned registrations
            abandoned_registrations = self._find_abandoned_registrations(config.max_age_hours)
            
            execution.add_log_entry("INFO", f"Found {len(abandoned_registrations)} abandoned registrations")
            
            # Combine and deduplicate
            all_registrations = self._deduplicate_registrations(
                expired_registrations + abandoned_registrations
            )
            
            execution.add_log_entry("INFO", f"Total registrations to cleanup: {len(all_registrations)}")
            
            # Process in batches
            total_processed = 0
            total_succeeded = 0
            total_failed = 0
            
            for i in range(0, len(all_registrations), config.batch_size):
                batch = all_registrations[i:i + config.batch_size]
                
                batch_results = self._process_cleanup_batch(batch, config.dry_run)
                
                total_processed += len(batch)
                total_succeeded += batch_results['succeeded']
                total_failed += batch_results['failed']
                
                execution.add_log_entry("INFO", f"Processed batch {i//config.batch_size + 1}", {
                    'batch_size': len(batch),
                    'succeeded': batch_results['succeeded'],
                    'failed': batch_results['failed']
                })
                
                # Update execution progress
                execution.items_processed = total_processed
                execution.items_succeeded = total_succeeded
                execution.items_failed = total_failed
            
            # Prepare result
            success = total_failed == 0
            message = f"Cleanup completed: {total_succeeded} succeeded, {total_failed} failed"
            
            if config.dry_run:
                message = f"Dry run completed: {total_processed} registrations would be cleaned up"
            
            return JobResult(
                success=success,
                execution_id=execution.execution_id,
                job_type=self.get_job_type(),
                items_processed=total_processed,
                items_succeeded=total_succeeded,
                items_failed=total_failed,
                duration_seconds=execution.get_duration_seconds(),
                message=message,
                details={
                    'expired_count': len(expired_registrations),
                    'abandoned_count': len(abandoned_registrations),
                    'total_count': len(all_registrations),
                    'dry_run': config.dry_run
                }
            )
            
        except Exception as e:
            execution.add_log_entry("ERROR", f"Cleanup failed: {str(e)}")
            raise
    
    def _find_expired_registrations(self, max_age_hours: int) -> List[Dict[str, Any]]:
        """Find registrations that have expired."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            cutoff_iso = cutoff_time.isoformat()
            
            response = self.registrations_table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('expires_at').lt(cutoff_iso)
            )
            
            return response.get('Items', [])
            
        except ClientError as e:
            lambda_logger.error("Failed to find expired registrations", extra={
                'error': str(e)
            })
            raise
    
    def _find_abandoned_registrations(self, max_age_hours: int) -> List[Dict[str, Any]]:
        """Find registrations that appear to be abandoned."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            cutoff_iso = cutoff_time.isoformat()
            
            # Find registrations in intermediate states that are old
            abandoned_states = ['INITIATED', 'EMAIL_SENT', 'EMAIL_VERIFIED', 'PLAN_SELECTED']
            
            response = self.registrations_table.scan(
                FilterExpression=(
                    boto3.dynamodb.conditions.Attr('created_at').lt(cutoff_iso) &
                    boto3.dynamodb.conditions.Attr('state').is_in(abandoned_states)
                )
            )
            
            return response.get('Items', [])
            
        except ClientError as e:
            lambda_logger.error("Failed to find abandoned registrations", extra={
                'error': str(e)
            })
            raise
    
    def _deduplicate_registrations(self, registrations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate registrations based on registration_id."""
        seen_ids = set()
        unique_registrations = []
        
        for registration in registrations:
            registration_id = registration.get('registration_id')
            if registration_id and registration_id not in seen_ids:
                seen_ids.add(registration_id)
                unique_registrations.append(registration)
        
        return unique_registrations
    
    def _process_cleanup_batch(self, batch: List[Dict[str, Any]], dry_run: bool) -> Dict[str, int]:
        """Process a batch of registrations for cleanup."""
        succeeded = 0
        failed = 0
        
        for registration in batch:
            try:
                registration_id = registration.get('registration_id')
                state = registration.get('state')
                
                lambda_logger.info("Processing registration cleanup", extra={
                    'registration_id': registration_id,
                    'state': state,
                    'dry_run': dry_run
                })
                
                if not dry_run:
                    # Perform cleanup actions
                    self._cleanup_registration_resources(registration)
                    self._delete_registration_record(registration)
                
                succeeded += 1
                
            except Exception as e:
                lambda_logger.error("Failed to cleanup registration", extra={
                    'registration_id': registration.get('registration_id'),
                    'error': str(e)
                })
                failed += 1
        
        return {'succeeded': succeeded, 'failed': failed}
    
    def _cleanup_registration_resources(self, registration: Dict[str, Any]) -> None:
        """Cleanup resources associated with a registration."""
        try:
            registration_id = registration.get('registration_id')
            tenant_id = registration.get('tenant_id')
            user_id = registration.get('user_id')
            
            # If tenant was created, we might need to clean it up
            if tenant_id:
                lambda_logger.info("Registration has tenant - considering cleanup", extra={
                    'registration_id': registration_id,
                    'tenant_id': tenant_id
                })
                
                # For now, just log - in a real implementation, you might:
                # - Check if tenant has any activity
                # - Delete tenant if it's truly abandoned
                # - Clean up any partial resources
            
            # If user was created, we might need to clean it up
            if user_id:
                lambda_logger.info("Registration has user - considering cleanup", extra={
                    'registration_id': registration_id,
                    'user_id': user_id
                })
                
                # For now, just log - in a real implementation, you might:
                # - Delete user account if no activity
                # - Clean up user-related resources
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup registration resources", extra={
                'registration_id': registration.get('registration_id'),
                'error': str(e)
            })
            raise
    
    def _delete_registration_record(self, registration: Dict[str, Any]) -> None:
        """Delete the registration record from DynamoDB."""
        try:
            registration_id = registration.get('registration_id')
            
            self.registrations_table.delete_item(
                Key={'registration_id': registration_id}
            )
            
            lambda_logger.info("Registration record deleted", extra={
                'registration_id': registration_id
            })
            
        except ClientError as e:
            lambda_logger.error("Failed to delete registration record", extra={
                'registration_id': registration.get('registration_id'),
                'error': str(e)
            })
            raise
