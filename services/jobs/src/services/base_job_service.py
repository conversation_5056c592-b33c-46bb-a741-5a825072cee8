#!/usr/bin/env python3
# services/jobs/src/services/base_job_service.py
# Base job service for common functionality

"""
Base job service providing common functionality for all scheduled jobs.
Handles logging, metrics, error handling, and service communication.
"""

import os
import uuid
import boto3
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime
from abc import ABC, abstractmethod

from shared.logger import lambda_logger, audit_log
from shared.exceptions import ExternalServiceException, ValidationException
from ..models.job_models import JobExecution, JobStatus, JobResult, JobType


class BaseJobService(ABC):
    """Base class for all job services."""
    
    def __init__(self):
        self.dynamodb = boto3.resource('dynamodb')
        self.ses_client = boto3.client('ses')
        self.cloudwatch = boto3.client('cloudwatch')
        
        # Service endpoints
        self.orchestrator_url = os.environ.get('ORCHESTRATOR_SERVICE_URL', '').rstrip('/')
        self.payment_url = os.environ.get('PAYMENT_SERVICE_URL', '').rstrip('/')
        self.tenant_url = os.environ.get('TENANT_SERVICE_URL', '').rstrip('/')
        
        # Configuration
        self.region = os.environ.get('REGION', 'us-east-1')
        self.stage = os.environ.get('STAGE', 'dev')
        self.email_from = os.environ.get('EMAIL_FROM', '<EMAIL>')
    
    @abstractmethod
    def get_job_type(self) -> JobType:
        """Get the job type for this service."""
        pass
    
    @abstractmethod
    def execute_job_logic(self, execution: JobExecution) -> JobResult:
        """Execute the specific job logic."""
        pass
    
    def execute_job(self, configuration: Optional[Dict[str, Any]] = None) -> JobResult:
        """
        Execute the job with proper logging and error handling.
        
        Args:
            configuration: Job configuration parameters
            
        Returns:
            JobResult with execution details
        """
        execution_id = f"{self.get_job_type().value.lower()}_{uuid.uuid4().hex[:8]}"
        execution = JobExecution(
            job_type=self.get_job_type(),
            execution_id=execution_id,
            status=JobStatus.STARTED,
            started_at=datetime.utcnow(),
            configuration=configuration
        )
        
        try:
            lambda_logger.info("Starting job execution", extra={
                'job_type': self.get_job_type().value,
                'execution_id': execution_id,
                'configuration': configuration
            })
            
            execution.add_log_entry("INFO", "Job execution started", {
                'configuration': configuration
            })
            
            # Update status to in progress
            execution.status = JobStatus.IN_PROGRESS
            
            # Execute the specific job logic
            result = self.execute_job_logic(execution)
            
            # Update execution with results
            execution.status = JobStatus.COMPLETED if result.success else JobStatus.FAILED
            execution.completed_at = datetime.utcnow()
            execution.items_processed = result.items_processed
            execution.items_succeeded = result.items_succeeded
            execution.items_failed = result.items_failed
            execution.results = result.details
            
            if not result.success:
                execution.error_message = result.message
            
            execution.add_log_entry(
                "INFO" if result.success else "ERROR",
                f"Job execution {'completed' if result.success else 'failed'}: {result.message}",
                {
                    'items_processed': result.items_processed,
                    'items_succeeded': result.items_succeeded,
                    'items_failed': result.items_failed,
                    'duration_seconds': execution.get_duration_seconds()
                }
            )
            
            # Send metrics to CloudWatch
            self._send_metrics(execution, result)
            
            # Audit log
            audit_log("job_executed", {
                'job_type': self.get_job_type().value,
                'execution_id': execution_id,
                'success': result.success,
                'items_processed': result.items_processed,
                'duration_seconds': execution.get_duration_seconds()
            })
            
            lambda_logger.info("Job execution completed", extra={
                'job_type': self.get_job_type().value,
                'execution_id': execution_id,
                'success': result.success,
                'items_processed': result.items_processed,
                'duration_seconds': execution.get_duration_seconds()
            })
            
            return result
            
        except Exception as e:
            # Handle unexpected errors
            execution.status = JobStatus.FAILED
            execution.completed_at = datetime.utcnow()
            execution.error_message = str(e)
            execution.error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
            
            execution.add_log_entry("ERROR", f"Job execution failed with exception: {str(e)}", {
                'error_type': type(e).__name__,
                'duration_seconds': execution.get_duration_seconds()
            })
            
            # Send failure metrics
            self._send_failure_metrics(execution, str(e))
            
            lambda_logger.error("Job execution failed", extra={
                'job_type': self.get_job_type().value,
                'execution_id': execution_id,
                'error': str(e),
                'error_type': type(e).__name__,
                'duration_seconds': execution.get_duration_seconds()
            })
            
            # Return failure result
            return JobResult(
                success=False,
                execution_id=execution_id,
                job_type=self.get_job_type(),
                items_processed=execution.items_processed,
                items_succeeded=execution.items_succeeded,
                items_failed=execution.items_failed,
                duration_seconds=execution.get_duration_seconds(),
                message=f"Job failed with exception: {str(e)}",
                details=execution.error_details
            )
    
    def _send_metrics(self, execution: JobExecution, result: JobResult) -> None:
        """Send metrics to CloudWatch."""
        try:
            job_type = self.get_job_type().value
            
            # Basic metrics
            metrics = [
                {
                    'MetricName': f'{job_type}_Executions',
                    'Value': 1,
                    'Unit': 'Count'
                },
                {
                    'MetricName': f'{job_type}_ItemsProcessed',
                    'Value': result.items_processed,
                    'Unit': 'Count'
                },
                {
                    'MetricName': f'{job_type}_ItemsSucceeded',
                    'Value': result.items_succeeded,
                    'Unit': 'Count'
                },
                {
                    'MetricName': f'{job_type}_ItemsFailed',
                    'Value': result.items_failed,
                    'Unit': 'Count'
                }
            ]
            
            # Duration metric
            if result.duration_seconds:
                metrics.append({
                    'MetricName': f'{job_type}_Duration',
                    'Value': result.duration_seconds,
                    'Unit': 'Seconds'
                })
            
            # Success rate metric
            if result.items_processed > 0:
                success_rate = (result.items_succeeded / result.items_processed) * 100
                metrics.append({
                    'MetricName': f'{job_type}_SuccessRate',
                    'Value': success_rate,
                    'Unit': 'Percent'
                })
            
            # Send metrics
            self.cloudwatch.put_metric_data(
                Namespace='Jobs/Scheduled',
                MetricData=metrics
            )
            
        except Exception as e:
            lambda_logger.warning("Failed to send metrics", extra={
                'job_type': self.get_job_type().value,
                'execution_id': execution.execution_id,
                'error': str(e)
            })
    
    def _send_failure_metrics(self, execution: JobExecution, error_message: str) -> None:
        """Send failure metrics to CloudWatch."""
        try:
            job_type = self.get_job_type().value
            
            metrics = [
                {
                    'MetricName': f'{job_type}_Failures',
                    'Value': 1,
                    'Unit': 'Count'
                }
            ]
            
            if execution.get_duration_seconds():
                metrics.append({
                    'MetricName': f'{job_type}_FailureDuration',
                    'Value': execution.get_duration_seconds(),
                    'Unit': 'Seconds'
                })
            
            self.cloudwatch.put_metric_data(
                Namespace='Jobs/Scheduled',
                MetricData=metrics
            )
            
        except Exception as e:
            lambda_logger.warning("Failed to send failure metrics", extra={
                'job_type': self.get_job_type().value,
                'execution_id': execution.execution_id,
                'error': str(e)
            })
    
    def make_service_request(self, service_url: str, endpoint: str, method: str = 'GET',
                           data: Optional[Dict[str, Any]] = None, timeout: int = 30) -> Dict[str, Any]:
        """Make HTTP request to another service."""
        try:
            url = f"{service_url}{endpoint}"
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': f'jobs-service/{self.stage}'
            }
            
            lambda_logger.info("Making service request", extra={
                'url': url,
                'method': method
            })
            
            response = requests.request(
                method=method,
                url=url,
                json=data,
                headers=headers,
                timeout=timeout
            )
            
            if response.status_code >= 400:
                raise ExternalServiceException(
                    f"Service request failed: {response.status_code} - {response.text}"
                )
            
            return response.json()
            
        except requests.exceptions.Timeout:
            raise ExternalServiceException("Service request timeout")
        except requests.exceptions.ConnectionError:
            raise ExternalServiceException("Service connection error")
        except Exception as e:
            raise ExternalServiceException(f"Service request failed: {str(e)}")
    
    def send_email_notification(self, recipient: str, subject: str, body_html: str,
                              body_text: Optional[str] = None) -> None:
        """Send email notification using SES."""
        try:
            destination = {'ToAddresses': [recipient]}
            
            message = {
                'Subject': {'Data': subject, 'Charset': 'UTF-8'},
                'Body': {
                    'Html': {'Data': body_html, 'Charset': 'UTF-8'}
                }
            }
            
            if body_text:
                message['Body']['Text'] = {'Data': body_text, 'Charset': 'UTF-8'}
            
            self.ses_client.send_email(
                Source=self.email_from,
                Destination=destination,
                Message=message
            )
            
            lambda_logger.info("Email notification sent", extra={
                'recipient': recipient,
                'subject': subject
            })
            
        except Exception as e:
            lambda_logger.error("Failed to send email notification", extra={
                'recipient': recipient,
                'subject': subject,
                'error': str(e)
            })
            raise
