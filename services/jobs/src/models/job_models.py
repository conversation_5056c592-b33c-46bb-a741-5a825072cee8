#!/usr/bin/env python3
# services/jobs/src/models/job_models.py
# Job data models

"""
Job data models for scheduled tasks and background processing.
Defines the data structures used throughout the jobs service.
"""

from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class JobType(Enum):
    """Job type enumeration."""
    REGISTRATION_CLEANUP = "REGISTRATION_CLEANUP"
    PAYMENT_VALIDATION = "PAYMENT_VALIDATION"
    PAYMENT_REMINDERS = "PAYMENT_REMINDERS"
    TENANT_SUSPENSION = "TENANT_SUSPENSION"
    DATA_CLEANUP = "DATA_CLEANUP"


class JobStatus(Enum):
    """Job execution status."""
    STARTED = "STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"


@dataclass
class JobExecution:
    """Job execution record."""
    job_type: JobType
    execution_id: str
    status: JobStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    
    # Execution details
    items_processed: int = 0
    items_succeeded: int = 0
    items_failed: int = 0
    
    # Configuration
    configuration: Optional[Dict[str, Any]] = None
    
    # Results and errors
    results: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Execution log
    execution_log: Optional[List[Dict[str, Any]]] = None
    
    def __post_init__(self):
        if self.execution_log is None:
            self.execution_log = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['job_type'] = self.job_type.value
        data['status'] = self.status.value
        data['started_at'] = self.started_at.isoformat()
        if self.completed_at:
            data['completed_at'] = self.completed_at.isoformat()
        return data
    
    def add_log_entry(self, level: str, message: str, details: Optional[Dict[str, Any]] = None) -> None:
        """Add entry to execution log."""
        if self.execution_log is None:
            self.execution_log = []
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level,
            'message': message,
            'details': details or {}
        }
        self.execution_log.append(log_entry)
    
    def get_duration_seconds(self) -> Optional[float]:
        """Get execution duration in seconds."""
        if self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def get_success_rate(self) -> float:
        """Get success rate as percentage."""
        if self.items_processed == 0:
            return 0.0
        return (self.items_succeeded / self.items_processed) * 100.0


@dataclass
class RegistrationCleanupConfig:
    """Configuration for registration cleanup job."""
    max_age_hours: int = 1
    batch_size: int = 100
    dry_run: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PaymentValidationConfig:
    """Configuration for payment validation job."""
    check_overdue: bool = True
    send_notifications: bool = True
    grace_period_days: int = 3
    batch_size: int = 50
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PaymentRemindersConfig:
    """Configuration for payment reminders job."""
    reminder_days: List[int] = None  # Days before due date
    overdue_days: List[int] = None   # Days after due date
    batch_size: int = 50
    
    def __post_init__(self):
        if self.reminder_days is None:
            self.reminder_days = [7, 3, 1]
        if self.overdue_days is None:
            self.overdue_days = [1, 7, 14]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class JobResult:
    """Job execution result."""
    success: bool
    execution_id: str
    job_type: JobType
    items_processed: int
    items_succeeded: int
    items_failed: int
    duration_seconds: Optional[float]
    message: str
    details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['job_type'] = self.job_type.value
        return data


@dataclass
class EmailNotification:
    """Email notification data."""
    recipient_email: str
    subject: str
    template_name: str
    template_data: Dict[str, Any]
    priority: str = "normal"  # low, normal, high
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


# Job configuration defaults
DEFAULT_CONFIGS = {
    JobType.REGISTRATION_CLEANUP: RegistrationCleanupConfig(),
    JobType.PAYMENT_VALIDATION: PaymentValidationConfig(),
    JobType.PAYMENT_REMINDERS: PaymentRemindersConfig()
}


def get_default_config(job_type: JobType) -> Dict[str, Any]:
    """Get default configuration for job type."""
    config = DEFAULT_CONFIGS.get(job_type)
    return config.to_dict() if config else {}


def create_job_execution(job_type: JobType, execution_id: str, 
                        configuration: Optional[Dict[str, Any]] = None) -> JobExecution:
    """Create a new job execution record."""
    return JobExecution(
        job_type=job_type,
        execution_id=execution_id,
        status=JobStatus.STARTED,
        started_at=datetime.utcnow(),
        configuration=configuration or get_default_config(job_type)
    )
