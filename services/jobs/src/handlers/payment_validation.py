#!/usr/bin/env python3
# services/jobs/src/handlers/payment_validation.py
# Payment validation handler

"""
Payment validation handler for scheduled validation of subscription payments.
Triggered by CloudWatch Events on a schedule.
"""

from typing import Any, Dict, Optional

from shared.logger import lambda_logger
from shared.metrics import measure_performance
from ..services.payment_validation_service import PaymentValidationService


@measure_performance("jobs_payment_validation")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Payment validation handler.
    
    Triggered by CloudWatch Events schedule.
    Event format:
    {
        "job_type": "payment_validation",
        "check_overdue": true,
        "send_notifications": true
    }
    """
    
    try:
        lambda_logger.info("Payment validation job started", extra={
            'event': event,
            'context': context.function_name if context else None
        })
        
        # Extract configuration from event
        config_data = {
            'check_overdue': event.get('check_overdue', True),
            'send_notifications': event.get('send_notifications', True),
            'grace_period_days': event.get('grace_period_days', 3),
            'batch_size': event.get('batch_size', 50)
        }
        
        # Validate configuration
        if config_data['grace_period_days'] < 0:
            raise ValueError("grace_period_days must be non-negative")
        
        if config_data['batch_size'] <= 0:
            raise ValueError("batch_size must be positive")
        
        lambda_logger.info("Starting payment validation with configuration", extra={
            'configuration': config_data
        })
        
        # Execute validation job
        validation_service = PaymentValidationService()
        result = validation_service.execute_job(config_data)
        
        # Prepare response
        response = {
            'success': result.success,
            'job_type': result.job_type.value,
            'execution_id': result.execution_id,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds,
            'message': result.message,
            'details': result.details
        }
        
        lambda_logger.info("Payment validation job completed", extra={
            'success': result.success,
            'execution_id': result.execution_id,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds,
            'overdue_count': result.details.get('overdue_count', 0) if result.details else 0
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("Payment validation job failed", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'event': event
        })
        
        # Return error response
        return {
            'success': False,
            'job_type': 'PAYMENT_VALIDATION',
            'execution_id': 'unknown',
            'items_processed': 0,
            'items_succeeded': 0,
            'items_failed': 0,
            'duration_seconds': None,
            'message': f"Job failed: {str(e)}",
            'error': {
                'type': type(e).__name__,
                'message': str(e)
            }
        }
