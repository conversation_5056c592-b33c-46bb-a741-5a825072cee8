#!/usr/bin/env python3
# services/jobs/src/handlers/payment_reminders.py
# Payment reminders handler

"""
Payment reminders handler for scheduled sending of payment due and overdue notifications.
Triggered by CloudWatch Events on a schedule.
"""

from typing import Any, Dict, Optional

from shared.logger import lambda_logger
from shared.metrics import measure_performance
from ..services.payment_reminders_service import PaymentRemindersService


@measure_performance("jobs_payment_reminders")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Payment reminders handler.
    
    Triggered by CloudWatch Events schedule.
    Event format:
    {
        "job_type": "payment_reminders",
        "reminder_days": [7, 3, 1],
        "overdue_days": [1, 7, 14]
    }
    """
    
    try:
        lambda_logger.info("Payment reminders job started", extra={
            'event': event,
            'context': context.function_name if context else None
        })
        
        # Extract configuration from event
        config_data = {
            'reminder_days': event.get('reminder_days', [7, 3, 1]),
            'overdue_days': event.get('overdue_days', [1, 7, 14]),
            'batch_size': event.get('batch_size', 50)
        }
        
        # Validate configuration
        if not isinstance(config_data['reminder_days'], list):
            raise ValueError("reminder_days must be a list")
        
        if not isinstance(config_data['overdue_days'], list):
            raise ValueError("overdue_days must be a list")
        
        if config_data['batch_size'] <= 0:
            raise ValueError("batch_size must be positive")
        
        # Validate day values
        for day in config_data['reminder_days'] + config_data['overdue_days']:
            if not isinstance(day, int) or day < 0:
                raise ValueError("All day values must be non-negative integers")
        
        lambda_logger.info("Starting payment reminders with configuration", extra={
            'configuration': config_data
        })
        
        # Execute reminders job
        reminders_service = PaymentRemindersService()
        result = reminders_service.execute_job(config_data)
        
        # Prepare response
        response = {
            'success': result.success,
            'job_type': result.job_type.value,
            'execution_id': result.execution_id,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds,
            'message': result.message,
            'details': result.details
        }
        
        lambda_logger.info("Payment reminders job completed", extra={
            'success': result.success,
            'execution_id': result.execution_id,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds,
            'reminders_sent': result.details.get('reminders_sent', 0) if result.details else 0
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("Payment reminders job failed", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'event': event
        })
        
        # Return error response
        return {
            'success': False,
            'job_type': 'PAYMENT_REMINDERS',
            'execution_id': 'unknown',
            'items_processed': 0,
            'items_succeeded': 0,
            'items_failed': 0,
            'duration_seconds': None,
            'message': f"Job failed: {str(e)}",
            'error': {
                'type': type(e).__name__,
                'message': str(e)
            }
        }
