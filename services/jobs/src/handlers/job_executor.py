#!/usr/bin/env python3
# services/jobs/src/handlers/job_executor.py
# Manual job executor handler

"""
Manual job executor handler for triggering jobs on demand.
Allows manual execution of scheduled jobs for testing and emergency situations.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException
from shared.validators import validate_required_fields

from ..models.job_models import JobType
from ..services.registration_cleanup_service import RegistrationCleanupService
from ..services.payment_validation_service import PaymentValidationService
from ..services.payment_reminders_service import PaymentRemindersService


# Job service mapping
JOB_SERVICES = {
    JobType.REGISTRATION_CLEANUP: RegistrationCleanupService,
    JobType.PAYMENT_VALIDATION: PaymentValidationService,
    JobType.PAYMENT_REMINDERS: PaymentRemindersService
}


def validate_job_request(data: Dict[str, Any]) -> None:
    """Validate job execution request."""
    validate_required_fields(data, ['job_type'])
    
    job_type_str = data.get('job_type', '')
    
    # Validate job type
    try:
        JobType(job_type_str)
    except ValueError:
        valid_types = [jt.value for jt in JobType]
        raise ValidationException(f"Invalid job_type. Valid types: {valid_types}")
    
    # Validate configuration if provided
    configuration = data.get('configuration', {})
    if configuration and not isinstance(configuration, dict):
        raise ValidationException("Configuration must be a dictionary")


@rate_limit(requests_per_minute=10)  # Moderate rate limiting for manual execution
@measure_performance("jobs_manual_execution")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manual job executor handler.
    
    POST /jobs/execute
    {
        "job_type": "REGISTRATION_CLEANUP",
        "configuration": {
            "max_age_hours": 2,
            "batch_size": 50,
            "dry_run": true
        }
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "job_executor")
        
        # Parse request body
        body = event.get('body', '{}')
        if isinstance(body, str):
            data = json.loads(body)
        else:
            data = body
        
        job_type_str = data.get('job_type')
        configuration = data.get('configuration', {})
        
        lambda_logger.info("Processing manual job execution request", extra={
            'request_id': request_id,
            'job_type': job_type_str,
            'configuration': configuration
        })
        
        # Validate request
        validate_job_request(data)
        
        # Get job type enum
        job_type = JobType(job_type_str)
        
        # Get job service class
        service_class = JOB_SERVICES.get(job_type)
        if not service_class:
            raise ValidationException(f"Job type {job_type_str} is not supported for manual execution")
        
        # Execute job
        lambda_logger.info("Executing job manually", extra={
            'job_type': job_type_str,
            'service_class': service_class.__name__,
            'configuration': configuration
        })
        
        job_service = service_class()
        result = job_service.execute_job(configuration)
        
        # Prepare response data
        response_data = {
            'execution_id': result.execution_id,
            'job_type': result.job_type.value,
            'success': result.success,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds,
            'message': result.message,
            'details': result.details,
            'executed_manually': True,
            'executed_at': lambda_logger.get_timestamp()
        }
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Job executed successfully"
        )
        
        # Log API response
        log_api_response(response, "job_executor")
        
        lambda_logger.info("Manual job execution completed", extra={
            'request_id': request_id,
            'job_type': job_type_str,
            'execution_id': result.execution_id,
            'success': result.success,
            'items_processed': result.items_processed,
            'duration_seconds': result.duration_seconds
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Job execution validation failed", extra={
            'request_id': request_id,
            'job_type': data.get('job_type') if 'data' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "job_executor")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Job execution platform error", extra={
            'request_id': request_id,
            'job_type': data.get('job_type') if 'data' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Job execution failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "job_executor")
        return response
        
    except Exception as e:
        lambda_logger.error("Job execution unexpected error", extra={
            'request_id': request_id,
            'job_type': data.get('job_type') if 'data' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "job_executor")
        return response
