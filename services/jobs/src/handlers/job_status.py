#!/usr/bin/env python3
# services/jobs/src/handlers/job_status.py
# Job status handler

"""
Job status handler for monitoring scheduled jobs and their execution history.
Provides insights into job performance and execution statistics.
"""

import boto3
from typing import Any, Dict, Optional
from datetime import datetime, timed<PERSON>ta

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import PlatformException

from ..models.job_models import JobType


@rate_limit(requests_per_minute=30)  # Higher limit for status checks
@measure_performance("jobs_status")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Job status handler.
    
    GET /jobs/status
    GET /jobs/status?job_type=REGISTRATION_CLEANUP
    GET /jobs/status?hours=24
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "job_status")
        
        # Extract query parameters
        query_params = event.get('queryStringParameters') or {}
        job_type_filter = query_params.get('job_type')
        hours_filter = int(query_params.get('hours', 24))
        
        lambda_logger.info("Getting job status", extra={
            'request_id': request_id,
            'job_type_filter': job_type_filter,
            'hours_filter': hours_filter
        })
        
        # Validate job type filter if provided
        if job_type_filter:
            try:
                JobType(job_type_filter)
            except ValueError:
                valid_types = [jt.value for jt in JobType]
                return APIResponse.error(
                    message="Invalid job_type parameter",
                    details={'valid_types': valid_types},
                    status_code=400
                )
        
        # Get job statistics
        status_data = _get_job_status(job_type_filter, hours_filter)
        
        # Create success response
        response = APIResponse.success(
            data=status_data,
            message="Job status retrieved successfully"
        )
        
        # Log API response
        log_api_response(response, "job_status")
        
        lambda_logger.info("Job status retrieved", extra={
            'request_id': request_id,
            'job_type_filter': job_type_filter,
            'hours_filter': hours_filter,
            'total_executions': status_data.get('summary', {}).get('total_executions', 0)
        })
        
        return response
        
    except PlatformException as e:
        lambda_logger.error("Job status platform error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to get job status",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "job_status")
        return response
        
    except Exception as e:
        lambda_logger.error("Job status unexpected error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "job_status")
        return response


def _get_job_status(job_type_filter: Optional[str], hours_filter: int) -> Dict[str, Any]:
    """Get job status and statistics."""
    try:
        # Get CloudWatch metrics for job executions
        cloudwatch = boto3.client('cloudwatch')
        
        # Calculate time range
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours_filter)
        
        # Get metrics for each job type
        job_types = [job_type_filter] if job_type_filter else [jt.value for jt in JobType]
        
        job_statistics = {}
        total_executions = 0
        total_successes = 0
        total_failures = 0
        
        for job_type in job_types:
            try:
                # Get execution metrics
                executions_response = cloudwatch.get_metric_statistics(
                    Namespace='Jobs/Scheduled',
                    MetricName=f'{job_type}_Executions',
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,  # 1 hour periods
                    Statistics=['Sum']
                )
                
                # Get failure metrics
                failures_response = cloudwatch.get_metric_statistics(
                    Namespace='Jobs/Scheduled',
                    MetricName=f'{job_type}_Failures',
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,
                    Statistics=['Sum']
                )
                
                # Get duration metrics
                duration_response = cloudwatch.get_metric_statistics(
                    Namespace='Jobs/Scheduled',
                    MetricName=f'{job_type}_Duration',
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,
                    Statistics=['Average', 'Maximum']
                )
                
                # Calculate statistics
                executions = sum(point['Sum'] for point in executions_response['Datapoints'])
                failures = sum(point['Sum'] for point in failures_response['Datapoints'])
                successes = executions - failures
                
                avg_duration = 0
                max_duration = 0
                if duration_response['Datapoints']:
                    avg_duration = sum(point['Average'] for point in duration_response['Datapoints']) / len(duration_response['Datapoints'])
                    max_duration = max(point['Maximum'] for point in duration_response['Datapoints'])
                
                success_rate = (successes / executions * 100) if executions > 0 else 0
                
                job_statistics[job_type] = {
                    'executions': int(executions),
                    'successes': int(successes),
                    'failures': int(failures),
                    'success_rate': round(success_rate, 2),
                    'avg_duration_seconds': round(avg_duration, 2),
                    'max_duration_seconds': round(max_duration, 2),
                    'last_execution': _get_last_execution_time(job_type, hours_filter)
                }
                
                total_executions += executions
                total_successes += successes
                total_failures += failures
                
            except Exception as e:
                lambda_logger.warning(f"Failed to get metrics for {job_type}", extra={
                    'job_type': job_type,
                    'error': str(e)
                })
                
                job_statistics[job_type] = {
                    'executions': 0,
                    'successes': 0,
                    'failures': 0,
                    'success_rate': 0,
                    'avg_duration_seconds': 0,
                    'max_duration_seconds': 0,
                    'last_execution': None,
                    'error': str(e)
                }
        
        # Calculate overall statistics
        overall_success_rate = (total_successes / total_executions * 100) if total_executions > 0 else 0
        
        return {
            'summary': {
                'time_range_hours': hours_filter,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'total_executions': int(total_executions),
                'total_successes': int(total_successes),
                'total_failures': int(total_failures),
                'overall_success_rate': round(overall_success_rate, 2)
            },
            'job_types': job_statistics,
            'health_status': _get_health_status(job_statistics),
            'next_scheduled_runs': _get_next_scheduled_runs()
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get job status", extra={
            'error': str(e)
        })
        raise


def _get_last_execution_time(job_type: str, hours_filter: int) -> Optional[str]:
    """Get the last execution time for a job type."""
    try:
        # In a real implementation, this would query CloudWatch Logs or a database
        # For now, return a mock timestamp
        return (datetime.utcnow() - timedelta(hours=1)).isoformat()
        
    except Exception as e:
        lambda_logger.warning(f"Failed to get last execution time for {job_type}", extra={
            'job_type': job_type,
            'error': str(e)
        })
        return None


def _get_health_status(job_statistics: Dict[str, Any]) -> Dict[str, Any]:
    """Determine overall health status of jobs."""
    try:
        total_jobs = len(job_statistics)
        healthy_jobs = 0
        warning_jobs = 0
        critical_jobs = 0
        
        for job_type, stats in job_statistics.items():
            success_rate = stats.get('success_rate', 0)
            executions = stats.get('executions', 0)
            
            if executions == 0:
                warning_jobs += 1  # No recent executions
            elif success_rate >= 95:
                healthy_jobs += 1
            elif success_rate >= 80:
                warning_jobs += 1
            else:
                critical_jobs += 1
        
        # Determine overall status
        if critical_jobs > 0:
            overall_status = 'critical'
        elif warning_jobs > 0:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'
        
        return {
            'overall_status': overall_status,
            'healthy_jobs': healthy_jobs,
            'warning_jobs': warning_jobs,
            'critical_jobs': critical_jobs,
            'total_jobs': total_jobs
        }
        
    except Exception as e:
        lambda_logger.error("Failed to determine health status", extra={
            'error': str(e)
        })
        return {
            'overall_status': 'unknown',
            'error': str(e)
        }


def _get_next_scheduled_runs() -> Dict[str, str]:
    """Get next scheduled run times for jobs."""
    try:
        # Calculate next run times based on schedule
        now = datetime.utcnow()
        
        # Registration cleanup runs every hour
        next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        
        # Payment validation and reminders run daily at midnight UTC
        next_midnight = (now + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        
        return {
            'REGISTRATION_CLEANUP': next_hour.isoformat(),
            'PAYMENT_VALIDATION': next_midnight.isoformat(),
            'PAYMENT_REMINDERS': next_midnight.isoformat()
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get next scheduled runs", extra={
            'error': str(e)
        })
        return {}
