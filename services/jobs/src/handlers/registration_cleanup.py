#!/usr/bin/env python3
# services/jobs/src/handlers/registration_cleanup.py
# Registration cleanup handler

"""
Registration cleanup handler for scheduled cleanup of abandoned registrations.
Triggered by CloudWatch Events on a schedule.
"""

from typing import Any, Dict, Optional

from shared.logger import lambda_logger
from shared.metrics import measure_performance
from ..services.registration_cleanup_service import RegistrationCleanupService
from ..models.job_models import RegistrationCleanupConfig


@measure_performance("jobs_registration_cleanup")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Registration cleanup handler.
    
    Triggered by CloudWatch Events schedule.
    Event format:
    {
        "job_type": "registration_cleanup",
        "max_age_hours": 1,
        "batch_size": 100
    }
    """
    
    try:
        lambda_logger.info("Registration cleanup job started", extra={
            'event': event,
            'context': context.function_name if context else None
        })
        
        # Extract configuration from event
        config_data = {
            'max_age_hours': event.get('max_age_hours', 1),
            'batch_size': event.get('batch_size', 100),
            'dry_run': event.get('dry_run', False)
        }
        
        # Validate configuration
        if config_data['max_age_hours'] < 0:
            raise ValueError("max_age_hours must be non-negative")
        
        if config_data['batch_size'] <= 0:
            raise ValueError("batch_size must be positive")
        
        lambda_logger.info("Starting registration cleanup with configuration", extra={
            'configuration': config_data
        })
        
        # Execute cleanup job
        cleanup_service = RegistrationCleanupService()
        result = cleanup_service.execute_job(config_data)
        
        # Prepare response
        response = {
            'success': result.success,
            'job_type': result.job_type.value,
            'execution_id': result.execution_id,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds,
            'message': result.message,
            'details': result.details
        }
        
        lambda_logger.info("Registration cleanup job completed", extra={
            'success': result.success,
            'execution_id': result.execution_id,
            'items_processed': result.items_processed,
            'items_succeeded': result.items_succeeded,
            'items_failed': result.items_failed,
            'duration_seconds': result.duration_seconds
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("Registration cleanup job failed", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'event': event
        })
        
        # Return error response
        return {
            'success': False,
            'job_type': 'REGISTRATION_CLEANUP',
            'execution_id': 'unknown',
            'items_processed': 0,
            'items_succeeded': 0,
            'items_failed': 0,
            'duration_seconds': None,
            'message': f"Job failed: {str(e)}",
            'error': {
                'type': type(e).__name__,
                'message': str(e)
            }
        }
