#!/usr/bin/env python3
# services/jobs/src/handlers/health.py
# Health check handler

"""
Health check handler for the jobs service.
Provides service health status and job execution diagnostics.
"""

import os
import boto3
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger


@rate_limit(requests_per_minute=60)  # Higher limit for health checks
@measure_performance("jobs_health")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Health check endpoint.
    
    GET /health
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    try:
        # Check environment variables
        required_env_vars = [
            'SUBSCRIPTIONS_TABLE',
            'REGISTRATIONS_TABLE',
            'REGION',
            'STAGE',
            'EMAIL_FROM'
        ]
        
        env_status = {}
        all_env_ok = True
        
        for var in required_env_vars:
            value = os.environ.get(var)
            env_status[var] = {
                'configured': value is not None,
                'value': value[:20] + '...' if value and len(value) > 20 else value
            }
            if not value:
                all_env_ok = False
        
        # Check DynamoDB table access
        dynamodb_status = {'accessible': False, 'tables': {}, 'error': None}
        try:
            dynamodb = boto3.resource('dynamodb')
            
            # Check subscriptions table
            if os.environ.get('SUBSCRIPTIONS_TABLE'):
                subscriptions_table = dynamodb.Table(os.environ.get('SUBSCRIPTIONS_TABLE'))
                table_desc = subscriptions_table.meta.client.describe_table(
                    TableName=subscriptions_table.name
                )
                dynamodb_status['tables']['subscriptions'] = {
                    'status': table_desc['Table']['TableStatus'],
                    'item_count': table_desc['Table'].get('ItemCount', 0)
                }
            
            # Check registrations table
            if os.environ.get('REGISTRATIONS_TABLE'):
                registrations_table = dynamodb.Table(os.environ.get('REGISTRATIONS_TABLE'))
                table_desc = registrations_table.meta.client.describe_table(
                    TableName=registrations_table.name
                )
                dynamodb_status['tables']['registrations'] = {
                    'status': table_desc['Table']['TableStatus'],
                    'item_count': table_desc['Table'].get('ItemCount', 0)
                }
            
            dynamodb_status['accessible'] = True
            
        except Exception as e:
            dynamodb_status['error'] = str(e)
        
        # Check SES access
        ses_status = {'accessible': False, 'error': None}
        try:
            ses_client = boto3.client('ses')
            # Try to get sending quota
            quota = ses_client.get_send_quota()
            ses_status['accessible'] = True
            ses_status['send_quota'] = {
                'max_24_hour': quota.get('Max24HourSend', 0),
                'max_send_rate': quota.get('MaxSendRate', 0),
                'sent_last_24_hours': quota.get('SentLast24Hours', 0)
            }
        except Exception as e:
            ses_status['error'] = str(e)
        
        # Check CloudWatch access
        cloudwatch_status = {'accessible': False, 'error': None}
        try:
            cloudwatch = boto3.client('cloudwatch')
            # Try to list metrics
            metrics = cloudwatch.list_metrics(
                Namespace='Jobs/Scheduled',
                MaxRecords=1
            )
            cloudwatch_status['accessible'] = True
            cloudwatch_status['metrics_count'] = len(metrics.get('Metrics', []))
        except Exception as e:
            cloudwatch_status['error'] = str(e)
        
        # Check recent job executions
        job_executions_status = _check_recent_job_executions()
        
        # Overall health status
        is_healthy = (
            all_env_ok and 
            dynamodb_status['accessible'] and 
            ses_status['accessible'] and 
            cloudwatch_status['accessible'] and
            job_executions_status['healthy']
        )
        
        # Prepare health data
        health_data = {
            'service': 'jobs',
            'status': 'healthy' if is_healthy else 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'environment': os.environ.get('STAGE', 'unknown'),
            'region': os.environ.get('REGION', 'unknown'),
            'checks': {
                'environment_variables': {
                    'status': 'pass' if all_env_ok else 'fail',
                    'details': env_status
                },
                'dynamodb': {
                    'status': 'pass' if dynamodb_status['accessible'] else 'fail',
                    'details': dynamodb_status
                },
                'ses': {
                    'status': 'pass' if ses_status['accessible'] else 'fail',
                    'details': ses_status
                },
                'cloudwatch': {
                    'status': 'pass' if cloudwatch_status['accessible'] else 'fail',
                    'details': cloudwatch_status
                },
                'job_executions': {
                    'status': 'pass' if job_executions_status['healthy'] else 'fail',
                    'details': job_executions_status
                }
            }
        }
        
        # Add context information if available
        if context:
            health_data['lambda'] = {
                'function_name': context.function_name,
                'function_version': context.function_version,
                'memory_limit': context.memory_limit_in_mb,
                'remaining_time': context.get_remaining_time_in_millis()
            }
        
        # Add scheduled jobs information
        health_data['scheduled_jobs'] = {
            'registration_cleanup': {
                'schedule': 'rate(1 hour)',
                'enabled': True,
                'description': 'Cleanup abandoned registrations'
            },
            'payment_validation': {
                'schedule': 'rate(1 day)',
                'enabled': True,
                'description': 'Validate subscription payments'
            },
            'payment_reminders': {
                'schedule': 'rate(1 day)',
                'enabled': True,
                'description': 'Send payment reminders'
            }
        }
        
        # Create response
        status_code = 200 if is_healthy else 503
        message = "Service is healthy" if is_healthy else "Service is unhealthy"
        
        response = APIResponse.success(
            data=health_data,
            message=message
        )
        
        # Override status code for unhealthy service
        if not is_healthy:
            response['statusCode'] = 503
        
        lambda_logger.info("Health check completed", extra={
            'status': health_data['status'],
            'all_env_ok': all_env_ok,
            'dynamodb_accessible': dynamodb_status['accessible'],
            'ses_accessible': ses_status['accessible'],
            'cloudwatch_accessible': cloudwatch_status['accessible'],
            'job_executions_healthy': job_executions_status['healthy']
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("Health check failed", extra={
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        # Return unhealthy status
        health_data = {
            'service': 'jobs',
            'status': 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }
        
        response = APIResponse.error(
            message="Health check failed",
            details=health_data,
            status_code=503
        )
        
        return response


def _check_recent_job_executions() -> Dict[str, Any]:
    """Check recent job executions for health assessment."""
    try:
        cloudwatch = boto3.client('cloudwatch')
        
        # Check for job executions in the last 24 hours
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=24)
        
        job_types = ['REGISTRATION_CLEANUP', 'PAYMENT_VALIDATION', 'PAYMENT_REMINDERS']
        execution_status = {}
        
        total_executions = 0
        total_failures = 0
        
        for job_type in job_types:
            try:
                # Get execution metrics
                executions_response = cloudwatch.get_metric_statistics(
                    Namespace='Jobs/Scheduled',
                    MetricName=f'{job_type}_Executions',
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=86400,  # 24 hours
                    Statistics=['Sum']
                )
                
                # Get failure metrics
                failures_response = cloudwatch.get_metric_statistics(
                    Namespace='Jobs/Scheduled',
                    MetricName=f'{job_type}_Failures',
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=86400,
                    Statistics=['Sum']
                )
                
                executions = sum(point['Sum'] for point in executions_response['Datapoints'])
                failures = sum(point['Sum'] for point in failures_response['Datapoints'])
                
                execution_status[job_type] = {
                    'executions': int(executions),
                    'failures': int(failures),
                    'success_rate': ((executions - failures) / executions * 100) if executions > 0 else 0
                }
                
                total_executions += executions
                total_failures += failures
                
            except Exception as e:
                execution_status[job_type] = {
                    'executions': 0,
                    'failures': 0,
                    'success_rate': 0,
                    'error': str(e)
                }
        
        # Determine if job executions are healthy
        overall_success_rate = ((total_executions - total_failures) / total_executions * 100) if total_executions > 0 else 100
        healthy = overall_success_rate >= 80  # 80% success rate threshold
        
        return {
            'healthy': healthy,
            'overall_success_rate': round(overall_success_rate, 2),
            'total_executions': int(total_executions),
            'total_failures': int(total_failures),
            'job_types': execution_status,
            'time_range': '24 hours'
        }
        
    except Exception as e:
        lambda_logger.error("Failed to check recent job executions", extra={
            'error': str(e)
        })
        return {
            'healthy': False,
            'error': str(e)
        }
