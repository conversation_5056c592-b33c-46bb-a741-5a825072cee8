sequenceDiagram
    participant CWE as ⏰ CloudWatch Events
    participant RCH as 🧹 Registration Cleanup Handler
    participant RCS as 🧹 Registration Cleanup Service
    participant BJS as 🔧 Base Job Service
    participant DB as 🗄️ DynamoDB
    participant OS as 🎯 Orchestrator Service
    participant AS as 🔐 Auth Service
    participant TS as 🏢 Tenant Service
    participant CW as 📊 CloudWatch

    Note over CWE: Hourly trigger (rate(1 hour))
    CWE->>RCH: Trigger registration cleanup job
    RCH->>RCH: Initialize job execution
    RCH->>BJS: create_job_execution(REGISTRATION_CLEANUP)
    
    BJS->>BJS: Generate execution_id
    BJS->>DB: Store job execution record
    Note over DB: PK: JOB_EXECUTION#{execution_id}<br/>status: STARTED<br/>job_type: REGISTRATION_CLEANUP
    
    BJS->>CW: Log job start metrics
    BJS-->>RCH: Job execution initialized
    
    RCH->>RCS: execute_job_logic(execution)
    RCS->>RCS: Load job configuration
    Note over RCS: max_age_hours: 1<br/>batch_size: 100<br/>dry_run: false
    
    RCS->>DB: Query expired registrations
    Note over DB: Query registrations older than 1 hour<br/>in intermediate states
    DB-->>RCS: List of expired registrations (batch 1)
    
    loop For each batch of expired registrations
        RCS->>RCS: Process batch of registrations
        
        loop For each registration in batch
            RCS->>RCS: Validate registration state
            
            alt Registration has tenant_id
                RCS->>TS: cleanup_tenant_resources(tenant_id)
                TS->>TS: Delete tenant data
                TS-->>RCS: Tenant resources cleaned
            end
            
            alt Registration has user_id
                RCS->>AS: cleanup_user_resources(user_id)
                AS->>AS: Delete user account
                AS-->>RCS: User resources cleaned
            end
            
            RCS->>DB: Delete registration record
            DB-->>RCS: Registration deleted
            
            RCS->>RCS: Update processing counters
        end
        
        RCS->>BJS: update_job_progress(execution_id, batch_results)
        BJS->>DB: Update job execution progress
        BJS->>CW: Send progress metrics
        
        RCS->>DB: Query next batch of expired registrations
        DB-->>RCS: Next batch or empty result
    end
    
    RCS->>RCS: Generate cleanup summary
    Note over RCS: items_processed: 45<br/>items_succeeded: 43<br/>items_failed: 2
    
    RCS-->>RCH: Job completed with results
    RCH->>BJS: complete_job_execution(execution_id, results)
    
    BJS->>DB: Update job execution status to COMPLETED
    BJS->>CW: Send completion metrics
    Note over CW: Custom metrics:<br/>- jobs.registrations.cleaned<br/>- jobs.execution.duration<br/>- jobs.items.processed
    
    BJS-->>RCH: Job execution completed
    RCH-->>CWE: Handler execution finished
    
    rect rgb(255, 245, 245)
        Note over RCS: Error handling scenario
        RCS->>RCS: Error during cleanup
        RCS->>BJS: handle_job_error(execution_id, error)
        BJS->>DB: Update job status to FAILED
        BJS->>CW: Send error metrics
        BJS->>CW: Create alarm for job failure
    end
    
    Note over CWE,CW: Registration cleanup job completed