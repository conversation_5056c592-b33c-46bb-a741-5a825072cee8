# 🔄 Jobs Service - Gaps and Improvements

## 📊 **Current Status: 8.3/10 - MUY BUENO**

### **Completitud:** 90% funcional, gaps identificados

---

## 🎯 **Gaps Identificados**

### **1. CRITICAL GAPS (10%)**

#### **1.1 Real Cleanup Operations Implementation**
**Priority:** High  
**Effort:** 3-4 days  
**Impact:** Production reliability

**Current State:**
- Cleanup operations are simulated
- Missing real database cleanup
- Limited integration with external services

**Required Implementation:**

```python
# src/services/real_cleanup_service.py
"""Real cleanup service implementation."""

import asyncio
import time
from typing import Dict, Any, List, Optional
from shared.database import db_client
from shared.logger import lambda_logger
from shared.exceptions import CleanupException

class RealCleanupService:
    """Real cleanup service for automated maintenance."""
    
    def __init__(self):
        self.db = db_client
        self.cleanup_batch_size = 100
        self.max_cleanup_age_days = 30
    
    async def cleanup_expired_registrations(self) -> Dict[str, Any]:
        """Clean up expired registration records."""
        try:
            cutoff_timestamp = int(time.time()) - (self.max_cleanup_age_days * 24 * 60 * 60)
            
            # Query expired registrations
            expired_registrations = await self._get_expired_registrations(cutoff_timestamp)
            
            cleanup_results = {
                'total_found': len(expired_registrations),
                'cleaned_count': 0,
                'failed_count': 0,
                'errors': []
            }
            
            # Process in batches
            for i in range(0, len(expired_registrations), self.cleanup_batch_size):
                batch = expired_registrations[i:i + self.cleanup_batch_size]
                batch_result = await self._cleanup_registration_batch(batch)
                
                cleanup_results['cleaned_count'] += batch_result['cleaned_count']
                cleanup_results['failed_count'] += batch_result['failed_count']
                cleanup_results['errors'].extend(batch_result['errors'])
            
            lambda_logger.info(f"Cleanup completed: {cleanup_results}")
            return cleanup_results
            
        except Exception as e:
            lambda_logger.error(f"Failed to cleanup expired registrations: {str(e)}")
            raise CleanupException(f"Cleanup failed: {str(e)}")
    
    async def _get_expired_registrations(self, cutoff_timestamp: int) -> List[Dict[str, Any]]:
        """Get expired registration records."""
        try:
            # Query registrations older than cutoff
            expired_records = []
            
            # Scan for expired workflow records
            scan_params = {
                'FilterExpression': 'begins_with(PK, :pk_prefix) AND created_at < :cutoff AND workflow_status IN (:status1, :status2)',
                'ExpressionAttributeValues': {
                    ':pk_prefix': 'WORKFLOW#',
                    ':cutoff': cutoff_timestamp,
                    ':status1': 'FAILED',
                    ':status2': 'EXPIRED'
                }
            }
            
            # Perform scan with pagination
            last_evaluated_key = None
            while True:
                if last_evaluated_key:
                    scan_params['ExclusiveStartKey'] = last_evaluated_key
                
                response = self.db.scan(**scan_params)
                expired_records.extend(response.get('Items', []))
                
                last_evaluated_key = response.get('LastEvaluatedKey')
                if not last_evaluated_key:
                    break
            
            return expired_records
            
        except Exception as e:
            lambda_logger.error(f"Failed to get expired registrations: {str(e)}")
            raise
    
    async def _cleanup_registration_batch(
        self, 
        batch: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Clean up a batch of registration records."""
        result = {
            'cleaned_count': 0,
            'failed_count': 0,
            'errors': []
        }
        
        for record in batch:
            try:
                workflow_id = record.get('workflow_id')
                tenant_id = record.get('tenant_id')
                
                # Clean up workflow record
                await self.db.delete_item(
                    pk=record['PK'],
                    sk=record['SK'],
                    tenant_id=tenant_id
                )
                
                # Clean up related records
                await self._cleanup_related_records(workflow_id, tenant_id)
                
                result['cleaned_count'] += 1
                
            except Exception as e:
                error_msg = f"Failed to cleanup record {record.get('PK', 'unknown')}: {str(e)}"
                result['errors'].append(error_msg)
                result['failed_count'] += 1
                lambda_logger.error(error_msg)
        
        return result
    
    async def _cleanup_related_records(self, workflow_id: str, tenant_id: str) -> None:
        """Clean up records related to a workflow."""
        try:
            # Clean up workflow steps
            steps = await self.db.query(
                pk=f'WORKFLOW#{workflow_id}',
                sk_prefix='STEP#',
                tenant_id=tenant_id
            )
            
            for step in steps:
                await self.db.delete_item(
                    pk=step['PK'],
                    sk=step['SK'],
                    tenant_id=tenant_id
                )
            
            # Clean up workflow logs
            logs = await self.db.query(
                pk=f'WORKFLOW#{workflow_id}',
                sk_prefix='LOG#',
                tenant_id=tenant_id
            )
            
            for log in logs:
                await self.db.delete_item(
                    pk=log['PK'],
                    sk=log['SK'],
                    tenant_id=tenant_id
                )
                
        except Exception as e:
            lambda_logger.error(f"Failed to cleanup related records: {str(e)}")
            # Don't raise here to avoid stopping the cleanup process
    
    async def cleanup_orphaned_resources(self) -> Dict[str, Any]:
        """Clean up orphaned resources."""
        try:
            cleanup_results = {
                'orphaned_users': 0,
                'orphaned_payments': 0,
                'orphaned_invitations': 0,
                'errors': []
            }
            
            # Find and clean orphaned user records
            orphaned_users = await self._find_orphaned_users()
            for user in orphaned_users:
                try:
                    await self.db.delete_item(
                        pk=user['PK'],
                        sk=user['SK'],
                        tenant_id=user.get('tenant_id')
                    )
                    cleanup_results['orphaned_users'] += 1
                except Exception as e:
                    cleanup_results['errors'].append(f"Failed to cleanup user {user.get('user_id')}: {str(e)}")
            
            # Find and clean orphaned payment records
            orphaned_payments = await self._find_orphaned_payments()
            for payment in orphaned_payments:
                try:
                    await self.db.delete_item(
                        pk=payment['PK'],
                        sk=payment['SK'],
                        tenant_id=payment.get('tenant_id')
                    )
                    cleanup_results['orphaned_payments'] += 1
                except Exception as e:
                    cleanup_results['errors'].append(f"Failed to cleanup payment {payment.get('payment_id')}: {str(e)}")
            
            # Find and clean expired invitations
            expired_invitations = await self._find_expired_invitations()
            for invitation in expired_invitations:
                try:
                    await self.db.delete_item(
                        pk=invitation['PK'],
                        sk=invitation['SK'],
                        tenant_id=invitation.get('tenant_id')
                    )
                    cleanup_results['orphaned_invitations'] += 1
                except Exception as e:
                    cleanup_results['errors'].append(f"Failed to cleanup invitation {invitation.get('invitation_id')}: {str(e)}")
            
            return cleanup_results
            
        except Exception as e:
            lambda_logger.error(f"Failed to cleanup orphaned resources: {str(e)}")
            raise CleanupException(f"Orphaned resource cleanup failed: {str(e)}")
    
    async def _find_orphaned_users(self) -> List[Dict[str, Any]]:
        """Find user records without corresponding tenant."""
        orphaned_users = []
        
        try:
            # Scan for user records
            user_records = await self.db.scan(
                FilterExpression='begins_with(SK, :sk_prefix)',
                ExpressionAttributeValues={':sk_prefix': 'USER#'}
            )
            
            for user in user_records:
                tenant_id = user.get('tenant_id')
                if tenant_id:
                    # Check if tenant exists
                    try:
                        tenant = await self.db.get_item(
                            pk=f'TENANT#{tenant_id}',
                            sk='METADATA',
                            tenant_id=tenant_id
                        )
                        if not tenant:
                            orphaned_users.append(user)
                    except:
                        orphaned_users.append(user)
            
            return orphaned_users
            
        except Exception as e:
            lambda_logger.error(f"Failed to find orphaned users: {str(e)}")
            return []
    
    async def _find_orphaned_payments(self) -> List[Dict[str, Any]]:
        """Find payment records without corresponding tenant."""
        orphaned_payments = []
        
        try:
            # Scan for payment records
            payment_records = await self.db.scan(
                FilterExpression='begins_with(SK, :sk_prefix)',
                ExpressionAttributeValues={':sk_prefix': 'PAYMENT#'}
            )
            
            for payment in payment_records:
                tenant_id = payment.get('tenant_id')
                if tenant_id:
                    # Check if tenant exists
                    try:
                        tenant = await self.db.get_item(
                            pk=f'TENANT#{tenant_id}',
                            sk='METADATA',
                            tenant_id=tenant_id
                        )
                        if not tenant:
                            orphaned_payments.append(payment)
                    except:
                        orphaned_payments.append(payment)
            
            return orphaned_payments
            
        except Exception as e:
            lambda_logger.error(f"Failed to find orphaned payments: {str(e)}")
            return []
    
    async def _find_expired_invitations(self) -> List[Dict[str, Any]]:
        """Find expired invitation records."""
        expired_invitations = []
        current_time = int(time.time())
        
        try:
            # Scan for invitation records
            invitation_records = await self.db.scan(
                FilterExpression='begins_with(SK, :sk_prefix) AND expires_at < :current_time',
                ExpressionAttributeValues={
                    ':sk_prefix': 'INVITATION#',
                    ':current_time': current_time
                }
            )
            
            expired_invitations.extend(invitation_records)
            return expired_invitations
            
        except Exception as e:
            lambda_logger.error(f"Failed to find expired invitations: {str(e)}")
            return []
```

#### **1.2 Missing Job Management Endpoints**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Operational excellence

**Current State:**
- Jobs run automatically via CloudWatch Events
- Missing manual job control
- Limited job monitoring

**Required Endpoints:**

```python
# src/handlers/job_control.py
"""Job control handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@jobs_resilience("job_control")
@measure_performance("jobs_control")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Control job execution.
    
    POST /jobs/run/{job_type}
    POST /jobs/stop/{job_id}
    GET /jobs/status/{job_id}
    GET /jobs/history
    """
    pass

# src/handlers/job_monitoring.py
"""Job monitoring handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@jobs_resilience("job_monitoring")
@measure_performance("jobs_monitoring")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Monitor job execution.
    
    GET /jobs/active
    GET /jobs/metrics
    GET /jobs/logs/{job_id}
    """
    pass

# src/handlers/job_configuration.py
"""Job configuration handler."""

@require_auth
@rate_limit(requests_per_minute=20)
@jobs_resilience("job_configuration")
@measure_performance("jobs_configuration")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Configure job settings.
    
    GET /jobs/config
    PUT /jobs/config/{job_type}
    POST /jobs/schedule/{job_type}
    DELETE /jobs/schedule/{job_type}
    """
    pass
```
