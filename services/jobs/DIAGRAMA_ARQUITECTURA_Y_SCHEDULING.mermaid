graph TB
    %% External Triggers
    CloudWatchEvents[⏰ CloudWatch Events<br/>Scheduled Triggers]
    ManualTrigger[👤 Manual Triggers<br/>API Gateway]
    
    %% API Gateway
    APIGateway[🌐 API Gateway<br/>REST API]
    
    %% Lambda Functions - Scheduled Jobs
    RegistrationCleanupHandler[🧹 Registration Cleanup Handler<br/>Hourly Cleanup]
    PaymentValidationHandler[💳 Payment Validation Handler<br/>Daily Validation]
    PaymentRemindersHandler[📧 Payment Reminders Handler<br/>Daily Reminders]
    
    %% Lambda Functions - Manual Execution
    JobExecutorHandler[⚡ Job Executor Handler<br/>POST /jobs/execute]
    JobStatusHandler[📊 Job Status Handler<br/>GET /jobs/status/{id}]
    HealthCheckHandler[🏥 Health Check Handler<br/>GET /jobs/health]
    MetricsAggregatorHandler[📈 Metrics Aggregator<br/>Scheduled Metrics]
    
    %% Core Services
    BaseJobService[🔧 Base Job Service<br/>Common Job Functionality]
    RegistrationCleanupService[🧹 Registration Cleanup Service<br/>Expired Records Cleanup]
    PaymentValidationService[💳 Payment Validation Service<br/>Stripe Integration]
    PaymentRemindersService[📧 Payment Reminders Service<br/>Email Notifications]
    JobExecutionEngine[⚙️ Job Execution Engine<br/>Job Orchestration]
    JobMetricsService[📊 Job Metrics Service<br/>Performance Tracking]
    JobConfigurationService[⚙️ Job Configuration Service<br/>Config Management]
    EmailTemplateService[📧 Email Template Service<br/>Template Management]
    BatchProcessorService[📦 Batch Processor Service<br/>Batch Operations]
    
    %% External Services (Coordinated)
    OrchestratorService[🎯 Orchestrator Service<br/>Registration Management]
    AuthService[🔐 Auth Service<br/>User Management]
    TenantService[🏢 Tenant Service<br/>Tenant Management]
    PaymentService[💳 Payment Service<br/>Payment Management]
    
    %% External APIs
    StripeAPI[💳 Stripe API<br/>Payment Validation]
    SES[📧 AWS SES<br/>Email Service]
    
    %% Storage & Monitoring
    DynamoDB[(🗄️ DynamoDB<br/>agent-scl-dev)]
    CloudWatch[📊 CloudWatch<br/>Logs & Metrics]
    SSM[📋 SSM Parameter Store<br/>Configuration]
    
    %% Triggers
    CloudWatchEvents -->|Hourly| RegistrationCleanupHandler
    CloudWatchEvents -->|Daily 00:00 UTC| PaymentValidationHandler
    CloudWatchEvents -->|Daily 00:00 UTC| PaymentRemindersHandler
    CloudWatchEvents -->|Daily 01:00 UTC| MetricsAggregatorHandler
    
    ManualTrigger -->|HTTP REST| APIGateway
    APIGateway --> JobExecutorHandler
    APIGateway --> JobStatusHandler
    APIGateway --> HealthCheckHandler
    
    %% Handler Dependencies - Scheduled Jobs
    RegistrationCleanupHandler --> RegistrationCleanupService
    RegistrationCleanupHandler --> BaseJobService
    RegistrationCleanupHandler --> JobExecutionEngine
    
    PaymentValidationHandler --> PaymentValidationService
    PaymentValidationHandler --> BaseJobService
    PaymentValidationHandler --> JobExecutionEngine
    
    PaymentRemindersHandler --> PaymentRemindersService
    PaymentRemindersHandler --> BaseJobService
    PaymentRemindersHandler --> JobExecutionEngine
    
    %% Handler Dependencies - Manual Execution
    JobExecutorHandler --> JobExecutionEngine
    JobExecutorHandler --> JobConfigurationService
    JobExecutorHandler --> BaseJobService
    
    JobStatusHandler --> JobMetricsService
    JobStatusHandler --> DynamoDB
    
    HealthCheckHandler --> JobMetricsService
    HealthCheckHandler --> BaseJobService
    
    MetricsAggregatorHandler --> JobMetricsService
    MetricsAggregatorHandler --> CloudWatch
    
    %% Service Dependencies - Core
    RegistrationCleanupService --> BaseJobService
    RegistrationCleanupService --> BatchProcessorService
    RegistrationCleanupService --> OrchestratorService
    RegistrationCleanupService --> AuthService
    RegistrationCleanupService --> TenantService
    
    PaymentValidationService --> BaseJobService
    PaymentValidationService --> BatchProcessorService
    PaymentValidationService --> PaymentService
    PaymentValidationService --> StripeAPI
    PaymentValidationService --> EmailTemplateService
    
    PaymentRemindersService --> BaseJobService
    PaymentRemindersService --> BatchProcessorService
    PaymentRemindersService --> PaymentService
    PaymentRemindersService --> EmailTemplateService
    PaymentRemindersService --> SES
    
    JobExecutionEngine --> JobConfigurationService
    JobExecutionEngine --> JobMetricsService
    JobExecutionEngine --> BaseJobService
    
    %% Base Service Dependencies
    BaseJobService --> DynamoDB
    BaseJobService --> CloudWatch
    BaseJobService --> SSM
    
    BatchProcessorService --> DynamoDB
    JobMetricsService --> CloudWatch
    JobConfigurationService --> SSM
    EmailTemplateService --> SES
    
    %% External Service Integration
    RegistrationCleanupService --> OrchestratorService
    RegistrationCleanupService --> AuthService
    RegistrationCleanupService --> TenantService
    
    PaymentValidationService --> PaymentService
    PaymentValidationService --> StripeAPI
    
    PaymentRemindersService --> PaymentService
    PaymentRemindersService --> SES
    
    %% Storage Dependencies
    RegistrationCleanupService --> DynamoDB
    PaymentValidationService --> DynamoDB
    PaymentRemindersService --> DynamoDB
    JobExecutionEngine --> DynamoDB
    
    %% Monitoring
    BaseJobService --> CloudWatch
    JobExecutionEngine --> CloudWatch
    JobMetricsService --> CloudWatch
    
    %% Styling
    classDef trigger fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handler fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef coordinated fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class CloudWatchEvents,ManualTrigger trigger
    class APIGateway gateway
    class RegistrationCleanupHandler,PaymentValidationHandler,PaymentRemindersHandler,JobExecutorHandler,JobStatusHandler,HealthCheckHandler,MetricsAggregatorHandler handler
    class BaseJobService,RegistrationCleanupService,PaymentValidationService,PaymentRemindersService,JobExecutionEngine,JobMetricsService,JobConfigurationService,EmailTemplateService,BatchProcessorService service
    class SSM,CloudWatch external
    class DynamoDB storage
    class OrchestratorService,AuthService,TenantService,PaymentService coordinated
    class StripeAPI,SES api