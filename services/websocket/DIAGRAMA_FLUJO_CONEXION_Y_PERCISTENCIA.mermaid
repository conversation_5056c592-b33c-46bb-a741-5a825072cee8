sequenceDiagram
    participant C as 👤 Client
    participant WS as 🌐 WebSocket Gateway
    participant Auth as 🔐 WebSocket Authorizer
    participant AS as 🔐 Auth Service
    participant SM as 🔑 Secrets Manager
    participant CH as 🔗 Connect Handler
    participant CM as 🔗 Connection Manager
    participant PM as 👁️ Presence Manager
    participant DB as 🗄️ DynamoDB
    participant BS as 📡 Broadcast Service
    participant Other as 👥 Other Clients
    participant DH as ❌ Disconnect Handler
    participant CW as 📊 CloudWatch

    Note over C: User wants to connect
    C->>WS: WebSocket Connect Request
    Note over C,WS: wss://api.agent-scl.com/dev?token=jwt_token
    
    WS->>Auth: Authorize connection
    Auth->>AS: validate_jwt_token(token)
    AS->>SM: Get JWT secret
    SM-->>AS: JWT secret value
    AS->>AS: Verify token signature & expiration
    AS-->>Auth: {user_id, tenant_id, permissions}
    Auth-->>WS: Authorization successful
    
    WS->>CH: $connect event
    Note over CH: Connection authorized, process setup
    
    CH->>CM: add_connection(connection_id, user_id, tenant_id)
    CM->>DB: Store connection record
    Note over DB: PK: CONNECTION#{connection_id}<br/>SK: METADATA<br/>GSI1PK: USER#{user_id}<br/>GSI1SK: TENANT#{tenant_id}
    DB-->>CM: Connection stored
    CM-->>CH: Connection registered
    
    CH->>PM: update_presence(user_id, tenant_id, "online")
    PM->>DB: Store presence record with TTL
    Note over DB: PK: PRESENCE#{user_id}<br/>SK: TENANT#{tenant_id}<br/>TTL: now + 300 seconds
    DB-->>PM: Presence updated
    
    PM->>BS: broadcast_presence_update(tenant_id, user_id, presence_data)
    BS->>CM: get_tenant_connections(tenant_id, exclude_user=user_id)
    CM->>DB: Query GSI2 (TenantIndex)
    DB-->>CM: Other user connections
    CM-->>BS: Connection list
    
    BS->>WS: Send presence update to other connections
    WS->>Other: User came online notification
    
    CH->>CW: Log connection event
    CH-->>WS: Connection setup complete
    WS-->>C: WebSocket connected
    
    Note over C,Other: User is now online and receiving real-time updates
    
    rect rgb(255, 245, 245)
        Note over C: User disconnects (browser close, network issue, etc.)
        C->>WS: WebSocket Disconnect
        WS->>DH: $disconnect event
        
        DH->>CM: remove_connection(connection_id)
        CM->>DB: Delete connection record
        DB-->>CM: Connection removed
        
        DH->>PM: update_presence(user_id, tenant_id, "offline")
        PM->>DB: Update presence record
        
        PM->>BS: broadcast_presence_update(tenant_id, user_id, offline_data)
        BS->>Other: User went offline notification
        
        DH->>CW: Log disconnection event
        DH-->>WS: Cleanup complete
    end
    
    Note over C,CW: Connection lifecycle completed