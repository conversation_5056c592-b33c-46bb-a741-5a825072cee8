graph TB
    %% External Systems
    Client1[👤 Client 1<br/>WebSocket Connection]
    Client2[👤 Client 2<br/>WebSocket Connection]
    Orchestrator[🎯 Message Orchestrator<br/>HTTP Client]
    
    %% API Gateway
    WSGateway[🌐 API Gateway<br/>WebSocket API]
    HTTPGateway[🌐 API Gateway<br/>HTTP API]
    
    %% Lambda Functions
    ConnectHandler[🔗 Connect Handler<br/>$connect route]
    DisconnectHandler[❌ Disconnect Handler<br/>$disconnect route]
    DefaultHandler[📨 Default Handler<br/>$default route]
    PingHandler[💓 Ping Handler<br/>ping route]
    BroadcastHandler[📡 Orchestrator Broadcast<br/>HTTP POST]
    AuthHandler[🔐 WebSocket Authorizer<br/>Custom Auth]
    
    %% Core Services
    ConnManager[🔗 Connection Manager<br/>Lifecycle Management]
    BroadcastService[📡 Broadcast Service<br/>Message Distribution]
    PresenceManager[👁️ Presence Manager<br/>Online/Offline Status]
    TypingManager[⌨️ Typing Manager<br/>Typing Indicators]
    AuthService[🔐 Auth Service<br/>JWT Validation]
    RateLimiter[⚡ Rate Limiter<br/>Abuse Prevention]
    
    %% Storage & External
    DynamoDB[(🗄️ DynamoDB<br/>agent-scl-dev)]
    SecretsManager[🔑 Secrets Manager<br/>JWT Secret]
    CloudWatch[📊 CloudWatch<br/>Logs & Metrics]
    
    %% Client Connections
    Client1 -.->|WebSocket| WSGateway
    Client2 -.->|WebSocket| WSGateway
    Orchestrator -->|HTTP POST| HTTPGateway
    
    %% WebSocket Routing
    WSGateway --> ConnectHandler
    WSGateway --> DisconnectHandler
    WSGateway --> DefaultHandler
    WSGateway --> PingHandler
    WSGateway --> AuthHandler
    
    %% HTTP Routing
    HTTPGateway --> BroadcastHandler
    
    %% Handler Dependencies
    ConnectHandler --> AuthService
    ConnectHandler --> ConnManager
    ConnectHandler --> PresenceManager
    
    DisconnectHandler --> ConnManager
    DisconnectHandler --> PresenceManager
    
    DefaultHandler --> RateLimiter
    DefaultHandler --> TypingManager
    DefaultHandler --> BroadcastService
    
    BroadcastHandler --> ConnManager
    BroadcastHandler --> BroadcastService
    
    AuthHandler --> AuthService
    
    %% Service Dependencies
    ConnManager --> DynamoDB
    BroadcastService --> WSGateway
    BroadcastService --> DynamoDB
    PresenceManager --> DynamoDB
    TypingManager --> DynamoDB
    AuthService --> SecretsManager
    
    %% Monitoring
    ConnectHandler --> CloudWatch
    DisconnectHandler --> CloudWatch
    DefaultHandler --> CloudWatch
    BroadcastHandler --> CloudWatch
    ConnManager --> CloudWatch
    BroadcastService --> CloudWatch
    
    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handler fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class Client1,Client2,Orchestrator client
    class WSGateway,HTTPGateway gateway
    class ConnectHandler,DisconnectHandler,DefaultHandler,PingHandler,BroadcastHandler,AuthHandler handler
    class ConnManager,BroadcastService,PresenceManager,TypingManager,AuthService,RateLimiter service
    class DynamoDB storage
    class SecretsManager,CloudWatch external