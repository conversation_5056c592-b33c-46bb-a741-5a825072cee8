# 🔌 **WEBSOCKET SERVICE - DOCUMENTACIÓN TÉCNICA COMPLETA**

## **📋 ÍNDICE**
1. [Resumen Ejecutivo](#resumen-ejecutivo)
2. [Arquitectura del Servicio](#arquitectura-del-servicio)
3. [Componentes Principales](#componentes-principales)
4. [Handlers y Endpoints](#handlers-y-endpoints)
5. [Servicios Internos](#servicios-internos)
6. [Modelos de Datos](#modelos-de-datos)
7. [Flujo de <PERSON>](#flujo-de-datos)
8. [Dependencias](#dependencias)
9. [Configuración](#configuración)
10. [Seguridad](#seguridad)
11. [Monitoreo y Logging](#monitoreo-y-logging)
12. [Deployment](#deployment)

---

## **📊 RESUMEN EJECUTIVO**

### **🎯 Propósito**
El WebSocket Service es el módulo de comunicación en tiempo real del sistema **agent-scl**. Proporciona infraestructura para:
- Conexiones WebSocket bidireccionales
- Broadcasting de mensajes en tiempo real
- Gestión de presencia de usuarios
- Notificaciones instantáneas
- Coordinación con el Message Orchestrator

### **🏗️ Arquitectura**
- **Patrón**: Event-Driven Architecture con WebSocket Gateway
- **Deployment**: AWS Lambda + API Gateway WebSocket
- **Storage**: DynamoDB (tabla unificada)
- **Integration**: Message Orchestrator, Chat Service, Agent Service

### **📈 Métricas Clave**
- **Latencia**: < 100ms para broadcasting
- **Concurrencia**: Hasta 10,000 conexiones simultáneas
- **Disponibilidad**: 99.9% SLA
- **Escalabilidad**: Auto-scaling basado en conexiones activas

---

## **🏗️ ARQUITECTURA DEL SERVICIO**

### **📦 Estructura de Directorios**
```
services/websocket/
├── src/
│   ├── handlers/           # Lambda handlers para endpoints
│   ├── services/          # Lógica de negocio
│   ├── models/            # Modelos de datos específicos
│   ├── config/            # Configuración y dependencias
│   ├── common/            # Utilidades compartidas
│   └── utils/             # Utilidades específicas
├── tests/                 # Tests unitarios e integración
├── serverless.yml         # Configuración de deployment
└── requirements.txt       # Dependencias Python
```

### **🔄 Patrón Arquitectónico**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WebSocket     │    │   HTTP Endpoint  │    │   Message       │
│   Connections   │◄──►│   (Orchestrator) │◄──►│   Orchestrator  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Connection    │    │   Broadcast      │    │   DynamoDB      │
│   Manager       │◄──►│   Service        │◄──►│   (Unified)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## **🧩 COMPONENTES PRINCIPALES**

### **1. 🔗 Connection Manager**
**Archivo**: `src/services/connection_manager.py`

**Responsabilidades**:
- Gestión del ciclo de vida de conexiones WebSocket
- Almacenamiento de metadatos de conexión
- Indexación por usuario y tenant
- Cleanup automático de conexiones inactivas

**Métodos Principales**:
```python
add_connection(connection_id, user_id, tenant_id, metadata)
remove_connection(connection_id)
get_user_connections(user_id, tenant_id)
get_tenant_connections(tenant_id)
cleanup_stale_connections()
```

### **2. 📡 Broadcast Service**
**Archivo**: `src/services/broadcast_service.py`

**Responsabilidades**:
- Envío de mensajes a conexiones específicas
- Broadcasting masivo por conversación/tenant
- Gestión de fallos de entrega
- Rate limiting y throttling

**Métodos Principales**:
```python
send_to_connection(connection_id, message)
broadcast_to_conversation(conversation_id, message)
broadcast_to_tenant(tenant_id, message)
broadcast_presence_update(tenant_id, user_id, presence_data)
```

### **3. 👁️ Presence Manager**
**Archivo**: `src/services/presence_manager.py`

**Responsabilidades**:
- Tracking de estado online/offline
- Gestión de typing indicators
- Sincronización de presencia entre conexiones
- TTL automático para estados de presencia

### **4. ⚡ Rate Limiter**
**Archivo**: `src/services/rate_limiter.py`

**Responsabilidades**:
- Control de frecuencia de mensajes por conexión
- Prevención de spam y abuse
- Throttling inteligente basado en patrones
- Blacklisting temporal de conexiones abusivas

---

## **🎯 HANDLERS Y ENDPOINTS**

### **WebSocket Endpoints**

#### **1. 🔗 Connect Handler**
**Archivo**: `src/handlers/connect.py`
**Trigger**: WebSocket $connect route
```python
# Funcionalidad:
- Autenticación JWT del usuario
- Registro de nueva conexión
- Inicialización de metadatos
- Notificación de presencia online
```

#### **2. ❌ Disconnect Handler**
**Archivo**: `src/handlers/disconnect.py`
**Trigger**: WebSocket $disconnect route
```python
# Funcionalidad:
- Cleanup de conexión
- Actualización de presencia offline
- Notificación a otros usuarios
- Liberación de recursos
```

#### **3. 📨 Default Message Handler**
**Archivo**: `src/handlers/message.py`
**Trigger**: WebSocket $default route
```python
# Funcionalidad:
- Routing de mensajes entrantes
- Validación de formato
- Rate limiting
- Delegación a servicios específicos
```

#### **4. ⌨️ Typing Handler**
**Archivo**: `src/handlers/typing.py`
**Trigger**: WebSocket typing route
```python
# Funcionalidad:
- Gestión de typing indicators
- Broadcasting a participantes
- TTL automático de estados
- Debouncing de eventos
```

### **HTTP Endpoints**

#### **1. 📡 Orchestrator Broadcast**
**Archivo**: `src/handlers/orchestrator_broadcast.py`
**Endpoint**: `POST /websocket/orchestrator/broadcast`

**Request Format**:
```json
{
    "recipients": ["user-123", "user-456"],
    "notification": {
        "type": "message_notification",
        "message": { ... },
        "conversation": { ... },
        "routing_info": { ... }
    },
    "tenant_id": "tenant-789"
}
```

**Funcionalidad**:
- Recibe requests del Message Orchestrator
- Valida estructura de notificación
- Obtiene conexiones activas de recipients
- Broadcasting coordinado a múltiples usuarios
- Logging detallado de operaciones

---

## **🔧 SERVICIOS INTERNOS**

### **1. 🔐 Auth Service**
**Archivo**: `src/services/auth_service.py`
- Validación de JWT tokens
- Extracción de contexto de usuario
- Verificación de permisos de tenant
- Integración con AWS Secrets Manager

### **2. 🏥 Health Monitor**
**Archivo**: `src/services/health_monitor.py`
- Monitoreo de conexiones activas
- Detección de conexiones zombie
- Métricas de performance
- Alertas automáticas

### **3. 📊 Subscription Manager**
**Archivo**: `src/services/subscription_manager.py`
- Gestión de suscripciones a conversaciones
- Optimización de broadcasting
- Filtrado inteligente de notificaciones
- Gestión de preferencias de usuario

### **4. 🔄 Message Router**
**Archivo**: `src/services/message_router.py`
- Routing inteligente de mensajes WebSocket
- Delegación a handlers específicos
- Validación de formato y estructura
- Error handling centralizado

---

## **📊 MODELOS DE DATOS**

### **1. 🔗 WebSocket Connection**
**Archivo**: `src/models/connection.py`
```python
@dataclass
class WebSocketConnection:
    connection_id: str
    user_id: str
    tenant_id: str
    connected_at: datetime
    last_activity: datetime
    metadata: Dict[str, Any]
    status: ConnectionStatus
    subscriptions: List[str]
```

### **2. 📨 WebSocket Message**
**Archivo**: `src/models/websocket_message.py`
```python
@dataclass
class WebSocketMessage:
    action: str
    data: Dict[str, Any]
    timestamp: datetime
    connection_id: str
    user_id: str
    tenant_id: str
```

### **3. 🗄️ DynamoDB Schema**
**Tabla Unificada**: `agent-scl-dev`

**Connection Records**:
```
PK: "CONNECTION#{connection_id}"
SK: "METADATA"
GSI1PK: "USER#{user_id}"
GSI1SK: "TENANT#{tenant_id}"
GSI2PK: "TENANT#{tenant_id}"
GSI2SK: "CONNECTION#{connection_id}"
```

**Presence Records**:
```
PK: "PRESENCE#{user_id}"
SK: "TENANT#{tenant_id}"
TTL: timestamp + 300 seconds
```

---

## **🔄 FLUJO DE DATOS**

### **1. 🔗 Flujo de Conexión WebSocket**
```mermaid
sequenceDiagram
    participant Client
    participant WSGateway as WebSocket Gateway
    participant ConnectHandler as Connect Handler
    participant AuthService as Auth Service
    participant ConnManager as Connection Manager
    participant DynamoDB
    participant PresenceManager as Presence Manager

    Client->>WSGateway: WebSocket Connect Request
    WSGateway->>ConnectHandler: $connect event
    ConnectHandler->>AuthService: Validate JWT Token
    AuthService-->>ConnectHandler: User Context
    ConnectHandler->>ConnManager: Add Connection
    ConnManager->>DynamoDB: Store Connection Record
    ConnectHandler->>PresenceManager: Update Online Status
    PresenceManager->>DynamoDB: Store Presence Record
    ConnectHandler-->>WSGateway: Connection Accepted
    WSGateway-->>Client: WebSocket Connected
```

### **2. 📡 Flujo de Broadcasting desde Orchestrator**
```mermaid
sequenceDiagram
    participant Orchestrator as Message Orchestrator
    participant BroadcastHandler as Orchestrator Broadcast Handler
    participant ConnManager as Connection Manager
    participant BroadcastService as Broadcast Service
    participant WSGateway as WebSocket Gateway
    participant Client1
    participant Client2

    Orchestrator->>BroadcastHandler: POST /websocket/orchestrator/broadcast
    BroadcastHandler->>ConnManager: Get User Connections
    ConnManager-->>BroadcastHandler: Active Connections List
    BroadcastHandler->>BroadcastService: Send to Connections
    BroadcastService->>WSGateway: Send Message (Connection 1)
    BroadcastService->>WSGateway: Send Message (Connection 2)
    WSGateway->>Client1: Real-time Notification
    WSGateway->>Client2: Real-time Notification
    BroadcastService-->>BroadcastHandler: Delivery Report
    BroadcastHandler-->>Orchestrator: Success Response
```

### **3. ⌨️ Flujo de Typing Indicators**
```mermaid
sequenceDiagram
    participant Client1
    participant WSGateway as WebSocket Gateway
    participant MessageHandler as Message Handler
    participant TypingManager as Typing Manager
    participant BroadcastService as Broadcast Service
    participant Client2

    Client1->>WSGateway: Typing Start Message
    WSGateway->>MessageHandler: Route Message
    MessageHandler->>TypingManager: Process Typing Event
    TypingManager->>BroadcastService: Broadcast to Conversation
    BroadcastService->>WSGateway: Send Typing Indicator
    WSGateway->>Client2: Typing Notification

    Note over TypingManager: Auto-expire after 3 seconds
    TypingManager->>BroadcastService: Broadcast Typing Stop
    BroadcastService->>WSGateway: Send Stop Indicator
    WSGateway->>Client2: Stop Typing Notification
```

---

## **🔗 DEPENDENCIAS**

### **📚 Shared Layer Dependencies**
```python
# Modelos Unificados
from shared.models.conversation import Conversation
from shared.models.message import Message
from shared.models.message_routing import MessageRouting

# Servicios Compartidos
from shared.database import DynamoDBClient
from shared.logger import lambda_logger, log_business_operation
from shared.auth import AuthContext, validate_jwt_token
from shared.config import get_settings
from shared.exceptions import ValidationError, AuthenticationError
from shared.utils import parse_request_body, validate_required_fields
```

### **🌐 External Service Dependencies**
```yaml
# Message Orchestrator
ORCHESTRATOR_SERVICE_URL: agent-scl-orchestrator-dev

# Chat Service (para coordinación)
CHAT_SERVICE_URL: agent-scl-chat-dev

# Auth Service (para validación de tokens)
JWT_SECRET_NAME: agent-scl/dev/jwt-secret
```

### **☁️ AWS Service Dependencies**
```yaml
# API Gateway WebSocket
- WebSocket API Gateway
- Custom Domain (opcional)
- Route53 (para DNS)

# Lambda Functions
- Runtime: Python 3.11
- Memory: 256-512 MB
- Timeout: 30 seconds

# DynamoDB
- Table: agent-scl-dev (unified table)
- GSI1: UserIndex (user_id, tenant_id)
- GSI2: TenantIndex (tenant_id, connection_id)
- TTL: enabled for presence records

# Secrets Manager
- JWT Secret: agent-scl/dev/jwt-secret

# CloudWatch
- Logs: /aws/lambda/agent-scl-websocket-dev-*
- Metrics: Custom metrics for connections
- Alarms: Connection count, error rate
```

---

## **⚙️ CONFIGURACIÓN**

### **🔧 Environment Variables**
```yaml
# Core Configuration
STAGE: dev
REGION: us-east-1
PROJECT_NAME: agent-scl
DYNAMODB_TABLE: agent-scl-dev

# Security
JWT_SECRET_NAME: agent-scl/dev/jwt-secret

# Service Integration
WEBSOCKET_API_ENDPOINT: wss://api.agent-scl.com/dev
ORCHESTRATOR_SERVICE_URL: https://api.agent-scl.com/dev
CHAT_SERVICE_URL: https://api.agent-scl.com/dev

# Performance Tuning
MAX_CONNECTIONS_PER_USER: 5
CONNECTION_TTL_SECONDS: 3600
PRESENCE_TTL_SECONDS: 300
TYPING_TIMEOUT_SECONDS: 3
RATE_LIMIT_MESSAGES_PER_MINUTE: 60
```

### **📊 DynamoDB Configuration**
```yaml
# Table Configuration
BillingMode: PAY_PER_REQUEST
StreamSpecification:
  StreamViewType: NEW_AND_OLD_IMAGES

# Global Secondary Indexes
GSI1:
  IndexName: UserIndex
  KeySchema:
    - AttributeName: GSI1PK (user_id)
    - AttributeName: GSI1SK (tenant_id)

GSI2:
  IndexName: TenantIndex
  KeySchema:
    - AttributeName: GSI2PK (tenant_id)
    - AttributeName: GSI2SK (connection_id)

# TTL Configuration
TimeToLiveSpecification:
  AttributeName: ttl
  Enabled: true
```

---

## **🔐 SEGURIDAD**

### **🛡️ Autenticación y Autorización**
```python
# JWT Token Validation
- Validación en conexión WebSocket inicial
- Extracción de user_id y tenant_id del token
- Verificación de expiración y firma
- Rate limiting por usuario autenticado

# Tenant Isolation
- Todas las operaciones filtradas por tenant_id
- Prevención de cross-tenant data leakage
- Validación de permisos en cada operación
```

### **🔒 Medidas de Seguridad**
```yaml
# Rate Limiting
- 60 mensajes por minuto por conexión
- 5 conexiones máximas por usuario
- Blacklisting temporal por abuso

# Input Validation
- Sanitización de todos los inputs
- Validación de estructura de mensajes
- Prevención de injection attacks

# Connection Security
- HTTPS/WSS únicamente
- Validación de origen (CORS)
- Timeout automático de conexiones inactivas

# Data Protection
- Encriptación en tránsito (TLS 1.2+)
- Encriptación en reposo (DynamoDB)
- No almacenamiento de datos sensibles en logs
```

### **🚨 Monitoring de Seguridad**
```python
# Alertas Automáticas
- Conexiones anómalas por IP
- Rate limiting violations
- Fallos de autenticación repetidos
- Patrones de tráfico sospechosos

# Audit Logging
- Todas las conexiones/desconexiones
- Operaciones de broadcasting
- Fallos de autenticación
- Cambios de configuración
```

---

## **📊 MONITOREO Y LOGGING**

### **📈 Métricas Clave**
```yaml
# Connection Metrics
- websocket.connections.active: Conexiones activas
- websocket.connections.new: Nuevas conexiones por minuto
- websocket.connections.dropped: Conexiones perdidas
- websocket.connections.duration: Duración promedio de conexión

# Performance Metrics
- websocket.broadcast.latency: Latencia de broadcasting
- websocket.message.throughput: Mensajes por segundo
- websocket.api.response_time: Tiempo de respuesta HTTP
- websocket.errors.rate: Tasa de errores

# Business Metrics
- websocket.users.concurrent: Usuarios concurrentes
- websocket.tenants.active: Tenants con conexiones activas
- websocket.conversations.active: Conversaciones con actividad
- websocket.typing.events: Eventos de typing por minuto
```

### **🔍 Logging Strategy**
```python
# Structured Logging
{
    "timestamp": "2024-01-15T10:30:00Z",
    "level": "INFO",
    "service": "websocket",
    "function": "orchestrator_broadcast",
    "operation": "broadcast_message",
    "tenant_id": "tenant-123",
    "user_id": "user-456",
    "connection_id": "abc123",
    "message_type": "message_notification",
    "recipients_count": 3,
    "successful_sends": 2,
    "failed_sends": 1,
    "latency_ms": 45,
    "correlation_id": "req-789"
}
```

### **🚨 Alertas y Notificaciones**
```yaml
# Critical Alerts (PagerDuty)
- Error rate > 5%
- Connection failures > 10/minute
- API latency > 1000ms
- DynamoDB throttling

# Warning Alerts (Slack)
- Connection count > 8000
- Memory usage > 80%
- Failed broadcasts > 5%
- Stale connections > 100

# Info Notifications (Dashboard)
- Daily connection summary
- Performance trends
- Usage patterns by tenant
```

---

## **🚀 DEPLOYMENT**

### **📦 Deployment Configuration**
```yaml
# Serverless Framework
service: agent-scl-websocket
frameworkVersion: '3'
provider:
  name: aws
  runtime: python3.11
  region: us-east-1
  stage: dev

# Lambda Functions
functions:
  connect: # WebSocket $connect
  disconnect: # WebSocket $disconnect
  default: # WebSocket $default
  ping: # WebSocket ping
  orchestratorBroadcast: # HTTP POST endpoint
  websocketAuthorizer: # Custom authorizer
```

### **🔄 CI/CD Pipeline**
```yaml
# GitHub Actions Workflow
stages:
  1. Code Quality:
     - Linting (flake8, black)
     - Type checking (mypy)
     - Security scan (bandit)

  2. Testing:
     - Unit tests (pytest)
     - Integration tests
     - Load testing (WebSocket connections)

  3. Deployment:
     - Deploy to dev environment
     - Smoke tests
     - Deploy to staging
     - Production deployment (manual approval)
```

### **📋 Deployment Checklist**
```markdown
Pre-Deployment:
- [ ] Shared layer deployed and accessible
- [ ] Auth service deployed with JWT secret
- [ ] DynamoDB table created with correct schema
- [ ] SSM parameters configured
- [ ] CloudWatch dashboards ready

Post-Deployment:
- [ ] WebSocket connection test successful
- [ ] Broadcasting functionality verified
- [ ] Integration with orchestrator confirmed
- [ ] Monitoring alerts configured
- [ ] Performance baselines established
```

---

## **🎯 CONCLUSIONES Y MEJORES PRÁCTICAS**

### **✅ Fortalezas del Diseño**
- **Escalabilidad**: Auto-scaling basado en conexiones
- **Resilencia**: Manejo robusto de fallos de conexión
- **Performance**: Latencia optimizada para tiempo real
- **Seguridad**: Autenticación y autorización robustas
- **Observabilidad**: Logging y métricas comprehensivas

### **🔄 Áreas de Mejora Futuras**
- **Clustering**: Soporte para múltiples regiones
- **Caching**: Redis para metadatos de conexión
- **Compression**: Compresión de mensajes grandes
- **Analytics**: ML para detección de patrones anómalos

### **📚 Recursos Adicionales**
- [AWS WebSocket API Documentation](https://docs.aws.amazon.com/apigateway/latest/developerguide/websocket-api.html)
- [DynamoDB Best Practices](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/best-practices.html)
- [Lambda Performance Optimization](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)

---

**📝 Documento generado el**: 2024-08-29
**🔄 Última actualización**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
