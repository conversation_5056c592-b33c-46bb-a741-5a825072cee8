# 🔌 **WebSocket Service - Agent-SCL Platform**

## **🚀 Descripción General**

El WebSocket Service es el núcleo de comunicación en tiempo real del ecosistema **agent-scl**. Proporciona infraestructura robusta para conexiones bidireccionales, broadcasting de mensajes, gestión de presencia y coordinación con el Message Orchestrator.

### **✨ Características Principales**
- 🔗 **Conexiones WebSocket**: Gestión completa del ciclo de vida
- 📡 **Broadcasting**: Distribución eficiente de mensajes en tiempo real
- 👁️ **Gestión de Presencia**: Tracking de usuarios online/offline
- ⌨️ **Typing Indicators**: Indicadores de escritura en tiempo real
- 🔐 **Autenticación JWT**: Seguridad robusta con tokens
- ⚡ **Rate Limiting**: Prevención de abuso y spam
- 📊 **Monitoreo**: Métricas y logging comprehensivos

---

## **📋 Tabla de Contenidos**
1. [Instalación y Setup](#instalación-y-setup)
2. [Configuración](#configuración)
3. [API Reference](#api-reference)
4. [Ejemplos de Uso](#ejemplos-de-uso)
5. [Testing](#testing)
6. [Deployment](#deployment)
7. [Troubleshooting](#troubleshooting)

---

## **🛠️ Instalación y Setup**

### **Prerrequisitos**
```bash
# Servicios requeridos (deben estar desplegados)
✅ agent-scl-shared-layer-dev
✅ agent-scl-auth-dev
✅ agent-scl-orchestrator-dev

# Herramientas de desarrollo
- Python 3.11+
- Serverless Framework 3.x
- AWS CLI configurado
- Node.js 18+ (para Serverless)
```

### **Instalación Local**
```bash
# 1. Clonar y navegar al directorio
cd services/websocket

# 2. Instalar dependencias Python
pip install -r requirements.txt

# 3. Instalar Serverless plugins
npm install

# 4. Configurar variables de entorno
export STAGE=dev
export REGION=us-east-1
```

### **Configuración de Secrets**
```bash
# JWT Secret (ya debe existir del auth service)
aws secretsmanager describe-secret --secret-id "agent-scl/dev/jwt-secret"

# Verificar tabla DynamoDB
aws dynamodb describe-table --table-name "agent-scl-dev"
```

---

## **⚙️ Configuración**

### **Variables de Entorno**
```yaml
# Core Configuration
STAGE: dev                           # Ambiente de deployment
REGION: us-east-1                   # Región AWS
PROJECT_NAME: agent-scl             # Nombre del proyecto
DYNAMODB_TABLE: agent-scl-dev       # Tabla DynamoDB unificada

# Security
JWT_SECRET_NAME: agent-scl/dev/jwt-secret  # Secret en AWS Secrets Manager

# Service Integration
WEBSOCKET_API_ENDPOINT: wss://api.agent-scl.com/dev
ORCHESTRATOR_SERVICE_URL: https://api.agent-scl.com/dev

# Performance Tuning
MAX_CONNECTIONS_PER_USER: 5         # Máximo conexiones por usuario
CONNECTION_TTL_SECONDS: 3600        # TTL de conexiones inactivas
PRESENCE_TTL_SECONDS: 300           # TTL de registros de presencia
TYPING_TIMEOUT_SECONDS: 3           # Timeout de typing indicators
RATE_LIMIT_MESSAGES_PER_MINUTE: 60  # Rate limit por conexión
```

### **Configuración DynamoDB**
```yaml
# Tabla Principal: agent-scl-dev
Partition Key: PK (String)
Sort Key: SK (String)

# Global Secondary Indexes
GSI1: UserIndex
  - PK: GSI1PK (user_id)
  - SK: GSI1SK (tenant_id)

GSI2: TenantIndex
  - PK: GSI2PK (tenant_id)
  - SK: GSI2SK (connection_id)

# TTL Configuration
TTL Attribute: ttl (Number)
```

---

## **📡 API Reference**

### **WebSocket Endpoints**

#### **Conexión WebSocket**
```javascript
// URL de conexión
const wsUrl = 'wss://api.agent-scl.com/dev?token=JWT_TOKEN';
const ws = new WebSocket(wsUrl);

// Eventos de conexión
ws.onopen = () => console.log('Connected');
ws.onclose = () => console.log('Disconnected');
ws.onerror = (error) => console.error('Error:', error);
```

#### **Mensajes WebSocket**
```javascript
// Ping/Health Check
ws.send(JSON.stringify({
    action: 'ping',
    data: {}
}));

// Typing Indicator
ws.send(JSON.stringify({
    action: 'typing',
    data: {
        conversation_id: 'conv-123',
        status: 'start' // 'start' | 'stop'
    }
}));

// Recibir mensajes
ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('Received:', message);
};
```

### **HTTP Endpoints**

#### **Orchestrator Broadcast**
```http
POST /websocket/orchestrator/broadcast
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
    "recipients": ["user-123", "user-456"],
    "notification": {
        "type": "message_notification",
        "message": {
            "id": "msg-789",
            "content": "Hello World",
            "sender_id": "user-000"
        },
        "conversation": {
            "id": "conv-123",
            "name": "General Chat"
        }
    },
    "tenant_id": "tenant-789"
}
```

**Response:**
```json
{
    "success": true,
    "delivered": 2,
    "failed": 0,
    "details": {
        "successful_connections": ["conn-1", "conn-2"],
        "failed_connections": [],
        "cleaned_connections": []
    }
}
```

---

## **💡 Ejemplos de Uso**

### **Cliente JavaScript Completo**
```javascript
class WebSocketClient {
    constructor(token) {
        this.token = token;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    connect() {
        const wsUrl = `wss://api.agent-scl.com/dev?token=${this.token}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            this.startHeartbeat();
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };

        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.stopHeartbeat();
            this.attemptReconnect();
        };
    }

    handleMessage(message) {
        switch(message.type) {
            case 'message_notification':
                this.onNewMessage(message.data);
                break;
            case 'presence_update':
                this.onPresenceUpdate(message.data);
                break;
            case 'typing_indicator':
                this.onTypingIndicator(message.data);
                break;
        }
    }

    sendTyping(conversationId, status) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                action: 'typing',
                data: {
                    conversation_id: conversationId,
                    status: status
                }
            }));
        }
    }
}
```

---

## **🧪 Testing**

### **Unit Tests**
```bash
# Ejecutar todos los tests
pytest src/tests/

# Tests específicos
pytest src/tests/test_connection_manager.py
pytest src/tests/test_broadcast_service.py
pytest src/tests/test_presence_manager.py

# Con coverage
pytest --cov=src src/tests/
```

### **Integration Tests**
```bash
# Tests de integración
pytest src/tests/integration/

# Test de WebSocket completo
pytest src/tests/integration/test_websocket_flow.py
```

### **Load Testing**
```bash
# Test de carga con Artillery
npm install -g artillery
artillery run tests/load/websocket-load-test.yml

# Test de conexiones concurrentes
python tests/load/concurrent_connections.py
```

---

## **🚀 Deployment**

### **Orden de Deployment**
```bash
# 1. Verificar prerrequisitos
aws cloudformation describe-stacks --stack-name "agent-scl-shared-layer-dev"
aws cloudformation describe-stacks --stack-name "agent-scl-auth-dev"

# 2. Deploy WebSocket Service
cd services/websocket
serverless deploy --stage dev

# 3. Verificar deployment
aws apigatewayv2 get-apis --query "Items[?Name=='agent-scl-websocket-dev']"
```

### **Rollback**
```bash
# Rollback a versión anterior
serverless rollback --timestamp TIMESTAMP --stage dev

# Verificar rollback
serverless info --stage dev
```

---

## **🔐 Seguridad**

### **Medidas Implementadas**
- 🔑 **JWT Authentication**: Validación en cada conexión
- 🏢 **Multi-tenant Isolation**: Aislamiento completo por tenant
- ⚡ **Rate Limiting**: 60 mensajes/minuto por conexión
- 🕐 **Connection TTL**: Limpieza automática de conexiones inactivas
- 🛡️ **Input Validation**: Sanitización de todos los inputs
- 🔒 **HTTPS/WSS Only**: Encriptación en tránsito obligatoria

### **Monitoreo de Seguridad**
```bash
# Verificar conexiones anómalas
aws logs filter-log-events \
  --log-group-name "/aws/lambda/agent-scl-websocket-dev-connect" \
  --filter-pattern "ERROR"

# Revisar rate limiting violations
aws logs filter-log-events \
  --log-group-name "/aws/lambda/agent-scl-websocket-dev-default" \
  --filter-pattern "rate_limit_exceeded"
```

---

## **📊 Monitoreo y Métricas**

### **Métricas Clave**
```yaml
# Connection Metrics
- websocket.connections.active: Conexiones activas
- websocket.connections.new: Nuevas conexiones/min
- websocket.broadcast.latency: Latencia de broadcasting
- websocket.errors.rate: Tasa de errores

# Business Metrics
- websocket.users.concurrent: Usuarios concurrentes
- websocket.messages.throughput: Mensajes/segundo
```

### **Dashboards CloudWatch**
```bash
# Crear dashboard personalizado
aws cloudwatch put-dashboard \
  --dashboard-name "WebSocket-Service-Metrics" \
  --dashboard-body file://monitoring/dashboard.json
```

---

## **🔗 Integración con Otros Servicios**

### **Message Orchestrator**
- Recibe requests de broadcasting
- Coordina entrega de notificaciones
- Maneja routing inteligente

### **Chat Service**
- Proporciona contexto de conversaciones
- Gestiona metadatos de mensajes
- Coordina con agentes

### **Auth Service**
- Valida JWT tokens
- Proporciona contexto de usuario
- Gestiona permisos de tenant

---

## **🛠️ Troubleshooting**

### **Problemas Comunes**

#### **Conexiones que fallan**
```bash
# Verificar logs de conexión
aws logs tail /aws/lambda/agent-scl-websocket-dev-connect --follow

# Verificar JWT secret
aws secretsmanager get-secret-value --secret-id "agent-scl/dev/jwt-secret"
```

#### **Broadcasting lento**
```bash
# Verificar métricas de DynamoDB
aws cloudwatch get-metric-statistics \
  --namespace AWS/DynamoDB \
  --metric-name ConsumedReadCapacityUnits \
  --dimensions Name=TableName,Value=agent-scl-dev
```

#### **Conexiones zombie**
```bash
# Ejecutar cleanup manual
aws lambda invoke \
  --function-name agent-scl-websocket-dev-cleanup \
  --payload '{"action": "cleanup_stale_connections"}' \
  response.json
```

---

## **📚 Documentación Adicional**

- 📖 [Documentación Técnica Completa](./TECHNICAL_DOCUMENTATION.md)
- 🔧 [Guía de Desarrollo](./docs/development.md)
- 🚀 [Guía de Deployment](./docs/deployment.md)
- 🐛 [Troubleshooting Avanzado](./docs/troubleshooting.md)

---

## **📝 Changelog**

### **v1.0.0 (2024-08-29) - ✅ DEPLOYED**
- ✅ Gestión completa de conexiones WebSocket
- ✅ Autenticación JWT robusta
- ✅ Broadcasting eficiente desde Orchestrator
- ✅ Gestión de presencia online/offline
- ✅ Typing indicators en tiempo real
- ✅ Rate limiting y seguridad
- ✅ Monitoreo y logging comprehensivos
- ✅ Integración con tabla DynamoDB unificada

### **Próximas Versiones**
- 🔄 Clustering multi-región
- 🔄 Compresión de mensajes
- 🔄 Analytics avanzados
- 🔄 File sharing en tiempo real

---

**📝 Documento actualizado**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
**🔗 Repositorio**: [agent-scl/services/websocket](https://github.com/agent-scl/services/websocket)
