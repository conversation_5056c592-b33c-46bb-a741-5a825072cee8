# services/websocket/serverless.yml
# WebSocket Service for real-time chat functionality

service: ${self:custom.projectName}-websocket

provider:
  name: aws
  runtime: python3.11
  region: ${self:custom.region}
  stage: ${self:custom.stage}
  memorySize: 256
  timeout: 30
  
  # Environment variables
  environment:
    STAGE: ${self:custom.stage}
    REGION: ${self:custom.region}
    PROJECT_NAME: ${self:custom.projectName}
    DYNAMODB_TABLE: ${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}
    JWT_SECRET_NAME: ${self:custom.projectName}/${self:custom.stage}/jwt-secret
    WEBSOCKET_API_ENDPOINT: !Sub "wss://${WebSocketApi}.execute-api.${AWS::Region}.amazonaws.com/${self:custom.stage}"
    ORCHESTRATOR_SERVICE_URL: ${cf:${self:custom.projectName}-orchestrator-${self:custom.stage}.ServiceEndpoint, ''}
    
  # IAM permissions
  iam:
    role:
      statements:
        # DynamoDB permissions
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
            - dynamodb:BatchGetItem
            - dynamodb:BatchWriteItem
          Resource:
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}/index/*
        
        # API Gateway Management permissions for WebSocket
        - Effect: Allow
          Action:
            - execute-api:ManageConnections
          Resource:
            - arn:aws:execute-api:${self:custom.region}:*:*/@connections/*
        
        # CloudWatch permissions
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:${self:custom.region}:*:*
        
        # Secrets Manager permissions
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource:
            - arn:aws:secretsmanager:${self:custom.region}:*:secret:${self:custom.projectName}/${self:custom.stage}/*

# Custom variables
custom:
  # Project configuration
  projectName: agent-scl
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  
  # API Gateway configuration
  apiGateway:
    restApiId: ${cf:${self:custom.projectName}-shared-${self:custom.stage}.RestApiId, ''}
    restApiRootResourceId: ${cf:${self:custom.projectName}-shared-${self:custom.stage}.RestApiRootResourceId, ''}
  
  # Stage-specific configuration
  stageConfig:
    dev:
      dynamodb:
        tableName: ${self:custom.projectName}-dev
    staging:
      dynamodb:
        tableName: ${self:custom.projectName}-staging
    prod:
      dynamodb:
        tableName: ${self:custom.projectName}-prod

# Functions
functions:
  # WebSocket connection handler
  connect:
    handler: src.handlers.connect.handler
    description: Handle WebSocket connection establishment
    events:
      - websocket:
          route: $connect
          authorizer:
            name: websocketAuthorizer
            identitySource:
              - route.request.querystring.token
    environment:
      FUNCTION_NAME: websocket-connect
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # WebSocket disconnection handler
  disconnect:
    handler: src.handlers.disconnect.handler
    description: Handle WebSocket disconnection
    events:
      - websocket:
          route: $disconnect
    environment:
      FUNCTION_NAME: websocket-disconnect
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Default message handler
  default:
    handler: src.handlers.default.handler
    description: Handle WebSocket messages
    timeout: 30
    memorySize: 512
    events:
      - websocket:
          route: $default
    environment:
      FUNCTION_NAME: websocket-default
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Ping handler for health checks
  ping:
    handler: src.handlers.ping.handler
    description: WebSocket ping/health check
    timeout: 10
    memorySize: 256
    events:
      - websocket:
          route: ping
    environment:
      FUNCTION_NAME: websocket-ping
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Orchestrator broadcast handler (HTTP endpoint)
  orchestratorBroadcast:
    handler: src.handlers.orchestrator_broadcast.handler
    description: Broadcast messages from orchestrator
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /websocket/orchestrator/broadcast
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: websocket-orchestrator-broadcast
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # WebSocket authorizer
  websocketAuthorizer:
    handler: src.handlers.auth.handler
    description: WebSocket JWT authorizer
    environment:
      FUNCTION_NAME: websocket-authorizer
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'
    - '!src/**/.pytest_cache/**'
    - '!src/**/tests/**'

# Resources
resources:
  Resources:
    # WebSocket API Gateway
    WebSocketApi:
      Type: AWS::ApiGatewayV2::Api
      Properties:
        Name: ${self:custom.projectName}-websocket-${self:custom.stage}
        ProtocolType: WEBSOCKET
        RouteSelectionExpression: "$request.body.action"
        Description: "WebSocket API for real-time communication"
        Tags:
          Service: websocket
          Environment: ${self:custom.stage}
          Project: ${self:custom.projectName}

  Outputs:
    WebSocketApiUrl:
      Description: "WebSocket API Gateway URL"
      Value:
        Fn::Join:
          - ""
          - - "wss://"
            - Ref: WebSocketApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-websocket-${self:custom.stage}-ApiUrl

    ServiceEndpoint:
      Description: "WebSocket Service HTTP endpoint"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-websocket-${self:custom.stage}-ServiceEndpoint
