sequenceDiagram
    participant <PERSON><PERSON> as 🎯 Message Orchestrator
    participant HTTP as 🌐 HTTP Gateway
    participant BH as 📡 Broadcast Handler
    participant CM as 🔗 Connection Manager
    participant DB as 🗄️ DynamoDB
    participant BS as 📡 Broadcast Service
    participant WS as 🌐 WebSocket Gateway
    participant C1 as 👤 Client 1
    participant C2 as 👤 Client 2
    participant C3 as 👤 Client 3
    participant CW as 📊 CloudWatch

    Note over MO: New message needs broadcasting
    MO->>HTTP: POST /websocket/orchestrator/broadcast
    Note over MO,HTTP: {recipients: [user1, user2], notification: {...}}
    
    HTTP->>BH: Route to Broadcast Handler
    BH->>BH: Validate request structure
    BH->>BH: Extract recipients & notification
    
    Note over BH: Get active connections for recipients
    BH->>CM: get_user_connections(user1, tenant_id)
    CM->>DB: Query GSI1 (UserIndex)
    DB-->>CM: [connection_1, connection_2]
    CM-->>BH: Active connections for user1
    
    BH->>CM: get_user_connections(user2, tenant_id)
    CM->>DB: Query GSI1 (UserIndex)
    DB-->>CM: [connection_3]
    CM-->>BH: Active connections for user2
    
    Note over BH: Prepare broadcast message
    BH->>BH: Format WebSocket message
    BH->>BS: broadcast_to_connections(connections, message)
    
    Note over BS: Send to each connection
    BS->>WS: send_to_connection(connection_1, message)
    WS->>C1: Real-time notification
    
    BS->>WS: send_to_connection(connection_2, message)
    WS->>C2: Real-time notification
    
    BS->>WS: send_to_connection(connection_3, message)
    WS->>C3: Real-time notification
    
    Note over BS: Handle delivery results
    WS-->>BS: Success (connection_1)
    WS-->>BS: Success (connection_2)
    WS-->>BS: Failed (connection_3 - stale)
    
    Note over BS: Clean up failed connections
    BS->>CM: remove_connection(connection_3)
    CM->>DB: Delete stale connection record
    
    Note over BS: Compile delivery report
    BS-->>BH: {successful: 2, failed: 1, cleaned: 1}
    
    Note over BH: Log operation results
    BH->>CW: Log broadcast metrics
    BH->>CW: Log business operation
    
    BH-->>HTTP: 200 OK {delivered: 2, failed: 1}
    HTTP-->>MO: Success response
    
    Note over MO: Broadcasting completed