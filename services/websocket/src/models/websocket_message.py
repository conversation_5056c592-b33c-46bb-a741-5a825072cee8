# services/websocket/src/models/websocket_message.py
# WebSocket message model

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import uuid
import json

class WebSocketMessage:
    """Model for WebSocket messages"""
    
    def __init__(self, action: str, data: Dict[str, Any] = None, 
                 message_id: str = None, user_id: str = None, 
                 tenant_id: str = None, connection_id: str = None):
        self.message_id = message_id or str(uuid.uuid4())
        self.action = action
        self.data = data or {}
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.connection_id = connection_id
        
        # Timestamps
        self.timestamp = datetime.utcnow()
        self.created_at = self.timestamp
        
        # Message metadata
        self.status = 'pending'
        self.retry_count = 0
        self.error_message = None
        
        # TTL for DynamoDB (30 days from now)
        self.ttl = int((datetime.utcnow() + timedelta(days=30)).timestamp())
    
    @classmethod
    def from_payload(cls, payload: Dict[str, Any], user_id: str = None, 
                    tenant_id: str = None, connection_id: str = None) -> 'WebSocketMessage':
        """Create message from WebSocket payload"""
        return cls(
            action=payload.get('action'),
            data=payload.get('data', {}),
            message_id=payload.get('messageId'),
            user_id=user_id,
            tenant_id=tenant_id,
            connection_id=connection_id
        )
    
    def set_status(self, status: str, error_message: str = None):
        """Set message status"""
        self.status = status
        if error_message:
            self.error_message = error_message
    
    def increment_retry(self):
        """Increment retry count"""
        self.retry_count += 1
    
    def to_websocket_payload(self) -> str:
        """Convert to WebSocket payload JSON string"""
        payload = {
            'messageId': self.message_id,
            'action': self.action,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'status': self.status
        }
        
        if self.error_message:
            payload['error'] = self.error_message
        
        return json.dumps(payload)
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format"""
        item = {
            'messageId': self.message_id,
            'action': self.action,
            'data': self.data,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'connectionId': self.connection_id,
            'timestamp': self.timestamp.isoformat(),
            'createdAt': self.created_at.isoformat(),
            'status': self.status,
            'retryCount': self.retry_count,
            'ttl': self.ttl
        }
        
        if self.error_message:
            item['errorMessage'] = self.error_message
        
        return item
    
    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any]) -> 'WebSocketMessage':
        """Create message from DynamoDB item"""
        message = cls(
            action=item['action'],
            data=item.get('data', {}),
            message_id=item['messageId'],
            user_id=item.get('userId'),
            tenant_id=item.get('tenantId'),
            connection_id=item.get('connectionId')
        )
        
        # Parse timestamps
        if 'timestamp' in item:
            message.timestamp = datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00'))
        if 'createdAt' in item:
            message.created_at = datetime.fromisoformat(item['createdAt'].replace('Z', '+00:00'))
        
        # Set metadata
        message.status = item.get('status', 'pending')
        message.retry_count = item.get('retryCount', 0)
        message.error_message = item.get('errorMessage')
        message.ttl = item.get('ttl', message.ttl)
        
        return message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            'messageId': self.message_id,
            'action': self.action,
            'data': self.data,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'connectionId': self.connection_id,
            'timestamp': self.timestamp.isoformat(),
            'createdAt': self.created_at.isoformat(),
            'status': self.status,
            'retryCount': self.retry_count
        }
        
        if self.error_message:
            result['errorMessage'] = self.error_message
        
        return result
    
    def is_expired(self) -> bool:
        """Check if message is expired"""
        current_time = datetime.utcnow()
        expiry_time = datetime.fromtimestamp(self.ttl)
        return current_time > expiry_time
    
    def get_age(self) -> timedelta:
        """Get message age"""
        return datetime.utcnow() - self.created_at
    
    def should_retry(self, max_retries: int = 3) -> bool:
        """Check if message should be retried"""
        return self.retry_count < max_retries and self.status in ['pending', 'failed']
    
    def __str__(self) -> str:
        return f"WebSocketMessage(id={self.message_id}, action={self.action}, status={self.status})"
    
    def __repr__(self) -> str:
        return self.__str__()


class ChatMessage(WebSocketMessage):
    """Specialized WebSocket message for chat"""
    
    def __init__(self, conversation_id: str, content: str, message_type: str = 'text',
                 user_id: str = None, tenant_id: str = None, connection_id: str = None,
                 attachments: List[Dict[str, Any]] = None):
        
        data = {
            'conversationId': conversation_id,
            'content': content,
            'type': message_type,
            'attachments': attachments or []
        }
        
        super().__init__(
            action='send_message',
            data=data,
            user_id=user_id,
            tenant_id=tenant_id,
            connection_id=connection_id
        )
        
        self.conversation_id = conversation_id
        self.content = content
        self.message_type = message_type
        self.attachments = attachments or []
    
    @classmethod
    def from_websocket_message(cls, ws_message: WebSocketMessage) -> 'ChatMessage':
        """Create ChatMessage from WebSocketMessage"""
        if ws_message.action != 'send_message':
            raise ValueError("WebSocketMessage must have action 'send_message'")
        
        data = ws_message.data
        chat_message = cls(
            conversation_id=data['conversationId'],
            content=data['content'],
            message_type=data.get('type', 'text'),
            user_id=ws_message.user_id,
            tenant_id=ws_message.tenant_id,
            connection_id=ws_message.connection_id,
            attachments=data.get('attachments', [])
        )
        
        # Copy metadata
        chat_message.message_id = ws_message.message_id
        chat_message.timestamp = ws_message.timestamp
        chat_message.created_at = ws_message.created_at
        chat_message.status = ws_message.status
        chat_message.retry_count = ws_message.retry_count
        chat_message.error_message = ws_message.error_message
        chat_message.ttl = ws_message.ttl
        
        return chat_message
    
    def has_attachments(self) -> bool:
        """Check if message has attachments"""
        return len(self.attachments) > 0
    
    def get_attachment_count(self) -> int:
        """Get number of attachments"""
        return len(self.attachments)
    
    def add_attachment(self, attachment: Dict[str, Any]):
        """Add attachment to message"""
        self.attachments.append(attachment)
        self.data['attachments'] = self.attachments
