# services/websocket/src/models/connection.py
# WebSocket connection model

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import uuid

class WebSocketConnection:
    """Model for WebSocket connection"""
    
    def __init__(self, connection_id: str, user_id: str, tenant_id: str, 
                 email: str, role: str = 'USER', permissions: list = None):
        self.connection_id = connection_id
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.email = email
        self.role = role
        self.permissions = permissions or []
        
        # Timestamps
        self.connected_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        
        # Connection metadata
        self.status = 'connected'
        self.user_agent = None
        self.ip_address = None
        
        # TTL for DynamoDB (24 hours from now)
        self.ttl = int((datetime.utcnow() + timedelta(hours=24)).timestamp())
    
    @classmethod
    def from_auth_context(cls, connection_id: str, auth_context: Dict[str, Any]) -> 'WebSocketConnection':
        """Create connection from authentication context"""
        return cls(
            connection_id=connection_id,
            user_id=auth_context['user_id'],
            tenant_id=auth_context['tenant_id'],
            email=auth_context['email'],
            role=auth_context.get('role', 'USER'),
            permissions=auth_context.get('permissions', [])
        )
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.utcnow()
    
    def set_metadata(self, user_agent: str = None, ip_address: str = None):
        """Set connection metadata"""
        if user_agent:
            self.user_agent = user_agent
        if ip_address:
            self.ip_address = ip_address
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format"""
        return {
            'connectionId': self.connection_id,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'email': self.email,
            'role': self.role,
            'permissions': self.permissions,
            'connectedAt': self.connected_at.isoformat(),
            'lastActivity': self.last_activity.isoformat(),
            'status': self.status,
            'userAgent': self.user_agent,
            'ipAddress': self.ip_address,
            'ttl': self.ttl
        }
    
    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any]) -> 'WebSocketConnection':
        """Create connection from DynamoDB item"""
        connection = cls(
            connection_id=item['connectionId'],
            user_id=item['userId'],
            tenant_id=item['tenantId'],
            email=item['email'],
            role=item.get('role', 'USER'),
            permissions=item.get('permissions', [])
        )
        
        # Parse timestamps
        if 'connectedAt' in item:
            connection.connected_at = datetime.fromisoformat(item['connectedAt'].replace('Z', '+00:00'))
        if 'lastActivity' in item:
            connection.last_activity = datetime.fromisoformat(item['lastActivity'].replace('Z', '+00:00'))
        
        # Set metadata
        connection.status = item.get('status', 'connected')
        connection.user_agent = item.get('userAgent')
        connection.ip_address = item.get('ipAddress')
        connection.ttl = item.get('ttl', connection.ttl)
        
        return connection
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'connectionId': self.connection_id,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'email': self.email,
            'role': self.role,
            'permissions': self.permissions,
            'connectedAt': self.connected_at.isoformat(),
            'lastActivity': self.last_activity.isoformat(),
            'status': self.status,
            'userAgent': self.user_agent,
            'ipAddress': self.ip_address
        }
    
    def is_expired(self) -> bool:
        """Check if connection is expired"""
        current_time = datetime.utcnow()
        expiry_time = datetime.fromtimestamp(self.ttl)
        return current_time > expiry_time
    
    def get_connection_duration(self) -> timedelta:
        """Get connection duration"""
        return datetime.utcnow() - self.connected_at
    
    def get_idle_time(self) -> timedelta:
        """Get idle time since last activity"""
        return datetime.utcnow() - self.last_activity
    
    def __str__(self) -> str:
        return f"WebSocketConnection(id={self.connection_id}, user={self.user_id}, tenant={self.tenant_id})"
    
    def __repr__(self) -> str:
        return self.__str__()
