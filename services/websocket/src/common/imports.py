# services/websocket/src/common/imports.py
# Common imports for websocket service handlers and services

"""
Common imports for websocket service.
Centralizes all shared layer imports and websocket-specific utilities.
"""

import json
import uuid
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

# Shared layer imports
from shared.logger import lambda_logger, log_api_request, log_api_response, log_business_operation
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, AuthContext
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.decorators import measure_performance
from shared.exceptions import (
    ValidationException,
    AuthorizationException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.validation import validate_required_fields
from shared.validators import validate_uuid
from shared.config import get_settings, get_database_config
from shared.database import DynamoDBClient
from shared.dependency_injection import container

# WebSocket service specific imports
from ..config.dependencies import configure_dependencies


# Common validation helpers
def validate_tenant_access(auth_context: AuthContext, tenant_id: str) -> bool:
    """Validate that user has access to tenant."""
    if auth_context.tenant_id != tenant_id:
        raise AuthorizationException(f"Access denied to tenant {tenant_id}")
    return True


def extract_path_parameter(event: Dict[str, Any], param_name: str) -> str:
    """Extract path parameter from event."""
    path_params = event.get('pathParameters') or {}
    param_value = path_params.get(param_name)
    if not param_value:
        raise ValidationException(f"Missing required path parameter: {param_name}")
    return param_value


def extract_query_parameter(event: Dict[str, Any], param_name: str, default: Any = None) -> Any:
    """Extract query parameter from event."""
    query_params = event.get('queryStringParameters') or {}
    return query_params.get(param_name, default)


def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
    """Parse and validate JSON request body."""
    body_str = event.get('body')
    if not body_str:
        raise ValidationException("Request body is required")
    
    try:
        return json.loads(body_str)
    except json.JSONDecodeError:
        raise ValidationException("Invalid JSON in request body")


def extract_websocket_info(event: Dict[str, Any]) -> Dict[str, Any]:
    """Extract WebSocket connection information from event."""
    request_context = event.get('requestContext', {})
    
    return {
        'connection_id': request_context.get('connectionId'),
        'route_key': request_context.get('routeKey'),
        'event_type': request_context.get('eventType'),
        'domain_name': request_context.get('domainName'),
        'stage': request_context.get('stage'),
        'request_id': request_context.get('requestId'),
        'query_params': event.get('queryStringParameters') or {},
        'headers': event.get('headers') or {}
    }


# WebSocket service specific response helpers
def success_response(data: Any = None, status_code: int = 200) -> Dict[str, Any]:
    """Create success response for WebSocket operations."""
    if data is None:
        return {'statusCode': status_code}
    return APIResponse.success(data, status_code)


def error_response(message: str, status_code: int = 400) -> Dict[str, Any]:
    """Create error response for WebSocket operations."""
    return APIResponse.error(message, status_code)


# WebSocket service specific logging helpers
def log_websocket_operation(
    operation: str,
    connection_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log WebSocket operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="websocket_connection",
        entity_id=connection_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details=details or {}
    )


def log_connection_operation(
    operation: str,
    connection_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log connection operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="connection",
        entity_id=connection_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details=details or {}
    )


def log_message_routing(
    operation: str,
    message_id: str,
    connection_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log message routing operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="message_routing",
        entity_id=message_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details={
            'connection_id': connection_id,
            **(details or {})
        }
    )


# WebSocket service specific error handling
def handle_websocket_error(error: Exception, operation: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle WebSocket service specific errors."""
    if isinstance(error, ValidationException):
        lambda_logger.warning(f"Validation error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 400)
    
    elif isinstance(error, AuthorizationException):
        lambda_logger.warning(f"Authorization error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 403)
    
    elif isinstance(error, ResourceNotFoundException):
        lambda_logger.warning(f"Resource not found in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 404)
    
    else:
        lambda_logger.error(f"Unexpected error in {operation}", extra={
            'error': str(error),
            'error_type': type(error).__name__,
            'context': context
        })
        return error_response("Internal server error", 500)


# WebSocket service specific decorator
def websocket_handler(operation_name: str, require_auth: bool = True):
    """Decorator for WebSocket service handlers."""
    def decorator(func):
        # Apply shared decorators in correct order
        decorated_func = func
        
        # Note: WebSocket handlers don't use require_auth in the same way as HTTP handlers
        # Authentication is handled differently for WebSocket connections
        
        decorated_func = rate_limit(requests_per_minute=300)(decorated_func)  # Higher limit for WebSocket
        decorated_func = user_resilience(operation_name)(decorated_func)
        decorated_func = measure_performance(f"websocket_{operation_name}")(decorated_func)
        
        return decorated_func
    
    return decorator


# Service getters using dependency injection
def get_connection_manager():
    """Get connection manager instance."""
    from ..services.connection_manager import IConnectionManager
    return container.get(IConnectionManager)


def get_message_router():
    """Get message router instance."""
    from ..services.message_router import IMessageRouter
    return container.get(IMessageRouter)


def get_presence_manager():
    """Get presence manager instance."""
    from ..services.presence_manager import IPresenceManager
    return container.get(IPresenceManager)


def get_subscription_manager():
    """Get subscription manager instance."""
    from ..services.subscription_manager import ISubscriptionManager
    return container.get(ISubscriptionManager)


def get_broadcast_service():
    """Get broadcast service instance."""
    from ..services.broadcast_service import IBroadcastService
    return container.get(IBroadcastService)


def get_websocket_auth_service():
    """Get WebSocket auth service instance."""
    from ..services.auth_service import IWebSocketAuthService
    return container.get(IWebSocketAuthService)


def get_rate_limiter():
    """Get rate limiter instance."""
    from ..services.rate_limiter import IRateLimiter
    return container.get(IRateLimiter)


def get_health_monitor():
    """Get health monitor instance."""
    from ..services.health_monitor import IHealthMonitor
    return container.get(IHealthMonitor)


# Export all common utilities
__all__ = [
    # Core imports
    'json', 'uuid', 'datetime', 'timezone',
    'Dict', 'Any', 'List', 'Optional', 'Tuple',
    
    # Shared layer
    'lambda_logger', 'log_api_request', 'log_api_response', 'log_business_operation',
    'APIResponse', 'handle_cors_preflight',
    'require_auth', 'AuthContext',
    'rate_limit', 'user_resilience', 'measure_performance',
    'ValidationException', 'AuthorizationException', 'ResourceNotFoundException', 'BusinessLogicException',
    'validate_required_fields', 'validate_uuid',
    'get_settings', 'get_database_config',
    'DynamoDBClient', 'container',
    
    # WebSocket service utilities
    'validate_tenant_access', 'extract_path_parameter', 'extract_query_parameter',
    'parse_request_body', 'extract_websocket_info', 'success_response', 'error_response',
    'log_websocket_operation', 'log_connection_operation', 'log_message_routing',
    'handle_websocket_error', 'websocket_handler',
    
    # Service getters
    'get_connection_manager', 'get_message_router', 'get_presence_manager',
    'get_subscription_manager', 'get_broadcast_service', 'get_websocket_auth_service',
    'get_rate_limiter', 'get_health_monitor'
]
