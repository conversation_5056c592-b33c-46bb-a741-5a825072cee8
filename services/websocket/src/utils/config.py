# services/websocket/src/utils/config.py
# Configuration utilities for WebSocket service

import os
from typing import Dict, Any, Optional

class WebSocketConfig:
    """Configuration manager for WebSocket service"""
    
    def __init__(self):
        self.stage = os.environ.get('STAGE', 'dev')
        self.region = os.environ.get('REGION', 'us-east-1')
        self.project_name = os.environ.get('PROJECT_NAME', 'the-jungle-agents')
        
        # Table names
        self.connections_table = os.environ.get('CONNECTIONS_TABLE', 
                                               f'{self.project_name}-websocket-connections-{self.stage}')
        self.messages_table = os.environ.get('MESSAGES_TABLE', 
                                            f'{self.project_name}-realtime-messages-{self.stage}')
        
        # JWT configuration
        self.jwt_secret = os.environ.get('JWT_SECRET', 'dev-secret-key')
        
        # WebSocket configuration
        self.connection_ttl = int(os.environ.get('CONNECTION_TTL', '86400'))  # 24 hours
        self.message_ttl = int(os.environ.get('MESSAGE_TTL', '2592000'))     # 30 days
        
        # Rate limiting
        self.max_messages_per_minute = int(os.environ.get('MAX_MESSAGES_PER_MINUTE', '100'))
        self.max_connections_per_user = int(os.environ.get('MAX_CONNECTIONS_PER_USER', '5'))
        
        # Performance settings
        self.batch_size = int(os.environ.get('BATCH_SIZE', '25'))
        self.max_broadcast_targets = int(os.environ.get('MAX_BROADCAST_TARGETS', '100'))
    
    def get_table_name(self, table_type: str) -> str:
        """Get table name by type"""
        if table_type == 'connections':
            return self.connections_table
        elif table_type == 'messages':
            return self.messages_table
        else:
            raise ValueError(f"Unknown table type: {table_type}")
    
    def get_websocket_endpoint(self) -> str:
        """Get WebSocket API Gateway endpoint"""
        # This will be set by API Gateway
        return os.environ.get('WEBSOCKET_ENDPOINT', f'wss://localhost/{self.stage}')
    
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.stage in ['dev', 'development', 'local']
    
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.stage in ['prod', 'production']
    
    def get_cors_config(self) -> Dict[str, Any]:
        """Get CORS configuration"""
        if self.is_development():
            return {
                'origins': ['*'],
                'methods': ['*'],
                'headers': ['*']
            }
        else:
            return {
                'origins': [
                    'https://app.agentscl.com',
                    'https://staging.agentscl.com'
                ],
                'methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
                'headers': ['Content-Type', 'Authorization', 'X-Requested-With']
            }
    
    def get_rate_limit_config(self) -> Dict[str, int]:
        """Get rate limiting configuration"""
        return {
            'messages_per_minute': self.max_messages_per_minute,
            'connections_per_user': self.max_connections_per_user,
            'burst_limit': self.max_messages_per_minute * 2,
            'window_size': 60  # seconds
        }
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration"""
        return {
            'metrics_enabled': True,
            'detailed_metrics': not self.is_production(),
            'log_level': 'DEBUG' if self.is_development() else 'INFO',
            'alarm_thresholds': {
                'error_rate': 0.05,  # 5%
                'latency_p99': 1000,  # 1 second
                'connection_count': 1000
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'stage': self.stage,
            'region': self.region,
            'project_name': self.project_name,
            'connections_table': self.connections_table,
            'messages_table': self.messages_table,
            'connection_ttl': self.connection_ttl,
            'message_ttl': self.message_ttl,
            'rate_limits': self.get_rate_limit_config(),
            'monitoring': self.get_monitoring_config()
        }


# Global configuration instance
config = WebSocketConfig()
