# services/websocket/src/utils/validation.py
# Validation utilities for WebSocket messages and connections

import json
import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

class ValidationError(Exception):
    """Custom validation error"""
    def __init__(self, message: str, field: str = None, code: str = None):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(self.message)

class WebSocketValidator:
    """Validator for WebSocket messages and connections"""
    
    # Message type constants
    VALID_MESSAGE_TYPES = [
        'text', 'document', 'audio', 'image', 'video',
        'typing_start', 'typing_stop', 'presence_update',
        'message_status', 'system'
    ]
    
    # Action constants
    VALID_ACTIONS = [
        'send_message', 'get_messages', 'join_conversation',
        'leave_conversation', 'typing_indicator', 'presence_update',
        'message_status_update', 'ping', 'pong'
    ]
    
    @staticmethod
    def validate_connection_request(event: Dict[str, Any]) -> <PERSON>ple[bool, Optional[str]]:
        """Validate WebSocket connection request"""
        try:
            # Check for required query parameters
            query_params = event.get('queryStringParameters', {}) or {}
            
            # Validate token presence
            if 'token' not in query_params:
                return False, "Missing authentication token"
            
            token = query_params['token']
            if not token or len(token.strip()) == 0:
                return False, "Empty authentication token"
            
            # Basic token format validation (JWT should have 3 parts)
            token_parts = token.split('.')
            if len(token_parts) != 3:
                return False, "Invalid token format"
            
            return True, None
            
        except Exception as e:
            return False, f"Connection validation error: {str(e)}"
    
    @staticmethod
    def validate_message_payload(payload: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate WebSocket message payload"""
        try:
            # Required fields
            required_fields = ['action']
            for field in required_fields:
                if field not in payload:
                    return False, f"Missing required field: {field}"
            
            # Validate action
            action = payload.get('action')
            if action not in WebSocketValidator.VALID_ACTIONS:
                return False, f"Invalid action: {action}. Valid actions: {WebSocketValidator.VALID_ACTIONS}"
            
            # Action-specific validation
            if action == 'send_message':
                return WebSocketValidator._validate_send_message(payload)
            elif action == 'typing_indicator':
                return WebSocketValidator._validate_typing_indicator(payload)
            elif action == 'presence_update':
                return WebSocketValidator._validate_presence_update(payload)
            elif action == 'message_status_update':
                return WebSocketValidator._validate_message_status_update(payload)
            
            return True, None
            
        except Exception as e:
            return False, f"Message validation error: {str(e)}"
    
    @staticmethod
    def _validate_send_message(payload: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate send_message payload"""
        data = payload.get('data', {})
        
        # Required fields for send_message
        required_fields = ['conversationId', 'content', 'type']
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field in data: {field}"
        
        # Validate message type
        message_type = data.get('type')
        if message_type not in WebSocketValidator.VALID_MESSAGE_TYPES:
            return False, f"Invalid message type: {message_type}"
        
        # Validate content
        content = data.get('content')
        if not content or (isinstance(content, str) and len(content.strip()) == 0):
            return False, "Message content cannot be empty"
        
        # Validate content length
        if isinstance(content, str) and len(content) > 10000:  # 10KB limit
            return False, "Message content too long (max 10KB)"
        
        # Validate conversation ID format
        conversation_id = data.get('conversationId')
        if not re.match(r'^[a-zA-Z0-9_-]+$', conversation_id):
            return False, "Invalid conversation ID format"
        
        return True, None
    
    @staticmethod
    def _validate_typing_indicator(payload: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate typing_indicator payload"""
        data = payload.get('data', {})
        
        # Required fields
        required_fields = ['conversationId', 'isTyping']
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field in data: {field}"
        
        # Validate isTyping boolean
        is_typing = data.get('isTyping')
        if not isinstance(is_typing, bool):
            return False, "isTyping must be a boolean"
        
        return True, None
    
    @staticmethod
    def _validate_presence_update(payload: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate presence_update payload"""
        data = payload.get('data', {})
        
        # Required fields
        required_fields = ['status']
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field in data: {field}"
        
        # Validate status
        status = data.get('status')
        valid_statuses = ['online', 'offline', 'away', 'busy']
        if status not in valid_statuses:
            return False, f"Invalid status: {status}. Valid statuses: {valid_statuses}"
        
        return True, None
    
    @staticmethod
    def _validate_message_status_update(payload: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate message_status_update payload"""
        data = payload.get('data', {})
        
        # Required fields
        required_fields = ['messageId', 'status']
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field in data: {field}"
        
        # Validate status
        status = data.get('status')
        valid_statuses = ['sent', 'delivered', 'read']
        if status not in valid_statuses:
            return False, f"Invalid message status: {status}. Valid statuses: {valid_statuses}"
        
        return True, None
    
    @staticmethod
    def validate_json_payload(body: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Validate and parse JSON payload"""
        try:
            if not body:
                return False, None, "Empty message body"
            
            # Parse JSON
            payload = json.loads(body)
            
            if not isinstance(payload, dict):
                return False, None, "Payload must be a JSON object"
            
            return True, payload, None
            
        except json.JSONDecodeError as e:
            return False, None, f"Invalid JSON: {str(e)}"
        except Exception as e:
            return False, None, f"Payload validation error: {str(e)}"
    
    @staticmethod
    def sanitize_message_content(content: str) -> str:
        """Sanitize message content"""
        if not isinstance(content, str):
            return str(content)
        
        # Remove potentially dangerous characters
        sanitized = content.strip()
        
        # Remove null bytes
        sanitized = sanitized.replace('\x00', '')
        
        # Limit length
        if len(sanitized) > 10000:
            sanitized = sanitized[:10000]
        
        return sanitized
    
    @staticmethod
    def validate_tenant_access(user_tenant_id: str, resource_tenant_id: str) -> bool:
        """Validate tenant access for multi-tenancy"""
        if not user_tenant_id or not resource_tenant_id:
            return False
        
        return user_tenant_id == resource_tenant_id
    
    @staticmethod
    def validate_rate_limit(user_id: str, action: str, current_count: int, limit: int) -> Tuple[bool, Optional[str]]:
        """Validate rate limiting"""
        if current_count >= limit:
            return False, f"Rate limit exceeded for {action}. Limit: {limit}/minute"
        
        return True, None

    @staticmethod
    def validate_typing_message(message_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate typing indicator message"""
        try:
            # Check required fields
            required_fields = ['conversationId', 'isTyping']
            for field in required_fields:
                if field not in message_data:
                    return False, f"Missing required field: {field}"

            # Validate conversation ID
            conversation_id = message_data.get('conversationId')
            if not isinstance(conversation_id, str) or len(conversation_id.strip()) == 0:
                return False, "conversationId must be a non-empty string"

            # Validate isTyping
            is_typing = message_data.get('isTyping')
            if not isinstance(is_typing, bool):
                return False, "isTyping must be a boolean"

            # Validate optional duration
            if 'duration' in message_data:
                duration = message_data.get('duration')
                if not isinstance(duration, int) or duration < 5 or duration > 300:
                    return False, "duration must be an integer between 5 and 300 seconds"

            return True, None

        except Exception as e:
            return False, f"Typing message validation error: {str(e)}"
