# services/websocket/src/utils/auth.py
# Authentication utilities for WebSocket connections

import jwt
import json
from typing import Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timezone

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from .config import config

class WebSocketAuthError(Exception):
    """WebSocket authentication error"""
    pass

class WebSocketAuth:
    """WebSocket authentication manager"""
    
    def __init__(self):
        self.jwt_secret = config.jwt_secret
        self.algorithm = 'HS256'
    
    def validate_token(self, token: str) -> <PERSON><PERSON>[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Validate JWT token and extract user information
        
        Returns:
            (is_valid, user_data, error_message)
        """
        try:
            if not token:
                return False, None, "Missing token"
            
            # Decode JWT token
            payload = jwt.decode(
                token, 
                self.jwt_secret, 
                algorithms=[self.algorithm],
                options={"verify_exp": True}
            )
            
            # Validate required fields
            required_fields = ['user_id', 'tenant_id', 'email']
            for field in required_fields:
                if field not in payload:
                    return False, None, f"Missing required field in token: {field}"
            
            # Extract user information
            user_data = {
                'user_id': payload['user_id'],
                'tenant_id': payload['tenant_id'],
                'email': payload['email'],
                'role': payload.get('role', 'USER'),
                'permissions': payload.get('permissions', []),
                'exp': payload.get('exp'),
                'iat': payload.get('iat')
            }
            
            # Validate expiration
            if 'exp' in payload:
                exp_timestamp = payload['exp']
                current_timestamp = datetime.now(timezone.utc).timestamp()
                
                if current_timestamp > exp_timestamp:
                    return False, None, "Token has expired"
            
            lambda_logger.info("Token validated successfully", extra={
                'user_id': user_data['user_id'],
                'tenant_id': user_data['tenant_id']
            })
            
            return True, user_data, None
            
        except jwt.ExpiredSignatureError:
            return False, None, "Token has expired"
        except jwt.InvalidTokenError as e:
            return False, None, f"Invalid token: {str(e)}"
        except Exception as e:
            lambda_logger.error("Token validation error", extra={
                'error': str(e)
            })
            return False, None, f"Token validation failed: {str(e)}"
    
    def generate_connection_context(self, user_data: Dict[str, Any], connection_id: str) -> Dict[str, Any]:
        """Generate connection context for WebSocket"""
        return {
            'connectionId': connection_id,
            'userId': user_data['user_id'],
            'tenantId': user_data['tenant_id'],
            'email': user_data['email'],
            'role': user_data['role'],
            'permissions': user_data['permissions'],
            'connectedAt': datetime.utcnow().isoformat(),
            'lastActivity': datetime.utcnow().isoformat()
        }
    
    def create_authorizer_response(self, effect: str, resource: str, 
                                 principal_id: str = None, 
                                 context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create API Gateway authorizer response"""
        response = {
            'principalId': principal_id or 'user',
            'policyDocument': {
                'Version': '2012-10-17',
                'Statement': [
                    {
                        'Action': 'execute-api:Invoke',
                        'Effect': effect,
                        'Resource': resource
                    }
                ]
            }
        }
        
        if context:
            # API Gateway context must be strings
            string_context = {}
            for key, value in context.items():
                if isinstance(value, (dict, list)):
                    string_context[key] = json.dumps(value)
                else:
                    string_context[key] = str(value)
            response['context'] = string_context
        
        return response
    
    def extract_token_from_event(self, event: Dict[str, Any]) -> Optional[str]:
        """Extract token from WebSocket connection event"""
        try:
            # Try query string parameters first
            query_params = event.get('queryStringParameters', {}) or {}
            if 'token' in query_params:
                return query_params['token']
            
            # Try headers
            headers = event.get('headers', {}) or {}
            auth_header = headers.get('Authorization') or headers.get('authorization')
            if auth_header and auth_header.startswith('Bearer '):
                return auth_header[7:]  # Remove 'Bearer ' prefix
            
            # Try multiValueQueryStringParameters
            multi_query_params = event.get('multiValueQueryStringParameters', {}) or {}
            if 'token' in multi_query_params:
                tokens = multi_query_params['token']
                if tokens and len(tokens) > 0:
                    return tokens[0]
            
            return None
            
        except Exception as e:
            lambda_logger.error("Error extracting token from event", extra={
                'error': str(e),
                'event_keys': list(event.keys()) if event else []
            })
            return None
    
    def validate_permissions(self, user_permissions: list, required_permission: str) -> bool:
        """Validate user permissions"""
        if not required_permission:
            return True
        
        if not user_permissions:
            return False
        
        # Check for specific permission or admin role
        return (
            required_permission in user_permissions or
            'admin' in user_permissions or
            '*' in user_permissions
        )
    
    def check_tenant_access(self, user_tenant_id: str, resource_tenant_id: str) -> bool:
        """Check if user has access to tenant resource"""
        if not user_tenant_id or not resource_tenant_id:
            return False
        
        return user_tenant_id == resource_tenant_id
    
    def is_token_expired(self, token_data: Dict[str, Any]) -> bool:
        """Check if token is expired"""
        if 'exp' not in token_data:
            return False
        
        exp_timestamp = token_data['exp']
        current_timestamp = datetime.now(timezone.utc).timestamp()
        
        return current_timestamp > exp_timestamp
    
    def get_token_remaining_time(self, token_data: Dict[str, Any]) -> Optional[int]:
        """Get remaining time for token in seconds"""
        if 'exp' not in token_data:
            return None
        
        exp_timestamp = token_data['exp']
        current_timestamp = datetime.now(timezone.utc).timestamp()
        
        remaining = int(exp_timestamp - current_timestamp)
        return max(0, remaining)


# Global auth instance
websocket_auth = WebSocketAuth()
