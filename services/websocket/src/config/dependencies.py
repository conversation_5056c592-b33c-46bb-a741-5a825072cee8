# services/websocket/src/config/dependencies.py
# Dependency injection configuration for websocket service

"""
Dependency injection configuration for websocket service.
Registers all services and their implementations.
"""

from shared.dependency_injection import container
from shared.logger import lambda_logger

# Import service interfaces and implementations
from ..services.connection_manager import IConnectionManager, ConnectionManager
from ..services.message_router import IMessageRouter, MessageRouter
from ..services.presence_manager import IPresenceManager, PresenceManager
from ..services.subscription_manager import ISubscriptionManager, SubscriptionManager
from ..services.broadcast_service import IBroadcastService, BroadcastService
from ..services.auth_service import IWebSocketAuthService, WebSocketAuthService
from ..services.rate_limiter import IRateLimiter, RateLimiter
from ..services.health_monitor import IHealthMonitor, HealthMonitor


def configure_dependencies():
    """
    Configure dependency injection for websocket service.

    This function registers all service implementations with their interfaces
    in the dependency injection container.
    """
    try:
        # Register connection manager as singleton
        container.register_singleton(IConnectionManager, ConnectionManager)
        
        # Register message router as singleton
        container.register_singleton(I<PERSON><PERSON>ageR<PERSON><PERSON>, MessageRouter)
        
        # Register presence manager as singleton
        container.register_singleton(IPresenceManager, PresenceManager)
        
        # Register subscription manager as singleton
        container.register_singleton(ISubscriptionManager, SubscriptionManager)
        
        # Register broadcast service as singleton
        container.register_singleton(IBroadcastService, BroadcastService)
        
        # Register auth service as singleton
        container.register_singleton(IWebSocketAuthService, WebSocketAuthService)
        
        # Register rate limiter as singleton
        container.register_singleton(IRateLimiter, RateLimiter)
        
        # Register health monitor as singleton
        container.register_singleton(IHealthMonitor, HealthMonitor)

        lambda_logger.info("WebSocket service dependencies configured successfully")

    except Exception as e:
        lambda_logger.error(f"Failed to configure WebSocket service dependencies: {str(e)}")
        raise


# Auto-configure dependencies when module is imported
configure_dependencies()
