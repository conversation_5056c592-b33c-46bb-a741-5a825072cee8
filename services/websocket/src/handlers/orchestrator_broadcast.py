# services/websocket/src/handlers/orchestrator_broadcast.py
# Handler for broadcasting messages from the orchestrator

"""
Orchestrator Broadcast Handler for WebSocket Service

This handler receives broadcast requests from the Message Orchestrator and
sends real-time notifications to connected users. It handles:
1. Message notifications
2. Agent response notifications  
3. Typing indicators
4. Presence updates

POST /websocket/orchestrator/broadcast
{
    "recipients": ["user-123", "user-456"],
    "notification": {
        "type": "message_notification",
        "message": { ... },
        "conversation": { ... },
        "routing_info": { ... }
    },
    "tenant_id": "tenant-789"
}
"""

from ..common.imports import *


@websocket_handler("orchestrator_broadcast", require_auth=False)  # Auth handled by orchestrator
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Broadcast notifications to WebSocket connections from the orchestrator.
    
    This handler is called by the Message Orchestrator to send
    real-time notifications to connected users.
    """
    
    try:
        # Parse request body
        body = parse_request_body(event)
        
        # Validate required fields
        validate_required_fields(body, ['recipients', 'notification', 'tenant_id'])
        
        recipients = body['recipients']
        notification = body['notification']
        tenant_id = body['tenant_id']
        
        # Validate recipients is a list
        if not isinstance(recipients, list):
            return error_response("Recipients must be a list of user IDs", 400)
        
        # Validate notification structure
        validate_required_fields(notification, ['type'])
        
        # Get services
        broadcast_service = get_broadcast_service()
        connection_manager = get_connection_manager()
        
        # Log broadcast operation start
        log_websocket_operation(
            operation="orchestrator_broadcast",
            connection_id="broadcast",
            tenant_id=tenant_id,
            user_id="system",
            status="started",
            details={
                'notification_type': notification['type'],
                'recipients_count': len(recipients),
                'tenant_id': tenant_id
            }
        )
        
        # Get active connections for recipients
        active_connections = []
        for recipient_id in recipients:
            connections = connection_manager.get_user_connections(recipient_id, tenant_id)
            active_connections.extend(connections)
        
        if not active_connections:
            # No active connections, but this is not an error
            log_websocket_operation(
                operation="orchestrator_broadcast",
                connection_id="broadcast",
                tenant_id=tenant_id,
                user_id="system",
                status="completed",
                details={
                    'notification_type': notification['type'],
                    'recipients_count': len(recipients),
                    'active_connections': 0,
                    'messages_sent': 0
                }
            )
            
            return success_response({
                'message': "Broadcast completed (no active connections)",
                'result': {
                    'recipients_requested': len(recipients),
                    'active_connections': 0,
                    'messages_sent': 0,
                    'notification_type': notification['type']
                }
            }, 200)
        
        # Broadcast notification to all active connections
        successful_sends = 0
        failed_sends = 0
        
        for connection in active_connections:
            connection_id = connection.get('connection_id')
            if connection_id:
                success = broadcast_service.send_to_connection(connection_id, notification)
                if success:
                    successful_sends += 1
                else:
                    failed_sends += 1
        
        # Log broadcast completion
        log_websocket_operation(
            operation="orchestrator_broadcast",
            connection_id="broadcast",
            tenant_id=tenant_id,
            user_id="system",
            status="completed",
            details={
                'notification_type': notification['type'],
                'recipients_count': len(recipients),
                'active_connections': len(active_connections),
                'successful_sends': successful_sends,
                'failed_sends': failed_sends
            }
        )
        
        return success_response({
            'message': "Broadcast completed successfully",
            'result': {
                'recipients_requested': len(recipients),
                'active_connections': len(active_connections),
                'messages_sent': successful_sends,
                'failed_sends': failed_sends,
                'notification_type': notification['type'],
                'success_rate': successful_sends / len(active_connections) if active_connections else 0
            }
        }, 200)
    
    except Exception as e:
        return handle_websocket_error(e, "orchestrator_broadcast", {
            'recipients': locals().get('recipients'),
            'notification_type': locals().get('notification', {}).get('type'),
            'tenant_id': locals().get('tenant_id')
        })
