# services/websocket/src/handlers/connect.py
# WebSocket connection handler

import json
from typing import Dict, Any
from datetime import datetime

try:
    from shared.logger import lambda_logger
    from shared.database import DynamoDBClient
    from shared.responses import APIResponse
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class MockDynamoDBClient:
        def __init__(self, table_name): self.table_name = table_name
        def put_item(self, item): pass
    
    DynamoDBClient = MockDynamoDBClient

from ..utils.config import config
from ..utils.auth import websocket_auth
from ..utils.validation import WebSocketValidator
from ..models.connection import WebSocketConnection
from ..services.presence_manager import presence_manager

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle WebSocket connection establishment
    
    This function is called when a client connects to the WebSocket API.
    It validates the JWT token and stores the connection information.
    """
    
    connection_id = event.get('requestContext', {}).get('connectionId')
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("WebSocket connection attempt", extra={
        'connection_id': connection_id,
        'request_id': request_id,
        'event_keys': list(event.keys())
    })
    
    try:
        # Validate connection request
        is_valid, error_message = WebSocketValidator.validate_connection_request(event)
        if not is_valid:
            lambda_logger.warning("Invalid connection request", extra={
                'connection_id': connection_id,
                'error': error_message
            })
            return {
                'statusCode': 401,
                'body': json.dumps({'error': error_message})
            }
        
        # Extract and validate JWT token
        token = websocket_auth.extract_token_from_event(event)
        if not token:
            lambda_logger.warning("No token provided", extra={
                'connection_id': connection_id
            })
            return {
                'statusCode': 401,
                'body': json.dumps({'error': 'Authentication token required'})
            }
        
        # Validate token
        is_valid, user_data, error_message = websocket_auth.validate_token(token)
        if not is_valid:
            lambda_logger.warning("Token validation failed", extra={
                'connection_id': connection_id,
                'error': error_message
            })
            return {
                'statusCode': 401,
                'body': json.dumps({'error': error_message})
            }
        
        # Create connection object
        connection = WebSocketConnection.from_auth_context(connection_id, user_data)
        
        # Set connection metadata
        request_context = event.get('requestContext', {})
        identity = request_context.get('identity', {})
        
        connection.set_metadata(
            user_agent=identity.get('userAgent'),
            ip_address=identity.get('sourceIp')
        )
        
        # Store connection in DynamoDB
        if shared_available:
            db_client = DynamoDBClient(config.connections_table)
            db_client.put_item(connection.to_dynamodb_item())

        # Update user presence to online
        presence_metadata = {
            'userAgent': identity.get('userAgent'),
            'ipAddress': identity.get('sourceIp'),
            'connectedAt': datetime.utcnow().isoformat()
        }

        presence_success, presence_error = presence_manager.handle_user_connect(
            connection_id=connection_id,
            user_id=user_data['user_id'],
            tenant_id=user_data['tenant_id'],
            metadata=presence_metadata
        )

        if not presence_success:
            lambda_logger.warning("Failed to update user presence on connect", extra={
                'connection_id': connection_id,
                'user_id': user_data['user_id'],
                'error': presence_error
            })

        lambda_logger.info("WebSocket connection established", extra={
            'connection_id': connection_id,
            'user_id': user_data['user_id'],
            'tenant_id': user_data['tenant_id'],
            'email': user_data['email'],
            'presence_updated': presence_success
        })
        
        # Return success response
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Connected successfully',
                'connectionId': connection_id,
                'userId': user_data['user_id'],
                'tenantId': user_data['tenant_id']
            })
        }
        
    except Exception as e:
        lambda_logger.error("WebSocket connection error", extra={
            'connection_id': connection_id,
            'request_id': request_id,
            'error': str(e)
        })
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'message': 'Failed to establish connection'
            })
        }
