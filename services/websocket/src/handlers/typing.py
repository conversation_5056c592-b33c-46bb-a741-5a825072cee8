# services/websocket/src/handlers/typing.py
# Dedicated handler for typing indicators

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()

from ..utils.validation import WebSocketValidator
from ..models.websocket_message import WebSocketMessage
from ..services.connection_manager import connection_manager
from ..services.typing_manager import typing_manager

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle typing indicator WebSocket messages
    
    This handler processes typing start/stop events and manages
    real-time typing indicators for conversations.
    """
    
    connection_id = event.get('requestContext', {}).get('connectionId')
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.debug("Typing indicator request", extra={
        'connection_id': connection_id,
        'request_id': request_id
    })
    
    try:
        # Get connection information
        connection = connection_manager.get_connection(connection_id)
        if not connection:
            lambda_logger.warning("Connection not found for typing indicator", extra={
                'connection_id': connection_id
            })
            return {
                'statusCode': 404,
                'body': json.dumps({
                    'error': 'Connection not found',
                    'action': 'typing_error'
                })
            }
        
        # Parse WebSocket message
        body = event.get('body', '{}')
        try:
            message_data = json.loads(body)
        except json.JSONDecodeError:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'Invalid JSON in message body',
                    'action': 'typing_error'
                })
            }
        
        # Validate message structure
        is_valid, error_message = WebSocketValidator.validate_typing_message(message_data)
        if not is_valid:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': error_message,
                    'action': 'typing_error'
                })
            }
        
        # Extract typing data
        conversation_id = message_data.get('conversationId')
        is_typing = message_data.get('isTyping', False)
        duration = message_data.get('duration', 30)  # Default 30 seconds
        
        # Validate duration
        if duration < 5 or duration > 300:  # 5 seconds to 5 minutes
            duration = 30
        
        # Process typing indicator
        if is_typing:
            success, error_msg = typing_manager.start_typing(
                conversation_id=conversation_id,
                user_id=connection.user_id,
                tenant_id=connection.tenant_id,
                connection_id=connection_id,
                duration_seconds=duration
            )
            action = 'typing_started'
        else:
            success, error_msg = typing_manager.stop_typing(
                conversation_id=conversation_id,
                user_id=connection.user_id,
                tenant_id=connection.tenant_id,
                connection_id=connection_id
            )
            action = 'typing_stopped'
        
        if not success:
            lambda_logger.warning("Failed to process typing indicator", extra={
                'connection_id': connection_id,
                'user_id': connection.user_id,
                'conversation_id': conversation_id,
                'is_typing': is_typing,
                'error': error_msg
            })
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': error_msg,
                    'action': 'typing_error'
                })
            }
        
        # Return success response
        response_data = {
            'action': action,
            'conversationId': conversation_id,
            'userId': connection.user_id,
            'isTyping': is_typing,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        if is_typing:
            response_data['duration'] = duration
        
        lambda_logger.debug("Typing indicator processed successfully", extra={
            'connection_id': connection_id,
            'user_id': connection.user_id,
            'conversation_id': conversation_id,
            'is_typing': is_typing,
            'action': action
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps(response_data)
        }
        
    except Exception as e:
        lambda_logger.error("Typing indicator handler error", extra={
            'connection_id': connection_id,
            'request_id': request_id,
            'error': str(e)
        })
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'action': 'typing_error'
            })
        }


def get_typing_status_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get current typing status for a conversation
    
    This handler returns the list of users currently typing in a conversation.
    """
    
    connection_id = event.get('requestContext', {}).get('connectionId')
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.debug("Get typing status request", extra={
        'connection_id': connection_id,
        'request_id': request_id
    })
    
    try:
        # Get connection information
        connection = connection_manager.get_connection(connection_id)
        if not connection:
            return {
                'statusCode': 404,
                'body': json.dumps({
                    'error': 'Connection not found',
                    'action': 'typing_status_error'
                })
            }
        
        # Parse request
        body = event.get('body', '{}')
        try:
            message_data = json.loads(body)
        except json.JSONDecodeError:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'Invalid JSON in message body',
                    'action': 'typing_status_error'
                })
            }
        
        conversation_id = message_data.get('conversationId')
        if not conversation_id:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'Missing conversationId',
                    'action': 'typing_status_error'
                })
            }
        
        # Get typing users
        typing_users = typing_manager.get_typing_users(conversation_id)
        
        # Filter out current user from the list
        typing_users = [
            user for user in typing_users 
            if user['userId'] != connection.user_id
        ]
        
        response_data = {
            'action': 'typing_status',
            'conversationId': conversation_id,
            'typingUsers': typing_users,
            'count': len(typing_users),
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.debug("Typing status retrieved", extra={
            'connection_id': connection_id,
            'conversation_id': conversation_id,
            'typing_count': len(typing_users)
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps(response_data)
        }
        
    except Exception as e:
        lambda_logger.error("Get typing status error", extra={
            'connection_id': connection_id,
            'request_id': request_id,
            'error': str(e)
        })
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'action': 'typing_status_error'
            })
        }
