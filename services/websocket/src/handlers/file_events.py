# services/websocket/src/handlers/file_events.py
# WebSocket handler for file-related events

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..services.connection_manager import connection_manager
from ..services.message_router import message_router

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle file-related WebSocket events
    
    Supported events:
    - file_upload_progress
    - file_download_request
    - file_preview_request
    """
    
    connection_id = event.get('requestContext', {}).get('connectionId')
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("File event received", extra={
        'connection_id': connection_id,
        'request_id': request_id
    })
    
    try:
        # Get connection info
        connection = connection_manager.get_connection(connection_id)
        if not connection:
            lambda_logger.warning("Connection not found for file event", extra={
                'connection_id': connection_id
            })
            return {'statusCode': 404}
        
        # Parse message body
        body = json.loads(event.get('body', '{}'))
        event_type = body.get('type')
        event_data = body.get('data', {})
        
        # Route file event
        if event_type == 'file_upload_progress':
            return handle_file_upload_progress(connection, event_data)
        elif event_type == 'file_download_request':
            return handle_file_download_request(connection, event_data)
        elif event_type == 'file_preview_request':
            return handle_file_preview_request(connection, event_data)
        elif event_type == 'file_share_request':
            return handle_file_share_request(connection, event_data)
        else:
            lambda_logger.warning("Unknown file event type", extra={
                'connection_id': connection_id,
                'event_type': event_type
            })
            return {
                'statusCode': 400,
                'body': json.dumps({'error': f'Unknown event type: {event_type}'})
            }
    
    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'body': json.dumps({'error': 'Invalid JSON'})
        }
    except Exception as e:
        lambda_logger.error("File event handler error", extra={
            'connection_id': connection_id,
            'request_id': request_id,
            'error': str(e)
        })
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Internal server error'})
        }


def handle_file_upload_progress(connection: Any, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle file upload progress updates"""
    try:
        file_id = event_data.get('fileId')
        progress = event_data.get('progress', 0)
        conversation_id = event_data.get('conversationId')
        
        if not file_id or not conversation_id:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing fileId or conversationId'})
            }
        
        # Validate progress
        if not isinstance(progress, (int, float)) or progress < 0 or progress > 100:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Invalid progress value'})
            }
        
        # Prepare progress notification
        progress_notification = {
            'type': 'file_upload_progress',
            'data': {
                'fileId': file_id,
                'progress': progress,
                'conversationId': conversation_id,
                'uploadedBy': connection.user_id,
                'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
            }
        }
        
        # Broadcast to conversation participants
        success, error_msg = message_router.broadcast_to_conversation(
            conversation_id=conversation_id,
            message=progress_notification,
            sender_connection_id=connection.connection_id,
            tenant_id=connection.tenant_id
        )
        
        if not success:
            lambda_logger.warning("Failed to broadcast upload progress", extra={
                'file_id': file_id,
                'conversation_id': conversation_id,
                'error': error_msg
            })
        
        lambda_logger.debug("File upload progress handled", extra={
            'file_id': file_id,
            'progress': progress,
            'conversation_id': conversation_id,
            'user_id': connection.user_id
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'type': 'file_upload_progress_ack',
                'fileId': file_id,
                'progress': progress
            })
        }
        
    except Exception as e:
        lambda_logger.error("Failed to handle file upload progress", extra={
            'connection_id': connection.connection_id,
            'event_data': event_data,
            'error': str(e)
        })
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Failed to handle upload progress'})
        }


def handle_file_download_request(connection: Any, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle file download requests"""
    try:
        file_id = event_data.get('fileId')
        
        if not file_id:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing fileId'})
            }
        
        # This would call Chat Service to get download URL
        # For now, return mock response
        download_response = {
            'type': 'file_download_response',
            'data': {
                'fileId': file_id,
                'downloadUrl': f'https://example.com/download/{file_id}',
                'expiresIn': 3600,
                'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
            }
        }
        
        # Send response back to requesting user
        success, error_msg = connection_manager.send_to_connection(
            connection_id=connection.connection_id,
            message=download_response
        )
        
        if not success:
            lambda_logger.warning("Failed to send download response", extra={
                'file_id': file_id,
                'connection_id': connection.connection_id,
                'error': error_msg
            })
        
        lambda_logger.debug("File download request handled", extra={
            'file_id': file_id,
            'user_id': connection.user_id
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'type': 'file_download_request_ack',
                'fileId': file_id
            })
        }
        
    except Exception as e:
        lambda_logger.error("Failed to handle file download request", extra={
            'connection_id': connection.connection_id,
            'event_data': event_data,
            'error': str(e)
        })
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Failed to handle download request'})
        }


def handle_file_preview_request(connection: Any, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle file preview requests"""
    try:
        file_id = event_data.get('fileId')
        preview_type = event_data.get('previewType', 'thumbnail')
        
        if not file_id:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing fileId'})
            }
        
        # Validate preview type
        allowed_preview_types = ['thumbnail', 'preview', 'metadata']
        if preview_type not in allowed_preview_types:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': f'Invalid preview type. Allowed: {allowed_preview_types}'})
            }
        
        # This would call Chat Service to get preview data
        # For now, return mock response
        preview_response = {
            'type': 'file_preview_response',
            'data': {
                'fileId': file_id,
                'previewType': preview_type,
                'previewUrl': f'https://example.com/preview/{file_id}?type={preview_type}',
                'metadata': {
                    'width': 800,
                    'height': 600,
                    'format': 'jpeg'
                } if preview_type == 'metadata' else None,
                'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
            }
        }
        
        # Send response back to requesting user
        success, error_msg = connection_manager.send_to_connection(
            connection_id=connection.connection_id,
            message=preview_response
        )
        
        if not success:
            lambda_logger.warning("Failed to send preview response", extra={
                'file_id': file_id,
                'preview_type': preview_type,
                'connection_id': connection.connection_id,
                'error': error_msg
            })
        
        lambda_logger.debug("File preview request handled", extra={
            'file_id': file_id,
            'preview_type': preview_type,
            'user_id': connection.user_id
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'type': 'file_preview_request_ack',
                'fileId': file_id,
                'previewType': preview_type
            })
        }
        
    except Exception as e:
        lambda_logger.error("Failed to handle file preview request", extra={
            'connection_id': connection.connection_id,
            'event_data': event_data,
            'error': str(e)
        })
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Failed to handle preview request'})
        }


def handle_file_share_request(connection: Any, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle file sharing requests"""
    try:
        file_id = event_data.get('fileId')
        share_with = event_data.get('shareWith', [])
        conversation_id = event_data.get('conversationId')
        
        if not file_id or not share_with:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing fileId or shareWith'})
            }
        
        # Validate share_with is a list
        if not isinstance(share_with, list):
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'shareWith must be an array'})
            }
        
        # Prepare share notification
        share_notification = {
            'type': 'file_shared',
            'data': {
                'fileId': file_id,
                'sharedBy': connection.user_id,
                'sharedWith': share_with,
                'conversationId': conversation_id,
                'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
            }
        }
        
        # Send to specified users
        success_count = 0
        for user_id in share_with:
            success, _ = message_router.send_to_user(
                user_id=user_id,
                message=share_notification,
                tenant_id=connection.tenant_id
            )
            if success:
                success_count += 1
        
        lambda_logger.info("File share request handled", extra={
            'file_id': file_id,
            'shared_by': connection.user_id,
            'share_with_count': len(share_with),
            'successful_shares': success_count
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'type': 'file_share_request_ack',
                'fileId': file_id,
                'sharedWith': share_with,
                'successfulShares': success_count
            })
        }
        
    except Exception as e:
        lambda_logger.error("Failed to handle file share request", extra={
            'connection_id': connection.connection_id,
            'event_data': event_data,
            'error': str(e)
        })
        return {
            'statusCode': 500,
            'body': json.dumps({'error': 'Failed to handle share request'})
        }
