# services/websocket/src/handlers/message.py
# WebSocket message handler

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.validation import WebSocketValidator
from ..models.websocket_message import WebSocketMessage
from ..services.connection_manager import connection_manager
from ..services.message_router import message_router
from ..services.typing_manager import typing_manager

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle WebSocket messages
    
    This function processes incoming WebSocket messages and routes them
    to appropriate handlers based on the action type.
    """
    
    connection_id = event.get('requestContext', {}).get('connectionId')
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    body = event.get('body', '{}')
    
    lambda_logger.info("WebSocket message received", extra={
        'connection_id': connection_id,
        'request_id': request_id,
        'body_length': len(body) if body else 0
    })
    
    try:
        # Get connection information
        connection = connection_manager.get_connection(connection_id)
        if not connection:
            lambda_logger.warning("Connection not found", extra={
                'connection_id': connection_id
            })
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Connection not found'})
            }
        
        # Update connection activity
        connection_manager.update_connection_activity(connection_id)
        
        # Validate and parse JSON payload
        is_valid, payload, error_message = WebSocketValidator.validate_json_payload(body)
        if not is_valid:
            lambda_logger.warning("Invalid JSON payload", extra={
                'connection_id': connection_id,
                'error': error_message
            })
            return {
                'statusCode': 400,
                'body': json.dumps({'error': error_message})
            }
        
        # Validate message payload
        is_valid, error_message = WebSocketValidator.validate_message_payload(payload)
        if not is_valid:
            lambda_logger.warning("Invalid message payload", extra={
                'connection_id': connection_id,
                'error': error_message
            })
            return {
                'statusCode': 400,
                'body': json.dumps({'error': error_message})
            }
        
        # Create WebSocket message
        ws_message = WebSocketMessage.from_payload(
            payload,
            user_id=connection.user_id,
            tenant_id=connection.tenant_id,
            connection_id=connection_id
        )
        
        # Route message based on action
        action = payload.get('action')
        
        if action == 'ping':
            response = handle_ping(ws_message, connection)
        elif action == 'send_message':
            response = handle_send_message(ws_message, connection)
        elif action == 'typing_indicator':
            response = handle_typing_indicator(ws_message, connection)
        elif action == 'presence_update':
            response = handle_presence_update(ws_message, connection)
        elif action == 'message_status_update':
            response = handle_message_status_update(ws_message, connection)
        else:
            lambda_logger.warning("Unknown action", extra={
                'connection_id': connection_id,
                'action': action
            })
            response = {
                'statusCode': 400,
                'body': json.dumps({'error': f'Unknown action: {action}'})
            }
        
        lambda_logger.info("WebSocket message processed", extra={
            'connection_id': connection_id,
            'action': action,
            'message_id': ws_message.message_id,
            'user_id': connection.user_id
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("WebSocket message processing error", extra={
            'connection_id': connection_id,
            'request_id': request_id,
            'error': str(e)
        })
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'message': 'Failed to process message'
            })
        }


def handle_ping(ws_message: WebSocketMessage, connection) -> Dict[str, Any]:
    """Handle ping message"""
    lambda_logger.debug("Handling ping", extra={
        'connection_id': connection.connection_id,
        'user_id': connection.user_id
    })
    
    # TODO: Send pong response back to client
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'action': 'pong',
            'messageId': ws_message.message_id,
            'timestamp': ws_message.timestamp.isoformat()
        })
    }


def handle_send_message(ws_message: WebSocketMessage, connection) -> Dict[str, Any]:
    """Handle send message action"""
    lambda_logger.info("Handling send message", extra={
        'connection_id': connection.connection_id,
        'user_id': connection.user_id,
        'conversation_id': ws_message.data.get('conversationId')
    })

    try:
        # Extract message data
        conversation_id = ws_message.data.get('conversationId')
        content = ws_message.data.get('content')
        message_type = ws_message.data.get('type', 'text')
        attachments = ws_message.data.get('attachments', [])

        # Validate required fields
        if not conversation_id or not content:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'action': 'message_error',
                    'messageId': ws_message.message_id,
                    'error': 'Missing conversationId or content'
                })
            }

        # Call Chat Service to create and route message
        success, response_data = send_message_via_chat_service(
            conversation_id=conversation_id,
            user_id=connection.user_id,
            tenant_id=connection.tenant_id,
            content=content,
            message_type=message_type,
            attachments=attachments,
            websocket_message_id=ws_message.message_id
        )

        if success:
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'action': 'message_sent',
                    'messageId': response_data.get('messageId', ws_message.message_id),
                    'conversationId': conversation_id,
                    'status': 'sent',
                    'timestamp': response_data.get('timestamp'),
                    'routing': response_data.get('routing', {})
                })
            }
        else:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'action': 'message_error',
                    'messageId': ws_message.message_id,
                    'error': response_data.get('error', 'Failed to send message')
                })
            }

    except Exception as e:
        lambda_logger.error("Error handling send message", extra={
            'connection_id': connection.connection_id,
            'user_id': connection.user_id,
            'error': str(e)
        })

        return {
            'statusCode': 500,
            'body': json.dumps({
                'action': 'message_error',
                'messageId': ws_message.message_id,
                'error': 'Internal server error'
            })
        }


def handle_typing_indicator(ws_message: WebSocketMessage, connection) -> Dict[str, Any]:
    """Handle typing indicator"""
    lambda_logger.debug("Handling typing indicator", extra={
        'connection_id': connection.connection_id,
        'user_id': connection.user_id,
        'is_typing': ws_message.data.get('isTyping')
    })

    try:
        conversation_id = ws_message.data.get('conversationId')
        is_typing = ws_message.data.get('isTyping', False)
        duration = ws_message.data.get('duration', 30)  # Default 30 seconds

        if not conversation_id:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'action': 'typing_error',
                    'messageId': ws_message.message_id,
                    'error': 'Missing conversationId'
                })
            }

        # Use typing manager to handle indicator
        if is_typing:
            success, error_msg = typing_manager.start_typing(
                conversation_id=conversation_id,
                user_id=connection.user_id,
                tenant_id=connection.tenant_id,
                connection_id=connection.connection_id,
                duration_seconds=duration
            )
        else:
            success, error_msg = typing_manager.stop_typing(
                conversation_id=conversation_id,
                user_id=connection.user_id,
                tenant_id=connection.tenant_id,
                connection_id=connection.connection_id
            )

        if not success:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'action': 'typing_error',
                    'messageId': ws_message.message_id,
                    'error': error_msg
                })
            }

        lambda_logger.debug("Typing indicator processed", extra={
            'conversation_id': conversation_id,
            'user_id': connection.user_id,
            'is_typing': is_typing,
            'success': success
        })

        return {
            'statusCode': 200,
            'body': json.dumps({
                'action': 'typing_indicator_processed',
                'messageId': ws_message.message_id,
                'conversationId': conversation_id,
                'isTyping': is_typing,
                'success': success
            })
        }

    except Exception as e:
        lambda_logger.error("Error handling typing indicator", extra={
            'connection_id': connection.connection_id,
            'user_id': connection.user_id,
            'error': str(e)
        })

        return {
            'statusCode': 500,
            'body': json.dumps({
                'action': 'typing_error',
                'messageId': ws_message.message_id,
                'error': 'Internal server error'
            })
        }


def handle_presence_update(ws_message: WebSocketMessage, connection) -> Dict[str, Any]:
    """Handle presence update"""
    lambda_logger.debug("Handling presence update", extra={
        'connection_id': connection.connection_id,
        'user_id': connection.user_id,
        'status': ws_message.data.get('status')
    })

    try:
        status = ws_message.data.get('status')

        if not status:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'action': 'presence_error',
                    'messageId': ws_message.message_id,
                    'error': 'Missing status'
                })
            }

        # Validate status
        valid_statuses = ['online', 'offline', 'away', 'busy']
        if status not in valid_statuses:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'action': 'presence_error',
                    'messageId': ws_message.message_id,
                    'error': f'Invalid status. Must be one of: {valid_statuses}'
                })
            }

        # Route presence update to relevant users
        sent_count, failed_count = message_router.route_presence_update(
            user_id=connection.user_id,
            status=status,
            tenant_id=connection.tenant_id,
            sender_connection_id=connection.connection_id
        )

        lambda_logger.debug("Presence update routed", extra={
            'user_id': connection.user_id,
            'status': status,
            'tenant_id': connection.tenant_id,
            'sent_count': sent_count,
            'failed_count': failed_count
        })

        return {
            'statusCode': 200,
            'body': json.dumps({
                'action': 'presence_updated',
                'messageId': ws_message.message_id,
                'status': status,
                'sentCount': sent_count,
                'failedCount': failed_count
            })
        }

    except Exception as e:
        lambda_logger.error("Error handling presence update", extra={
            'connection_id': connection.connection_id,
            'user_id': connection.user_id,
            'error': str(e)
        })

        return {
            'statusCode': 500,
            'body': json.dumps({
                'action': 'presence_error',
                'messageId': ws_message.message_id,
                'error': 'Internal server error'
            })
        }


def handle_message_status_update(ws_message: WebSocketMessage, connection) -> Dict[str, Any]:
    """Handle message status update"""
    lambda_logger.debug("Handling message status update", extra={
        'connection_id': connection.connection_id,
        'user_id': connection.user_id,
        'message_id': ws_message.data.get('messageId'),
        'status': ws_message.data.get('status')
    })
    
    # TODO: Update message status
    # TODO: Notify sender about status change
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'action': 'message_status_updated',
            'messageId': ws_message.message_id
        })
    }


def send_message_via_chat_service(conversation_id: str, user_id: str, tenant_id: str,
                                content: str, message_type: str = 'text',
                                attachments: list = None, websocket_message_id: str = None) -> tuple:
    """
    Send message via Chat Service API

    Returns:
        (success, response_data)
    """
    try:
        import boto3
        import os
        from datetime import datetime

        # Get Chat Service function name
        chat_function_name = f"agent-scl-chat-{os.environ.get('ENVIRONMENT', 'dev')}-sendMessage"

        # Prepare payload for Chat Service
        payload = {
            'httpMethod': 'POST',
            'path': '/chat/messages',
            'headers': {
                'Content-Type': 'application/json'
            },
            'requestContext': {
                'authorizer': {
                    'userId': user_id,
                    'tenantId': tenant_id,
                    'email': f'{user_id}@example.com'  # Placeholder
                }
            },
            'body': json.dumps({
                'conversationId': conversation_id,
                'content': content,
                'type': message_type,
                'attachments': attachments or [],
                'websocketMessageId': websocket_message_id
            })
        }

        # Invoke Chat Service
        if shared_available:
            lambda_client = boto3.client('lambda')
            response = lambda_client.invoke(
                FunctionName=chat_function_name,
                InvocationType='RequestResponse',
                Payload=json.dumps(payload)
            )

            # Parse response
            response_payload = json.loads(response['Payload'].read())

            if response_payload.get('statusCode') == 201:
                # Success
                body = json.loads(response_payload.get('body', '{}'))
                return True, body
            else:
                # Error
                body = json.loads(response_payload.get('body', '{}'))
                return False, body
        else:
            # Mock response for development
            return True, {
                'messageId': websocket_message_id,
                'timestamp': datetime.utcnow().isoformat(),
                'routing': {'method': 'mock'}
            }

    except Exception as e:
        lambda_logger.error("Failed to call Chat Service", extra={
            'conversation_id': conversation_id,
            'user_id': user_id,
            'error': str(e)
        })
        return False, {'error': str(e)}
