# services/websocket/src/handlers/auth.py
# WebSocket JWT authorizer handler

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.auth import websocket_auth
from ..utils.validation import WebSocketValidator

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    WebSocket JWT authorizer
    
    This function validates JWT tokens for WebSocket connections
    and returns an authorization policy for API Gateway.
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    method_arn = event.get('methodArn', '')
    
    lambda_logger.info("WebSocket authorization request", extra={
        'request_id': request_id,
        'method_arn': method_arn,
        'event_keys': list(event.keys())
    })
    
    try:
        # Extract token from event
        token = websocket_auth.extract_token_from_event(event)
        
        if not token:
            lambda_logger.warning("No token provided for WebSocket authorization", extra={
                'request_id': request_id
            })
            
            # Return deny policy
            return websocket_auth.create_authorizer_response(
                effect='Deny',
                resource=method_arn,
                principal_id='unauthorized'
            )
        
        # Validate token
        is_valid, user_data, error_message = websocket_auth.validate_token(token)
        
        if not is_valid:
            lambda_logger.warning("Token validation failed for WebSocket", extra={
                'request_id': request_id,
                'error': error_message
            })
            
            # Return deny policy
            return websocket_auth.create_authorizer_response(
                effect='Deny',
                resource=method_arn,
                principal_id='unauthorized'
            )
        
        # Create authorization context
        auth_context = {
            'userId': user_data['user_id'],
            'tenantId': user_data['tenant_id'],
            'email': user_data['email'],
            'role': user_data['role'],
            'permissions': user_data['permissions'],
            'tokenExp': str(user_data.get('exp', '')),
            'tokenIat': str(user_data.get('iat', ''))
        }
        
        lambda_logger.info("WebSocket authorization successful", extra={
            'request_id': request_id,
            'user_id': user_data['user_id'],
            'tenant_id': user_data['tenant_id'],
            'role': user_data['role']
        })
        
        # Return allow policy with context
        return websocket_auth.create_authorizer_response(
            effect='Allow',
            resource=method_arn,
            principal_id=user_data['user_id'],
            context=auth_context
        )
        
    except Exception as e:
        lambda_logger.error("WebSocket authorization error", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        # Return deny policy on any error
        return websocket_auth.create_authorizer_response(
            effect='Deny',
            resource=method_arn,
            principal_id='error'
        )
