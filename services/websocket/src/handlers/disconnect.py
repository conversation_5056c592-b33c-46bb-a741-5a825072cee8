# services/websocket/src/handlers/disconnect.py
# WebSocket disconnection handler

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.database import DynamoDBClient
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class MockDynamoDBClient:
        def __init__(self, table_name): self.table_name = table_name
        def get_item(self, pk, sk): return None
        def delete_item(self, pk, sk): pass
    
    DynamoDBClient = MockDynamoDBClient

from ..utils.config import config
from ..models.connection import WebSocketConnection
from ..services.presence_manager import presence_manager

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle WebSocket disconnection
    
    This function is called when a client disconnects from the WebSocket API.
    It cleans up the connection information and updates presence status.
    """
    
    connection_id = event.get('requestContext', {}).get('connectionId')
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("WebSocket disconnection", extra={
        'connection_id': connection_id,
        'request_id': request_id
    })
    
    try:
        # Get connection information before deletion
        connection_data = None
        if shared_available:
            db_client = DynamoDBClient(config.connections_table)
            connection_item = db_client.get_item(connection_id, connection_id)
            
            if connection_item:
                connection_data = WebSocketConnection.from_dynamodb_item(connection_item)
                
                lambda_logger.info("Connection found for cleanup", extra={
                    'connection_id': connection_id,
                    'user_id': connection_data.user_id,
                    'tenant_id': connection_data.tenant_id,
                    'connection_duration': str(connection_data.get_connection_duration())
                })
            else:
                lambda_logger.warning("Connection not found in database", extra={
                    'connection_id': connection_id
                })
        
        # Update user presence before deleting connection
        if connection_data:
            presence_success, presence_error = presence_manager.handle_user_disconnect(
                connection_id=connection_id,
                user_id=connection_data.user_id,
                tenant_id=connection_data.tenant_id
            )

            if not presence_success:
                lambda_logger.warning("Failed to update user presence on disconnect", extra={
                    'connection_id': connection_id,
                    'user_id': connection_data.user_id,
                    'error': presence_error
                })

        # Delete connection from DynamoDB
        if shared_available:
            db_client.delete_item(connection_id, connection_id)

        # Log successful disconnection
        if connection_data:
            lambda_logger.info("WebSocket disconnection completed", extra={
                'connection_id': connection_id,
                'user_id': connection_data.user_id,
                'tenant_id': connection_data.tenant_id,
                'total_duration': str(connection_data.get_connection_duration()),
                'presence_updated': presence_success if 'presence_success' in locals() else False
            })
        else:
            lambda_logger.info("WebSocket disconnection completed", extra={
                'connection_id': connection_id,
                'note': 'Connection data not found'
            })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Disconnected successfully',
                'connectionId': connection_id
            })
        }
        
    except Exception as e:
        lambda_logger.error("WebSocket disconnection error", extra={
            'connection_id': connection_id,
            'request_id': request_id,
            'error': str(e)
        })
        
        # Even if there's an error, we should return success
        # because the connection is being closed anyway
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Disconnection processed',
                'connectionId': connection_id,
                'note': 'Error occurred during cleanup but connection closed'
            })
        }
