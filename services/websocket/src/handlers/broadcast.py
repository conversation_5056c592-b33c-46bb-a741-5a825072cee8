# services/websocket/src/handlers/broadcast.py
# Handler for broadcasting messages from the orchestrator

"""
Broadcast Handler for WebSocket Service

This handler receives broadcast requests from the Message Orchestrator and
sends real-time notifications to connected users. It handles:
1. Message notifications
2. Agent response notifications
3. Typing indicators
4. Presence updates

POST /websocket/broadcast
{
    "recipients": ["user-123", "user-456"],
    "notification": {
        "type": "message_notification",
        "message": { ... },
        "conversation": { ... },
        "routing_info": { ... }
    },
    "tenant_id": "tenant-789"
}
"""

from ..common.imports import *

from ..utils.config import config
from ..services.connection_manager import connection_manager
from ..models.websocket_message import WebSocketMessage

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle WebSocket message broadcasting
    
    This function broadcasts messages to multiple WebSocket connections.
    It can be invoked directly or triggered by other services.
    """
    
    request_id = event.get('requestId', 'unknown')
    
    lambda_logger.info("WebSocket broadcast request", extra={
        'request_id': request_id,
        'event_keys': list(event.keys())
    })
    
    try:
        # Parse broadcast request
        if 'body' in event:
            # HTTP invocation
            body = json.loads(event['body']) if isinstance(event['body'], str) else event['body']
        else:
            # Direct invocation
            body = event
        
        # Extract broadcast parameters
        message = body.get('message', {})
        target_type = body.get('targetType', 'user')  # user, tenant, conversation, all
        targets = body.get('targets', [])  # List of target IDs
        sender_connection_id = body.get('senderConnectionId')  # Exclude sender
        
        # Validate required fields
        if not message:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Message is required'})
            }
        
        if not targets and target_type != 'all':
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Targets are required'})
            }
        
        # Get target connections
        target_connections = get_target_connections(target_type, targets, sender_connection_id)
        
        if not target_connections:
            lambda_logger.info("No target connections found", extra={
                'target_type': target_type,
                'targets': targets
            })
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'No target connections found',
                    'sent_count': 0
                })
            }
        
        # Broadcast message to connections
        sent_count, failed_count = broadcast_to_connections(target_connections, message)
        
        lambda_logger.info("Broadcast completed", extra={
            'target_type': target_type,
            'total_targets': len(target_connections),
            'sent_count': sent_count,
            'failed_count': failed_count
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Broadcast completed',
                'sent_count': sent_count,
                'failed_count': failed_count,
                'total_targets': len(target_connections)
            })
        }
        
    except Exception as e:
        lambda_logger.error("WebSocket broadcast error", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'message': 'Failed to broadcast message'
            })
        }


def get_target_connections(target_type: str, targets: List[str],
                         sender_connection_id: str = None) -> List[Dict[str, Any]]:
    """Get target connections based on type and targets"""

    all_connections = []

    try:
        if target_type == 'user':
            # Get connections for specific users
            for user_id in targets:
                user_connections = connection_manager.get_user_connections(user_id)
                all_connections.extend(user_connections)

        elif target_type == 'tenant':
            # Get connections for specific tenants
            for tenant_id in targets:
                tenant_connections = connection_manager.get_tenant_connections(tenant_id)
                all_connections.extend(tenant_connections)

        elif target_type == 'conversation':
            # Get connections for conversation participants
            for conversation_id in targets:
                conversation_connections = get_conversation_connections(conversation_id)
                all_connections.extend(conversation_connections)

        elif target_type == 'all':
            # Get all active connections (admin broadcast)
            lambda_logger.warning("All connections targeting requires admin privileges")
            return []

        else:
            lambda_logger.error("Unknown target type", extra={'target_type': target_type})
            return []

        # Filter out sender connection if specified
        if sender_connection_id:
            all_connections = [
                conn for conn in all_connections
                if conn.connection_id != sender_connection_id
            ]

        # Convert to dict format for broadcasting
        connection_dicts = []
        for conn in all_connections:
            connection_dicts.append({
                'connectionId': conn.connection_id,
                'userId': conn.user_id,
                'tenantId': conn.tenant_id
            })

        # Remove duplicates based on connection ID
        seen_connections = set()
        unique_connections = []
        for conn in connection_dicts:
            conn_id = conn['connectionId']
            if conn_id not in seen_connections:
                seen_connections.add(conn_id)
                unique_connections.append(conn)

        return unique_connections

    except Exception as e:
        lambda_logger.error("Failed to get target connections", extra={
            'target_type': target_type,
            'targets': targets,
            'error': str(e)
        })
        return []


def broadcast_to_connections(connections: List[Dict[str, Any]], 
                           message: Dict[str, Any]) -> tuple[int, int]:
    """Broadcast message to list of connections"""
    
    sent_count = 0
    failed_count = 0
    
    # Prepare message payload
    message_payload = json.dumps(message)
    
    for connection in connections:
        connection_id = connection['connectionId']
        
        try:
            # Send message to connection
            if shared_available:
                apigateway_management.post_to_connection(
                    ConnectionId=connection_id,
                    Data=message_payload
                )
            
            sent_count += 1
            
            lambda_logger.debug("Message sent to connection", extra={
                'connection_id': connection_id,
                'user_id': connection.get('userId'),
                'tenant_id': connection.get('tenantId')
            })
            
        except apigateway_management.exceptions.GoneException:
            # Connection is no longer available
            lambda_logger.warning("Connection gone, removing from database", extra={
                'connection_id': connection_id
            })
            
            # Remove stale connection
            connection_manager.remove_connection(connection_id)
            failed_count += 1
            
        except Exception as e:
            lambda_logger.error("Failed to send message to connection", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            failed_count += 1
    
    return sent_count, failed_count


def send_to_user(user_id: str, message: Dict[str, Any], 
                sender_connection_id: str = None) -> Dict[str, Any]:
    """Helper function to send message to specific user"""
    
    return handler({
        'message': message,
        'targetType': 'user',
        'targets': [user_id],
        'senderConnectionId': sender_connection_id
    }, None)


def send_to_tenant(tenant_id: str, message: Dict[str, Any], 
                  sender_connection_id: str = None) -> Dict[str, Any]:
    """Helper function to send message to all users in tenant"""
    
    return handler({
        'message': message,
        'targetType': 'tenant',
        'targets': [tenant_id],
        'senderConnectionId': sender_connection_id
    }, None)


def send_to_conversation(conversation_id: str, message: Dict[str, Any],
                        sender_connection_id: str = None) -> Dict[str, Any]:
    """Helper function to send message to conversation participants"""

    return handler({
        'message': message,
        'targetType': 'conversation',
        'targets': [conversation_id],
        'senderConnectionId': sender_connection_id
    }, None)


def get_conversation_connections(conversation_id: str):
    """Get connections for conversation participants"""
    try:
        import boto3
        import os

        # Get conversation participants from Agent Service
        participants = get_conversation_participants_from_agent_service(conversation_id)

        # Get connections for all participants
        all_connections = []
        for participant_id in participants:
            user_connections = connection_manager.get_user_connections(participant_id)
            all_connections.extend(user_connections)

        return all_connections

    except Exception as e:
        lambda_logger.error("Failed to get conversation connections", extra={
            'conversation_id': conversation_id,
            'error': str(e)
        })
        return []


def get_conversation_participants_from_agent_service(conversation_id: str) -> List[str]:
    """Get conversation participants from Agent Service"""
    try:
        import boto3
        import os

        # Call Agent Service to get conversation details
        agent_function_name = f"agent-scl-agent-{os.environ.get('ENVIRONMENT', 'dev')}-getConversation"

        if shared_available:
            lambda_client = boto3.client('lambda')

            payload = {
                'httpMethod': 'GET',
                'pathParameters': {'id': conversation_id},
                'requestContext': {
                    'authorizer': {
                        'userId': 'system',
                        'tenantId': 'system'
                    }
                }
            }

            response = lambda_client.invoke(
                FunctionName=agent_function_name,
                InvocationType='RequestResponse',
                Payload=json.dumps(payload)
            )

            response_payload = json.loads(response['Payload'].read())

            if response_payload.get('statusCode') == 200:
                body = json.loads(response_payload.get('body', '{}'))
                conversation = body.get('conversation', {})

                # Extract participants
                participants = conversation.get('participants', [])
                created_by = conversation.get('createdBy')

                if created_by and created_by not in participants:
                    participants.append(created_by)

                return participants
            else:
                lambda_logger.warning("Failed to get conversation from Agent Service", extra={
                    'conversation_id': conversation_id,
                    'status_code': response_payload.get('statusCode')
                })
                return []
        else:
            # Mock participants for development
            return ['user1', 'user2']

    except Exception as e:
        lambda_logger.error("Failed to get conversation participants", extra={
            'conversation_id': conversation_id,
            'error': str(e)
        })
        return []
