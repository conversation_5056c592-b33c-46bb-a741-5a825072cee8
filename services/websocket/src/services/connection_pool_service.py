# services/websocket/src/services/connection_pool_service.py
# Connection pooling service for WebSocket connections

import time
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class WebSocketConnectionPool:
    """Optimized connection pool for WebSocket connections"""
    
    def __init__(self):
        self.pool_config = config.get_connection_pool_config()
        
        # Connection pools
        self.active_connections = {}      # connection_id -> connection_info
        self.tenant_connections = defaultdict(set)  # tenant_id -> set of connection_ids
        self.user_connections = defaultdict(set)    # user_id -> set of connection_ids
        self.conversation_connections = defaultdict(set)  # conversation_id -> set of connection_ids
        
        # Pool statistics
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'peak_connections': 0,
            'connections_created': 0,
            'connections_closed': 0,
            'last_cleanup': datetime.utcnow(),
            'cleanup_count': 0
        }
        
        # Configuration
        self.max_connections = self.pool_config.get('max_websocket_connections', 10000)
        self.connection_timeout = self.pool_config.get('connection_timeout', 300)  # 5 minutes
        self.cleanup_interval = self.pool_config.get('cleanup_interval', 60)  # 1 minute
        
        # Thread lock for thread safety
        self._lock = threading.Lock()
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    def add_connection(self, connection_id: str, user_id: str, tenant_id: str,
                      conversation_id: str = None, metadata: Dict[str, Any] = None) -> bool:
        """Add WebSocket connection to pool with optimized indexing"""
        try:
            with self._lock:
                # Check connection limits
                if len(self.active_connections) >= self.max_connections:
                    lambda_logger.warning("WebSocket connection limit reached", extra={
                        'current_connections': len(self.active_connections),
                        'max_connections': self.max_connections,
                        'connection_id': connection_id
                    })
                    return False
                
                # Create optimized connection info
                connection_info = {
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'conversation_id': conversation_id,
                    'connected_at': datetime.utcnow(),
                    'last_activity': datetime.utcnow(),
                    'message_count': 0,
                    'metadata': metadata or {}
                }
                
                # Add to optimized indexes
                self.active_connections[connection_id] = connection_info
                self.tenant_connections[tenant_id].add(connection_id)
                self.user_connections[user_id].add(connection_id)
                
                if conversation_id:
                    self.conversation_connections[conversation_id].add(connection_id)
                
                # Update statistics
                self.stats['total_connections'] += 1
                self.stats['active_connections'] = len(self.active_connections)
                self.stats['connections_created'] += 1
                
                if self.stats['active_connections'] > self.stats['peak_connections']:
                    self.stats['peak_connections'] = self.stats['active_connections']
                
                lambda_logger.info("WebSocket connection added to optimized pool", extra={
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'conversation_id': conversation_id,
                    'active_connections': self.stats['active_connections'],
                    'pool_utilization': f"{(self.stats['active_connections'] / self.max_connections * 100):.1f}%"
                })
                
                return True
                
        except Exception as e:
            lambda_logger.error("Failed to add connection to optimized pool", extra={
                'connection_id': connection_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def remove_connection(self, connection_id: str) -> bool:
        """Remove WebSocket connection from pool with cleanup"""
        try:
            with self._lock:
                connection_info = self.active_connections.get(connection_id)
                
                if not connection_info:
                    lambda_logger.debug("Connection not found in pool", extra={
                        'connection_id': connection_id
                    })
                    return False
                
                # Remove from all indexes
                del self.active_connections[connection_id]
                
                tenant_id = connection_info['tenant_id']
                user_id = connection_info['user_id']
                conversation_id = connection_info.get('conversation_id')
                
                self.tenant_connections[tenant_id].discard(connection_id)
                self.user_connections[user_id].discard(connection_id)
                
                if conversation_id:
                    self.conversation_connections[conversation_id].discard(connection_id)
                
                # Clean up empty sets
                if not self.tenant_connections[tenant_id]:
                    del self.tenant_connections[tenant_id]
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
                if conversation_id and not self.conversation_connections[conversation_id]:
                    del self.conversation_connections[conversation_id]
                
                # Update statistics
                self.stats['active_connections'] = len(self.active_connections)
                self.stats['connections_closed'] += 1
                
                # Calculate session duration
                session_duration = (datetime.utcnow() - connection_info['connected_at']).total_seconds()
                
                lambda_logger.info("WebSocket connection removed from pool", extra={
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'session_duration_seconds': session_duration,
                    'message_count': connection_info.get('message_count', 0),
                    'active_connections': self.stats['active_connections']
                })
                
                return True
                
        except Exception as e:
            lambda_logger.error("Failed to remove connection from pool", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return False
    
    def get_tenant_connections(self, tenant_id: str) -> List[str]:
        """Get all active connections for a tenant (optimized)"""
        try:
            with self._lock:
                return list(self.tenant_connections.get(tenant_id, set()))
        except Exception as e:
            lambda_logger.error("Failed to get tenant connections", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def get_user_connections(self, user_id: str) -> List[str]:
        """Get all active connections for a user (optimized)"""
        try:
            with self._lock:
                return list(self.user_connections.get(user_id, set()))
        except Exception as e:
            lambda_logger.error("Failed to get user connections", extra={
                'user_id': user_id,
                'error': str(e)
            })
            return []
    
    def get_conversation_connections(self, conversation_id: str) -> List[str]:
        """Get all active connections for a conversation (optimized)"""
        try:
            with self._lock:
                return list(self.conversation_connections.get(conversation_id, set()))
        except Exception as e:
            lambda_logger.error("Failed to get conversation connections", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return []
    
    def update_activity(self, connection_id: str, increment_messages: bool = False) -> bool:
        """Update connection activity with message counting"""
        try:
            with self._lock:
                connection_info = self.active_connections.get(connection_id)
                
                if connection_info:
                    connection_info['last_activity'] = datetime.utcnow()
                    if increment_messages:
                        connection_info['message_count'] = connection_info.get('message_count', 0) + 1
                    return True
                
                return False
                
        except Exception as e:
            lambda_logger.error("Failed to update connection activity", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return False
    
    def cleanup_stale_connections(self) -> int:
        """Clean up stale connections with detailed logging"""
        try:
            with self._lock:
                current_time = datetime.utcnow()
                stale_connections = []
                
                # Find stale connections
                for connection_id, connection_info in self.active_connections.items():
                    last_activity = connection_info['last_activity']
                    idle_time = (current_time - last_activity).total_seconds()
                    
                    if idle_time > self.connection_timeout:
                        stale_connections.append({
                            'connection_id': connection_id,
                            'idle_time': idle_time,
                            'user_id': connection_info['user_id'],
                            'tenant_id': connection_info['tenant_id']
                        })
                
                # Remove stale connections
                cleaned_count = 0
                for stale_conn in stale_connections:
                    if self.remove_connection(stale_conn['connection_id']):
                        cleaned_count += 1
                
                self.stats['last_cleanup'] = current_time
                self.stats['cleanup_count'] += 1
                
                if cleaned_count > 0:
                    lambda_logger.info("Cleaned up stale WebSocket connections", extra={
                        'stale_count': cleaned_count,
                        'active_connections': self.stats['active_connections'],
                        'cleanup_run': self.stats['cleanup_count']
                    })
                
                return cleaned_count
                
        except Exception as e:
            lambda_logger.error("Failed to cleanup stale connections", extra={
                'error': str(e)
            })
            return 0
    
    def get_pool_health(self) -> Dict[str, Any]:
        """Get comprehensive pool health metrics"""
        try:
            with self._lock:
                current_time = datetime.utcnow()
                
                # Calculate utilization
                utilization = (self.stats['active_connections'] / self.max_connections * 100) if self.max_connections > 0 else 0
                
                # Calculate average session duration
                total_duration = 0
                active_count = len(self.active_connections)
                
                if active_count > 0:
                    for conn_info in self.active_connections.values():
                        duration = (current_time - conn_info['connected_at']).total_seconds()
                        total_duration += duration
                    avg_session_duration = total_duration / active_count
                else:
                    avg_session_duration = 0
                
                return {
                    'healthy': utilization < 90,  # Consider unhealthy if > 90% utilization
                    'utilization_percent': round(utilization, 2),
                    'active_connections': self.stats['active_connections'],
                    'max_connections': self.max_connections,
                    'peak_connections': self.stats['peak_connections'],
                    'total_created': self.stats['connections_created'],
                    'total_closed': self.stats['connections_closed'],
                    'avg_session_duration_seconds': round(avg_session_duration, 2),
                    'tenants_connected': len(self.tenant_connections),
                    'users_connected': len(self.user_connections),
                    'conversations_active': len(self.conversation_connections),
                    'last_cleanup': self.stats['last_cleanup'].isoformat(),
                    'cleanup_runs': self.stats['cleanup_count']
                }
                
        except Exception as e:
            lambda_logger.error("Failed to get pool health", extra={
                'error': str(e)
            })
            return {'healthy': False, 'error': str(e)}
    
    def _start_cleanup_thread(self):
        """Start optimized background cleanup thread"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    cleaned = self.cleanup_stale_connections()
                    
                    # Log periodic health status
                    if self.stats['cleanup_count'] % 10 == 0:  # Every 10 cleanups
                        health = self.get_pool_health()
                        lambda_logger.info("WebSocket pool health check", extra=health)
                        
                except Exception as e:
                    lambda_logger.error("Error in WebSocket cleanup thread", extra={
                        'error': str(e)
                    })
        
        # Start cleanup thread as daemon
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        
        lambda_logger.info("WebSocket connection pool cleanup thread started", extra={
            'cleanup_interval': self.cleanup_interval,
            'max_connections': self.max_connections,
            'connection_timeout': self.connection_timeout
        })


# Global WebSocket connection pool instance
websocket_connection_pool = WebSocketConnectionPool()
