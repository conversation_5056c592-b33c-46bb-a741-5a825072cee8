# services/websocket/src/services/connection_manager.py
# WebSocket connection management service

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

try:
    from shared.logger import lambda_logger
    from shared.database import DynamoDBClient
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()
    
    class MockDynamoDBClient:
        def __init__(self, table_name): self.table_name = table_name
        def put_item(self, item): pass
        def get_item(self, pk, sk): return None
        def delete_item(self, pk, sk): pass
        def query_gsi(self, index, pk, sk=None, **kwargs): return []
        def scan(self, **kwargs): return []
    
    DynamoDBClient = MockDynamoDBClient

from ..utils.config import config
from ..models.connection import WebSocketConnection


class IConnectionManager(ABC):
    """Interface for WebSocket connection management operations."""

    @abstractmethod
    def add_connection(
        self,
        connection_id: str,
        user_id: str,
        tenant_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Add a new WebSocket connection."""
        pass

    @abstractmethod
    def remove_connection(self, connection_id: str) -> bool:
        """Remove a WebSocket connection."""
        pass

    @abstractmethod
    def get_connection(self, connection_id: str) -> Optional[Dict[str, Any]]:
        """Get connection information by ID."""
        pass

    @abstractmethod
    def get_user_connections(self, user_id: str, tenant_id: str) -> List[Dict[str, Any]]:
        """Get all connections for a user."""
        pass

    @abstractmethod
    def get_tenant_connections(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get all connections for a tenant."""
        pass

    @abstractmethod
    def update_connection_activity(self, connection_id: str) -> bool:
        """Update last activity timestamp for a connection."""
        pass

    @abstractmethod
    def cleanup_stale_connections(self, max_age_minutes: int = 60) -> int:
        """Clean up stale connections and return count of removed connections."""
        pass

    @abstractmethod
    def get_connection_count(self, tenant_id: Optional[str] = None) -> int:
        """Get total connection count, optionally filtered by tenant."""
        pass


class ConnectionManager(IConnectionManager):
    """Manages WebSocket connections in DynamoDB"""
    
    def __init__(self):
        self.table_name = config.connections_table
        if shared_available:
            self.db_client = DynamoDBClient(self.table_name)
        else:
            self.db_client = MockDynamoDBClient(self.table_name)
    
    def store_connection(self, connection: WebSocketConnection) -> bool:
        """Store connection in DynamoDB"""
        try:
            item = connection.to_dynamodb_item()
            self.db_client.put_item(item)
            
            lambda_logger.info("Connection stored", extra={
                'connection_id': connection.connection_id,
                'user_id': connection.user_id,
                'tenant_id': connection.tenant_id
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to store connection", extra={
                'connection_id': connection.connection_id,
                'error': str(e)
            })
            return False
    
    def get_connection(self, connection_id: str) -> Optional[WebSocketConnection]:
        """Get connection by ID"""
        try:
            item = self.db_client.get_item(connection_id, connection_id)
            
            if item:
                connection = WebSocketConnection.from_dynamodb_item(item)
                lambda_logger.debug("Connection retrieved", extra={
                    'connection_id': connection_id,
                    'user_id': connection.user_id
                })
                return connection
            
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get connection", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return None
    
    def remove_connection(self, connection_id: str) -> bool:
        """Remove connection from DynamoDB"""
        try:
            # Get connection info before deletion for logging
            connection = self.get_connection(connection_id)
            
            # Delete the connection
            self.db_client.delete_item(connection_id, connection_id)
            
            if connection:
                lambda_logger.info("Connection removed", extra={
                    'connection_id': connection_id,
                    'user_id': connection.user_id,
                    'tenant_id': connection.tenant_id,
                    'duration': str(connection.get_connection_duration())
                })
            else:
                lambda_logger.info("Connection removed", extra={
                    'connection_id': connection_id,
                    'note': 'Connection not found in database'
                })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to remove connection", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return False
    
    def get_user_connections(self, user_id: str) -> List[WebSocketConnection]:
        """Get all connections for a user"""
        try:
            items = self.db_client.query_gsi(
                'UserIndex',
                user_id,
                filter_expression="attribute_exists(connectionId)"
            )
            
            connections = []
            for item in items:
                try:
                    connection = WebSocketConnection.from_dynamodb_item(item)
                    connections.append(connection)
                except Exception as e:
                    lambda_logger.warning("Failed to parse connection item", extra={
                        'user_id': user_id,
                        'item_keys': list(item.keys()) if item else [],
                        'error': str(e)
                    })
            
            lambda_logger.debug("User connections retrieved", extra={
                'user_id': user_id,
                'connection_count': len(connections)
            })
            
            return connections
            
        except Exception as e:
            lambda_logger.error("Failed to get user connections", extra={
                'user_id': user_id,
                'error': str(e)
            })
            return []
    
    def get_tenant_connections(self, tenant_id: str) -> List[WebSocketConnection]:
        """Get all connections for a tenant"""
        try:
            items = self.db_client.query_gsi(
                'TenantIndex',
                tenant_id,
                filter_expression="attribute_exists(connectionId)"
            )
            
            connections = []
            for item in items:
                try:
                    connection = WebSocketConnection.from_dynamodb_item(item)
                    connections.append(connection)
                except Exception as e:
                    lambda_logger.warning("Failed to parse connection item", extra={
                        'tenant_id': tenant_id,
                        'error': str(e)
                    })
            
            lambda_logger.debug("Tenant connections retrieved", extra={
                'tenant_id': tenant_id,
                'connection_count': len(connections)
            })
            
            return connections
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant connections", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def update_connection_activity(self, connection_id: str) -> bool:
        """Update last activity timestamp for connection"""
        try:
            connection = self.get_connection(connection_id)
            if not connection:
                return False
            
            connection.update_activity()
            return self.store_connection(connection)
            
        except Exception as e:
            lambda_logger.error("Failed to update connection activity", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return False
    
    def cleanup_expired_connections(self) -> int:
        """Clean up expired connections"""
        try:
            # Scan for expired connections
            current_timestamp = int(datetime.utcnow().timestamp())
            
            items = self.db_client.scan(
                filter_expression="attribute_exists(ttl) AND ttl < :current_time",
                expression_attribute_values={':current_time': current_timestamp}
            )
            
            cleaned_count = 0
            for item in items:
                try:
                    connection_id = item.get('connectionId')
                    if connection_id:
                        self.remove_connection(connection_id)
                        cleaned_count += 1
                except Exception as e:
                    lambda_logger.warning("Failed to clean up connection", extra={
                        'connection_id': item.get('connectionId'),
                        'error': str(e)
                    })
            
            if cleaned_count > 0:
                lambda_logger.info("Expired connections cleaned up", extra={
                    'cleaned_count': cleaned_count
                })
            
            return cleaned_count
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup expired connections", extra={
                'error': str(e)
            })
            return 0
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        try:
            # Get all active connections
            items = self.db_client.scan(
                filter_expression="attribute_exists(connectionId)"
            )
            
            total_connections = len(items)
            tenant_counts = {}
            user_counts = {}
            
            for item in items:
                tenant_id = item.get('tenantId')
                user_id = item.get('userId')
                
                if tenant_id:
                    tenant_counts[tenant_id] = tenant_counts.get(tenant_id, 0) + 1
                
                if user_id:
                    user_counts[user_id] = user_counts.get(user_id, 0) + 1
            
            stats = {
                'total_connections': total_connections,
                'unique_tenants': len(tenant_counts),
                'unique_users': len(user_counts),
                'connections_per_tenant': tenant_counts,
                'connections_per_user': user_counts,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            lambda_logger.debug("Connection stats calculated", extra=stats)
            
            return stats
            
        except Exception as e:
            lambda_logger.error("Failed to get connection stats", extra={
                'error': str(e)
            })
            return {
                'total_connections': 0,
                'unique_tenants': 0,
                'unique_users': 0,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def is_user_online(self, user_id: str) -> bool:
        """Check if user has any active connections"""
        connections = self.get_user_connections(user_id)
        return len(connections) > 0
    
    def get_online_users_in_tenant(self, tenant_id: str) -> List[str]:
        """Get list of online user IDs in a tenant"""
        connections = self.get_tenant_connections(tenant_id)
        online_users = list(set(conn.user_id for conn in connections))
        return online_users


# Global connection manager instance
connection_manager = ConnectionManager()
