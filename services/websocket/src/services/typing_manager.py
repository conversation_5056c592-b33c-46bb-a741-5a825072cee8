# services/websocket/src/services/typing_manager.py
# Typing indicators management service

import json
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timedelta
import asyncio
import threading
import time

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()

class TypingManager:
    """Service for managing typing indicators in real-time"""
    
    def __init__(self):
        # In-memory storage for typing indicators
        # Format: {conversation_id: {user_id: {'started_at': datetime, 'expires_at': datetime}}}
        self.typing_indicators: Dict[str, Dict[str, Dict[str, datetime]]] = {}
        
        # Cleanup thread for expired indicators
        self.cleanup_thread = None
        self.running = True
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    def start_typing(self, conversation_id: str, user_id: str, tenant_id: str,
                    connection_id: str = None, duration_seconds: int = 30) -> Tuple[bool, Optional[str]]:
        """
        Start typing indicator for user in conversation
        
        Args:
            conversation_id: Conversation identifier
            user_id: User who is typing
            tenant_id: Tenant identifier
            connection_id: WebSocket connection ID
            duration_seconds: How long the typing indicator should last
        
        Returns:
            (success, error_message)
        """
        try:
            current_time = datetime.utcnow()
            expires_at = current_time + timedelta(seconds=duration_seconds)
            
            # Initialize conversation if not exists
            if conversation_id not in self.typing_indicators:
                self.typing_indicators[conversation_id] = {}
            
            # Set typing indicator
            self.typing_indicators[conversation_id][user_id] = {
                'started_at': current_time,
                'expires_at': expires_at,
                'tenant_id': tenant_id,
                'connection_id': connection_id
            }
            
            # Broadcast typing indicator to conversation participants
            success, error_msg = self._broadcast_typing_indicator(
                conversation_id=conversation_id,
                user_id=user_id,
                is_typing=True,
                tenant_id=tenant_id,
                sender_connection_id=connection_id
            )
            
            lambda_logger.debug("Typing indicator started", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'expires_at': expires_at.isoformat(),
                'broadcast_success': success
            })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to start typing indicator", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Failed to start typing indicator: {str(e)}"
    
    def stop_typing(self, conversation_id: str, user_id: str, tenant_id: str,
                   connection_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        Stop typing indicator for user in conversation
        
        Returns:
            (success, error_message)
        """
        try:
            # Remove typing indicator
            if (conversation_id in self.typing_indicators and 
                user_id in self.typing_indicators[conversation_id]):
                
                del self.typing_indicators[conversation_id][user_id]
                
                # Clean up empty conversation
                if not self.typing_indicators[conversation_id]:
                    del self.typing_indicators[conversation_id]
                
                # Broadcast stop typing indicator
                success, error_msg = self._broadcast_typing_indicator(
                    conversation_id=conversation_id,
                    user_id=user_id,
                    is_typing=False,
                    tenant_id=tenant_id,
                    sender_connection_id=connection_id
                )
                
                lambda_logger.debug("Typing indicator stopped", extra={
                    'conversation_id': conversation_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'broadcast_success': success
                })
                
                return success, error_msg
            else:
                # No typing indicator to stop
                return True, None
                
        except Exception as e:
            lambda_logger.error("Failed to stop typing indicator", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Failed to stop typing indicator: {str(e)}"
    
    def get_typing_users(self, conversation_id: str) -> List[Dict[str, Any]]:
        """
        Get list of users currently typing in conversation
        
        Returns:
            List of typing user information
        """
        try:
            current_time = datetime.utcnow()
            typing_users = []
            
            if conversation_id in self.typing_indicators:
                for user_id, typing_info in self.typing_indicators[conversation_id].items():
                    # Check if typing indicator is still valid
                    if typing_info['expires_at'] > current_time:
                        typing_users.append({
                            'userId': user_id,
                            'startedAt': typing_info['started_at'].isoformat(),
                            'expiresAt': typing_info['expires_at'].isoformat(),
                            'tenantId': typing_info['tenant_id']
                        })
            
            return typing_users
            
        except Exception as e:
            lambda_logger.error("Failed to get typing users", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return []
    
    def is_user_typing(self, conversation_id: str, user_id: str) -> bool:
        """Check if specific user is currently typing"""
        try:
            if (conversation_id in self.typing_indicators and 
                user_id in self.typing_indicators[conversation_id]):
                
                typing_info = self.typing_indicators[conversation_id][user_id]
                return typing_info['expires_at'] > datetime.utcnow()
            
            return False
            
        except Exception as e:
            lambda_logger.error("Failed to check if user is typing", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False
    
    def cleanup_expired_indicators(self) -> int:
        """
        Clean up expired typing indicators
        
        Returns:
            Number of indicators cleaned up
        """
        try:
            current_time = datetime.utcnow()
            cleaned_count = 0
            
            conversations_to_remove = []
            
            for conversation_id, users in self.typing_indicators.items():
                users_to_remove = []
                
                for user_id, typing_info in users.items():
                    if typing_info['expires_at'] <= current_time:
                        users_to_remove.append(user_id)
                        
                        # Broadcast stop typing for expired indicator
                        self._broadcast_typing_indicator(
                            conversation_id=conversation_id,
                            user_id=user_id,
                            is_typing=False,
                            tenant_id=typing_info['tenant_id'],
                            sender_connection_id=typing_info.get('connection_id')
                        )
                        
                        cleaned_count += 1
                
                # Remove expired users
                for user_id in users_to_remove:
                    del users[user_id]
                
                # Mark empty conversations for removal
                if not users:
                    conversations_to_remove.append(conversation_id)
            
            # Remove empty conversations
            for conversation_id in conversations_to_remove:
                del self.typing_indicators[conversation_id]
            
            if cleaned_count > 0:
                lambda_logger.debug("Expired typing indicators cleaned", extra={
                    'cleaned_count': cleaned_count,
                    'empty_conversations_removed': len(conversations_to_remove)
                })
            
            return cleaned_count
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup expired indicators", extra={
                'error': str(e)
            })
            return 0
    
    def _broadcast_typing_indicator(self, conversation_id: str, user_id: str, is_typing: bool,
                                  tenant_id: str, sender_connection_id: str = None) -> Tuple[bool, Optional[str]]:
        """Broadcast typing indicator to conversation participants"""
        try:
            # Import here to avoid circular imports
            from .message_router import message_router
            
            # Route typing indicator
            sent_count, failed_count = message_router.route_typing_indicator(
                conversation_id=conversation_id,
                user_id=user_id,
                is_typing=is_typing,
                sender_connection_id=sender_connection_id,
                tenant_id=tenant_id
            )
            
            lambda_logger.debug("Typing indicator broadcasted", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'is_typing': is_typing,
                'sent_count': sent_count,
                'failed_count': failed_count
            })
            
            return sent_count > 0, None if sent_count > 0 else "No connections to broadcast to"
            
        except Exception as e:
            lambda_logger.error("Failed to broadcast typing indicator", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'is_typing': is_typing,
                'error': str(e)
            })
            return False, f"Broadcast failed: {str(e)}"
    
    def _start_cleanup_thread(self):
        """Start background thread for cleaning up expired indicators"""
        def cleanup_worker():
            while self.running:
                try:
                    self.cleanup_expired_indicators()
                    time.sleep(10)  # Check every 10 seconds
                except Exception as e:
                    lambda_logger.error("Error in typing cleanup thread", extra={
                        'error': str(e)
                    })
                    time.sleep(30)  # Wait longer on error
        
        if shared_available:
            self.cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
            self.cleanup_thread.start()
    
    def stop_cleanup_thread(self):
        """Stop the cleanup thread"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get typing manager statistics"""
        try:
            total_conversations = len(self.typing_indicators)
            total_typing_users = sum(len(users) for users in self.typing_indicators.values())
            
            return {
                'total_conversations': total_conversations,
                'total_typing_users': total_typing_users,
                'cleanup_thread_running': self.running and (self.cleanup_thread is not None),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get typing manager stats", extra={
                'error': str(e)
            })
            return {
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global typing manager instance
typing_manager = TypingManager()
