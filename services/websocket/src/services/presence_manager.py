# services/websocket/src/services/presence_manager.py
# Presence management service for WebSocket connections

import json
import boto3
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    from shared.database import Dynamo<PERSON>BClient
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()
    
    class MockDynamoDBClient:
        def __init__(self, table_name): self.table_name = table_name
        def put_item(self, item): pass
        def get_item(self, pk, sk): return None
        def update_item(self, **kwargs): pass
        def delete_item(self, **kwargs): pass
    
    DynamoDBClient = MockDynamoDBClient

from ..utils.config import config


class IPresenceManager(ABC):
    """Interface for user presence management operations."""

    @abstractmethod
    def set_user_online(self, user_id: str, tenant_id: str, connection_id: str) -> bool:
        """Set user as online with connection ID."""
        pass

    @abstractmethod
    def set_user_offline(self, user_id: str, tenant_id: str, connection_id: str) -> bool:
        """Set user as offline for specific connection."""
        pass

    @abstractmethod
    def get_user_presence(self, user_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get current presence status for a user."""
        pass

    @abstractmethod
    def get_online_users(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get all online users for a tenant."""
        pass

    @abstractmethod
    def update_user_activity(self, user_id: str, tenant_id: str, activity: str) -> bool:
        """Update user activity status (typing, idle, etc.)."""
        pass

    @abstractmethod
    def cleanup_stale_presence(self, max_age_minutes: int = 30) -> int:
        """Clean up stale presence records. Returns count of cleaned records."""
        pass

    @abstractmethod
    def broadcast_presence_update(self, user_id: str, tenant_id: str, status: str) -> bool:
        """Broadcast presence update to relevant connections."""
        pass


class PresenceManager(IPresenceManager):
    """Service for managing user presence through WebSocket connections"""
    
    def __init__(self):
        if shared_available:
            self.presence_db = DynamoDBClient(config.get_table_name('user_presence'))
            self.lambda_client = boto3.client('lambda')
        else:
            self.presence_db = MockDynamoDBClient('user_presence')
            self.lambda_client = None
    
    def handle_user_connect(self, connection_id: str, user_id: str, tenant_id: str,
                          metadata: Dict[str, Any] = None) -> Tuple[bool, Optional[str]]:
        """
        Handle user connection - update presence to online
        
        Returns:
            (success, error_message)
        """
        try:
            # Call Chat Service presence service
            success, error_msg = self._call_chat_presence_service(
                action='connect',
                user_id=user_id,
                tenant_id=tenant_id,
                connection_id=connection_id,
                metadata=metadata
            )
            
            if success:
                # Broadcast presence update to relevant users
                self._broadcast_presence_update(user_id, 'online', tenant_id, connection_id)
                
                lambda_logger.info("User connected - presence updated", extra={
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'status': 'online'
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to handle user connect", extra={
                'connection_id': connection_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Failed to handle user connect: {str(e)}"
    
    def handle_user_disconnect(self, connection_id: str, user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Handle user disconnection - update presence based on remaining connections
        
        Returns:
            (success, error_message)
        """
        try:
            # Call Chat Service presence service
            success, error_msg = self._call_chat_presence_service(
                action='disconnect',
                user_id=user_id,
                tenant_id=tenant_id,
                connection_id=connection_id
            )
            
            if success:
                # Check if user still has other connections
                # If not, broadcast offline status
                self._broadcast_presence_update(user_id, 'offline', tenant_id, connection_id)
                
                lambda_logger.info("User disconnected - presence updated", extra={
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'status': 'offline'
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to handle user disconnect", extra={
                'connection_id': connection_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Failed to handle user disconnect: {str(e)}"
    
    def update_user_status(self, user_id: str, tenant_id: str, status: str,
                          connection_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        Update user presence status (online, away, busy, offline)
        
        Returns:
            (success, error_message)
        """
        try:
            # Validate status
            valid_statuses = ['online', 'offline', 'away', 'busy']
            if status not in valid_statuses:
                return False, f"Invalid status. Must be one of: {valid_statuses}"
            
            # Call Chat Service presence service
            success, error_msg = self._call_chat_presence_service(
                action='update_status',
                user_id=user_id,
                tenant_id=tenant_id,
                status=status,
                connection_id=connection_id
            )
            
            if success:
                # Broadcast presence update
                self._broadcast_presence_update(user_id, status, tenant_id, connection_id)
                
                lambda_logger.info("User status updated", extra={
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'status': status,
                    'connection_id': connection_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to update user status", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'status': status,
                'error': str(e)
            })
            return False, f"Failed to update user status: {str(e)}"
    
    def get_user_presence(self, user_id: str, tenant_id: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Get user presence information
        
        Returns:
            (success, presence_data, error_message)
        """
        try:
            # Call Chat Service to get presence
            success, response_data = self._call_chat_presence_service(
                action='get_presence',
                user_id=user_id,
                tenant_id=tenant_id
            )
            
            if success and response_data:
                return True, response_data, None
            else:
                return False, None, "Presence not found"
                
        except Exception as e:
            lambda_logger.error("Failed to get user presence", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, None, f"Failed to get user presence: {str(e)}"
    
    def _call_chat_presence_service(self, action: str, user_id: str, tenant_id: str,
                                  **kwargs) -> Tuple[bool, Any]:
        """
        Call Chat Service presence endpoints
        
        Returns:
            (success, response_data)
        """
        try:
            import os
            
            # Get Chat Service function name
            chat_function_name = f"agent-scl-chat-{os.environ.get('ENVIRONMENT', 'dev')}-presenceHandler"
            
            # Prepare payload based on action
            if action == 'connect':
                payload = {
                    'httpMethod': 'POST',
                    'path': '/chat/presence/connect',
                    'body': json.dumps({
                        'userId': user_id,
                        'connectionId': kwargs.get('connection_id'),
                        'metadata': kwargs.get('metadata', {})
                    })
                }
            elif action == 'disconnect':
                payload = {
                    'httpMethod': 'POST',
                    'path': '/chat/presence/disconnect',
                    'body': json.dumps({
                        'userId': user_id,
                        'connectionId': kwargs.get('connection_id')
                    })
                }
            elif action == 'update_status':
                payload = {
                    'httpMethod': 'PATCH',
                    'path': f'/chat/presence/{user_id}',
                    'body': json.dumps({
                        'status': kwargs.get('status')
                    })
                }
            elif action == 'get_presence':
                payload = {
                    'httpMethod': 'GET',
                    'path': f'/chat/presence/{user_id}',
                    'pathParameters': {'userId': user_id}
                }
            else:
                return False, f"Unknown action: {action}"
            
            # Add auth context
            payload['requestContext'] = {
                'authorizer': {
                    'userId': user_id,
                    'tenantId': tenant_id,
                    'email': f'{user_id}@example.com'  # Placeholder
                }
            }
            
            # Invoke Chat Service
            if shared_available and self.lambda_client:
                response = self.lambda_client.invoke(
                    FunctionName=chat_function_name,
                    InvocationType='RequestResponse',
                    Payload=json.dumps(payload)
                )
                
                # Parse response
                response_payload = json.loads(response['Payload'].read())
                
                if response_payload.get('statusCode') in [200, 201]:
                    body = json.loads(response_payload.get('body', '{}'))
                    return True, body
                else:
                    body = json.loads(response_payload.get('body', '{}'))
                    error_msg = body.get('error', 'Unknown error')
                    return False, error_msg
            else:
                # Mock success for development
                return True, {'status': 'success', 'action': action}
                
        except Exception as e:
            lambda_logger.error("Failed to call Chat Service presence", extra={
                'action': action,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Chat Service call failed: {str(e)}"
    
    def _broadcast_presence_update(self, user_id: str, status: str, tenant_id: str,
                                 sender_connection_id: str = None):
        """Broadcast presence update to relevant users"""
        try:
            # Import here to avoid circular imports
            from .message_router import message_router
            
            # Route presence update
            sent_count, failed_count = message_router.route_presence_update(
                user_id=user_id,
                status=status,
                tenant_id=tenant_id,
                sender_connection_id=sender_connection_id
            )
            
            lambda_logger.debug("Presence update broadcasted", extra={
                'user_id': user_id,
                'status': status,
                'tenant_id': tenant_id,
                'sent_count': sent_count,
                'failed_count': failed_count
            })
            
        except Exception as e:
            lambda_logger.error("Failed to broadcast presence update", extra={
                'user_id': user_id,
                'status': status,
                'tenant_id': tenant_id,
                'error': str(e)
            })


# Global presence manager instance
presence_manager = PresenceManager()
