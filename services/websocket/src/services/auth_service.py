# services/websocket/src/services/auth_service.py
# Authentication service for WebSocket connections

import json
import jwt
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from shared.logger import lambda_logger
from shared.config import get_settings
from shared.auth import AuthContext
from shared.exceptions import AuthorizationException


class IWebSocketAuthService(ABC):
    """Interface for WebSocket authentication operations."""
    
    @abstractmethod
    def authenticate_connection(
        self,
        connection_id: str,
        auth_token: Optional[str] = None,
        query_params: Optional[Dict[str, str]] = None
    ) -> Tuple[bool, Optional[AuthContext], Optional[str]]:
        """Authenticate WebSocket connection. Returns (success, auth_context, error_message)."""
        pass
    
    @abstractmethod
    def validate_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Validate JWT token. Returns (valid, payload, error_message)."""
        pass
    
    @abstractmethod
    def extract_auth_from_query(self, query_params: Dict[str, str]) -> Optional[str]:
        """Extract authentication token from query parameters."""
        pass
    
    @abstractmethod
    def create_auth_context(self, token_payload: Dict[str, Any]) -> AuthContext:
        """Create AuthContext from validated token payload."""
        pass
    
    @abstractmethod
    def check_connection_permissions(
        self,
        auth_context: AuthContext,
        requested_permissions: List[str]
    ) -> bool:
        """Check if connection has required permissions."""
        pass


class WebSocketAuthService(IWebSocketAuthService):
    """Service for authenticating WebSocket connections"""
    
    def __init__(self):
        self.settings = get_settings()
        self.jwt_secret = self.settings.get('jwt_secret_key')
        self.jwt_algorithm = self.settings.get('jwt_algorithm', 'HS256')
        self.token_expiry_buffer = self.settings.get('token_expiry_buffer_seconds', 300)  # 5 minutes
    
    def authenticate_connection(
        self,
        connection_id: str,
        auth_token: Optional[str] = None,
        query_params: Optional[Dict[str, str]] = None
    ) -> Tuple[bool, Optional[AuthContext], Optional[str]]:
        """Authenticate WebSocket connection."""
        try:
            # Extract token from various sources
            token = auth_token
            if not token and query_params:
                token = self.extract_auth_from_query(query_params)
            
            if not token:
                return False, None, "No authentication token provided"
            
            # Validate the token
            is_valid, payload, error_msg = self.validate_token(token)
            if not is_valid:
                return False, None, error_msg
            
            # Create auth context
            auth_context = self.create_auth_context(payload)
            
            lambda_logger.info(f"WebSocket connection {connection_id} authenticated for user {auth_context.user_id}")
            return True, auth_context, None
            
        except Exception as e:
            error_msg = f"Authentication failed: {str(e)}"
            lambda_logger.error(error_msg, extra={'connection_id': connection_id})
            return False, None, error_msg
    
    def validate_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Validate JWT token."""
        try:
            if not self.jwt_secret:
                return False, None, "JWT secret not configured"
            
            # Remove 'Bearer ' prefix if present
            if token.startswith('Bearer '):
                token = token[7:]
            
            # Decode and validate the token
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=[self.jwt_algorithm],
                options={'verify_exp': True}
            )
            
            # Check if token is about to expire
            exp = payload.get('exp')
            if exp:
                current_time = datetime.utcnow().timestamp()
                if exp - current_time < self.token_expiry_buffer:
                    return False, None, "Token is about to expire"
            
            # Validate required fields
            required_fields = ['user_id', 'tenant_id']
            for field in required_fields:
                if field not in payload:
                    return False, None, f"Missing required field: {field}"
            
            return True, payload, None
            
        except jwt.ExpiredSignatureError:
            return False, None, "Token has expired"
        except jwt.InvalidTokenError as e:
            return False, None, f"Invalid token: {str(e)}"
        except Exception as e:
            return False, None, f"Token validation error: {str(e)}"
    
    def extract_auth_from_query(self, query_params: Dict[str, str]) -> Optional[str]:
        """Extract authentication token from query parameters."""
        # Try different parameter names
        token_params = ['token', 'auth', 'authorization', 'access_token']
        
        for param in token_params:
            if param in query_params:
                return query_params[param]
        
        return None
    
    def create_auth_context(self, token_payload: Dict[str, Any]) -> AuthContext:
        """Create AuthContext from validated token payload."""
        return AuthContext(
            user_id=token_payload['user_id'],
            tenant_id=token_payload['tenant_id'],
            email=token_payload.get('email'),
            role=token_payload.get('role', 'user'),
            permissions=token_payload.get('permissions', []),
            session_id=token_payload.get('session_id'),
            expires_at=token_payload.get('exp')
        )
    
    def check_connection_permissions(
        self,
        auth_context: AuthContext,
        requested_permissions: List[str]
    ) -> bool:
        """Check if connection has required permissions."""
        try:
            if not requested_permissions:
                return True
            
            user_permissions = auth_context.permissions or []
            
            # Check if user has all requested permissions
            for permission in requested_permissions:
                if permission not in user_permissions:
                    # Check for admin role as fallback
                    if auth_context.role != 'admin':
                        lambda_logger.warning(
                            f"Permission denied: {permission} for user {auth_context.user_id}"
                        )
                        return False
            
            return True
            
        except Exception as e:
            lambda_logger.error(f"Permission check failed: {str(e)}")
            return False
    
    def refresh_token_if_needed(self, token: str) -> Optional[str]:
        """Check if token needs refresh and return new token if available."""
        # This would integrate with the auth service to refresh tokens
        # For now, just return None to indicate no refresh available
        return None
    
    def get_connection_auth_info(self, connection_id: str) -> Optional[Dict[str, Any]]:
        """Get cached authentication info for a connection."""
        # This would typically retrieve from cache/database
        # For now, return None
        return None
    
    def cache_connection_auth(
        self,
        connection_id: str,
        auth_context: AuthContext,
        ttl_seconds: int = 3600
    ) -> bool:
        """Cache authentication info for a connection."""
        try:
            # This would typically store in Redis or similar cache
            # For now, just log and return True
            lambda_logger.debug(f"Caching auth info for connection {connection_id}")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to cache auth info: {str(e)}")
            return False
    
    def invalidate_connection_auth(self, connection_id: str) -> bool:
        """Invalidate cached authentication info for a connection."""
        try:
            # This would typically remove from cache
            lambda_logger.debug(f"Invalidating auth info for connection {connection_id}")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to invalidate auth info: {str(e)}")
            return False


# Create singleton instance
websocket_auth_service = WebSocketAuthService()
