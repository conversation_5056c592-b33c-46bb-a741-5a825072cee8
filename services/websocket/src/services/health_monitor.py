# services/websocket/src/services/health_monitor.py
# Health monitoring service for WebSocket connections

import time
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from enum import Enum

from shared.logger import lambda_logger
from shared.config import get_settings


class HealthStatus(Enum):
    """Health status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"


class IHealthMonitor(ABC):
    """Interface for health monitoring operations."""
    
    @abstractmethod
    def check_overall_health(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        pass
    
    @abstractmethod
    def check_connection_health(self) -> Dict[str, Any]:
        """Check WebSocket connection health."""
        pass
    
    @abstractmethod
    def check_service_dependencies(self) -> Dict[str, Any]:
        """Check health of service dependencies."""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        pass
    
    @abstractmethod
    def record_connection_event(self, event_type: str, connection_id: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record connection-related event for monitoring."""
        pass
    
    @abstractmethod
    def get_connection_statistics(self) -> Dict[str, Any]:
        """Get connection statistics."""
        pass
    
    @abstractmethod
    def check_rate_limiting_health(self) -> Dict[str, Any]:
        """Check rate limiting system health."""
        pass


class HealthMonitor(IHealthMonitor):
    """Service for monitoring WebSocket service health"""
    
    def __init__(self):
        self.settings = get_settings()
        self.start_time = time.time()
        
        # Health thresholds
        self.connection_warning_threshold = self.settings.get('connection_warning_threshold', 8000)
        self.connection_critical_threshold = self.settings.get('connection_critical_threshold', 9500)
        self.error_rate_warning = self.settings.get('error_rate_warning_threshold', 0.05)
        self.error_rate_critical = self.settings.get('error_rate_critical_threshold', 0.10)
        
        # Metrics storage (would use proper metrics system in production)
        self.metrics = {
            'connection_events': [],
            'error_events': [],
            'performance_metrics': []
        }
    
    def check_overall_health(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        try:
            health_checks = {
                'connections': self.check_connection_health(),
                'dependencies': self.check_service_dependencies(),
                'rate_limiting': self.check_rate_limiting_health(),
                'performance': self.get_performance_metrics()
            }
            
            # Determine overall status
            statuses = [check.get('status', HealthStatus.HEALTHY.value) for check in health_checks.values()]
            
            if HealthStatus.CRITICAL.value in statuses:
                overall_status = HealthStatus.CRITICAL.value
            elif HealthStatus.UNHEALTHY.value in statuses:
                overall_status = HealthStatus.UNHEALTHY.value
            elif HealthStatus.DEGRADED.value in statuses:
                overall_status = HealthStatus.DEGRADED.value
            else:
                overall_status = HealthStatus.HEALTHY.value
            
            return {
                'status': overall_status,
                'timestamp': datetime.utcnow().isoformat(),
                'uptime_seconds': time.time() - self.start_time,
                'checks': health_checks,
                'service': 'websocket'
            }
            
        except Exception as e:
            lambda_logger.error(f"Health check failed: {str(e)}")
            return {
                'status': HealthStatus.CRITICAL.value,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def check_connection_health(self) -> Dict[str, Any]:
        """Check WebSocket connection health."""
        try:
            from .connection_manager import connection_manager
            
            # Get connection count
            total_connections = connection_manager.get_connection_count()
            
            # Determine status based on connection count
            if total_connections >= self.connection_critical_threshold:
                status = HealthStatus.CRITICAL.value
            elif total_connections >= self.connection_warning_threshold:
                status = HealthStatus.DEGRADED.value
            else:
                status = HealthStatus.HEALTHY.value
            
            return {
                'status': status,
                'total_connections': total_connections,
                'warning_threshold': self.connection_warning_threshold,
                'critical_threshold': self.connection_critical_threshold,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error(f"Connection health check failed: {str(e)}")
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': str(e)
            }
    
    def check_service_dependencies(self) -> Dict[str, Any]:
        """Check health of service dependencies."""
        try:
            dependencies = {
                'dynamodb': self._check_dynamodb_health(),
                'api_gateway': self._check_api_gateway_health(),
                'lambda': self._check_lambda_health()
            }
            
            # Determine overall dependency status
            failed_deps = [name for name, health in dependencies.items() if not health.get('healthy', False)]
            
            if len(failed_deps) >= 2:
                status = HealthStatus.CRITICAL.value
            elif len(failed_deps) == 1:
                status = HealthStatus.DEGRADED.value
            else:
                status = HealthStatus.HEALTHY.value
            
            return {
                'status': status,
                'dependencies': dependencies,
                'failed_dependencies': failed_deps,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error(f"Dependency health check failed: {str(e)}")
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': str(e)
            }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        try:
            # Calculate recent error rate
            recent_events = [
                event for event in self.metrics['error_events']
                if event['timestamp'] > time.time() - 300  # Last 5 minutes
            ]
            
            total_recent_events = len([
                event for event in self.metrics['connection_events']
                if event['timestamp'] > time.time() - 300
            ])
            
            error_rate = len(recent_events) / max(total_recent_events, 1)
            
            # Determine status based on error rate
            if error_rate >= self.error_rate_critical:
                status = HealthStatus.CRITICAL.value
            elif error_rate >= self.error_rate_warning:
                status = HealthStatus.DEGRADED.value
            else:
                status = HealthStatus.HEALTHY.value
            
            return {
                'status': status,
                'error_rate': error_rate,
                'recent_errors': len(recent_events),
                'recent_events': total_recent_events,
                'uptime_seconds': time.time() - self.start_time,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error(f"Performance metrics check failed: {str(e)}")
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': str(e)
            }
    
    def record_connection_event(self, event_type: str, connection_id: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record connection-related event for monitoring."""
        try:
            event = {
                'type': event_type,
                'connection_id': connection_id,
                'timestamp': time.time(),
                'metadata': metadata or {}
            }
            
            if event_type in ['error', 'disconnect_error', 'auth_failure']:
                self.metrics['error_events'].append(event)
            else:
                self.metrics['connection_events'].append(event)
            
            # Keep only recent events (last hour)
            cutoff_time = time.time() - 3600
            self.metrics['connection_events'] = [
                e for e in self.metrics['connection_events'] if e['timestamp'] > cutoff_time
            ]
            self.metrics['error_events'] = [
                e for e in self.metrics['error_events'] if e['timestamp'] > cutoff_time
            ]
            
        except Exception as e:
            lambda_logger.error(f"Failed to record connection event: {str(e)}")
    
    def get_connection_statistics(self) -> Dict[str, Any]:
        """Get connection statistics."""
        try:
            from .connection_manager import connection_manager
            
            # Get basic stats
            total_connections = connection_manager.get_connection_count()
            
            # Calculate event statistics
            recent_connects = len([
                e for e in self.metrics['connection_events']
                if e['type'] == 'connect' and e['timestamp'] > time.time() - 300
            ])
            
            recent_disconnects = len([
                e for e in self.metrics['connection_events']
                if e['type'] == 'disconnect' and e['timestamp'] > time.time() - 300
            ])
            
            return {
                'total_connections': total_connections,
                'recent_connects_5min': recent_connects,
                'recent_disconnects_5min': recent_disconnects,
                'connection_rate': recent_connects / 5,  # per minute
                'disconnect_rate': recent_disconnects / 5,  # per minute
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to get connection statistics: {str(e)}")
            return {'error': str(e)}
    
    def check_rate_limiting_health(self) -> Dict[str, Any]:
        """Check rate limiting system health."""
        try:
            from .rate_limiter import rate_limiter
            
            stats = rate_limiter.get_global_stats()
            
            # Simple health check based on stats availability
            if 'error' in stats:
                status = HealthStatus.UNHEALTHY.value
            else:
                status = HealthStatus.HEALTHY.value
            
            return {
                'status': status,
                'rate_limiter_stats': stats,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error(f"Rate limiting health check failed: {str(e)}")
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'error': str(e)
            }
    
    def _check_dynamodb_health(self) -> Dict[str, Any]:
        """Check DynamoDB health."""
        try:
            # This would perform a simple DynamoDB operation
            return {
                'healthy': True,
                'response_time_ms': 50,  # Mock value
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _check_api_gateway_health(self) -> Dict[str, Any]:
        """Check API Gateway health."""
        try:
            # This would check API Gateway WebSocket endpoint
            return {
                'healthy': True,
                'endpoint_available': True,
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _check_lambda_health(self) -> Dict[str, Any]:
        """Check Lambda function health."""
        try:
            # This would check Lambda function status
            return {
                'healthy': True,
                'memory_usage': 'normal',
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Create singleton instance
health_monitor = HealthMonitor()
