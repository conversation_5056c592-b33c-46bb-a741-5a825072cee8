# services/websocket/src/services/message_router.py
# Message routing service for WebSocket connections

import json
import boto3
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    from shared.database import Dynamo<PERSON><PERSON>lient
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()
    
    class MockDynamoDBClient:
        def __init__(self, table_name): self.table_name = table_name
        def get_item(self, pk, sk): return None
        def query_gsi(self, index, pk, **kwargs): return []
    
    DynamoDBClient = MockDynamoDBClient

from ..utils.config import config
from ..services.connection_manager import connection_manager


class IM<PERSON>ageRouter(ABC):
    """Interface for WebSocket message routing operations."""

    @abstractmethod
    def route_message(
        self,
        connection_id: str,
        message_type: str,
        payload: Dict[str, Any],
        tenant_id: str
    ) -> bool:
        """Route incoming WebSocket message to appropriate handler."""
        pass

    @abstractmethod
    def send_to_connection(
        self,
        connection_id: str,
        message: Dict[str, Any]
    ) -> bool:
        """Send message to specific WebSocket connection."""
        pass

    @abstractmethod
    def send_to_user(
        self,
        user_id: str,
        tenant_id: str,
        message: Dict[str, Any]
    ) -> int:
        """Send message to all connections of a user. Returns count of successful sends."""
        pass

    @abstractmethod
    def broadcast_to_tenant(
        self,
        tenant_id: str,
        message: Dict[str, Any],
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Broadcast message to all connections in a tenant. Returns count of successful sends."""
        pass

    @abstractmethod
    def handle_chat_message(
        self,
        connection_id: str,
        message_data: Dict[str, Any],
        tenant_id: str
    ) -> bool:
        """Handle incoming chat message."""
        pass


class MessageRouter(IMessageRouter):
    """Service for routing WebSocket messages to appropriate connections"""
    
    def __init__(self):
        if shared_available:
            self.main_db = DynamoDBClient(config.get_table_name('main'))
            self.apigateway_management = boto3.client('apigatewaymanagementapi')
        else:
            self.main_db = MockDynamoDBClient('main')
            self.apigateway_management = None
    
    def route_message_to_conversation(self, conversation_id: str, message: Dict[str, Any],
                                    sender_connection_id: str = None, 
                                    tenant_id: str = None) -> Tuple[int, int, List[str]]:
        """
        Route message to all participants in a conversation
        
        Returns:
            (sent_count, failed_count, failed_connection_ids)
        """
        try:
            # Get conversation participants
            participants = self._get_conversation_participants(conversation_id, tenant_id)
            
            if not participants:
                lambda_logger.warning("No participants found for conversation", extra={
                    'conversation_id': conversation_id,
                    'tenant_id': tenant_id
                })
                return 0, 0, []
            
            # Get active connections for participants
            target_connections = []
            for participant_id in participants:
                user_connections = connection_manager.get_user_connections(participant_id)
                target_connections.extend(user_connections)
            
            # Filter out sender connection
            if sender_connection_id:
                target_connections = [
                    conn for conn in target_connections 
                    if conn.connection_id != sender_connection_id
                ]
            
            # Send message to connections
            sent_count, failed_count, failed_connections = self._send_to_connections(
                target_connections, message
            )
            
            lambda_logger.info("Message routed to conversation", extra={
                'conversation_id': conversation_id,
                'participants_count': len(participants),
                'connections_count': len(target_connections),
                'sent_count': sent_count,
                'failed_count': failed_count
            })
            
            return sent_count, failed_count, failed_connections
            
        except Exception as e:
            lambda_logger.error("Failed to route message to conversation", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return 0, 1, []
    
    def route_message_to_user(self, user_id: str, message: Dict[str, Any],
                            sender_connection_id: str = None) -> Tuple[int, int, List[str]]:
        """
        Route message to specific user's connections
        
        Returns:
            (sent_count, failed_count, failed_connection_ids)
        """
        try:
            # Get user connections
            user_connections = connection_manager.get_user_connections(user_id)
            
            # Filter out sender connection
            if sender_connection_id:
                user_connections = [
                    conn for conn in user_connections 
                    if conn.connection_id != sender_connection_id
                ]
            
            # Send message to connections
            sent_count, failed_count, failed_connections = self._send_to_connections(
                user_connections, message
            )
            
            lambda_logger.debug("Message routed to user", extra={
                'user_id': user_id,
                'connections_count': len(user_connections),
                'sent_count': sent_count,
                'failed_count': failed_count
            })
            
            return sent_count, failed_count, failed_connections
            
        except Exception as e:
            lambda_logger.error("Failed to route message to user", extra={
                'user_id': user_id,
                'error': str(e)
            })
            return 0, 1, []
    
    def route_typing_indicator(self, conversation_id: str, user_id: str, is_typing: bool,
                             sender_connection_id: str = None, 
                             tenant_id: str = None) -> Tuple[int, int]:
        """
        Route typing indicator to conversation participants
        
        Returns:
            (sent_count, failed_count)
        """
        try:
            typing_message = {
                'action': 'typing_indicator',
                'data': {
                    'conversationId': conversation_id,
                    'userId': user_id,
                    'isTyping': is_typing,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            sent_count, failed_count, _ = self.route_message_to_conversation(
                conversation_id, typing_message, sender_connection_id, tenant_id
            )
            
            lambda_logger.debug("Typing indicator routed", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'is_typing': is_typing,
                'sent_count': sent_count
            })
            
            return sent_count, failed_count
            
        except Exception as e:
            lambda_logger.error("Failed to route typing indicator", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'error': str(e)
            })
            return 0, 1
    
    def route_presence_update(self, user_id: str, status: str, tenant_id: str,
                            sender_connection_id: str = None) -> Tuple[int, int]:
        """
        Route presence update to relevant users
        
        Returns:
            (sent_count, failed_count)
        """
        try:
            presence_message = {
                'action': 'presence_update',
                'data': {
                    'userId': user_id,
                    'status': status,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            # Get users in same tenant (simplified - in real app would be contacts/conversation participants)
            tenant_connections = connection_manager.get_tenant_connections(tenant_id)
            
            # Filter out sender connection
            if sender_connection_id:
                tenant_connections = [
                    conn for conn in tenant_connections 
                    if conn.connection_id != sender_connection_id
                ]
            
            sent_count, failed_count, _ = self._send_to_connections(
                tenant_connections, presence_message
            )
            
            lambda_logger.debug("Presence update routed", extra={
                'user_id': user_id,
                'status': status,
                'tenant_id': tenant_id,
                'sent_count': sent_count
            })
            
            return sent_count, failed_count
            
        except Exception as e:
            lambda_logger.error("Failed to route presence update", extra={
                'user_id': user_id,
                'status': status,
                'error': str(e)
            })
            return 0, 1
    
    def _get_conversation_participants(self, conversation_id: str, tenant_id: str) -> List[str]:
        """Get list of participant user IDs for a conversation"""
        try:
            # Query main table for conversation
            conversation_key = f'TENANT#{tenant_id}'
            conversation_sk = f'CONVERSATION#{conversation_id}'
            
            conversation = self.main_db.get_item(conversation_key, conversation_sk)
            
            if not conversation:
                return []
            
            # Extract participants from conversation
            participants = conversation.get('Participants', [])
            
            # Also include the user who created the conversation
            created_by = conversation.get('CreatedBy')
            if created_by and created_by not in participants:
                participants.append(created_by)
            
            return participants
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation participants", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def _send_to_connections(self, connections: List, message: Dict[str, Any]) -> Tuple[int, int, List[str]]:
        """
        Send message to list of connections
        
        Returns:
            (sent_count, failed_count, failed_connection_ids)
        """
        sent_count = 0
        failed_count = 0
        failed_connections = []
        
        message_payload = json.dumps(message)
        
        for connection in connections:
            try:
                connection_id = connection.connection_id
                
                if shared_available and self.apigateway_management:
                    self.apigateway_management.post_to_connection(
                        ConnectionId=connection_id,
                        Data=message_payload
                    )
                
                sent_count += 1
                
                lambda_logger.debug("Message sent to connection", extra={
                    'connection_id': connection_id,
                    'user_id': connection.user_id
                })
                
            except self.apigateway_management.exceptions.GoneException:
                # Connection is stale, remove it
                lambda_logger.warning("Stale connection removed", extra={
                    'connection_id': connection.connection_id
                })
                connection_manager.remove_connection(connection.connection_id)
                failed_count += 1
                failed_connections.append(connection.connection_id)
                
            except Exception as e:
                lambda_logger.error("Failed to send message to connection", extra={
                    'connection_id': connection.connection_id,
                    'user_id': connection.user_id,
                    'error': str(e)
                })
                failed_count += 1
                failed_connections.append(connection.connection_id)
        
        return sent_count, failed_count, failed_connections


# Global message router instance
message_router = MessageRouter()
