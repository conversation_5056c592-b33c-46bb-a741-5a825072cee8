# services/websocket/src/services/subscription_manager.py
# Subscription management service for WebSocket connections

import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Set
from datetime import datetime

from shared.logger import lambda_logger, log_business_operation
from shared.database import Dynamo<PERSON><PERSON><PERSON>
from shared.config import get_settings


class ISubscriptionManager(ABC):
    """Interface for WebSocket subscription management operations."""
    
    @abstractmethod
    def subscribe_to_conversation(
        self,
        connection_id: str,
        conversation_id: str,
        user_id: str,
        tenant_id: str
    ) -> bool:
        """Subscribe connection to conversation updates."""
        pass
    
    @abstractmethod
    def unsubscribe_from_conversation(
        self,
        connection_id: str,
        conversation_id: str
    ) -> bool:
        """Unsubscribe connection from conversation updates."""
        pass
    
    @abstractmethod
    def get_conversation_subscribers(self, conversation_id: str) -> List[str]:
        """Get all connection IDs subscribed to a conversation."""
        pass
    
    @abstractmethod
    def get_connection_subscriptions(self, connection_id: str) -> List[str]:
        """Get all conversation IDs a connection is subscribed to."""
        pass
    
    @abstractmethod
    def cleanup_connection_subscriptions(self, connection_id: str) -> int:
        """Clean up all subscriptions for a connection. Returns count of cleaned subscriptions."""
        pass
    
    @abstractmethod
    def subscribe_to_presence_updates(
        self,
        connection_id: str,
        user_id: str,
        tenant_id: str
    ) -> bool:
        """Subscribe connection to presence updates."""
        pass
    
    @abstractmethod
    def get_presence_subscribers(self, tenant_id: str) -> List[str]:
        """Get all connection IDs subscribed to presence updates for a tenant."""
        pass


class SubscriptionManager(ISubscriptionManager):
    """Service for managing WebSocket subscriptions to various events"""
    
    def __init__(self):
        self.subscriptions = {}  # In-memory storage for now
        self.conversation_subscribers = {}
        self.presence_subscribers = {}
        
    def subscribe_to_conversation(
        self,
        connection_id: str,
        conversation_id: str,
        user_id: str,
        tenant_id: str
    ) -> bool:
        """Subscribe connection to conversation updates."""
        try:
            if conversation_id not in self.conversation_subscribers:
                self.conversation_subscribers[conversation_id] = set()
            
            self.conversation_subscribers[conversation_id].add(connection_id)
            
            # Track connection subscriptions
            if connection_id not in self.subscriptions:
                self.subscriptions[connection_id] = {
                    'conversations': set(),
                    'presence': False,
                    'user_id': user_id,
                    'tenant_id': tenant_id
                }
            
            self.subscriptions[connection_id]['conversations'].add(conversation_id)
            
            lambda_logger.info(f"Connection {connection_id} subscribed to conversation {conversation_id}")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to subscribe to conversation: {str(e)}")
            return False
    
    def unsubscribe_from_conversation(
        self,
        connection_id: str,
        conversation_id: str
    ) -> bool:
        """Unsubscribe connection from conversation updates."""
        try:
            if conversation_id in self.conversation_subscribers:
                self.conversation_subscribers[conversation_id].discard(connection_id)
                
                # Clean up empty sets
                if not self.conversation_subscribers[conversation_id]:
                    del self.conversation_subscribers[conversation_id]
            
            if connection_id in self.subscriptions:
                self.subscriptions[connection_id]['conversations'].discard(conversation_id)
            
            lambda_logger.info(f"Connection {connection_id} unsubscribed from conversation {conversation_id}")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to unsubscribe from conversation: {str(e)}")
            return False
    
    def get_conversation_subscribers(self, conversation_id: str) -> List[str]:
        """Get all connection IDs subscribed to a conversation."""
        return list(self.conversation_subscribers.get(conversation_id, set()))
    
    def get_connection_subscriptions(self, connection_id: str) -> List[str]:
        """Get all conversation IDs a connection is subscribed to."""
        if connection_id in self.subscriptions:
            return list(self.subscriptions[connection_id]['conversations'])
        return []
    
    def cleanup_connection_subscriptions(self, connection_id: str) -> int:
        """Clean up all subscriptions for a connection."""
        cleaned_count = 0
        
        try:
            if connection_id in self.subscriptions:
                # Remove from conversation subscriptions
                conversations = self.subscriptions[connection_id]['conversations'].copy()
                for conversation_id in conversations:
                    if self.unsubscribe_from_conversation(connection_id, conversation_id):
                        cleaned_count += 1
                
                # Remove from presence subscriptions
                tenant_id = self.subscriptions[connection_id].get('tenant_id')
                if tenant_id and connection_id in self.presence_subscribers.get(tenant_id, set()):
                    self.presence_subscribers[tenant_id].discard(connection_id)
                    cleaned_count += 1
                
                # Remove connection record
                del self.subscriptions[connection_id]
            
            lambda_logger.info(f"Cleaned up {cleaned_count} subscriptions for connection {connection_id}")
            return cleaned_count
            
        except Exception as e:
            lambda_logger.error(f"Failed to cleanup subscriptions: {str(e)}")
            return cleaned_count
    
    def subscribe_to_presence_updates(
        self,
        connection_id: str,
        user_id: str,
        tenant_id: str
    ) -> bool:
        """Subscribe connection to presence updates."""
        try:
            if tenant_id not in self.presence_subscribers:
                self.presence_subscribers[tenant_id] = set()
            
            self.presence_subscribers[tenant_id].add(connection_id)
            
            # Update connection record
            if connection_id not in self.subscriptions:
                self.subscriptions[connection_id] = {
                    'conversations': set(),
                    'presence': True,
                    'user_id': user_id,
                    'tenant_id': tenant_id
                }
            else:
                self.subscriptions[connection_id]['presence'] = True
            
            lambda_logger.info(f"Connection {connection_id} subscribed to presence updates for tenant {tenant_id}")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to subscribe to presence updates: {str(e)}")
            return False
    
    def get_presence_subscribers(self, tenant_id: str) -> List[str]:
        """Get all connection IDs subscribed to presence updates for a tenant."""
        return list(self.presence_subscribers.get(tenant_id, set()))


# Create singleton instance
subscription_manager = SubscriptionManager()
