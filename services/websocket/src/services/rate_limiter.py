# services/websocket/src/services/rate_limiter.py
# Rate limiting service for WebSocket connections

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timedelta
from collections import defaultdict, deque

from shared.logger import lambda_logger
from shared.config import get_settings


class IRateLimiter(ABC):
    """Interface for rate limiting operations."""
    
    @abstractmethod
    def check_rate_limit(
        self,
        identifier: str,
        limit_type: str,
        max_requests: int,
        window_seconds: int
    ) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """Check if request is within rate limit. Returns (allowed, limit_info)."""
        pass
    
    @abstractmethod
    def check_connection_rate_limit(self, connection_id: str) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """Check rate limit for WebSocket connection."""
        pass
    
    @abstractmethod
    def check_user_rate_limit(self, user_id: str, tenant_id: str) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """Check rate limit for user across all connections."""
        pass
    
    @abstractmethod
    def check_tenant_rate_limit(self, tenant_id: str) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """Check rate limit for entire tenant."""
        pass
    
    @abstractmethod
    def record_request(self, identifier: str, limit_type: str) -> None:
        """Record a request for rate limiting purposes."""
        pass
    
    @abstractmethod
    def get_rate_limit_status(self, identifier: str, limit_type: str) -> Dict[str, Any]:
        """Get current rate limit status for an identifier."""
        pass
    
    @abstractmethod
    def reset_rate_limit(self, identifier: str, limit_type: str) -> bool:
        """Reset rate limit for an identifier."""
        pass


class RateLimiter(IRateLimiter):
    """Service for rate limiting WebSocket connections and operations"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Rate limit configurations
        self.connection_limits = {
            'messages_per_minute': self.settings.get('websocket_messages_per_minute', 60),
            'connections_per_minute': self.settings.get('websocket_connections_per_minute', 10),
        }
        
        self.user_limits = {
            'messages_per_minute': self.settings.get('user_messages_per_minute', 120),
            'connections_per_user': self.settings.get('max_connections_per_user', 5),
        }
        
        self.tenant_limits = {
            'messages_per_minute': self.settings.get('tenant_messages_per_minute', 1000),
            'connections_per_tenant': self.settings.get('max_connections_per_tenant', 1000),
        }
        
        # In-memory storage for rate limiting (would use Redis in production)
        self.request_history = defaultdict(lambda: defaultdict(deque))
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5 minutes
    
    def check_rate_limit(
        self,
        identifier: str,
        limit_type: str,
        max_requests: int,
        window_seconds: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """Check if request is within rate limit."""
        try:
            current_time = time.time()
            
            # Cleanup old entries periodically
            if current_time - self.last_cleanup > self.cleanup_interval:
                self._cleanup_old_entries()
                self.last_cleanup = current_time
            
            # Get request history for this identifier and limit type
            history = self.request_history[identifier][limit_type]
            
            # Remove requests outside the time window
            cutoff_time = current_time - window_seconds
            while history and history[0] < cutoff_time:
                history.popleft()
            
            # Check if we're within the limit
            current_count = len(history)
            allowed = current_count < max_requests
            
            # Calculate reset time
            reset_time = None
            if history:
                reset_time = history[0] + window_seconds
            
            limit_info = {
                'limit': max_requests,
                'remaining': max(0, max_requests - current_count),
                'reset_time': reset_time,
                'window_seconds': window_seconds,
                'current_count': current_count
            }
            
            return allowed, limit_info
            
        except Exception as e:
            lambda_logger.error(f"Rate limit check failed: {str(e)}")
            # Fail open - allow the request if rate limiting fails
            return True, {'error': str(e)}
    
    def check_connection_rate_limit(self, connection_id: str) -> Tuple[bool, Dict[str, Any]]:
        """Check rate limit for WebSocket connection."""
        return self.check_rate_limit(
            identifier=f"connection:{connection_id}",
            limit_type="messages",
            max_requests=self.connection_limits['messages_per_minute'],
            window_seconds=60
        )
    
    def check_user_rate_limit(self, user_id: str, tenant_id: str) -> Tuple[bool, Dict[str, Any]]:
        """Check rate limit for user across all connections."""
        return self.check_rate_limit(
            identifier=f"user:{tenant_id}:{user_id}",
            limit_type="messages",
            max_requests=self.user_limits['messages_per_minute'],
            window_seconds=60
        )
    
    def check_tenant_rate_limit(self, tenant_id: str) -> Tuple[bool, Dict[str, Any]]:
        """Check rate limit for entire tenant."""
        return self.check_rate_limit(
            identifier=f"tenant:{tenant_id}",
            limit_type="messages",
            max_requests=self.tenant_limits['messages_per_minute'],
            window_seconds=60
        )
    
    def record_request(self, identifier: str, limit_type: str) -> None:
        """Record a request for rate limiting purposes."""
        try:
            current_time = time.time()
            self.request_history[identifier][limit_type].append(current_time)
            
        except Exception as e:
            lambda_logger.error(f"Failed to record request: {str(e)}")
    
    def get_rate_limit_status(self, identifier: str, limit_type: str) -> Dict[str, Any]:
        """Get current rate limit status for an identifier."""
        try:
            # Determine limits based on identifier type
            if identifier.startswith('connection:'):
                max_requests = self.connection_limits.get(f"{limit_type}_per_minute", 60)
            elif identifier.startswith('user:'):
                max_requests = self.user_limits.get(f"{limit_type}_per_minute", 120)
            elif identifier.startswith('tenant:'):
                max_requests = self.tenant_limits.get(f"{limit_type}_per_minute", 1000)
            else:
                max_requests = 60  # Default
            
            allowed, limit_info = self.check_rate_limit(
                identifier=identifier,
                limit_type=limit_type,
                max_requests=max_requests,
                window_seconds=60
            )
            
            return {
                'allowed': allowed,
                **limit_info
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to get rate limit status: {str(e)}")
            return {'error': str(e)}
    
    def reset_rate_limit(self, identifier: str, limit_type: str) -> bool:
        """Reset rate limit for an identifier."""
        try:
            if identifier in self.request_history:
                if limit_type in self.request_history[identifier]:
                    self.request_history[identifier][limit_type].clear()
                    lambda_logger.info(f"Reset rate limit for {identifier}:{limit_type}")
                    return True
            
            return False
            
        except Exception as e:
            lambda_logger.error(f"Failed to reset rate limit: {str(e)}")
            return False
    
    def _cleanup_old_entries(self) -> None:
        """Clean up old rate limiting entries."""
        try:
            current_time = time.time()
            cutoff_time = current_time - 3600  # Remove entries older than 1 hour
            
            identifiers_to_remove = []
            
            for identifier, limit_types in self.request_history.items():
                for limit_type, history in limit_types.items():
                    # Remove old entries
                    while history and history[0] < cutoff_time:
                        history.popleft()
                
                # Mark empty identifiers for removal
                if not any(history for history in limit_types.values()):
                    identifiers_to_remove.append(identifier)
            
            # Remove empty identifiers
            for identifier in identifiers_to_remove:
                del self.request_history[identifier]
            
            lambda_logger.debug(f"Cleaned up {len(identifiers_to_remove)} empty rate limit entries")
            
        except Exception as e:
            lambda_logger.error(f"Rate limit cleanup failed: {str(e)}")
    
    def get_global_stats(self) -> Dict[str, Any]:
        """Get global rate limiting statistics."""
        try:
            total_identifiers = len(self.request_history)
            total_entries = sum(
                len(history)
                for limit_types in self.request_history.values()
                for history in limit_types.values()
            )
            
            return {
                'total_identifiers': total_identifiers,
                'total_entries': total_entries,
                'last_cleanup': self.last_cleanup,
                'cleanup_interval': self.cleanup_interval
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to get global stats: {str(e)}")
            return {'error': str(e)}


# Create singleton instance
rate_limiter = RateLimiter()
