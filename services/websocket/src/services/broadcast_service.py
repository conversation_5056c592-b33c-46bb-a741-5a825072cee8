# services/websocket/src/services/broadcast_service.py
# Broadcast service for WebSocket connections

import json
import boto3
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

from shared.logger import lambda_logger, log_business_operation
from shared.config import get_settings


class IBroadcastService(ABC):
    """Interface for WebSocket broadcast operations."""
    
    @abstractmethod
    def broadcast_to_conversation(
        self,
        conversation_id: str,
        message: Dict[str, Any],
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Broadcast message to all subscribers of a conversation. Returns count of successful sends."""
        pass
    
    @abstractmethod
    def broadcast_to_tenant(
        self,
        tenant_id: str,
        message: Dict[str, Any],
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Broadcast message to all connections in a tenant. Returns count of successful sends."""
        pass
    
    @abstractmethod
    def broadcast_presence_update(
        self,
        tenant_id: str,
        user_id: str,
        presence_data: Dict[str, Any]
    ) -> int:
        """Broadcast presence update to relevant connections. Returns count of successful sends."""
        pass
    
    @abstractmethod
    def send_to_connection(
        self,
        connection_id: str,
        message: Dict[str, Any]
    ) -> bool:
        """Send message to specific connection."""
        pass
    
    @abstractmethod
    def send_typing_indicator(
        self,
        conversation_id: str,
        user_id: str,
        is_typing: bool,
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Send typing indicator to conversation subscribers. Returns count of successful sends."""
        pass


class BroadcastService(IBroadcastService):
    """Service for broadcasting messages to WebSocket connections"""
    
    def __init__(self):
        try:
            self.api_gateway_client = boto3.client('apigatewaymanagementapi')
            self.websocket_endpoint = get_settings().get('websocket_api_endpoint')
        except Exception as e:
            lambda_logger.warning(f"Failed to initialize API Gateway client: {str(e)}")
            self.api_gateway_client = None
            self.websocket_endpoint = None
    
    def broadcast_to_conversation(
        self,
        conversation_id: str,
        message: Dict[str, Any],
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Broadcast message to all subscribers of a conversation."""
        try:
            from .subscription_manager import subscription_manager
            
            # Get all subscribers to this conversation
            subscribers = subscription_manager.get_conversation_subscribers(conversation_id)
            
            # Filter out excluded connection
            if exclude_connection_id:
                subscribers = [conn_id for conn_id in subscribers if conn_id != exclude_connection_id]
            
            successful_sends = 0
            for connection_id in subscribers:
                if self.send_to_connection(connection_id, message):
                    successful_sends += 1
            
            lambda_logger.info(f"Broadcasted to conversation {conversation_id}: {successful_sends}/{len(subscribers)} successful")
            return successful_sends
            
        except Exception as e:
            lambda_logger.error(f"Failed to broadcast to conversation: {str(e)}")
            return 0
    
    def broadcast_to_tenant(
        self,
        tenant_id: str,
        message: Dict[str, Any],
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Broadcast message to all connections in a tenant."""
        try:
            from .connection_manager import connection_manager
            
            # Get all connections for this tenant
            connections = connection_manager.get_tenant_connections(tenant_id)
            
            # Filter out excluded connection
            if exclude_connection_id:
                connections = [conn for conn in connections if conn.get('connection_id') != exclude_connection_id]
            
            successful_sends = 0
            for connection in connections:
                connection_id = connection.get('connection_id')
                if connection_id and self.send_to_connection(connection_id, message):
                    successful_sends += 1
            
            lambda_logger.info(f"Broadcasted to tenant {tenant_id}: {successful_sends}/{len(connections)} successful")
            return successful_sends
            
        except Exception as e:
            lambda_logger.error(f"Failed to broadcast to tenant: {str(e)}")
            return 0
    
    def broadcast_presence_update(
        self,
        tenant_id: str,
        user_id: str,
        presence_data: Dict[str, Any]
    ) -> int:
        """Broadcast presence update to relevant connections."""
        try:
            from .subscription_manager import subscription_manager
            
            # Get all connections subscribed to presence updates for this tenant
            subscribers = subscription_manager.get_presence_subscribers(tenant_id)
            
            message = {
                'type': 'presence_update',
                'data': {
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    **presence_data
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
            successful_sends = 0
            for connection_id in subscribers:
                if self.send_to_connection(connection_id, message):
                    successful_sends += 1
            
            lambda_logger.info(f"Broadcasted presence update for user {user_id}: {successful_sends}/{len(subscribers)} successful")
            return successful_sends
            
        except Exception as e:
            lambda_logger.error(f"Failed to broadcast presence update: {str(e)}")
            return 0
    
    def send_to_connection(
        self,
        connection_id: str,
        message: Dict[str, Any]
    ) -> bool:
        """Send message to specific connection."""
        try:
            if not self.api_gateway_client or not self.websocket_endpoint:
                lambda_logger.warning("API Gateway client not available, cannot send message")
                return False
            
            # Configure the API Gateway client with the WebSocket endpoint
            self.api_gateway_client._endpoint.host = self.websocket_endpoint
            
            response = self.api_gateway_client.post_to_connection(
                ConnectionId=connection_id,
                Data=json.dumps(message)
            )
            
            lambda_logger.debug(f"Message sent to connection {connection_id}")
            return True
            
        except self.api_gateway_client.exceptions.GoneException:
            # Connection is no longer available
            lambda_logger.info(f"Connection {connection_id} is gone, cleaning up")
            from .connection_manager import connection_manager
            connection_manager.remove_connection(connection_id)
            return False
            
        except Exception as e:
            lambda_logger.error(f"Failed to send message to connection {connection_id}: {str(e)}")
            return False
    
    def send_typing_indicator(
        self,
        conversation_id: str,
        user_id: str,
        is_typing: bool,
        exclude_connection_id: Optional[str] = None
    ) -> int:
        """Send typing indicator to conversation subscribers."""
        try:
            message = {
                'type': 'typing_indicator',
                'data': {
                    'conversation_id': conversation_id,
                    'user_id': user_id,
                    'is_typing': is_typing
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
            return self.broadcast_to_conversation(
                conversation_id=conversation_id,
                message=message,
                exclude_connection_id=exclude_connection_id
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to send typing indicator: {str(e)}")
            return 0


# Create singleton instance
broadcast_service = BroadcastService()
