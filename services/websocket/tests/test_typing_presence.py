# services/websocket/tests/test_typing_presence.py
# Tests for WebSocket typing and presence functionality

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.services.typing_manager import TypingManager
from src.services.presence_manager import PresenceManager
from src.handlers.typing import handler as typing_handler, get_typing_status_handler

class TestTypingManager:
    """Test suite for TypingManager"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.typing_manager = TypingManager()
        self.typing_manager.running = False  # Disable cleanup thread for testing
        
        self.conversation_id = 'conv-test-123'
        self.user_id = 'user-test-123'
        self.tenant_id = 'tenant-test-123'
        self.connection_id = 'conn-test-123'
    
    def test_start_typing_success(self):
        """Test successful typing indicator start"""
        with patch.object(self.typing_manager, '_broadcast_typing_indicator', return_value=(True, None)):
            # Execute
            success, error_msg = self.typing_manager.start_typing(
                conversation_id=self.conversation_id,
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                connection_id=self.connection_id,
                duration_seconds=30
            )
            
            # Assertions
            assert success is True
            assert error_msg is None
            
            # Check internal state
            assert self.conversation_id in self.typing_manager.typing_indicators
            assert self.user_id in self.typing_manager.typing_indicators[self.conversation_id]
            
            typing_info = self.typing_manager.typing_indicators[self.conversation_id][self.user_id]
            assert typing_info['tenant_id'] == self.tenant_id
            assert typing_info['connection_id'] == self.connection_id
    
    def test_stop_typing_success(self):
        """Test successful typing indicator stop"""
        # Setup initial typing state
        self.typing_manager.typing_indicators[self.conversation_id] = {
            self.user_id: {
                'started_at': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(seconds=30),
                'tenant_id': self.tenant_id,
                'connection_id': self.connection_id
            }
        }
        
        with patch.object(self.typing_manager, '_broadcast_typing_indicator', return_value=(True, None)):
            # Execute
            success, error_msg = self.typing_manager.stop_typing(
                conversation_id=self.conversation_id,
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                connection_id=self.connection_id
            )
            
            # Assertions
            assert success is True
            assert error_msg is None
            
            # Check internal state
            assert self.conversation_id not in self.typing_manager.typing_indicators
    
    def test_get_typing_users(self):
        """Test getting typing users for conversation"""
        # Setup typing state
        start_time = datetime.utcnow()
        expire_time = start_time + timedelta(seconds=30)
        
        self.typing_manager.typing_indicators[self.conversation_id] = {
            self.user_id: {
                'started_at': start_time,
                'expires_at': expire_time,
                'tenant_id': self.tenant_id,
                'connection_id': self.connection_id
            },
            'user2': {
                'started_at': start_time,
                'expires_at': start_time - timedelta(seconds=10),  # Expired
                'tenant_id': self.tenant_id,
                'connection_id': 'conn2'
            }
        }
        
        # Execute
        typing_users = self.typing_manager.get_typing_users(self.conversation_id)
        
        # Assertions
        assert len(typing_users) == 1  # Only non-expired user
        assert typing_users[0]['userId'] == self.user_id
        assert typing_users[0]['tenantId'] == self.tenant_id
    
    def test_is_user_typing(self):
        """Test checking if user is typing"""
        # Setup typing state
        self.typing_manager.typing_indicators[self.conversation_id] = {
            self.user_id: {
                'started_at': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(seconds=30),
                'tenant_id': self.tenant_id
            }
        }
        
        # Execute
        is_typing = self.typing_manager.is_user_typing(self.conversation_id, self.user_id)
        not_typing = self.typing_manager.is_user_typing(self.conversation_id, 'other-user')
        
        # Assertions
        assert is_typing is True
        assert not_typing is False
    
    def test_cleanup_expired_indicators(self):
        """Test cleanup of expired typing indicators"""
        # Setup expired typing state
        expired_time = datetime.utcnow() - timedelta(seconds=10)
        valid_time = datetime.utcnow() + timedelta(seconds=30)
        
        self.typing_manager.typing_indicators[self.conversation_id] = {
            'expired_user': {
                'started_at': expired_time,
                'expires_at': expired_time,
                'tenant_id': self.tenant_id,
                'connection_id': 'conn1'
            },
            'valid_user': {
                'started_at': datetime.utcnow(),
                'expires_at': valid_time,
                'tenant_id': self.tenant_id,
                'connection_id': 'conn2'
            }
        }
        
        with patch.object(self.typing_manager, '_broadcast_typing_indicator', return_value=(True, None)):
            # Execute
            cleaned_count = self.typing_manager.cleanup_expired_indicators()
            
            # Assertions
            assert cleaned_count == 1
            assert 'expired_user' not in self.typing_manager.typing_indicators[self.conversation_id]
            assert 'valid_user' in self.typing_manager.typing_indicators[self.conversation_id]
    
    def test_get_stats(self):
        """Test getting typing manager statistics"""
        # Setup typing state
        self.typing_manager.typing_indicators = {
            'conv1': {'user1': {}, 'user2': {}},
            'conv2': {'user3': {}}
        }
        
        # Execute
        stats = self.typing_manager.get_stats()
        
        # Assertions
        assert stats['total_conversations'] == 2
        assert stats['total_typing_users'] == 3
        assert 'timestamp' in stats


class TestPresenceManager:
    """Test suite for PresenceManager"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.presence_manager = PresenceManager()
        self.presence_manager.lambda_client = Mock()
        
        self.connection_id = 'conn-test-123'
        self.user_id = 'user-test-123'
        self.tenant_id = 'tenant-test-123'
    
    @patch.object(PresenceManager, '_call_chat_presence_service')
    @patch.object(PresenceManager, '_broadcast_presence_update')
    def test_handle_user_connect(self, mock_broadcast, mock_call_service):
        """Test handling user connection"""
        # Setup mocks
        mock_call_service.return_value = (True, {'status': 'success'})
        mock_broadcast.return_value = None
        
        # Execute
        success, error_msg = self.presence_manager.handle_user_connect(
            connection_id=self.connection_id,
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            metadata={'userAgent': 'test-browser'}
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        mock_call_service.assert_called_once()
        mock_broadcast.assert_called_once()
    
    @patch.object(PresenceManager, '_call_chat_presence_service')
    @patch.object(PresenceManager, '_broadcast_presence_update')
    def test_handle_user_disconnect(self, mock_broadcast, mock_call_service):
        """Test handling user disconnection"""
        # Setup mocks
        mock_call_service.return_value = (True, {'status': 'success'})
        mock_broadcast.return_value = None
        
        # Execute
        success, error_msg = self.presence_manager.handle_user_disconnect(
            connection_id=self.connection_id,
            user_id=self.user_id,
            tenant_id=self.tenant_id
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        mock_call_service.assert_called_once()
        mock_broadcast.assert_called_once()
    
    @patch.object(PresenceManager, '_call_chat_presence_service')
    @patch.object(PresenceManager, '_broadcast_presence_update')
    def test_update_user_status(self, mock_broadcast, mock_call_service):
        """Test updating user status"""
        # Setup mocks
        mock_call_service.return_value = (True, {'status': 'success'})
        mock_broadcast.return_value = None
        
        # Execute
        success, error_msg = self.presence_manager.update_user_status(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            status='away',
            connection_id=self.connection_id
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        mock_call_service.assert_called_once()
        mock_broadcast.assert_called_once()
    
    def test_update_user_status_invalid(self):
        """Test updating user status with invalid status"""
        # Execute
        success, error_msg = self.presence_manager.update_user_status(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            status='invalid_status'
        )
        
        # Assertions
        assert success is False
        assert 'Invalid status' in error_msg


class TestTypingHandler:
    """Test suite for typing WebSocket handler"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_connection = Mock()
        self.mock_connection.connection_id = 'conn-test-123'
        self.mock_connection.user_id = 'user-test-123'
        self.mock_connection.tenant_id = 'tenant-test-123'
        
        self.base_event = {
            'requestContext': {
                'connectionId': 'conn-test-123',
                'requestId': 'req-test-123'
            }
        }
    
    @patch('src.handlers.typing.connection_manager')
    @patch('src.handlers.typing.typing_manager')
    def test_typing_handler_start_success(self, mock_typing_manager, mock_connection_manager):
        """Test successful typing start via handler"""
        # Setup mocks
        mock_connection_manager.get_connection.return_value = self.mock_connection
        mock_typing_manager.start_typing.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': 'conv-123',
                'isTyping': True,
                'duration': 30
            })
        }
        
        # Execute
        response = typing_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['action'] == 'typing_started'
        assert body['conversationId'] == 'conv-123'
        assert body['isTyping'] is True
        
        # Verify service call
        mock_typing_manager.start_typing.assert_called_once()
    
    @patch('src.handlers.typing.connection_manager')
    @patch('src.handlers.typing.typing_manager')
    def test_typing_handler_stop_success(self, mock_typing_manager, mock_connection_manager):
        """Test successful typing stop via handler"""
        # Setup mocks
        mock_connection_manager.get_connection.return_value = self.mock_connection
        mock_typing_manager.stop_typing.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': 'conv-123',
                'isTyping': False
            })
        }
        
        # Execute
        response = typing_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['action'] == 'typing_stopped'
        assert body['isTyping'] is False
        
        # Verify service call
        mock_typing_manager.stop_typing.assert_called_once()
    
    @patch('src.handlers.typing.connection_manager')
    def test_typing_handler_connection_not_found(self, mock_connection_manager):
        """Test typing handler when connection not found"""
        # Setup mocks
        mock_connection_manager.get_connection.return_value = None
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': 'conv-123',
                'isTyping': True
            })
        }
        
        # Execute
        response = typing_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 404
        body = json.loads(response['body'])
        assert 'Connection not found' in body['error']
    
    @patch('src.handlers.typing.connection_manager')
    @patch('src.handlers.typing.typing_manager')
    def test_get_typing_status_success(self, mock_typing_manager, mock_connection_manager):
        """Test successful get typing status"""
        # Setup mocks
        mock_connection_manager.get_connection.return_value = self.mock_connection
        mock_typing_users = [
            {
                'userId': 'other-user',
                'startedAt': datetime.utcnow().isoformat(),
                'expiresAt': (datetime.utcnow() + timedelta(seconds=30)).isoformat(),
                'tenantId': 'tenant-test-123'
            }
        ]
        mock_typing_manager.get_typing_users.return_value = mock_typing_users
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': 'conv-123'
            })
        }
        
        # Execute
        response = get_typing_status_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['action'] == 'typing_status'
        assert body['conversationId'] == 'conv-123'
        assert body['count'] == 1
        assert len(body['typingUsers']) == 1


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
