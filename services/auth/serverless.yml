# services/auth/serverless.yml
# Auth service configuration - Adapted for new modular structure

service: agent-scl-auth

# Custom configuration
custom:
  serviceName: auth
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # VPC configuration
  vpcConfig:
    securityGroupIds:
      - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-LambdaSecurityGroupId
    subnetIds:
      - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnetId1
      - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-PrivateSubnetId2

  # CORS configuration
  corsOrigins: ${self:custom.stageConfig.cors.allowedOrigins}
  
  # Python requirements
  pythonRequirements:
    dockerizePip: false
    slim: true
    strip: false
    useDownloadCache: true
    useStaticCache: true

# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # VPC configuration
  vpc: ${self:custom.vpcConfig}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE: ${self:custom.dynamodbTable}
    PYTHONPATH: "/opt/python:/var/runtime:/var/task:/var/task/src"
    
    # JWT Configuration
    JWT_SECRET_NAME: ${self:custom.projectName}/${self:custom.stage}/jwt-secret
    JWT_ACCESS_TOKEN_EXPIRY: 3600
    JWT_REFRESH_TOKEN_EXPIRY: 604800
    
    # Security Configuration
    PASSWORD_MIN_LENGTH: ${self:custom.stageConfig.security.passwordMinLength, 12}
    MAX_LOGIN_ATTEMPTS: ${self:custom.stageConfig.security.maxLoginAttempts, 5}
    ACCOUNT_LOCK_DURATION: ${self:custom.stageConfig.security.accountLockDuration, 1800}
    
    # Email Configuration
    EMAIL_FROM_ADDRESS: ${self:custom.stageConfig.email.fromAddress}
    EMAIL_VERIFICATION_EXPIRY: 86400
    PASSWORD_RESET_EXPIRY: 3600

  # IAM permissions
  iam:
    role:
      statements:
        # DynamoDB permissions
        - Effect: Allow
          Action:
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
            - dynamodb:Query
            - dynamodb:Scan
          Resource:
            - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
            - Fn::Join:
                - ""
                - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
                  - "/index/*"
        
        # Secrets Manager permissions
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource:
            - arn:aws:secretsmanager:${self:custom.region}:*:secret:${self:custom.projectName}/${self:custom.stage}/*
        
        # SES permissions
        - Effect: Allow
          Action:
            - ses:SendEmail
            - ses:SendRawEmail
          Resource: "*"
        
        # SNS permissions for events
        - Effect: Allow
          Action:
            - sns:Publish
          Resource:
            - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-UserEventsTopicArn
            - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-SecurityEventsTopicArn
        
        # CloudWatch permissions
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: "*"
        
        # X-Ray permissions
        - Effect: Allow
          Action:
            - xray:PutTraceSegments
            - xray:PutTelemetryRecords
          Resource: "*"

# Functions
functions:
  # User authentication
  login:
    handler: src.handlers.login.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.quick.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.quick.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/login
          method: post
          cors:
            origins: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
    environment:
      FUNCTION_NAME: auth_login
    tracing: ${self:custom.stageConfig.lambda.defaults.tracing}

  # Health check endpoint
  health:
    handler: src.handlers.health.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.ultra-quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.ultra-quick.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.ultra-quick.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/health
          method: get
          cors:
            origins: ${self:custom.corsOrigins}
    environment:
      FUNCTION_NAME: auth_health
    tracing: ${self:custom.stageConfig.lambda.defaults.tracing}

  # User profile endpoint
  me:
    handler: src.handlers.me.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.quick.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.quick.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/me
          method: get
          cors:
            origins: ${self:custom.corsOrigins}
          authorizer:
            name: jwtAuthorizer
            type: request
            arn: !GetAtt JwtAuthorizerLambdaFunction.Arn
    environment:
      FUNCTION_NAME: auth_me
    tracing: ${self:custom.stageConfig.lambda.defaults.tracing}

  register:
    handler: src.handlers.register.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.quick.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.quick.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/register
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_register
    tracing: Active

  activateRegistration:
    handler: src.handlers.activate_registration.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.quick.memorySize}
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/activate-registration
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_activate_registration
    tracing: Active

  refreshToken:
    handler: src.handlers.refresh_token.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.quick.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.quick.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/refresh
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_refresh_token
    tracing: Active

  refreshTokenRevoke:
    handler: src.handlers.refresh_token_revoke.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/refresh-token/revoke
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn: !GetAtt JwtAuthorizerLambdaFunction.Arn
    environment:
      FUNCTION_NAME: auth_refresh_token_revoke
    tracing: Active

  logout:
    handler: src.handlers.logout.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/logout
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn: !GetAtt JwtAuthorizerLambdaFunction.Arn
    environment:
      FUNCTION_NAME: auth_logout
    tracing: Active
  
  # Email verification
  verifyEmail:
    handler: src.handlers.verify_email.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/verify-email
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_verify_email
    tracing: Active

  # Password management
  forgotPassword:
    handler: src.handlers.forgot_password.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/forgot-password
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_forgot_password
    tracing: Active

  # Token validation
  validateToken:
    handler: src.handlers.validate_token.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.quick.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.quick.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.quick.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/validate-token
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_validate_token
    tracing: Active
  
  resendVerification:
    handler: src.handlers.resend_verification.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/resend-verification
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_resend_verification
    tracing: Active
  
  resetPassword:
    handler: src.handlers.reset_password.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.standard.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.standard.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.standard.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /auth/reset-password
          method: post
          cors: true
    environment:
      FUNCTION_NAME: auth_reset_password
    tracing: Active
  
  # Token validation and authorization (comentado temporalmente)
  jwtAuthorizer:
    handler: src.handlers.authorizer.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.critical.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.critical.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.critical.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    environment:
      FUNCTION_NAME: jwt_authorizer
    tracing: Active

  # Test function for debugging layer
  testSimple:
    handler: src.handlers.test_simple.handler
    timeout: 15
    memorySize: 128
    layers:
      - ${self:custom.sharedLayerArn}
    environment:
      FUNCTION_NAME: test_simple
    tracing: Active
  
# Plugins
plugins:
  - serverless-python-requirements

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'
    - '!tests/**'
    - '!.pytest_cache/**'

# Outputs
outputs:
  JwtAuthorizerLambdaFunctionQualifiedArn:
    Description: "JWT Authorizer Lambda Function ARN"
    Value:
      Fn::GetAtt: [JwtAuthorizerLambdaFunction, Arn]
    Export:
      Name: ${self:custom.projectName}-${self:custom.stage}-JwtAuthorizerArn
