#!/usr/bin/env python3
# services/auth/src/handlers/authorizer.py
# JWT Authorizer for API Gateway with enterprise-grade security

"""
JWT Authorizer for API Gateway with enterprise-grade security and monitoring.
Validates JWT tokens and provides authorization context for protected endpoints.
"""

import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.logger import lambda_logger, audit_log
from shared.jwt_manager import get_jwt_manager
from shared.metrics import metrics_manager


def extract_token_from_event(event: Dict[str, Any]) -> Optional[str]:
    """Extract JWT token from API Gateway event."""
    # Try different ways to get the token based on event structure

    # Method 1: From authorizationToken (TOKEN authorizer)
    auth_token = event.get('authorizationToken')
    if auth_token and auth_token.startswith('Bearer '):
        return auth_token[7:]  # Remove 'Bearer ' prefix

    # Method 2: From headers (REQUEST authorizer)
    headers = event.get('headers', {})
    auth_header = headers.get('Authorization') or headers.get('authorization')
    if auth_header and auth_header.startswith('Bearer '):
        return auth_header[7:]  # Remove 'Bearer ' prefix

    # Method 3: From query parameters
    query_params = event.get('queryStringParameters') or {}
    if query_params.get('token'):
        return query_params['token']

    return None


def validate_jwt_token(token: str) -> Dict[str, Any]:
    """
    JWT token validation using shared layer.
    """
    try:
        if not token or len(token) < 10:
            raise Exception("Invalid token format")

        # Use shared JWT manager for validation
        jwt_manager = get_jwt_manager()
        payload = jwt_manager.verify_token(token, 'access')

        # Check if token is blacklisted
        try:
            dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
            blacklist_table_name = os.environ.get('TOKEN_BLACKLIST_TABLE', 'agent-scl-token-blacklist-dev')
            blacklist_table = dynamodb.Table(blacklist_table_name)

            # Check if token is in blacklist
            response = blacklist_table.get_item(
                Key={'token_hash': token[-20:]}  # Use last 20 chars as identifier
            )

            if response.get('Item'):
                lambda_logger.warning(f"Authorization failed - blacklisted token")
                raise Exception("Token has been revoked")

        except Exception as e:
            if "revoked" in str(e):
                raise
            lambda_logger.warning(f"Failed to check token blacklist: {str(e)}")
            # Continue with validation even if blacklist check fails

        # Validate required fields for authorization
        user_id = payload.get('user_id') or payload.get('sub')
        tenant_id = payload.get('tenant_id')

        if not user_id:
            raise Exception("Missing user_id in token")

        return {
            'user_id': user_id,
            'email': payload.get('email'),
            'name': payload.get('name'),
            'role': payload.get('role'),
            'tenant_id': tenant_id,
            'tenant_name': payload.get('tenant_name'),
            'exp': payload.get('exp'),
            'type': payload.get('type', 'access'),
            'jti': payload.get('jti')
        }
    except Exception as e:
        lambda_logger.warning(f"Token validation error: {str(e)}")
        raise Exception("Invalid token")


def generate_policy(principal_id: str, effect: str, resource: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Generate API Gateway authorization policy."""
    policy = {
        'principalId': principal_id,
        'policyDocument': {
            'Version': '2012-10-17',
            'Statement': [
                {
                    'Action': 'execute-api:Invoke',
                    'Effect': effect,
                    'Resource': resource
                }
            ]
        }
    }

    if context:
        # Convert all context values to strings (API Gateway requirement)
        policy['context'] = {k: str(v) for k, v in context.items()}

    return policy


def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    API Gateway Lambda Authorizer for JWT tokens with enterprise-grade security.

    This authorizer validates JWT tokens and returns authorization policies
    for API Gateway to allow or deny access to protected resources.
    """
    try:
        lambda_logger.info(f"Authorizer invoked for method: {event.get('methodArn', 'unknown')}")

        # Record authorization attempt
        metrics_manager.security.record_security_event('authorization_attempt', 'info')

        # Extract token from the event
        token = extract_token_from_event(event)

        if not token:
            lambda_logger.warning("No authorization token provided")
            metrics_manager.security.record_security_event('authorization_failed_no_token', 'warning')
            raise Exception("Authorization token required")

        lambda_logger.info(f"Token extracted successfully: {token[:20]}...")

        # Validate the token
        payload = validate_jwt_token(token)

        # Extract user context from validated payload
        user_id = payload.get("user_id") or payload.get("sub")
        tenant_id = payload.get("tenant_id")
        email = payload.get("email")
        role = payload.get("role")
        token_id = payload.get("jti")

        if not user_id:
            lambda_logger.warning("Invalid token payload: missing user_id")
            metrics_manager.security.record_security_event('authorization_failed_invalid_payload', 'medium')
            raise Exception("Invalid token payload")

        # Build principal ID (unique identifier for the user)
        principal_id = f"{user_id}#{tenant_id}" if tenant_id else user_id

        # Get the resource ARN
        method_arn = event.get('methodArn', '*')

        # Build authorization context (available to downstream services)
        auth_context = {
            'user_id': user_id,
            'tenant_id': tenant_id or '',
            'email': email or '',
            'role': role or 'MEMBER',
            'token_id': token_id or ''
        }

        # Record successful authorization
        metrics_manager.security.record_security_event('authorization_successful', 'info', tenant_id)
        metrics_manager.business.record_authorization(tenant_id)

        audit_log(
            action='authorization_successful',
            resource_type='api_endpoint',
            resource_id=method_arn,
            user_id=user_id,
            tenant_id=tenant_id,
            changes={
                'access_granted': True,
                'endpoint': method_arn,
                'role': role
            }
        )

        lambda_logger.info(f"Authorization successful for user: {user_id}, tenant: {tenant_id}")

        # Generate Allow policy
        policy = generate_policy(
            principal_id=principal_id,
            effect="Allow",
            resource=method_arn,
            context=auth_context
        )

        return policy

    except Exception as e:
        lambda_logger.warning(f"Authorization failed: {str(e)}")
        metrics_manager.security.record_security_event('authorization_failed', 'medium')

        # For any error, return a Deny policy
        method_arn = event.get('methodArn', '*')

        return generate_policy(
            principal_id="unauthorized",
            effect="Deny",
            resource=method_arn,
            context={'error': str(e)}
        )


