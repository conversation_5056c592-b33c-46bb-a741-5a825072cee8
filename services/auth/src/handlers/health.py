#!/usr/bin/env python3
# services/auth/src/handlers/health.py
# Health check handler for auth service

"""
Health check endpoint for auth service.
Provides service status and dependency health information.
"""

import json
import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone


def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Simple health check handler for auth service.

    GET /auth/health
    """
    try:
        current_time = datetime.now(timezone.utc)

        health_data = {
            "service": "auth",
            "status": "healthy",
            "timestamp": current_time.isoformat(),
            "environment": "dev",
            "region": "us-east-1",
            "version": "1.0.0",
            "endpoints": [
                "/auth/login",
                "/auth/register",
                "/auth/health",
                "/auth/refresh",
                "/auth/logout"
            ]
        }

        print(f"Health check successful for auth service at {current_time}")

        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                'Access-Control-Allow-Methods': 'GET,OPTIONS'
            },
            'body': json.dumps({
                'success': True,
                'data': health_data,
                'message': 'Auth service is healthy'
            })
        }

    except Exception as e:
        print(f"Health check failed: {str(e)}")

        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'success': False,
                'message': 'Health check failed',
                'error': str(e)
            })
        }
