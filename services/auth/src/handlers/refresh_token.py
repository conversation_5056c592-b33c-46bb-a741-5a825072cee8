#!/usr/bin/env python3
# services/auth/src/handlers/refresh_token.py
# Token refresh handler

"""
Token refresh handler with enterprise-grade security and monitoring.
Handles JWT token refresh using refresh tokens with comprehensive validation.
"""

import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response
from shared.jwt_manager import get_jwt_manager
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    TokenExpiredException,
    InvalidTokenException
)
from shared.metrics import metrics_manager
# from shared.middleware.resilience_middleware import rate_limit  # Commented out - not available
from shared.user_model import User

# Using shared layer utilities - no need for local functions


# @rate_limit(requests_per_minute=20)  # Commented out - not available
# @measure_performance("auth_refresh_token")  # Commented out - not available
@auth_handler("token_refresh")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Refresh JWT access token using refresh token with enterprise-grade security.

    POST /auth/refresh
    {
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        from shared.responses import handle_cors_preflight
        return handle_cors_preflight()

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('refresh_token'):
            return auth_error_response('Refresh token is required', 'VALIDATION_ERROR', 400)

        refresh_token = data['refresh_token'].strip()
    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Record token refresh attempt for security metrics
    metrics_manager.security.record_security_event('token_refresh_attempt', 'info')

    lambda_logger.info(f"Token refresh attempt from IP: {client_ip}, token: {refresh_token[:20]}...")

    try:
        # Use JWT manager to validate and refresh token
        jwt_manager_instance = get_jwt_manager()

        # Validate refresh token
        try:
            payload = jwt_manager_instance.verify_token(refresh_token, 'refresh')
        except TokenExpiredException:
            lambda_logger.warning(f"Token refresh failed - expired token from IP: {client_ip}")
            metrics_manager.security.record_security_event('token_refresh_failed_expired', 'warning')
            return auth_error_response('Refresh token has expired', 'TOKEN_EXPIRED', 401)
        except InvalidTokenException:
            lambda_logger.warning(f"Token refresh failed - invalid token from IP: {client_ip}")
            metrics_manager.security.record_security_event('token_refresh_failed_invalid', 'high')
            return auth_error_response('Invalid refresh token', 'INVALID_TOKEN', 401)
        except Exception as e:
            lambda_logger.warning(f"Token refresh failed - invalid token from IP: {client_ip}")
            metrics_manager.security.record_security_event('token_refresh_failed_invalid', 'warning')
            return auth_error_response('Invalid refresh token', 'INVALID_TOKEN', 401)

        # Verify token type
        if payload.get('type') != 'refresh':
            lambda_logger.warning(f"Token refresh failed - wrong token type from IP: {client_ip}")
            metrics_manager.security.record_security_event('token_refresh_failed_wrong_type', 'medium')
            return auth_error_response('Invalid token type', 'INVALID_TOKEN_TYPE', 401)

        # Get user information
        user_id = payload.get('sub')
        tenant_id = payload.get('tenant_id')

        if not user_id or not tenant_id:
            lambda_logger.warning(f"Token refresh failed - missing claims from IP: {client_ip}")
            metrics_manager.security.record_security_event('token_refresh_failed_missing_claims', 'medium')
            return auth_error_response('Invalid token claims', 'INVALID_TOKEN_CLAIMS', 401)
        # Get user using User model
        user_obj = User.get_by_id(user_id, tenant_id)
        if not user_obj:
            lambda_logger.warning(f"Token refresh failed - user not found: {user_id}")
            metrics_manager.security.record_security_event('token_refresh_failed_user_not_found', 'warning')
            return auth_error_response('User not found', 'USER_NOT_FOUND', 401)

        # Verify user account is active (subscription validated)
        user_status = user_obj.status.value if hasattr(user_obj.status, 'value') else str(user_obj.status)
        if user_status != 'ACTIVE':
            lambda_logger.warning(f"Token refresh failed - user not active: {user_id}, status: {user_status}")
            metrics_manager.security.record_security_event('token_refresh_failed_user_not_active', 'warning')
            return auth_error_response('Account not activated. Please complete subscription payment.', 'ACCOUNT_NOT_ACTIVE', 403)

        # Get tenant information for token generation
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        table_name = os.environ.get('DYNAMODB_TABLE', 'agent-scl-main-dev')
        table = dynamodb.Table(table_name)

        tenant_response = table.get_item(
            Key={
                'PK': f'TENANT#{tenant_id}',
                'SK': f'TENANT#{tenant_id}'
            }
        )

        tenant = tenant_response.get('Item', {})

        # Generate new access token (refresh token remains the same)
        current_time = int(time.time())

        new_access_token = jwt_manager_instance.generate_access_token(
            user_id=user_obj.user_id,
            email=user_obj.email,
            tenant_id=user_obj.tenant_id,
            role=user_obj.role.value if hasattr(user_obj.role, 'value') else str(user_obj.role),
            additional_claims={
                'name': user_obj.name,
                'tenant_name': tenant.get('company_name')
            }
        )
        # Record successful token refresh metrics and audit
        metrics_manager.security.record_security_event('token_refresh_successful', 'info', tenant_id)
        metrics_manager.business.record_token_refresh(tenant_id)

        audit_log(
            action='token_refresh_successful',
            resource_type='token',
            resource_id=refresh_token[:20] + "...",
            user_id=user_obj.user_id,
            tenant_id=user_obj.tenant_id,
            client_ip=client_ip,
            changes={'new_access_token_generated': True}
        )

        # Prepare response data
        response_data = {
            'access_token': new_access_token,
            'token_type': 'Bearer',
            'expires_in': 3600,  # 1 hour
            'user': {
                'user_id': user_obj.user_id,
                'name': user_obj.name,
                'email': user_obj.email,
                'role': user_obj.role.value if hasattr(user_obj.role, 'value') else str(user_obj.role),
                'status': user_obj.status.value if hasattr(user_obj.status, 'value') else str(user_obj.status)
            },
            'tenant': {
                'tenant_id': tenant.get('tenant_id'),
                'company_name': tenant.get('company_name'),
                'status': tenant.get('status')
            }
        }

        lambda_logger.info(f"Token refresh successful for user: {user_obj.user_id}")
        return auth_success_response(response_data, 'Token refreshed successfully')

    except TokenExpiredException as e:
        lambda_logger.warning(f"Token refresh failed - expired token from IP: {client_ip}")
        metrics_manager.security.record_security_event('token_refresh_failed_expired', 'warning')
        return auth_error_response(str(e), 'TOKEN_EXPIRED', 401)

    except InvalidTokenException as e:
        lambda_logger.warning(f"Token refresh failed - invalid token from IP: {client_ip}")
        metrics_manager.security.record_security_event('token_refresh_failed_invalid', 'high')
        return auth_error_response(str(e), 'INVALID_TOKEN', 401)

    except ValidationException as e:
        lambda_logger.warning(f"Token refresh failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('token_refresh_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except Exception as e:
        lambda_logger.error(f"Token refresh failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('token_refresh_failed_system_error', 'high')
        return auth_error_response('Token refresh failed', 'TOKEN_REFRESH_ERROR', 500)

