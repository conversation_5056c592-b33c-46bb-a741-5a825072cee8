#!/usr/bin/env python3
# services/auth/src/handlers/verify_email.py
# Email verification handler

"""
Email verification handler with enterprise-grade security and monitoring.
Handles email verification using verification tokens with comprehensive validation.
"""

import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response, handle_cors_preflight
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    ResourceNotFoundException,
    TokenExpiredException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.user_model import User


# Using shared layer utilities - no need for local functions


@rate_limit(requests_per_minute=10)  # Moderate rate limiting for email verification
@measure_performance("auth_verify_email")
@auth_handler("email_verification")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Verify user email address using verification token with enterprise-grade security.

    POST /auth/verify-email
    {
        "token": "verification_token_here"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('token'):
            return auth_error_response('Verification token is required', 'VALIDATION_ERROR', 400)

        token = data['token'].strip()
    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record email verification attempt for security metrics
    metrics_manager.security.record_security_event('email_verification_attempt', 'info')

    lambda_logger.info(f"Email verification attempt from IP: {client_ip}, token: {token[:20]}...")

    try:
        # Connect to DynamoDB to find user by verification token
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        table_name = os.environ.get('DYNAMODB_TABLE', 'agent-scl-main-dev')
        table = dynamodb.Table(table_name)

        # Find user by verification token
        response = table.scan(
            FilterExpression='verification_token = :token',
            ExpressionAttributeValues={
                ':token': token
            }
        )

        if not response.get('Items'):
            lambda_logger.warning(f"Email verification failed - invalid token from IP: {client_ip}")
            metrics_manager.security.record_security_event('email_verification_failed_invalid_token', 'warning')
            return auth_error_response('Invalid or expired verification token', 'INVALID_TOKEN', 400)

        user_data = response['Items'][0]
        user_id = user_data.get('user_id')
        tenant_id = user_data.get('tenant_id')
        email = user_data.get('email')

        # Check if already verified
        if user_data.get('email_verified'):
            lambda_logger.info(f"Email verification - already verified: {email}")
            metrics_manager.security.record_security_event('email_verification_already_verified', 'info')
            return auth_success_response(
                {
                    'user_id': user_id,
                    'email': email,
                    'email_verified': True,
                    'status': user_data.get('status')
                },
                'Email already verified'
            )
        # Update user to mark email as verified and activate account
        current_time = int(time.time())

        # Update user record directly in DynamoDB
        table.update_item(
            Key={
                'PK': user_data['PK'],
                'SK': user_data['SK']
            },
            UpdateExpression='SET email_verified = :verified, email_verified_at = :verified_at, updated_at = :updated_at, #status = :status REMOVE verification_token',
            ExpressionAttributeNames={
                '#status': 'status'  # status is a reserved word in DynamoDB
            },
            ExpressionAttributeValues={
                ':verified': True,
                ':verified_at': current_time,
                ':updated_at': current_time,
                ':status': 'active'  # Activate user account upon email verification
            }
        )

        # Record successful email verification metrics and audit
        metrics_manager.security.record_security_event('email_verification_successful', 'info', tenant_id)
        metrics_manager.business.record_email_verification(tenant_id)

        audit_log(
            action='email_verification_successful',
            resource_type='user',
            resource_id=user_id,
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'email_verified': True,
                'status': 'active',
                'verification_token_removed': True
            }
        )

        lambda_logger.info(f"Email verification successful for user: {user_id}, email: {email}")

        # Prepare response data
        response_data = {
            'user_id': user_id,
            'email': email,
            'email_verified': True,
            'email_verified_at': current_time,
            'status': 'active',
            'verified_at': current_time
        }

        return auth_success_response(response_data, 'Email verified successfully')

    except ResourceNotFoundException as e:
        lambda_logger.warning(f"Email verification failed - token not found from IP: {client_ip}")
        metrics_manager.security.record_security_event('email_verification_failed_token_not_found', 'warning')
        return auth_error_response(str(e), 'TOKEN_NOT_FOUND', 404)

    except TokenExpiredException as e:
        lambda_logger.warning(f"Email verification failed - token expired from IP: {client_ip}")
        metrics_manager.security.record_security_event('email_verification_failed_token_expired', 'warning')
        return auth_error_response(str(e), 'TOKEN_EXPIRED', 400)

    except ValidationException as e:
        lambda_logger.warning(f"Email verification failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('email_verification_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except Exception as e:
        lambda_logger.error(f"Email verification failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('email_verification_failed_system_error', 'high')
        return auth_error_response('Email verification failed', 'EMAIL_VERIFICATION_ERROR', 500)

