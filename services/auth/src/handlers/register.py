#!/usr/bin/env python3
# services/auth/src/handlers/register.py
# User registration handler

"""
User registration handler with enterprise-grade security and monitoring.
Handles tenant and master user creation during registration.
"""

import json
import time
import uuid
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.validators import RegisterRequestValidator
from shared.responses import auth_success_response, auth_error_response, handle_cors_preflight
from shared.auth import password_manager
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.user_model import User  # Import unified User model
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    ResourceConflictException,
    WeakPasswordException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit


# Using shared layer utilities - no need for local functions


@rate_limit(requests_per_minute=5)  # Stricter rate limiting for registration
@measure_performance("auth_register")
@auth_handler("user_registration")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Register a new user for an existing tenant with enterprise-grade security.

    POST /auth/register
    {
        "tenant_id": "tenant-123",
        "email": "<EMAIL>",
        "password": "SecurePassword123!",
        "name": "John Doe",
        "role": "MASTER"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        # Use unified validation from shared layer
        from shared.validators import UnifiedUserValidator, UnifiedRequestValidator

        # Parse request using unified parser
        parsed_data = UnifiedRequestValidator.parse_request_body(event)

        # Handle backward compatibility: 'name' -> 'first_name/last_name'
        if 'name' in parsed_data and 'first_name' not in parsed_data:
            name_parts = parsed_data['name'].strip().split(' ', 1)
            parsed_data['first_name'] = name_parts[0]
            parsed_data['last_name'] = name_parts[1] if len(name_parts) > 1 else ''

        # Validate required fields for unified model
        required_fields = ['tenant_id', 'email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not parsed_data.get(field):
                return auth_error_response(f"{field.replace('_', ' ').title()} is required", 'VALIDATION_ERROR', 400)

        # Use unified user validation
        is_valid, validation_errors = UnifiedUserValidator.validate_user_data(parsed_data)
        if not is_valid:
            return auth_error_response(f"Validation failed: {'; '.join(validation_errors)}", 'VALIDATION_ERROR', 422)

        validated_data = {
            'tenant_id': parsed_data['tenant_id'].strip(),
            'email': parsed_data['email'].strip().lower(),
            'password': parsed_data['password'],
            'first_name': parsed_data['first_name'].strip(),
            'last_name': parsed_data['last_name'].strip(),
            'role': parsed_data.get('role', 'MASTER').upper()
        }
    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)
    tenant_id = validated_data['tenant_id']
    email = validated_data['email']
    password = validated_data['password']
    first_name = validated_data['first_name']
    last_name = validated_data['last_name']
    role = validated_data.get('role', 'MASTER')

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Record registration attempt for security metrics
    metrics_manager.security.record_security_event('registration_attempt', 'info')

    # Audit log for registration attempt
    audit_log(
        action='registration_attempt',
        resource_type='user',
        resource_id=email,
        user_id=None,  # Not authenticated yet
        tenant_id=tenant_id,
        client_ip=client_ip
    )

    lambda_logger.info(f"User registration attempt for tenant: {tenant_id}, email: {email} from IP: {client_ip}")

    try:
        # Import shared database client
        from shared.database import db_client

        # Debug logging (sanitized)
        lambda_logger.info(f"Auth service initialized for tenant registration")
        lambda_logger.info(f"Looking for tenant: {tenant_id[:8]}...")

        # Use shared database client to get tenant
        tenant_obj = db_client.get_item(
            pk=f'TENANT#{tenant_id}',
            sk='PROFILE',
            tenant_id=tenant_id
        )

        # Debug logging
        lambda_logger.info(f"Shared DB client response: {tenant_obj}")
        if not tenant_obj:
            lambda_logger.warning(f"Registration failed - invalid tenant: {tenant_id}")
            metrics_manager.security.record_security_event('registration_failed_invalid_tenant', 'warning')
            return auth_error_response('Invalid tenant ID', 'INVALID_TENANT', 400)

        # Check if tenant allows registration (case-insensitive)
        # TRIAL: Allows registration during onboarding process
        # ACTIVE: Fully active tenant
        # SUSPENDED/INACTIVE: Block registration
        tenant_status = tenant_obj.get('status', '').upper()
        if tenant_status not in ['ACTIVE', 'TRIAL']:
            lambda_logger.warning(f"Registration failed - tenant not accepting registrations: {tenant_id}, status: {tenant_status}")
            metrics_manager.security.record_security_event('registration_failed_inactive_tenant', 'warning')
            return auth_error_response('Tenant is not accepting new registrations', 'TENANT_INACTIVE', 403)

        # Check if email already exists
        existing_user = User.get_by_email(email)
        if existing_user:
            lambda_logger.warning(f"Registration failed - email already exists: {email}")
            metrics_manager.security.record_security_event('registration_failed_email_exists', 'warning')
            return auth_error_response('Email already registered', 'EMAIL_EXISTS', 409)

        # Validate password strength
        try:
            password_manager.validate_password_strength(password)
        except WeakPasswordException as e:
            lambda_logger.warning(f"Registration failed - weak password: {email}")
            metrics_manager.security.record_security_event('registration_failed_weak_password', 'medium')
            return auth_error_response(str(e), 'WEAK_PASSWORD', 400)

        # Create new user using User model
        user_id = str(uuid.uuid4())
        password_hash = password_manager.hash_password(password, user_id)
        verification_token = str(uuid.uuid4())
        current_time = int(time.time())

        # Import UserStatus for proper enum usage
        from shared.models import UserStatus

        # Create user using unified User model (correct parameter order)
        user_obj = User(
            user_id=user_id,
            email=email,
            tenant_id=tenant_id,
            role=role,
            status=UserStatus.PENDING_VERIFICATION,  # Pending verification until email is confirmed
            first_name=first_name,
            last_name=last_name,
            password_hash=password_hash,
            email_verified=False,
            email_verification_token=verification_token,
            created_at=current_time,
            updated_at=current_time
        )

        # Save user to database
        user_obj.save()

        # Send verification email
        try:
            from shared.email import email_service
            email_sent = email_service.send_verification_email(
                to_email=email,
                verification_token=verification_token,
                user_name=name
            )
            if email_sent:
                lambda_logger.info(f"Verification email sent successfully to {email}")
            else:
                lambda_logger.warning(f"Failed to send verification email to {email}")
        except Exception as e:
            lambda_logger.error(f"Error sending verification email to {email}: {str(e)}")

        # Record successful registration metrics and audit
        metrics_manager.security.record_security_event('registration_successful', 'info', tenant_id)
        metrics_manager.business.record_user_registration(tenant_id, tenant_obj.get('plan', 'FREE'))

        audit_log(
            action='registration_successful',
            resource_type='user',
            resource_id=user_id,
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'email': email,
                'name': name,
                'role': role,
                'status': 'PENDING_VERIFICATION'
            }
        )

        # Return clean response (remove sensitive data)
        response_data = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'name': name,
            'email': email,
            'role': role,
            'status': 'PENDING_VERIFICATION',
            'email_verified': False,
            'created_at': current_time,
            'verification_token': verification_token
        }

        lambda_logger.info(f"User registration successful: {user_id}")

        # Create success response with 201 status code for creation
        response = auth_success_response(response_data, 'User registered successfully - pending verification')
        response['statusCode'] = 201
        return response

    except ResourceConflictException as e:
        lambda_logger.warning(f"Registration failed - resource conflict: {email}")
        metrics_manager.security.record_security_event('registration_failed_conflict', 'warning')
        return auth_error_response(str(e), 'RESOURCE_CONFLICT', 409)

    except WeakPasswordException as e:
        lambda_logger.warning(f"Registration failed - weak password: {email}")
        metrics_manager.security.record_security_event('registration_failed_weak_password', 'medium')
        return auth_error_response(str(e), 'WEAK_PASSWORD', 400)

    except ValidationException as e:
        lambda_logger.warning(f"Registration failed - validation error: {email}")
        metrics_manager.security.record_security_event('registration_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except Exception as e:
        lambda_logger.error(f"Registration failed - unexpected error: {email}, error: {str(e)}")
        metrics_manager.security.record_security_event('registration_failed_system_error', 'high')
        return auth_error_response('Registration failed', 'REGISTRATION_ERROR', 500)


