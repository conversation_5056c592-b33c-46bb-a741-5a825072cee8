#!/usr/bin/env python3
# services/auth/src/handlers/activate_registration.py
# Activate registration after successful payment

"""
Activate registration handler with enterprise-grade security and monitoring.
Activates user account and completes registration flow after successful payment.
"""

import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response, handle_cors_preflight
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    ResourceNotFoundException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.user_model import User


# Using shared layer utilities - no need for local functions


def create_tenant_s3_structure(tenant_id: str) -> bool:
    """Create S3 folder structure for new tenant."""
    try:
        s3 = boto3.client('s3', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        bucket_name = os.environ.get('S3_DATA_BUCKET', 'agent-scl-data-dev')

        # Create folder structure by uploading empty objects
        folders = [
            f'tenants/{tenant_id}/raw_data/',
            f'tenants/{tenant_id}/processed_data/',
            f'tenants/{tenant_id}/templates/',
            f'tenants/{tenant_id}/reports/',
            f'tenants/{tenant_id}/exports/',
            f'tenants/{tenant_id}/backups/'
        ]

        for folder in folders:
            s3.put_object(
                Bucket=bucket_name,
                Key=folder,
                Body=b'',
                ContentType='application/x-directory'
            )

        print(f"S3 structure created for tenant: {tenant_id}")
        return True
    except Exception as e:
        print(f"Error creating S3 structure: {str(e)}")
        return False


def send_welcome_emails(tenant_id: str, user_email: str, user_name: str) -> bool:
    """
    Send welcome and verification emails.
    TODO: Implement actual email sending when SES integration is ready.
    """
    try:
        lambda_logger.info(f"Sending welcome emails to {user_email} for tenant {tenant_id}")

        # TODO: Integrate with SES
        # - Send welcome email
        # - Send email verification
        # - Send account activation confirmation

        return True
    except Exception as e:
        lambda_logger.error(f"Error sending emails: {str(e)}")
        return False


@rate_limit(requests_per_minute=5)  # Moderate rate limiting for activation
@measure_performance("auth_activate_registration")
@auth_handler("registration_activation")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Activate registration after successful payment with enterprise-grade security.

    POST /auth/activate-registration
    {
        "tenant_id": "tenant-123",
        "subscription_id": "sub-456"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('tenant_id'):
            return auth_error_response('Tenant ID is required', 'VALIDATION_ERROR', 400)

        tenant_id = data['tenant_id'].strip()
        subscription_id = data.get('subscription_id', '').strip()

    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record registration activation attempt for security metrics
    metrics_manager.security.record_security_event('registration_activation_attempt', 'info')

    lambda_logger.info(f"Registration activation attempt from IP: {client_ip}, tenant: {tenant_id}")

    try:
        # Connect to DynamoDB
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        table_name = os.environ.get('DYNAMODB_TABLE', 'agent-scl-main-dev')
        table = dynamodb.Table(table_name)
        current_time = int(time.time())

        # 1. Update tenant status to 'active'
        try:
            tenant_response = table.update_item(
                Key={
                    'PK': f'TENANT#{tenant_id}',
                    'SK': f'TENANT#{tenant_id}'
                },
                UpdateExpression='SET #status = :status, #updated_at = :updated_at, GSI1SK = :gsi1sk',
                ExpressionAttributeNames={
                    '#status': 'status',
                    '#updated_at': 'updated_at'
                },
                ExpressionAttributeValues={
                    ':status': 'active',
                    ':updated_at': current_time,
                    ':gsi1sk': f'STATUS#active#{current_time}'
                },
                ReturnValues='ALL_NEW'
            )

            lambda_logger.info(f"Tenant {tenant_id} status updated to active")

        except Exception as e:
            lambda_logger.error(f"Failed to update tenant status: {str(e)}")
            metrics_manager.security.record_security_event('registration_activation_failed_tenant_update', 'high')
            return auth_error_response('Failed to activate tenant', 'TENANT_ACTIVATION_ERROR', 500)

        # 2. Find and update user status to 'active'
        try:
            user_response = table.query(
                IndexName='GSI1',
                KeyConditionExpression='GSI1PK = :tenant_pk',
                ExpressionAttributeValues={
                    ':tenant_pk': f'TENANT#{tenant_id}'
                }
            )

            user_updated = False
            user_email = None
            user_name = None

            for item in user_response.get('Items', []):
                if item.get('entity_type') == 'USER':
                    # Extract user data before updating
                    user_email = item.get('email', '<EMAIL>')
                    user_name = item.get('name', 'Unknown User')

                    # Update user status
                    table.update_item(
                        Key={
                            'PK': item['PK'],
                            'SK': item['SK']
                        },
                        UpdateExpression='SET #status = :status, #updated_at = :updated_at',
                        ExpressionAttributeNames={
                            '#status': 'status',
                            '#updated_at': 'updated_at'
                        },
                        ExpressionAttributeValues={
                            ':status': 'active',
                            ':updated_at': current_time
                        }
                    )
                    user_updated = True
                    user_email = item.get('email', user_email)
                    user_name = item.get('name', user_name)
                    user_id = item.get('user_id')
                    break

            if not user_updated:
                lambda_logger.warning(f"No user found for tenant {tenant_id}")
                # Set fallback values for tenant-only activation
                user_email = f"admin@tenant-{tenant_id[:8]}.agentscl.com"
                user_name = f"Tenant {tenant_id[:8]} Admin"

        except Exception as e:
            lambda_logger.error(f"Failed to update user status: {str(e)}")
            metrics_manager.security.record_security_event('registration_activation_failed_user_update', 'medium')

        # 3. Create tenant S3 folder structure
        s3_created = create_tenant_s3_structure(tenant_id)

        # Send welcome emails
        email_sent = send_welcome_emails(
            tenant_id=tenant_id,
            user_email=user_email,
            user_name=user_name
        )

        # Record successful registration activation metrics and audit
        metrics_manager.security.record_security_event('registration_activation_successful', 'info', tenant_id)
        metrics_manager.business.record_registration_activation(tenant_id)

        audit_log(
            action='registration_activation_successful',
            resource_type='tenant',
            resource_id=tenant_id,
            user_id=user_id if user_updated else None,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'tenant_status': 'active',
                'user_status': 'active' if user_updated else 'not_found',
                's3_structure_created': s3_created,
                'emails_sent': email_sent,
                'subscription_id': subscription_id
            }
        )

        lambda_logger.info(f"Registration activation completed for tenant: {tenant_id}")

        # Prepare response data
        activation_data = {
            'tenant_id': tenant_id,
            'subscription_id': subscription_id,
            'activation_status': 'completed',
            'activated_at': current_time,
            'user_status': 'active' if user_updated else 'not_found',
            'tenant_status': 'active',
            'user_email': user_email,
            'user_name': user_name,
            'resources_created': {
                's3_tenant_folder': f'/tenants/{tenant_id}/',
                's3_creation_status': 'success' if s3_created else 'failed',
                'database_records': 'updated',
                'configuration': 'initialized'
            },
            'emails_sent': {
                'welcome_email': email_sent,
                'verification_email': email_sent,
                'activation_confirmation': email_sent,
                'status': 'sent' if email_sent else 'failed'
            },
            'next_steps': [
                'Check your email for verification link',
                'Complete email verification',
                'Login to access your dashboard'
            ]
        }

        return auth_success_response(activation_data, 'Registration activated successfully')

    except ValidationException as e:
        lambda_logger.warning(f"Registration activation failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('registration_activation_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except ResourceNotFoundException as e:
        lambda_logger.warning(f"Registration activation failed - resource not found from IP: {client_ip}")
        metrics_manager.security.record_security_event('registration_activation_failed_not_found', 'warning')
        return auth_error_response(str(e), 'RESOURCE_NOT_FOUND', 404)

    except Exception as e:
        lambda_logger.error(f"Registration activation failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('registration_activation_failed_system_error', 'high')
        return auth_error_response('Registration activation failed', 'ACTIVATION_ERROR', 500)
