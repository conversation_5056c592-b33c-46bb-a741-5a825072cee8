#!/usr/bin/env python3
# services/auth/src/handlers/login.py
# User login handler with shared layer integration

"""
User login handler.
Handles user authentication and JWT token generation using shared layer utilities.
"""

import hashlib
import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.auth import password_manager
from shared.jwt_manager import get_jwt_manager
from shared.responses import auth_success_response, auth_error_response, handle_cors_preflight
from shared.validators import LoginRequestValidator
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    AccountLockedException,
    EmailNotVerifiedException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.user_model import User


# Using shared layer utilities - no need for local functions


def generate_simple_jwt(user: dict, tenant: dict, token_type: str) -> str:
    """Generate simple JWT token."""
    import base64

    current_time = int(time.time())
    expires_in = 3600 if token_type == 'access' else 604800  # 1 hour vs 7 days

    # Create header
    header = {"alg": "HS256", "typ": "JWT"}

    # Create payload
    payload = {
        "sub": user.get('user_id'),
        "email": user.get('email'),
        "name": user.get('name'),
        "role": user.get('role'),
        "tenant_id": user.get('tenant_id'),
        "tenant_name": tenant.get('company_name'),
        "iat": current_time,
        "exp": current_time + expires_in,
        "type": token_type
    }

    # Encode header and payload
    header_encoded = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

    # Create signature
    message = f"{header_encoded}.{payload_encoded}"
    secret = os.environ.get('JWT_SECRET', 'default-secret-key')
    signature = hashlib.sha256(f"{message}{secret}".encode()).hexdigest()[:32]
    signature_encoded = base64.urlsafe_b64encode(signature.encode()).decode().rstrip('=')

    return f"{header_encoded}.{payload_encoded}.{signature_encoded}"


def store_refresh_token(refresh_token: str, user_id: str, client_ip: str) -> None:
    """Store refresh token in sessions table."""
    try:
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        sessions_table = dynamodb.Table('agent-scl-sessions-dev')

        current_time = int(time.time())
        session_id = f"session_{current_time}_{user_id[:8]}"

        sessions_table.put_item(
            Item={
                'session_id': session_id,
                'user_id': user_id,
                'refresh_token': refresh_token,
                'client_ip': client_ip,
                'created_at': current_time,
                'expires_at': current_time + 604800,  # 7 days
                'ttl': current_time + 604800
            }
        )
        print(f"Session stored successfully: {session_id}")
    except Exception as e:
        print(f"Error storing refresh token: {str(e)}")
        # Continue without failing the login


@rate_limit(requests_per_minute=10)  # Strict rate limiting for login
@measure_performance("auth_login")
@auth_handler("user_login")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Authenticate user and return JWT tokens using shared layer.

    POST /auth/login
    {
        "email": "<EMAIL>",
        "password": "SecurePassword123!"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Validate request body using shared layer
    body = event.get('body', '{}')
    validated_data = LoginRequestValidator.validate_from_body(body)
    email = validated_data['email']
    password = validated_data['password']

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Record login attempt for security metrics
    metrics_manager.security.record_security_event('login_attempt', 'info')

    # Audit log for login attempt
    audit_log(
        action='login_attempt',
        resource_type='user',
        resource_id=email,
        user_id=None,  # Not authenticated yet
        tenant_id=None,
        client_ip=client_ip
    )

    lambda_logger.info(f"Login attempt for email: {email} from IP: {client_ip}")

    try:
        # Use User model for authentication (includes account lockout and email verification)
        user_obj = User.get_by_email(email)

        if not user_obj:
            lambda_logger.warning(f"Login failed - user not found: {email}")
            metrics_manager.security.record_security_event('login_failed_user_not_found', 'warning')
            return auth_error_response('Invalid email or password', 'INVALID_CREDENTIALS', 401)

        # Authenticate using User model (handles lockout, email verification, password check)
        if not user_obj.authenticate(password):
            lambda_logger.warning(f"Login failed - authentication failed: {email}")
            metrics_manager.security.record_security_event('login_failed_invalid_credentials', 'warning')
            return auth_error_response('Invalid email or password', 'INVALID_CREDENTIALS', 401)

        # Verify user account is active (subscription validated)
        user_status = user_obj.status.value if hasattr(user_obj.status, 'value') else str(user_obj.status)
        if user_status != 'ACTIVE':
            lambda_logger.warning(f"Login failed - user not active: {email}, status: {user_status}")
            metrics_manager.security.record_security_event('login_failed_user_not_active', 'warning')
            audit_log(
                action='login_failed_not_active',
                resource_type='user',
                resource_id=user_obj.user_id,
                user_id=user_obj.user_id,
                tenant_id=user_obj.tenant_id,
                client_ip=client_ip,
                changes={'status': user_status}
            )
            return auth_error_response('Account not activated. Please complete subscription payment.', 'ACCOUNT_NOT_ACTIVE', 403)

    except AccountLockedException as e:
        lambda_logger.warning(f"Login failed - account locked: {email}")
        metrics_manager.security.record_security_event('login_failed_account_locked', 'high')
        audit_log(
            action='login_failed_account_locked',
            resource_type='user',
            resource_id=email,
            client_ip=client_ip
        )
        return auth_error_response(str(e), 'ACCOUNT_LOCKED', 423)

    except EmailNotVerifiedException as e:
        lambda_logger.warning(f"Login failed - email not verified: {email}")
        metrics_manager.security.record_security_event('login_failed_email_not_verified', 'medium')
        return auth_error_response(str(e), 'EMAIL_NOT_VERIFIED', 403)

    except Exception as e:
        lambda_logger.error(f"Login failed - unexpected error: {email}, error: {str(e)}")
        metrics_manager.security.record_security_event('login_failed_system_error', 'high')
        return auth_error_response('Authentication failed', 'AUTHENTICATION_ERROR', 500)

    # Note: Tenant data removed for performance - use GET /auth/me and GET /tenant/profile instead

    # Generate JWT tokens using shared layer
    current_time = int(time.time())
    jwt_manager_instance = get_jwt_manager()

    # Generate tokens with minimal claims for performance
    access_token = jwt_manager_instance.generate_access_token(
        user_id=user_obj.user_id,
        email=user_obj.email,
        tenant_id=user_obj.tenant_id,
        role=user_obj.role.value if hasattr(user_obj.role, 'value') else str(user_obj.role),
        additional_claims={
            'name': user_obj.name
        }
    )

    refresh_token = jwt_manager_instance.generate_refresh_token(
        user_id=user_obj.user_id,
        tenant_id=user_obj.tenant_id,
        additional_claims={
            'email': user_obj.email,
            'name': user_obj.name
        }
    )

    # Update last login using User model
    user_obj.last_login = current_time
    user_obj.updated_at = current_time
    user_obj.save()

    # Store refresh token in sessions table
    store_refresh_token(refresh_token, user_obj.user_id, client_ip)

    # Record successful login metrics and audit
    metrics_manager.security.record_security_event('login_successful', 'info', user_obj.tenant_id)
    metrics_manager.business.record_user_login(user_obj.tenant_id)

    audit_log(
        action='login_successful',
        resource_type='user',
        resource_id=user_obj.user_id,
        user_id=user_obj.user_id,
        tenant_id=user_obj.tenant_id,
        client_ip=client_ip,
        changes={'last_login': current_time}
    )

    # Prepare minimal response data (OAuth2 standard)
    response_data = {
        'access_token': access_token,
        'refresh_token': refresh_token,
        'token_type': 'Bearer',
        'expires_in': 3600  # 1 hour
    }

    lambda_logger.info(f"Login successful for user: {email}")
    return auth_success_response(response_data, 'Login successful')

# Error handling is now managed by the @auth_handler decorator


