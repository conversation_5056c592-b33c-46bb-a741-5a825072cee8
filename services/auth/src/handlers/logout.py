#!/usr/bin/env python3
# services/auth/src/handlers/logout.py
# Logout handler

"""
Logout handler with enterprise-grade security and monitoring.
Handles user logout and token invalidation with comprehensive audit trail.
"""

import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response
from shared.jwt_manager import get_jwt_manager
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    TokenExpiredException,
    InvalidTokenException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.user_model import User

# Using shared layer utilities - no need for local functions


@rate_limit(requests_per_minute=30)  # Higher limit for logout operations
@measure_performance("auth_logout")
@auth_handler("user_logout")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Logout user and invalidate tokens with enterprise-grade security.

    POST /auth/logout
    {
        "refresh_token": "optional_refresh_token_here"  // Optional
    }

    Requires Authorization header with Bearer token.
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        from shared.responses import handle_cors_preflight
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Get authorization context from authorizer (when enabled)
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')

    # If no auth context, this endpoint requires authorization
    if not user_id:
        lambda_logger.warning(f"Logout failed - missing authorization context from IP: {client_ip}")
        metrics_manager.security.record_security_event('logout_failed_missing_auth', 'warning')
        return auth_error_response('Authorization required', 'AUTHORIZATION_REQUIRED', 401)

    # Get access token from Authorization header for blacklisting
    headers = event.get('headers', {})
    auth_header = headers.get('Authorization') or headers.get('authorization', '')
    access_token = auth_header[7:] if auth_header and auth_header.startswith('Bearer ') else None

    # Validate request body (refresh token is optional)
    body = event.get('body', '{}')
    refresh_token = None
    try:
        if body and body != '{}':
            data = json.loads(body) if isinstance(body, str) else body
            refresh_token = data.get('refresh_token', '').strip() if data.get('refresh_token') else None
    except json.JSONDecodeError:
        lambda_logger.warning(f"Logout failed - invalid JSON from IP: {client_ip}")
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record logout attempt for security metrics
    metrics_manager.security.record_security_event('logout_attempt', 'info')

    lambda_logger.info(f"Logout attempt from IP: {client_ip}, access_token: {access_token[:20]}...")

    try:
        # User is already authenticated by authorizer, proceed with logout
        # user_id and tenant_id are already available from auth_context

        # Initialize JWT manager and payload variable
        jwt_manager = get_jwt_manager()
        payload = None

        # If access token is available, try to decode it for blacklisting
        if access_token:
            try:
                payload = jwt_manager.verify_token(access_token, 'access')
            except Exception:
                # Token might be invalid/expired, but we can still proceed with logout
                payload = None

        # Connect to DynamoDB for session management
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        sessions_table = dynamodb.Table('agent-scl-sessions-dev')

        sessions_deleted = 0

        # If refresh token provided, delete the specific session
        if refresh_token:
            try:
                # Validate refresh token first
                refresh_payload = jwt_manager.verify_token(refresh_token, 'refresh')
                if refresh_payload.get('type') != 'refresh':
                    lambda_logger.warning(f"Logout failed - invalid refresh token type from IP: {client_ip}")
                    return auth_error_response('Invalid refresh token type', 'INVALID_TOKEN_TYPE', 400)

                # Find and delete session by refresh token
                session_response = sessions_table.scan(
                    FilterExpression='refresh_token = :token',
                    ExpressionAttributeValues={
                        ':token': refresh_token
                    }
                )

                for session in session_response.get('Items', []):
                    sessions_table.delete_item(
                        Key={'session_id': session['session_id']}
                    )
                    sessions_deleted += 1
                    lambda_logger.info(f"Deleted session: {session['session_id']}")

            except TokenExpiredException:
                lambda_logger.info(f"Logout with expired refresh token from IP: {client_ip}")
                # Continue with logout even if refresh token is expired
            except Exception as e:
                lambda_logger.warning(f"Error deleting session: {str(e)}")
                # Continue with logout even if session deletion fails

        # If we have user_id from access token, delete all sessions for this user
        elif user_id:
            try:
                # Find all sessions for this user
                session_response = sessions_table.scan(
                    FilterExpression='user_id = :user_id',
                    ExpressionAttributeValues={
                        ':user_id': user_id
                    }
                )

                for session in session_response.get('Items', []):
                    sessions_table.delete_item(
                        Key={'session_id': session['session_id']}
                    )
                    sessions_deleted += 1
                    lambda_logger.info(f"Deleted user session: {session['session_id']}")

            except Exception as e:
                lambda_logger.warning(f"Error deleting user sessions: {str(e)}")
                # Continue with logout even if session deletion fails

        # Add access token to blacklist for immediate invalidation
        try:
            if payload:  # Only blacklist if token was valid
                blacklist_table = dynamodb.Table('agent-scl-token-blacklist-dev')
                current_time = int(time.time())
                token_exp = payload.get('exp', current_time + 3600)  # Default 1 hour if no exp

                blacklist_table.put_item(
                    Item={
                        'token_hash': access_token[:64],  # Store first 64 chars as identifier
                        'blacklisted_at': current_time,
                        'expires_at': token_exp,
                        'ttl': token_exp + 86400  # Keep blacklist entry for 24h after token expiry
                    }
                )
                lambda_logger.info(f"Access token blacklisted for user: {user_id}")
        except Exception as e:
            lambda_logger.warning(f"Error blacklisting token: {str(e)}")
            # Continue with logout even if blacklisting fails

        # Record successful logout metrics and audit
        metrics_manager.security.record_security_event('logout_successful', 'info', tenant_id)
        metrics_manager.business.record_logout(tenant_id or 'unknown')

        audit_log(
            action='logout_successful',
            resource_type='session',
            resource_id=f"sessions_deleted_{sessions_deleted}",
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'sessions_deleted': sessions_deleted,
                'token_blacklisted': payload is not None
            }
        )

        lambda_logger.info(f"User logged out successfully: {user_id}, sessions deleted: {sessions_deleted}")

        return auth_success_response(
            {
                'sessions_deleted': sessions_deleted,
                'token_blacklisted': payload is not None
            },
            'Logged out successfully'
        )
    except TokenExpiredException as e:
        lambda_logger.info(f"Logout with expired token from IP: {client_ip}")
        metrics_manager.security.record_security_event('logout_expired_token', 'info')
        return auth_success_response({'sessions_deleted': 0}, 'Logged out (token was expired)')

    except InvalidTokenException as e:
        lambda_logger.warning(f"Logout failed - invalid token from IP: {client_ip}")
        metrics_manager.security.record_security_event('logout_failed_invalid_token', 'warning')
        return auth_error_response(str(e), 'INVALID_TOKEN', 401)

    except ValidationException as e:
        lambda_logger.warning(f"Logout failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('logout_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except Exception as e:
        lambda_logger.error(f"Logout failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('logout_failed_system_error', 'high')
        return auth_error_response('Logout failed', 'LOGOUT_ERROR', 500)
