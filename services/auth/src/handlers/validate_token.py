#!/usr/bin/env python3
# services/auth/src/handlers/validate_token.py
# Validate token handler

"""
Validate token handler with enterprise-grade security and monitoring.
Handles JWT token validation for external services with comprehensive checks.
"""

import json
import time
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    TokenExpiredException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.jwt_manager import get_jwt_manager


@rate_limit(requests_per_minute=200)  # Higher limit for token validation
@measure_performance("auth_validate_token")
@auth_handler("token_validation")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Validate JWT token with enterprise-grade security.

    POST /auth/validate-token
    {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        from shared.responses import handle_cors_preflight
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('token'):
            return auth_error_response('Token is required', 'VALIDATION_ERROR', 400)

        token = data['token'].strip()

    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record token validation attempt for security metrics
    metrics_manager.security.record_security_event('token_validation_attempt', 'info')

    lambda_logger.info(f"Token validation request from IP: {client_ip}")

    try:
        # Validate token using JWT manager
        jwt_manager = get_jwt_manager()
        try:
            payload = jwt_manager.verify_token(token, 'access')
            user_id = payload.get('user_id')
            tenant_id = payload.get('tenant_id')
            token_type = payload.get('type', 'access')
            exp = payload.get('exp')

            # Check if token has expired
            current_time = int(time.time())
            if exp and exp < current_time:
                lambda_logger.warning(f"Token validation failed - expired token from IP: {client_ip}")
                metrics_manager.security.record_security_event('token_validation_failed_expired', 'warning')
                return auth_success_response({
                    'valid': False,
                    'reason': 'Token has expired'
                }, 'Token validation completed')

            # Check if token is blacklisted
            try:
                dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
                blacklist_table_name = os.environ.get('TOKEN_BLACKLIST_TABLE', 'agent-scl-token-blacklist-dev')
                blacklist_table = dynamodb.Table(blacklist_table_name)

                # Check if token is in blacklist
                response = blacklist_table.get_item(
                    Key={'token_hash': token[-20:]}  # Use last 20 chars as identifier
                )

                if response.get('Item'):
                    lambda_logger.warning(f"Token validation failed - blacklisted token from IP: {client_ip}")
                    metrics_manager.security.record_security_event('token_validation_failed_blacklisted', 'medium')
                    return auth_success_response({
                        'valid': False,
                        'reason': 'Token has been revoked'
                    }, 'Token validation completed')

            except Exception as e:
                lambda_logger.warning(f"Failed to check token blacklist: {str(e)}")
                # Continue with validation even if blacklist check fails

            # Token is valid
            response_data = {
                'valid': True,
                'payload': {
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'role': payload.get('role'),
                    'email': payload.get('email'),
                    'type': token_type,
                    'exp': exp,
                    'iat': payload.get('iat')
                },
                'is_impersonation': payload.get('is_impersonation', False)
            }

            # Record successful token validation metrics and audit
            metrics_manager.security.record_security_event('token_validation_successful', 'info', tenant_id)
            metrics_manager.business.record_token_validation(tenant_id)

            audit_log(
                action='token_validation_successful',
                resource_type='token',
                resource_id=token[-20:],  # Last 20 chars for identification
                user_id=user_id,
                tenant_id=tenant_id,
                client_ip=client_ip,
                changes={
                    'token_validated': True,
                    'token_type': token_type
                }
            )

            return auth_success_response(response_data, 'Token validation completed')

        except Exception as e:
            lambda_logger.warning(f"Token validation failed - invalid token from IP: {client_ip}")
            metrics_manager.security.record_security_event('token_validation_failed_invalid', 'warning')

            response_data = {
                'valid': False,
                'reason': 'Invalid or malformed token'
            }

            return auth_success_response(response_data, 'Token validation completed')
    except ValidationException as e:
        lambda_logger.warning(f"Token validation failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('token_validation_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except AuthenticationException as e:
        lambda_logger.warning(f"Token validation failed - authentication error from IP: {client_ip}")
        metrics_manager.security.record_security_event('token_validation_failed_auth', 'medium')
        return auth_error_response(str(e), 'AUTHENTICATION_ERROR', 401)

    except TokenExpiredException as e:
        lambda_logger.warning(f"Token validation failed - expired token from IP: {client_ip}")
        metrics_manager.security.record_security_event('token_validation_failed_expired', 'warning')
        return auth_error_response(str(e), 'TOKEN_EXPIRED', 401)

    except Exception as e:
        lambda_logger.error(f"Token validation failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('token_validation_failed_system_error', 'high')
        return auth_error_response('Token validation failed', 'TOKEN_VALIDATION_ERROR', 500)
