# services/auth/src/handlers/me.py
# User profile endpoint - returns current user information

"""
User profile endpoint.
Returns current user information based on JWT token.
Separated from login for better performance and responsibility separation.
"""

import json
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response
from shared.logger import lambda_logger, audit_log
from shared.metrics import metrics_manager
from shared.user_model import User
from shared.exceptions import (
    AuthenticationException,
    ResourceNotFoundException
)


def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get current user profile information.
    
    GET /auth/me
    Headers: Authorization: Bearer <access_token>
    
    Returns:
    {
        "user_id": "...",
        "name": "...",
        "email": "...",
        "role": "...",
        "status": "...",
        "email_verified": true/false,
        "last_login": timestamp,
        "created_at": timestamp
    }
    """
    
    try:
        # Extract user information from JWT authorizer context
        request_context = event.get('requestContext', {})
        authorizer = request_context.get('authorizer', {})

        user_id = authorizer.get('user_id')
        tenant_id = authorizer.get('tenant_id')

        if not user_id or not tenant_id:
            lambda_logger.warning("Missing user context from JWT authorizer")
            return auth_error_response('Invalid authorization context', 'INVALID_CONTEXT', 401)
        
        # Get client IP for audit logging
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        
        # Log profile access attempt
        lambda_logger.info(f"Profile access for user: {user_id}, tenant: {tenant_id} from IP: {client_ip}")
        
        # Audit log
        audit_log(
            action='profile_access',
            resource_type='user',
            resource_id=user_id,
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip
        )
        
        # Get user information using User model
        user_obj = User.get_by_id(user_id, tenant_id)
        
        if not user_obj:
            lambda_logger.warning(f"User not found: {user_id}")
            metrics_manager.security.record_security_event('profile_access_user_not_found', 'medium')
            return auth_error_response('User not found', 'USER_NOT_FOUND', 404)

        # Verify user account is active (subscription validated)
        user_status = user_obj.status.value if hasattr(user_obj.status, 'value') else str(user_obj.status)
        if user_status != 'ACTIVE':
            lambda_logger.warning(f"Profile access denied - user not active: {user_id}, status: {user_status}")
            metrics_manager.security.record_security_event('profile_access_user_not_active', 'medium')
            return auth_error_response('Account not activated. Please complete subscription payment.', 'ACCOUNT_NOT_ACTIVE', 403)
        
        # Record successful profile access
        metrics_manager.business.record_api_usage(tenant_id, 'auth_me', 'GET')
        
        # Prepare response data using unified model (convert Decimal to int for JSON serialization)
        response_data = {
            'user_id': user_obj.user_id,
            'first_name': user_obj.first_name,
            'last_name': user_obj.last_name,
            'name': user_obj.full_name,  # Backward compatibility
            'email': user_obj.email,
            'role': user_obj.role.value if hasattr(user_obj.role, 'value') else str(user_obj.role),
            'status': user_obj.status.value if hasattr(user_obj.status, 'value') else str(user_obj.status),
            'email_verified': user_obj.email_verified,
            'last_login_at': int(user_obj.last_login_at) if user_obj.last_login_at else None,
            'created_at': int(user_obj.created_at) if user_obj.created_at else None,
            'updated_at': int(user_obj.updated_at) if user_obj.updated_at else None
        }
        
        lambda_logger.info(f"Profile access successful: {user_id}")
        
        return auth_success_response(response_data, 'User profile retrieved successfully')
        
    except ResourceNotFoundException as e:
        lambda_logger.warning(f"Profile access failed - user not found: {user_id}")
        metrics_manager.security.record_security_event('profile_access_not_found', 'medium')
        return auth_error_response('User not found', 'USER_NOT_FOUND', 404)
        
    except AuthenticationException as e:
        lambda_logger.warning(f"Profile access failed - authentication error: {str(e)}")
        metrics_manager.security.record_security_event('profile_access_auth_error', 'medium')
        return auth_error_response('Authentication failed', 'AUTHENTICATION_ERROR', 401)
        
    except Exception as e:
        lambda_logger.error(f"Profile access failed - unexpected error: {user_id}, error: {str(e)}")
        metrics_manager.security.record_security_event('profile_access_system_error', 'high')
        return auth_error_response('Profile access failed', 'SYSTEM_ERROR', 500)
