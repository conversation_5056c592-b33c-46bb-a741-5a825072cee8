#!/usr/bin/env python3
# services/auth/src/handlers/resend_verification.py
# Resend verification email handler

"""
Resend verification email handler with enterprise-grade security and monitoring.
Handles resending email verification for unverified users with comprehensive validation.
"""

import json
import time
import uuid
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response, handle_cors_preflight
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    ResourceNotFoundException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.user_model import User
from shared.validators import validate_email_address
from shared.exceptions import ValidationException


@rate_limit(requests_per_minute=5)  # Very restrictive for resend verification
@measure_performance("auth_resend_verification")
@auth_handler("email_verification_resend")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Resend email verification with enterprise-grade security.

    POST /auth/resend-verification
    {
        "email": "<EMAIL>"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('email'):
            return auth_error_response('Email is required', 'VALIDATION_ERROR', 400)

        # Use shared email validation
        try:
            email = validate_email_address(data['email'])
        except ValidationException as e:
            return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record resend verification attempt for security metrics
    metrics_manager.security.record_security_event('resend_verification_attempt', 'info')

    lambda_logger.info(f"Resend verification request from IP: {client_ip}, email: {email}")

    try:
        # Connect to DynamoDB to find user by email
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        table_name = os.environ.get('DYNAMODB_TABLE', 'agent-scl-main-dev')
        table = dynamodb.Table(table_name)

        # Find user by email using scan (in production, consider using GSI)
        response = table.scan(
            FilterExpression='email = :email',
            ExpressionAttributeValues={
                ':email': email
            }
        )

        if not response.get('Items'):
            # Log security event but don't reveal if user exists (timing attack prevention)
            lambda_logger.warning(f"Resend verification failed - user not found: {email}")
            metrics_manager.security.record_security_event('resend_verification_failed_user_not_found', 'warning')

            # Always return success to prevent email enumeration
            return auth_success_response(
                {'message': 'If the email exists and is not verified, a verification email has been sent'},
                'Verification email request processed'
            )
        user_data = response['Items'][0]
        user_id = user_data.get('user_id')
        tenant_id = user_data.get('tenant_id')
        status = user_data.get('status')
        email_verified = user_data.get('email_verified', False)

        # Check if user is already verified
        if email_verified:
            lambda_logger.info(f"Resend verification for already verified user: {email}")
            metrics_manager.security.record_security_event('resend_verification_already_verified', 'info')

            # Always return success to prevent information disclosure
            return auth_success_response(
                {'message': 'If the email exists and is not verified, a verification email has been sent'},
                'Verification email request processed'
            )

        # Check if user account is active or pending verification
        allowed_statuses = ['ACTIVE', 'PENDING_VERIFICATION']
        if status not in allowed_statuses:
            lambda_logger.warning(f"Resend verification for inactive user: {email}")
            metrics_manager.security.record_security_event('resend_verification_failed_user_inactive', 'warning')

            # Always return success to prevent information disclosure
            return auth_success_response(
                {'message': 'If the email exists and is not verified, a verification email has been sent'},
                'Verification email request processed'
            )

        # Generate new verification token
        verification_token = str(uuid.uuid4())
        current_time = int(time.time())
        expires_at = current_time + 86400  # 24 hours expiry

        # Update user record with new verification token
        table.update_item(
            Key={
                'PK': user_data['PK'],
                'SK': user_data['SK']
            },
            UpdateExpression='SET verification_token = :token, verification_token_expires = :expires, updated_at = :updated_at',
            ExpressionAttributeValues={
                ':token': verification_token,
                ':expires': expires_at,
                ':updated_at': current_time
            }
        )

        # Send verification email
        try:
            from shared.email import email_service
            user_name = user_data.get('name', '')
            email_sent = email_service.send_verification_email(
                to_email=email,
                verification_token=verification_token,
                user_name=user_name
            )
            if email_sent:
                lambda_logger.info(f"Verification email sent successfully to {email}")
            else:
                lambda_logger.warning(f"Failed to send verification email to {email}")
        except Exception as e:
            lambda_logger.error(f"Error sending verification email to {email}: {str(e)}")

        # Record successful resend verification metrics and audit
        metrics_manager.security.record_security_event('resend_verification_successful', 'info', tenant_id)
        metrics_manager.business.record_verification_resend(tenant_id)

        audit_log(
            action='resend_verification_successful',
            resource_type='user',
            resource_id=user_id,
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'verification_token_generated': True,
                'expires_at': expires_at
            }
        )
        # Prepare response data
        response_data = {
            'message': 'If the email exists and is not verified, a verification email has been sent',
            'expires_in': 86400,  # 24 hours
            'resent_at': current_time
        }

        return auth_success_response(response_data, 'Verification email request processed')

    except ValidationException as e:
        lambda_logger.warning(f"Resend verification failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('resend_verification_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except AuthenticationException as e:
        lambda_logger.warning(f"Resend verification failed - authentication error from IP: {client_ip}")
        metrics_manager.security.record_security_event('resend_verification_failed_auth', 'medium')
        return auth_error_response(str(e), 'AUTHENTICATION_ERROR', 401)

    except ResourceNotFoundException as e:
        lambda_logger.warning(f"Resend verification failed - resource not found from IP: {client_ip}")
        metrics_manager.security.record_security_event('resend_verification_failed_not_found', 'warning')
        return auth_error_response(str(e), 'RESOURCE_NOT_FOUND', 404)

    except Exception as e:
        lambda_logger.error(f"Resend verification failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('resend_verification_failed_system_error', 'high')
        return auth_error_response('Verification resend failed', 'VERIFICATION_RESEND_ERROR', 500)
