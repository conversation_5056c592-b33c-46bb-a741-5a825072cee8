#!/usr/bin/env python3
# Simple test handler to debug layer loading

import json
import sys
import os

def handler(event, context):
    """Simple test handler"""
    
    result = {
        "message": "Test handler working",
        "python_path": sys.path,
        "opt_exists": os.path.exists('/opt'),
        "opt_python_exists": os.path.exists('/opt/python'),
        "opt_python_shared_exists": os.path.exists('/opt/python/shared'),
        "environment_pythonpath": os.environ.get('PYTHONPATH', 'NOT_SET')
    }
    
    if os.path.exists('/opt'):
        result["opt_contents"] = os.listdir('/opt')
    
    if os.path.exists('/opt/python'):
        result["opt_python_contents"] = os.listdir('/opt/python')
    
    if os.path.exists('/opt/python/shared'):
        result["opt_python_shared_contents"] = os.listdir('/opt/python/shared')
    
    # Try to import shared
    try:
        import shared
        result["shared_import"] = "SUCCESS"
        result["shared_file"] = shared.__file__ if hasattr(shared, '__file__') else 'NO_FILE'
    except Exception as e:
        result["shared_import"] = f"ERROR: {str(e)}"
    
    return {
        'statusCode': 200,
        'body': json.dumps(result, indent=2)
    }
