#!/usr/bin/env python3
# services/auth/src/handlers/reset_password.py
# Reset password handler

"""
Reset password handler with enterprise-grade security and monitoring.
Handles password reset completion with token validation and security measures.
"""

import json
import time
import os
import boto3
import hashlib
import re
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    ResourceNotFoundException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.auth import password_manager


@rate_limit(requests_per_minute=5)  # Restrictive for security
@measure_performance("auth_reset_password")
@auth_handler("password_reset")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Reset user password using reset token with enterprise-grade security.

    POST /auth/reset-password
    {
        "token": "reset_token_here",
        "new_password": "NewSecurePassword123!"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        from shared.responses import handle_cors_preflight
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('token'):
            return auth_error_response('Reset token is required', 'VALIDATION_ERROR', 400)

        if not data.get('new_password'):
            return auth_error_response('New password is required', 'VALIDATION_ERROR', 400)

        reset_token = data['token'].strip()
        new_password = data['new_password']

    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record password reset attempt for security metrics
    metrics_manager.security.record_security_event('password_reset_attempt', 'info')

    lambda_logger.info(f"Password reset attempt from IP: {client_ip}, token: {reset_token[:10]}...")

    try:
        # Validate new password strength
        is_strong, password_errors = password_manager.validate_password_strength(new_password)
        if not is_strong:
            lambda_logger.warning(f"Password reset failed - weak password from IP: {client_ip}")
            metrics_manager.security.record_security_event('password_reset_failed_weak_password', 'warning')
            return auth_error_response(
                f"Password does not meet security requirements: {', '.join(password_errors)}",
                'WEAK_PASSWORD',
                400
            )

        # Connect to DynamoDB to find user by reset token
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        table_name = os.environ.get('DYNAMODB_TABLE', 'agent-scl-main-dev')
        table = dynamodb.Table(table_name)

        # Find user by reset token using scan (in production, consider using GSI)
        response = table.scan(
            FilterExpression='password_reset_token = :token',
            ExpressionAttributeValues={
                ':token': reset_token
            },
            ConsistentRead=True
        )

        if not response.get('Items'):
            lambda_logger.warning(f"Password reset failed - invalid token from IP: {client_ip}")
            lambda_logger.info(f"DEBUG - No items found for reset_token: {reset_token}")
            metrics_manager.security.record_security_event('password_reset_failed_invalid_token', 'medium')
            return auth_error_response('Invalid or expired reset token', 'INVALID_TOKEN', 400)

        user_data = response['Items'][0]
        user_id = user_data.get('user_id')
        tenant_id = user_data.get('tenant_id')
        status = user_data.get('status')
        reset_token_expires = user_data.get('password_reset_expires', 0)
        current_time = int(time.time())

        # Check if reset token has expired
        if reset_token_expires < current_time:
            lambda_logger.warning(f"Password reset failed - expired token from IP: {client_ip}")
            metrics_manager.security.record_security_event('password_reset_failed_expired_token', 'medium')
            return auth_error_response('Invalid or expired reset token', 'EXPIRED_TOKEN', 400)

        # Check if user account is active
        if status != 'ACTIVE':
            lambda_logger.warning(f"Password reset failed - inactive user from IP: {client_ip}")
            metrics_manager.security.record_security_event('password_reset_failed_user_inactive', 'medium')
            return auth_error_response('Invalid or expired reset token', 'INVALID_TOKEN', 400)
        # Hash the new password
        password_hash = password_manager.hash_password(new_password, user_id)

        # Update user record with new password and clear reset token
        try:
            table.update_item(
                Key={
                    'PK': user_data['PK'],
                    'SK': user_data['SK']
                },
                UpdateExpression='SET password_hash = :password, updated_at = :updated_at REMOVE password_reset_token, password_reset_expires',
                ExpressionAttributeValues={
                    ':password': password_hash,
                    ':updated_at': current_time
                }
            )

            lambda_logger.info(f"Password reset successful for user: {user_id}")

        except Exception as e:
            lambda_logger.error(f"Failed to update password for user {user_id}: {str(e)}")
            metrics_manager.security.record_security_event('password_reset_failed_database_error', 'high')
            return auth_error_response('Failed to reset password', 'PASSWORD_RESET_FAILED', 500)

        # Record successful password reset metrics and audit
        metrics_manager.security.record_security_event('password_reset_successful', 'info', tenant_id)
        metrics_manager.business.record_password_reset(tenant_id)

        audit_log(
            action='password_reset_successful',
            resource_type='user',
            resource_id=user_id,
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'password_updated': True,
                'reset_token_cleared': True,
                'reset_at': current_time
            }
        )

        # Prepare response data
        response_data = {
            'message': 'Password has been reset successfully. You can now login with your new password.',
            'user_id': user_id,
            'reset_at': current_time
        }
        return auth_success_response(response_data, 'Password reset successfully')

    except ValidationException as e:
        lambda_logger.warning(f"Password reset failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('password_reset_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except AuthenticationException as e:
        lambda_logger.warning(f"Password reset failed - authentication error from IP: {client_ip}")
        metrics_manager.security.record_security_event('password_reset_failed_auth', 'medium')
        return auth_error_response(str(e), 'AUTHENTICATION_ERROR', 401)

    except ResourceNotFoundException as e:
        lambda_logger.warning(f"Password reset failed - resource not found from IP: {client_ip}")
        metrics_manager.security.record_security_event('password_reset_failed_not_found', 'warning')
        return auth_error_response(str(e), 'RESOURCE_NOT_FOUND', 404)

    except Exception as e:
        lambda_logger.error(f"Password reset failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('password_reset_failed_system_error', 'high')
        return auth_error_response('Password reset failed', 'PASSWORD_RESET_ERROR', 500)
