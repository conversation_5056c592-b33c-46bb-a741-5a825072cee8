#!/usr/bin/env python3
# services/auth/src/handlers/forgot_password.py
# Forgot password handler

"""
Forgot password handler with enterprise-grade security and monitoring.
Handles password reset request initiation with comprehensive validation and audit trail.
"""

import json
import time
import uuid
import os
import boto3
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import auth_success_response, auth_error_response
from shared.logger import lambda_logger, audit_log
from shared.business_logic_decorator import auth_handler
from shared.exceptions import (
    ValidationException,
    AuthenticationException,
    ResourceNotFoundException
)
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import rate_limit
from shared.user_model import User
from shared.validators import validate_email_address
from shared.exceptions import ValidationException


@rate_limit(requests_per_minute=3)  # Very restrictive for security
@measure_performance("auth_forgot_password")
@auth_handler("password_reset_request")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Initiate password reset process with enterprise-grade security.

    POST /auth/forgot-password
    {
        "email": "<EMAIL>"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        from shared.responses import handle_cors_preflight
        return handle_cors_preflight()

    # Get client information for security logging
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Validate request body
    body = event.get('body', '{}')
    try:
        data = json.loads(body) if isinstance(body, str) else body

        if not data.get('email'):
            return auth_error_response('Email is required', 'VALIDATION_ERROR', 400)

        # Use shared email validation
        try:
            email = validate_email_address(data['email'])
        except ValidationException as e:
            return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except json.JSONDecodeError:
        return auth_error_response('Invalid JSON format', 'INVALID_JSON', 400)

    # Record password reset attempt for security metrics
    metrics_manager.security.record_security_event('password_reset_attempt', 'info')

    lambda_logger.info(f"Password reset request from IP: {client_ip}, email: {email}")

    try:
        # Connect to DynamoDB to find user by email
        dynamodb = boto3.resource('dynamodb', region_name=os.environ.get('AWS_REGION', 'us-east-1'))
        table_name = os.environ.get('DYNAMODB_TABLE', 'agent-scl-main-dev')
        table = dynamodb.Table(table_name)

        # Find user by email using scan (in production, consider using GSI)
        response = table.scan(
            FilterExpression='email = :email',
            ExpressionAttributeValues={
                ':email': email
            },
            ConsistentRead=True
        )

        user_obj = response.get('Items', [{}])[0] if response.get('Items') else None

        if not user_obj:
            # Log security event but don't reveal if user exists (timing attack prevention)
            lambda_logger.warning(f"Password reset failed - user not found: {email}")
            metrics_manager.security.record_security_event('password_reset_failed_user_not_found', 'warning')

            # Always return success to prevent email enumeration
            return auth_success_response(
                {'message': 'If the email exists, a password reset link has been sent'},
                'Password reset request processed'
            )
        # Extract user data
        user_id = user_obj.get('user_id')
        tenant_id = user_obj.get('tenant_id')
        status = user_obj.get('status')
        email_verified = user_obj.get('email_verified', False)

        # Debug logging to see what data we're reading
        lambda_logger.info(f"DEBUG - User data: user_id={user_id}, status={status}, email_verified={email_verified}, email={email}")

        # Verify user is active and email is verified
        if status != 'ACTIVE':
            lambda_logger.warning(f"Password reset failed - user inactive: {email}")
            metrics_manager.security.record_security_event('password_reset_failed_user_inactive', 'warning')

            # Always return success to prevent information disclosure
            return auth_success_response(
                {'message': 'If the email exists, a password reset link has been sent'},
                'Password reset request processed'
            )

        if not email_verified:
            lambda_logger.warning(f"Password reset failed - email not verified: {email}")
            metrics_manager.security.record_security_event('password_reset_failed_email_not_verified', 'warning')

            # Always return success to prevent information disclosure
            return auth_success_response(
                {'message': 'If the email exists, a password reset link has been sent'},
                'Password reset request processed'
            )

        # Generate password reset token
        reset_token = str(uuid.uuid4())
        current_time = int(time.time())
        expires_at = current_time + 3600  # 1 hour expiry

        # Update user record with reset token (reuse the table connection)
        table.update_item(
            Key={
                'PK': user_obj['PK'],
                'SK': user_obj['SK']
            },
            UpdateExpression='SET password_reset_token = :token, password_reset_expires = :expires, updated_at = :updated_at',
            ExpressionAttributeValues={
                ':token': reset_token,
                ':expires': expires_at,
                ':updated_at': current_time
            }
        )

        # Send password reset email
        try:
            from shared.email import email_service
            user_name = user_obj.get('name', '')
            email_sent = email_service.send_password_reset_email(
                to_email=email,
                reset_token=reset_token,
                user_name=user_name
            )
            if email_sent:
                lambda_logger.info(f"Password reset email sent successfully to {email}")
            else:
                lambda_logger.warning(f"Failed to send password reset email to {email}")
        except Exception as e:
            lambda_logger.error(f"Error sending password reset email to {email}: {str(e)}")

        # Record successful password reset request metrics and audit
        metrics_manager.security.record_security_event('password_reset_successful', 'info', tenant_id)
        metrics_manager.business.record_password_reset_request(tenant_id)

        audit_log(
            action='password_reset_request_successful',
            resource_type='user',
            resource_id=user_id,
            user_id=user_id,
            tenant_id=tenant_id,
            client_ip=client_ip,
            changes={
                'reset_token_generated': True,
                'expires_at': expires_at
            }
        )
        lambda_logger.info(f"Password reset request successful for user: {user_id}")

        # Return success response (always the same message for security)
        return auth_success_response(
            {
                'message': 'If the email exists, a password reset link has been sent',
                'expires_in': 3600  # 1 hour
            },
            'Password reset request processed'
        )

    except ValidationException as e:
        lambda_logger.warning(f"Password reset failed - validation error from IP: {client_ip}")
        metrics_manager.security.record_security_event('password_reset_failed_validation', 'warning')
        return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

    except AuthenticationException as e:
        lambda_logger.warning(f"Password reset failed - authentication error from IP: {client_ip}")
        metrics_manager.security.record_security_event('password_reset_failed_auth', 'medium')
        return auth_error_response(str(e), 'AUTHENTICATION_ERROR', 401)

    except Exception as e:
        lambda_logger.error(f"Password reset failed - unexpected error from IP: {client_ip}, error: {str(e)}")
        metrics_manager.security.record_security_event('password_reset_failed_system_error', 'high')
        return auth_error_response('Password reset request failed', 'PASSWORD_RESET_ERROR', 500)


