sequenceDiagram
    participant User as 👤 User
    participant App as 📱 Client App
    participant API as 🌐 Orchestrator API
    participant CRH as 🚀 Complete Registration Handler
    participant OS as 🔄 Orchestration Service
    participant SM as 📊 State Manager
    participant DB as 🗄️ DynamoDB
    participant TC as 🏢 Tenant Client
    participant AC as 🔐 Auth Client
    participant ES as 📧 Email Service
    participant SES as 📧 AWS SES
    participant VEH as ✅ Verify Email Handler
    participant PPH as 💳 Process Payment Handler
    participant PC as 💳 Payment Client

    Note over User: User fills registration form
    User->>App: Submit registration data
    App->>API: POST /orchestrator/registration/complete
    Note over App,API: {company: {...}, user: {...}, plan: "starter"}
    
    API->>CRH: Route to Complete Registration Handler
    CRH->>CRH: Validate registration data
    CRH->>CRH: Check email uniqueness
    
    CRH->>OS: start_registration(request)
    OS->>SM: create_registration_record()
    SM->>SM: Generate registration_id
    SM->>DB: Store initial registration
    Note over DB: PK: REGISTRATION#{registration_id}<br/>state: "initiated"<br/>TTL: 24 hours
    DB-->>SM: Registration created
    SM-->>OS: Registration record created
    
    Note over OS: Step 1: Create Tenant
    OS->>TC: create_tenant(company_data)
    TC->>TC: Validate company data
    TC->>DB: Create tenant record
    TC-->>OS: {tenant_id: "tenant-123", status: "created"}
    
    OS->>SM: update_state("initiated" → "tenant_created")
    SM->>DB: Update registration state
    
    Note over OS: Step 2: Create Master User
    OS->>AC: create_master_user(user_data, tenant_id)
    AC->>AC: Hash password & generate verification token
    AC->>DB: Create user record
    AC-->>OS: {user_id: "user-456", verification_token: "token-abc"}
    
    OS->>SM: update_state("tenant_created" → "user_created")
    SM->>DB: Update registration state
    
    Note over OS: Step 3: Send Verification Email
    OS->>ES: send_verification_email(email, token, registration_id)
    ES->>ES: Generate email template
    ES->>SES: Send verification email
    SES-->>ES: Email sent successfully
    ES-->>OS: Email delivery confirmed
    
    OS->>SM: update_state("user_created" → "email_sent")
    SM->>DB: Update registration state
    
    OS-->>CRH: Registration flow initiated
    CRH-->>API: 201 Created
    Note over CRH,API: {registration_id, state: "email_sent", next_steps: [...]}
    API-->>App: Registration response
    App-->>User: "Check your email for verification"
    
    rect rgb(245, 255, 245)
        Note over User: User clicks email verification link
        User->>API: GET /verify?registration_id=123&token=abc
        API->>VEH: Route to Verify Email Handler
        
        VEH->>OS: verify_email(registration_id, token)
        OS->>SM: get_registration(registration_id)
        SM->>DB: Query registration record
        DB-->>SM: Registration data
        
        OS->>OS: Validate token and current state
        OS->>AC: verify_email_token(user_id, token)
        AC->>AC: Validate token and mark email as verified
        AC-->>OS: Email verified successfully
        
        OS->>SM: update_state("email_sent" → "email_verified")
        SM->>DB: Update registration state
        
        OS-->>VEH: Email verification successful
        VEH-->>API: 200 OK {state: "email_verified", redirect_url: "/select-plan"}
        API-->>User: Redirect to plan selection
    end
    
    rect rgb(255, 245, 245)
        Note over User: User selects plan and enters payment
        User->>API: POST /orchestrator/registration/payment
        Note over User,API: {registration_id, plan: "starter", payment_method: {...}}
        
        API->>PPH: Route to Process Payment Handler
        PPH->>OS: process_payment(registration_id, payment_data)
        
        OS->>SM: update_state("email_verified" → "payment_processing")
        SM->>DB: Update registration state
        
        OS->>PC: process_payment(amount, payment_method)
        PC->>PC: Call Stripe/PayPal API
        PC-->>OS: Payment successful {transaction_id: "txn-789"}
        
        OS->>SM: update_state("payment_processing" → "payment_completed")
        SM->>DB: Update registration state
        
        Note over OS: Configure tenant resources
        OS->>TC: configure_tenant_resources(tenant_id, plan)
        TC-->>OS: Resources configured
        
        OS->>AC: activate_user_account(user_id)
        AC-->>OS: Account activated
        
        OS->>SM: update_state("payment_completed" → "completed")
        SM->>DB: Update registration state
        
        OS-->>PPH: Registration completed successfully
        PPH-->>API: 200 OK {state: "completed", login_url: "/login"}
        API-->>User: Registration complete - Welcome!
    end
    
    Note over User,DB: Registration flow completed successfully