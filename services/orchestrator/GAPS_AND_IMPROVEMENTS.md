# 🎭 Orchestrator Service - Gaps and Improvements

## 📊 **Current Status: 8.9/10 - EXCELENTE**

### **Completitud:** 95% funcional, gaps menores identificados

---

## 🎯 **Gaps Identificados**

### **1. MINOR GAPS (5%)**

#### **1.1 Missing Advanced Orchestration Endpoints**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Feature enhancement

**Current State:**
- Basic registration orchestration exists
- Missing workflow monitoring
- Missing manual intervention capabilities
- Missing workflow templates

**Required Endpoints:**

```python
# src/handlers/workflow_status.py
"""Workflow status monitoring handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@orchestrator_resilience("workflow_status")
@measure_performance("orchestrator_workflow_status")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get workflow status and history.
    
    GET /orchestrator/workflows/{workflow_id}
    GET /orchestrator/workflows/{workflow_id}/history
    GET /orchestrator/workflows/active
    """
    pass

# src/handlers/manual_intervention.py
"""Manual intervention handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@orchestrator_resilience("manual_intervention")
@measure_performance("orchestrator_manual_intervention")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manual workflow intervention.
    
    POST /orchestrator/workflows/{workflow_id}/retry
    POST /orchestrator/workflows/{workflow_id}/skip-step
    POST /orchestrator/workflows/{workflow_id}/cancel
    POST /orchestrator/workflows/{workflow_id}/resume
    """
    pass

# src/handlers/workflow_templates.py
"""Workflow templates handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@orchestrator_resilience("workflow_templates")
@measure_performance("orchestrator_workflow_templates")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage workflow templates.
    
    GET /orchestrator/templates
    POST /orchestrator/templates
    PUT /orchestrator/templates/{template_id}
    DELETE /orchestrator/templates/{template_id}
    """
    pass

# src/handlers/bulk_operations.py
"""Bulk orchestration operations handler."""

@require_auth
@rate_limit(requests_per_minute=10)
@orchestrator_resilience("bulk_operations")
@measure_performance("orchestrator_bulk_operations")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Bulk orchestration operations.
    
    POST /orchestrator/bulk/registrations
    POST /orchestrator/bulk/cleanup
    GET /orchestrator/bulk/status/{batch_id}
    """
    pass
```

#### **1.2 Complete Cleanup Implementation**
**Priority:** High  
**Effort:** 2-3 days  
**Impact:** Production reliability

**Current State:**
- Cleanup logic marked as TODO
- Missing rollback mechanisms
- Limited error recovery

**Required Implementation:**

```python
# src/services/cleanup_service.py
"""Complete cleanup service implementation."""

import asyncio
from typing import Dict, Any, List, Optional
from shared.logger import lambda_logger
from shared.exceptions import CleanupException
from shared.database import db_client

class CleanupService:
    """Complete cleanup service for failed workflows."""
    
    def __init__(self):
        self.db = db_client
        self.cleanup_strategies = {
            'TENANT_CREATION_FAILED': self._cleanup_tenant_creation,
            'USER_CREATION_FAILED': self._cleanup_user_creation,
            'PAYMENT_FAILED': self._cleanup_payment_failure,
            'SETUP_FAILED': self._cleanup_setup_failure
        }
    
    async def cleanup_failed_workflow(
        self, 
        workflow_id: str, 
        failure_point: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Clean up resources from failed workflow."""
        try:
            lambda_logger.info(f"Starting cleanup for workflow {workflow_id}")
            
            cleanup_strategy = self.cleanup_strategies.get(failure_point)
            if not cleanup_strategy:
                raise CleanupException(f"No cleanup strategy for {failure_point}")
            
            # Execute cleanup
            cleanup_result = await cleanup_strategy(workflow_id, context)
            
            # Update workflow status
            await self._update_workflow_cleanup_status(
                workflow_id, 
                'CLEANUP_COMPLETED',
                cleanup_result
            )
            
            lambda_logger.info(f"Cleanup completed for workflow {workflow_id}")
            
            return {
                'workflow_id': workflow_id,
                'cleanup_status': 'COMPLETED',
                'cleaned_resources': cleanup_result.get('cleaned_resources', []),
                'cleanup_duration': cleanup_result.get('duration', 0)
            }
            
        except Exception as e:
            lambda_logger.error(f"Cleanup failed for workflow {workflow_id}: {str(e)}")
            
            await self._update_workflow_cleanup_status(
                workflow_id, 
                'CLEANUP_FAILED',
                {'error': str(e)}
            )
            
            raise CleanupException(f"Cleanup failed: {str(e)}")
    
    async def _cleanup_tenant_creation(
        self, 
        workflow_id: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Clean up failed tenant creation."""
        cleaned_resources = []
        
        try:
            tenant_id = context.get('tenant_id')
            if tenant_id:
                # Remove tenant record
                await self.db.delete_item(
                    pk=f'TENANT#{tenant_id}',
                    sk='METADATA',
                    tenant_id=tenant_id
                )
                cleaned_resources.append(f'tenant_record:{tenant_id}')
                
                # Remove any partial user records
                user_records = await self.db.query(
                    pk=f'TENANT#{tenant_id}',
                    sk_prefix='USER#',
                    tenant_id=tenant_id
                )
                
                for user_record in user_records:
                    await self.db.delete_item(
                        pk=user_record['PK'],
                        sk=user_record['SK'],
                        tenant_id=tenant_id
                    )
                    cleaned_resources.append(f'user_record:{user_record["SK"]}')
            
            return {
                'cleaned_resources': cleaned_resources,
                'duration': 0  # Calculate actual duration
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to cleanup tenant creation: {str(e)}")
            raise
    
    async def _cleanup_payment_failure(
        self, 
        workflow_id: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Clean up failed payment setup."""
        cleaned_resources = []
        
        try:
            # Cancel any pending Stripe subscriptions
            stripe_customer_id = context.get('stripe_customer_id')
            if stripe_customer_id:
                # Cancel subscription
                # stripe.Subscription.delete(subscription_id)
                cleaned_resources.append(f'stripe_subscription:{stripe_customer_id}')
            
            # Remove payment records
            tenant_id = context.get('tenant_id')
            if tenant_id:
                payment_records = await self.db.query(
                    pk=f'TENANT#{tenant_id}',
                    sk_prefix='PAYMENT#',
                    tenant_id=tenant_id
                )
                
                for payment_record in payment_records:
                    await self.db.delete_item(
                        pk=payment_record['PK'],
                        sk=payment_record['SK'],
                        tenant_id=tenant_id
                    )
                    cleaned_resources.append(f'payment_record:{payment_record["SK"]}')
            
            return {
                'cleaned_resources': cleaned_resources,
                'duration': 0
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to cleanup payment failure: {str(e)}")
            raise
    
    async def _update_workflow_cleanup_status(
        self,
        workflow_id: str,
        status: str,
        details: Dict[str, Any]
    ) -> None:
        """Update workflow cleanup status."""
        try:
            # Update workflow record with cleanup status
            await self.db.update_item(
                pk=f'WORKFLOW#{workflow_id}',
                sk='METADATA',
                update_expression='SET cleanup_status = :status, cleanup_details = :details, updated_at = :timestamp',
                expression_attribute_values={
                    ':status': status,
                    ':details': details,
                    ':timestamp': int(time.time())
                }
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to update cleanup status: {str(e)}")
            # Don't raise here to avoid masking original cleanup error
```

#### **1.3 Enhanced Monitoring and Alerting**
**Priority:** Medium  
**Effort:** 2 days  
**Impact:** Operational excellence

**Current State:**
- Basic logging exists
- Missing real-time monitoring
- No alerting for failed workflows

**Required Implementation:**

```python
# src/services/monitoring_service.py
"""Enhanced monitoring service."""

import boto3
from typing import Dict, Any, List
from shared.logger import lambda_logger
from shared.metrics import metrics_manager

class MonitoringService:
    """Enhanced monitoring for orchestrator workflows."""
    
    def __init__(self):
        self.cloudwatch = boto3.client('cloudwatch')
        self.sns = boto3.client('sns')
        self.alert_topic_arn = os.environ.get('ALERT_TOPIC_ARN')
    
    async def track_workflow_metrics(
        self,
        workflow_id: str,
        workflow_type: str,
        step: str,
        status: str,
        duration: float,
        error: Optional[str] = None
    ) -> None:
        """Track detailed workflow metrics."""
        try:
            # Send custom metrics to CloudWatch
            metrics = [
                {
                    'MetricName': 'WorkflowStepDuration',
                    'Value': duration,
                    'Unit': 'Seconds',
                    'Dimensions': [
                        {'Name': 'WorkflowType', 'Value': workflow_type},
                        {'Name': 'Step', 'Value': step},
                        {'Name': 'Status', 'Value': status}
                    ]
                },
                {
                    'MetricName': 'WorkflowStepCount',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'WorkflowType', 'Value': workflow_type},
                        {'Name': 'Step', 'Value': step},
                        {'Name': 'Status', 'Value': status}
                    ]
                }
            ]
            
            self.cloudwatch.put_metric_data(
                Namespace='OrchestatorService',
                MetricData=metrics
            )
            
            # Send alert for failures
            if status == 'FAILED' and self.alert_topic_arn:
                await self._send_failure_alert(
                    workflow_id, workflow_type, step, error
                )
                
        except Exception as e:
            lambda_logger.error(f"Failed to track workflow metrics: {str(e)}")
    
    async def _send_failure_alert(
        self,
        workflow_id: str,
        workflow_type: str,
        step: str,
        error: Optional[str]
    ) -> None:
        """Send failure alert via SNS."""
        try:
            message = {
                'alert_type': 'WORKFLOW_FAILURE',
                'workflow_id': workflow_id,
                'workflow_type': workflow_type,
                'failed_step': step,
                'error': error,
                'timestamp': int(time.time())
            }
            
            self.sns.publish(
                TopicArn=self.alert_topic_arn,
                Message=json.dumps(message),
                Subject=f'Workflow Failure: {workflow_type} - {step}'
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to send failure alert: {str(e)}")
```
