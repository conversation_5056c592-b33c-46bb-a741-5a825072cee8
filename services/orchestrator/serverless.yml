service: agent-scl-orchestrator

custom:
  serviceName: orchestrator
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}

  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName

    # Service endpoints
    AUTH_SERVICE_URL:
      Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-ServiceEndpoint
    TENANT_SERVICE_URL:
      Fn::ImportValue: sls-${self:custom.projectName}-tenant-${self:custom.stage}-ServiceEndpoint
    PAYMENT_SERVICE_URL:
      Fn::ImportValue: sls-${self:custom.projectName}-payment-${self:custom.stage}-ServiceEndpoint

    # Orchestrator-specific tables
    REGISTRATIONS_TABLE: ${self:custom.projectName}-registrations-${self:custom.stage}

    # Email configuration
    EMAIL_FROM: <EMAIL>

  # IAM role statements
  iamRoleStatements:
    # Main DynamoDB table permissions
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"

    # Orchestrator-specific table permissions
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-registrations-${self:custom.stage}
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-registrations-${self:custom.stage}/index/*
        
    # SES permissions for email
    - Effect: Allow
      Action:
        - ses:SendEmail
        - ses:SendRawEmail
      Resource: "*"
    
    # CloudWatch Logs
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    
    # CloudWatch Metrics
    - Effect: Allow
      Action:
        - cloudwatch:PutMetricData
      Resource: "*"

# Lambda functions
functions:
  # Registration orchestration endpoints
  registrationComplete:
    handler: src/handlers/registration_complete.handler
    timeout: 30
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /registration/complete
          method: post
          cors: true
  
  registrationVerify:
    handler: src/handlers/registration_verify.handler
    timeout: 30
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /registration/verify
          method: post
          cors: true
  
  registrationPayment:
    handler: src/handlers/registration_payment.handler
    timeout: 30
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /registration/payment
          method: post
          cors: true
  
  registrationStatus:
    handler: src/handlers/registration_status.handler
    timeout: 15
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /registration/status/{registration_id}
          method: get
          cors: true
  
  registrationCancel:
    handler: src/handlers/registration_cancel.handler
    timeout: 30
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /registration/{registration_id}
          method: delete
          cors: true
  
  # Health check
  health:
    handler: src/handlers/health.handler
    timeout: 10
    memorySize: 128
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /health
          method: get
          cors: true

# Resources
resources:
  Resources:
    # DynamoDB table for registration state management
    RegistrationsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.projectName}-registrations-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: registration_id
            AttributeType: S
          - AttributeName: email
            AttributeType: S
          - AttributeName: created_at
            AttributeType: S
        KeySchema:
          - AttributeName: registration_id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: EmailIndex
            KeySchema:
              - AttributeName: email
                KeyType: HASH
            Projection:
              ProjectionType: ALL
          - IndexName: CreatedAtIndex
            KeySchema:
              - AttributeName: created_at
                KeyType: HASH
            Projection:
              ProjectionType: ALL
        StreamSpecification:
          StreamViewType: NEW_AND_OLD_IMAGES
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
        SSESpecification:
          SSEEnabled: true
        Tags:
          - Key: Service
            Value: orchestrator
          - Key: Environment
            Value: ${self:custom.stage}
          - Key: Project
            Value: ${self:custom.projectName}

  # Outputs
  Outputs:
    ServiceEndpoint:
      Description: "Orchestrator Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
            - "/orchestrator"
      Export:
        Name: sls-${self:custom.projectName}-orchestrator-${self:custom.stage}-ServiceEndpoint
    
    RegistrationsTableName:
      Description: "DynamoDB table name for registrations"
      Value: ${self:custom.projectName}-registrations-${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-orchestrator-${self:custom.stage}-RegistrationsTableName

plugins:
  - serverless-python-requirements

package:
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.pytest_cache/**'
    - '!tests/**'
    - '!*.md'
    - '!.env*'
