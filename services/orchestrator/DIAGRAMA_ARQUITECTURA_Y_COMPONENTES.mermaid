graph TB
    %% External Systems
    Client[👤 Client Applications<br/>Web/Mobile]
    EmailUser[📧 User Email Click<br/>Verification]
    PaymentProvider[💳 Payment Provider<br/>Stripe/PayPal]
    
    %% API Gateway
    APIGateway[🌐 API Gateway<br/>REST API]
    
    %% Lambda Functions - Registration Flow
    CompleteRegHandler[🚀 Complete Registration Handler<br/>POST /registration/complete]
    VerifyEmailHandler[✅ Verify Email Handler<br/>POST /registration/verify]
    ProcessPaymentHandler[💳 Process Payment Handler<br/>POST /registration/payment]
    GetStatusHandler[📊 Get Registration Status<br/>GET /registration/status/{id}]
    
    %% Lambda Functions - Message Coordination
    ProcessMessageHandler[📨 Process Message Handler<br/>POST /messages/process]
    HealthCheckHandler[🏥 Health Check Handler<br/>GET /health]
    SystemStatusHandler[📊 System Status Handler<br/>GET /status]
    CleanupHandler[🧹 Cleanup Handler<br/>Scheduled cleanup]
    MetricsHandler[📈 Metrics Aggregator<br/>Scheduled metrics]
    
    %% Core Services
    OrchestrationService[🔄 Orchestration Service<br/>Registration Flow Management]
    MessageOrchestrator[🎯 Message Orchestrator<br/>Central Message Routing]
    StateManager[📊 State Manager<br/>Registration State Management]
    RoutingEngine[🔀 Routing Engine<br/>Intelligent Message Routing]
    ServiceClientFactory[🔗 Service Client Factory<br/>External Service Integration]
    EmailService[📧 Email Service<br/>SES Integration]
    RetryManager[🔄 Retry Manager<br/>Resilience & Circuit Breaker]
    MetricsAggregator[📊 Metrics Aggregator<br/>System Performance]
    SecurityCoordinator[🔐 Security Coordinator<br/>Auth & Audit]
    EventCoordinator[🎯 Event Coordinator<br/>Event Sourcing]
    
    %% External Services (Coordinated)
    AuthService[🔐 Auth Service<br/>Authentication]
    TenantService[🏢 Tenant Service<br/>Tenant Management]
    ChatService[💬 Chat Service<br/>Message Processing]
    AgentService[🤖 Agent Service<br/>AI Agents]
    WebSocketService[🔌 WebSocket Service<br/>Real-time Communication]
    PaymentService[💳 Payment Service<br/>Payment Processing]
    
    %% Storage & External
    DynamoDB[(🗄️ DynamoDB<br/>agent-scl-dev)]
    SES[📧 AWS SES<br/>Email Service]
    SecretsManager[🔑 Secrets Manager<br/>JWT Secret]
    CloudWatch[📊 CloudWatch<br/>Logs & Metrics]
    
    %% Client Connections
    Client -->|HTTP REST| APIGateway
    EmailUser -->|Email Verification| APIGateway
    PaymentProvider -.->|Webhook| APIGateway
    
    %% API Gateway Routing - Registration
    APIGateway --> CompleteRegHandler
    APIGateway --> VerifyEmailHandler
    APIGateway --> ProcessPaymentHandler
    APIGateway --> GetStatusHandler
    
    %% API Gateway Routing - Message Coordination
    APIGateway --> ProcessMessageHandler
    APIGateway --> HealthCheckHandler
    APIGateway --> SystemStatusHandler
    
    %% Scheduled Functions
    APIGateway --> CleanupHandler
    APIGateway --> MetricsHandler
    
    %% Handler Dependencies - Registration
    CompleteRegHandler --> OrchestrationService
    CompleteRegHandler --> StateManager
    CompleteRegHandler --> EmailService
    
    VerifyEmailHandler --> OrchestrationService
    VerifyEmailHandler --> StateManager
    
    ProcessPaymentHandler --> OrchestrationService
    ProcessPaymentHandler --> StateManager
    ProcessPaymentHandler --> ServiceClientFactory
    
    GetStatusHandler --> StateManager
    
    %% Handler Dependencies - Message Coordination
    ProcessMessageHandler --> MessageOrchestrator
    ProcessMessageHandler --> RoutingEngine
    ProcessMessageHandler --> RetryManager
    
    HealthCheckHandler --> ServiceClientFactory
    HealthCheckHandler --> MetricsAggregator
    
    SystemStatusHandler --> MetricsAggregator
    SystemStatusHandler --> ServiceClientFactory
    
    CleanupHandler --> StateManager
    MetricsHandler --> MetricsAggregator
    
    %% Service Dependencies - Core
    OrchestrationService --> StateManager
    OrchestrationService --> ServiceClientFactory
    OrchestrationService --> EmailService
    OrchestrationService --> SecurityCoordinator
    
    MessageOrchestrator --> RoutingEngine
    MessageOrchestrator --> ServiceClientFactory
    MessageOrchestrator --> RetryManager
    MessageOrchestrator --> EventCoordinator
    
    StateManager --> DynamoDB
    RoutingEngine --> MetricsAggregator
    ServiceClientFactory --> RetryManager
    EmailService --> SES
    
    %% External Service Coordination
    ServiceClientFactory --> AuthService
    ServiceClientFactory --> TenantService
    ServiceClientFactory --> ChatService
    ServiceClientFactory --> AgentService
    ServiceClientFactory --> WebSocketService
    ServiceClientFactory --> PaymentService
    
    %% Storage Dependencies
    StateManager --> DynamoDB
    MetricsAggregator --> CloudWatch
    SecurityCoordinator --> SecretsManager
    EventCoordinator --> DynamoDB
    
    %% Monitoring
    OrchestrationService --> CloudWatch
    MessageOrchestrator --> CloudWatch
    RetryManager --> CloudWatch
    SecurityCoordinator --> CloudWatch
    
    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handler fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef coordinated fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    
    class Client,EmailUser,PaymentProvider client
    class APIGateway gateway
    class CompleteRegHandler,VerifyEmailHandler,ProcessPaymentHandler,GetStatusHandler,ProcessMessageHandler,HealthCheckHandler,SystemStatusHandler,CleanupHandler,MetricsHandler handler
    class OrchestrationService,MessageOrchestrator,StateManager,RoutingEngine,ServiceClientFactory,EmailService,RetryManager,MetricsAggregator,SecurityCoordinator,EventCoordinator service
    class SES,SecretsManager,CloudWatch external
    class DynamoDB storage
    class AuthService,TenantService,ChatService,AgentService,WebSocketService,PaymentService coordinated