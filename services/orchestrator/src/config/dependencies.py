# services/orchestrator/src/config/dependencies.py
# Dependency injection configuration for orchestrator service

"""
Dependency injection configuration for the orchestrator service.
Registers all service interfaces and their implementations.
"""

from shared.dependency_injection import container
from shared.logger import lambda_logger

# Import service interfaces and implementations
from ..services.message_orchestrator import IMessageOrchestrator, MessageOrchestrator


def configure_dependencies() -> None:
    """Configure dependency injection for orchestrator service."""
    try:
        lambda_logger.info("Configuring orchestrator service dependencies...")
        
        # Register message orchestrator as singleton
        container.register_singleton(IMessageOrchestrator, MessageOrchestrator)
        
        lambda_logger.info("Orchestrator service dependencies configured successfully")
        
    except Exception as e:
        lambda_logger.error(f"Failed to configure orchestrator dependencies: {str(e)}")
        raise


# Auto-configure on import
configure_dependencies()
