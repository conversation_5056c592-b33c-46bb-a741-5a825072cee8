# services/orchestrator/src/common/imports.py
# Common imports for orchestrator service handlers and services

"""
Common imports for orchestrator service.
Centralizes all shared layer imports and orchestrator-specific utilities.
"""

import json
import uuid
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

# Shared layer imports
from shared.logger import lambda_logger, log_api_request, log_api_response, log_business_operation
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, AuthContext
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.decorators import measure_performance
from shared.exceptions import (
    ValidationException,
    AuthorizationException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.validation import validate_required_fields
from shared.validators import validate_uuid
from shared.config import get_settings, get_database_config
from shared.database import DynamoDBClient
from shared.dependency_injection import container

# Shared models
from shared.models import (
    Conversation, ConversationType, ConversationStatus, ConversationParticipant, ParticipantType,
    Message, MessageType, MessageDirection, MessageStatus, MessagePriority,
    MessageAttachment, MessageReaction,
    RoutingDestination, RoutingStrategy, RoutingPriority, RoutingRule, RoutingContext, RoutingDecision,
    MessageRouter, message_router, route_message, create_routing_context_from_message,
    create_chat_conversation, create_agent_conversation,
    create_chat_message, create_agent_request_message, create_agent_response_message
)

# Orchestrator service specific imports
from ..config.dependencies import configure_dependencies


# Common validation helpers
def validate_tenant_access(auth_context: AuthContext, tenant_id: str) -> bool:
    """Validate that user has access to tenant."""
    if auth_context.tenant_id != tenant_id:
        raise AuthorizationException(f"Access denied to tenant {tenant_id}")
    return True


def extract_path_parameter(event: Dict[str, Any], param_name: str) -> str:
    """Extract path parameter from event."""
    path_params = event.get('pathParameters') or {}
    param_value = path_params.get(param_name)
    if not param_value:
        raise ValidationException(f"Missing required path parameter: {param_name}")
    return param_value


def extract_query_parameter(event: Dict[str, Any], param_name: str, default: Any = None) -> Any:
    """Extract query parameter from event."""
    query_params = event.get('queryStringParameters') or {}
    return query_params.get(param_name, default)


def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
    """Parse and validate JSON request body."""
    body_str = event.get('body')
    if not body_str:
        raise ValidationException("Request body is required")
    
    try:
        return json.loads(body_str)
    except json.JSONDecodeError:
        raise ValidationException("Invalid JSON in request body")


# Orchestrator service specific response helpers
def success_response(data: Any = None, status_code: int = 200) -> Dict[str, Any]:
    """Create success response for orchestrator operations."""
    if data is None:
        return {'statusCode': status_code}
    return APIResponse.success(data, status_code)


def error_response(message: str, status_code: int = 400) -> Dict[str, Any]:
    """Create error response for orchestrator operations."""
    return APIResponse.error(message, status_code)


# Orchestrator service specific logging helpers
def log_orchestration_operation(
    operation: str,
    entity_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log orchestration operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="orchestration",
        entity_id=entity_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details=details or {}
    )


def log_message_routing(
    operation: str,
    message_id: str,
    conversation_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log message routing operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="message_routing",
        entity_id=message_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details={
            'conversation_id': conversation_id,
            **(details or {})
        }
    )


def log_service_coordination(
    operation: str,
    coordination_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log service coordination operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="service_coordination",
        entity_id=coordination_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details=details or {}
    )


# Orchestrator service specific error handling
def handle_orchestrator_error(error: Exception, operation: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle orchestrator service specific errors."""
    if isinstance(error, ValidationException):
        lambda_logger.warning(f"Validation error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 400)
    
    elif isinstance(error, AuthorizationException):
        lambda_logger.warning(f"Authorization error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 403)
    
    elif isinstance(error, ResourceNotFoundException):
        lambda_logger.warning(f"Resource not found in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 404)
    
    elif isinstance(error, BusinessLogicException):
        lambda_logger.warning(f"Business logic error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 422)
    
    else:
        lambda_logger.error(f"Unexpected error in {operation}", extra={
            'error': str(error),
            'error_type': type(error).__name__,
            'context': context
        })
        return error_response("Internal server error", 500)


# Orchestrator service specific decorator
def orchestrator_handler(operation_name: str, require_auth: bool = True):
    """Decorator for orchestrator service handlers."""
    def decorator(func):
        # Apply shared decorators in correct order
        decorated_func = func
        
        if require_auth:
            decorated_func = require_auth(decorated_func)
        
        decorated_func = rate_limit(requests_per_minute=300)(decorated_func)  # High limit for orchestration
        decorated_func = user_resilience(operation_name)(decorated_func)
        decorated_func = measure_performance(f"orchestrator_{operation_name}")(decorated_func)
        
        return decorated_func
    
    return decorator


# Service getters using dependency injection
def get_message_orchestrator():
    """Get message orchestrator instance."""
    from ..services.message_orchestrator import IMessageOrchestrator
    return container.get(IMessageOrchestrator)


# Message and conversation utilities
def create_message_from_request(
    request_data: Dict[str, Any],
    conversation: Conversation,
    auth_context: AuthContext
) -> Message:
    """Create a message object from request data."""
    message_type = MessageType(request_data.get('message_type', 'text'))
    
    if conversation.conversation_type == ConversationType.USER_TO_AGENT:
        return create_agent_request_message(
            conversation_id=conversation.conversation_id,
            tenant_id=conversation.tenant_id,
            user_id=auth_context.user_id,
            user_name=auth_context.email or "User",
            content=request_data['content'],
            agent_id=conversation.agent_id
        )
    else:
        return create_chat_message(
            conversation_id=conversation.conversation_id,
            tenant_id=conversation.tenant_id,
            user_id=auth_context.user_id,
            user_name=auth_context.email or "User",
            content=request_data['content']
        )


def validate_message_routing_request(request_data: Dict[str, Any]) -> None:
    """Validate message routing request data."""
    validate_required_fields(request_data, ['message'])
    
    message_data = request_data['message']
    validate_required_fields(message_data, [
        'message_id', 'conversation_id', 'tenant_id', 'sender_id',
        'sender_type', 'sender_name', 'message_type', 'content'
    ])
    
    # Validate message type
    try:
        MessageType(message_data['message_type'])
    except ValueError:
        raise ValidationException(f"Invalid message type: {message_data['message_type']}")
    
    # Validate conversation data if provided
    if 'conversation' in request_data:
        conversation_data = request_data['conversation']
        validate_required_fields(conversation_data, [
            'conversation_id', 'conversation_type', 'tenant_id'
        ])
        
        try:
            ConversationType(conversation_data['conversation_type'])
        except ValueError:
            raise ValidationException(f"Invalid conversation type: {conversation_data['conversation_type']}")


# Export all common utilities
__all__ = [
    # Core imports
    'json', 'uuid', 'datetime', 'timezone',
    'Dict', 'Any', 'List', 'Optional', 'Tuple',
    
    # Shared layer
    'lambda_logger', 'log_api_request', 'log_api_response', 'log_business_operation',
    'APIResponse', 'handle_cors_preflight',
    'require_auth', 'AuthContext',
    'rate_limit', 'user_resilience', 'measure_performance',
    'ValidationException', 'AuthorizationException', 'ResourceNotFoundException', 'BusinessLogicException',
    'validate_required_fields', 'validate_uuid',
    'get_settings', 'get_database_config',
    'DynamoDBClient', 'container',
    
    # Shared models
    'Conversation', 'ConversationType', 'ConversationStatus', 'ConversationParticipant', 'ParticipantType',
    'Message', 'MessageType', 'MessageDirection', 'MessageStatus', 'MessagePriority',
    'MessageAttachment', 'MessageReaction',
    'RoutingDestination', 'RoutingStrategy', 'RoutingPriority', 'RoutingRule', 'RoutingContext', 'RoutingDecision',
    'MessageRouter', 'message_router', 'route_message', 'create_routing_context_from_message',
    'create_chat_conversation', 'create_agent_conversation',
    'create_chat_message', 'create_agent_request_message', 'create_agent_response_message',
    
    # Orchestrator service utilities
    'validate_tenant_access', 'extract_path_parameter', 'extract_query_parameter',
    'parse_request_body', 'success_response', 'error_response',
    'log_orchestration_operation', 'log_message_routing', 'log_service_coordination',
    'handle_orchestrator_error', 'orchestrator_handler',
    'create_message_from_request', 'validate_message_routing_request',
    
    # Service getters
    'get_message_orchestrator'
]
