#!/usr/bin/env python3
# services/orchestrator/src/utils/state_manager.py
# State management for registration records

"""
State manager for registration records in DynamoDB.
Handles CRUD operations and state transitions with proper validation.
"""

import os
import boto3
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

from shared.logger import lambda_logger
from shared.exceptions import ResourceNotFoundException, ValidationException
from ..models.registration_models import (
    RegistrationRecord, RegistrationState, is_valid_transition
)


class StateManager:
    """Manages registration state in DynamoDB."""
    
    def __init__(self):
        self.dynamodb = boto3.resource('dynamodb')
        self.table_name = os.environ.get('REGISTRATIONS_TABLE')
        if not self.table_name:
            raise ValueError("REGISTRATIONS_TABLE environment variable not set")
        
        self.table = self.dynamodb.Table(self.table_name)
    
    def save_registration(self, registration: RegistrationRecord) -> None:
        """Save registration record to DynamoDB."""
        try:
            item = registration.to_dict()
            
            lambda_logger.info("Saving registration record", extra={
                'registration_id': registration.registration_id,
                'state': registration.state.value,
                'email': registration.email
            })
            
            self.table.put_item(Item=item)
            
        except ClientError as e:
            lambda_logger.error("Failed to save registration", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })
            raise
    
    def get_registration(self, registration_id: str) -> Optional[RegistrationRecord]:
        """Get registration record by ID."""
        try:
            response = self.table.get_item(
                Key={'registration_id': registration_id}
            )
            
            if 'Item' not in response:
                return None
            
            return RegistrationRecord.from_dict(response['Item'])
            
        except ClientError as e:
            lambda_logger.error("Failed to get registration", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise
    
    def update_registration_state(self, registration_id: str, new_state: RegistrationState,
                                 updates: Optional[Dict[str, Any]] = None) -> RegistrationRecord:
        """Update registration state with validation."""
        # Get current registration
        registration = self.get_registration(registration_id)
        if not registration:
            raise ResourceNotFoundException(f"Registration {registration_id} not found")
        
        # Validate state transition
        if not is_valid_transition(registration.state, new_state):
            raise ValidationException(
                f"Invalid state transition from {registration.state.value} to {new_state.value}"
            )
        
        # Record state transition
        old_state = registration.state
        registration.add_state_transition(old_state, new_state, updates)
        
        # Update state and timestamp
        registration.state = new_state
        registration.updated_at = datetime.utcnow()
        
        # Apply additional updates
        if updates:
            for key, value in updates.items():
                if hasattr(registration, key):
                    setattr(registration, key, value)
        
        # Save updated registration
        self.save_registration(registration)
        
        lambda_logger.info("Registration state updated", extra={
            'registration_id': registration_id,
            'old_state': old_state.value,
            'new_state': new_state.value,
            'updates': updates
        })
        
        return registration
    
    def find_by_email(self, email: str) -> List[RegistrationRecord]:
        """Find registrations by email."""
        try:
            response = self.table.query(
                IndexName='EmailIndex',
                KeyConditionExpression=boto3.dynamodb.conditions.Key('email').eq(email)
            )
            
            registrations = []
            for item in response['Items']:
                registrations.append(RegistrationRecord.from_dict(item))
            
            return registrations
            
        except ClientError as e:
            lambda_logger.error("Failed to find registrations by email", extra={
                'email': email,
                'error': str(e)
            })
            raise
    
    def find_expired_registrations(self, limit: int = 100) -> List[RegistrationRecord]:
        """Find expired registrations for cleanup."""
        try:
            current_time = datetime.utcnow().isoformat()
            
            response = self.table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('expires_at').lt(current_time),
                Limit=limit
            )
            
            registrations = []
            for item in response['Items']:
                registrations.append(RegistrationRecord.from_dict(item))
            
            return registrations
            
        except ClientError as e:
            lambda_logger.error("Failed to find expired registrations", extra={
                'error': str(e)
            })
            raise
    
    def find_abandoned_registrations(self, hours_old: int = 1, limit: int = 100) -> List[RegistrationRecord]:
        """Find abandoned registrations for cleanup."""
        try:
            cutoff_time = (datetime.utcnow() - timedelta(hours=hours_old)).isoformat()
            
            # Find registrations in intermediate states that are old
            abandoned_states = [
                RegistrationState.INITIATED.value,
                RegistrationState.EMAIL_SENT.value,
                RegistrationState.EMAIL_VERIFIED.value,
                RegistrationState.PLAN_SELECTED.value
            ]
            
            response = self.table.scan(
                FilterExpression=(
                    boto3.dynamodb.conditions.Attr('created_at').lt(cutoff_time) &
                    boto3.dynamodb.conditions.Attr('state').is_in(abandoned_states)
                ),
                Limit=limit
            )
            
            registrations = []
            for item in response['Items']:
                registrations.append(RegistrationRecord.from_dict(item))
            
            return registrations
            
        except ClientError as e:
            lambda_logger.error("Failed to find abandoned registrations", extra={
                'hours_old': hours_old,
                'error': str(e)
            })
            raise
    
    def delete_registration(self, registration_id: str) -> None:
        """Delete registration record."""
        try:
            lambda_logger.info("Deleting registration record", extra={
                'registration_id': registration_id
            })
            
            self.table.delete_item(
                Key={'registration_id': registration_id}
            )
            
        except ClientError as e:
            lambda_logger.error("Failed to delete registration", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise
    
    def cleanup_registration(self, registration_id: str) -> None:
        """Cleanup registration and mark as abandoned."""
        try:
            registration = self.get_registration(registration_id)
            if not registration:
                return
            
            # Mark as abandoned before cleanup
            if registration.state not in [RegistrationState.COMPLETED, RegistrationState.FAILED, RegistrationState.ABANDONED]:
                self.update_registration_state(registration_id, RegistrationState.ABANDONED)
            
            lambda_logger.info("Registration cleaned up", extra={
                'registration_id': registration_id,
                'final_state': registration.state.value
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup registration", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise
    
    def get_registration_stats(self) -> Dict[str, Any]:
        """Get registration statistics."""
        try:
            # This is a simplified version - in production you'd use more efficient queries
            response = self.table.scan()
            
            stats = {
                'total': 0,
                'by_state': {},
                'completed_today': 0,
                'failed_today': 0
            }
            
            today = datetime.utcnow().date()
            
            for item in response['Items']:
                registration = RegistrationRecord.from_dict(item)
                stats['total'] += 1
                
                state_value = registration.state.value
                stats['by_state'][state_value] = stats['by_state'].get(state_value, 0) + 1
                
                # Check if completed/failed today
                if registration.updated_at.date() == today:
                    if registration.state == RegistrationState.COMPLETED:
                        stats['completed_today'] += 1
                    elif registration.state == RegistrationState.FAILED:
                        stats['failed_today'] += 1
            
            return stats
            
        except ClientError as e:
            lambda_logger.error("Failed to get registration stats", extra={
                'error': str(e)
            })
            raise
