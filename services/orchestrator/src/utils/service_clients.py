#!/usr/bin/env python3
# services/orchestrator/src/utils/service_clients.py
# Service clients for inter-service communication

"""
Service clients for communicating with other microservices.
Handles HTTP requests with proper error handling and retries.
"""

import os
import json
import time
import requests
from typing import Dict, Any, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from shared.logger import lambda_logger
from shared.exceptions import ExternalServiceException


class BaseServiceClient:
    """Base class for service clients with common functionality."""
    
    def __init__(self, base_url: str, service_name: str):
        self.base_url = base_url.rstrip('/')
        self.service_name = service_name
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Default headers
        session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': f'orchestrator-service/1.0'
        })
        
        return session
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None,
                     headers: Optional[Dict[str, str]] = None, timeout: int = 30) -> Dict[str, Any]:
        """Make HTTP request with error handling."""
        url = f"{self.base_url}{endpoint}"
        
        # Merge headers
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        try:
            lambda_logger.info(f"Making {method} request to {self.service_name}", extra={
                'service': self.service_name,
                'method': method,
                'endpoint': endpoint,
                'url': url
            })
            
            start_time = time.time()
            
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                headers=request_headers,
                timeout=timeout
            )
            
            duration = time.time() - start_time
            
            lambda_logger.info(f"{self.service_name} request completed", extra={
                'service': self.service_name,
                'method': method,
                'endpoint': endpoint,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2)
            })
            
            # Handle response
            if response.status_code >= 400:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    error_data = {'message': response.text}
                
                raise ExternalServiceException(
                    f"{self.service_name} request failed",
                    details={
                        'service': self.service_name,
                        'status_code': response.status_code,
                        'error': error_data,
                        'endpoint': endpoint
                    }
                )
            
            return response.json()
            
        except requests.exceptions.Timeout:
            lambda_logger.error(f"{self.service_name} request timeout", extra={
                'service': self.service_name,
                'endpoint': endpoint,
                'timeout': timeout
            })
            raise ExternalServiceException(f"{self.service_name} request timeout")
            
        except requests.exceptions.ConnectionError:
            lambda_logger.error(f"{self.service_name} connection error", extra={
                'service': self.service_name,
                'endpoint': endpoint
            })
            raise ExternalServiceException(f"{self.service_name} connection error")
            
        except Exception as e:
            lambda_logger.error(f"{self.service_name} request failed", extra={
                'service': self.service_name,
                'endpoint': endpoint,
                'error': str(e)
            })
            raise ExternalServiceException(f"{self.service_name} request failed: {str(e)}")


class TenantServiceClient(BaseServiceClient):
    """Client for Tenant Service operations."""
    
    def __init__(self):
        base_url = os.environ.get('TENANT_SERVICE_URL')
        if not base_url:
            raise ValueError("TENANT_SERVICE_URL environment variable not set")
        super().__init__(base_url, 'tenant-service')
    
    def create_tenant(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new tenant."""
        return self._make_request('POST', '/tenant/register', data=company_data)
    
    def get_tenant(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant information."""
        return self._make_request('GET', f'/tenant/profile', headers={
            'X-Tenant-ID': tenant_id
        })


class AuthServiceClient(BaseServiceClient):
    """Client for Auth Service operations."""
    
    def __init__(self):
        base_url = os.environ.get('AUTH_SERVICE_URL')
        if not base_url:
            raise ValueError("AUTH_SERVICE_URL environment variable not set")
        super().__init__(base_url, 'auth-service')
    
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new user."""
        return self._make_request('POST', '/auth/register', data=user_data)
    
    def send_verification_email(self, user_id: str, email: str) -> Dict[str, Any]:
        """Send verification email."""
        return self._make_request('POST', '/auth/resend-verification', data={
            'email': email
        })
    
    def verify_email(self, verification_token: str) -> Dict[str, Any]:
        """Verify email with token."""
        return self._make_request('POST', '/auth/verify-email', data={
            'verification_token': verification_token
        })


class PaymentServiceClient(BaseServiceClient):
    """Client for Payment Service operations."""
    
    def __init__(self):
        base_url = os.environ.get('PAYMENT_SERVICE_URL')
        if not base_url:
            raise ValueError("PAYMENT_SERVICE_URL environment variable not set")
        super().__init__(base_url, 'payment-service')
    
    def get_plans(self) -> Dict[str, Any]:
        """Get available subscription plans."""
        return self._make_request('GET', '/payment/plans')
    
    def create_subscription(self, subscription_data: Dict[str, Any], 
                          auth_token: str) -> Dict[str, Any]:
        """Create a new subscription."""
        return self._make_request('POST', '/payment/subscriptions', 
                                data=subscription_data,
                                headers={'Authorization': f'Bearer {auth_token}'})
    
    def get_subscription(self, subscription_id: str, auth_token: str) -> Dict[str, Any]:
        """Get subscription details."""
        return self._make_request('GET', f'/payment/subscriptions/{subscription_id}',
                                headers={'Authorization': f'Bearer {auth_token}'})


class EmailServiceClient(BaseServiceClient):
    """Client for Email Service operations."""
    
    def __init__(self):
        # For now, we'll use SES directly, but this can be extended to use a dedicated email service
        self.service_name = 'email-service'
    
    def send_verification_email(self, email: str, verification_link: str, 
                              user_name: str) -> Dict[str, Any]:
        """Send verification email."""
        # This would integrate with SES or a dedicated email service
        # For now, return success (actual implementation would send email)
        lambda_logger.info("Sending verification email", extra={
            'email': email,
            'user_name': user_name
        })
        
        return {
            'success': True,
            'message': 'Verification email sent successfully'
        }
    
    def send_welcome_email(self, email: str, user_name: str, 
                          company_name: str) -> Dict[str, Any]:
        """Send welcome email after successful registration."""
        lambda_logger.info("Sending welcome email", extra={
            'email': email,
            'user_name': user_name,
            'company_name': company_name
        })
        
        return {
            'success': True,
            'message': 'Welcome email sent successfully'
        }


# Service client factory
class ServiceClientFactory:
    """Factory for creating service clients."""
    
    _clients = {}
    
    @classmethod
    def get_tenant_client(cls) -> TenantServiceClient:
        """Get tenant service client (singleton)."""
        if 'tenant' not in cls._clients:
            cls._clients['tenant'] = TenantServiceClient()
        return cls._clients['tenant']
    
    @classmethod
    def get_auth_client(cls) -> AuthServiceClient:
        """Get auth service client (singleton)."""
        if 'auth' not in cls._clients:
            cls._clients['auth'] = AuthServiceClient()
        return cls._clients['auth']
    
    @classmethod
    def get_payment_client(cls) -> PaymentServiceClient:
        """Get payment service client (singleton)."""
        if 'payment' not in cls._clients:
            cls._clients['payment'] = PaymentServiceClient()
        return cls._clients['payment']
    
    @classmethod
    def get_email_client(cls) -> EmailServiceClient:
        """Get email service client (singleton)."""
        if 'email' not in cls._clients:
            cls._clients['email'] = EmailServiceClient()
        return cls._clients['email']
