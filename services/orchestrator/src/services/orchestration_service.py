#!/usr/bin/env python3
# services/orchestrator/src/services/orchestration_service.py
# Main orchestration service for registration flow

"""
Main orchestration service that coordinates the complete registration flow.
Handles state transitions and service coordination with proper error handling.
"""

import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from shared.logger import lambda_logger, audit_log
from shared.exceptions import ValidationException, ExternalServiceException
from ..models.registration_models import (
    RegistrationRequest, RegistrationRecord, RegistrationState, 
    PaymentData, get_next_steps
)
from ..utils.state_manager import StateManager
from ..utils.service_clients import ServiceClientFactory


class OrchestrationService:
    """Main orchestration service for registration flow."""
    
    def __init__(self):
        self.state_manager = StateManager()
        self.tenant_client = ServiceClientFactory.get_tenant_client()
        self.auth_client = ServiceClientFactory.get_auth_client()
        self.payment_client = ServiceClientFactory.get_payment_client()
        self.email_client = ServiceClientFactory.get_email_client()
    
    def start_registration(self, request: RegistrationRequest) -> RegistrationRecord:
        """Start the complete registration process."""
        registration_id = f"reg_{uuid.uuid4().hex[:12]}"
        
        # Create initial registration record
        registration = RegistrationRecord(
            registration_id=registration_id,
            state=RegistrationState.INITIATED,
            email=request.user_data.email,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1),
            company_data=request.company_data.to_dict(),
            user_data=request.user_data.to_dict(),
            state_history=[]
        )
        
        # Save initial state
        self.state_manager.save_registration(registration)
        
        audit_log("registration_started", {
            'registration_id': registration_id,
            'email': request.user_data.email,
            'company_name': request.company_data.company_name
        })
        
        try:
            # Step 1: Create tenant
            registration = self._create_tenant(registration, request.company_data.to_dict())
            
            # Step 2: Create master user
            registration = self._create_master_user(registration, request.user_data.to_dict())
            
            # Step 3: Send verification email
            registration = self._send_verification_email(registration)
            
            lambda_logger.info("Registration flow initiated successfully", extra={
                'registration_id': registration_id,
                'state': registration.state.value,
                'tenant_id': registration.tenant_id,
                'user_id': registration.user_id
            })
            
            return registration
            
        except Exception as e:
            # Mark as failed and cleanup
            try:
                self.state_manager.update_registration_state(
                    registration_id, 
                    RegistrationState.FAILED,
                    {'error_message': str(e)}
                )
                self._cleanup_failed_registration(registration)
            except:
                pass  # Don't fail on cleanup errors
            
            lambda_logger.error("Registration flow failed", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise
    
    def verify_email(self, registration_id: str, verification_token: str) -> RegistrationRecord:
        """Verify email and advance registration."""
        registration = self.state_manager.get_registration(registration_id)
        if not registration:
            raise ValidationException("Registration not found")
        
        if registration.state != RegistrationState.EMAIL_SENT:
            raise ValidationException(f"Invalid state for email verification: {registration.state.value}")
        
        try:
            # Verify email with auth service
            verify_result = self.auth_client.verify_email(verification_token)
            
            # Update registration state
            registration = self.state_manager.update_registration_state(
                registration_id,
                RegistrationState.EMAIL_VERIFIED,
                {'verification_token': verification_token}
            )
            
            audit_log("email_verified", {
                'registration_id': registration_id,
                'user_id': registration.user_id
            })
            
            lambda_logger.info("Email verified successfully", extra={
                'registration_id': registration_id,
                'user_id': registration.user_id
            })
            
            return registration
            
        except Exception as e:
            lambda_logger.error("Email verification failed", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise
    
    def process_payment(self, registration_id: str, payment_data: PaymentData, 
                       auth_token: str) -> RegistrationRecord:
        """Process payment and complete registration."""
        registration = self.state_manager.get_registration(registration_id)
        if not registration:
            raise ValidationException("Registration not found")
        
        if registration.state != RegistrationState.EMAIL_VERIFIED:
            raise ValidationException(f"Invalid state for payment: {registration.state.value}")
        
        try:
            # Update state to payment processing
            registration = self.state_manager.update_registration_state(
                registration_id,
                RegistrationState.PAYMENT_PROCESSING,
                {'plan_id': payment_data.plan_id}
            )
            
            # Create subscription
            subscription_data = {
                'tenant_id': registration.tenant_id,
                'plan_id': payment_data.plan_id,
                'billing_interval': payment_data.billing_interval,
                'payment_method_id': payment_data.payment_method_id
            }
            
            subscription_result = self.payment_client.create_subscription(
                subscription_data, auth_token
            )
            
            # Update state to payment completed
            registration = self.state_manager.update_registration_state(
                registration_id,
                RegistrationState.PAYMENT_COMPLETED,
                {'subscription_id': subscription_result['data']['subscription_id']}
            )
            
            # Start configuration process
            registration = self._configure_tenant_resources(registration)
            
            # Mark as completed
            registration = self.state_manager.update_registration_state(
                registration_id,
                RegistrationState.COMPLETED
            )
            
            # Send welcome email
            self._send_welcome_email(registration)
            
            audit_log("registration_completed", {
                'registration_id': registration_id,
                'tenant_id': registration.tenant_id,
                'subscription_id': registration.subscription_id
            })
            
            lambda_logger.info("Registration completed successfully", extra={
                'registration_id': registration_id,
                'tenant_id': registration.tenant_id,
                'subscription_id': registration.subscription_id
            })
            
            return registration
            
        except Exception as e:
            # Mark as failed
            try:
                self.state_manager.update_registration_state(
                    registration_id,
                    RegistrationState.FAILED,
                    {'error_message': str(e)}
                )
            except:
                pass
            
            lambda_logger.error("Payment processing failed", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise
    
    def get_registration_status(self, registration_id: str) -> Dict[str, Any]:
        """Get registration status and next steps."""
        registration = self.state_manager.get_registration(registration_id)
        if not registration:
            raise ValidationException("Registration not found")
        
        return {
            'registration_id': registration_id,
            'state': registration.state.value,
            'created_at': registration.created_at.isoformat(),
            'updated_at': registration.updated_at.isoformat(),
            'expires_at': registration.expires_at.isoformat(),
            'tenant_id': registration.tenant_id,
            'user_id': registration.user_id,
            'subscription_id': registration.subscription_id,
            'error_message': registration.error_message,
            'next_steps': get_next_steps(registration.state)
        }
    
    def cancel_registration(self, registration_id: str) -> None:
        """Cancel and cleanup registration."""
        registration = self.state_manager.get_registration(registration_id)
        if not registration:
            raise ValidationException("Registration not found")
        
        if registration.state in [RegistrationState.COMPLETED, RegistrationState.FAILED]:
            raise ValidationException("Cannot cancel completed or failed registration")
        
        try:
            # Cleanup resources
            self._cleanup_failed_registration(registration)
            
            # Mark as abandoned
            self.state_manager.update_registration_state(
                registration_id,
                RegistrationState.ABANDONED
            )
            
            audit_log("registration_cancelled", {
                'registration_id': registration_id,
                'tenant_id': registration.tenant_id
            })
            
            lambda_logger.info("Registration cancelled", extra={
                'registration_id': registration_id
            })
            
        except Exception as e:
            lambda_logger.error("Registration cancellation failed", extra={
                'registration_id': registration_id,
                'error': str(e)
            })
            raise

    def _create_tenant(self, registration: RegistrationRecord,
                      company_data: Dict[str, Any]) -> RegistrationRecord:
        """Create tenant using tenant service."""
        try:
            tenant_result = self.tenant_client.create_tenant(company_data)

            # Update registration with tenant_id
            registration = self.state_manager.update_registration_state(
                registration.registration_id,
                RegistrationState.TENANT_CREATED,
                {'tenant_id': tenant_result['data']['tenant_id']}
            )

            lambda_logger.info("Tenant created successfully", extra={
                'registration_id': registration.registration_id,
                'tenant_id': registration.tenant_id
            })

            return registration

        except Exception as e:
            lambda_logger.error("Tenant creation failed", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Tenant creation failed: {str(e)}")

    def _create_master_user(self, registration: RegistrationRecord,
                           user_data: Dict[str, Any]) -> RegistrationRecord:
        """Create master user using auth service."""
        try:
            # Add tenant_id and role to user data
            user_data_with_tenant = {
                **user_data,
                'tenant_id': registration.tenant_id,
                'role': 'MASTER'
            }

            user_result = self.auth_client.create_user(user_data_with_tenant)

            # Update registration with user_id
            registration = self.state_manager.update_registration_state(
                registration.registration_id,
                RegistrationState.USER_CREATED,
                {'user_id': user_result['data']['user_id']}
            )

            lambda_logger.info("Master user created successfully", extra={
                'registration_id': registration.registration_id,
                'user_id': registration.user_id,
                'tenant_id': registration.tenant_id
            })

            return registration

        except Exception as e:
            lambda_logger.error("Master user creation failed", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Master user creation failed: {str(e)}")

    def _send_verification_email(self, registration: RegistrationRecord) -> RegistrationRecord:
        """Send verification email."""
        try:
            # Send verification email through auth service
            email_result = self.auth_client.send_verification_email(
                registration.user_id,
                registration.email
            )

            # Update registration state
            registration = self.state_manager.update_registration_state(
                registration.registration_id,
                RegistrationState.EMAIL_SENT
            )

            lambda_logger.info("Verification email sent", extra={
                'registration_id': registration.registration_id,
                'email': registration.email
            })

            return registration

        except Exception as e:
            lambda_logger.error("Verification email failed", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Verification email failed: {str(e)}")

    def _configure_tenant_resources(self, registration: RegistrationRecord) -> RegistrationRecord:
        """Configure tenant resources (placeholder for Setup Service)."""
        try:
            # Update state to configuring
            registration = self.state_manager.update_registration_state(
                registration.registration_id,
                RegistrationState.CONFIGURING
            )

            # TODO: Call Setup Service to configure AWS resources
            # For now, we'll simulate the configuration
            lambda_logger.info("Configuring tenant resources", extra={
                'registration_id': registration.registration_id,
                'tenant_id': registration.tenant_id,
                'plan_id': registration.plan_id
            })

            # Simulate configuration time
            import time
            time.sleep(1)

            lambda_logger.info("Tenant resources configured", extra={
                'registration_id': registration.registration_id,
                'tenant_id': registration.tenant_id
            })

            return registration

        except Exception as e:
            lambda_logger.error("Tenant configuration failed", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })
            raise ExternalServiceException(f"Tenant configuration failed: {str(e)}")

    def _send_welcome_email(self, registration: RegistrationRecord) -> None:
        """Send welcome email after successful registration."""
        try:
            user_name = f"{registration.user_data.get('first_name', '')} {registration.user_data.get('last_name', '')}"
            company_name = registration.company_data.get('company_name', '')

            self.email_client.send_welcome_email(
                registration.email,
                user_name.strip(),
                company_name
            )

            lambda_logger.info("Welcome email sent", extra={
                'registration_id': registration.registration_id,
                'email': registration.email
            })

        except Exception as e:
            # Don't fail the registration for email issues
            lambda_logger.warning("Welcome email failed", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })

    def _cleanup_failed_registration(self, registration: RegistrationRecord) -> None:
        """Cleanup resources for failed registration."""
        try:
            lambda_logger.info("Cleaning up failed registration", extra={
                'registration_id': registration.registration_id,
                'state': registration.state.value
            })

            # TODO: Implement cleanup logic
            # - Delete tenant if created
            # - Delete user if created
            # - Cancel any pending payments
            # For now, just log the cleanup

            lambda_logger.info("Registration cleanup completed", extra={
                'registration_id': registration.registration_id
            })

        except Exception as e:
            lambda_logger.error("Registration cleanup failed", extra={
                'registration_id': registration.registration_id,
                'error': str(e)
            })
