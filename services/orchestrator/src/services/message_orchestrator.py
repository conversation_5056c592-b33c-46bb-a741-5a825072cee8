# services/orchestrator/src/services/message_orchestrator.py
# Message orchestration service for routing messages between services

"""
Message Orchestrator Service

This service is responsible for:
1. Receiving all messages from any source
2. Determining the correct routing based on conversation type and content
3. Coordinating between Chat Service, Agent Service, and WebSocket Service
4. Ensuring messages are delivered to the correct destinations
"""

import json
import requests
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timezone
from abc import ABC, abstractmethod

from shared.logger import lambda_logger, log_business_operation
from shared.models import (
    Conversation, ConversationType, Message, MessageType, MessageDirection,
    RoutingContext, RoutingDecision, route_message, create_routing_context_from_message
)
from shared.database import DynamoDBClient
from shared.config import get_settings
from shared.exceptions import ValidationException, BusinessLogicException


class IMessageOrchestrator(ABC):
    """Interface for message orchestration operations."""
    
    @abstractmethod
    def process_message(
        self,
        message_data: Dict[str, Any],
        conversation_data: Optional[Dict[str, Any]] = None
    ) -> <PERSON><PERSON>[bool, Dict[str, Any], Optional[str]]:
        """Process and route a message."""
        pass
    
    @abstractmethod
    def route_to_chat_service(
        self,
        message: Message,
        conversation: Conversation
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to chat service."""
        pass
    
    @abstractmethod
    def route_to_agent_service(
        self,
        message: Message,
        conversation: Conversation
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to agent service."""
        pass
    
    @abstractmethod
    def notify_websocket_service(
        self,
        message: Message,
        conversation: Conversation,
        routing_decision: RoutingDecision
    ) -> Tuple[bool, Optional[str]]:
        """Send real-time notification via WebSocket service."""
        pass


class MessageOrchestrator(IMessageOrchestrator):
    """
    Central message orchestrator that routes messages between services.
    
    This orchestrator ensures that:
    - User-to-user messages go to Chat Service
    - User-to-agent messages go to Agent Service
    - Agent responses are properly routed back
    - Real-time notifications are sent via WebSocket Service
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.db_client = DynamoDBClient()
        
        # Service endpoints
        self.chat_service_url = self.settings.get('chat_service_url', 'https://api.platform.com/chat')
        self.agent_service_url = self.settings.get('agent_service_url', 'https://api.platform.com/agent')
        self.websocket_service_url = self.settings.get('websocket_service_url', 'https://api.platform.com/websocket')
        
        # Timeout settings
        self.service_timeout = self.settings.get('service_timeout_seconds', 30)
    
    def process_message(
        self,
        message_data: Dict[str, Any],
        conversation_data: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Process and route a message through the system.
        
        This is the main entry point for all messages.
        """
        try:
            # Validate message data
            self._validate_message_data(message_data)
            
            # Get or create conversation
            if conversation_data:
                conversation = Conversation.from_dict(conversation_data)
            else:
                conversation = self._get_or_create_conversation(message_data)
            
            # Create message object
            message = Message.from_dict(message_data)
            
            # Create routing context
            routing_context = create_routing_context_from_message(
                message_data, conversation.to_dict()
            )
            
            # Make routing decision
            routing_decision = route_message(routing_context)
            
            lambda_logger.info("Message routing decision made", extra={
                'message_id': message.message_id,
                'conversation_id': conversation.conversation_id,
                'primary_destination': routing_decision.primary_destination.value,
                'strategy': routing_decision.strategy.value,
                'routing_rules': routing_decision.routing_rules_applied
            })
            
            # Execute routing
            success, result, error = self._execute_routing(message, conversation, routing_decision)
            
            if success:
                # Send real-time notifications if needed
                self._handle_realtime_notifications(message, conversation, routing_decision)
                
                # Log successful processing
                log_business_operation(
                    lambda_logger,
                    operation="message_orchestrated",
                    entity_type="message",
                    entity_id=message.message_id,
                    tenant_id=message.tenant_id,
                    user_id=message.sender_id,
                    status="completed",
                    details={
                        'conversation_id': conversation.conversation_id,
                        'conversation_type': conversation.conversation_type.value,
                        'destination': routing_decision.primary_destination.value,
                        'strategy': routing_decision.strategy.value
                    }
                )
                
                return True, {
                    'message_id': message.message_id,
                    'conversation_id': conversation.conversation_id,
                    'routing_decision': routing_decision.to_dict(),
                    'processing_result': result
                }, None
            else:
                return False, {}, error
                
        except Exception as e:
            lambda_logger.error(f"Message orchestration failed: {str(e)}", extra={
                'message_data': message_data,
                'conversation_data': conversation_data
            })
            return False, {}, str(e)
    
    def route_to_chat_service(
        self,
        message: Message,
        conversation: Conversation
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to chat service."""
        try:
            # Prepare payload for Chat Service
            payload = {
                'message': message.to_dict(),
                'conversation': conversation.to_dict(),
                'source': 'orchestrator'
            }
            
            # Determine the correct chat service endpoint
            if conversation.conversation_type == ConversationType.USER_TO_USER:
                endpoint = f"{self.chat_service_url}/messages"
            else:
                # Agent responses go through chat service for delivery
                endpoint = f"{self.chat_service_url}/agent-responses"
            
            # Call Chat Service
            response = requests.post(
                endpoint,
                json=payload,
                timeout=self.service_timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                lambda_logger.info("Message routed to chat service successfully", extra={
                    'message_id': message.message_id,
                    'endpoint': endpoint
                })
                return True, result, None
            else:
                error_msg = f"Chat service returned {response.status_code}: {response.text}"
                lambda_logger.error(error_msg)
                return False, {}, error_msg
                
        except Exception as e:
            error_msg = f"Failed to route to chat service: {str(e)}"
            lambda_logger.error(error_msg)
            return False, {}, error_msg
    
    def route_to_agent_service(
        self,
        message: Message,
        conversation: Conversation
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to agent service."""
        try:
            # Prepare payload for Agent Service
            payload = {
                'message': message.to_dict(),
                'conversation': conversation.to_dict(),
                'agent_id': conversation.agent_id,
                'agent_type': conversation.agent_type,
                'source': 'orchestrator'
            }
            
            # Call Agent Service
            endpoint = f"{self.agent_service_url}/messages/process"
            response = requests.post(
                endpoint,
                json=payload,
                timeout=self.service_timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                lambda_logger.info("Message routed to agent service successfully", extra={
                    'message_id': message.message_id,
                    'agent_id': conversation.agent_id,
                    'agent_type': conversation.agent_type
                })
                return True, result, None
            else:
                error_msg = f"Agent service returned {response.status_code}: {response.text}"
                lambda_logger.error(error_msg)
                return False, {}, error_msg
                
        except Exception as e:
            error_msg = f"Failed to route to agent service: {str(e)}"
            lambda_logger.error(error_msg)
            return False, {}, error_msg
    
    def notify_websocket_service(
        self,
        message: Message,
        conversation: Conversation,
        routing_decision: RoutingDecision
    ) -> Tuple[bool, Optional[str]]:
        """Send real-time notification via WebSocket service."""
        try:
            # Prepare notification payload
            notification_payload = {
                'type': 'message_notification',
                'message': message.to_dict(),
                'conversation': conversation.to_dict(),
                'routing_info': {
                    'destination': routing_decision.primary_destination.value,
                    'strategy': routing_decision.strategy.value
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Determine recipients based on conversation type
            recipients = self._get_notification_recipients(conversation, message)
            
            # Call WebSocket Service
            endpoint = f"{self.websocket_service_url}/broadcast"
            payload = {
                'recipients': recipients,
                'notification': notification_payload,
                'tenant_id': conversation.tenant_id
            }
            
            response = requests.post(
                endpoint,
                json=payload,
                timeout=self.service_timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                lambda_logger.info("WebSocket notification sent successfully", extra={
                    'message_id': message.message_id,
                    'recipients_count': len(recipients)
                })
                return True, None
            else:
                error_msg = f"WebSocket service returned {response.status_code}: {response.text}"
                lambda_logger.warning(error_msg)  # Warning, not error, as this is not critical
                return False, error_msg
                
        except Exception as e:
            error_msg = f"Failed to send WebSocket notification: {str(e)}"
            lambda_logger.warning(error_msg)  # Warning, not error, as this is not critical
            return False, error_msg
    
    def _validate_message_data(self, message_data: Dict[str, Any]) -> None:
        """Validate message data."""
        required_fields = ['message_id', 'conversation_id', 'tenant_id', 'sender_id', 'content']
        
        for field in required_fields:
            if field not in message_data:
                raise ValidationException(f"Missing required field: {field}")
        
        # Validate message type
        if 'message_type' in message_data:
            try:
                MessageType(message_data['message_type'])
            except ValueError:
                raise ValidationException(f"Invalid message type: {message_data['message_type']}")
    
    def _get_or_create_conversation(self, message_data: Dict[str, Any]) -> Conversation:
        """Get existing conversation or create a new one."""
        conversation_id = message_data['conversation_id']
        tenant_id = message_data['tenant_id']
        
        # Try to get existing conversation
        try:
            conversation_data = self.db_client.get_item(
                pk=f"TENANT#{tenant_id}",
                sk=f"CONVERSATION#{conversation_id}",
                tenant_id=tenant_id
            )
            
            if conversation_data:
                return Conversation.from_dict(conversation_data)
        except Exception as e:
            lambda_logger.warning(f"Failed to get conversation: {str(e)}")
        
        # Create new conversation if not found
        # This is a simplified version - in practice, you'd need more context
        conversation = Conversation(
            conversation_id=conversation_id,
            conversation_type=ConversationType.USER_TO_USER,  # Default
            tenant_id=tenant_id
        )
        
        return conversation
    
    def _execute_routing(
        self,
        message: Message,
        conversation: Conversation,
        routing_decision: RoutingDecision
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Execute the routing decision."""
        from shared.models import RoutingDestination
        
        if routing_decision.primary_destination == RoutingDestination.CHAT_SERVICE:
            return self.route_to_chat_service(message, conversation)
        elif routing_decision.primary_destination == RoutingDestination.AGENT_SERVICE:
            return self.route_to_agent_service(message, conversation)
        elif routing_decision.primary_destination == RoutingDestination.WEBSOCKET_SERVICE:
            # For direct WebSocket routing (like typing indicators)
            success, error = self.notify_websocket_service(message, conversation, routing_decision)
            return success, {'websocket_sent': success}, error
        else:
            error_msg = f"Unsupported routing destination: {routing_decision.primary_destination.value}"
            return False, {}, error_msg
    
    def _handle_realtime_notifications(
        self,
        message: Message,
        conversation: Conversation,
        routing_decision: RoutingDecision
    ) -> None:
        """Handle real-time notifications for the message."""
        from shared.models import RoutingDestination, RoutingStrategy
        
        # Send WebSocket notifications for broadcast strategy
        if routing_decision.strategy == RoutingStrategy.BROADCAST:
            self.notify_websocket_service(message, conversation, routing_decision)
        
        # Send notifications for secondary destinations
        for destination in routing_decision.secondary_destinations:
            if destination == RoutingDestination.WEBSOCKET_SERVICE:
                self.notify_websocket_service(message, conversation, routing_decision)
    
    def _get_notification_recipients(
        self,
        conversation: Conversation,
        message: Message
    ) -> List[str]:
        """Get list of user IDs who should receive notifications."""
        recipients = []
        
        # Get all active user participants except the sender
        for participant in conversation.get_user_participants():
            if participant.participant_id != message.sender_id:
                recipients.append(participant.participant_id)
        
        return recipients


# Create singleton instance
message_orchestrator = MessageOrchestrator()
