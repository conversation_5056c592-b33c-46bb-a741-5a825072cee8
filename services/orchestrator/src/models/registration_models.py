#!/usr/bin/env python3
# services/orchestrator/src/models/registration_models.py
# Registration data models for orchestration

"""
Registration data models for the orchestration service.
Defines the data structures used throughout the registration flow.
"""

from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class RegistrationState(Enum):
    """Registration state enumeration."""
    INITIATED = "INITIATED"
    TENANT_CREATED = "TENANT_CREATED"
    USER_CREATED = "USER_CREATED"
    EMAIL_SENT = "EMAIL_SENT"
    EMAIL_VERIFIED = "EMAIL_VERIFIED"
    PLAN_SELECTED = "PLAN_SELECTED"
    PAYMENT_PROCESSING = "PAYMENT_PROCESSING"
    PAYMENT_COMPLETED = "PAYMENT_COMPLETED"
    CONFIGURING = "CONFIGURING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    ABANDONED = "ABANDONED"


@dataclass
class CompanyData:
    """Company information for tenant creation."""
    company_name: str
    industry: str
    company_size: str
    country: str
    address: str
    phone: str
    website: Optional[str] = None
    tax_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class UserData:
    """User information for master user creation."""
    email: str
    password: str
    first_name: str
    last_name: str
    phone: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding password for logging."""
        data = asdict(self)
        # Remove password for security
        data.pop('password', None)
        return data


@dataclass
class RegistrationRequest:
    """Complete registration request data."""
    company_data: CompanyData
    user_data: UserData
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RegistrationRequest':
        """Create from dictionary."""
        return cls(
            company_data=CompanyData(**data['company_data']),
            user_data=UserData(**data['user_data'])
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'company_data': self.company_data.to_dict(),
            'user_data': self.user_data.to_dict()
        }


@dataclass
class RegistrationRecord:
    """Registration record stored in DynamoDB."""
    registration_id: str
    state: RegistrationState
    email: str
    created_at: datetime
    updated_at: datetime
    expires_at: datetime
    
    # Optional fields populated during flow
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None
    subscription_id: Optional[str] = None
    plan_id: Optional[str] = None
    verification_token: Optional[str] = None
    error_message: Optional[str] = None
    
    # Original request data
    company_data: Optional[Dict[str, Any]] = None
    user_data: Optional[Dict[str, Any]] = None
    
    # Audit trail
    state_history: Optional[List[Dict[str, Any]]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for DynamoDB storage."""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['expires_at'] = self.expires_at.isoformat()
        data['state'] = self.state.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RegistrationRecord':
        """Create from dictionary (DynamoDB response)."""
        # Convert ISO strings back to datetime objects
        created_at = datetime.fromisoformat(data['created_at'])
        updated_at = datetime.fromisoformat(data['updated_at'])
        expires_at = datetime.fromisoformat(data['expires_at'])
        state = RegistrationState(data['state'])
        
        return cls(
            registration_id=data['registration_id'],
            state=state,
            email=data['email'],
            created_at=created_at,
            updated_at=updated_at,
            expires_at=expires_at,
            tenant_id=data.get('tenant_id'),
            user_id=data.get('user_id'),
            subscription_id=data.get('subscription_id'),
            plan_id=data.get('plan_id'),
            verification_token=data.get('verification_token'),
            error_message=data.get('error_message'),
            company_data=data.get('company_data'),
            user_data=data.get('user_data'),
            state_history=data.get('state_history', [])
        )
    
    def add_state_transition(self, from_state: RegistrationState, to_state: RegistrationState, 
                           details: Optional[Dict[str, Any]] = None) -> None:
        """Add state transition to history."""
        if self.state_history is None:
            self.state_history = []
        
        transition = {
            'timestamp': datetime.utcnow().isoformat(),
            'from_state': from_state.value,
            'to_state': to_state.value,
            'details': details or {}
        }
        self.state_history.append(transition)


@dataclass
class PaymentData:
    """Payment information for subscription creation."""
    plan_id: str
    billing_interval: str  # MONTHLY or ANNUAL
    payment_method_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class RegistrationResponse:
    """Response data for registration operations."""
    success: bool
    registration_id: str
    state: RegistrationState
    message: str
    data: Optional[Dict[str, Any]] = None
    next_steps: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'success': self.success,
            'registration_id': self.registration_id,
            'state': self.state.value,
            'message': self.message,
            'data': self.data,
            'next_steps': self.next_steps
        }


# State transition rules
STATE_TRANSITIONS = {
    RegistrationState.INITIATED: [RegistrationState.TENANT_CREATED, RegistrationState.FAILED],
    RegistrationState.TENANT_CREATED: [RegistrationState.USER_CREATED, RegistrationState.FAILED],
    RegistrationState.USER_CREATED: [RegistrationState.EMAIL_SENT, RegistrationState.FAILED],
    RegistrationState.EMAIL_SENT: [RegistrationState.EMAIL_VERIFIED, RegistrationState.ABANDONED],
    RegistrationState.EMAIL_VERIFIED: [RegistrationState.PLAN_SELECTED, RegistrationState.ABANDONED],
    RegistrationState.PLAN_SELECTED: [RegistrationState.PAYMENT_PROCESSING, RegistrationState.ABANDONED],
    RegistrationState.PAYMENT_PROCESSING: [RegistrationState.PAYMENT_COMPLETED, RegistrationState.FAILED],
    RegistrationState.PAYMENT_COMPLETED: [RegistrationState.CONFIGURING],
    RegistrationState.CONFIGURING: [RegistrationState.COMPLETED, RegistrationState.FAILED],
    RegistrationState.COMPLETED: [],
    RegistrationState.FAILED: [],
    RegistrationState.ABANDONED: []
}


def is_valid_transition(from_state: RegistrationState, to_state: RegistrationState) -> bool:
    """Check if state transition is valid."""
    return to_state in STATE_TRANSITIONS.get(from_state, [])


def get_next_steps(state: RegistrationState) -> List[str]:
    """Get next steps for user based on current state."""
    steps_map = {
        RegistrationState.INITIATED: [
            "Registration process is starting",
            "Please wait while we create your account"
        ],
        RegistrationState.TENANT_CREATED: [
            "Company account created successfully",
            "Creating master user account"
        ],
        RegistrationState.USER_CREATED: [
            "User account created successfully",
            "Sending verification email"
        ],
        RegistrationState.EMAIL_SENT: [
            "Check your email for verification link",
            "Click the verification link to continue",
            "Email verification expires in 1 hour"
        ],
        RegistrationState.EMAIL_VERIFIED: [
            "Email verified successfully",
            "Select your subscription plan to continue"
        ],
        RegistrationState.PLAN_SELECTED: [
            "Plan selected successfully",
            "Proceed to payment to complete registration"
        ],
        RegistrationState.PAYMENT_PROCESSING: [
            "Processing payment",
            "Please wait while we confirm your payment"
        ],
        RegistrationState.PAYMENT_COMPLETED: [
            "Payment successful",
            "Setting up your account resources"
        ],
        RegistrationState.CONFIGURING: [
            "Configuring your account",
            "Setting up databases and storage",
            "Almost ready!"
        ],
        RegistrationState.COMPLETED: [
            "Registration completed successfully",
            "You can now log in to your account"
        ],
        RegistrationState.FAILED: [
            "Registration failed",
            "Please contact support for assistance"
        ],
        RegistrationState.ABANDONED: [
            "Registration was abandoned",
            "You can start a new registration anytime"
        ]
    }
    
    return steps_map.get(state, [])
