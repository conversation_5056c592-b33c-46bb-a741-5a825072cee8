# services/orchestrator/src/handlers/process_message.py
# Handler for processing and routing messages through the orchestrator

"""
Message Processing Handler

This handler is the main entry point for all messages in the system.
It receives messages from any source and routes them to the appropriate services.

POST /orchestrator/messages/process
{
    "message": {
        "message_id": "msg-123",
        "conversation_id": "conv-456",
        "tenant_id": "tenant-789",
        "sender_id": "user-123",
        "sender_type": "user",
        "sender_name": "John Doe",
        "message_type": "text",
        "content": "Hello, I need help with logistics",
        "direction": "inbound"
    },
    "conversation": {
        "conversation_id": "conv-456",
        "conversation_type": "user_to_agent",
        "tenant_id": "tenant-789",
        "agent_id": "feedo",
        "agent_type": "feedo"
    }
}
"""

from typing import Dict, Any
from datetime import datetime, timezone

from shared.logger import lambda_logger, log_business_operation
from shared.responses import APIResponse
from shared.auth import require_auth
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.decorators import measure_performance
from shared.exceptions import ValidationException, BusinessLogicException
from shared.validation import validate_required_fields

from ..services.message_orchestrator import message_orchestrator


@require_auth
@rate_limit(requests_per_minute=300)  # High limit for message processing
@user_resilience("process_message")
@measure_performance("orchestrator_process_message")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process and route a message through the orchestrator.
    
    This is the central entry point for all messages in the system.
    """
    
    try:
        # Extract auth context
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)
        
        # Parse request body
        body_str = event.get('body')
        if not body_str:
            return APIResponse.error("Request body is required", 400)
        
        try:
            body = json.loads(body_str)
        except json.JSONDecodeError:
            return APIResponse.error("Invalid JSON in request body", 400)
        
        # Validate required fields
        validate_required_fields(body, ['message'])
        
        message_data = body['message']
        conversation_data = body.get('conversation')
        
        # Validate message data
        validate_required_fields(message_data, [
            'message_id', 'conversation_id', 'tenant_id', 'sender_id', 
            'sender_type', 'sender_name', 'message_type', 'content'
        ])
        
        # Validate tenant access
        if auth_context.tenant_id != message_data['tenant_id']:
            return APIResponse.error("Access denied to tenant", 403)
        
        # Log message processing start
        log_business_operation(
            lambda_logger,
            operation="process_message_start",
            entity_type="message",
            entity_id=message_data['message_id'],
            tenant_id=message_data['tenant_id'],
            user_id=auth_context.user_id,
            status="started",
            details={
                'conversation_id': message_data['conversation_id'],
                'sender_id': message_data['sender_id'],
                'sender_type': message_data['sender_type'],
                'message_type': message_data['message_type'],
                'content_length': len(message_data['content']),
                'has_conversation_data': conversation_data is not None
            }
        )
        
        # Process message through orchestrator
        success, result, error_msg = message_orchestrator.process_message(
            message_data=message_data,
            conversation_data=conversation_data
        )
        
        if success:
            # Log successful processing
            log_business_operation(
                lambda_logger,
                operation="process_message_completed",
                entity_type="message",
                entity_id=message_data['message_id'],
                tenant_id=message_data['tenant_id'],
                user_id=auth_context.user_id,
                status="completed",
                details={
                    'conversation_id': result.get('conversation_id'),
                    'routing_destination': result.get('routing_decision', {}).get('primary_destination'),
                    'routing_strategy': result.get('routing_decision', {}).get('strategy'),
                    'processing_time_ms': 0  # Will be calculated by decorator
                }
            )
            
            return APIResponse.success({
                'message': "Message processed successfully",
                'result': result
            }, 200)
        else:
            # Log processing failure
            log_business_operation(
                lambda_logger,
                operation="process_message_failed",
                entity_type="message",
                entity_id=message_data['message_id'],
                tenant_id=message_data['tenant_id'],
                user_id=auth_context.user_id,
                status="failed",
                details={
                    'error': error_msg,
                    'conversation_id': message_data['conversation_id']
                }
            )
            
            return APIResponse.error(f"Message processing failed: {error_msg}", 500)
    
    except ValidationException as e:
        lambda_logger.warning(f"Validation error in process_message: {str(e)}")
        return APIResponse.error(str(e), 400)
    
    except BusinessLogicException as e:
        lambda_logger.warning(f"Business logic error in process_message: {str(e)}")
        return APIResponse.error(str(e), 422)
    
    except Exception as e:
        lambda_logger.error(f"Unexpected error in process_message: {str(e)}", extra={
            'event': event,
            'error_type': type(e).__name__
        })
        return APIResponse.error("Internal server error", 500)
