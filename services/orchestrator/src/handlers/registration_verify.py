#!/usr/bin/env python3
# services/orchestrator/src/handlers/registration_verify.py
# Registration email verification handler

"""
Registration email verification handler.
Handles email verification step in the orchestrated registration flow.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException
from shared.validators import validate_required_fields

from ..services.orchestration_service import OrchestrationService


def validate_verify_request(data: Dict[str, Any]) -> None:
    """Validate email verification request data."""
    validate_required_fields(data, ['registration_id', 'verification_token'])
    
    registration_id = data.get('registration_id', '')
    verification_token = data.get('verification_token', '')
    
    if not registration_id.startswith('reg_'):
        raise ValidationException("Invalid registration ID format")
    
    if len(verification_token) < 10:
        raise ValidationException("Invalid verification token")


@rate_limit(requests_per_minute=10)  # Moderate rate limiting for verification
@measure_performance("orchestrator_registration_verify")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Verify email and advance registration.
    
    POST /registration/verify
    {
        "registration_id": "reg_abc123def456",
        "verification_token": "verify_token_xyz789"
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "registration_verify")
        
        # Parse request body
        body = event.get('body', '{}')
        if isinstance(body, str):
            data = json.loads(body)
        else:
            data = body
        
        registration_id = data.get('registration_id')
        verification_token = data.get('verification_token')
        
        lambda_logger.info("Processing email verification", extra={
            'request_id': request_id,
            'registration_id': registration_id
        })
        
        # Validate request
        validate_verify_request(data)
        
        # Verify email through orchestration service
        orchestration_service = OrchestrationService()
        registration = orchestration_service.verify_email(registration_id, verification_token)
        
        # Get available plans for next step
        from ..utils.service_clients import ServiceClientFactory
        payment_client = ServiceClientFactory.get_payment_client()
        plans_result = payment_client.get_plans()
        
        # Prepare response data
        response_data = {
            'registration_id': registration.registration_id,
            'state': registration.state.value,
            'tenant_id': registration.tenant_id,
            'user_id': registration.user_id,
            'updated_at': registration.updated_at.isoformat(),
            'available_plans': plans_result.get('data', []),
            'next_steps': [
                "Email verified successfully",
                "Select your subscription plan below",
                "Complete payment to finish registration"
            ]
        }
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Email verified successfully"
        )
        
        # Log API response
        log_api_response(response, "registration_verify")
        
        lambda_logger.info("Email verification completed", extra={
            'request_id': request_id,
            'registration_id': registration_id,
            'state': registration.state.value
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Email verification validation failed", extra={
            'request_id': request_id,
            'registration_id': data.get('registration_id') if 'data' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "registration_verify")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Email verification platform error", extra={
            'request_id': request_id,
            'registration_id': data.get('registration_id') if 'data' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Email verification failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "registration_verify")
        return response
        
    except Exception as e:
        lambda_logger.error("Email verification unexpected error", extra={
            'request_id': request_id,
            'registration_id': data.get('registration_id') if 'data' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "registration_verify")
        return response
