#!/usr/bin/env python3
# services/orchestrator/src/handlers/registration_status.py
# Registration status handler

"""
Registration status handler.
Provides status information for ongoing registrations.
"""

from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException

from ..services.orchestration_service import OrchestrationService


@rate_limit(requests_per_minute=30)  # Higher limit for status checks
@measure_performance("orchestrator_registration_status")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get registration status.
    
    GET /registration/status/{registration_id}
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "registration_status")
        
        # Extract registration_id from path parameters
        path_parameters = event.get('pathParameters', {})
        registration_id = path_parameters.get('registration_id')
        
        if not registration_id:
            raise ValidationException("Registration ID is required")
        
        if not registration_id.startswith('reg_'):
            raise ValidationException("Invalid registration ID format")
        
        lambda_logger.info("Getting registration status", extra={
            'request_id': request_id,
            'registration_id': registration_id
        })
        
        # Get status through orchestration service
        orchestration_service = OrchestrationService()
        status_data = orchestration_service.get_registration_status(registration_id)
        
        # Create success response
        response = APIResponse.success(
            data=status_data,
            message="Registration status retrieved successfully"
        )
        
        # Log API response
        log_api_response(response, "registration_status")
        
        lambda_logger.info("Registration status retrieved", extra={
            'request_id': request_id,
            'registration_id': registration_id,
            'state': status_data.get('state')
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Registration status validation failed", extra={
            'request_id': request_id,
            'registration_id': registration_id if 'registration_id' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "registration_status")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Registration status platform error", extra={
            'request_id': request_id,
            'registration_id': registration_id if 'registration_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to get registration status",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "registration_status")
        return response
        
    except Exception as e:
        lambda_logger.error("Registration status unexpected error", extra={
            'request_id': request_id,
            'registration_id': registration_id if 'registration_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "registration_status")
        return response
