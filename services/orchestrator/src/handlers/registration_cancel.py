#!/usr/bin/env python3
# services/orchestrator/src/handlers/registration_cancel.py
# Registration cancellation handler

"""
Registration cancellation handler.
Handles cancellation and cleanup of ongoing registrations.
"""

from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException

from ..services.orchestration_service import OrchestrationService


@rate_limit(requests_per_minute=10)  # Moderate rate limiting for cancellation
@measure_performance("orchestrator_registration_cancel")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Cancel registration and cleanup resources.
    
    DELETE /registration/{registration_id}
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "registration_cancel")
        
        # Extract registration_id from path parameters
        path_parameters = event.get('pathParameters', {})
        registration_id = path_parameters.get('registration_id')
        
        if not registration_id:
            raise ValidationException("Registration ID is required")
        
        if not registration_id.startswith('reg_'):
            raise ValidationException("Invalid registration ID format")
        
        lambda_logger.info("Cancelling registration", extra={
            'request_id': request_id,
            'registration_id': registration_id
        })
        
        # Cancel registration through orchestration service
        orchestration_service = OrchestrationService()
        orchestration_service.cancel_registration(registration_id)
        
        # Prepare response data
        response_data = {
            'registration_id': registration_id,
            'cancelled': True,
            'message': 'Registration cancelled and resources cleaned up'
        }
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Registration cancelled successfully"
        )
        
        # Log API response
        log_api_response(response, "registration_cancel")
        
        lambda_logger.info("Registration cancelled successfully", extra={
            'request_id': request_id,
            'registration_id': registration_id
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Registration cancellation validation failed", extra={
            'request_id': request_id,
            'registration_id': registration_id if 'registration_id' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "registration_cancel")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Registration cancellation platform error", extra={
            'request_id': request_id,
            'registration_id': registration_id if 'registration_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to cancel registration",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "registration_cancel")
        return response
        
    except Exception as e:
        lambda_logger.error("Registration cancellation unexpected error", extra={
            'request_id': request_id,
            'registration_id': registration_id if 'registration_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "registration_cancel")
        return response
