#!/usr/bin/env python3
# services/orchestrator/src/handlers/registration_payment.py
# Registration payment processing handler

"""
Registration payment processing handler.
Handles payment processing and completes the registration flow.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException, AuthenticationException
from shared.validators import validate_required_fields

from ..models.registration_models import PaymentData
from ..services.orchestration_service import OrchestrationService


def validate_payment_request(data: Dict[str, Any]) -> None:
    """Validate payment request data."""
    validate_required_fields(data, ['registration_id', 'plan_id', 'billing_interval'])
    
    registration_id = data.get('registration_id', '')
    plan_id = data.get('plan_id', '')
    billing_interval = data.get('billing_interval', '')
    
    if not registration_id.startswith('reg_'):
        raise ValidationException("Invalid registration ID format")
    
    if not plan_id.startswith('plan_'):
        raise ValidationException("Invalid plan ID format")
    
    if billing_interval not in ['MONTHLY', 'ANNUAL']:
        raise ValidationException("Billing interval must be MONTHLY or ANNUAL")


def extract_auth_token(event: Dict[str, Any]) -> str:
    """Extract auth token from request headers."""
    headers = event.get('headers', {})
    
    # Check Authorization header
    auth_header = headers.get('Authorization') or headers.get('authorization')
    if auth_header and auth_header.startswith('Bearer '):
        return auth_header[7:]  # Remove 'Bearer ' prefix
    
    raise AuthenticationException("Authorization token required")


@rate_limit(requests_per_minute=5)  # Strict rate limiting for payment
@measure_performance("orchestrator_registration_payment")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Process payment and complete registration.
    
    POST /registration/payment
    Authorization: Bearer <auth_token>
    {
        "registration_id": "reg_abc123def456",
        "plan_id": "plan_pro",
        "billing_interval": "MONTHLY",
        "payment_method_id": "pm_1234567890"
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "registration_payment")
        
        # Extract auth token
        auth_token = extract_auth_token(event)
        
        # Parse request body
        body = event.get('body', '{}')
        if isinstance(body, str):
            data = json.loads(body)
        else:
            data = body
        
        registration_id = data.get('registration_id')
        plan_id = data.get('plan_id')
        
        lambda_logger.info("Processing registration payment", extra={
            'request_id': request_id,
            'registration_id': registration_id,
            'plan_id': plan_id
        })
        
        # Validate request
        validate_payment_request(data)
        
        # Create payment data object
        payment_data = PaymentData(
            plan_id=data['plan_id'],
            billing_interval=data['billing_interval'],
            payment_method_id=data.get('payment_method_id')
        )
        
        # Process payment through orchestration service
        orchestration_service = OrchestrationService()
        registration = orchestration_service.process_payment(
            registration_id, 
            payment_data, 
            auth_token
        )
        
        # Prepare response data
        response_data = {
            'registration_id': registration.registration_id,
            'state': registration.state.value,
            'tenant_id': registration.tenant_id,
            'user_id': registration.user_id,
            'subscription_id': registration.subscription_id,
            'plan_id': registration.plan_id,
            'updated_at': registration.updated_at.isoformat(),
            'next_steps': [
                "Registration completed successfully!",
                "You can now log in to your account",
                "Welcome to the platform!"
            ]
        }
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Registration completed successfully"
        )
        
        # Log API response
        log_api_response(response, "registration_payment")
        
        lambda_logger.info("Registration payment completed", extra={
            'request_id': request_id,
            'registration_id': registration_id,
            'state': registration.state.value,
            'subscription_id': registration.subscription_id
        })
        
        return response
        
    except AuthenticationException as e:
        lambda_logger.warning("Registration payment authentication failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Authentication required",
            details={'error': str(e)},
            status_code=401
        )
        log_api_response(response, "registration_payment")
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Registration payment validation failed", extra={
            'request_id': request_id,
            'registration_id': data.get('registration_id') if 'data' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "registration_payment")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Registration payment platform error", extra={
            'request_id': request_id,
            'registration_id': data.get('registration_id') if 'data' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Payment processing failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "registration_payment")
        return response
        
    except Exception as e:
        lambda_logger.error("Registration payment unexpected error", extra={
            'request_id': request_id,
            'registration_id': data.get('registration_id') if 'data' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "registration_payment")
        return response
