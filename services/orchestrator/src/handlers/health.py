#!/usr/bin/env python3
# services/orchestrator/src/handlers/health.py
# Health check handler

"""
Health check handler for the orchestrator service.
Provides service health status and basic diagnostics.
"""

import os
from typing import Any, Dict, Optional
from datetime import datetime

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger


@rate_limit(requests_per_minute=60)  # Higher limit for health checks
@measure_performance("orchestrator_health")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Health check endpoint.
    
    GET /health
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    try:
        # Check environment variables
        required_env_vars = [
            'REGISTRATIONS_TABLE',
            'AUTH_SERVICE_URL',
            'TENANT_SERVICE_URL',
            'PAYMENT_SERVICE_URL'
        ]
        
        env_status = {}
        all_env_ok = True
        
        for var in required_env_vars:
            value = os.environ.get(var)
            env_status[var] = {
                'configured': value is not None,
                'value': value[:20] + '...' if value and len(value) > 20 else value
            }
            if not value:
                all_env_ok = False
        
        # Check DynamoDB table access
        dynamodb_status = {'accessible': False, 'error': None}
        try:
            from ..utils.state_manager import StateManager
            state_manager = StateManager()
            # Try to get table description
            table_description = state_manager.table.meta.client.describe_table(
                TableName=state_manager.table_name
            )
            dynamodb_status['accessible'] = True
            dynamodb_status['table_status'] = table_description['Table']['TableStatus']
        except Exception as e:
            dynamodb_status['error'] = str(e)
        
        # Overall health status
        is_healthy = all_env_ok and dynamodb_status['accessible']
        
        # Prepare health data
        health_data = {
            'service': 'orchestrator',
            'status': 'healthy' if is_healthy else 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'environment': os.environ.get('STAGE', 'unknown'),
            'region': os.environ.get('REGION', 'unknown'),
            'checks': {
                'environment_variables': {
                    'status': 'pass' if all_env_ok else 'fail',
                    'details': env_status
                },
                'dynamodb': {
                    'status': 'pass' if dynamodb_status['accessible'] else 'fail',
                    'details': dynamodb_status
                }
            }
        }
        
        # Add context information if available
        if context:
            health_data['lambda'] = {
                'function_name': context.function_name,
                'function_version': context.function_version,
                'memory_limit': context.memory_limit_in_mb,
                'remaining_time': context.get_remaining_time_in_millis()
            }
        
        # Create response
        status_code = 200 if is_healthy else 503
        message = "Service is healthy" if is_healthy else "Service is unhealthy"
        
        response = APIResponse.success(
            data=health_data,
            message=message
        )
        
        # Override status code for unhealthy service
        if not is_healthy:
            response['statusCode'] = 503
        
        lambda_logger.info("Health check completed", extra={
            'status': health_data['status'],
            'all_env_ok': all_env_ok,
            'dynamodb_accessible': dynamodb_status['accessible']
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("Health check failed", extra={
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        # Return unhealthy status
        health_data = {
            'service': 'orchestrator',
            'status': 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }
        
        response = APIResponse.error(
            message="Health check failed",
            details=health_data,
            status_code=503
        )
        
        return response
