#!/usr/bin/env python3
# services/orchestrator/src/handlers/registration_complete.py
# Registration complete handler

"""
Registration complete handler for starting the orchestrated registration flow.
Handles the initial registration request and coordinates all services.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException
from shared.validators import validate_required_fields, validate_email_address
from shared.request_utils import parse_and_validate_request, get_request_context

from ..models.registration_models import RegistrationRequest, CompanyData, UserData
from ..services.orchestration_service import OrchestrationService


def validate_registration_request(data: Dict[str, Any]) -> None:
    """Validate registration request data."""
    # Validate required top-level fields
    validate_required_fields(data, ['company_data', 'user_data'])
    
    company_data = data.get('company_data', {})
    user_data = data.get('user_data', {})
    
    # Validate company data
    validate_required_fields(company_data, [
        'company_name', 'industry', 'company_size', 'country', 'address', 'phone'
    ])
    
    # Validate user data
    validate_required_fields(user_data, [
        'email', 'password', 'first_name', 'last_name', 'phone'
    ])
    
    # Additional validations
    if len(company_data.get('company_name', '')) < 2:
        raise ValidationException("Company name must be at least 2 characters")
    
    if len(user_data.get('password', '')) < 8:
        raise ValidationException("Password must be at least 8 characters")
    
    # Email format validation using shared validator
    email = user_data.get('email', '')
    try:
        validate_email_address(email)
    except ValidationException as e:
        raise ValidationException(f"Invalid email format: {str(e)}")


@rate_limit(requests_per_minute=5)  # Strict rate limiting for registration
@measure_performance("orchestrator_registration_complete")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Start complete registration process.
    
    POST /registration/complete
    {
        "company_data": {
            "company_name": "Test Company",
            "industry": "logistics",
            "company_size": "10-50",
            "country": "US",
            "address": "123 Main St",
            "phone": "+*********0",
            "website": "https://testcompany.com",
            "tax_id": "*********"
        },
        "user_data": {
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "first_name": "John",
            "last_name": "Doe",
            "phone": "+*********0"
        }
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    # Get request context
    request_context = get_request_context(event)
    request_id = request_context['request_id']

    try:
        # Log API request
        log_api_request(event, "registration_complete")

        # Parse request body using shared utility
        from shared.request_utils import parse_request_body
        data = parse_request_body(event)
        
        lambda_logger.info("Processing registration request", extra={
            'request_id': request_id,
            'company_name': data.get('company_data', {}).get('company_name'),
            'email': data.get('user_data', {}).get('email')
        })
        
        # Validate request
        validate_registration_request(data)
        
        # Create registration request object
        registration_request = RegistrationRequest.from_dict(data)
        
        # Start orchestration
        orchestration_service = OrchestrationService()
        registration = orchestration_service.start_registration(registration_request)
        
        # Prepare response data
        response_data = {
            'registration_id': registration.registration_id,
            'state': registration.state.value,
            'tenant_id': registration.tenant_id,
            'user_id': registration.user_id,
            'expires_at': registration.expires_at.isoformat(),
            'next_steps': [
                "Check your email for verification link",
                "Click the verification link to continue",
                "Select your subscription plan"
            ]
        }
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Registration process initiated successfully"
        )
        
        # Log API response
        log_api_response(response, "registration_complete")
        
        lambda_logger.info("Registration initiated successfully", extra={
            'request_id': request_id,
            'registration_id': registration.registration_id,
            'state': registration.state.value,
            'tenant_id': registration.tenant_id
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Registration validation failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "registration_complete")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Registration platform error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Registration failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "registration_complete")
        return response
        
    except Exception as e:
        lambda_logger.error("Registration unexpected error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "registration_complete")
        return response
