sequenceDiagram
    participant Source as 📱 Message Source
    participant API as 🌐 Orchestrator API
    participant PMH as 📨 Process Message Handler
    participant MO as 🎯 Message Orchestrator
    participant RE as 🔀 Routing Engine
    participant DB as 🗄️ DynamoDB
    participant RM as 🔄 Retry Manager
    participant CS as 💬 Chat Service
    participant AS as 🤖 Agent Service
    participant WS as 🔌 WebSocket Service
    participant CW as 📊 CloudWatch

    Note over Source: User sends message via UI
    Source->>API: POST /orchestrator/messages/process
    Note over Source,API: {message: {...}, conversation: {...}, routing_hint: "auto"}
    
    API->>PMH: Route to Process Message Handler
    PMH->>PMH: Validate message structure
    PMH->>PMH: Extract routing context
    
    PMH->>MO: process_message(message_data, conversation_data)
    MO->>MO: Generate correlation_id for tracking
    
    Note over MO: Determine routing strategy
    MO->>RE: determine_routing_strategy(message, conversation)
    RE->>RE: Analyze conversation type and content
    RE->>DB: Query routing history and patterns
    Note over DB: Query message routing analytics
    DB-->>RE: Historical routing data
    
    RE->>RE: Apply routing algorithms
    Note over RE: Content analysis + ML predictions + load balancing
    RE-->>MO: Routing decision: [chat_service, agent_service, websocket_service]
    
    MO->>DB: Store routing decision
    Note over DB: PK: MESSAGE_ROUTE#{message_id}<br/>routing_decision: [...]<br/>correlation_id: req-xyz
    
    Note over MO: Execute parallel routing
    
    par Chat Service
        MO->>CS: POST /chat/messages/process
        Note over MO,CS: {message: {...}, conversation: {...}, source: "orchestrator"}
        CS->>CS: Process and store message
        CS->>CS: Update search indexes
        CS-->>MO: {status: "processed", message_id: "msg-123"}
    and Agent Service
        MO->>AS: POST /agent/messages/process
        Note over MO,AS: {message: {...}, conversation: {...}, agent_type: "auto"}
        AS->>AS: Determine agent routing
        AS->>AS: Route to appropriate agent (Feedo/Forecaster)
        AS-->>MO: {status: "processing", agent_id: "forecaster-001"}
    and WebSocket Service
        MO->>WS: POST /websocket/orchestrator/broadcast
        Note over MO,WS: {recipients: [...], notification: {...}}
        WS->>WS: Get active connections
        WS->>WS: Broadcast to connected users
        WS-->>MO: {delivered: 2, failed: 0}
    end
    
    Note over MO: Aggregate results
    MO->>MO: Compile routing results
    MO->>DB: Update routing record with results
    Note over DB: successful_routes: 3<br/>failed_routes: 0<br/>total_latency: 450ms
    
    MO->>CW: Log routing metrics
    Note over CW: Custom metrics:<br/>- routing_success_rate<br/>- routing_latency<br/>- service_availability
    
    MO-->>PMH: All services coordinated successfully
    PMH-->>API: 200 OK
    Note over PMH,API: {status: "success", routed_to: [...], correlation_id: "req-xyz"}
    API-->>Source: Routing confirmation
    
    rect rgb(255, 245, 245)
        Note over MO: Handle routing failures (if any)
        Note over RM: Retry Manager monitors for failures
        RM->>RM: Detect service failure
        RM->>MO: Trigger retry for failed service
        
        MO->>RM: retry_failed_routing(message, failed_service)
        RM->>RM: Apply exponential backoff
        RM->>CS: Retry POST /chat/messages/process
        CS-->>RM: Success on retry
        
        RM->>DB: Update routing record
        RM->>CW: Log retry metrics
        RM-->>MO: Retry successful
    end
    
    rect rgb(245, 255, 245)
        Note over AS: Agent responds asynchronously
        AS->>CS: POST /chat/agent-responses
        Note over AS,CS: {agent_response: {...}, conversation_context: {...}}
        CS->>CS: Process agent response
        CS->>WS: Coordinate real-time delivery
        WS->>WS: Broadcast agent response
        
        Note over MO: Complete message lifecycle
        MO->>DB: Mark message routing as completed
        MO->>CW: Log completion metrics
    end
    
    Note over Source,CW: Message coordination completed successfully