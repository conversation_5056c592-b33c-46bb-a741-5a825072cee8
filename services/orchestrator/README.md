# 🎯 **Orchestrator Service - Agent-SCL Platform**

## **🚀 Descripción General**

El Orchestrator Service es el coordinador central del ecosistema **agent-scl**. Proporciona infraestructura completa para coordinación de flujo de registro, routing inteligente de mensajes, gestión de estado, integración entre servicios, manejo de errores y auditoría centralizada.

### **✨ Características Principales**
- 🎯 **Coordinación Central**: Punto único de coordinación para todo el sistema
- 🔄 **Gestión de Estado**: State machine robusto para registros
- 📨 **Routing Inteligente**: Coordinación de mensajes entre servicios
- 🔗 **Integración de Servicios**: Coordinación de todos los servicios del ecosistema
- ⚡ **Resilencia**: Circuit breakers y manejo robusto de fallos
- 📊 **Observabilidad**: Logging y métricas centralizadas
- 🔐 **Seguridad**: Autenticación y audit trail completo
- 🎛️ **Control de Flujo**: Orquestación de procesos complejos

---

## **📋 Tabla de Contenidos**
1. [Instalación y Setup](#instalación-y-setup)
2. [Configuración](#configuración)
3. [API Reference](#api-reference)
4. [Flujo de Registro](#flujo-de-registro)
5. [Coordinación de Mensajes](#coordinación-de-mensajes)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)

---

## **🛠️ Instalación y Setup**

### **Prerrequisitos**
```bash
# Servicios coordinados (deben estar desplegados)
✅ agent-scl-shared-layer-dev
✅ agent-scl-auth-dev
✅ agent-scl-tenant-dev
✅ agent-scl-payment-dev
✅ agent-scl-chat-dev
✅ agent-scl-agent-dev
✅ agent-scl-websocket-dev

# Herramientas de desarrollo
- Python 3.11+
- Serverless Framework 3.x
- AWS CLI configurado
- Node.js 18+ (para Serverless)
```

### **Instalación Local**
```bash
# 1. Clonar y navegar al directorio
cd services/orchestrator

# 2. Instalar dependencias Python
pip install -r requirements.txt

# 3. Instalar Serverless plugins
npm install

# 4. Configurar variables de entorno
export STAGE=dev
export REGION=us-east-1
```

### **Configuración de Servicios**
```bash
# JWT Secret (ya debe existir del auth service)
aws secretsmanager describe-secret --secret-id "agent-scl/dev/jwt-secret"

# Verificar tabla DynamoDB
aws dynamodb describe-table --table-name "agent-scl-dev"

# Verificar SES para emails
aws ses get-identity-verification-attributes --identities <EMAIL>
```

---

## **🏗️ Arquitectura y State Machine**

### **State Machine de Registro**
```mermaid
stateDiagram-v2
    [*] --> INITIATED
    INITIATED --> TENANT_CREATED : create_tenant()
    TENANT_CREATED --> USER_CREATED : create_master_user()
    USER_CREATED --> EMAIL_SENT : send_verification_email()
    EMAIL_SENT --> EMAIL_VERIFIED : verify_email()
    EMAIL_VERIFIED --> PLAN_SELECTED : select_plan()
    PLAN_SELECTED --> PAYMENT_PROCESSING : process_payment()
    PAYMENT_PROCESSING --> PAYMENT_COMPLETED : payment_success()
    PAYMENT_COMPLETED --> CONFIGURING : configure_resources()
    CONFIGURING --> COMPLETED : activation_complete()

    INITIATED --> FAILED : validation_error()
    TENANT_CREATED --> FAILED : tenant_creation_error()
    USER_CREATED --> FAILED : user_creation_error()
    EMAIL_SENT --> ABANDONED : email_timeout()
    PAYMENT_PROCESSING --> FAILED : payment_error()

    FAILED --> [*] : cleanup()
    ABANDONED --> [*] : cleanup()
    COMPLETED --> [*]
```

---

## **⚙️ Configuración**

### **Variables de Entorno**
```yaml
# Core Configuration
STAGE: dev                           # Ambiente de deployment
REGION: us-east-1                   # Región AWS
PROJECT_NAME: agent-scl             # Nombre del proyecto
DYNAMODB_TABLE: agent-scl-dev       # Tabla DynamoDB unificada

# Security
JWT_SECRET_NAME: agent-scl/dev/jwt-secret  # Secret en AWS Secrets Manager

# Service URLs (for coordination)
CHAT_SERVICE_URL: https://api.agent-scl.com/dev
AGENT_SERVICE_URL: https://api.agent-scl.com/dev
WEBSOCKET_SERVICE_URL: https://api.agent-scl.com/dev
AUTH_SERVICE_URL: https://api.agent-scl.com/dev
TENANT_SERVICE_URL: https://api.agent-scl.com/dev
PAYMENT_SERVICE_URL: https://api.agent-scl.com/dev

# Registration Configuration
REGISTRATION_TTL_HOURS: 24
EMAIL_VERIFICATION_TTL_HOURS: 1
MAX_REGISTRATION_ATTEMPTS: 3

# Message Routing Configuration
MAX_ROUTING_RETRIES: 3
ROUTING_TIMEOUT_SECONDS: 30
CIRCUIT_BREAKER_THRESHOLD: 5
```

---

## **📡 API Reference**

### **Registration Flow Endpoints**

#### **Complete Registration**
```http
POST /orchestrator/registration/complete
Content-Type: application/json

{
    "company_data": {
        "company_name": "Test Company",
        "industry": "logistics",
        "company_size": "10-50",
        "country": "US",
        "address": "123 Main St",
        "phone": "+**********",
        "website": "https://testcompany.com"
    },
    "user_data": {
        "email": "<EMAIL>",
        "password": "SecurePassword123!",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+**********"
    }
}

Response:
{
    "success": true,
    "data": {
        "registration_id": "reg_abc123def456",
        "state": "EMAIL_SENT",
        "tenant_id": "tnt_xyz789",
        "user_id": "usr_master_123",
        "expires_at": "2025-08-14T20:30:00Z",
        "next_steps": [
            "Check your email for verification link",
            "Click the verification link to continue"
        ]
    }
}
```

#### **Verify Email**
```http
POST /orchestrator/registration/verify
Content-Type: application/json

{
    "registration_id": "reg_abc123def456",
    "verification_token": "verify_token_xyz789"
}

Response:
{
    "success": true,
    "data": {
        "registration_id": "reg_abc123def456",
        "state": "EMAIL_VERIFIED",
        "next_steps": [
            "Select your plan",
            "Complete payment to activate account"
        ],
        "redirect_url": "/select-plan"
    }
}
```

#### **Process Payment**
```http
POST /orchestrator/registration/payment
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
    "registration_id": "reg_abc123def456",
    "plan_id": "plan_pro",
    "billing_interval": "MONTHLY",
    "payment_method_id": "pm_**********"
}

Response:
{
    "success": true,
    "data": {
        "registration_id": "reg_abc123def456",
        "state": "COMPLETED",
        "transaction_id": "txn_789abc",
        "login_url": "/login",
        "welcome_message": "Welcome to Agent-SCL!"
    }
}
```

#### **Get Registration Status**
```http
GET /orchestrator/registration/status/{registrationId}

Response:
{
    "success": true,
    "data": {
        "registration_id": "reg_abc123def456",
        "state": "EMAIL_VERIFIED",
        "progress": 60,
        "created_at": "2024-01-15T10:00:00Z",
        "expires_at": "2024-01-16T10:00:00Z",
        "next_steps": [
            "Select your plan",
            "Complete payment"
        ]
    }
}
```

### **Message Coordination Endpoints**

#### **Process Message**
```http
POST /orchestrator/messages/process
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
    "message": {
        "message_id": "msg-123",
        "conversation_id": "conv-456",
        "sender_id": "user-789",
        "content": "Hello, I need help with logistics",
        "message_type": "text"
    },
    "conversation": {
        "conversation_id": "conv-456",
        "conversation_type": "user_to_agent",
        "tenant_id": "tenant-123",
        "participants": ["user-789"]
    },
    "routing_hint": "auto"
}

Response:
{
    "success": true,
    "data": {
        "status": "success",
        "routed_to": ["chat_service", "agent_service", "websocket_service"],
        "correlation_id": "req-xyz789",
        "routing_latency_ms": 450
    }
}
```

### **System Monitoring Endpoints**

#### **Health Check**
```http
GET /orchestrator/health

Response:
{
    "status": "healthy",
    "services": {
        "chat_service": {
            "status": "healthy",
            "response_time_ms": 120
        },
        "agent_service": {
            "status": "healthy",
            "response_time_ms": 200
        },
        "websocket_service": {
            "status": "healthy",
            "response_time_ms": 80
        }
    },
    "system_health_score": 0.95
}
```

#### **System Status**
```http
GET /orchestrator/status

Response:
{
    "system": {
        "status": "operational",
        "uptime_hours": 168,
        "version": "1.0.0"
    },
    "metrics": {
        "registrations_today": 45,
        "messages_routed_today": 1250,
        "success_rate": 0.98,
        "avg_response_time_ms": 350
    }
}
```

### **DELETE /registration/{registration_id}**
Cancel registration and cleanup resources.

### **GET /health**
Service health check.

## 🗄️ **Data Models**

### **RegistrationRecord**
```python
{
  "registration_id": "reg_abc123def456",
  "state": "EMAIL_SENT",
  "email": "<EMAIL>",
  "created_at": "2025-08-14T19:30:00Z",
  "updated_at": "2025-08-14T19:31:00Z",
  "expires_at": "2025-08-14T20:30:00Z",
  "tenant_id": "tnt_xyz789",
  "user_id": "usr_master_123",
  "subscription_id": null,
  "plan_id": null,
  "company_data": {...},
  "user_data": {...},
  "state_history": [...]
}
```

## 🔗 **Service Dependencies**

### **Auth Service**
- `POST /auth/register` - Create master user
- `POST /auth/resend-verification` - Send verification email
- `POST /auth/verify-email` - Verify email token

### **Tenant Service**
- `POST /tenant/register` - Create tenant
- `GET /tenant/profile` - Get tenant information

### **Payment Service**
- `GET /payment/plans` - Get available plans
- `POST /payment/subscriptions` - Create subscription

### **Setup Service** (Future)
- `POST /setup/tenant/{tenant_id}` - Configure tenant resources

## 🚀 **Deployment**

### **Environment Variables**
```bash
# Service endpoints
AUTH_SERVICE_URL=https://api.platform.com/auth
TENANT_SERVICE_URL=https://api.platform.com/tenant
PAYMENT_SERVICE_URL=https://api.platform.com/payment

# DynamoDB
REGISTRATIONS_TABLE=agent-scl-registrations-dev

# Email
EMAIL_FROM=<EMAIL>

# AWS
STAGE=dev
REGION=us-east-1
PROJECT_NAME=agent-scl
```

### **Deploy Commands**
```bash
# Install dependencies
npm install

# Deploy to dev
serverless deploy --stage dev

# Deploy to production
serverless deploy --stage prod
```

## 🧪 **Testing**

### **Unit Tests**
```bash
# Run unit tests
pytest tests/unit/

# Run with coverage
pytest tests/unit/ --cov=src --cov-report=html
```

### **Integration Tests**
```bash
# Run integration tests
pytest tests/integration/

# Test specific flow
pytest tests/integration/test_registration_flow.py
```

### **Manual Testing**
```bash
# Test registration flow
curl -X POST https://api.dev.com/orchestrator/registration/complete \
  -H "Content-Type: application/json" \
  -d @test_data/registration_request.json

# Check status
curl https://api.dev.com/orchestrator/registration/status/reg_abc123def456
```

## 📊 **Monitoring**

### **CloudWatch Metrics**
- `RegistrationStarted` - Number of registrations initiated
- `RegistrationCompleted` - Number of successful registrations
- `RegistrationFailed` - Number of failed registrations
- `StateTransitions` - State machine transitions
- `ServiceCallDuration` - External service call performance

### **Alarms**
- High error rate (>5% in 5 minutes)
- Long response times (>30 seconds)
- Failed state transitions
- DynamoDB throttling

## 🔒 **Security**

### **Rate Limiting**
- Registration: 5 requests/minute
- Verification: 10 requests/minute
- Payment: 5 requests/minute
- Status: 30 requests/minute

### **Data Protection**
- Passwords excluded from logs
- PII data encrypted in DynamoDB
- Audit logging for all operations
- Secure service-to-service communication

## 🐛 **Troubleshooting**

### **Common Issues**

**Registration Stuck in EMAIL_SENT**
- Check email service configuration
- Verify SES permissions
- Check spam folders

**Payment Processing Fails**
- Verify Stripe webhook configuration
- Check payment method validity
- Review subscription limits

**State Transition Errors**
- Check state machine validation
- Review error logs for details
- Verify service dependencies

### **Debug Commands**
```bash
# Check service health
curl https://api.dev.com/orchestrator/health

# Get registration details
aws dynamodb get-item \
  --table-name agent-scl-registrations-dev \
  --key '{"registration_id": {"S": "reg_abc123def456"}}'

# Check CloudWatch logs
aws logs filter-log-events \
  --log-group-name /aws/lambda/orchestrator-dev-registrationComplete \
  --start-time 1692000000000
```

## 📝 **Development**

### **Adding New States**
1. Update `RegistrationState` enum
2. Add state transitions to `STATE_TRANSITIONS`
3. Update `get_next_steps()` function
4. Add validation logic
5. Update tests

### **Adding New Endpoints**
1. Create handler in `src/handlers/`
2. Add function to `serverless.yml`
3. Update API documentation
4. Add tests
5. Update monitoring

## 🔄 **Future Enhancements**

- [ ] Integration with Setup Service
- [ ] Advanced retry mechanisms
- [ ] Registration analytics dashboard
- [ ] Multi-step payment flows
- [ ] Custom registration workflows
- [ ] Webhook notifications for registration events

---

## **📚 Documentación Adicional**

- 📖 [Documentación Técnica Completa](./TECHNICAL_DOCUMENTATION.md)
- 🔧 [Guía de State Machine](./docs/state-machine.md)
- 🚀 [Guía de Deployment](./docs/deployment.md)
- 🐛 [Troubleshooting Avanzado](./docs/troubleshooting.md)

---

## **📝 Changelog**

### **v1.0.0 (2024-08-29) - ✅ DEPLOYED**
- ✅ Coordinación completa de flujo de registro
- ✅ State machine robusto con manejo de errores
- ✅ Routing inteligente de mensajes entre servicios
- ✅ Integración con todos los servicios del ecosistema
- ✅ Circuit breakers y resilencia
- ✅ Monitoreo y logging centralizados
- ✅ Seguridad y audit trail completo

### **Próximas Versiones**
- 🔄 Machine Learning para routing predictivo
- 🔄 Event sourcing completo
- 🔄 Multi-region coordination
- 🔄 Advanced analytics y optimización

---

**📝 Documento actualizado**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
**🔗 Repositorio**: [agent-scl/services/orchestrator](https://github.com/agent-scl/services/orchestrator)
