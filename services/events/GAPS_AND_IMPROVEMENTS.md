# 📡 Events Service - Gaps and Improvements

## 📊 **Current Status: 8.1/10 - MUY BUENO**

### **Completitud:** 80% funcional, gaps identificados

---

## 🎯 **Gaps Identificados**

### **1. CRITICAL GAPS (20%)**

#### **1.1 Real AWS SNS/SQS Integration**
**Priority:** High  
**Effort:** 4-5 days  
**Impact:** Core functionality

**Current State:**
- SNS/SQS integrations are simulated
- Missing real event streaming
- Limited error handling for AWS failures

**Required Implementation:**

```python
# src/services/real_event_streaming_service.py
"""Real event streaming service with SNS/SQS integration."""

import boto3
import json
import asyncio
from typing import Dict, Any, List, Optional
from shared.logger import lambda_logger
from shared.exceptions import EventException
from shared.database import db_client

class RealEventStreamingService:
    """Real event streaming service."""
    
    def __init__(self):
        self.sns_client = boto3.client('sns')
        self.sqs_client = boto3.client('sqs')
        self.db = db_client
        
        # Topic ARNs from environment
        self.topic_arns = {
            'user_events': os.environ.get('USER_EVENTS_TOPIC_ARN'),
            'tenant_events': os.environ.get('TENANT_EVENTS_TOPIC_ARN'),
            'payment_events': os.environ.get('PAYMENT_EVENTS_TOPIC_ARN'),
            'system_events': os.environ.get('SYSTEM_EVENTS_TOPIC_ARN')
        }
        
        # Queue URLs from environment
        self.queue_urls = {
            'event_processing': os.environ.get('EVENT_PROCESSING_QUEUE_URL'),
            'dead_letter': os.environ.get('DEAD_LETTER_QUEUE_URL')
        }
    
    async def publish_event(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Publish event to appropriate SNS topic."""
        try:
            # Determine topic based on event type
            topic_arn = self._get_topic_for_event_type(event_type)
            if not topic_arn:
                raise EventException(f"No topic configured for event type: {event_type}")
            
            # Prepare event message
            event_message = {
                'event_id': self._generate_event_id(),
                'event_type': event_type,
                'event_data': event_data,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'timestamp': int(time.time()),
                'source': 'events-service',
                'version': '1.0'
            }
            
            # Add message attributes
            message_attributes = {
                'event_type': {
                    'DataType': 'String',
                    'StringValue': event_type
                }
            }
            
            if tenant_id:
                message_attributes['tenant_id'] = {
                    'DataType': 'String',
                    'StringValue': tenant_id
                }
            
            # Publish to SNS
            response = self.sns_client.publish(
                TopicArn=topic_arn,
                Message=json.dumps(event_message),
                MessageAttributes=message_attributes,
                Subject=f'Event: {event_type}'
            )
            
            # Store event in database for history
            await self._store_event_history(event_message)
            
            lambda_logger.info(f"Event published: {event_type}", extra={
                'event_id': event_message['event_id'],
                'message_id': response['MessageId'],
                'tenant_id': tenant_id
            })
            
            return {
                'event_id': event_message['event_id'],
                'message_id': response['MessageId'],
                'topic_arn': topic_arn,
                'status': 'published'
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to publish event: {str(e)}")
            raise EventException(f"Event publishing failed: {str(e)}")
    
    async def process_event_queue(self, max_messages: int = 10) -> Dict[str, Any]:
        """Process events from SQS queue."""
        try:
            if not self.queue_urls['event_processing']:
                raise EventException("Event processing queue URL not configured")
            
            # Receive messages from queue
            response = self.sqs_client.receive_message(
                QueueUrl=self.queue_urls['event_processing'],
                MaxNumberOfMessages=max_messages,
                WaitTimeSeconds=20,  # Long polling
                MessageAttributeNames=['All']
            )
            
            messages = response.get('Messages', [])
            processing_results = {
                'processed_count': 0,
                'failed_count': 0,
                'errors': []
            }
            
            # Process each message
            for message in messages:
                try:
                    # Parse event data
                    event_data = json.loads(message['Body'])
                    
                    # If the message is from SNS, extract the actual message
                    if 'Message' in event_data:
                        event_data = json.loads(event_data['Message'])
                    
                    # Process the event
                    await self._process_single_event(event_data)
                    
                    # Delete message from queue
                    self.sqs_client.delete_message(
                        QueueUrl=self.queue_urls['event_processing'],
                        ReceiptHandle=message['ReceiptHandle']
                    )
                    
                    processing_results['processed_count'] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to process message {message.get('MessageId', 'unknown')}: {str(e)}"
                    processing_results['errors'].append(error_msg)
                    processing_results['failed_count'] += 1
                    
                    lambda_logger.error(error_msg)
                    
                    # Send to dead letter queue if configured
                    if self.queue_urls['dead_letter']:
                        await self._send_to_dead_letter_queue(message, str(e))
            
            return processing_results
            
        except Exception as e:
            lambda_logger.error(f"Failed to process event queue: {str(e)}")
            raise EventException(f"Queue processing failed: {str(e)}")
    
    async def _process_single_event(self, event_data: Dict[str, Any]) -> None:
        """Process a single event."""
        try:
            event_type = event_data.get('event_type')
            event_id = event_data.get('event_id')
            
            # Route event to appropriate processor
            if event_type.startswith('user.'):
                await self._process_user_event(event_data)
            elif event_type.startswith('tenant.'):
                await self._process_tenant_event(event_data)
            elif event_type.startswith('payment.'):
                await self._process_payment_event(event_data)
            elif event_type.startswith('system.'):
                await self._process_system_event(event_data)
            else:
                lambda_logger.warning(f"Unknown event type: {event_type}")
            
            # Update event status
            await self._update_event_status(event_id, 'processed')
            
        except Exception as e:
            lambda_logger.error(f"Failed to process event {event_data.get('event_id')}: {str(e)}")
            await self._update_event_status(event_data.get('event_id'), 'failed', str(e))
            raise
    
    async def _process_user_event(self, event_data: Dict[str, Any]) -> None:
        """Process user-related events."""
        event_type = event_data.get('event_type')
        user_data = event_data.get('event_data', {})
        
        if event_type == 'user.created':
            # Handle user creation event
            await self._handle_user_created(user_data)
        elif event_type == 'user.updated':
            # Handle user update event
            await self._handle_user_updated(user_data)
        elif event_type == 'user.deleted':
            # Handle user deletion event
            await self._handle_user_deleted(user_data)
        elif event_type == 'user.login':
            # Handle user login event
            await self._handle_user_login(user_data)
    
    async def _process_tenant_event(self, event_data: Dict[str, Any]) -> None:
        """Process tenant-related events."""
        event_type = event_data.get('event_type')
        tenant_data = event_data.get('event_data', {})
        
        if event_type == 'tenant.created':
            await self._handle_tenant_created(tenant_data)
        elif event_type == 'tenant.updated':
            await self._handle_tenant_updated(tenant_data)
        elif event_type == 'tenant.suspended':
            await self._handle_tenant_suspended(tenant_data)
        elif event_type == 'tenant.activated':
            await self._handle_tenant_activated(tenant_data)
    
    async def _process_payment_event(self, event_data: Dict[str, Any]) -> None:
        """Process payment-related events."""
        event_type = event_data.get('event_type')
        payment_data = event_data.get('event_data', {})
        
        if event_type == 'payment.succeeded':
            await self._handle_payment_succeeded(payment_data)
        elif event_type == 'payment.failed':
            await self._handle_payment_failed(payment_data)
        elif event_type == 'subscription.created':
            await self._handle_subscription_created(payment_data)
        elif event_type == 'subscription.cancelled':
            await self._handle_subscription_cancelled(payment_data)
    
    async def _process_system_event(self, event_data: Dict[str, Any]) -> None:
        """Process system-related events."""
        event_type = event_data.get('event_type')
        system_data = event_data.get('event_data', {})
        
        if event_type == 'system.health_check':
            await self._handle_health_check(system_data)
        elif event_type == 'system.error':
            await self._handle_system_error(system_data)
        elif event_type == 'system.maintenance':
            await self._handle_maintenance_event(system_data)
    
    def _get_topic_for_event_type(self, event_type: str) -> Optional[str]:
        """Get SNS topic ARN for event type."""
        if event_type.startswith('user.'):
            return self.topic_arns['user_events']
        elif event_type.startswith('tenant.'):
            return self.topic_arns['tenant_events']
        elif event_type.startswith('payment.') or event_type.startswith('subscription.'):
            return self.topic_arns['payment_events']
        elif event_type.startswith('system.'):
            return self.topic_arns['system_events']
        else:
            return None
    
    def _generate_event_id(self) -> str:
        """Generate unique event ID."""
        import uuid
        return f"evt_{uuid.uuid4().hex}"
    
    async def _store_event_history(self, event_message: Dict[str, Any]) -> None:
        """Store event in database for history."""
        try:
            event_record = {
                'PK': f'EVENT#{event_message["event_id"]}',
                'SK': 'METADATA',
                'entity_type': 'EVENT',
                'event_id': event_message['event_id'],
                'event_type': event_message['event_type'],
                'event_data': event_message['event_data'],
                'tenant_id': event_message.get('tenant_id'),
                'user_id': event_message.get('user_id'),
                'timestamp': event_message['timestamp'],
                'status': 'published',
                'ttl': int(time.time()) + (90 * 24 * 60 * 60)  # 90 days TTL
            }
            
            await self.db.put_item(event_record, event_message.get('tenant_id'))
            
        except Exception as e:
            lambda_logger.error(f"Failed to store event history: {str(e)}")
    
    async def _update_event_status(
        self, 
        event_id: str, 
        status: str, 
        error_message: Optional[str] = None
    ) -> None:
        """Update event processing status."""
        try:
            update_expression = 'SET #status = :status, updated_at = :timestamp'
            expression_attribute_names = {'#status': 'status'}
            expression_attribute_values = {
                ':status': status,
                ':timestamp': int(time.time())
            }
            
            if error_message:
                update_expression += ', error_message = :error'
                expression_attribute_values[':error'] = error_message
            
            await self.db.update_item(
                pk=f'EVENT#{event_id}',
                sk='METADATA',
                update_expression=update_expression,
                expression_attribute_names=expression_attribute_names,
                expression_attribute_values=expression_attribute_values
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to update event status: {str(e)}")
    
    async def _send_to_dead_letter_queue(
        self, 
        message: Dict[str, Any], 
        error_reason: str
    ) -> None:
        """Send failed message to dead letter queue."""
        try:
            if not self.queue_urls['dead_letter']:
                return
            
            dead_letter_message = {
                'original_message': message,
                'error_reason': error_reason,
                'failed_at': int(time.time()),
                'retry_count': message.get('retry_count', 0) + 1
            }
            
            self.sqs_client.send_message(
                QueueUrl=self.queue_urls['dead_letter'],
                MessageBody=json.dumps(dead_letter_message)
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to send to dead letter queue: {str(e)}")
    
    # Event handler methods (implement based on business logic)
    async def _handle_user_created(self, user_data: Dict[str, Any]) -> None:
        """Handle user creation event."""
        # Implement user creation side effects
        pass
    
    async def _handle_user_login(self, user_data: Dict[str, Any]) -> None:
        """Handle user login event."""
        # Update last login timestamp, track analytics, etc.
        pass
    
    async def _handle_tenant_created(self, tenant_data: Dict[str, Any]) -> None:
        """Handle tenant creation event."""
        # Initialize tenant resources, send welcome emails, etc.
        pass
    
    async def _handle_payment_succeeded(self, payment_data: Dict[str, Any]) -> None:
        """Handle successful payment event."""
        # Update subscription status, send receipts, etc.
        pass
```

#### **1.2 Missing Event Management Endpoints**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Operational excellence

**Current State:**
- Events are processed automatically
- Missing event monitoring
- Limited event replay capabilities

**Required Endpoints:**

```python
# src/handlers/event_monitoring.py
"""Event monitoring handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@events_resilience("event_monitoring")
@measure_performance("events_monitoring")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Monitor event processing.
    
    GET /events/status
    GET /events/metrics
    GET /events/failed
    POST /events/retry/{event_id}
    """
    pass

# src/handlers/event_history.py
"""Event history handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@events_resilience("event_history")
@measure_performance("events_history")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Access event history.
    
    GET /events/history
    GET /events/{event_id}
    POST /events/search
    """
    pass

# src/handlers/event_replay.py
"""Event replay handler."""

@require_auth
@rate_limit(requests_per_minute=10)
@events_resilience("event_replay")
@measure_performance("events_replay")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Replay events for recovery.
    
    POST /events/replay
    GET /events/replay/{replay_id}/status
    """
    pass
```
