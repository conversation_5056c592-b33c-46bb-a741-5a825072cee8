# src/events/services/queue_service.py
# Queue service for managing SQS operations

"""
Queue service that provides functionality for managing SQS queues,
including message sending, receiving, and queue management operations.
"""

import json
import os
from typing import Dict, Any, List, Optional

from shared.logger import lambda_logger
from shared.exceptions import PlatformException


class QueueService:
    """Service for managing SQS queues."""
    
    def __init__(self):
        self.region = os.environ.get('REGION', 'us-east-1')
        self.project_name = os.environ.get('PROJECT_NAME', 'agent-scl')
        self.stage = os.environ.get('ENVIRONMENT', 'dev')
        
        # Queue names
        self.events_queue_name = f"{self.project_name}-{self.stage}-events-queue"
        self.dlq_name = f"{self.project_name}-{self.stage}-events-dlq"
    
    def send_message(self, message: Dict[str, Any], 
                    queue_name: Optional[str] = None,
                    delay_seconds: int = 0) -> str:
        """
        Send a message to SQS queue.
        
        Args:
            message: Message payload to send
            queue_name: Optional queue name (defaults to events queue)
            delay_seconds: Delay before message becomes available
            
        Returns:
            Message ID
        """
        try:
            target_queue = queue_name or self.events_queue_name
            message_body = json.dumps(message)
            
            lambda_logger.info(f"Sending message to queue {target_queue}")
            
            # This would use boto3 SQS client to send message
            # For now, just log the operation
            
            # Example SQS send:
            # response = sqs_client.send_message(
            #     QueueUrl=f"https://sqs.{self.region}.amazonaws.com/{account_id}/{target_queue}",
            #     MessageBody=message_body,
            #     DelaySeconds=delay_seconds
            # )
            # return response['MessageId']
            
            # Mock message ID for now
            mock_message_id = f"msg_{hash(message_body) % 1000000}"
            lambda_logger.info(f"Message sent with ID: {mock_message_id}")
            return mock_message_id
            
        except Exception as e:
            lambda_logger.error(f"Failed to send message to queue: {str(e)}")
            raise PlatformException(f"Queue message sending failed: {str(e)}")
    
    def receive_messages(self, queue_name: Optional[str] = None,
                        max_messages: int = 10,
                        wait_time_seconds: int = 20) -> List[Dict[str, Any]]:
        """
        Receive messages from SQS queue.
        
        Args:
            queue_name: Optional queue name (defaults to events queue)
            max_messages: Maximum number of messages to receive
            wait_time_seconds: Long polling wait time
            
        Returns:
            List of messages
        """
        try:
            target_queue = queue_name or self.events_queue_name
            
            lambda_logger.info(f"Receiving messages from queue {target_queue}")
            
            # This would use boto3 SQS client to receive messages
            # For now, just log the operation
            
            # Example SQS receive:
            # response = sqs_client.receive_message(
            #     QueueUrl=f"https://sqs.{self.region}.amazonaws.com/{account_id}/{target_queue}",
            #     MaxNumberOfMessages=max_messages,
            #     WaitTimeSeconds=wait_time_seconds,
            #     MessageAttributeNames=['All']
            # )
            # return response.get('Messages', [])
            
            # Mock empty response for now
            return []
            
        except Exception as e:
            lambda_logger.error(f"Failed to receive messages from queue: {str(e)}")
            raise PlatformException(f"Queue message receiving failed: {str(e)}")
    
    def delete_message(self, receipt_handle: str, 
                      queue_name: Optional[str] = None) -> bool:
        """
        Delete a message from SQS queue.
        
        Args:
            receipt_handle: Receipt handle of the message to delete
            queue_name: Optional queue name (defaults to events queue)
            
        Returns:
            True if successful
        """
        try:
            target_queue = queue_name or self.events_queue_name
            
            lambda_logger.info(f"Deleting message from queue {target_queue}")
            
            # This would use boto3 SQS client to delete message
            # For now, just log the operation
            
            # Example SQS delete:
            # sqs_client.delete_message(
            #     QueueUrl=f"https://sqs.{self.region}.amazonaws.com/{account_id}/{target_queue}",
            #     ReceiptHandle=receipt_handle
            # )
            
            lambda_logger.info("Message deleted successfully")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to delete message from queue: {str(e)}")
            raise PlatformException(f"Queue message deletion failed: {str(e)}")
    
    def get_queue_attributes(self, queue_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get queue attributes and statistics.
        
        Args:
            queue_name: Optional queue name (defaults to events queue)
            
        Returns:
            Queue attributes
        """
        try:
            target_queue = queue_name or self.events_queue_name
            
            lambda_logger.info(f"Getting attributes for queue {target_queue}")
            
            # This would use boto3 SQS client to get queue attributes
            # For now, return mock attributes
            
            # Example SQS get attributes:
            # response = sqs_client.get_queue_attributes(
            #     QueueUrl=f"https://sqs.{self.region}.amazonaws.com/{account_id}/{target_queue}",
            #     AttributeNames=['All']
            # )
            # return response['Attributes']
            
            # Mock attributes for now
            return {
                'ApproximateNumberOfMessages': '0',
                'ApproximateNumberOfMessagesNotVisible': '0',
                'ApproximateNumberOfMessagesDelayed': '0',
                'CreatedTimestamp': '**********',
                'LastModifiedTimestamp': '**********',
                'VisibilityTimeoutSeconds': '30',
                'MaxReceiveCount': '3',
                'MessageRetentionPeriod': '1209600'
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to get queue attributes: {str(e)}")
            raise PlatformException(f"Queue attributes retrieval failed: {str(e)}")
    
    def purge_queue(self, queue_name: Optional[str] = None) -> bool:
        """
        Purge all messages from a queue.
        
        Args:
            queue_name: Optional queue name (defaults to events queue)
            
        Returns:
            True if successful
        """
        try:
            target_queue = queue_name or self.events_queue_name
            
            lambda_logger.warning(f"Purging all messages from queue {target_queue}")
            
            # This would use boto3 SQS client to purge queue
            # For now, just log the operation
            
            # Example SQS purge:
            # sqs_client.purge_queue(
            #     QueueUrl=f"https://sqs.{self.region}.amazonaws.com/{account_id}/{target_queue}"
            # )
            
            lambda_logger.info("Queue purged successfully")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to purge queue: {str(e)}")
            raise PlatformException(f"Queue purging failed: {str(e)}")
    
    def send_to_dlq(self, message: Dict[str, Any], 
                   original_error: str) -> str:
        """
        Send a failed message to dead letter queue.
        
        Args:
            message: Original message that failed
            original_error: Error that caused the failure
            
        Returns:
            Message ID in DLQ
        """
        try:
            # Add error information to message
            dlq_message = {
                'original_message': message,
                'error': original_error,
                'failed_at': lambda_logger.get_timestamp(),
                'retry_count': message.get('retry_count', 0) + 1
            }
            
            return self.send_message(dlq_message, self.dlq_name)
            
        except Exception as e:
            lambda_logger.error(f"Failed to send message to DLQ: {str(e)}")
            raise PlatformException(f"DLQ message sending failed: {str(e)}")
    
    def get_dlq_messages(self, max_messages: int = 10) -> List[Dict[str, Any]]:
        """
        Get messages from dead letter queue for analysis.
        
        Args:
            max_messages: Maximum number of messages to retrieve
            
        Returns:
            List of failed messages
        """
        try:
            return self.receive_messages(
                queue_name=self.dlq_name,
                max_messages=max_messages,
                wait_time_seconds=1  # Short wait for DLQ
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to get DLQ messages: {str(e)}")
            raise PlatformException(f"DLQ message retrieval failed: {str(e)}")


# Global instance
queue_service = QueueService()
