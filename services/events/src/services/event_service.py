# src/events/services/event_service.py
# Event service for managing event operations

"""
Event service that provides core functionality for event management,
including event publishing, storage, and retrieval operations.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from shared.logger import lambda_logger
from shared.exceptions import PlatformException, ValidationException
from shared.database import db_client


class EventService:
    """Service for managing events."""
    
    def __init__(self):
        self.table_name = "events"  # This would be configured via environment
    
    def publish_event(self, event_type: str, data: Dict[str, Any], 
                     tenant_id: str, source: str = "unknown") -> str:
        """
        Publish an event to the event system.
        
        Args:
            event_type: Type of event (e.g., 'tenant.created')
            data: Event data payload
            tenant_id: Tenant ID associated with the event
            source: Source service that generated the event
            
        Returns:
            Event ID
        """
        try:
            event_id = str(uuid.uuid4())
            timestamp = datetime.utcnow().isoformat()
            
            event_record = {
                'event_id': event_id,
                'event_type': event_type,
                'timestamp': timestamp,
                'tenant_id': tenant_id,
                'data': data,
                'metadata': {
                    'source': source,
                    'version': '1.0',
                    'created_at': timestamp
                }
            }
            
            # Store event in DynamoDB
            self._store_event(event_record)
            
            # Publish to SNS for real-time processing
            self._publish_to_sns(event_record)
            
            lambda_logger.info(f"Published event {event_id} of type {event_type}")
            return event_id
            
        except Exception as e:
            lambda_logger.error(f"Failed to publish event: {str(e)}")
            raise PlatformException(f"Event publishing failed: {str(e)}")
    
    def get_events(self, tenant_id: str, event_type: Optional[str] = None,
                   start_date: Optional[datetime] = None, 
                   end_date: Optional[datetime] = None,
                   limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Retrieve events for a tenant.
        
        Args:
            tenant_id: Tenant ID to filter by
            event_type: Optional event type filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Maximum number of events to return
            offset: Number of events to skip
            
        Returns:
            List of events
        """
        try:
            # Build query parameters
            query_params = {
                'tenant_id': tenant_id,
                'limit': min(limit, 1000),  # Cap at 1000
                'offset': offset
            }
            
            if event_type:
                query_params['event_type'] = event_type
            if start_date:
                query_params['start_date'] = start_date.isoformat()
            if end_date:
                query_params['end_date'] = end_date.isoformat()
            
            # Query DynamoDB
            events = self._query_events(query_params)
            
            lambda_logger.info(f"Retrieved {len(events)} events for tenant {tenant_id}")
            return events
            
        except Exception as e:
            lambda_logger.error(f"Failed to retrieve events: {str(e)}")
            raise PlatformException(f"Event retrieval failed: {str(e)}")
    
    def get_event_by_id(self, event_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific event by ID.
        
        Args:
            event_id: Event ID
            tenant_id: Tenant ID for authorization
            
        Returns:
            Event data or None if not found
        """
        try:
            event = self._get_event_from_db(event_id)
            
            if event and event.get('tenant_id') == tenant_id:
                return event
            
            return None
            
        except Exception as e:
            lambda_logger.error(f"Failed to get event {event_id}: {str(e)}")
            raise PlatformException(f"Event retrieval failed: {str(e)}")
    
    def get_event_statistics(self, tenant_id: str, 
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get event statistics for a tenant.
        
        Args:
            tenant_id: Tenant ID
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            Event statistics
        """
        try:
            # Query for statistics
            stats = self._calculate_statistics(tenant_id, start_date, end_date)
            
            lambda_logger.info(f"Calculated statistics for tenant {tenant_id}")
            return stats
            
        except Exception as e:
            lambda_logger.error(f"Failed to calculate statistics: {str(e)}")
            raise PlatformException(f"Statistics calculation failed: {str(e)}")
    
    def delete_old_events(self, days_to_keep: int = 90) -> int:
        """
        Delete events older than specified days.
        
        Args:
            days_to_keep: Number of days to keep events
            
        Returns:
            Number of events deleted
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            deleted_count = self._delete_events_before_date(cutoff_date)
            
            lambda_logger.info(f"Deleted {deleted_count} old events")
            return deleted_count
            
        except Exception as e:
            lambda_logger.error(f"Failed to delete old events: {str(e)}")
            raise PlatformException(f"Event cleanup failed: {str(e)}")
    
    def _store_event(self, event_record: Dict[str, Any]) -> None:
        """Store event in DynamoDB."""
        try:
            # This would use the shared database client to store the event
            # For now, just log the operation
            lambda_logger.info(f"Storing event {event_record['event_id']} in DynamoDB")
            
            # Example DynamoDB put operation:
            # db_client.put_item(
            #     table_name=self.table_name,
            #     item=event_record
            # )
            
        except Exception as e:
            lambda_logger.error(f"Failed to store event in DynamoDB: {str(e)}")
            raise
    
    def _publish_to_sns(self, event_record: Dict[str, Any]) -> None:
        """Publish event to SNS topic."""
        try:
            # This would publish to SNS topic for real-time processing
            lambda_logger.info(f"Publishing event {event_record['event_id']} to SNS")
            
            # Example SNS publish:
            # sns_client.publish(
            #     TopicArn=f"arn:aws:sns:{region}:{account}:agent-scl-dev-tenant-events",
            #     Message=json.dumps(event_record)
            # )
            
        except Exception as e:
            lambda_logger.error(f"Failed to publish to SNS: {str(e)}")
            raise
    
    def _query_events(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Query events from DynamoDB."""
        try:
            # This would query DynamoDB with the given parameters
            lambda_logger.info(f"Querying events with params: {query_params}")
            
            # Mock response for now
            return []
            
        except Exception as e:
            lambda_logger.error(f"Failed to query events: {str(e)}")
            raise
    
    def _get_event_from_db(self, event_id: str) -> Optional[Dict[str, Any]]:
        """Get single event from DynamoDB."""
        try:
            # This would get a single event from DynamoDB
            lambda_logger.info(f"Getting event {event_id} from DynamoDB")
            
            # Mock response for now
            return None
            
        except Exception as e:
            lambda_logger.error(f"Failed to get event from DB: {str(e)}")
            raise
    
    def _calculate_statistics(self, tenant_id: str, 
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """Calculate event statistics."""
        try:
            # This would calculate real statistics from DynamoDB
            lambda_logger.info(f"Calculating statistics for tenant {tenant_id}")
            
            # Mock statistics for now
            return {
                'total_events': 0,
                'events_by_type': {},
                'events_by_day': {},
                'most_active_sources': []
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to calculate statistics: {str(e)}")
            raise
    
    def _delete_events_before_date(self, cutoff_date: datetime) -> int:
        """Delete events before specified date."""
        try:
            # This would delete old events from DynamoDB
            lambda_logger.info(f"Deleting events before {cutoff_date}")
            
            # Mock deletion for now
            return 0
            
        except Exception as e:
            lambda_logger.error(f"Failed to delete old events: {str(e)}")
            raise


# Global instance
event_service = EventService()
