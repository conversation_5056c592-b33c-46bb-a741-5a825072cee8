# src/events/handlers/event_history.py
# Event history handler for retrieving event history

"""
Event history handler that provides API endpoints for retrieving
event history and analytics for tenants and users.
"""

import json
import os
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body
from shared.auth import require_auth
from shared.middleware.resilience_middleware import with_retry, events_resilience, rate_limit
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    AuthorizationException
)
from shared.logger import lambda_logger, log_api_request, log_api_response


@require_auth
@rate_limit(requests_per_minute=60)  # Allow reasonable rate for history queries
@events_resilience("event_history")
@measure_performance("events_event_history")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get event history for tenant.
    
    Args:
        event: API Gateway event
        context: Lambda context
        
    Returns:
        Event history data
    """
    log_api_request(event, "event_history")
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    try:
        # Extract user info from authorizer
        user_info = event.get('requestContext', {}).get('authorizer', {})
        tenant_id = user_info.get('tenant_id')
        user_id = user_info.get('user_id')
        
        if not tenant_id:
            raise AuthorizationException("Tenant ID not found in authorization context")
        
        # Parse query parameters
        query_params = event.get('queryStringParameters') or {}
        
        # Build filter criteria
        filter_criteria = build_filter_criteria(query_params, tenant_id)
        
        # Get event history
        events_data = get_event_history(filter_criteria)
        
        # Get event statistics
        stats = get_event_statistics(tenant_id, filter_criteria)
        
        response_data = {
            'events': events_data,
            'statistics': stats,
            'filter_criteria': filter_criteria,
            'total_count': len(events_data)
        }
        
        # Record metrics
        metrics_manager.record_metric('event_history_requests', 1)
        metrics_manager.record_metric('events_returned', len(events_data))
        
        response = APIResponse.success(
            data=response_data,
            message=f"Retrieved {len(events_data)} events"
        )
        
        log_api_response(response, "event_history")
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning(f"Authorization error in event history: {str(e)}")
        response = APIResponse.error(
            message="Authorization failed",
            error_code="AUTHORIZATION_ERROR",
            status_code=403
        )
        log_api_response(response, "event_history")
        return response
        
    except ValidationException as e:
        lambda_logger.warning(f"Validation error in event history: {str(e)}")
        response = APIResponse.error(
            message="Invalid request parameters",
            error_code="VALIDATION_ERROR",
            details=str(e),
            status_code=400
        )
        log_api_response(response, "event_history")
        return response
        
    except Exception as e:
        lambda_logger.error(f"Event history error: {str(e)}")
        metrics_manager.record_metric('event_history_errors', 1)
        
        response = APIResponse.error(
            message="Failed to retrieve event history",
            error_code="EVENT_HISTORY_ERROR",
            details=str(e)
        )
        
        log_api_response(response, "event_history")
        return response


def build_filter_criteria(query_params: Dict[str, Any], tenant_id: str) -> Dict[str, Any]:
    """
    Build filter criteria from query parameters.
    
    Args:
        query_params: Query parameters from request
        tenant_id: Tenant ID for filtering
        
    Returns:
        Filter criteria dictionary
    """
    criteria = {
        'tenant_id': tenant_id
    }
    
    # Event type filter
    if query_params.get('event_type'):
        criteria['event_type'] = query_params['event_type']
    
    # Date range filter
    if query_params.get('start_date'):
        try:
            criteria['start_date'] = datetime.fromisoformat(query_params['start_date'])
        except ValueError:
            raise ValidationException("Invalid start_date format. Use ISO format.")
    
    if query_params.get('end_date'):
        try:
            criteria['end_date'] = datetime.fromisoformat(query_params['end_date'])
        except ValueError:
            raise ValidationException("Invalid end_date format. Use ISO format.")
    
    # Default to last 30 days if no date range specified
    if 'start_date' not in criteria and 'end_date' not in criteria:
        criteria['start_date'] = datetime.utcnow() - timedelta(days=30)
        criteria['end_date'] = datetime.utcnow()
    
    # Pagination
    criteria['limit'] = min(int(query_params.get('limit', 100)), 1000)  # Max 1000 events
    criteria['offset'] = int(query_params.get('offset', 0))
    
    # Sorting
    criteria['sort_order'] = query_params.get('sort_order', 'desc')  # desc = newest first
    
    return criteria


def get_event_history(filter_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Retrieve event history from DynamoDB based on filter criteria.
    
    Args:
        filter_criteria: Filter criteria for events
        
    Returns:
        List of events
    """
    try:
        # This would integrate with DynamoDB to query event history
        # For now, return mock data structure
        
        lambda_logger.info(f"Querying event history with criteria: {filter_criteria}")
        
        # Mock events for demonstration
        mock_events = [
            {
                'event_id': 'evt_001',
                'event_type': 'tenant.created',
                'timestamp': datetime.utcnow().isoformat(),
                'tenant_id': filter_criteria['tenant_id'],
                'data': {
                    'tenant_name': 'Example Tenant',
                    'created_by': 'user_123'
                },
                'metadata': {
                    'source': 'tenant-service',
                    'version': '1.0'
                }
            },
            {
                'event_id': 'evt_002',
                'event_type': 'user.created',
                'timestamp': (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                'tenant_id': filter_criteria['tenant_id'],
                'data': {
                    'user_id': 'user_456',
                    'email': '<EMAIL>'
                },
                'metadata': {
                    'source': 'user-service',
                    'version': '1.0'
                }
            }
        ]
        
        # Apply filters (in real implementation, this would be done in DynamoDB query)
        filtered_events = apply_filters(mock_events, filter_criteria)
        
        return filtered_events
        
    except Exception as e:
        lambda_logger.error(f"Error retrieving event history: {str(e)}")
        raise PlatformException(f"Failed to retrieve event history: {str(e)}")


def apply_filters(events: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Apply filters to events list (mock implementation).
    
    Args:
        events: List of events
        criteria: Filter criteria
        
    Returns:
        Filtered events
    """
    filtered = events
    
    # Filter by event type
    if criteria.get('event_type'):
        filtered = [e for e in filtered if e['event_type'] == criteria['event_type']]
    
    # Sort by timestamp
    if criteria.get('sort_order') == 'desc':
        filtered = sorted(filtered, key=lambda x: x['timestamp'], reverse=True)
    else:
        filtered = sorted(filtered, key=lambda x: x['timestamp'])
    
    # Apply pagination
    offset = criteria.get('offset', 0)
    limit = criteria.get('limit', 100)
    filtered = filtered[offset:offset + limit]
    
    return filtered


def get_event_statistics(tenant_id: str, filter_criteria: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get event statistics for the tenant.
    
    Args:
        tenant_id: Tenant ID
        filter_criteria: Filter criteria
        
    Returns:
        Event statistics
    """
    try:
        # This would calculate real statistics from DynamoDB
        # For now, return mock statistics
        
        stats = {
            'total_events': 150,
            'events_by_type': {
                'tenant.created': 1,
                'tenant.updated': 5,
                'user.created': 25,
                'user.updated': 45,
                'payment.subscription_created': 10,
                'payment.subscription_updated': 15,
                'security.audit_log': 49
            },
            'events_by_day': {
                'today': 12,
                'yesterday': 18,
                'last_7_days': 89,
                'last_30_days': 150
            },
            'most_active_sources': [
                {'source': 'user-service', 'count': 70},
                {'source': 'security-service', 'count': 49},
                {'source': 'payment-service', 'count': 25},
                {'source': 'tenant-service', 'count': 6}
            ]
        }
        
        return stats
        
    except Exception as e:
        lambda_logger.error(f"Error calculating event statistics: {str(e)}")
        return {
            'total_events': 0,
            'events_by_type': {},
            'events_by_day': {},
            'most_active_sources': []
        }
