# src/events/handlers/tenant_events.py
# Tenant event handlers for event-driven architecture

"""
Tenant event handlers that process tenant domain events
and trigger appropriate side effects and integrations.
"""

import json
import asyncio
from typing import Dict, Any, List

from shared.logger import audit_log, lambda_logger
from shared.events import EventType, register_event_handler, handle_sqs_event
from shared.email import email_service
from shared.database import db_client
from shared.models import TenantInfo as Tenant, TenantStatus, TenantPlan
# TODO: Use shared models - from ...auth.models.tenant import Tenant


async def handle_tenant_created(event_data: Dict[str, Any]) -> None:
    """Handle tenant created event."""
    lambda_logger.info("Processing tenant created event", extra={
        'event_id': event_data.get('event_id'),
        'tenant_id': event_data.get('tenant_id')
    })
    
    try:
        tenant_id = event_data['data']['tenant_id']
        tenant_name = event_data['data']['tenant_name']
        master_user_email = event_data['data']['master_user_email']
        
        # Send welcome email to master user
        await email_service.send_tenant_welcome_email(
            email=master_user_email,
            tenant_name=tenant_name,
            tenant_id=tenant_id
        )
        
        # Initialize tenant analytics
        await _initialize_tenant_analytics(tenant_id)
        
        # Create default tenant settings
        await _create_default_tenant_settings(tenant_id)
        
        # Log tenant creation for analytics
        await _log_tenant_creation_analytics(event_data)
        
        lambda_logger.info("Tenant created event processed successfully", extra={
            'event_id': event_data.get('event_id'),
            'tenant_id': tenant_id
        })
        
    except Exception as e:
        lambda_logger.error("Failed to process tenant created event", extra={
            'event_id': event_data.get('event_id'),
            'error': str(e)
        })
        raise


async def handle_tenant_activated(event_data: Dict[str, Any]) -> None:
    """Handle tenant activated event."""
    lambda_logger.info("Processing tenant activated event", extra={
        'event_id': event_data.get('event_id'),
        'tenant_id': event_data.get('tenant_id')
    })
    
    try:
        tenant_id = event_data['data']['tenant_id']
        
        # Enable tenant features
        await _enable_tenant_features(tenant_id)
        
        # Send activation confirmation email
        tenant = Tenant.get_by_id(tenant_id)
        if tenant:
            await email_service.send_tenant_activation_email(
                email=tenant.master_user_email,
                tenant_name=tenant.name
            )
        
        # Update tenant analytics
        await _update_tenant_activation_analytics(tenant_id)
        
        lambda_logger.info("Tenant activated event processed successfully", extra={
            'event_id': event_data.get('event_id'),
            'tenant_id': tenant_id
        })
        
    except Exception as e:
        lambda_logger.error("Failed to process tenant activated event", extra={
            'event_id': event_data.get('event_id'),
            'error': str(e)
        })
        raise


async def handle_tenant_suspended(event_data: Dict[str, Any]) -> None:
    """Handle tenant suspended event."""
    lambda_logger.info("Processing tenant suspended event", extra={
        'event_id': event_data.get('event_id'),
        'tenant_id': event_data.get('tenant_id')
    })
    
    try:
        tenant_id = event_data['data']['tenant_id']
        reason = event_data['data'].get('reason', 'No reason provided')
        
        # Disable tenant features
        await _disable_tenant_features(tenant_id)
        
        # Send suspension notification email
        tenant = Tenant.get_by_id(tenant_id)
        if tenant:
            await email_service.send_tenant_suspension_email(
                email=tenant.master_user_email,
                tenant_name=tenant.name,
                reason=reason
            )
        
        # Update tenant analytics
        await _update_tenant_suspension_analytics(tenant_id, reason)
        
        lambda_logger.info("Tenant suspended event processed successfully", extra={
            'event_id': event_data.get('event_id'),
            'tenant_id': tenant_id
        })
        
    except Exception as e:
        lambda_logger.error("Failed to process tenant suspended event", extra={
            'event_id': event_data.get('event_id'),
            'error': str(e)
        })
        raise


async def handle_tenant_updated(event_data: Dict[str, Any]) -> None:
    """Handle tenant updated event."""
    lambda_logger.info("Processing tenant updated event", extra={
        'event_id': event_data.get('event_id'),
        'tenant_id': event_data.get('tenant_id')
    })
    
    try:
        tenant_id = event_data['data']['tenant_id']
        changes = event_data['data'].get('changes', {})
        
        # Handle plan changes
        if 'plan' in changes:
            await _handle_tenant_plan_change(tenant_id, changes['plan'])
        
        # Handle name changes
        if 'name' in changes:
            await _handle_tenant_name_change(tenant_id, changes['name'])
        
        # Update tenant analytics
        await _update_tenant_modification_analytics(tenant_id, changes)
        
        lambda_logger.info("Tenant updated event processed successfully", extra={
            'event_id': event_data.get('event_id'),
            'tenant_id': tenant_id,
            'changes': list(changes.keys())
        })
        
    except Exception as e:
        lambda_logger.error("Failed to process tenant updated event", extra={
            'event_id': event_data.get('event_id'),
            'error': str(e)
        })
        raise


# Helper functions
async def _initialize_tenant_analytics(tenant_id: str) -> None:
    """Initialize analytics for a new tenant."""
    analytics_data = {
        'PK': f"TENANT#{tenant_id}",
        'SK': 'ANALYTICS#SUMMARY',
        'entity_type': 'TENANT_ANALYTICS',
        'tenant_id': tenant_id,
        'created_at': lambda_logger.get_current_timestamp(),
        'total_users': 1,  # Master user
        'total_logins': 0,
        'total_api_calls': 0,
        'storage_used_bytes': 0,
        'last_activity': lambda_logger.get_current_timestamp(),
        'status': 'active'
    }
    
    db_client.put_item(analytics_data, tenant_id)


async def _create_default_tenant_settings(tenant_id: str) -> None:
    """Create default settings for a new tenant."""
    settings_data = {
        'PK': f"TENANT#{tenant_id}",
        'SK': 'SETTINGS#DEFAULT',
        'entity_type': 'TENANT_SETTINGS',
        'tenant_id': tenant_id,
        'created_at': lambda_logger.get_current_timestamp(),
        'settings': {
            'notifications': {
                'email_enabled': True,
                'sms_enabled': False,
                'push_enabled': True
            },
            'security': {
                'password_policy': 'standard',
                'mfa_required': False,
                'session_timeout_minutes': 480
            },
            'features': {
                'api_access': True,
                'advanced_analytics': False,
                'custom_integrations': False
            }
        }
    }
    
    db_client.put_item(settings_data, tenant_id)


async def _log_tenant_creation_analytics(event_data: Dict[str, Any]) -> None:
    """Log tenant creation for platform analytics."""
    analytics_data = {
        'PK': 'PLATFORM#ANALYTICS',
        'SK': f"TENANT_CREATION#{event_data.get('event_id')}",
        'entity_type': 'PLATFORM_ANALYTICS',
        'event_type': 'tenant_creation',
        'tenant_id': event_data['data']['tenant_id'],
        'timestamp': event_data.get('timestamp'),
        'metadata': {
            'tenant_name': event_data['data']['tenant_name'],
            'plan': event_data['data'].get('plan', 'FREE'),
            'source': event_data.get('metadata', {}).get('source', 'web')
        }
    }
    
    db_client.put_item(analytics_data, 'platform')


async def _enable_tenant_features(tenant_id: str) -> None:
    """Enable features for an activated tenant."""
    # This would enable various tenant features
    # Implementation depends on specific feature requirements
    lambda_logger.info("Enabling tenant features", extra={'tenant_id': tenant_id})


async def _disable_tenant_features(tenant_id: str) -> None:
    """Disable features for a suspended tenant."""
    # This would disable various tenant features
    # Implementation depends on specific feature requirements
    lambda_logger.info("Disabling tenant features", extra={'tenant_id': tenant_id})


async def _update_tenant_activation_analytics(tenant_id: str) -> None:
    """Update analytics for tenant activation."""
    # Update tenant analytics with activation timestamp
    pass


async def _update_tenant_suspension_analytics(tenant_id: str, reason: str) -> None:
    """Update analytics for tenant suspension."""
    # Update tenant analytics with suspension timestamp and reason
    pass


async def _handle_tenant_plan_change(tenant_id: str, plan_change: Dict[str, Any]) -> None:
    """Handle tenant plan change."""
    old_plan = plan_change.get('old_value')
    new_plan = plan_change.get('new_value')
    
    lambda_logger.info("Handling tenant plan change", extra={
        'tenant_id': tenant_id,
        'old_plan': old_plan,
        'new_plan': new_plan
    })
    
    # Update tenant features based on new plan
    # Send plan change notification email
    # Update billing information


async def _handle_tenant_name_change(tenant_id: str, name_change: Dict[str, Any]) -> None:
    """Handle tenant name change."""
    old_name = name_change.get('old_value')
    new_name = name_change.get('new_value')
    
    lambda_logger.info("Handling tenant name change", extra={
        'tenant_id': tenant_id,
        'old_name': old_name,
        'new_name': new_name
    })
    
    # Update any references to the old name
    # Send name change notification email


async def _update_tenant_modification_analytics(tenant_id: str, changes: Dict[str, Any]) -> None:
    """Update analytics for tenant modifications."""
    # Log tenant modification for analytics
    pass


# Register event handlers
register_event_handler(EventType.TENANT_CREATED, handle_tenant_created)
register_event_handler(EventType.TENANT_ACTIVATED, handle_tenant_activated)
register_event_handler(EventType.TENANT_SUSPENDED, handle_tenant_suspended)
register_event_handler(EventType.TENANT_UPDATED, handle_tenant_updated)


def process_tenant_events(event, context):
    """
    Lambda handler for processing tenant events from SQS.
    
    Args:
        event: SQS event containing tenant domain events
        context: Lambda context
        
    Returns:
        Dict with processing results
    """
    lambda_logger.info("Processing tenant events batch", extra={
        'record_count': len(event.get('Records', [])),
        'function_name': context.function_name
    })
    
    processed_count = 0
    failed_count = 0
    
    for record in event.get('Records', []):
        try:
            # Parse SQS message
            message_body = json.loads(record['body'])
            
            # Handle SNS message format
            if 'Message' in message_body:
                event_data = json.loads(message_body['Message'])
            else:
                event_data = message_body
            
            # Process the event
            asyncio.run(handle_sqs_event(event_data))
            processed_count += 1
            
        except Exception as e:
            lambda_logger.error("Failed to process tenant event record", extra={
                'record': record,
                'error': str(e)
            })
            failed_count += 1
    
    lambda_logger.info("Tenant events batch processing completed", extra={
        'processed_count': processed_count,
        'failed_count': failed_count,
        'total_records': len(event.get('Records', []))
    })
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'processed_count': processed_count,
            'failed_count': failed_count,
            'total_records': len(event.get('Records', []))
        })
    }


def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Main Lambda handler for tenant events from SNS.

    Args:
        event: SNS event containing records
        context: Lambda context

    Returns:
        Processing results
    """
    try:
        lambda_logger.info("Processing tenant events from SNS", extra={
            'records_count': len(event.get('Records', []))
        })

        processed_count = 0
        failed_count = 0

        # Process SNS records
        for record in event.get('Records', []):
            try:
                # Extract SNS message
                sns_message = json.loads(record.get('Message', '{}'))

                # Process the tenant event
                result = process_tenant_event(sns_message)

                if result.get('success'):
                    processed_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                lambda_logger.error(f"Error processing SNS record: {str(e)}")
                failed_count += 1

        return {
            'statusCode': 200,
            'body': json.dumps({
                'processed_count': processed_count,
                'failed_count': failed_count,
                'total_records': len(event.get('Records', []))
            })
        }

    except Exception as e:
        lambda_logger.error(f"Tenant events handler error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        }


def process_tenant_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single tenant event.

    Args:
        event_data: Event data to process

    Returns:
        Processing result
    """
    try:
        event_type = event_data.get('event_type')

        if event_type == 'tenant.created':
            return handle_tenant_created_sync(event_data)
        elif event_type == 'tenant.updated':
            return handle_tenant_updated_sync(event_data)
        elif event_type == 'tenant.deleted':
            return handle_tenant_deleted_sync(event_data)
        else:
            lambda_logger.warning(f"Unknown tenant event type: {event_type}")
            return {'success': False, 'reason': f'Unknown event type: {event_type}'}

    except Exception as e:
        lambda_logger.error(f"Error processing tenant event: {str(e)}")
        return {'success': False, 'reason': str(e)}


def handle_tenant_created_sync(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle tenant created event synchronously."""
    try:
        lambda_logger.info(f"Processing tenant created event: {event_data.get('event_id')}")
        # Synchronous processing logic here
        return {'success': True, 'event_type': 'tenant.created'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def handle_tenant_updated_sync(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle tenant updated event synchronously."""
    try:
        lambda_logger.info(f"Processing tenant updated event: {event_data.get('event_id')}")
        # Synchronous processing logic here
        return {'success': True, 'event_type': 'tenant.updated'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def handle_tenant_deleted_sync(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle tenant deleted event synchronously."""
    try:
        lambda_logger.info(f"Processing tenant deleted event: {event_data.get('event_id')}")
        # Synchronous processing logic here
        return {'success': True, 'event_type': 'tenant.deleted'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}
