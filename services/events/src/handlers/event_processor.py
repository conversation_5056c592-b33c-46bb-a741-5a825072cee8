# src/events/handlers/event_processor.py
# Event processor handler for processing events from SQS queue

"""
Event processor handler that processes events from SQS queue
and routes them to appropriate handlers based on event type.
"""

import json
import os
from typing import Any, Dict, List

from shared.responses import APIResponse
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import PlatformException, ValidationException
from shared.metrics import metrics_manager, measure_performance
from shared.middleware.resilience_middleware import with_retry, events_resilience


@events_resilience("event_processor")
@measure_performance("events_event_processor")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process events from SQS queue.
    
    Args:
        event: SQS event containing records
        context: Lambda context
        
    Returns:
        Processing results
    """
    log_api_request(event, "event_processor")
    
    try:
        # Process SQS records
        records = event.get('Records', [])
        processed_count = 0
        failed_count = 0
        
        lambda_logger.info(f"Processing {len(records)} SQS records")
        
        for record in records:
            try:
                # Extract message body
                message_body = json.loads(record.get('body', '{}'))
                
                # Process the event
                result = process_single_event(message_body)
                
                if result.get('success'):
                    processed_count += 1
                    lambda_logger.info(f"Successfully processed event: {message_body.get('event_id')}")
                else:
                    failed_count += 1
                    lambda_logger.error(f"Failed to process event: {message_body.get('event_id')}")
                    
            except Exception as e:
                failed_count += 1
                lambda_logger.error(f"Error processing SQS record: {str(e)}", extra={
                    'record': record,
                    'error': str(e)
                })
        
        # Prepare response
        response_data = {
            'processed_count': processed_count,
            'failed_count': failed_count,
            'total_records': len(records)
        }
        
        # Record metrics
        metrics_manager.record_metric('events_processed', processed_count)
        metrics_manager.record_metric('events_failed', failed_count)
        
        response = APIResponse.success(
            data=response_data,
            message=f"Processed {processed_count}/{len(records)} events successfully"
        )
        
        log_api_response(response, "event_processor")
        return response
        
    except Exception as e:
        lambda_logger.error(f"Event processor error: {str(e)}")
        metrics_manager.record_metric('event_processor_errors', 1)
        
        response = APIResponse.error(
            message="Event processing failed",
            error_code="EVENT_PROCESSOR_ERROR",
            details=str(e)
        )
        
        log_api_response(response, "event_processor")
        return response


def process_single_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single event based on its type.
    
    Args:
        event_data: Event data to process
        
    Returns:
        Processing result
    """
    try:
        event_type = event_data.get('event_type')
        event_id = event_data.get('event_id')
        
        lambda_logger.info(f"Processing event {event_id} of type {event_type}")
        
        # Route event based on type
        if event_type == 'tenant.created':
            return process_tenant_created_event(event_data)
        elif event_type == 'tenant.updated':
            return process_tenant_updated_event(event_data)
        elif event_type == 'tenant.deleted':
            return process_tenant_deleted_event(event_data)
        elif event_type == 'user.created':
            return process_user_created_event(event_data)
        elif event_type == 'user.updated':
            return process_user_updated_event(event_data)
        elif event_type == 'payment.subscription_created':
            return process_payment_event(event_data)
        elif event_type == 'security.audit_log':
            return process_security_event(event_data)
        else:
            lambda_logger.warning(f"Unknown event type: {event_type}")
            return {'success': False, 'reason': f'Unknown event type: {event_type}'}
            
    except Exception as e:
        lambda_logger.error(f"Error processing single event: {str(e)}")
        return {'success': False, 'reason': str(e)}


def process_tenant_created_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process tenant created event."""
    try:
        # Store event in history
        store_event_in_history(event_data)
        
        # Trigger analytics update
        update_tenant_analytics(event_data)
        
        return {'success': True, 'event_type': 'tenant.created'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def process_tenant_updated_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process tenant updated event."""
    try:
        store_event_in_history(event_data)
        update_tenant_analytics(event_data)
        return {'success': True, 'event_type': 'tenant.updated'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def process_tenant_deleted_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process tenant deleted event."""
    try:
        store_event_in_history(event_data)
        cleanup_tenant_data(event_data)
        return {'success': True, 'event_type': 'tenant.deleted'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def process_user_created_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process user created event."""
    try:
        store_event_in_history(event_data)
        update_user_analytics(event_data)
        return {'success': True, 'event_type': 'user.created'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def process_user_updated_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process user updated event."""
    try:
        store_event_in_history(event_data)
        return {'success': True, 'event_type': 'user.updated'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def process_payment_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process payment related event."""
    try:
        store_event_in_history(event_data)
        update_payment_analytics(event_data)
        return {'success': True, 'event_type': 'payment'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def process_security_event(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process security related event."""
    try:
        store_event_in_history(event_data)
        update_security_metrics(event_data)
        return {'success': True, 'event_type': 'security'}
    except Exception as e:
        return {'success': False, 'reason': str(e)}


def store_event_in_history(event_data: Dict[str, Any]) -> None:
    """Store event in DynamoDB for history tracking."""
    try:
        # This would integrate with DynamoDB to store event history
        # Implementation would use shared.database module
        lambda_logger.info(f"Storing event {event_data.get('event_id')} in history")
        pass
    except Exception as e:
        lambda_logger.error(f"Failed to store event in history: {str(e)}")


def update_tenant_analytics(event_data: Dict[str, Any]) -> None:
    """Update tenant analytics based on event."""
    try:
        lambda_logger.info(f"Updating tenant analytics for event {event_data.get('event_id')}")
        pass
    except Exception as e:
        lambda_logger.error(f"Failed to update tenant analytics: {str(e)}")


def update_user_analytics(event_data: Dict[str, Any]) -> None:
    """Update user analytics based on event."""
    try:
        lambda_logger.info(f"Updating user analytics for event {event_data.get('event_id')}")
        pass
    except Exception as e:
        lambda_logger.error(f"Failed to update user analytics: {str(e)}")


def update_payment_analytics(event_data: Dict[str, Any]) -> None:
    """Update payment analytics based on event."""
    try:
        lambda_logger.info(f"Updating payment analytics for event {event_data.get('event_id')}")
        pass
    except Exception as e:
        lambda_logger.error(f"Failed to update payment analytics: {str(e)}")


def update_security_metrics(event_data: Dict[str, Any]) -> None:
    """Update security metrics based on event."""
    try:
        lambda_logger.info(f"Updating security metrics for event {event_data.get('event_id')}")
        pass
    except Exception as e:
        lambda_logger.error(f"Failed to update security metrics: {str(e)}")


def cleanup_tenant_data(event_data: Dict[str, Any]) -> None:
    """Cleanup tenant data when tenant is deleted."""
    try:
        lambda_logger.info(f"Cleaning up tenant data for event {event_data.get('event_id')}")
        pass
    except Exception as e:
        lambda_logger.error(f"Failed to cleanup tenant data: {str(e)}")
