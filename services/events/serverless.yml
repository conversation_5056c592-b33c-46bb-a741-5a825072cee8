# services/events/serverless.yml
# Events service configuration

service: agent-scl-events

# Custom configuration
custom:
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl
  
  # Reference shared layer
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName
    SNS_TOPIC_ARN:
      Ref: TenantEventsTopic
    SQS_QUEUE_URL:
      Ref: EventsQueue
    
  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"
    - Effect: Allow
      Action:
        - sns:Publish
        - sns:Subscribe
        - sns:Unsubscribe
        - sns:ListTopics
      Resource:
        - Ref: TenantEventsTopic
    - Effect: Allow
      Action:
        - sqs:SendMessage
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
        - sqs:GetQueueAttributes
        - sqs:PurgeQueue
      Resource:
        - Fn::GetAtt: [EventsQueue, Arn]
        - Fn::GetAtt: [EventsDeadLetterQueue, Arn]
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"

# Functions
functions:
  # Tenant events handler
  tenantEvents:
    handler: src.handlers.tenant_events.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - sns:
          arn:
            Ref: TenantEventsTopic
          topicName: ${self:custom.projectName}-${self:custom.stage}-tenant-events
    environment:
      FUNCTION_NAME: events_tenant_events
    tracing: Active

  # Event processor
  eventProcessor:
    handler: src.handlers.event_processor.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - sqs:
          arn:
            Fn::GetAtt: [EventsQueue, Arn]
          batchSize: 10
    environment:
      FUNCTION_NAME: events_processor
    tracing: Active

  # Event history
  eventHistory:
    handler: src.handlers.event_history.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /events/history
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: events_history
    tracing: Active

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'

# Plugins
plugins:
  - serverless-python-requirements

# Resources
resources:
  Description: Events service for Agent SCL platform

  Resources:
    # SNS Topic for tenant events
    TenantEventsTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:custom.projectName}-${self:custom.stage}-tenant-events
        DisplayName: "Tenant Events Topic"

    # Dead Letter Queue for failed events
    EventsDeadLetterQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.projectName}-${self:custom.stage}-events-dlq
        MessageRetentionPeriod: 1209600  # 14 days

    # Main events processing queue
    EventsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.projectName}-${self:custom.stage}-events-queue
        MessageRetentionPeriod: 1209600  # 14 days
        ReceiveMessageWaitTimeSeconds: 20  # Long polling
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt: [EventsDeadLetterQueue, Arn]
          maxReceiveCount: 3

    # SNS subscription to SQS queue
    EventsQueueSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        TopicArn:
          Ref: TenantEventsTopic
        Endpoint:
          Fn::GetAtt: [EventsQueue, Arn]
        Protocol: sqs
        RawMessageDelivery: true

    # SQS Queue Policy to allow SNS to send messages
    EventsQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        Queues:
          - Ref: EventsQueue
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: sns.amazonaws.com
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt: [EventsQueue, Arn]
              Condition:
                ArnEquals:
                  aws:SourceArn:
                    Ref: TenantEventsTopic

  Outputs:
    EventsServiceEndpoint:
      Description: "Events Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-events-${self:custom.stage}-EventsServiceEndpoint

    TenantEventsTopicArn:
      Description: "SNS Topic ARN for tenant events"
      Value:
        Ref: TenantEventsTopic
      Export:
        Name: sls-${self:custom.projectName}-events-${self:custom.stage}-TenantEventsTopicArn

    EventsQueueUrl:
      Description: "SQS Queue URL for events processing"
      Value:
        Ref: EventsQueue
      Export:
        Name: sls-${self:custom.projectName}-events-${self:custom.stage}-EventsQueueUrl

    EventsQueueArn:
      Description: "SQS Queue ARN for events processing"
      Value:
        Fn::GetAtt: [EventsQueue, Arn]
      Export:
        Name: sls-${self:custom.projectName}-events-${self:custom.stage}-EventsQueueArn
