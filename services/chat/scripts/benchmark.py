#!/usr/bin/env python3
# services/chat/scripts/benchmark.py
# Comprehensive benchmark script for chat system performance

import asyncio
import aiohttp
import time
import json
import statistics
import argparse
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Dict, Any, List
import websockets
import ssl

class ChatSystemBenchmark:
    """Comprehensive benchmark for chat system"""
    
    def __init__(self, base_url: str, websocket_url: str, api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.websocket_url = websocket_url
        self.api_key = api_key
        
        # Benchmark configuration
        self.config = {
            'concurrent_users': 100,
            'messages_per_user': 50,
            'test_duration': 300,  # 5 minutes
            'ramp_up_time': 60,    # 1 minute
            'file_upload_size': 1024 * 1024,  # 1MB
            'search_queries': 100,
            'cache_operations': 1000
        }
        
        # Results storage
        self.results = {
            'rest_api': {},
            'websocket': {},
            'file_operations': {},
            'search_performance': {},
            'cache_performance': {},
            'database_performance': {}
        }
        
        # Headers for API requests
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ChatBenchmark/1.0'
        }
        
        if self.api_key:
            self.headers['Authorization'] = f'Bearer {self.api_key}'
    
    async def benchmark_rest_api(self):
        """Benchmark REST API endpoints"""
        print("🔥 Benchmarking REST API...")
        
        endpoints = [
            ('GET', '/conversations', 'list_conversations'),
            ('POST', '/conversations', 'create_conversation'),
            ('GET', '/conversations/test-conv-123/messages', 'get_messages'),
            ('POST', '/conversations/test-conv-123/messages', 'send_message'),
            ('GET', '/search/messages?q=test', 'search_messages'),
            ('GET', '/analytics/metrics', 'get_metrics')
        ]
        
        async with aiohttp.ClientSession(headers=self.headers) as session:
            for method, endpoint, test_name in endpoints:
                print(f"  📊 Testing {test_name}...")
                
                response_times = []
                success_count = 0
                error_count = 0
                
                # Prepare request data
                data = None
                if method == 'POST':
                    if 'conversations' in endpoint and endpoint.endswith('conversations'):
                        data = {
                            'title': 'Benchmark Conversation',
                            'participants': ['user1', 'user2']
                        }
                    elif 'messages' in endpoint:
                        data = {
                            'content': 'Benchmark test message',
                            'type': 'text'
                        }
                
                # Run concurrent requests
                tasks = []
                for i in range(100):  # 100 requests per endpoint
                    task = self._make_request(session, method, endpoint, data)
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for result in results:
                    if isinstance(result, Exception):
                        error_count += 1
                    else:
                        response_time, status_code = result
                        response_times.append(response_time)
                        if 200 <= status_code < 300:
                            success_count += 1
                        else:
                            error_count += 1
                
                # Calculate metrics
                avg_response_time = statistics.mean(response_times) if response_times else 0
                p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else 0
                p99_response_time = statistics.quantiles(response_times, n=100)[98] if len(response_times) > 100 else 0
                throughput = success_count / max(response_times) if response_times else 0
                error_rate = error_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
                
                self.results['rest_api'][test_name] = {
                    'avg_response_time': avg_response_time,
                    'p95_response_time': p95_response_time,
                    'p99_response_time': p99_response_time,
                    'throughput': throughput,
                    'error_rate': error_rate,
                    'success_count': success_count,
                    'error_count': error_count
                }
                
                print(f"    ✅ Avg: {avg_response_time:.3f}s, P95: {p95_response_time:.3f}s, Errors: {error_rate:.2%}")
    
    async def benchmark_websocket(self):
        """Benchmark WebSocket connections and messaging"""
        print("🔗 Benchmarking WebSocket...")
        
        connection_times = []
        message_times = []
        success_connections = 0
        failed_connections = 0
        
        async def websocket_test(user_id):
            try:
                # Connect
                start_connect = time.time()
                
                # Create SSL context for secure connections
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                async with websockets.connect(
                    f"{self.websocket_url}?userId=user-{user_id}&tenantId=tenant-1",
                    ssl=ssl_context if self.websocket_url.startswith('wss') else None
                ) as websocket:
                    connect_time = time.time() - start_connect
                    connection_times.append(connect_time)
                    
                    # Send messages
                    for i in range(10):  # 10 messages per connection
                        start_message = time.time()
                        
                        message = {
                            'action': 'sendMessage',
                            'conversationId': f'conv-{user_id % 10}',
                            'content': f'Benchmark message {i} from user {user_id}',
                            'type': 'text'
                        }
                        
                        await websocket.send(json.dumps(message))
                        
                        # Wait for response (with timeout)
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            message_time = time.time() - start_message
                            message_times.append(message_time)
                        except asyncio.TimeoutError:
                            pass
                    
                    return True
                    
            except Exception as e:
                return False
        
        # Run concurrent WebSocket tests
        tasks = []
        for i in range(50):  # 50 concurrent connections
            task = websocket_test(i)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception) or result is False:
                failed_connections += 1
            else:
                success_connections += 1
        
        # Calculate metrics
        avg_connection_time = statistics.mean(connection_times) if connection_times else 0
        avg_message_time = statistics.mean(message_times) if message_times else 0
        connection_success_rate = success_connections / (success_connections + failed_connections) if (success_connections + failed_connections) > 0 else 0
        
        self.results['websocket'] = {
            'avg_connection_time': avg_connection_time,
            'avg_message_time': avg_message_time,
            'connection_success_rate': connection_success_rate,
            'successful_connections': success_connections,
            'failed_connections': failed_connections,
            'total_messages': len(message_times)
        }
        
        print(f"    ✅ Avg Connection: {avg_connection_time:.3f}s")
        print(f"    ✅ Avg Message: {avg_message_time:.3f}s")
        print(f"    ✅ Success Rate: {connection_success_rate:.2%}")
    
    async def benchmark_file_operations(self):
        """Benchmark file upload/download operations"""
        print("📁 Benchmarking File Operations...")
        
        # Create test file data
        test_file_data = b'0' * self.config['file_upload_size']  # 1MB of zeros
        
        upload_times = []
        download_times = []
        success_uploads = 0
        success_downloads = 0
        
        async with aiohttp.ClientSession(headers=self.headers) as session:
            # Test file uploads
            for i in range(20):  # 20 file uploads
                try:
                    start_time = time.time()
                    
                    # Prepare multipart data
                    data = aiohttp.FormData()
                    data.add_field('file', test_file_data, filename=f'benchmark-{i}.txt', content_type='text/plain')
                    data.add_field('conversationId', 'conv-benchmark')
                    
                    async with session.post(f'{self.base_url}/files/upload', data=data) as response:
                        if response.status == 200:
                            upload_time = time.time() - start_time
                            upload_times.append(upload_time)
                            success_uploads += 1
                            
                            # Test download
                            response_data = await response.json()
                            if 'fileId' in response_data:
                                start_download = time.time()
                                
                                async with session.get(f'{self.base_url}/files/{response_data["fileId"]}/download') as download_response:
                                    if download_response.status == 200:
                                        await download_response.read()  # Read the file content
                                        download_time = time.time() - start_download
                                        download_times.append(download_time)
                                        success_downloads += 1
                        
                except Exception as e:
                    pass
        
        # Calculate metrics
        avg_upload_time = statistics.mean(upload_times) if upload_times else 0
        avg_download_time = statistics.mean(download_times) if download_times else 0
        upload_throughput = (success_uploads * self.config['file_upload_size']) / sum(upload_times) if upload_times else 0
        
        self.results['file_operations'] = {
            'avg_upload_time': avg_upload_time,
            'avg_download_time': avg_download_time,
            'upload_throughput_bytes_per_sec': upload_throughput,
            'successful_uploads': success_uploads,
            'successful_downloads': success_downloads,
            'file_size_bytes': self.config['file_upload_size']
        }
        
        print(f"    ✅ Avg Upload: {avg_upload_time:.3f}s")
        print(f"    ✅ Avg Download: {avg_download_time:.3f}s")
        print(f"    ✅ Upload Throughput: {upload_throughput / 1024 / 1024:.2f} MB/s")
    
    async def benchmark_search_performance(self):
        """Benchmark search functionality"""
        print("🔍 Benchmarking Search Performance...")
        
        search_queries = [
            'test message',
            'important document',
            'meeting notes',
            'project update',
            'file attachment',
            'user conversation',
            'system notification',
            'error log',
            'performance metrics',
            'benchmark results'
        ]
        
        search_times = []
        success_count = 0
        
        async with aiohttp.ClientSession(headers=self.headers) as session:
            for query in search_queries:
                for i in range(10):  # 10 searches per query
                    try:
                        start_time = time.time()
                        
                        async with session.get(f'{self.base_url}/search/messages?q={query}&page=1&size=20') as response:
                            if response.status == 200:
                                await response.json()
                                search_time = time.time() - start_time
                                search_times.append(search_time)
                                success_count += 1
                    
                    except Exception as e:
                        pass
        
        # Calculate metrics
        avg_search_time = statistics.mean(search_times) if search_times else 0
        p95_search_time = statistics.quantiles(search_times, n=20)[18] if len(search_times) > 20 else 0
        search_throughput = success_count / sum(search_times) if search_times else 0
        
        self.results['search_performance'] = {
            'avg_search_time': avg_search_time,
            'p95_search_time': p95_search_time,
            'search_throughput': search_throughput,
            'successful_searches': success_count,
            'total_queries': len(search_queries) * 10
        }
        
        print(f"    ✅ Avg Search: {avg_search_time:.3f}s")
        print(f"    ✅ P95 Search: {p95_search_time:.3f}s")
        print(f"    ✅ Throughput: {search_throughput:.1f} searches/sec")
    
    async def _make_request(self, session, method, endpoint, data=None):
        """Make HTTP request and measure response time"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method == 'GET':
                async with session.get(url) as response:
                    await response.text()
                    return time.time() - start_time, response.status
            elif method == 'POST':
                async with session.post(url, json=data) as response:
                    await response.text()
                    return time.time() - start_time, response.status
            elif method == 'PUT':
                async with session.put(url, json=data) as response:
                    await response.text()
                    return time.time() - start_time, response.status
            elif method == 'DELETE':
                async with session.delete(url) as response:
                    await response.text()
                    return time.time() - start_time, response.status
        
        except Exception as e:
            return time.time() - start_time, 500
    
    def generate_report(self):
        """Generate comprehensive benchmark report"""
        print("\n" + "="*80)
        print("📊 CHAT SYSTEM BENCHMARK REPORT")
        print("="*80)
        print(f"Generated at: {datetime.now().isoformat()}")
        print(f"Base URL: {self.base_url}")
        print(f"WebSocket URL: {self.websocket_url}")
        
        # REST API Results
        if self.results['rest_api']:
            print("\n🔥 REST API PERFORMANCE:")
            print("-" * 40)
            for endpoint, metrics in self.results['rest_api'].items():
                print(f"{endpoint}:")
                print(f"  Avg Response Time: {metrics['avg_response_time']:.3f}s")
                print(f"  P95 Response Time: {metrics['p95_response_time']:.3f}s")
                print(f"  Throughput: {metrics['throughput']:.1f} req/sec")
                print(f"  Error Rate: {metrics['error_rate']:.2%}")
                print()
        
        # WebSocket Results
        if self.results['websocket']:
            print("🔗 WEBSOCKET PERFORMANCE:")
            print("-" * 40)
            ws_metrics = self.results['websocket']
            print(f"Avg Connection Time: {ws_metrics['avg_connection_time']:.3f}s")
            print(f"Avg Message Time: {ws_metrics['avg_message_time']:.3f}s")
            print(f"Connection Success Rate: {ws_metrics['connection_success_rate']:.2%}")
            print(f"Total Messages: {ws_metrics['total_messages']}")
            print()
        
        # File Operations Results
        if self.results['file_operations']:
            print("📁 FILE OPERATIONS PERFORMANCE:")
            print("-" * 40)
            file_metrics = self.results['file_operations']
            print(f"Avg Upload Time: {file_metrics['avg_upload_time']:.3f}s")
            print(f"Avg Download Time: {file_metrics['avg_download_time']:.3f}s")
            print(f"Upload Throughput: {file_metrics['upload_throughput_bytes_per_sec'] / 1024 / 1024:.2f} MB/s")
            print(f"Successful Uploads: {file_metrics['successful_uploads']}")
            print(f"Successful Downloads: {file_metrics['successful_downloads']}")
            print()
        
        # Search Performance Results
        if self.results['search_performance']:
            print("🔍 SEARCH PERFORMANCE:")
            print("-" * 40)
            search_metrics = self.results['search_performance']
            print(f"Avg Search Time: {search_metrics['avg_search_time']:.3f}s")
            print(f"P95 Search Time: {search_metrics['p95_search_time']:.3f}s")
            print(f"Search Throughput: {search_metrics['search_throughput']:.1f} searches/sec")
            print(f"Successful Searches: {search_metrics['successful_searches']}")
            print()
        
        # Overall Assessment
        print("📈 OVERALL ASSESSMENT:")
        print("-" * 40)
        
        # Performance thresholds
        rest_api_good = all(
            metrics['avg_response_time'] < 2.0 and metrics['error_rate'] < 0.05
            for metrics in self.results['rest_api'].values()
        ) if self.results['rest_api'] else True
        
        websocket_good = (
            self.results['websocket']['connection_success_rate'] > 0.95 and
            self.results['websocket']['avg_message_time'] < 1.0
        ) if self.results['websocket'] else True
        
        search_good = (
            self.results['search_performance']['avg_search_time'] < 1.0
        ) if self.results['search_performance'] else True
        
        overall_status = "✅ EXCELLENT" if all([rest_api_good, websocket_good, search_good]) else "⚠️ NEEDS OPTIMIZATION"
        
        print(f"REST API Performance: {'✅ Good' if rest_api_good else '⚠️ Poor'}")
        print(f"WebSocket Performance: {'✅ Good' if websocket_good else '⚠️ Poor'}")
        print(f"Search Performance: {'✅ Good' if search_good else '⚠️ Poor'}")
        print(f"Overall Status: {overall_status}")
        
        print("\n" + "="*80)
    
    async def run_full_benchmark(self):
        """Run complete benchmark suite"""
        print("🚀 Starting Chat System Benchmark...")
        print(f"Target: {self.base_url}")
        print(f"WebSocket: {self.websocket_url}")
        
        start_time = time.time()
        
        # Run all benchmarks
        await self.benchmark_rest_api()
        await self.benchmark_websocket()
        await self.benchmark_file_operations()
        await self.benchmark_search_performance()
        
        total_time = time.time() - start_time
        
        print(f"\n⏱️ Total benchmark time: {total_time:.2f} seconds")
        
        # Generate report
        self.generate_report()
        
        return self.results


async def main():
    """Main benchmark execution"""
    parser = argparse.ArgumentParser(description='Chat System Benchmark')
    parser.add_argument('--base-url', required=True, help='Base URL for REST API')
    parser.add_argument('--websocket-url', required=True, help='WebSocket URL')
    parser.add_argument('--api-key', help='API key for authentication')
    parser.add_argument('--output', help='Output file for results (JSON)')
    
    args = parser.parse_args()
    
    # Create and run benchmark
    benchmark = ChatSystemBenchmark(
        base_url=args.base_url,
        websocket_url=args.websocket_url,
        api_key=args.api_key
    )
    
    results = await benchmark.run_full_benchmark()
    
    # Save results if output file specified
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n💾 Results saved to: {args.output}")


if __name__ == '__main__':
    asyncio.run(main())
