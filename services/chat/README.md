# 💬 **Chat Service - Agent-SCL Platform**

## **🚀 Descripción General**

El Chat Service es el núcleo de gestión de mensajería del ecosistema **agent-scl**. Proporciona infraestructura completa para procesamiento de mensajes, gestión de conversaciones, integración con agentes IA, file sharing y analytics avanzados.

### **✨ Características Principales**
- 💬 **Gestión de Mensajes**: CRUD completo con validación robusta
- 🤖 **Integración con Agentes**: Procesamiento de respuestas IA
- 📁 **File Sharing**: Upload directo a S3 con previews
- 🔍 **Búsqueda Avanzada**: Full-text search con filtros
- 📊 **Analytics**: Métricas de uso y engagement
- 👁️ **Gestión de Presencia**: Estados online/offline
- 📡 **Real-time**: Coordinación con WebSocket Service
- 🔐 **Seguridad**: Autenticación JWT y tenant isolation

---

## **📋 Tabla de Contenidos**
1. [Instalación y Setup](#instalación-y-setup)
2. [Configuración](#configuración)
3. [API Reference](#api-reference)
4. [Ejemplos de Uso](#ejemplos-de-uso)
5. [File Sharing](#file-sharing)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)

---

## **🛠️ Instalación y Setup**

### **Prerrequisitos**
```bash
# Servicios requeridos (deben estar desplegados)
✅ agent-scl-shared-layer-dev
✅ agent-scl-auth-dev
✅ agent-scl-orchestrator-dev
✅ agent-scl-websocket-dev

# Herramientas de desarrollo
- Python 3.11+
- Serverless Framework 3.x
- AWS CLI configurado
- Node.js 18+ (para Serverless)
```

### **Instalación Local**
```bash
# 1. Clonar y navegar al directorio
cd services/chat

# 2. Instalar dependencias Python
pip install -r requirements.txt

# 3. Instalar Serverless plugins
npm install

# 4. Configurar variables de entorno
export STAGE=dev
export REGION=us-east-1
```

### **Configuración de Recursos**
```bash
# JWT Secret (ya debe existir del auth service)
aws secretsmanager describe-secret --secret-id "agent-scl/dev/jwt-secret"

# Verificar tabla DynamoDB
aws dynamodb describe-table --table-name "agent-scl-dev"

# Verificar S3 bucket para archivos
aws s3 ls s3://agent-scl-files-dev/
```

---

## **⚙️ Configuración**

### **Variables de Entorno**
```yaml
# Core Configuration
STAGE: dev                           # Ambiente de deployment
REGION: us-east-1                   # Región AWS
PROJECT_NAME: agent-scl             # Nombre del proyecto
DYNAMODB_TABLE: agent-scl-dev       # Tabla DynamoDB unificada

# Security
JWT_SECRET_NAME: agent-scl/dev/jwt-secret  # Secret en AWS Secrets Manager

# Service Integration
WEBSOCKET_SERVICE_URL: https://api.agent-scl.com/dev
ORCHESTRATOR_SERVICE_URL: https://api.agent-scl.com/dev
AGENT_SERVICE_URL: https://api.agent-scl.com/dev

# File Storage
S3_BUCKET: agent-scl-files-dev
MAX_FILE_SIZE_MB: 50
ALLOWED_FILE_TYPES: "jpg,jpeg,png,gif,pdf,doc,docx,txt"
```

---

## **📡 API Reference**

### **Core Message Endpoints**

#### **Process Message**
```http
POST /chat/messages/process
Authorization: Bearer JWT_TOKEN

{
    "message": {
        "conversation_id": "conv-123",
        "sender_id": "user-789",
        "content": "Hello there!"
    },
    "conversation": {
        "conversation_id": "conv-123",
        "tenant_id": "tenant-456"
    }
}
```

#### **Get Messages**
```http
GET /chat/conversations/{conversationId}/messages?limit=50
Authorization: Bearer JWT_TOKEN

Response:
{
    "messages": [
        {
            "message_id": "msg-123",
            "content": "Hello!",
            "sender_id": "user-789",
            "created_at": "2024-01-15T10:30:00Z",
            "status": "read"
        }
    ],
    "has_more": true
}
```

#### **Mark as Read**
```http
PUT /chat/messages/{messageId}/read
Authorization: Bearer JWT_TOKEN

Response:
{
    "success": true,
    "message_id": "msg-123",
    "status": "read"
}
```

### **File Sharing**

#### **Upload File**
```http
POST /chat/files/upload
Authorization: Bearer JWT_TOKEN

{
    "filename": "document.pdf",
    "content_type": "application/pdf",
    "size": 1024000
}

Response:
{
    "file_id": "file-abc123",
    "upload_url": "https://s3.amazonaws.com/..."
}
```

### **Search and Analytics**

#### **Search Messages**
```http
GET /chat/search?q=hello&limit=20
Authorization: Bearer JWT_TOKEN

Response:
{
    "results": [
        {
            "message_id": "msg-123",
            "content": "Hello there!",
            "relevance_score": 0.95
        }
    ]
}
```

---

## **🔗 Integración con Otros Servicios**

### **Message Orchestrator**
- Recibe requests de procesamiento de mensajes
- Coordina flujo de datos entre servicios
- Gestiona routing inteligente

### **WebSocket Service**
- Notificaciones en tiempo real
- Broadcasting de mensajes
- Gestión de presencia

### **Agent Service**
- Procesamiento de respuestas de agentes
- Coordinación de workflows híbridos
- Gestión de contexto de conversación

### Flujo Híbrido:
1. **Mensajes simples** → WebSocket directo
2. **Workflows complejos** → Agent Service → N8N
3. **Respuestas** → Chat Service → WebSocket broadcast

## 🚀 Deployment

```bash
# Desarrollo
serverless deploy --stage dev

# Staging
serverless deploy --stage staging

# Producción
serverless deploy --stage prod
```

## 🔐 Seguridad

- **JWT Authentication** - Todos los endpoints protegidos
- **Multi-tenant Isolation** - Aislamiento por tenant
- **Rate Limiting** - Control de frecuencia
- **File Validation** - Validación de archivos compartidos

## 📈 Monitoreo

### CloudWatch Metrics:
- Message throughput
- Response times
- Error rates
- WebSocket integration health

### Alarmas:
- High error rate
- High latency
- Failed WebSocket broadcasts

## 🧪 Testing

```bash
# Unit tests
pytest src/tests/

# Integration tests
pytest src/tests/integration/

# WebSocket tests
pytest src/tests/websocket/
```

## 📚 Documentación

- [API Documentation](./docs/api.md)
- [WebSocket Integration](./docs/websocket.md)
- [Agent Integration](./docs/agent-integration.md)

## 📝 Changelog

### v1.0.0 (2024-08-29) - ✅ DEPLOYED
- ✅ Procesamiento completo de mensajes
- ✅ Integración robusta con agentes IA
- ✅ File sharing con S3 y previews
- ✅ Búsqueda full-text avanzada
- ✅ Analytics y métricas comprehensivas
- ✅ Coordinación con WebSocket Service
- ✅ Seguridad y tenant isolation

### Próximas Versiones
- 🔄 Machine Learning para análisis de sentimientos
- 🔄 Encriptación end-to-end
- 🔄 Búsqueda semántica con embeddings

---

**📝 Documento actualizado**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
**🔗 Repositorio**: [agent-scl/services/chat](https://github.com/agent-scl/services/chat)
