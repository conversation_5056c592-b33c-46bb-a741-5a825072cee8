# services/chat/serverless.yml
# Chat service configuration for real-time messaging

service: ${self:custom.projectName}-chat

# Custom configuration
custom:
  # Project configuration
  projectName: agent-scl
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}

  # Stage-specific configuration
  stageConfig:
    dev:
      dynamodb:
        tableName: ${self:custom.projectName}-dev
    staging:
      dynamodb:
        tableName: ${self:custom.projectName}-staging
    prod:
      dynamodb:
        tableName: ${self:custom.projectName}-prod

# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  memorySize: 256
  timeout: 30
  
  # Environment variables
  environment:
    STAGE: ${self:custom.stage}
    REGION: ${self:custom.region}
    PROJECT_NAME: ${self:custom.projectName}
    DYNAMODB_TABLE: ${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}
    JWT_SECRET_NAME: ${self:custom.projectName}/${self:custom.stage}/jwt-secret
    WEBSOCKET_SERVICE_URL: ${cf:${self:custom.projectName}-websocket-${self:custom.stage}.ServiceEndpoint, ''}
    ORCHESTRATOR_SERVICE_URL: ${cf:${self:custom.projectName}-orchestrator-${self:custom.stage}.ServiceEndpoint, ''}
  
  # IAM role statements
  iam:
    role:
      statements:
        # DynamoDB permissions
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
            - dynamodb:BatchGetItem
            - dynamodb:BatchWriteItem
          Resource:
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}/index/*

        # CloudWatch permissions
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:${self:custom.region}:*:*
        
        # Secrets Manager permissions
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource:
            - arn:aws:secretsmanager:${self:custom.region}:*:secret:${self:custom.projectName}/${self:custom.stage}/*

# Functions
functions:
  # Process message from orchestrator
  processMessage:
    handler: src.handlers.process_message.handler
    description: Process messages from orchestrator
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /chat/messages/process
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: chat-process-message
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Process agent responses from orchestrator
  processAgentResponse:
    handler: src.handlers.process_agent_response.handler
    description: Process agent responses from orchestrator
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /chat/agent-responses
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: chat-process-agent-response
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Get conversation messages
  getMessages:
    handler: src.handlers.get_messages.handler
    description: Get messages for a conversation
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /chat/conversations/{conversationId}/messages
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: chat-get-messages
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Mark message as read
  markAsRead:
    handler: src.handlers.mark_as_read.handler
    description: Mark message as read
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /chat/messages/{messageId}/read
          method: put
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: chat-mark-as-read
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

# Plugins
plugins:
  - serverless-python-requirements

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'
    - '!src/**/.pytest_cache/**'
    - '!src/**/tests/**'

# Resources
resources:
  Outputs:
    ServiceEndpoint:
      Description: "Chat Service endpoint"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-chat-${self:custom.stage}-ServiceEndpoint
