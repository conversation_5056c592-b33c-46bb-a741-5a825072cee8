sequenceDiagram
    participant C as 👤 Client
    participant API as 🌐 Chat Service API
    participant FUH as 📁 File Upload Handler
    participant FUS as 📁 File Upload Service
    participant S3 as 📦 S3 Bucket
    participant DB as 🗄️ DynamoDB
    participant MS as 💬 Message Service
    participant RS as 📡 Realtime Service
    participant WS as 🔌 WebSocket Service
    participant Other as 👥 Other Users
    participant FPH as 👁️ File Preview Handler
    participant CS as 💾 Cache Service

    Note over C: User wants to share a file
    C->>API: POST /chat/files/upload
    Note over C,API: {filename: "document.pdf", size: 1024000, type: "application/pdf"}
    
    API->>FUH: Route to File Upload Handler
    FUH->>FUH: Validate file metadata
    FUH->>FUH: Check file size & type limits
    
    FUH->>FUS: generate_presigned_url(filename, content_type)
    FUS->>FUS: Generate unique file_id
    FUS->>S3: Generate presigned upload URL
    Note over S3: URL valid for 15 minutes
    S3-->>FUS: Presigned URL + upload fields
    
    FUS->>DB: Store file metadata (pending)
    Note over DB: PK: FILE#{file_id}<br/>SK: METADATA<br/>status: "pending"
    DB-->>FUS: Metadata stored
    
    FUS-->>FUH: {file_id, upload_url, upload_fields}
    FUH-->>API: Upload URL response
    API-->>C: {file_id, upload_url, upload_fields}
    
    Note over C: Client uploads file directly to S3
    C->>S3: PUT file using presigned URL
    S3-->>C: Upload successful
    
    Note over C: Client sends message with file attachment
    C->>API: POST /chat/messages/process
    Note over C,API: {content: "Check this document", attachments: [{file_id, filename}]}
    
    API->>MS: create_message(content, attachments)
    MS->>DB: Update file status to "attached"
    MS->>DB: Store message with file reference
    Note over DB: PK: MESSAGE#{message_id}<br/>attachments: [{file_id, filename, size}]
    
    MS->>RS: notify_realtime_delivery(message_with_file)
    RS->>WS: Broadcast message with file attachment
    WS->>Other: Real-time notification with file
    
    MS-->>API: Message created with file
    API-->>C: Success response
    
    rect rgb(245, 245, 255)
        Note over Other: Other user wants to preview file
        Other->>API: GET /chat/files/{file_id}/preview
        API->>FPH: Route to File Preview Handler
        
        FPH->>CS: check_cache(file_id, "preview")
        CS-->>FPH: Cache miss
        
        FPH->>FUS: get_file_metadata(file_id)
        FUS->>DB: Query file metadata
        DB-->>FUS: File metadata
        FUS-->>FPH: {filename, size, type, s3_key}
        
        FPH->>S3: Generate presigned download URL
        S3-->>FPH: Download URL
        
        FPH->>FPH: Generate preview (if image/pdf)
        FPH->>CS: cache_preview(file_id, preview_data)
        
        FPH-->>API: {download_url, preview_url, metadata}
        API-->>Other: File preview response
    end
    
    rect rgb(255, 245, 245)
        Note over C: File cleanup after retention period
        Note over FUS: Scheduled cleanup job runs
        FUS->>DB: Query files older than retention period
        DB-->>FUS: List of old files
        
        loop For each old file
            FUS->>S3: Delete file object
            FUS->>DB: Delete file metadata
            FUS->>CS: Invalidate cached previews
        end
        
        Note over FUS: Cleanup completed
    end
    
    Note over C,CS: File sharing lifecycle completed