sequenceDiagram
    participant M<PERSON> as 🎯 Message Orchestrator
    participant API as 🌐 Chat Service API
    participant PMH as 📨 Process Message Handler
    participant MS as 💬 Message Service
    participant DB as 🗄️ DynamoDB
    participant SS as 🔍 Search Service
    parameter RS as 📡 Realtime Service
    participant WS as 🔌 WebSocket Service
    participant C1 as 👤 Client 1
    participant C2 as 👤 Client 2
    participant AS as 📊 Analytics Service
    participant CW as 📊 CloudWatch

    Note over MO: User sends message via UI
    MO->>API: POST /chat/messages/process
    Note over MO,API: {message: {...}, conversation: {...}, source: "orchestrator"}
    
    API->>PMH: Route to Process Message Handler
    PMH->>PMH: Validate request structure
    PMH->>PMH: Extract message & conversation data
    
    Note over PMH: Create message record
    PMH->>MS: create_message(conversation_id, user_id, content)
    MS->>MS: Generate message_id & timestamp
    MS->>MS: Validate content & attachments
    MS->>DB: Store message record
    Note over DB: PK: MESSAGE#{message_id}<br/>SK: METADATA<br/>GSI1PK: CONVERSATION#{conversation_id}
    DB-->>MS: Message stored successfully
    MS-->>PMH: {message_id, created_at, status: "sent"}
    
    Note over PMH: Update search index
    PMH->>SS: index_message(message_id, content, metadata)
    SS->>SS: Extract searchable terms
    SS->>DB: Store search index record
    Note over DB: PK: SEARCH#{tenant_id}<br/>SK: TERM#{term}#{message_id}
    DB-->>SS: Index updated
    SS-->>PMH: Indexing complete
    
    Note over PMH: Coordinate real-time delivery
    PMH->>RS: notify_realtime_delivery(conversation_id, message)
    RS->>RS: Get conversation participants
    RS->>WS: POST /websocket/orchestrator/broadcast
    Note over RS,WS: {recipients: [user1, user2], notification: {...}}
    
    WS->>WS: Get active connections for recipients
    WS->>C1: Real-time message notification
    WS->>C2: Real-time message notification
    WS-->>RS: {delivered: 2, failed: 0}
    RS-->>PMH: Real-time delivery complete
    
    Note over PMH: Update analytics
    PMH->>AS: track_message_event(message_id, event_type: "created")
    AS->>AS: Aggregate metrics
    AS->>DB: Update analytics counters
    AS-->>PMH: Analytics updated
    
    Note over PMH: Log operation
    PMH->>CW: Log message processing metrics
    PMH->>CW: Log business operation
    
    PMH-->>API: 200 OK {message_id, status: "processed"}
    API-->>MO: Success response
    
    Note over C1,C2: Users see message in real-time
    
    rect rgb(245, 255, 245)
        Note over C1: User marks message as read
        C1->>API: PUT /chat/messages/{message_id}/read
        API->>MS: update_message_status(message_id, "read")
        MS->>DB: Update message status
        MS->>RS: notify_read_receipt(message_id, user_id)
        RS->>WS: Broadcast read receipt
        WS->>C2: Read receipt notification
    end
    
    Note over MO,CW: Message lifecycle completed