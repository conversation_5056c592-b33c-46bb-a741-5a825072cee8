# services/chat/src/utils/chat_config.py
# Chat service specific configuration using shared config

from shared.config import get_settings, get_database_config
from typing import Dict, Any

def get_chat_config() -> Dict[str, Any]:
    """Get chat service specific configuration"""
    settings = get_settings()
    
    return {
        # Message configuration
        'max_message_length': settings.get('max_message_length', 4000),
        'supported_message_types': settings.get('message_types', ['text', 'image', 'file', 'audio', 'video']),
        'max_attachments_per_message': settings.get('max_attachments_per_message', 10),
        'max_attachment_size_mb': settings.get('max_attachment_size_mb', 50),
        
        # Conversation configuration
        'max_participants_per_conversation': settings.get('max_participants_per_conversation', 100),
        'conversation_timeout_hours': settings.get('conversation_timeout_hours', 24),
        'auto_archive_days': settings.get('auto_archive_days', 30),
        
        # Performance configuration
        'message_batch_size': settings.get('message_batch_size', 25),
        'search_results_limit': settings.get('search_results_limit', 50),
        'cache_ttl_seconds': settings.get('cache_ttl_seconds', 300),
        
        # Agent routing configuration
        'agent_response_timeout': settings.get('agent_response_timeout', 30),
        'max_routing_attempts': settings.get('max_routing_attempts', 3),
        'fallback_to_human': settings.get('fallback_to_human', True),
        
        # Notification configuration
        'websocket_timeout': settings.get('websocket_timeout', 10),
        'notification_retry_attempts': settings.get('notification_retry_attempts', 3),
        'notification_batch_size': settings.get('notification_batch_size', 100),
        
        # Monitoring configuration
        'metrics_enabled': settings.get('metrics_enabled', True),
        'detailed_logging': settings.get('detailed_logging', False),
        'performance_tracking': settings.get('performance_tracking', True),
        
        # Security configuration
        'content_filtering_enabled': settings.get('content_filtering_enabled', True),
        'rate_limit_per_user': settings.get('rate_limit_per_user', 100),  # messages per minute
        'rate_limit_per_tenant': settings.get('rate_limit_per_tenant', 1000),  # messages per minute
    }

def get_monitoring_config() -> Dict[str, Any]:
    """Get monitoring specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('monitoring_enabled', True),
        'cloudwatch_enabled': settings.get('cloudwatch_enabled', True),
        'custom_metrics_enabled': settings.get('custom_metrics_enabled', True),
        'detailed_monitoring': settings.get('detailed_monitoring', True),
        
        # Metric collection settings
        'metric_buffer_size': settings.get('metric_buffer_size', 20),
        'metric_flush_interval': settings.get('metric_flush_interval', 30),
        'realtime_metrics_enabled': settings.get('realtime_metrics_enabled', True),
        
        # Performance thresholds
        'response_time_warning_ms': settings.get('response_time_warning_ms', 2000),
        'response_time_critical_ms': settings.get('response_time_critical_ms', 5000),
        'error_rate_warning': settings.get('error_rate_warning', 0.05),
        'error_rate_critical': settings.get('error_rate_critical', 0.10),
        'memory_usage_warning': settings.get('memory_usage_warning', 0.80),
        'memory_usage_critical': settings.get('memory_usage_critical', 0.90),
        
        # Alerting configuration
        'alerts_enabled': settings.get('alerts_enabled', True),
        'alert_sns_topic': settings.get('alert_sns_topic'),
        'alert_email': settings.get('alert_email'),
        'alert_slack_webhook': settings.get('alert_slack_webhook'),
        
        # Health check configuration
        'health_check_enabled': settings.get('health_check_enabled', True),
        'health_check_interval': settings.get('health_check_interval', 60),
        'health_check_timeout': settings.get('health_check_timeout', 10),
    }

def get_cache_config() -> Dict[str, Any]:
    """Get cache specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('cache_enabled', True),
        'redis_enabled': settings.get('redis_enabled', True),
        'memory_cache_enabled': settings.get('memory_cache_enabled', True),
        
        # TTL settings
        'user_session_ttl': settings.get('user_session_ttl', 3600),  # 1 hour
        'conversation_ttl': settings.get('conversation_ttl', 1800),  # 30 minutes
        'message_ttl': settings.get('message_ttl', 900),  # 15 minutes
        'search_results_ttl': settings.get('search_results_ttl', 300),  # 5 minutes
        'presence_ttl': settings.get('presence_ttl', 300),  # 5 minutes
        'typing_ttl': settings.get('typing_ttl', 30),  # 30 seconds
        
        # Performance settings
        'max_cache_size': settings.get('max_cache_size', 1000),
        'cache_batch_size': settings.get('cache_batch_size', 25),
        'cache_compression': settings.get('cache_compression', True),
        
        # Redis specific
        'redis_host': settings.get('redis_host'),
        'redis_port': settings.get('redis_port', 6379),
        'redis_password': settings.get('redis_password'),
        'redis_ssl': settings.get('redis_ssl', True),
        'redis_timeout': settings.get('redis_timeout', 5),
    }

def get_connection_pool_config() -> Dict[str, Any]:
    """Get connection pool configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('connection_pool_enabled', True),
        
        # WebSocket connection pool settings
        'max_websocket_connections': settings.get('max_websocket_connections', 10000),
        'connection_timeout': settings.get('connection_timeout', 300),  # 5 minutes
        'cleanup_interval': settings.get('cleanup_interval', 60),  # 1 minute
        'heartbeat_interval': settings.get('heartbeat_interval', 30),  # 30 seconds
        
        # Database connection pool settings
        'max_db_pool_size': settings.get('max_db_pool_size', 50),
        'db_connection_timeout': settings.get('db_connection_timeout', 30),
        'db_idle_timeout': settings.get('db_idle_timeout', 300),  # 5 minutes
        'db_max_retries': settings.get('db_max_retries', 3),
        
        # Pool optimization settings
        'pool_warmup_enabled': settings.get('pool_warmup_enabled', True),
        'pool_warmup_size': settings.get('pool_warmup_size', 10),
        'connection_reuse_enabled': settings.get('connection_reuse_enabled', True),
        'pool_monitoring_enabled': settings.get('pool_monitoring_enabled', True),
        
        # Performance settings
        'batch_size': settings.get('pool_batch_size', 25),
        'concurrent_connections': settings.get('concurrent_connections', 100),
        'rate_limit_per_connection': settings.get('rate_limit_per_connection', 100),  # messages per minute
        
        # Health check settings
        'health_check_enabled': settings.get('health_check_enabled', True),
        'health_check_interval': settings.get('health_check_interval', 60),  # 1 minute
        'unhealthy_threshold': settings.get('unhealthy_threshold', 3),
        
        # Metrics and monitoring
        'metrics_enabled': settings.get('pool_metrics_enabled', True),
        'detailed_metrics': settings.get('detailed_metrics', False),
        'metrics_interval': settings.get('metrics_interval', 300)  # 5 minutes
    }

def get_cdn_config() -> Dict[str, Any]:
    """Get CDN configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('cdn_enabled', True),
        'cloudfront_enabled': settings.get('cloudfront_enabled', True),
        
        # CloudFront settings
        'distribution_domain': settings.get('cdn_distribution_domain'),
        'origin_access_identity': settings.get('cdn_origin_access_identity'),
        'signed_urls_enabled': settings.get('cdn_signed_urls_enabled', True),
        'url_expiration_seconds': settings.get('cdn_url_expiration_seconds', 3600),  # 1 hour
        
        # Cache behaviors
        'cache_behaviors': {
            'images': {
                'ttl': settings.get('cdn_images_ttl', 86400),  # 24 hours
                'compress': settings.get('cdn_images_compress', True),
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'documents': {
                'ttl': settings.get('cdn_documents_ttl', 3600),  # 1 hour
                'compress': settings.get('cdn_documents_compress', True),
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'videos': {
                'ttl': settings.get('cdn_videos_ttl', 86400),  # 24 hours
                'compress': settings.get('cdn_videos_compress', False),
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'thumbnails': {
                'ttl': settings.get('cdn_thumbnails_ttl', 604800),  # 7 days
                'compress': settings.get('cdn_thumbnails_compress', True),
                'viewer_protocol_policy': 'redirect-to-https'
            }
        },
        
        # Performance settings
        'optimization_enabled': settings.get('cdn_optimization_enabled', True),
        'compression_enabled': settings.get('cdn_compression_enabled', True),
        'http2_enabled': settings.get('cdn_http2_enabled', True),
        'ipv6_enabled': settings.get('cdn_ipv6_enabled', True),
        
        # Security settings
        'waf_enabled': settings.get('cdn_waf_enabled', True),
        'geo_restriction_enabled': settings.get('cdn_geo_restriction_enabled', False),
        'allowed_countries': settings.get('cdn_allowed_countries', []),
        
        # Monitoring settings
        'logging_enabled': settings.get('cdn_logging_enabled', True),
        'real_time_logs': settings.get('cdn_real_time_logs', False)
    }
