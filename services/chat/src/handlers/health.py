# services/chat/src/handlers/health.py
# Health check and system status endpoints

import json
import asyncio
from typing import Dict, Any

from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.responses import APIResponse
from shared.middleware.resilience_middleware import rate_limit
from shared.decorators import measure_performance

from ..services.health_check_service import health_check_service
from ..services.monitoring_service import monitoring_service

@rate_limit(requests_per_minute=300)  # Higher limit for health checks
@measure_performance("chat_health_check")
def health_check_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Simple health check endpoint for load balancers
    GET /health
    """
    # Get simple health status
    health_status = health_check_service.get_simple_health()

    # Return appropriate status code
    if health_status['status'] in ['healthy', 'degraded']:
        return APIResponse.success(health_status, 200)
    else:
        return APIResponse.error(health_status, 503)  # Service Unavailable

@rate_limit(requests_per_minute=60)  # Lower limit for detailed checks
@measure_performance("chat_detailed_health_check")
def detailed_health_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Detailed health check endpoint with component diagnostics
    GET /health/detailed
    """
    # Check if force refresh is requested
    query_params = event.get('queryStringParameters') or {}
    force_refresh = query_params.get('refresh', '').lower() == 'true'

    # Get comprehensive health status
    health_status = asyncio.run(
        health_check_service.get_system_health(force_refresh=force_refresh)
    )

    # Return appropriate status code based on overall health
    status_code_map = {
        'healthy': 200,
        'degraded': 200,  # Still operational
        'unhealthy': 503,
        'critical': 503
    }

    status_code = status_code_map.get(health_status.get('status', 'critical'), 503)

    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        'body': json.dumps(health_status, indent=2)
    }

@logging_middleware
def status_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    System status endpoint with real-time metrics
    GET /status
    """
    try:
        lambda_logger.info("System status requested")
        
        # Get real-time metrics
        realtime_metrics = monitoring_service.get_realtime_metrics()
        
        # Get simple health status
        health_status = health_check_service.get_simple_health()
        
        # Build status response
        status_response = {
            'service': 'ChatService',
            'environment': event.get('headers', {}).get('X-Environment', 'unknown'),
            'timestamp': health_status['timestamp'],
            'health': health_status['status'],
            'metrics': realtime_metrics,
            'uptime': {
                'service_start': '2024-01-01T00:00:00Z',  # Would be actual service start time
                'current_session': context.aws_request_id if hasattr(context, 'aws_request_id') else 'unknown'
            }
        }
        
        return APIResponse.success(status_response)
        
    except Exception as e:
        lambda_logger.error("Status check failed", extra={
            'error': str(e)
        })
        return APIResponse.error("Status check failed", 500)

@logging_middleware
def metrics_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Metrics endpoint for monitoring systems
    GET /metrics
    """
    try:
        lambda_logger.info("Metrics requested")
        
        # Get query parameters
        query_params = event.get('queryStringParameters') or {}
        format_type = query_params.get('format', 'json').lower()
        
        # Get real-time metrics
        realtime_metrics = monitoring_service.get_realtime_metrics()
        
        if format_type == 'prometheus':
            # Return Prometheus format
            prometheus_metrics = _convert_to_prometheus_format(realtime_metrics)
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'text/plain; version=0.0.4'
                },
                'body': prometheus_metrics
            }
        else:
            # Return JSON format
            return APIResponse.success({
                'timestamp': realtime_metrics.get('timestamp'),
                'metrics': realtime_metrics
            })
        
    except Exception as e:
        lambda_logger.error("Metrics request failed", extra={
            'error': str(e)
        })
        return APIResponse.error("Metrics request failed", 500)

@logging_middleware
def readiness_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Readiness probe for Kubernetes-style deployments
    GET /ready
    """
    try:
        lambda_logger.debug("Readiness check requested")
        
        # Quick readiness checks
        readiness_checks = {
            'database_ready': True,  # Would check DB connectivity
            'cache_ready': True,     # Would check cache connectivity
            'config_loaded': True    # Would check configuration
        }
        
        all_ready = all(readiness_checks.values())
        
        response = {
            'ready': all_ready,
            'timestamp': monitoring_service.get_realtime_metrics().get('timestamp'),
            'checks': readiness_checks
        }
        
        if all_ready:
            return APIResponse.success(response, 200)
        else:
            return APIResponse.error(response, 503)
            
    except Exception as e:
        lambda_logger.error("Readiness check failed", extra={
            'error': str(e)
        })
        return APIResponse.error("Readiness check failed", 503)

@logging_middleware
def liveness_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Liveness probe for Kubernetes-style deployments
    GET /live
    """
    try:
        lambda_logger.debug("Liveness check requested")
        
        # Basic liveness check - if we can respond, we're alive
        response = {
            'alive': True,
            'timestamp': monitoring_service.get_realtime_metrics().get('timestamp'),
            'service': 'ChatService'
        }
        
        return APIResponse.success(response, 200)
        
    except Exception as e:
        lambda_logger.error("Liveness check failed", extra={
            'error': str(e)
        })
        return APIResponse.error("Liveness check failed", 503)

@logging_middleware
def version_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Version information endpoint
    GET /version
    """
    try:
        lambda_logger.debug("Version info requested")
        
        version_info = {
            'service': 'ChatService',
            'version': '1.0.0',  # Would come from build/deployment
            'build_date': '2024-01-01T00:00:00Z',  # Would come from build
            'git_commit': 'abc123',  # Would come from build
            'environment': event.get('headers', {}).get('X-Environment', 'unknown'),
            'api_version': 'v1',
            'features': [
                'real_time_chat',
                'file_sharing',
                'search',
                'analytics',
                'caching',
                'monitoring'
            ]
        }
        
        return APIResponse.success(version_info)
        
    except Exception as e:
        lambda_logger.error("Version request failed", extra={
            'error': str(e)
        })
        return APIResponse.error("Version request failed", 500)

def _convert_to_prometheus_format(metrics: Dict[str, Any]) -> str:
    """Convert metrics to Prometheus format"""
    try:
        prometheus_lines = []
        
        # Add help and type information
        prometheus_lines.extend([
            '# HELP chat_active_connections Number of active WebSocket connections',
            '# TYPE chat_active_connections gauge',
            f'chat_active_connections {metrics.get("active_connections", 0)}',
            '',
            '# HELP chat_messages_per_minute Messages processed per minute',
            '# TYPE chat_messages_per_minute gauge',
            f'chat_messages_per_minute {metrics.get("messages_per_minute", 0)}',
            '',
            '# HELP chat_avg_response_time_ms Average response time in milliseconds',
            '# TYPE chat_avg_response_time_ms gauge',
            f'chat_avg_response_time_ms {metrics.get("avg_response_time_ms", 0)}',
            '',
            '# HELP chat_error_rate Error rate (0-1)',
            '# TYPE chat_error_rate gauge',
            f'chat_error_rate {metrics.get("error_rate", 0)}',
            '',
            '# HELP chat_cache_hit_rate Cache hit rate (0-1)',
            '# TYPE chat_cache_hit_rate gauge',
            f'chat_cache_hit_rate {metrics.get("cache_hit_rate", 0)}',
            '',
            '# HELP chat_health_status System health status (1=healthy, 0=unhealthy)',
            '# TYPE chat_health_status gauge'
        ])
        
        # Convert health status to numeric
        health_status = metrics.get('health_status', 'unknown')
        health_numeric = 1 if health_status == 'healthy' else 0
        prometheus_lines.append(f'chat_health_status {health_numeric}')
        
        return '\n'.join(prometheus_lines)
        
    except Exception as e:
        lambda_logger.error("Failed to convert metrics to Prometheus format", extra={
            'error': str(e)
        })
        return f'# Error converting metrics: {str(e)}'
