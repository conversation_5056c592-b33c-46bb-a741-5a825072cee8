# services/chat/src/handlers/file_preview.py
# Handlers for file preview and download

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.file_upload_service import file_upload_service
from ..services.file_preview_service import file_preview_service
from ..services.file_notification_service import file_notification_service

def generate_preview_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Generate file preview or thumbnail
    
    POST /chat/files/{fileId}/preview
    {
        "previewType": "thumbnail",
        "size": "medium"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Generate preview request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract file ID from path
        path_parameters = event.get('pathParameters', {})
        file_id = path_parameters.get('fileId')
        
        if not file_id:
            return APIResponse.error("Missing fileId in path", 400)
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        preview_type = body.get('previewType', 'thumbnail')
        size = body.get('size', 'medium')
        
        # Validate parameters
        allowed_preview_types = ['thumbnail', 'preview']
        allowed_sizes = ['small', 'medium', 'large']
        
        if preview_type not in allowed_preview_types:
            return APIResponse.error(f"Invalid preview type. Allowed: {allowed_preview_types}", 400)
        
        if size not in allowed_sizes:
            return APIResponse.error(f"Invalid size. Allowed: {allowed_sizes}", 400)
        
        # Get file information
        success, file_data, error_msg = file_upload_service.get_file_info(
            file_id=file_id,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 404 if "not found" in error_msg.lower() else 403)
        
        # Generate preview
        success, preview_data, error_msg = file_preview_service.generate_file_preview(
            file_id=file_id,
            file_key=file_data['fileKey'],
            content_type=file_data['contentType'],
            preview_type=preview_type,
            size=size
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        # Prepare response
        response_data = {
            'fileId': file_id,
            'filename': file_data['filename'],
            'preview': preview_data,
            'generatedAt': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("Preview generated", extra={
            'file_id': file_id,
            'user_id': user_id,
            'preview_type': preview_type,
            'size': size
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to generate preview", extra={
            'request_id': request_id,
            'file_id': path_parameters.get('fileId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_download_url_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get secure download URL for file
    
    GET /chat/files/{fileId}/download?expires=3600
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get download URL request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract file ID from path
        path_parameters = event.get('pathParameters', {})
        file_id = path_parameters.get('fileId')
        
        if not file_id:
            return APIResponse.error("Missing fileId in path", 400)
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        expires_in = int(query_parameters.get('expires', 3600))
        
        # Validate expires_in
        if expires_in < 60 or expires_in > 86400:  # 1 minute to 24 hours
            expires_in = 3600  # Default to 1 hour
        
        # Get file information
        success, file_data, error_msg = file_upload_service.get_file_info(
            file_id=file_id,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 404 if "not found" in error_msg.lower() else 403)
        
        # Generate secure download URL
        success, download_url, error_msg = file_preview_service.generate_secure_download_url(
            file_key=file_data['fileKey'],
            filename=file_data['filename'],
            user_id=user_id,
            tenant_id=tenant_id,
            expires_in=expires_in
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        # Log download request (for analytics)
        lambda_logger.info("Download URL generated", extra={
            'file_id': file_id,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'filename': file_data['filename'],
            'expires_in': expires_in
        })
        
        # Notify file owner about download (if different user)
        if file_data.get('uploadedBy') != user_id:
            file_notification_service.notify_file_download(
                user_id=file_data.get('uploadedBy'),
                tenant_id=tenant_id,
                file_id=file_id,
                filename=file_data['filename'],
                downloaded_by=user_id
            )
        
        # Prepare response
        response_data = {
            'fileId': file_id,
            'filename': file_data['filename'],
            'downloadUrl': download_url,
            'expiresIn': expires_in,
            'expiresAt': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None,
            'fileSize': file_data.get('fileSize'),
            'contentType': file_data.get('contentType')
        }
        
        return APIResponse.success(response_data, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to get download URL", extra={
            'request_id': request_id,
            'file_id': path_parameters.get('fileId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_file_metadata_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get detailed file metadata
    
    GET /chat/files/{fileId}/metadata
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get file metadata request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract file ID from path
        path_parameters = event.get('pathParameters', {})
        file_id = path_parameters.get('fileId')
        
        if not file_id:
            return APIResponse.error("Missing fileId in path", 400)
        
        # Get file information
        success, file_data, error_msg = file_upload_service.get_file_info(
            file_id=file_id,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 404 if "not found" in error_msg.lower() else 403)
        
        # Get detailed metadata
        success, detailed_metadata, error_msg = file_preview_service.get_file_metadata(
            file_key=file_data['fileKey']
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        # Combine file data with detailed metadata
        combined_metadata = {
            **file_data,
            'detailedMetadata': detailed_metadata,
            'retrievedAt': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.debug("File metadata retrieved", extra={
            'file_id': file_id,
            'user_id': user_id,
            'filename': file_data['filename']
        })
        
        return APIResponse.success(combined_metadata, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get file metadata", extra={
            'request_id': request_id,
            'file_id': path_parameters.get('fileId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def batch_generate_previews_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Generate previews for multiple files
    
    POST /chat/files/batch-preview
    {
        "fileIds": ["file-123", "file-456"],
        "previewType": "thumbnail",
        "size": "medium"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Batch preview generation request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        file_ids = body.get('fileIds', [])
        preview_type = body.get('previewType', 'thumbnail')
        size = body.get('size', 'medium')
        
        # Validate parameters
        if not file_ids or not isinstance(file_ids, list):
            return APIResponse.error("Missing or invalid fileIds array", 400)
        
        if len(file_ids) > 20:  # Limit batch size
            return APIResponse.error("Too many files. Maximum 20 files per batch", 400)
        
        # Process each file
        results = []
        success_count = 0
        
        for file_id in file_ids:
            try:
                # Get file information
                success, file_data, error_msg = file_upload_service.get_file_info(
                    file_id=file_id,
                    user_id=user_id,
                    tenant_id=tenant_id
                )
                
                if not success:
                    results.append({
                        'fileId': file_id,
                        'success': False,
                        'error': error_msg
                    })
                    continue
                
                # Generate preview
                success, preview_data, error_msg = file_preview_service.generate_file_preview(
                    file_id=file_id,
                    file_key=file_data['fileKey'],
                    content_type=file_data['contentType'],
                    preview_type=preview_type,
                    size=size
                )
                
                if success:
                    results.append({
                        'fileId': file_id,
                        'success': True,
                        'preview': preview_data,
                        'filename': file_data['filename']
                    })
                    success_count += 1
                else:
                    results.append({
                        'fileId': file_id,
                        'success': False,
                        'error': error_msg
                    })
                    
            except Exception as e:
                results.append({
                    'fileId': file_id,
                    'success': False,
                    'error': str(e)
                })
        
        # Prepare response
        response_data = {
            'results': results,
            'summary': {
                'total': len(file_ids),
                'successful': success_count,
                'failed': len(file_ids) - success_count
            },
            'previewType': preview_type,
            'size': size,
            'processedAt': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("Batch preview generation completed", extra={
            'user_id': user_id,
            'total_files': len(file_ids),
            'successful': success_count,
            'failed': len(file_ids) - success_count
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to process batch preview generation", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
