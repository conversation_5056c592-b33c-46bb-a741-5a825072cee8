# services/chat/src/handlers/analytics.py
# Handlers for analytics and metrics

import json
from typing import Dict, Any
from datetime import datetime, timedelta

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.analytics_service import analytics_service

def get_tenant_metrics_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get aggregated metrics for tenant
    
    GET /chat/analytics/metrics?start_date=2024-01-01&end_date=2024-01-31&metrics=MessagesSent,FilesShared
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get tenant metrics request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Parse date range
        try:
            start_date_str = query_parameters.get('start_date')
            end_date_str = query_parameters.get('end_date')
            
            if start_date_str:
                start_date = datetime.fromisoformat(start_date_str)
            else:
                start_date = datetime.utcnow() - timedelta(days=30)  # Default 30 days
            
            if end_date_str:
                end_date = datetime.fromisoformat(end_date_str)
            else:
                end_date = datetime.utcnow()
                
        except ValueError:
            return APIResponse.error("Invalid date format. Use ISO format (YYYY-MM-DD)", 400)
        
        # Validate date range
        if start_date >= end_date:
            return APIResponse.error("start_date must be before end_date", 400)
        
        if (end_date - start_date).days > 365:
            return APIResponse.error("Date range cannot exceed 365 days", 400)
        
        # Parse metric types
        metric_types = None
        if query_parameters.get('metrics'):
            metric_types = query_parameters['metrics'].split(',')
            # Validate metric types
            allowed_metrics = ['MessagesSent', 'FilesShared', 'SearchQueries', 'ActiveUsers']
            invalid_metrics = [m for m in metric_types if m not in allowed_metrics]
            if invalid_metrics:
                return APIResponse.error(f"Invalid metrics: {invalid_metrics}. Allowed: {allowed_metrics}", 400)
        
        # Get metrics
        success, metrics_data, error_msg = analytics_service.get_tenant_metrics(
            tenant_id=tenant_id,
            start_date=start_date,
            end_date=end_date,
            metric_types=metric_types
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        lambda_logger.info("Tenant metrics retrieved", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'metric_count': len(metrics_data.get('metrics', {}))
        })
        
        return APIResponse.success(metrics_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get tenant metrics", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_real_time_stats_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get real-time statistics for tenant
    
    GET /chat/analytics/realtime
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get real-time stats request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Get real-time stats
        success, stats_data, error_msg = analytics_service.get_real_time_stats(tenant_id)
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        lambda_logger.info("Real-time stats retrieved", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'stats_count': len(stats_data.get('real_time_stats', {}))
        })
        
        return APIResponse.success(stats_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get real-time stats", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_usage_analytics_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get detailed usage analytics
    
    GET /chat/analytics/usage?period_days=30
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get usage analytics request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Parse period
        try:
            period_days = int(query_parameters.get('period_days', 30))
        except ValueError:
            return APIResponse.error("Invalid period_days. Must be a number", 400)
        
        # Validate period
        if period_days < 1 or period_days > 365:
            return APIResponse.error("period_days must be between 1 and 365", 400)
        
        # Get usage analytics
        success, analytics_data, error_msg = analytics_service.get_usage_analytics(
            tenant_id=tenant_id,
            period_days=period_days
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        lambda_logger.info("Usage analytics retrieved", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'period_days': period_days,
            'insights_count': len(analytics_data.get('insights', []))
        })
        
        return APIResponse.success(analytics_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get usage analytics", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def track_event_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Track custom analytics event
    
    POST /chat/analytics/track
    {
        "event_type": "message_sent",
        "properties": {
            "message_type": "text",
            "conversation_id": "conv-123",
            "agent_involved": true
        }
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Track event request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        event_type = body.get('event_type')
        if not event_type:
            return APIResponse.error("Missing required field: event_type", 400)
        
        properties = body.get('properties', {})
        
        # Track different event types
        success = False
        
        if event_type == 'message_sent':
            success = analytics_service.track_message_sent(
                tenant_id=tenant_id,
                user_id=user_id,
                conversation_id=properties.get('conversation_id', ''),
                message_type=properties.get('message_type', 'text'),
                agent_involved=properties.get('agent_involved', False)
            )
        
        elif event_type == 'file_shared':
            success = analytics_service.track_file_shared(
                tenant_id=tenant_id,
                user_id=user_id,
                conversation_id=properties.get('conversation_id', ''),
                file_type=properties.get('file_type', 'unknown'),
                file_size=properties.get('file_size', 0)
            )
        
        elif event_type == 'search_query':
            success = analytics_service.track_search_query(
                tenant_id=tenant_id,
                user_id=user_id,
                query=properties.get('query', ''),
                search_type=properties.get('search_type', 'messages'),
                results_count=properties.get('results_count', 0),
                search_time_ms=properties.get('search_time_ms', 0)
            )
        
        elif event_type == 'user_activity':
            success = analytics_service.track_user_activity(
                tenant_id=tenant_id,
                user_id=user_id,
                activity_type=properties.get('activity_type', 'unknown'),
                duration_seconds=properties.get('duration_seconds')
            )
        
        else:
            return APIResponse.error(f"Unsupported event_type: {event_type}", 400)
        
        if not success:
            return APIResponse.error("Failed to track event", 500)
        
        response_data = {
            'tracked': True,
            'event_type': event_type,
            'tenant_id': tenant_id,
            'user_id': user_id,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        lambda_logger.info("Event tracked", extra={
            'event_type': event_type,
            'tenant_id': tenant_id,
            'user_id': user_id
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to track event", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_analytics_dashboard_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get comprehensive analytics dashboard data
    
    GET /chat/analytics/dashboard?period_days=7
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get analytics dashboard request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Parse period
        try:
            period_days = int(query_parameters.get('period_days', 7))
        except ValueError:
            return APIResponse.error("Invalid period_days. Must be a number", 400)
        
        # Validate period
        if period_days < 1 or period_days > 90:
            return APIResponse.error("period_days must be between 1 and 90", 400)
        
        # Get comprehensive dashboard data
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=period_days)
        
        # Get metrics
        metrics_success, metrics_data, metrics_error = analytics_service.get_tenant_metrics(
            tenant_id=tenant_id,
            start_date=start_date,
            end_date=end_date
        )
        
        # Get real-time stats
        realtime_success, realtime_data, realtime_error = analytics_service.get_real_time_stats(tenant_id)
        
        # Get usage analytics
        usage_success, usage_data, usage_error = analytics_service.get_usage_analytics(
            tenant_id=tenant_id,
            period_days=period_days
        )
        
        # Combine all data
        dashboard_data = {
            'tenant_id': tenant_id,
            'period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
                'days': period_days
            },
            'metrics': metrics_data if metrics_success else {},
            'real_time': realtime_data if realtime_success else {},
            'usage': usage_data if usage_success else {},
            'status': {
                'metrics_available': metrics_success,
                'realtime_available': realtime_success,
                'usage_available': usage_success
            },
            'errors': {
                'metrics': metrics_error if not metrics_success else None,
                'realtime': realtime_error if not realtime_success else None,
                'usage': usage_error if not usage_success else None
            },
            'generated_at': datetime.utcnow().isoformat()
        }
        
        lambda_logger.info("Analytics dashboard data retrieved", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'period_days': period_days,
            'metrics_success': metrics_success,
            'realtime_success': realtime_success,
            'usage_success': usage_success
        })
        
        return APIResponse.success(dashboard_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get analytics dashboard", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
