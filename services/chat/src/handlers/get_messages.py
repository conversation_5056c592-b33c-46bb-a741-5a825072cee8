# services/chat/src/handlers/get_messages.py
# Handler for getting conversation message history

from ..common.imports import *
@chat_handler("get_messages", require_auth=True)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get conversation message history
    
    GET /chat/messages/{conversationId}?limit=50&lastEvaluatedKey=...
    """
    
    try:
        # Extract auth context from event (set by @require_auth decorator)
        auth_context = event.get('auth_context')
        if not auth_context:
            return error_response("Authentication required", 401)

        user_id = auth_context.user_id
        tenant_id = auth_context.tenant_id

        # Extract conversation ID from path
        conversation_id = extract_path_parameter(event, 'conversationId')

        # Extract query parameters
        limit = min(int(extract_query_parameter(event, 'limit', 50)), 100)  # Max 100 messages
        cursor = extract_query_parameter(event, 'cursor')

        # Get services using dependency injection
        message_service = get_message_service()
        monitoring_service = get_monitoring_service()

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            event.get('path', '/chat/messages'),
            tenant_id=tenant_id,
            user_id=user_id
        )

        # Get conversation messages with pagination
        messages, next_cursor = message_service.list_conversation_messages(
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            limit=limit,
            cursor=cursor
        )

        # Record metrics
        monitoring_service.record_message_metrics(
            message_type="get_messages",
            processing_time_ms=0,  # Will be calculated by decorator
            tenant_id=tenant_id,
            success=True
        )

        # Log operation
        log_chat_operation(
            operation="get_messages",
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            user_id=user_id,
            status="completed",
            details={
                'message_count': len(messages),
                'limit': limit,
                'has_more': next_cursor is not None
            }
        )

        # Prepare response
        response_data = {
            'conversationId': conversation_id,
            'messages': messages,
            'pagination': {
                'limit': limit,
                'count': len(messages),
                'hasMore': next_cursor is not None,
                'nextCursor': next_cursor
            },
            'metadata': {
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        }

        return success_response(response_data)

    except Exception as e:
        return handle_chat_error(e, "get_messages", {
            'conversation_id': locals().get('conversation_id'),
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
