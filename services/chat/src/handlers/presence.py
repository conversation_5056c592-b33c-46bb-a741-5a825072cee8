# services/chat/src/handlers/presence.py
# Handler for user presence management

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.presence_service import presence_service

def connect_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle user connection for presence tracking
    
    POST /chat/presence/connect
    {
        "userId": "user-123",
        "connectionId": "conn-123",
        "metadata": {}
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Presence connect request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['userId', 'connectionId']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        user_id = body['userId']
        connection_id = body['connectionId']
        metadata = body.get('metadata', {})
        
        # Validate user access (user can only update their own presence)
        if user_id != user_context['user_id']:
            return APIResponse.error("Access denied: can only update own presence", 403)
        
        # Handle user connection
        success, error_msg = presence_service.handle_user_connect(
            user_id=user_id,
            tenant_id=tenant_id,
            connection_count_delta=1,
            metadata=metadata
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'userId': user_id,
            'status': 'online',
            'connectionId': connection_id,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("User connected - presence updated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'connection_id': connection_id
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to handle presence connect", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def disconnect_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle user disconnection for presence tracking
    
    POST /chat/presence/disconnect
    {
        "userId": "user-123",
        "connectionId": "conn-123"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Presence disconnect request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['userId', 'connectionId']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        user_id = body['userId']
        connection_id = body['connectionId']
        
        # Validate user access
        if user_id != user_context['user_id']:
            return APIResponse.error("Access denied: can only update own presence", 403)
        
        # Handle user disconnection
        success, error_msg = presence_service.handle_user_disconnect(
            user_id=user_id,
            tenant_id=tenant_id,
            connection_count_delta=-1
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'userId': user_id,
            'connectionId': connection_id,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("User disconnected - presence updated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'connection_id': connection_id
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to handle presence disconnect", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_user_presence_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get user presence status
    
    GET /chat/presence/{userId}
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get user presence request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Extract user ID from path
        path_parameters = event.get('pathParameters', {})
        target_user_id = path_parameters.get('userId')
        
        if not target_user_id:
            return APIResponse.error("Missing userId in path", 400)
        
        # Get user presence
        success, presence_data, error_msg = presence_service.get_user_presence(
            user_id=target_user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            if "not found" in error_msg.lower():
                return APIResponse.error("User presence not found", 404)
            else:
                return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'userId': target_user_id,
            'presence': presence_data,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.debug("User presence retrieved", extra={
            'target_user_id': target_user_id,
            'tenant_id': tenant_id,
            'status': presence_data.get('status') if presence_data else None
        })
        
        return APIResponse.success(response_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get user presence", extra={
            'request_id': request_id,
            'target_user_id': path_parameters.get('userId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def update_user_status_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update user presence status
    
    PATCH /chat/presence/{userId}
    {
        "status": "online" | "offline" | "away" | "busy"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Update user status request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Extract user ID from path
        path_parameters = event.get('pathParameters', {})
        target_user_id = path_parameters.get('userId')
        
        if not target_user_id:
            return APIResponse.error("Missing userId in path", 400)
        
        # Validate user access (user can only update their own status)
        if target_user_id != user_context['user_id']:
            return APIResponse.error("Access denied: can only update own status", 403)
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        if 'status' not in body:
            return APIResponse.error("Missing required field: status", 400)
        
        status = body['status']
        
        # Update user presence status
        success, error_msg = presence_service.update_user_presence(
            user_id=target_user_id,
            tenant_id=tenant_id,
            status=status
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'userId': target_user_id,
            'status': status,
            'updatedAt': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("User status updated", extra={
            'user_id': target_user_id,
            'tenant_id': tenant_id,
            'status': status
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to update user status", extra={
            'request_id': request_id,
            'target_user_id': path_parameters.get('userId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_tenant_presence_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get presence for all users in tenant
    
    GET /chat/presence?limit=50
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get tenant presence request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        limit = int(query_parameters.get('limit', 50))
        
        # Validate limit
        if limit > 100:
            limit = 100
        elif limit < 1:
            limit = 50
        
        # Get tenant presence
        success, presence_list, error_msg = presence_service.get_tenant_presence(
            tenant_id=tenant_id,
            limit=limit
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'tenantId': tenant_id,
            'presence': presence_list,
            'count': len(presence_list),
            'limit': limit,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.debug("Tenant presence retrieved", extra={
            'tenant_id': tenant_id,
            'presence_count': len(presence_list),
            'limit': limit
        })
        
        return APIResponse.success(response_data, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to get tenant presence", extra={
            'request_id': request_id,
            'tenant_id': user_context['tenant_id'] if 'user_context' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
