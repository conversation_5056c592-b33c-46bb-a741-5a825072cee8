# services/chat/src/handlers/agent_typing.py
# Handler for agent typing simulation management

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.agent_typing_service import agent_typing_service

def start_agent_typing_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Start agent typing simulation
    
    POST /chat/agent-typing/start
    {
        "conversationId": "conv-123",
        "agentId": "agent-feedo",
        "agentType": "feedo",
        "estimatedProcessingTime": 5.0
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Start agent typing request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['conversationId', 'agentId', 'agentType']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        conversation_id = body['conversationId']
        agent_id = body['agentId']
        agent_type = body['agentType']
        estimated_processing_time = body.get('estimatedProcessingTime')
        
        # Start typing simulation
        success, error_msg = agent_typing_service.start_agent_typing_simulation(
            conversation_id=conversation_id,
            agent_id=agent_id,
            agent_type=agent_type,
            tenant_id=tenant_id,
            estimated_processing_time=estimated_processing_time
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'status': 'started',
            'conversationId': conversation_id,
            'agentId': agent_id,
            'agentType': agent_type,
            'estimatedProcessingTime': estimated_processing_time,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("Agent typing simulation started", extra={
            'conversation_id': conversation_id,
            'agent_id': agent_id,
            'agent_type': agent_type,
            'estimated_processing_time': estimated_processing_time
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to start agent typing", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def stop_agent_typing_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Stop agent typing simulation
    
    POST /chat/agent-typing/stop
    {
        "conversationId": "conv-123",
        "agentId": "agent-feedo"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Stop agent typing request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['conversationId', 'agentId']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        conversation_id = body['conversationId']
        agent_id = body['agentId']
        
        # Stop typing simulation
        success, error_msg = agent_typing_service.stop_agent_typing_simulation(
            conversation_id=conversation_id,
            agent_id=agent_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'status': 'stopped',
            'conversationId': conversation_id,
            'agentId': agent_id,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("Agent typing simulation stopped", extra={
            'conversation_id': conversation_id,
            'agent_id': agent_id
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to stop agent typing", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_agent_typing_status_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get agent typing status and statistics
    
    GET /chat/agent-typing/status
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get agent typing status request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        
        # Get active sessions and stats
        active_sessions = agent_typing_service.get_active_sessions()
        typing_stats = agent_typing_service.get_typing_stats()
        
        # Prepare response
        response_data = {
            'activeSessions': active_sessions,
            'stats': typing_stats,
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.debug("Agent typing status retrieved", extra={
            'active_sessions_count': len(active_sessions),
            'stats': typing_stats
        })
        
        return APIResponse.success(response_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get agent typing status", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_conversation_typing_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get typing status for specific conversation
    
    GET /chat/agent-typing/conversation/{conversationId}
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get conversation typing status request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        
        # Extract conversation ID from path
        path_parameters = event.get('pathParameters', {})
        conversation_id = path_parameters.get('conversationId')
        
        if not conversation_id:
            return APIResponse.error("Missing conversationId in path", 400)
        
        # Get active sessions for conversation
        active_sessions = agent_typing_service.get_active_sessions()
        conversation_sessions = {
            session_key: session_data 
            for session_key, session_data in active_sessions.items()
            if session_data.get('conversation_id') == conversation_id
        }
        
        # Prepare response
        response_data = {
            'conversationId': conversation_id,
            'activeSessions': conversation_sessions,
            'sessionCount': len(conversation_sessions),
            'agentsTyping': [
                {
                    'agentId': session_data.get('agent_id'),
                    'agentType': session_data.get('agent_type'),
                    'startedAt': session_data.get('started_at').isoformat() if session_data.get('started_at') else None
                }
                for session_data in conversation_sessions.values()
            ],
            'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.debug("Conversation typing status retrieved", extra={
            'conversation_id': conversation_id,
            'active_sessions_count': len(conversation_sessions)
        })
        
        return APIResponse.success(response_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get conversation typing status", extra={
            'request_id': request_id,
            'conversation_id': path_parameters.get('conversationId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
