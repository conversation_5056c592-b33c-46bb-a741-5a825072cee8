# services/chat/src/handlers/process_agent_response.py
# Handler for processing agent responses from the orchestrator

"""
Process Agent Response Handler for Chat Service

This handler receives agent responses from the Message Orchestrator and processes them
for delivery to users. It handles:
1. Agent responses from Feedo, Forecaster, etc.
2. Delivery to the appropriate user
3. Real-time notifications via WebSocket

POST /chat/agent-responses
{
    "message": {
        "message_id": "msg-123",
        "conversation_id": "conv-456",
        "tenant_id": "tenant-789",
        "sender_id": "feedo",
        "sender_type": "agent",
        "sender_name": "Feedo",
        "content": "Based on your logistics data, I recommend...",
        "agent_id": "feedo",
        "agent_type": "feedo"
    },
    "conversation": {
        "conversation_id": "conv-456",
        "conversation_type": "user_to_agent",
        "tenant_id": "tenant-789",
        "agent_id": "feedo",
        "agent_type": "feedo"
    },
    "source": "orchestrator"
}
"""

from ..common.imports import *


@chat_handler("process_agent_response", require_auth=False)  # Auth handled by orchestrator
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process an agent response from the orchestrator for delivery to users.
    
    This handler is called by the Message Orchestrator when an agent
    has responded and the message needs to be delivered to the user.
    """
    
    try:
        # Parse request body
        body = parse_request_body(event)
        
        # Validate required fields
        validate_required_fields(body, ['message', 'conversation', 'source'])
        
        # Verify this request is from the orchestrator
        if body.get('source') != 'orchestrator':
            return error_response("This endpoint is only accessible from the orchestrator", 403)
        
        message_data = body['message']
        conversation_data = body['conversation']
        
        # Validate message and conversation data
        validate_required_fields(message_data, [
            'message_id', 'conversation_id', 'tenant_id', 'sender_id', 'content'
        ])
        
        validate_required_fields(conversation_data, [
            'conversation_id', 'conversation_type', 'tenant_id'
        ])
        
        # Create unified model objects
        conversation = Conversation.from_dict(conversation_data)
        message = Message.from_dict(message_data)

        # Validate this is an agent response
        if message.sender_type != "agent":
            return error_response("This handler only processes agent responses", 400)

        if conversation.conversation_type != ConversationType.USER_TO_AGENT:
            return error_response("This handler only processes user-to-agent conversations", 400)

        # Handle document attachments if present
        if message.message_type == 'document' and message.attachments:
            # Validate document attachments
            for attachment in message.attachments:
                if not attachment.get('url') or not attachment.get('filename'):
                    return error_response("Invalid document attachment format", 400)

                # Log document received from agent
                lambda_logger.info(f"Document received from agent {message.agent_id}", extra={
                    'filename': attachment.get('filename'),
                    'file_size': attachment.get('size'),
                    'file_type': attachment.get('type'),
                    'conversation_id': conversation.conversation_id
                })
        
        # Get services
        message_service = get_message_service()
        notification_service = get_notification_service()
        
        # Log agent response processing start
        log_chat_operation(
            operation="process_agent_response",
            conversation_id=conversation.conversation_id,
            tenant_id=conversation.tenant_id,
            user_id="system",  # Agent response processing
            status="started",
            details={
                'message_id': message.message_id,
                'agent_id': message.agent_id,
                'agent_type': message.agent_type,
                'content_length': len(message.content),
                'reply_to': message.reply_to_message_id
            }
        )
        
        # Store agent response in database
        success, stored_message, error_msg = message_service.store_agent_response(
            message=message,
            conversation=conversation
        )
        
        if not success:
            log_chat_operation(
                operation="process_agent_response",
                conversation_id=conversation.conversation_id,
                tenant_id=conversation.tenant_id,
                user_id="system",
                status="failed",
                details={'error': f"Failed to store agent response: {error_msg}"}
            )
            return error_response(f"Failed to store agent response: {error_msg}", 500)
        
        # Update conversation with new message
        conversation.update_last_message(message.created_at)
        
        # Store updated conversation
        conversation_success, conversation_error = message_service.update_conversation(conversation)
        if not conversation_success:
            lambda_logger.warning(f"Failed to update conversation: {conversation_error}")
        
        # Get user participants to notify
        user_recipients = []
        for participant in conversation.get_user_participants():
            user_recipients.append(participant.participant_id)
        
        # Send real-time notifications to users
        notification_success = True
        notification_error = None
        
        if user_recipients:
            notification_success, notification_error = notification_service.notify_agent_response(
                message=message,
                conversation=conversation,
                recipients=user_recipients
            )
        
        # Mark message as delivered
        message.mark_as_delivered()
        
        # Update message status in database
        message_service.update_message_status(
            message_id=message.message_id,
            status=message.status,
            tenant_id=conversation.tenant_id
        )
        
        # Log successful processing
        log_chat_operation(
            operation="process_agent_response",
            conversation_id=conversation.conversation_id,
            tenant_id=conversation.tenant_id,
            user_id="system",
            status="completed",
            details={
                'message_id': message.message_id,
                'agent_id': message.agent_id,
                'agent_type': message.agent_type,
                'users_notified': len(user_recipients),
                'notification_sent': notification_success,
                'message_status': message.status.value
            }
        )
        
        return success_response({
            'message': "Agent response processed and delivered successfully",
            'result': {
                'message_id': message.message_id,
                'conversation_id': conversation.conversation_id,
                'agent_id': message.agent_id,
                'agent_type': message.agent_type,
                'status': message.status.value,
                'users_notified': len(user_recipients),
                'notification_success': notification_success,
                'timestamp': message.updated_at.isoformat()
            }
        }, 200)
    
    except Exception as e:
        return handle_chat_error(e, "process_agent_response", {
            'message_data': locals().get('message_data'),
            'conversation_data': locals().get('conversation_data')
        })
