# services/chat/src/handlers/process_message.py
# Handler for processing messages from the orchestrator

"""
Process Message Handler for Chat Service

This handler receives messages from the Message Orchestrator and processes them
for chat communication. It handles:
1. User-to-user chat messages
2. Agent responses that need to be delivered to users
3. Real-time delivery coordination with WebSocket service

POST /chat/messages/process
{
    "message": {
        "message_id": "msg-123",
        "conversation_id": "conv-456",
        "tenant_id": "tenant-789",
        "sender_id": "user-123",
        "sender_type": "user",
        "content": "Hello there!"
    },
    "conversation": {
        "conversation_id": "conv-456",
        "conversation_type": "user_to_user",
        "tenant_id": "tenant-789"
    },
    "source": "orchestrator"
}
"""

from ..common.imports import *


@chat_handler("process_message", require_auth=False)  # Auth handled by orchestrator
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process a message from the orchestrator for chat communication.
    
    This handler is called by the Message Orchestrator when a message
    needs to be processed for chat delivery.
    """
    
    try:
        # Parse request body
        body = parse_request_body(event)
        
        # Validate required fields
        validate_required_fields(body, ['message', 'conversation', 'source'])
        
        # Verify this request is from the orchestrator
        if body.get('source') != 'orchestrator':
            return error_response("This endpoint is only accessible from the orchestrator", 403)
        
        message_data = body['message']
        conversation_data = body['conversation']
        
        # Validate message and conversation data
        validate_required_fields(message_data, [
            'message_id', 'conversation_id', 'tenant_id', 'sender_id', 'content'
        ])
        
        validate_required_fields(conversation_data, [
            'conversation_id', 'conversation_type', 'tenant_id'
        ])
        
        # Create unified model objects
        conversation = Conversation.from_dict(conversation_data)
        message = Message.from_dict(message_data)
        
        # Get services
        message_service = get_message_service()
        notification_service = get_notification_service()
        
        # Log message processing start
        log_chat_operation(
            operation="process_chat_message",
            conversation_id=conversation.conversation_id,
            tenant_id=conversation.tenant_id,
            user_id=message.sender_id,
            status="started",
            details={
                'message_id': message.message_id,
                'conversation_type': conversation.conversation_type.value,
                'sender_type': message.sender_type,
                'content_length': len(message.content)
            }
        )
        
        # Store message in database
        success, stored_message, error_msg = message_service.store_chat_message(
            message=message,
            conversation=conversation
        )
        
        if not success:
            log_chat_operation(
                operation="process_chat_message",
                conversation_id=conversation.conversation_id,
                tenant_id=conversation.tenant_id,
                user_id=message.sender_id,
                status="failed",
                details={'error': f"Failed to store message: {error_msg}"}
            )
            return error_response(f"Failed to store message: {error_msg}", 500)
        
        # Update conversation with new message
        conversation.update_last_message(message.created_at)
        
        # Store updated conversation
        conversation_success, conversation_error = message_service.update_conversation(conversation)
        if not conversation_success:
            lambda_logger.warning(f"Failed to update conversation: {conversation_error}")
        
        # Determine recipients for notifications
        recipients = []
        if conversation.conversation_type == ConversationType.USER_TO_USER:
            # For user-to-user chat, notify the other user
            for participant in conversation.get_user_participants():
                if participant.participant_id != message.sender_id:
                    recipients.append(participant.participant_id)
        elif conversation.conversation_type == ConversationType.USER_TO_AGENT:
            # For agent responses, notify the user
            if message.sender_type == "agent":
                for participant in conversation.get_user_participants():
                    recipients.append(participant.participant_id)
        
        # Send real-time notifications
        notification_success = True
        notification_error = None
        
        if recipients:
            notification_success, notification_error = notification_service.notify_new_message(
                message=message,
                conversation=conversation,
                recipients=recipients
            )
        
        # Mark message as delivered
        message.mark_as_delivered()
        
        # Update message status in database
        message_service.update_message_status(
            message_id=message.message_id,
            status=message.status,
            tenant_id=conversation.tenant_id
        )
        
        # Log successful processing
        log_chat_operation(
            operation="process_chat_message",
            conversation_id=conversation.conversation_id,
            tenant_id=conversation.tenant_id,
            user_id=message.sender_id,
            status="completed",
            details={
                'message_id': message.message_id,
                'conversation_type': conversation.conversation_type.value,
                'recipients_count': len(recipients),
                'notification_sent': notification_success,
                'message_status': message.status.value
            }
        )
        
        return success_response({
            'message': "Message processed and delivered successfully",
            'result': {
                'message_id': message.message_id,
                'conversation_id': conversation.conversation_id,
                'conversation_type': conversation.conversation_type.value,
                'status': message.status.value,
                'recipients_notified': len(recipients),
                'notification_success': notification_success,
                'timestamp': message.updated_at.isoformat()
            }
        }, 200)
    
    except Exception as e:
        return handle_chat_error(e, "process_message", {
            'message_data': locals().get('message_data'),
            'conversation_data': locals().get('conversation_data')
        })
