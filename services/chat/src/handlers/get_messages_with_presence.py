# services/chat/src/handlers/get_messages_with_presence.py
# Handler for getting conversation messages with user presence information

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.message_service import message_service
from ..utils.config import config

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get conversation message history with user presence information
    
    GET /chat/messages/{conversationId}/with-presence?limit=50&lastEvaluatedKey=...
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get messages with presence request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract conversation ID from path
        path_parameters = event.get('pathParameters', {})
        conversation_id = path_parameters.get('conversationId')
        
        if not conversation_id:
            return APIResponse.error("Missing conversationId in path", 400)
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Parse pagination parameters
        limit = int(query_parameters.get('limit', config.default_page_size))
        last_evaluated_key = query_parameters.get('lastEvaluatedKey')
        include_presence = query_parameters.get('includePresence', 'true').lower() == 'true'
        
        # Validate limit
        if limit > config.max_page_size:
            limit = config.max_page_size
        elif limit < 1:
            limit = config.default_page_size
        
        # Get conversation messages with presence
        if include_presence:
            success, result, error_msg = message_service.get_conversation_messages_with_presence(
                conversation_id=conversation_id,
                user_id=user_id,
                tenant_id=tenant_id,
                limit=limit,
                last_evaluated_key=last_evaluated_key
            )
        else:
            # Fallback to regular messages
            success, result, error_msg = message_service.get_conversation_messages(
                conversation_id=conversation_id,
                user_id=user_id,
                tenant_id=tenant_id,
                limit=limit,
                last_evaluated_key=last_evaluated_key
            )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_data = {
            'conversationId': conversation_id,
            'messages': result.get('messages', []),
            'pagination': {
                'limit': limit,
                'count': result.get('count', 0),
                'hasMore': result.get('hasMore', False),
                'lastEvaluatedKey': result.get('lastEvaluatedKey')
            },
            'metadata': {
                'requestId': request_id,
                'includePresence': include_presence,
                'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
            }
        }
        
        # Add presence information if available
        if include_presence and 'presence' in result:
            response_data['presence'] = result['presence']
            response_data['presenceTimestamp'] = result.get('presenceTimestamp')
        
        lambda_logger.info("Messages with presence retrieved successfully", extra={
            'conversation_id': conversation_id,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'message_count': result.get('count', 0),
            'has_more': result.get('hasMore', False),
            'include_presence': include_presence,
            'presence_users': len(result.get('presence', {})) if include_presence else 0
        })
        
        return APIResponse.success(response_data, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to get messages with presence", extra={
            'request_id': request_id,
            'conversation_id': path_parameters.get('conversationId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
