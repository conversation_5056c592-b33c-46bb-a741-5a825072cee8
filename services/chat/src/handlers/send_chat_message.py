# services/chat/src/handlers/send_chat_message.py
# Handler for sending real-time chat messages between users
# NOTE: This handler now works with the unified message orchestrator

from ..common.imports import *

@chat_handler("send_chat_message", require_auth=True)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Send real-time chat message between users (NOT to agents)
    
    POST /chat/messages
    {
        "recipientUserId": "user-456",  # Required for user-to-user chat
        "content": "Hello there!",
        "type": "text",
        "attachments": []
    }
    
    Note: For agent communication, use Agent Service endpoints
    """

    try:
        # Extract auth context from event (set by @require_auth decorator)
        auth_context = event.get('auth_context')
        if not auth_context:
            return error_response("Authentication required", 401)

        # Parse and validate request body
        body = parse_request_body(event)
        
        # Validate required fields for user-to-user chat
        validate_required_fields(body, ['recipientUserId', 'content', 'type'])

        # Get services using dependency injection
        message_service = get_message_service()
        notification_service = get_notification_service()
        monitoring_service = get_monitoring_service()

        sender_user_id = auth_context.user_id
        tenant_id = auth_context.tenant_id
        recipient_user_id = body['recipientUserId']
        content = body['content']
        message_type = body['type']
        attachments = body.get('attachments', [])

        # Validate that sender and recipient are in the same tenant
        validate_tenant_access(auth_context, tenant_id)

        # Create or get conversation between users
        conversation_id = f"chat_{min(sender_user_id, recipient_user_id)}_{max(sender_user_id, recipient_user_id)}"

        # Log message operation start
        log_message_operation(
            operation="send_chat_message",
            message_id="pending",
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            user_id=sender_user_id,
            status="started",
            details={
                'message_type': message_type,
                'content_length': len(content),
                'attachments_count': len(attachments),
                'recipient_user_id': recipient_user_id
            }
        )

        # Create message in database
        success, message_data, error_msg = message_service.create_chat_message(
            conversation_id=conversation_id,
            sender_user_id=sender_user_id,
            recipient_user_id=recipient_user_id,
            tenant_id=tenant_id,
            content=content,
            message_type=message_type,
            attachments=attachments
        )

        if not success:
            log_message_operation(
                operation="send_chat_message",
                message_id="failed",
                conversation_id=conversation_id,
                tenant_id=tenant_id,
                user_id=sender_user_id,
                status="failed",
                details={'error': error_msg}
            )
            return error_response(f"Failed to create message: {error_msg}", 500)

        # Get message ID from created message
        message_id = message_data.get('messageId', str(uuid.uuid4()))

        # Send real-time notification via WebSocket to recipient
        notification_success, notification_error = notification_service.notify_new_chat_message(
            conversation_id=conversation_id,
            message_data=message_data,
            sender_user_id=sender_user_id,
            recipient_user_id=recipient_user_id,
            tenant_id=tenant_id
        )

        # Record metrics
        monitoring_service.record_message_metrics(
            message_type=f"chat_{message_type}",
            processing_time_ms=0,  # Will be calculated by decorator
            tenant_id=tenant_id,
            success=True
        )

        # Log successful completion
        log_message_operation(
            operation="send_chat_message",
            message_id=message_id,
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            user_id=sender_user_id,
            status="completed",
            details={
                'message_type': message_type,
                'notification_sent': notification_success,
                'recipient_user_id': recipient_user_id
            }
        )

        # Return success response
        return success_response({
            'message': {
                'messageId': message_id,
                'conversationId': conversation_id,
                'senderUserId': sender_user_id,
                'recipientUserId': recipient_user_id,
                'content': content,
                'type': message_type,
                'status': 'sent',
                'timestamp': message_data.get('timestamp', datetime.now(timezone.utc).isoformat())
            },
            'notification': {
                'websocket_sent': notification_success,
                'error': notification_error
            }
        }, 201)

    except Exception as e:
        return handle_chat_error(e, "send_chat_message", {
            'conversation_id': locals().get('conversation_id'),
            'sender_user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'recipient_user_id': locals().get('recipient_user_id'),
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
