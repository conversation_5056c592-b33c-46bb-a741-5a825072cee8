# services/chat/src/handlers/agent_webhook.py
# Webhook handler for receiving agent responses from N8N

import json
import hmac
import hashlib
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}

from ..services.agent_response_processor import agent_response_processor
from ..utils.config import config

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Webhook handler for N8N agent responses
    
    POST /chat/webhook/agent-response
    {
        "conversationId": "conv-123",
        "agentId": "agent-123",
        "agentName": "Feedo",
        "agentType": "feedo",
        "response": {
            "content": "Based on your logistics data...",
            "type": "text",
            "confidence": 0.95,
            "metadata": {
                "processingTime": 2.5,
                "dataSourcesUsed": ["inventory", "routes"],
                "recommendations": [...]
            }
        },
        "requestId": "req-123",
        "timestamp": "2024-01-01T10:00:00Z",
        "signature": "sha256=..."
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Agent webhook request received", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path'),
        'source_ip': event.get('requestContext', {}).get('identity', {}).get('sourceIp')
    })
    
    try:
        # Validate webhook signature
        if not validate_webhook_signature(event):
            lambda_logger.warning("Invalid webhook signature", extra={
                'request_id': request_id,
                'source_ip': event.get('requestContext', {}).get('identity', {}).get('sourceIp')
            })
            return APIResponse.error("Invalid signature", 401)
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['conversationId', 'agentId', 'response']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        conversation_id = body['conversationId']
        agent_id = body['agentId']
        agent_name = body.get('agentName', 'Agent')
        agent_type = body.get('agentType', 'unknown')
        response_data = body['response']
        webhook_request_id = body.get('requestId')
        timestamp = body.get('timestamp')
        
        # Validate response structure
        if not isinstance(response_data, dict) or 'content' not in response_data:
            return APIResponse.error("Invalid response structure", 400)
        
        # Process agent response
        success, processed_response, error_msg = agent_response_processor.process_agent_response(
            conversation_id=conversation_id,
            agent_id=agent_id,
            agent_name=agent_name,
            agent_type=agent_type,
            response_data=response_data,
            webhook_request_id=webhook_request_id,
            timestamp=timestamp
        )
        
        if not success:
            lambda_logger.error("Failed to process agent response", extra={
                'request_id': request_id,
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'error': error_msg
            })
            return APIResponse.error(error_msg, 400)
        
        # Prepare response
        response_payload = {
            'status': 'success',
            'conversationId': conversation_id,
            'agentId': agent_id,
            'messageId': processed_response.get('messageId'),
            'timestamp': processed_response.get('timestamp'),
            'processingTime': processed_response.get('processingTime'),
            'webhookRequestId': webhook_request_id
        }
        
        lambda_logger.info("Agent response processed successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'agent_id': agent_id,
            'agent_name': agent_name,
            'agent_type': agent_type,
            'message_id': processed_response.get('messageId'),
            'webhook_request_id': webhook_request_id
        })
        
        return APIResponse.success(response_payload, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Agent webhook handler error", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def validate_webhook_signature(event: Dict[str, Any]) -> bool:
    """
    Validate webhook signature from N8N
    
    Returns:
        True if signature is valid, False otherwise
    """
    try:
        # Get signature from headers
        headers = event.get('headers', {})
        signature_header = headers.get('X-N8N-Signature') or headers.get('x-n8n-signature')
        
        if not signature_header:
            lambda_logger.warning("Missing signature header")
            return False
        
        # Get webhook secret from config
        webhook_secret = config.get_n8n_config().get('webhook_secret')
        if not webhook_secret:
            lambda_logger.warning("Webhook secret not configured")
            return False
        
        # Get request body
        body = event.get('body', '')
        if not body:
            lambda_logger.warning("Empty request body")
            return False
        
        # Calculate expected signature
        expected_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            body.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures
        expected_header = f"sha256={expected_signature}"
        
        # Use secure comparison to prevent timing attacks
        is_valid = hmac.compare_digest(signature_header, expected_header)
        
        if not is_valid:
            lambda_logger.warning("Signature mismatch", extra={
                'received_signature': signature_header[:20] + '...',
                'expected_signature': expected_header[:20] + '...'
            })
        
        return is_valid
        
    except Exception as e:
        lambda_logger.error("Error validating webhook signature", extra={
            'error': str(e)
        })
        return False


def health_check_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Health check endpoint for N8N webhook
    
    GET /chat/webhook/health
    """
    
    lambda_logger.debug("Webhook health check")
    
    return APIResponse.success({
        'status': 'healthy',
        'service': 'chat-agent-webhook',
        'timestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None,
        'version': '1.0.0'
    }, 200)


def webhook_info_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Webhook information endpoint
    
    GET /chat/webhook/info
    """
    
    lambda_logger.debug("Webhook info request")
    
    webhook_info = {
        'service': 'chat-agent-webhook',
        'version': '1.0.0',
        'endpoints': {
            'agent_response': {
                'method': 'POST',
                'path': '/chat/webhook/agent-response',
                'description': 'Receive agent responses from N8N',
                'authentication': 'HMAC-SHA256 signature'
            },
            'health': {
                'method': 'GET',
                'path': '/chat/webhook/health',
                'description': 'Health check endpoint'
            }
        },
        'signature': {
            'algorithm': 'HMAC-SHA256',
            'header': 'X-N8N-Signature',
            'format': 'sha256={hash}'
        },
        'required_fields': [
            'conversationId',
            'agentId',
            'response.content'
        ],
        'optional_fields': [
            'agentName',
            'agentType',
            'response.type',
            'response.confidence',
            'response.metadata',
            'requestId',
            'timestamp'
        ]
    }
    
    return APIResponse.success(webhook_info, 200)
