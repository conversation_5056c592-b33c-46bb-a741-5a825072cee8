# services/chat/src/handlers/message_status.py
# Handler for updating message status (delivered, read)

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.message_service import message_service
from ..services.notification_service import notification_service

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update message status
    
    PATCH /chat/messages/{messageId}/status
    {
        "status": "delivered" | "read"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Update message status request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract message ID from path
        path_parameters = event.get('pathParameters', {})
        message_id = path_parameters.get('messageId')
        
        if not message_id:
            return APIResponse.error("Missing messageId in path", 400)
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        if 'status' not in body:
            return APIResponse.error("Missing required field: status", 400)
        
        status = body['status']
        
        # Validate status value
        valid_statuses = ['delivered', 'read']
        if status not in valid_statuses:
            return APIResponse.error(f"Invalid status. Must be one of: {valid_statuses}", 400)
        
        # Update message status
        success, error_msg = message_service.update_message_status(
            message_id=message_id,
            status=status,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Get message details for notification
        message_success, message_data, message_error = message_service.get_message(
            message_id=message_id,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if message_success and message_data:
            conversation_id = message_data.get('conversationId')
            
            # Send status update notification
            notification_success, notification_error = notification_service.notify_message_status_update(
                message_id=message_id,
                conversation_id=conversation_id,
                status=status,
                user_id=user_id,
                tenant_id=tenant_id
            )
            
            if not notification_success:
                lambda_logger.warning("Failed to send status update notification", extra={
                    'message_id': message_id,
                    'status': status,
                    'error': notification_error
                })
        
        # Prepare response
        response_data = {
            'messageId': message_id,
            'status': status,
            'updatedBy': user_id,
            'updatedAt': message_data.get('updatedAt') if message_data else None,
            'notification': {
                'sent': notification_success if message_success else False,
                'error': notification_error if not notification_success else None
            }
        }
        
        lambda_logger.info("Message status updated successfully", extra={
            'message_id': message_id,
            'status': status,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'notification_sent': notification_success if message_success else False
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to update message status", extra={
            'request_id': request_id,
            'message_id': path_parameters.get('messageId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
