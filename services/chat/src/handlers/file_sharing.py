# services/chat/src/handlers/file_sharing.py
# Handlers for file sharing in chat

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.file_upload_service import file_upload_service
from ..services.message_service import message_service
from ..utils.config import config

def request_upload_url_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Request presigned URL for file upload
    
    POST /chat/files/upload-url
    {
        "filename": "document.pdf",
        "contentType": "application/pdf",
        "fileSize": 1024000,
        "conversationId": "conv-123"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("File upload URL request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['filename', 'contentType', 'fileSize']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        filename = body['filename']
        content_type = body['contentType']
        file_size = body['fileSize']
        conversation_id = body.get('conversationId')
        
        # Validate file size
        file_config = config.get_file_config()
        max_size = file_config['max_file_size_mb'] * 1024 * 1024
        
        if file_size > max_size:
            return APIResponse.error(f"File too large. Maximum size: {file_config['max_file_size_mb']}MB", 400)
        
        # Generate presigned upload URL
        success, upload_data, error_msg = file_upload_service.generate_presigned_upload_url(
            user_id=user_id,
            tenant_id=tenant_id,
            filename=filename,
            content_type=content_type,
            file_size=file_size
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Add conversation context
        upload_data['conversationId'] = conversation_id
        upload_data['uploadedBy'] = user_id
        upload_data['tenantId'] = tenant_id
        
        lambda_logger.info("Upload URL generated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'filename': filename,
            'file_size': file_size,
            'file_id': upload_data['fileId'],
            'conversation_id': conversation_id
        })
        
        return APIResponse.success(upload_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to generate upload URL", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def confirm_upload_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Confirm file upload and create message
    
    POST /chat/files/confirm-upload
    {
        "fileId": "file-123",
        "filename": "document.pdf",
        "conversationId": "conv-123",
        "messageContent": "Here's the document you requested"
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("File upload confirmation request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate required fields
        required_fields = ['fileId', 'filename', 'conversationId']
        for field in required_fields:
            if field not in body:
                return APIResponse.error(f"Missing required field: {field}", 400)
        
        file_id = body['fileId']
        filename = body['filename']
        conversation_id = body['conversationId']
        message_content = body.get('messageContent', '')
        
        # Confirm file upload
        success, file_data, error_msg = file_upload_service.confirm_file_upload(
            file_id=file_id,
            user_id=user_id,
            tenant_id=tenant_id,
            filename=filename,
            conversation_id=conversation_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 400)
        
        # Create chat message with file attachment
        attachment = {
            'type': 'file',
            'fileId': file_id,
            'filename': filename,
            'fileSize': file_data.get('fileSize'),
            'contentType': file_data.get('contentType'),
            'fileType': file_data.get('fileType'),
            'downloadUrl': file_data.get('downloadUrl'),
            'uploadedAt': file_data.get('uploadedAt')
        }
        
        # Send message with file attachment
        success, message_data, error_msg = message_service.send_message(
            conversation_id=conversation_id,
            user_id=user_id,
            tenant_id=tenant_id,
            content=message_content or f"📎 {filename}",
            message_type='file',
            attachments=[attachment]
        )
        
        if not success:
            lambda_logger.warning("Failed to send file message", extra={
                'file_id': file_id,
                'conversation_id': conversation_id,
                'error': error_msg
            })
            # File is uploaded but message failed - still return success
        
        # Prepare response
        response_data = {
            'fileId': file_id,
            'filename': filename,
            'fileData': file_data,
            'messageId': message_data.get('messageId') if success else None,
            'conversationId': conversation_id,
            'uploadConfirmed': True,
            'messageSent': success
        }
        
        lambda_logger.info("File upload confirmed", extra={
            'file_id': file_id,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'conversation_id': conversation_id,
            'filename': filename,
            'message_sent': success
        })
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to confirm file upload", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def get_file_info_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get file information and download URL
    
    GET /chat/files/{fileId}
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Get file info request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract file ID from path
        path_parameters = event.get('pathParameters', {})
        file_id = path_parameters.get('fileId')
        
        if not file_id:
            return APIResponse.error("Missing fileId in path", 400)
        
        # Get file information
        success, file_data, error_msg = file_upload_service.get_file_info(
            file_id=file_id,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 404 if "not found" in error_msg.lower() else 400)
        
        lambda_logger.debug("File info retrieved", extra={
            'file_id': file_id,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'filename': file_data.get('filename')
        })
        
        return APIResponse.success(file_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get file info", extra={
            'request_id': request_id,
            'file_id': path_parameters.get('fileId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def delete_file_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Delete file
    
    DELETE /chat/files/{fileId}
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Delete file request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract file ID from path
        path_parameters = event.get('pathParameters', {})
        file_id = path_parameters.get('fileId')
        
        if not file_id:
            return APIResponse.error("Missing fileId in path", 400)
        
        # Delete file
        success, error_msg = file_upload_service.delete_file(
            file_id=file_id,
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        if not success:
            return APIResponse.error(error_msg, 404 if "not found" in error_msg.lower() else 403)
        
        # Prepare response
        response_data = {
            'fileId': file_id,
            'deleted': True,
            'deletedBy': user_id,
            'deletedAt': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
        }
        
        lambda_logger.info("File deleted", extra={
            'file_id': file_id,
            'user_id': user_id,
            'tenant_id': tenant_id
        })
        
        return APIResponse.success(response_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to delete file", extra={
            'request_id': request_id,
            'file_id': path_parameters.get('fileId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def list_conversation_files_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    List files in conversation
    
    GET /chat/conversations/{conversationId}/files?limit=20&lastEvaluatedKey=...
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("List conversation files request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract conversation ID from path
        path_parameters = event.get('pathParameters', {})
        conversation_id = path_parameters.get('conversationId')
        
        if not conversation_id:
            return APIResponse.error("Missing conversationId in path", 400)
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        limit = int(query_parameters.get('limit', 20))
        last_evaluated_key = query_parameters.get('lastEvaluatedKey')
        
        # Validate limit
        if limit > 100:
            limit = 100
        elif limit < 1:
            limit = 20
        
        # Get conversation files (this would query database)
        # For now, return mock data
        files = [
            {
                'fileId': 'file-123',
                'filename': 'document.pdf',
                'fileSize': 1024000,
                'contentType': 'application/pdf',
                'fileType': 'document',
                'uploadedBy': user_id,
                'uploadedAt': '2024-01-01T10:00:00Z',
                'downloadUrl': 'https://example.com/download/file-123'
            }
        ]
        
        # Prepare response
        response_data = {
            'conversationId': conversation_id,
            'files': files,
            'pagination': {
                'limit': limit,
                'count': len(files),
                'hasMore': False,
                'lastEvaluatedKey': None
            }
        }
        
        lambda_logger.debug("Conversation files listed", extra={
            'conversation_id': conversation_id,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'file_count': len(files)
        })
        
        return APIResponse.success(response_data, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to list conversation files", extra={
            'request_id': request_id,
            'conversation_id': path_parameters.get('conversationId') if 'path_parameters' in locals() else None,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
