# services/chat/src/handlers/search_indexing.py
# Handlers for automatic search indexing

import json
from typing import Dict, Any

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.search_indexing_service import search_indexing_service

def index_message_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Index a message (triggered by DynamoDB stream or direct call)
    
    This handler can be triggered by:
    1. DynamoDB stream when a message is created
    2. Direct API call for manual indexing
    3. SQS message for async indexing
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Index message request", extra={
        'request_id': request_id,
        'event_source': event.get('eventSource', 'unknown')
    })
    
    try:
        # Handle different event sources
        if event.get('Records'):
            # DynamoDB stream or SQS event
            return handle_stream_records(event['Records'], 'message')
        else:
            # Direct API call
            return handle_direct_message_indexing(event)
            
    except Exception as e:
        lambda_logger.error("Failed to index message", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def index_file_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Index a file (triggered by DynamoDB stream or direct call)
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Index file request", extra={
        'request_id': request_id,
        'event_source': event.get('eventSource', 'unknown')
    })
    
    try:
        # Handle different event sources
        if event.get('Records'):
            # DynamoDB stream or SQS event
            return handle_stream_records(event['Records'], 'file')
        else:
            # Direct API call
            return handle_direct_file_indexing(event)
            
    except Exception as e:
        lambda_logger.error("Failed to index file", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def batch_index_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Batch index multiple messages and/or files
    
    POST /chat/search/index/batch
    {
        "messages": [...],
        "files": [...]
    }
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Batch index request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        messages = body.get('messages', [])
        files = body.get('files', [])
        
        if not messages and not files:
            return APIResponse.error("No messages or files provided for indexing", 400)
        
        results = {
            'messages': {'indexed': 0, 'failed': 0},
            'files': {'indexed': 0, 'failed': 0},
            'errors': []
        }
        
        # Index messages
        if messages:
            success, msg_result, error_msg = search_indexing_service.batch_index_messages(messages)
            if success:
                results['messages'] = msg_result
            else:
                results['errors'].append(f"Message indexing failed: {error_msg}")
        
        # Index files
        if files:
            success, file_result, error_msg = search_indexing_service.batch_index_files(files)
            if success:
                results['files'] = file_result
            else:
                results['errors'].append(f"File indexing failed: {error_msg}")
        
        lambda_logger.info("Batch indexing completed", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'messages_indexed': results['messages']['indexed'],
            'files_indexed': results['files']['indexed'],
            'total_errors': len(results['errors'])
        })
        
        return APIResponse.success(results, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)
    except Exception as e:
        lambda_logger.error("Failed to batch index", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def create_indices_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Create search indices with proper mappings
    
    POST /chat/search/index/create
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Create indices request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Create indices
        success, error_msg = search_indexing_service.create_indices()
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        response_data = {
            'indices_created': True,
            'message_index': search_indexing_service.message_index,
            'file_index': search_indexing_service.file_index,
            'created_by': user_id,
            'tenant_id': tenant_id
        }
        
        lambda_logger.info("Search indices created", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'message_index': search_indexing_service.message_index,
            'file_index': search_indexing_service.file_index
        })
        
        return APIResponse.success(response_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to create indices", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def handle_stream_records(records: list, record_type: str) -> Dict[str, Any]:
    """Handle DynamoDB stream records for automatic indexing"""
    
    indexed_count = 0
    failed_count = 0
    errors = []
    
    for record in records:
        try:
            # Only process INSERT and MODIFY events
            event_name = record.get('eventName')
            if event_name not in ['INSERT', 'MODIFY']:
                continue
            
            # Extract data from DynamoDB record
            if record_type == 'message':
                data = extract_message_from_dynamodb_record(record)
            else:  # file
                data = extract_file_from_dynamodb_record(record)
            
            if not data:
                continue
            
            # Index the data
            if record_type == 'message':
                success, error_msg = search_indexing_service.index_message(data)
            else:  # file
                success, error_msg = search_indexing_service.index_file(data)
            
            if success:
                indexed_count += 1
            else:
                failed_count += 1
                errors.append(error_msg)
                
        except Exception as e:
            failed_count += 1
            errors.append(str(e))
            lambda_logger.error(f"Failed to process {record_type} record", extra={
                'record': record,
                'error': str(e)
            })
    
    result = {
        'processed': len(records),
        'indexed': indexed_count,
        'failed': failed_count,
        'errors': errors[:10]  # Limit errors to prevent large responses
    }
    
    lambda_logger.info(f"Stream records processed for {record_type}", extra=result)
    
    return {
        'statusCode': 200,
        'body': json.dumps(result)
    }


def handle_direct_message_indexing(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle direct message indexing API call"""
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate message data
        if not body.get('messageId'):
            return APIResponse.error("Missing messageId", 400)
        
        # Index message
        success, error_msg = search_indexing_service.index_message(body)
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        response_data = {
            'indexed': True,
            'messageId': body.get('messageId'),
            'conversationId': body.get('conversationId')
        }
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)


def handle_direct_file_indexing(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle direct file indexing API call"""
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Validate file data
        if not body.get('fileId'):
            return APIResponse.error("Missing fileId", 400)
        
        # Index file
        success, error_msg = search_indexing_service.index_file(body)
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        response_data = {
            'indexed': True,
            'fileId': body.get('fileId'),
            'filename': body.get('filename')
        }
        
        return APIResponse.success(response_data, 200)
        
    except json.JSONDecodeError:
        return APIResponse.error("Invalid JSON in request body", 400)


def extract_message_from_dynamodb_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """Extract message data from DynamoDB stream record"""
    
    try:
        # Get the new image (after the change)
        dynamodb_record = record.get('dynamodb', {})
        new_image = dynamodb_record.get('NewImage', {})
        
        if not new_image:
            return {}
        
        # Convert DynamoDB format to regular dict
        message_data = {}
        for key, value in new_image.items():
            if 'S' in value:  # String
                message_data[key] = value['S']
            elif 'N' in value:  # Number
                message_data[key] = float(value['N'])
            elif 'BOOL' in value:  # Boolean
                message_data[key] = value['BOOL']
            elif 'L' in value:  # List
                message_data[key] = [item.get('S', item.get('N', item)) for item in value['L']]
            elif 'M' in value:  # Map
                message_data[key] = {k: v.get('S', v.get('N', v)) for k, v in value['M'].items()}
        
        return message_data
        
    except Exception as e:
        lambda_logger.error("Failed to extract message from DynamoDB record", extra={
            'record': record,
            'error': str(e)
        })
        return {}


def extract_file_from_dynamodb_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """Extract file data from DynamoDB stream record"""
    
    try:
        # Get the new image (after the change)
        dynamodb_record = record.get('dynamodb', {})
        new_image = dynamodb_record.get('NewImage', {})
        
        if not new_image:
            return {}
        
        # Convert DynamoDB format to regular dict
        file_data = {}
        for key, value in new_image.items():
            if 'S' in value:  # String
                file_data[key] = value['S']
            elif 'N' in value:  # Number
                file_data[key] = float(value['N'])
            elif 'BOOL' in value:  # Boolean
                file_data[key] = value['BOOL']
            elif 'L' in value:  # List
                file_data[key] = [item.get('S', item.get('N', item)) for item in value['L']]
            elif 'M' in value:  # Map
                file_data[key] = {k: v.get('S', v.get('N', v)) for k, v in value['M'].items()}
        
        return file_data
        
    except Exception as e:
        lambda_logger.error("Failed to extract file from DynamoDB record", extra={
            'record': record,
            'error': str(e)
        })
        return {}
