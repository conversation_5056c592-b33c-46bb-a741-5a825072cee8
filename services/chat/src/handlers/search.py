# services/chat/src/handlers/search.py
# Handlers for message and file search

import json
from typing import Dict, Any
from datetime import datetime, timedelta

try:
    from shared.logger import lambda_logger
    from shared.responses import APIResponse
    from shared.auth import extract_user_context
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    class APIResponse:
        @staticmethod
        def success(data, status_code=200): 
            return {'statusCode': status_code, 'body': json.dumps(data)}
        @staticmethod
        def error(message, status_code=400): 
            return {'statusCode': status_code, 'body': json.dumps({'error': message})}
    
    def extract_user_context(event):
        return {
            'user_id': 'test-user',
            'tenant_id': 'test-tenant',
            'email': '<EMAIL>'
        }

from ..services.search_service import search_service
from ..services.cache_service import cache_service

def search_messages_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Search messages with advanced filtering
    
    GET /chat/search/messages?q=query&conversation_id=conv-123&page=1&size=20&date_from=2024-01-01&date_to=2024-01-31
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Message search request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Validate required query parameter
        query = query_parameters.get('q', '').strip()
        if not query:
            return APIResponse.error("Missing required parameter: q (query)", 400)
        
        # Extract optional parameters
        page = int(query_parameters.get('page', 1))
        page_size = int(query_parameters.get('size', 20))
        
        # Validate pagination
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        
        # Build filters
        filters = {}

        if query_parameters.get('conversation_id'):
            filters['conversation_id'] = query_parameters['conversation_id']

        if query_parameters.get('message_type'):
            filters['message_type'] = query_parameters['message_type']

        if query_parameters.get('user_id'):
            filters['user_id'] = query_parameters['user_id']

        # Date range filters
        if query_parameters.get('date_from'):
            try:
                filters['date_from'] = datetime.fromisoformat(query_parameters['date_from']).isoformat()
            except ValueError:
                return APIResponse.error("Invalid date_from format. Use ISO format (YYYY-MM-DD)", 400)

        if query_parameters.get('date_to'):
            try:
                filters['date_to'] = datetime.fromisoformat(query_parameters['date_to']).isoformat()
            except ValueError:
                return APIResponse.error("Invalid date_to format. Use ISO format (YYYY-MM-DD)", 400)

        # Try to get results from cache first
        query_hash = cache_service.generate_search_hash(query, filters, page, page_size)
        cached_results = cache_service.get_search_results(query_hash, tenant_id)

        if cached_results:
            lambda_logger.debug("Search results retrieved from cache", extra={
                'query': query,
                'query_hash': query_hash,
                'tenant_id': tenant_id
            })

            # Add cache indicator to metadata
            cached_results['search_metadata']['cached'] = True
            return APIResponse.success(cached_results, 200)

        # Execute search
        success, results, error_msg = search_service.search_messages(
            query=query,
            tenant_id=tenant_id,
            user_id=user_id,
            filters=filters,
            page=page,
            page_size=page_size
        )

        # Cache the results for future requests
        if success and results:
            cache_service.cache_search_results(query_hash, tenant_id, {
                **results,
                'search_metadata': {
                    'query': query,
                    'filters': filters,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': results.get('total', 0),
                        'has_more': results.get('has_more', False)
                    },
                    'search_time_ms': results.get('search_time', 0),
                    'searched_at': datetime.utcnow().isoformat(),
                    'cached': False
                }
            })
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        # Add search metadata
        search_metadata = {
            'query': query,
            'filters': filters,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': results.get('total', 0),
                'has_more': results.get('has_more', False)
            },
            'search_time_ms': results.get('search_time', 0),
            'searched_at': datetime.utcnow().isoformat()
        }
        
        response_data = {
            **results,
            'search_metadata': search_metadata
        }
        
        lambda_logger.info("Message search completed", extra={
            'query': query,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'total_hits': results.get('total', 0),
            'returned_hits': len(results.get('messages', [])),
            'page': page,
            'search_time': results.get('search_time', 0)
        })
        
        return APIResponse.success(response_data, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to search messages", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def search_files_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Search files with advanced filtering
    
    GET /chat/search/files?q=query&file_type=image&content_type=image/jpeg&page=1&size=20
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("File search request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Query is optional for files (can browse all files)
        query = query_parameters.get('q', '').strip()
        
        # Extract pagination
        page = int(query_parameters.get('page', 1))
        page_size = int(query_parameters.get('size', 20))
        
        # Validate pagination
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        
        # Build filters
        filters = {}
        
        if query_parameters.get('conversation_id'):
            filters['conversation_id'] = query_parameters['conversation_id']
        
        if query_parameters.get('file_type'):
            allowed_file_types = ['image', 'video', 'audio', 'document', 'archive', 'other']
            file_type = query_parameters['file_type']
            if file_type in allowed_file_types:
                filters['file_type'] = file_type
            else:
                return APIResponse.error(f"Invalid file_type. Allowed: {allowed_file_types}", 400)
        
        if query_parameters.get('content_type'):
            filters['content_type'] = query_parameters['content_type']
        
        # File size filters
        if query_parameters.get('min_size'):
            try:
                filters['min_size'] = int(query_parameters['min_size'])
            except ValueError:
                return APIResponse.error("Invalid min_size. Must be a number", 400)
        
        if query_parameters.get('max_size'):
            try:
                filters['max_size'] = int(query_parameters['max_size'])
            except ValueError:
                return APIResponse.error("Invalid max_size. Must be a number", 400)
        
        # Execute search
        success, results, error_msg = search_service.search_files(
            query=query,
            tenant_id=tenant_id,
            user_id=user_id,
            filters=filters,
            page=page,
            page_size=page_size
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        # Add search metadata
        search_metadata = {
            'query': query or '*',
            'filters': filters,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': results.get('total', 0),
                'has_more': results.get('has_more', False)
            },
            'search_time_ms': results.get('search_time', 0),
            'searched_at': datetime.utcnow().isoformat()
        }
        
        response_data = {
            **results,
            'search_metadata': search_metadata
        }
        
        lambda_logger.info("File search completed", extra={
            'query': query,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'total_hits': results.get('total', 0),
            'returned_hits': len(results.get('files', [])),
            'page': page
        })
        
        return APIResponse.success(response_data, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to search files", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def search_combined_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Combined search across messages and files
    
    GET /chat/search/all?q=query&page=1&size=20
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Combined search request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Validate required query parameter
        query = query_parameters.get('q', '').strip()
        if not query:
            return APIResponse.error("Missing required parameter: q (query)", 400)
        
        # Extract pagination
        page = int(query_parameters.get('page', 1))
        page_size = int(query_parameters.get('size', 20))
        
        # Validate pagination
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        
        # Build basic filters (conversation_id is common)
        filters = {}
        if query_parameters.get('conversation_id'):
            filters['conversation_id'] = query_parameters['conversation_id']
        
        # Execute combined search
        success, results, error_msg = search_service.search_combined(
            query=query,
            tenant_id=tenant_id,
            user_id=user_id,
            filters=filters,
            page=page,
            page_size=page_size
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        lambda_logger.info("Combined search completed", extra={
            'query': query,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'message_hits': len(results.get('messages', [])),
            'file_hits': len(results.get('files', [])),
            'total_hits': results.get('total', {}).get('combined', 0)
        })
        
        return APIResponse.success(results, 200)
        
    except ValueError as e:
        return APIResponse.error(f"Invalid parameter: {str(e)}", 400)
    except Exception as e:
        lambda_logger.error("Failed to perform combined search", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)


def search_suggestions_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get search suggestions based on partial query
    
    GET /chat/search/suggestions?q=partial&type=all
    """
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    lambda_logger.info("Search suggestions request", extra={
        'request_id': request_id,
        'method': event.get('httpMethod'),
        'path': event.get('path')
    })
    
    try:
        # Extract user context from JWT
        user_context = extract_user_context(event)
        user_id = user_context['user_id']
        tenant_id = user_context['tenant_id']
        
        # Extract query parameters
        query_parameters = event.get('queryStringParameters') or {}
        
        # Get partial query
        partial_query = query_parameters.get('q', '').strip()
        if len(partial_query) < 2:
            return APIResponse.success({'suggestions': []}, 200)
        
        # Get suggestion type
        suggestion_type = query_parameters.get('type', 'all')
        allowed_types = ['messages', 'files', 'all']
        if suggestion_type not in allowed_types:
            return APIResponse.error(f"Invalid type. Allowed: {allowed_types}", 400)
        
        # Get suggestions
        success, suggestions, error_msg = search_service.get_search_suggestions(
            partial_query=partial_query,
            tenant_id=tenant_id,
            suggestion_type=suggestion_type
        )
        
        if not success:
            return APIResponse.error(error_msg, 500)
        
        response_data = {
            'suggestions': suggestions,
            'partial_query': partial_query,
            'suggestion_type': suggestion_type,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        lambda_logger.debug("Search suggestions generated", extra={
            'partial_query': partial_query,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'suggestion_count': len(suggestions)
        })
        
        return APIResponse.success(response_data, 200)
        
    except Exception as e:
        lambda_logger.error("Failed to get search suggestions", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error("Internal server error", 500)
