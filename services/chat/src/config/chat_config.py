# services/chat/src/config/chat_config.py
# Chat service specific configuration using shared config

"""
Chat service specific configuration.
Extends shared configuration with chat-specific settings.
"""

from typing import Dict, Any, Optional
from shared.config import get_settings, get_database_config
from shared.logger import lambda_logger


def get_chat_config() -> Dict[str, Any]:
    """Get chat service specific configuration"""
    settings = get_settings()
    
    return {
        # WebSocket configuration
        'websocket_enabled': settings.get('websocket_enabled', True),
        'websocket_timeout_seconds': settings.get('websocket_timeout_seconds', 300),
        'websocket_idle_timeout_seconds': settings.get('websocket_idle_timeout_seconds', 600),
        'max_connections_per_user': settings.get('max_connections_per_user', 5),
        'max_connections_per_tenant': settings.get('max_connections_per_tenant', 1000),
        
        # Message configuration
        'max_message_length': settings.get('max_message_length', 4000),
        'max_messages_per_conversation': settings.get('max_messages_per_conversation', 10000),
        'message_retention_days': settings.get('message_retention_days', 365),
        'auto_delete_old_messages': settings.get('auto_delete_old_messages', False),
        
        # Real-time features
        'typing_indicator_enabled': settings.get('typing_indicator_enabled', True),
        'typing_indicator_timeout_seconds': settings.get('typing_indicator_timeout_seconds', 10),
        'presence_enabled': settings.get('presence_enabled', True),
        'presence_update_interval_seconds': settings.get('presence_update_interval_seconds', 30),
        'read_receipts_enabled': settings.get('read_receipts_enabled', True),
        
        # Performance configuration
        'message_batch_size': settings.get('message_batch_size', 50),
        'max_message_batch_size': settings.get('max_message_batch_size', 100),
        'cache_ttl_seconds': settings.get('cache_ttl_seconds', 300),
        'cache_enabled': settings.get('cache_enabled', True),
        
        # File sharing configuration
        'file_sharing_enabled': settings.get('file_sharing_enabled', True),
        'max_file_size_mb': settings.get('max_file_size_mb', 50),
        'max_files_per_message': settings.get('max_files_per_message', 10),
        'allowed_file_types': settings.get('allowed_file_types', [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain', 'text/csv',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]),
        
        # Search configuration
        'search_enabled': settings.get('search_enabled', True),
        'search_index_enabled': settings.get('search_index_enabled', True),
        'max_search_results': settings.get('max_search_results', 100),
        'search_timeout_seconds': settings.get('search_timeout_seconds', 10),
        
        # Hybrid routing configuration
        'hybrid_routing_enabled': settings.get('hybrid_routing_enabled', True),
        'ai_routing_threshold': settings.get('ai_routing_threshold', 0.8),
        'human_escalation_enabled': settings.get('human_escalation_enabled', True),
        'auto_escalation_keywords': settings.get('auto_escalation_keywords', [
            'human', 'agent', 'help', 'support', 'escalate', 'manager'
        ]),
        
        # Rate limiting
        'rate_limit_messages_per_minute': settings.get('rate_limit_messages_per_minute', 60),
        'rate_limit_connections_per_minute': settings.get('rate_limit_connections_per_minute', 10),
        'rate_limit_enabled': settings.get('rate_limit_enabled', True),
        
        # Security configuration
        'message_encryption_enabled': settings.get('message_encryption_enabled', False),
        'content_filtering_enabled': settings.get('content_filtering_enabled', True),
        'spam_detection_enabled': settings.get('spam_detection_enabled', True),
        'profanity_filter_enabled': settings.get('profanity_filter_enabled', True),
    }


def get_websocket_config() -> Dict[str, Any]:
    """Get WebSocket specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('websocket_enabled', True),
        'api_gateway_endpoint': settings.get('websocket_api_gateway_endpoint'),
        'connection_timeout_seconds': settings.get('websocket_timeout_seconds', 300),
        'idle_timeout_seconds': settings.get('websocket_idle_timeout_seconds', 600),
        'ping_interval_seconds': settings.get('websocket_ping_interval_seconds', 30),
        'pong_timeout_seconds': settings.get('websocket_pong_timeout_seconds', 10),
        
        # Connection limits
        'max_connections_per_user': settings.get('max_connections_per_user', 5),
        'max_connections_per_tenant': settings.get('max_connections_per_tenant', 1000),
        'max_total_connections': settings.get('max_total_connections', 10000),
        
        # Message limits
        'max_message_size_bytes': settings.get('websocket_max_message_size_bytes', 32768),  # 32KB
        'max_messages_per_second': settings.get('websocket_max_messages_per_second', 10),
        'message_queue_size': settings.get('websocket_message_queue_size', 100),
        
        # Authentication
        'auth_required': settings.get('websocket_auth_required', True),
        'auth_timeout_seconds': settings.get('websocket_auth_timeout_seconds', 30),
        'token_validation_enabled': settings.get('websocket_token_validation_enabled', True),
        
        # Monitoring
        'connection_logging_enabled': settings.get('websocket_connection_logging_enabled', True),
        'message_logging_enabled': settings.get('websocket_message_logging_enabled', False),
        'performance_monitoring_enabled': settings.get('websocket_performance_monitoring_enabled', True),
    }


def get_notification_config() -> Dict[str, Any]:
    """Get notification specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('notifications_enabled', True),
        'websocket_notifications_enabled': settings.get('websocket_notifications_enabled', True),
        'push_notifications_enabled': settings.get('push_notifications_enabled', False),
        'email_notifications_enabled': settings.get('email_notifications_enabled', False),
        
        # Real-time features
        'typing_indicators_enabled': settings.get('typing_indicators_enabled', True),
        'presence_updates_enabled': settings.get('presence_updates_enabled', True),
        'read_receipts_enabled': settings.get('read_receipts_enabled', True),
        'delivery_receipts_enabled': settings.get('delivery_receipts_enabled', True),
        
        # Notification delivery
        'max_retry_attempts': settings.get('notification_max_retry_attempts', 3),
        'retry_delay_seconds': settings.get('notification_retry_delay_seconds', 1),
        'timeout_seconds': settings.get('notification_timeout_seconds', 10),
        'batch_size': settings.get('notification_batch_size', 100),
        
        # Content filtering
        'filter_sensitive_content': settings.get('notification_filter_sensitive_content', True),
        'truncate_long_messages': settings.get('notification_truncate_long_messages', True),
        'max_preview_length': settings.get('notification_max_preview_length', 100),
    }


def get_monitoring_config() -> Dict[str, Any]:
    """Get monitoring specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('monitoring_enabled', True),
        'cloudwatch_enabled': settings.get('cloudwatch_enabled', True),
        'custom_metrics_enabled': settings.get('custom_metrics_enabled', True),
        'detailed_monitoring': settings.get('detailed_monitoring', True),
        
        # Metric collection
        'metric_namespace': settings.get('metric_namespace', 'ChatService'),
        'metric_buffer_size': settings.get('metric_buffer_size', 20),
        'metric_flush_interval_seconds': settings.get('metric_flush_interval_seconds', 30),
        'realtime_metrics_enabled': settings.get('realtime_metrics_enabled', True),
        
        # Performance thresholds
        'message_processing_warning_ms': settings.get('message_processing_warning_ms', 1000),
        'message_processing_critical_ms': settings.get('message_processing_critical_ms', 3000),
        'websocket_connection_warning_count': settings.get('websocket_connection_warning_count', 8000),
        'websocket_connection_critical_count': settings.get('websocket_connection_critical_count', 9500),
        'error_rate_warning_threshold': settings.get('error_rate_warning_threshold', 0.05),
        'error_rate_critical_threshold': settings.get('error_rate_critical_threshold', 0.10),
        
        # Health checks
        'health_check_enabled': settings.get('health_check_enabled', True),
        'health_check_interval_seconds': settings.get('health_check_interval_seconds', 60),
        'health_check_timeout_seconds': settings.get('health_check_timeout_seconds', 10),
        'dependency_check_enabled': settings.get('dependency_check_enabled', True),
        
        # Alerting
        'alerts_enabled': settings.get('alerts_enabled', True),
        'alert_sns_topic': settings.get('alert_sns_topic'),
        'alert_email': settings.get('alert_email'),
        'alert_slack_webhook': settings.get('alert_slack_webhook'),
        'alert_cooldown_minutes': settings.get('alert_cooldown_minutes', 15),
    }


def get_analytics_config() -> Dict[str, Any]:
    """Get analytics specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('analytics_enabled', True),
        'event_tracking_enabled': settings.get('event_tracking_enabled', True),
        'user_behavior_tracking': settings.get('user_behavior_tracking', True),
        'performance_analytics': settings.get('performance_analytics', True),
        
        # Data retention
        'analytics_retention_days': settings.get('analytics_retention_days', 90),
        'detailed_logs_retention_days': settings.get('detailed_logs_retention_days', 30),
        'aggregated_data_retention_days': settings.get('aggregated_data_retention_days', 365),
        
        # Reporting
        'daily_reports_enabled': settings.get('daily_reports_enabled', True),
        'weekly_reports_enabled': settings.get('weekly_reports_enabled', True),
        'monthly_reports_enabled': settings.get('monthly_reports_enabled', True),
        'real_time_dashboard_enabled': settings.get('real_time_dashboard_enabled', True),
        
        # Privacy
        'anonymize_user_data': settings.get('anonymize_user_data', False),
        'exclude_sensitive_content': settings.get('exclude_sensitive_content', True),
        'gdpr_compliance_enabled': settings.get('gdpr_compliance_enabled', True),
    }


def get_cache_config() -> Dict[str, Any]:
    """Get cache specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('cache_enabled', True),
        'redis_enabled': settings.get('redis_enabled', True),
        'memory_cache_enabled': settings.get('memory_cache_enabled', True),
        
        # TTL settings (in seconds)
        'message_cache_ttl': settings.get('message_cache_ttl', 300),  # 5 minutes
        'conversation_cache_ttl': settings.get('conversation_cache_ttl', 600),  # 10 minutes
        'user_presence_cache_ttl': settings.get('user_presence_cache_ttl', 60),  # 1 minute
        'connection_cache_ttl': settings.get('connection_cache_ttl', 1800),  # 30 minutes
        'search_results_cache_ttl': settings.get('search_results_cache_ttl', 300),  # 5 minutes
        
        # Performance settings
        'max_cache_size': settings.get('max_cache_size', 10000),
        'cache_batch_size': settings.get('cache_batch_size', 100),
        'cache_compression_enabled': settings.get('cache_compression_enabled', True),
        'cache_encryption_enabled': settings.get('cache_encryption_enabled', False),
        
        # Redis specific (if available)
        'redis_host': settings.get('redis_host'),
        'redis_port': settings.get('redis_port', 6379),
        'redis_password': settings.get('redis_password'),
        'redis_ssl_enabled': settings.get('redis_ssl_enabled', True),
        'redis_timeout_seconds': settings.get('redis_timeout_seconds', 5),
        'redis_max_connections': settings.get('redis_max_connections', 100),
        'redis_connection_pool_size': settings.get('redis_connection_pool_size', 50),
    }


# Legacy support - maintain backward compatibility
class ChatConfig:
    """Legacy chat configuration class for backward compatibility"""
    
    def __init__(self):
        self._config = get_chat_config()
        lambda_logger.warning("ChatConfig class is deprecated. Use get_chat_config() function instead.")
    
    def __getattr__(self, name):
        return self._config.get(name)
    
    def get(self, key, default=None):
        return self._config.get(key, default)


# Create legacy instance for backward compatibility
config = ChatConfig()
