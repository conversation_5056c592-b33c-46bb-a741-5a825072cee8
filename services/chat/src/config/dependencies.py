# services/chat/src/config/dependencies.py
# Dependency injection configuration for chat service

"""
Dependency injection configuration for chat service.
Registers all services and their implementations.
"""

from shared.dependency_injection import container
from shared.logger import lambda_logger

# Import service interfaces and implementations
from ..services.message_service import IMessageService, MessageService
from ..services.notification_service import INotificationService, NotificationService
# Hybrid router moved to Agent Service
from ..services.health_check_service import IHealthCheckService, HealthCheckService
from ..services.monitoring_service import IMonitoringService, MonitoringService
from ..services.alerting_service import IAlertingService, AlertingService


def configure_dependencies():
    """
    Configure dependency injection for chat service.

    This function registers all service implementations with their interfaces
    in the dependency injection container.
    """
    try:
        # Register message service as singleton
        container.register_singleton(IMessageService, MessageService)
        
        # Register notification service as singleton
        container.register_singleton(INotificationService, NotificationService)
        
        # Hybrid router moved to Agent Service
        
        # Register health check service as singleton
        container.register_singleton(IHealthCheckService, HealthCheckService)
        
        # Register monitoring service as singleton
        container.register_singleton(IMonitoringService, MonitoringService)
        
        # Register alerting service as singleton
        container.register_singleton(IAlertingService, AlertingService)

        lambda_logger.info("Chat service dependencies configured successfully")

    except Exception as e:
        lambda_logger.error(f"Failed to configure chat service dependencies: {str(e)}")
        raise


# Auto-configure dependencies when module is imported
configure_dependencies()
