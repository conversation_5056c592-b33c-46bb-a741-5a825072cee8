# services/chat/src/services/realtime_service.py
# Real-time service for coordinating presence, typing, and messaging

import json
import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config
from ..services.presence_service import presence_service
from ..services.notification_service import notification_service

class RealtimeService:
    """Service for coordinating real-time features (presence, typing, notifications)"""
    
    def __init__(self):
        if shared_available:
            self.lambda_client = boto3.client('lambda')
        else:
            self.lambda_client = None
        
        # WebSocket configuration
        self.websocket_config = config.get_websocket_config()
    
    def get_conversation_realtime_info(self, conversation_id: str, user_id: str, 
                                     tenant_id: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get comprehensive real-time information for a conversation
        
        Returns:
            (success, realtime_info, error_message)
        """
        try:
            realtime_info = {
                'conversationId': conversation_id,
                'timestamp': datetime.utcnow().isoformat(),
                'presence': {},
                'typing': {},
                'activity': {}
            }
            
            # Get conversation participants from Agent Service
            participants = self._get_conversation_participants(conversation_id, tenant_id)
            
            if not participants:
                lambda_logger.warning("No participants found for conversation", extra={
                    'conversation_id': conversation_id,
                    'tenant_id': tenant_id
                })
                return True, realtime_info, None
            
            # Get presence information for all participants
            presence_info = {}
            for participant_id in participants:
                try:
                    success, presence_data, _ = presence_service.get_user_presence(
                        user_id=participant_id,
                        tenant_id=tenant_id
                    )
                    
                    if success and presence_data:
                        presence_info[participant_id] = {
                            'status': presence_data.get('status', 'offline'),
                            'lastSeen': presence_data.get('lastSeen'),
                            'connectionCount': presence_data.get('connectionCount', 0),
                            'isOnline': presence_service.is_user_online(participant_id, tenant_id)
                        }
                    else:
                        presence_info[participant_id] = {
                            'status': 'offline',
                            'lastSeen': None,
                            'connectionCount': 0,
                            'isOnline': False
                        }
                except Exception as e:
                    lambda_logger.warning("Failed to get presence for participant", extra={
                        'participant_id': participant_id,
                        'conversation_id': conversation_id,
                        'error': str(e)
                    })
                    presence_info[participant_id] = {
                        'status': 'unknown',
                        'lastSeen': None,
                        'connectionCount': 0,
                        'isOnline': False
                    }
            
            realtime_info['presence'] = presence_info
            
            # Get typing indicators from WebSocket service
            typing_info = self._get_typing_indicators(conversation_id)
            realtime_info['typing'] = typing_info
            
            # Calculate activity summary
            online_count = sum(1 for p in presence_info.values() if p['isOnline'])
            typing_count = len(typing_info.get('typingUsers', []))
            
            realtime_info['activity'] = {
                'totalParticipants': len(participants),
                'onlineParticipants': online_count,
                'typingParticipants': typing_count,
                'lastActivity': self._get_last_activity_time(presence_info)
            }
            
            lambda_logger.debug("Conversation realtime info retrieved", extra={
                'conversation_id': conversation_id,
                'participants_count': len(participants),
                'online_count': online_count,
                'typing_count': typing_count
            })
            
            return True, realtime_info, None
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation realtime info", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get realtime info: {str(e)}"
    
    def notify_user_activity(self, user_id: str, tenant_id: str, activity_type: str,
                           conversation_id: str = None, metadata: Dict[str, Any] = None) -> Tuple[bool, Optional[str]]:
        """
        Notify about user activity and update presence accordingly
        
        Returns:
            (success, error_message)
        """
        try:
            # Update user presence to indicate activity
            if activity_type in ['message_sent', 'typing_start', 'conversation_opened']:
                presence_success, presence_error = presence_service.update_user_presence(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    status='online',
                    metadata={'lastActivity': activity_type, 'timestamp': datetime.utcnow().isoformat()}
                )
                
                if not presence_success:
                    lambda_logger.warning("Failed to update presence on activity", extra={
                        'user_id': user_id,
                        'activity_type': activity_type,
                        'error': presence_error
                    })
            
            # Send activity notification if needed
            if conversation_id and activity_type in ['message_sent', 'conversation_joined']:
                notification_success, notification_error = notification_service.notify_presence_update(
                    user_id=user_id,
                    status='online',
                    tenant_id=tenant_id
                )
                
                if not notification_success:
                    lambda_logger.warning("Failed to send activity notification", extra={
                        'user_id': user_id,
                        'activity_type': activity_type,
                        'error': notification_error
                    })
            
            lambda_logger.debug("User activity notified", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'activity_type': activity_type,
                'conversation_id': conversation_id
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to notify user activity", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'activity_type': activity_type,
                'error': str(e)
            })
            return False, f"Failed to notify activity: {str(e)}"
    
    def get_tenant_realtime_summary(self, tenant_id: str, user_id: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get real-time summary for entire tenant
        
        Returns:
            (success, summary, error_message)
        """
        try:
            # Get tenant presence
            success, presence_list, error_msg = presence_service.get_tenant_presence(tenant_id, limit=100)
            
            if not success:
                return False, {}, error_msg
            
            # Calculate summary statistics
            total_users = len(presence_list)
            online_users = sum(1 for p in presence_list if p.get('status') == 'online')
            away_users = sum(1 for p in presence_list if p.get('status') == 'away')
            busy_users = sum(1 for p in presence_list if p.get('status') == 'busy')
            
            summary = {
                'tenantId': tenant_id,
                'timestamp': datetime.utcnow().isoformat(),
                'userCounts': {
                    'total': total_users,
                    'online': online_users,
                    'away': away_users,
                    'busy': busy_users,
                    'offline': total_users - online_users - away_users - busy_users
                },
                'presence': presence_list[:20],  # Limit to first 20 for summary
                'hasMore': total_users > 20
            }
            
            lambda_logger.debug("Tenant realtime summary retrieved", extra={
                'tenant_id': tenant_id,
                'total_users': total_users,
                'online_users': online_users
            })
            
            return True, summary, None
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant realtime summary", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get tenant summary: {str(e)}"
    
    def _get_conversation_participants(self, conversation_id: str, tenant_id: str) -> List[str]:
        """Get conversation participants from Agent Service"""
        try:
            import os
            
            # Call Agent Service to get conversation details
            agent_function_name = f"agent-scl-agent-{os.environ.get('ENVIRONMENT', 'dev')}-getConversation"
            
            if shared_available and self.lambda_client:
                payload = {
                    'httpMethod': 'GET',
                    'pathParameters': {'id': conversation_id},
                    'requestContext': {
                        'authorizer': {
                            'userId': 'system',
                            'tenantId': tenant_id
                        }
                    }
                }
                
                response = self.lambda_client.invoke(
                    FunctionName=agent_function_name,
                    InvocationType='RequestResponse',
                    Payload=json.dumps(payload)
                )
                
                response_payload = json.loads(response['Payload'].read())
                
                if response_payload.get('statusCode') == 200:
                    body = json.loads(response_payload.get('body', '{}'))
                    conversation = body.get('conversation', {})
                    
                    participants = conversation.get('participants', [])
                    created_by = conversation.get('createdBy')
                    
                    if created_by and created_by not in participants:
                        participants.append(created_by)
                    
                    return participants
            
            # Fallback for development
            return ['user1', 'user2']
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation participants", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def _get_typing_indicators(self, conversation_id: str) -> Dict[str, Any]:
        """Get typing indicators from WebSocket service"""
        try:
            # This would call the WebSocket service to get current typing indicators
            # For now, return empty structure
            return {
                'conversationId': conversation_id,
                'typingUsers': [],
                'count': 0,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get typing indicators", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return {
                'conversationId': conversation_id,
                'typingUsers': [],
                'count': 0,
                'error': str(e)
            }
    
    def _get_last_activity_time(self, presence_info: Dict[str, Dict[str, Any]]) -> Optional[str]:
        """Get the most recent activity time from presence info"""
        try:
            last_times = []
            
            for user_presence in presence_info.values():
                last_seen = user_presence.get('lastSeen')
                if last_seen:
                    last_times.append(last_seen)
            
            if last_times:
                return max(last_times)
            
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get last activity time", extra={
                'error': str(e)
            })
            return None


# Global realtime service instance
realtime_service = RealtimeService()
