# services/chat/src/services/message_service.py
# Message persistence and management service

import uuid
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from shared.logger import lambda_logger, log_database_operation
from shared.database import DynamoDBClient
from shared.responses import APIResponse
from shared.config import get_settings, get_database_config
from shared.auth import AuthContext


class IMessageService(ABC):
    """Interface for message service operations."""

    @abstractmethod
    def create_message(
        self,
        conversation_id: str,
        user_id: str,
        tenant_id: str,
        content: str,
        message_type: str = 'text',
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Create a new message."""
        pass

    @abstractmethod
    def get_message_by_id(self, message_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get message by ID."""
        pass

    @abstractmethod
    def list_conversation_messages(
        self,
        conversation_id: str,
        tenant_id: str,
        limit: int = 50,
        cursor: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], Optional[str]]:
        """List messages in a conversation with pagination."""
        pass

    @abstractmethod
    def update_message(
        self,
        message_id: str,
        tenant_id: str,
        updates: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Update message data."""
        pass

    @abstractmethod
    def delete_message(self, message_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Delete a message."""
        pass

    @abstractmethod
    def search_messages(
        self,
        tenant_id: str,
        query: str,
        conversation_id: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Search messages by content."""
        pass
from .search_indexing_service import search_indexing_service
from ..models.schema import ChatMessageSchema, ConversationStateSchema
from ..services.presence_service import presence_service

class MessageService(IMessageService):
    """Service for managing chat message persistence and operations"""
    
    def __init__(self):
        self.settings = get_settings()
        self.db_config = get_database_config()

        # Use shared database client
        self.messages_db = DynamoDBClient(self.db_config['table_name'])
        self.conversation_states_db = DynamoDBClient(self.db_config['table_name'])
        self.main_db = DynamoDBClient(self.db_config['table_name'])
    
    def create_message(self, conversation_id: str, user_id: str, tenant_id: str,
                      content: str, message_type: str = 'text',
                      attachments: List[Dict[str, Any]] = None,
                      reply_to_message_id: str = None,
                      metadata: Dict[str, Any] = None) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Create a new chat message
        
        Returns:
            (success, message_data, error_message)
        """
        try:
            # Validate conversation exists and user has access
            conversation_valid, error_msg = self._validate_conversation_access(
                conversation_id, user_id, tenant_id
            )
            if not conversation_valid:
                return False, None, error_msg
            
            # Create message item
            message_item = ChatMessageSchema.create_message_item(
                conversation_id=conversation_id,
                user_id=user_id,
                tenant_id=tenant_id,
                content=content,
                message_type=message_type,
                attachments=attachments,
                reply_to_message_id=reply_to_message_id,
                metadata=metadata
            )
            
            # Store message
            start_time = time.time()
            self.messages_db.put_item(message_item)
            duration_ms = (time.time() - start_time) * 1000

            # Log database operation
            log_database_operation(
                lambda_logger,
                operation="put_item",
                table_name=self.db_config['table_name'],
                tenant_id=tenant_id,
                duration_ms=duration_ms,
                success=True,
                item_count=1
            )

            # Update conversation activity
            self._update_conversation_activity(conversation_id)
            
            return True, message_item, None
            
        except Exception as e:
            lambda_logger.error("Failed to create message", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, None, f"Failed to create message: {str(e)}"
    
    def get_message(self, message_id: str, user_id: str, tenant_id: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Get a specific message by ID
        
        Returns:
            (success, message_data, error_message)
        """
        try:
            # Query by message ID using GSI
            messages = self.messages_db.query_gsi(
                'MessageIndex',
                message_id,
                filter_expression="EntityType = :entity_type",
                expression_attribute_values={':entity_type': 'ChatMessage'},
                limit=1
            )
            
            if not messages:
                return False, None, "Message not found"
            
            message = messages[0]
            
            # Validate tenant access
            if message.get('tenantId') != tenant_id:
                return False, None, "Access denied"
            
            lambda_logger.debug("Message retrieved", extra={
                'message_id': message_id,
                'user_id': user_id,
                'tenant_id': tenant_id
            })
            
            return True, message, None
            
        except Exception as e:
            lambda_logger.error("Failed to get message", extra={
                'message_id': message_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, None, f"Failed to get message: {str(e)}"
    
    def get_conversation_messages(self, conversation_id: str, user_id: str, tenant_id: str,
                                limit: int = 50, last_evaluated_key: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get messages for a conversation with pagination
        
        Returns:
            (success, {messages, last_evaluated_key}, error_message)
        """
        try:
            # Validate conversation access
            conversation_valid, error_msg = self._validate_conversation_access(
                conversation_id, user_id, tenant_id
            )
            if not conversation_valid:
                return False, {}, error_msg
            
            # Limit page size
            limit = min(limit, config.max_page_size)
            
            # Query conversation messages
            query_params = ChatMessageSchema.get_conversation_messages_query(
                conversation_id, limit, last_evaluated_key
            )
            
            result = self.messages_db.query_with_pagination(**query_params)
            
            messages = result.get('items', [])
            next_key = result.get('last_evaluated_key')
            
            lambda_logger.info("Conversation messages retrieved", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'message_count': len(messages),
                'has_more': bool(next_key)
            })
            
            return True, {
                'messages': messages,
                'lastEvaluatedKey': next_key,
                'hasMore': bool(next_key),
                'count': len(messages)
            }, None
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation messages", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get messages: {str(e)}"

    def get_conversation_messages_with_presence(self, conversation_id: str, user_id: str,
                                              tenant_id: str, limit: int = 50,
                                              last_evaluated_key: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get conversation messages with user presence information

        Returns:
            (success, result_with_presence, error_message)
        """
        try:
            # Get messages first
            success, result, error_msg = self.get_conversation_messages(
                conversation_id, user_id, tenant_id, limit, last_evaluated_key
            )

            if not success:
                return False, {}, error_msg

            messages = result.get('messages', [])

            # Get unique user IDs from messages
            user_ids = set()
            for message in messages:
                message_user_id = message.get('userId')
                if message_user_id:
                    user_ids.add(message_user_id)

            # Get presence information for all users
            presence_info = {}
            for message_user_id in user_ids:
                try:
                    presence_success, presence_data, _ = presence_service.get_user_presence(
                        user_id=message_user_id,
                        tenant_id=tenant_id
                    )

                    if presence_success and presence_data:
                        presence_info[message_user_id] = {
                            'status': presence_data.get('status', 'offline'),
                            'lastSeen': presence_data.get('lastSeen'),
                            'isOnline': presence_service.is_user_online(message_user_id, tenant_id)
                        }
                    else:
                        presence_info[message_user_id] = {
                            'status': 'offline',
                            'lastSeen': None,
                            'isOnline': False
                        }
                except Exception as e:
                    lambda_logger.warning("Failed to get presence for user", extra={
                        'user_id': message_user_id,
                        'error': str(e)
                    })
                    presence_info[message_user_id] = {
                        'status': 'unknown',
                        'lastSeen': None,
                        'isOnline': False
                    }

            # Add presence info to result
            result_with_presence = {
                **result,
                'presence': presence_info,
                'presenceTimestamp': lambda_logger.get_timestamp() if hasattr(lambda_logger, 'get_timestamp') else None
            }

            lambda_logger.debug("Messages with presence retrieved", extra={
                'conversation_id': conversation_id,
                'message_count': len(messages),
                'presence_users': len(presence_info)
            })

            return True, result_with_presence, None

        except Exception as e:
            lambda_logger.error("Failed to get messages with presence", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get messages with presence: {str(e)}"

    def create_agent_message(self, conversation_id: str, agent_id: str, tenant_id: str,
                           content: str, message_type: str = 'text', attachments: list = None,
                           metadata: Dict[str, Any] = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Create message from agent response

        Returns:
            (success, message_data, error_message)
        """
        try:
            # Generate message ID
            message_id = f"agent-{uuid.uuid4().hex[:12]}"
            timestamp = datetime.utcnow().isoformat()

            # Prepare message data
            message_data = {
                'messageId': message_id,
                'conversationId': conversation_id,
                'userId': agent_id,  # Agent acts as user
                'tenantId': tenant_id,
                'content': content,
                'type': message_type,
                'status': 'sent',
                'timestamp': timestamp,
                'createdAt': timestamp,
                'updatedAt': timestamp,
                'attachments': attachments or [],
                'metadata': metadata or {},
                'isEdited': False,
                'isDeleted': False,
                'isAgentMessage': True
            }

            # Create DynamoDB item
            message_item = ChatMessageSchema.create_message_item(
                conversation_id=conversation_id,
                user_id=agent_id,
                tenant_id=tenant_id,
                content=content,
                message_type=message_type,
                attachments=attachments,
                metadata=message_data['metadata']
            )

            # Store in messages table
            self.messages_db.put_item(message_item)

            # Update conversation state
            conversation_update = ConversationStateSchema.update_conversation_on_message(
                conversation_id=conversation_id,
                tenant_id=tenant_id,
                last_message_content=content[:100],  # Truncate for preview
                last_message_timestamp=timestamp,
                increment_count=True
            )

            self.conversation_states_db.update_item(**conversation_update)

            lambda_logger.info("Agent message created successfully", extra={
                'message_id': message_id,
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'tenant_id': tenant_id,
                'message_type': message_type,
                'content_length': len(content)
            })

            # Index message for search (async, don't fail if indexing fails)
            try:
                search_indexing_service.index_message(message_data)
            except Exception as e:
                lambda_logger.warning("Failed to index agent message for search", extra={
                    'message_id': message_id,
                    'error': str(e)
                })

            return True, message_data, None

        except Exception as e:
            lambda_logger.error("Failed to create agent message", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to create agent message: {str(e)}"
    
    def update_message_status(self, message_id: str, status: str, user_id: str, 
                            tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Update message status (delivered, read, etc.)
        
        Returns:
            (success, error_message)
        """
        try:
            # Validate status
            valid_statuses = ['sent', 'delivered', 'read', 'failed']
            if status not in valid_statuses:
                return False, f"Invalid status. Must be one of: {valid_statuses}"
            
            # Get message to validate access
            success, message, error_msg = self.get_message(message_id, user_id, tenant_id)
            if not success:
                return False, error_msg
            
            # Update message status - need conversation_id and timestamp from message
            conversation_id = message.get('conversationId')
            timestamp = message.get('timestamp')

            update_params = ChatMessageSchema.update_message_status(conversation_id, timestamp, status)
            self.messages_db.update_item(**update_params)
            
            lambda_logger.info("Message status updated", extra={
                'message_id': message_id,
                'status': status,
                'user_id': user_id,
                'tenant_id': tenant_id
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to update message status", extra={
                'message_id': message_id,
                'status': status,
                'user_id': user_id,
                'error': str(e)
            })
            return False, f"Failed to update message status: {str(e)}"
    
    def get_user_messages(self, user_id: str, tenant_id: str, 
                         limit: int = 50) -> Tuple[bool, List[Dict[str, Any]], Optional[str]]:
        """
        Get messages for a specific user across all conversations
        
        Returns:
            (success, messages, error_message)
        """
        try:
            # Limit page size
            limit = min(limit, config.max_page_size)
            
            # Query user messages using GSI
            query_params = ChatMessageSchema.get_user_messages_query(user_id, limit)
            messages = self.messages_db.query_gsi(**query_params)
            
            # Filter by tenant for security
            tenant_messages = [
                msg for msg in messages 
                if msg.get('tenantId') == tenant_id
            ]
            
            lambda_logger.debug("User messages retrieved", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'message_count': len(tenant_messages)
            })
            
            return True, tenant_messages, None
            
        except Exception as e:
            lambda_logger.error("Failed to get user messages", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, [], f"Failed to get user messages: {str(e)}"
    
    def _validate_conversation_access(self, conversation_id: str, user_id: str, 
                                    tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Validate user has access to conversation"""
        try:
            # Check if conversation exists in main table (from Agent Service)
            conversation_key = f'TENANT#{tenant_id}'
            conversation_sk = f'CONVERSATION#{conversation_id}'
            
            conversation = self.main_db.get_item(conversation_key, conversation_sk)
            
            if not conversation:
                return False, "Conversation not found"
            
            # Check if user is participant or has tenant access
            if conversation.get('TenantId') != tenant_id:
                return False, "Access denied"
            
            # Additional validation could check if user is in participants list
            # For now, tenant-level access is sufficient
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to validate conversation access", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Access validation failed: {str(e)}"
    
    def _update_conversation_activity(self, conversation_id: str):
        """Update conversation last activity"""
        try:
            # Get conversation state
            state_key = ConversationStateSchema.get_conversation_state_key(conversation_id)
            
            # Update activity
            update_params = ConversationStateSchema.update_conversation_activity(conversation_id)
            self.conversation_states_db.update_item(**update_params)
            
        except Exception as e:
            lambda_logger.warning("Failed to update conversation activity", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            # Don't fail the main operation for this


# Global message service instance
message_service = MessageService()
