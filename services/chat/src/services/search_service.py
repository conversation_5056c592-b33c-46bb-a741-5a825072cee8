# services/chat/src/services/search_service.py
# Service for message and file search using ElasticSearch/OpenSearch

import json
import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from elasticsearch import Elasticsearch
from opensearchpy import OpenSearch

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class SearchService:
    """Service for searching messages and files using ElasticSearch/OpenSearch"""
    
    def __init__(self):
        self.search_config = config.get_search_config()
        self.search_client = self._initialize_search_client()
        
        # Index configurations
        self.message_index = self.search_config.get('message_index', 'chat-messages')
        self.file_index = self.search_config.get('file_index', 'chat-files')
        self.analytics_index = self.search_config.get('analytics_index', 'chat-analytics')
        
        # Search configurations
        self.max_results = self.search_config.get('max_results', 100)
        self.default_page_size = self.search_config.get('default_page_size', 20)
        self.highlight_enabled = self.search_config.get('highlight_enabled', True)
    
    def _initialize_search_client(self):
        """Initialize ElasticSearch or OpenSearch client"""
        try:
            search_type = self.search_config.get('type', 'opensearch')
            
            if search_type == 'elasticsearch':
                return self._create_elasticsearch_client()
            else:
                return self._create_opensearch_client()
                
        except Exception as e:
            lambda_logger.warning("Failed to initialize search client", extra={
                'error': str(e),
                'search_type': self.search_config.get('type')
            })
            return None
    
    def _create_elasticsearch_client(self):
        """Create ElasticSearch client"""
        config_es = self.search_config.get('elasticsearch', {})
        
        return Elasticsearch(
            hosts=[config_es.get('host', 'localhost:9200')],
            http_auth=(config_es.get('username'), config_es.get('password')) if config_es.get('username') else None,
            use_ssl=config_es.get('use_ssl', True),
            verify_certs=config_es.get('verify_certs', True),
            timeout=config_es.get('timeout', 30)
        )
    
    def _create_opensearch_client(self):
        """Create OpenSearch client"""
        config_os = self.search_config.get('opensearch', {})
        
        return OpenSearch(
            hosts=[{'host': config_os.get('host', 'localhost'), 'port': config_os.get('port', 9200)}],
            http_auth=(config_os.get('username'), config_os.get('password')) if config_os.get('username') else None,
            use_ssl=config_os.get('use_ssl', True),
            verify_certs=config_os.get('verify_certs', True),
            timeout=config_os.get('timeout', 30)
        )
    
    def search_messages(self, query: str, tenant_id: str, user_id: str,
                       filters: Dict[str, Any] = None, page: int = 1,
                       page_size: int = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Search messages with advanced filtering
        
        Args:
            query: Search query string
            tenant_id: Tenant identifier
            user_id: User identifier (for access control)
            filters: Additional filters (conversation_id, date_range, message_type, etc.)
            page: Page number (1-based)
            page_size: Results per page
        
        Returns:
            (success, search_results, error_message)
        """
        try:
            if not self.search_client:
                return False, {}, "Search service not available"
            
            # Validate parameters
            if not query or not query.strip():
                return False, {}, "Search query cannot be empty"
            
            page_size = page_size or self.default_page_size
            page_size = min(page_size, self.max_results)
            
            # Build search query
            search_body = self._build_message_search_query(
                query=query.strip(),
                tenant_id=tenant_id,
                user_id=user_id,
                filters=filters or {},
                page=page,
                page_size=page_size
            )
            
            # Execute search
            response = self.search_client.search(
                index=self.message_index,
                body=search_body
            )
            
            # Process results
            results = self._process_message_search_results(response)
            
            lambda_logger.info("Message search completed", extra={
                'query': query,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'total_hits': results['total'],
                'returned_hits': len(results['messages']),
                'page': page,
                'page_size': page_size
            })
            
            return True, results, None
            
        except Exception as e:
            lambda_logger.error("Failed to search messages", extra={
                'query': query,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, {}, f"Search failed: {str(e)}"
    
    def search_files(self, query: str, tenant_id: str, user_id: str,
                    filters: Dict[str, Any] = None, page: int = 1,
                    page_size: int = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Search files with advanced filtering
        
        Returns:
            (success, search_results, error_message)
        """
        try:
            if not self.search_client:
                return False, {}, "Search service not available"
            
            page_size = page_size or self.default_page_size
            page_size = min(page_size, self.max_results)
            
            # Build search query
            search_body = self._build_file_search_query(
                query=query.strip() if query else "*",
                tenant_id=tenant_id,
                user_id=user_id,
                filters=filters or {},
                page=page,
                page_size=page_size
            )
            
            # Execute search
            response = self.search_client.search(
                index=self.file_index,
                body=search_body
            )
            
            # Process results
            results = self._process_file_search_results(response)
            
            lambda_logger.info("File search completed", extra={
                'query': query,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'total_hits': results['total'],
                'returned_hits': len(results['files']),
                'page': page
            })
            
            return True, results, None
            
        except Exception as e:
            lambda_logger.error("Failed to search files", extra={
                'query': query,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, {}, f"File search failed: {str(e)}"
    
    def search_combined(self, query: str, tenant_id: str, user_id: str,
                       filters: Dict[str, Any] = None, page: int = 1,
                       page_size: int = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Combined search across messages and files
        
        Returns:
            (success, search_results, error_message)
        """
        try:
            page_size = page_size or self.default_page_size
            
            # Search messages
            msg_success, msg_results, msg_error = self.search_messages(
                query=query,
                tenant_id=tenant_id,
                user_id=user_id,
                filters=filters,
                page=page,
                page_size=page_size // 2  # Split page size
            )
            
            # Search files
            file_success, file_results, file_error = self.search_files(
                query=query,
                tenant_id=tenant_id,
                user_id=user_id,
                filters=filters,
                page=page,
                page_size=page_size // 2
            )
            
            # Combine results
            combined_results = {
                'query': query,
                'total': {
                    'messages': msg_results.get('total', 0) if msg_success else 0,
                    'files': file_results.get('total', 0) if file_success else 0,
                    'combined': (msg_results.get('total', 0) if msg_success else 0) + 
                               (file_results.get('total', 0) if file_success else 0)
                },
                'messages': msg_results.get('messages', []) if msg_success else [],
                'files': file_results.get('files', []) if file_success else [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'has_more': (msg_results.get('has_more', False) if msg_success else False) or
                               (file_results.get('has_more', False) if file_success else False)
                },
                'search_time': datetime.utcnow().isoformat(),
                'errors': {
                    'messages': msg_error if not msg_success else None,
                    'files': file_error if not file_success else None
                }
            }
            
            overall_success = msg_success or file_success
            
            lambda_logger.info("Combined search completed", extra={
                'query': query,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'message_hits': len(combined_results['messages']),
                'file_hits': len(combined_results['files']),
                'total_hits': combined_results['total']['combined']
            })
            
            return overall_success, combined_results, None if overall_success else "Both searches failed"
            
        except Exception as e:
            lambda_logger.error("Failed to perform combined search", extra={
                'query': query,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, {}, f"Combined search failed: {str(e)}"
    
    def get_search_suggestions(self, partial_query: str, tenant_id: str,
                             suggestion_type: str = 'all') -> Tuple[bool, List[str], Optional[str]]:
        """
        Get search suggestions based on partial query
        
        Args:
            partial_query: Partial search query
            tenant_id: Tenant identifier
            suggestion_type: 'messages', 'files', or 'all'
        
        Returns:
            (success, suggestions, error_message)
        """
        try:
            if not self.search_client or len(partial_query.strip()) < 2:
                return True, [], None
            
            suggestions = []
            
            if suggestion_type in ['messages', 'all']:
                msg_suggestions = self._get_message_suggestions(partial_query, tenant_id)
                suggestions.extend(msg_suggestions)
            
            if suggestion_type in ['files', 'all']:
                file_suggestions = self._get_file_suggestions(partial_query, tenant_id)
                suggestions.extend(file_suggestions)
            
            # Remove duplicates and limit
            unique_suggestions = list(dict.fromkeys(suggestions))[:10]
            
            lambda_logger.debug("Search suggestions generated", extra={
                'partial_query': partial_query,
                'tenant_id': tenant_id,
                'suggestion_count': len(unique_suggestions)
            })
            
            return True, unique_suggestions, None
            
        except Exception as e:
            lambda_logger.error("Failed to get search suggestions", extra={
                'partial_query': partial_query,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, [], f"Failed to get suggestions: {str(e)}"
    
    def _build_message_search_query(self, query: str, tenant_id: str, user_id: str,
                                  filters: Dict[str, Any], page: int, page_size: int) -> Dict[str, Any]:
        """Build ElasticSearch query for message search"""
        
        # Base query structure
        search_body = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"tenantId": tenant_id}}
                    ],
                    "should": [
                        {"match": {"content": {"query": query, "boost": 2.0}}},
                        {"match": {"content.keyword": {"query": query, "boost": 1.5}}},
                        {"wildcard": {"content": f"*{query.lower()}*"}}
                    ],
                    "minimum_should_match": 1,
                    "filter": []
                }
            },
            "sort": [
                {"timestamp": {"order": "desc"}},
                {"_score": {"order": "desc"}}
            ],
            "from": (page - 1) * page_size,
            "size": page_size
        }
        
        # Add filters
        if filters.get('conversation_id'):
            search_body["query"]["bool"]["filter"].append(
                {"term": {"conversationId": filters['conversation_id']}}
            )
        
        if filters.get('message_type'):
            search_body["query"]["bool"]["filter"].append(
                {"term": {"type": filters['message_type']}}
            )
        
        if filters.get('user_id'):
            search_body["query"]["bool"]["filter"].append(
                {"term": {"userId": filters['user_id']}}
            )
        
        # Date range filter
        if filters.get('date_from') or filters.get('date_to'):
            date_range = {}
            if filters.get('date_from'):
                date_range['gte'] = filters['date_from']
            if filters.get('date_to'):
                date_range['lte'] = filters['date_to']
            
            search_body["query"]["bool"]["filter"].append(
                {"range": {"timestamp": date_range}}
            )
        
        # Add highlighting
        if self.highlight_enabled:
            search_body["highlight"] = {
                "fields": {
                    "content": {
                        "fragment_size": 150,
                        "number_of_fragments": 3
                    }
                }
            }
        
        return search_body
    
    def _build_file_search_query(self, query: str, tenant_id: str, user_id: str,
                               filters: Dict[str, Any], page: int, page_size: int) -> Dict[str, Any]:
        """Build ElasticSearch query for file search"""
        
        search_body = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"tenantId": tenant_id}}
                    ],
                    "should": [
                        {"match": {"filename": {"query": query, "boost": 3.0}}},
                        {"match": {"content": {"query": query, "boost": 1.0}}},
                        {"wildcard": {"filename": f"*{query.lower()}*"}}
                    ] if query != "*" else [{"match_all": {}}],
                    "minimum_should_match": 1 if query != "*" else 0,
                    "filter": []
                }
            },
            "sort": [
                {"uploadedAt": {"order": "desc"}},
                {"_score": {"order": "desc"}}
            ],
            "from": (page - 1) * page_size,
            "size": page_size
        }
        
        # Add file-specific filters
        if filters.get('file_type'):
            search_body["query"]["bool"]["filter"].append(
                {"term": {"fileType": filters['file_type']}}
            )
        
        if filters.get('content_type'):
            search_body["query"]["bool"]["filter"].append(
                {"term": {"contentType": filters['content_type']}}
            )
        
        if filters.get('conversation_id'):
            search_body["query"]["bool"]["filter"].append(
                {"term": {"conversationId": filters['conversation_id']}}
            )
        
        # File size range
        if filters.get('min_size') or filters.get('max_size'):
            size_range = {}
            if filters.get('min_size'):
                size_range['gte'] = filters['min_size']
            if filters.get('max_size'):
                size_range['lte'] = filters['max_size']
            
            search_body["query"]["bool"]["filter"].append(
                {"range": {"fileSize": size_range}}
            )
        
        return search_body
    
    def _process_message_search_results(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Process ElasticSearch message search response"""
        
        hits = response.get('hits', {})
        total = hits.get('total', {})
        
        # Handle different ES versions
        if isinstance(total, dict):
            total_count = total.get('value', 0)
        else:
            total_count = total
        
        messages = []
        for hit in hits.get('hits', []):
            source = hit['_source']
            message = {
                **source,
                'searchScore': hit['_score'],
                'highlights': hit.get('highlight', {})
            }
            messages.append(message)
        
        return {
            'messages': messages,
            'total': total_count,
            'has_more': total_count > len(messages) + ((response.get('from', 0))),
            'search_time': response.get('took', 0)
        }
    
    def _process_file_search_results(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Process ElasticSearch file search response"""
        
        hits = response.get('hits', {})
        total = hits.get('total', {})
        
        if isinstance(total, dict):
            total_count = total.get('value', 0)
        else:
            total_count = total
        
        files = []
        for hit in hits.get('hits', []):
            source = hit['_source']
            file_result = {
                **source,
                'searchScore': hit['_score']
            }
            files.append(file_result)
        
        return {
            'files': files,
            'total': total_count,
            'has_more': total_count > len(files) + (response.get('from', 0)),
            'search_time': response.get('took', 0)
        }
    
    def _get_message_suggestions(self, partial_query: str, tenant_id: str) -> List[str]:
        """Get message-based search suggestions"""
        try:
            # This would implement completion suggester
            # For now, return basic suggestions
            return [
                f"{partial_query} in messages",
                f"{partial_query} today",
                f"{partial_query} this week"
            ]
        except Exception:
            return []
    
    def _get_file_suggestions(self, partial_query: str, tenant_id: str) -> List[str]:
        """Get file-based search suggestions"""
        try:
            # This would implement completion suggester for files
            # For now, return basic suggestions
            return [
                f"{partial_query}.pdf",
                f"{partial_query}.docx",
                f"{partial_query} files"
            ]
        except Exception:
            return []


# Global search service instance
search_service = SearchService()
