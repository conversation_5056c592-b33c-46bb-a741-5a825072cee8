# services/chat/src/services/cdn_service.py
# CDN service for optimized file delivery with CloudFront

import boto3
import hashlib
import hmac
import base64
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from urllib.parse import quote

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class CDNService:
    """Service for CDN-optimized file delivery with CloudFront"""
    
    def __init__(self):
        self.cdn_config = config.get_cdn_config()
        self.cloudfront_client = self._initialize_cloudfront_client()
        
        # CDN configuration
        self.distribution_domain = self.cdn_config.get('distribution_domain')
        self.distribution_id = self.cdn_config.get('distribution_id')
        self.key_pair_id = self.cdn_config.get('key_pair_id')
        self.private_key = self.cdn_config.get('private_key')
        
        # Cache settings
        self.cache_behaviors = {
            'images': {
                'ttl': 86400,  # 24 hours
                'compress': True,
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'documents': {
                'ttl': 3600,   # 1 hour
                'compress': True,
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'videos': {
                'ttl': 86400,  # 24 hours
                'compress': False,
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'audio': {
                'ttl': 86400,  # 24 hours
                'compress': False,
                'viewer_protocol_policy': 'redirect-to-https'
            },
            'thumbnails': {
                'ttl': 604800,  # 7 days
                'compress': True,
                'viewer_protocol_policy': 'redirect-to-https'
            }
        }
        
        self.enabled = self.cdn_config.get('enabled', True)
    
    def _initialize_cloudfront_client(self):
        """Initialize CloudFront client"""
        try:
            if not self.cdn_config.get('enabled', True):
                lambda_logger.info("CDN service disabled")
                return None
            
            return boto3.client('cloudfront')
            
        except Exception as e:
            lambda_logger.warning("Failed to initialize CloudFront client", extra={
                'error': str(e)
            })
            return None
    
    def generate_cdn_url(self, file_key: str, file_type: str = 'documents',
                        expires_in: int = 3600, secure: bool = True) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Generate CDN URL for file access
        
        Args:
            file_key: S3 file key
            file_type: Type of file (images, documents, videos, audio, thumbnails)
            expires_in: URL expiration time in seconds
            secure: Whether to generate signed URL
        
        Returns:
            (success, cdn_url, error_message)
        """
        try:
            if not self.enabled or not self.distribution_domain:
                # Fallback to direct S3 URL
                return self._generate_s3_fallback_url(file_key, expires_in)
            
            # Build base CDN URL
            base_url = f"https://{self.distribution_domain}/{file_key}"
            
            if secure and self.private_key and self.key_pair_id:
                # Generate signed URL
                signed_url = self._generate_signed_url(
                    base_url, 
                    expires_in,
                    file_type
                )
                return True, signed_url, None
            else:
                # Return public CDN URL
                return True, base_url, None
                
        except Exception as e:
            lambda_logger.error("Failed to generate CDN URL", extra={
                'file_key': file_key,
                'file_type': file_type,
                'error': str(e)
            })
            return False, None, f"Failed to generate CDN URL: {str(e)}"
    
    def generate_thumbnail_url(self, file_key: str, size: str = 'medium',
                             expires_in: int = 86400) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Generate CDN URL for file thumbnail
        
        Args:
            file_key: Original file key
            size: Thumbnail size (small, medium, large)
            expires_in: URL expiration time in seconds
        
        Returns:
            (success, thumbnail_url, error_message)
        """
        try:
            # Generate thumbnail key
            thumbnail_key = self._generate_thumbnail_key(file_key, size)
            
            return self.generate_cdn_url(
                file_key=thumbnail_key,
                file_type='thumbnails',
                expires_in=expires_in,
                secure=True
            )
            
        except Exception as e:
            lambda_logger.error("Failed to generate thumbnail URL", extra={
                'file_key': file_key,
                'size': size,
                'error': str(e)
            })
            return False, None, f"Failed to generate thumbnail URL: {str(e)}"
    
    def generate_streaming_url(self, file_key: str, file_type: str = 'videos',
                             expires_in: int = 7200) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Generate CDN URL optimized for streaming
        
        Args:
            file_key: Video/audio file key
            file_type: Type of media (videos, audio)
            expires_in: URL expiration time in seconds
        
        Returns:
            (success, streaming_url, error_message)
        """
        try:
            if not self.enabled or not self.distribution_domain:
                return self._generate_s3_fallback_url(file_key, expires_in)
            
            # Build streaming URL with optimized parameters
            base_url = f"https://{self.distribution_domain}/{file_key}"
            
            # Add streaming-specific query parameters
            streaming_params = {
                'response-cache-control': 'max-age=3600',
                'response-content-type': self._get_streaming_content_type(file_type)
            }
            
            if self.private_key and self.key_pair_id:
                signed_url = self._generate_signed_url(
                    base_url,
                    expires_in,
                    file_type,
                    additional_params=streaming_params
                )
                return True, signed_url, None
            else:
                # Add parameters to public URL
                param_string = '&'.join([f"{k}={quote(v)}" for k, v in streaming_params.items()])
                streaming_url = f"{base_url}?{param_string}"
                return True, streaming_url, None
                
        except Exception as e:
            lambda_logger.error("Failed to generate streaming URL", extra={
                'file_key': file_key,
                'file_type': file_type,
                'error': str(e)
            })
            return False, None, f"Failed to generate streaming URL: {str(e)}"
    
    def invalidate_cache(self, file_keys: List[str]) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Invalidate CDN cache for specific files
        
        Args:
            file_keys: List of file keys to invalidate
        
        Returns:
            (success, invalidation_id, error_message)
        """
        try:
            if not self.enabled or not self.cloudfront_client or not self.distribution_id:
                lambda_logger.warning("CDN invalidation not available")
                return True, None, None
            
            # Prepare invalidation paths
            paths = [f"/{key}" for key in file_keys]
            
            # Create invalidation
            response = self.cloudfront_client.create_invalidation(
                DistributionId=self.distribution_id,
                InvalidationBatch={
                    'Paths': {
                        'Quantity': len(paths),
                        'Items': paths
                    },
                    'CallerReference': f"invalidation-{datetime.utcnow().timestamp()}"
                }
            )
            
            invalidation_id = response['Invalidation']['Id']
            
            lambda_logger.info("CDN cache invalidation created", extra={
                'invalidation_id': invalidation_id,
                'file_count': len(file_keys),
                'distribution_id': self.distribution_id
            })
            
            return True, invalidation_id, None
            
        except Exception as e:
            lambda_logger.error("Failed to invalidate CDN cache", extra={
                'file_keys': file_keys,
                'error': str(e)
            })
            return False, None, f"Failed to invalidate cache: {str(e)}"
    
    def get_cache_statistics(self) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get CDN cache statistics
        
        Returns:
            (success, statistics, error_message)
        """
        try:
            if not self.enabled or not self.cloudfront_client or not self.distribution_id:
                return True, {'enabled': False}, None
            
            # Get distribution statistics
            response = self.cloudfront_client.get_distribution(
                Id=self.distribution_id
            )
            
            distribution = response['Distribution']
            
            statistics = {
                'enabled': True,
                'distribution_id': self.distribution_id,
                'domain_name': distribution['DomainName'],
                'status': distribution['Status'],
                'last_modified': distribution['LastModifiedTime'].isoformat(),
                'price_class': distribution['DistributionConfig']['PriceClass'],
                'enabled_status': distribution['DistributionConfig']['Enabled'],
                'cache_behaviors': len(distribution['DistributionConfig']['CacheBehaviors']['Items']) + 1,  # +1 for default
                'origins': len(distribution['DistributionConfig']['Origins']['Items'])
            }
            
            return True, statistics, None
            
        except Exception as e:
            lambda_logger.error("Failed to get CDN statistics", extra={
                'error': str(e)
            })
            return False, {}, f"Failed to get statistics: {str(e)}"
    
    def optimize_file_delivery(self, file_key: str, file_type: str, 
                             user_location: str = None) -> Dict[str, Any]:
        """
        Get optimized delivery configuration for file
        
        Args:
            file_key: File key
            file_type: Type of file
            user_location: User's geographic location (optional)
        
        Returns:
            Optimization configuration
        """
        try:
            cache_behavior = self.cache_behaviors.get(file_type, self.cache_behaviors['documents'])
            
            optimization = {
                'cache_ttl': cache_behavior['ttl'],
                'compression_enabled': cache_behavior['compress'],
                'protocol_policy': cache_behavior['viewer_protocol_policy'],
                'recommended_headers': self._get_recommended_headers(file_type),
                'edge_locations': self._get_edge_locations(user_location) if user_location else None
            }
            
            return optimization
            
        except Exception as e:
            lambda_logger.warning("Failed to get optimization config", extra={
                'file_key': file_key,
                'file_type': file_type,
                'error': str(e)
            })
            return {}
    
    def _generate_signed_url(self, base_url: str, expires_in: int, file_type: str,
                           additional_params: Dict[str, str] = None) -> str:
        """Generate CloudFront signed URL"""
        try:
            # Calculate expiration time
            expire_time = datetime.utcnow() + timedelta(seconds=expires_in)
            expire_timestamp = int(expire_time.timestamp())
            
            # Build policy
            policy = {
                "Statement": [
                    {
                        "Resource": base_url,
                        "Condition": {
                            "DateLessThan": {
                                "AWS:EpochTime": expire_timestamp
                            }
                        }
                    }
                ]
            }
            
            # Convert policy to JSON and encode
            policy_json = json.dumps(policy, separators=(',', ':'))
            policy_b64 = base64.b64encode(policy_json.encode()).decode()
            
            # Create signature
            signature = self._create_signature(policy_json)
            
            # Build signed URL
            params = {
                'Expires': str(expire_timestamp),
                'Signature': signature,
                'Key-Pair-Id': self.key_pair_id
            }
            
            if additional_params:
                params.update(additional_params)
            
            param_string = '&'.join([f"{k}={quote(v)}" for k, v in params.items()])
            signed_url = f"{base_url}?{param_string}"
            
            return signed_url
            
        except Exception as e:
            lambda_logger.error("Failed to generate signed URL", extra={
                'base_url': base_url,
                'error': str(e)
            })
            return base_url  # Fallback to unsigned URL
    
    def _create_signature(self, policy: str) -> str:
        """Create signature for CloudFront signed URL"""
        try:
            # Use private key to sign policy
            # This is a simplified version - in production, use proper RSA signing
            signature = hmac.new(
                self.private_key.encode() if isinstance(self.private_key, str) else self.private_key,
                policy.encode(),
                hashlib.sha256
            ).digest()
            
            return base64.b64encode(signature).decode()
            
        except Exception as e:
            lambda_logger.error("Failed to create signature", extra={
                'error': str(e)
            })
            return "mock-signature"
    
    def _generate_s3_fallback_url(self, file_key: str, expires_in: int) -> Tuple[bool, str, None]:
        """Generate fallback S3 URL when CDN is not available"""
        try:
            s3_client = boto3.client('s3')
            bucket_name = config.get_file_config()['s3_bucket_name']
            
            presigned_url = s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': file_key},
                ExpiresIn=expires_in
            )
            
            return True, presigned_url, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate S3 fallback URL", extra={
                'file_key': file_key,
                'error': str(e)
            })
            return False, None, str(e)
    
    def _generate_thumbnail_key(self, file_key: str, size: str) -> str:
        """Generate thumbnail key from original file key"""
        # Extract file path and extension
        path_parts = file_key.split('/')
        filename = path_parts[-1]
        path = '/'.join(path_parts[:-1])
        
        # Add thumbnail prefix and size
        thumbnail_filename = f"thumb_{size}_{filename}"
        return f"{path}/thumbnails/{thumbnail_filename}"
    
    def _get_streaming_content_type(self, file_type: str) -> str:
        """Get appropriate content type for streaming"""
        streaming_types = {
            'videos': 'video/mp4',
            'audio': 'audio/mpeg'
        }
        return streaming_types.get(file_type, 'application/octet-stream')
    
    def _get_recommended_headers(self, file_type: str) -> Dict[str, str]:
        """Get recommended headers for file type"""
        headers = {
            'images': {
                'Cache-Control': 'public, max-age=86400',
                'Content-Encoding': 'gzip'
            },
            'documents': {
                'Cache-Control': 'private, max-age=3600',
                'Content-Disposition': 'inline'
            },
            'videos': {
                'Cache-Control': 'public, max-age=86400',
                'Accept-Ranges': 'bytes'
            },
            'audio': {
                'Cache-Control': 'public, max-age=86400',
                'Accept-Ranges': 'bytes'
            },
            'thumbnails': {
                'Cache-Control': 'public, max-age=604800',
                'Content-Encoding': 'gzip'
            }
        }
        
        return headers.get(file_type, {})
    
    def _get_edge_locations(self, user_location: str) -> List[str]:
        """Get recommended edge locations for user"""
        # This would typically use CloudFront's edge location data
        # For now, return mock data
        edge_locations = {
            'US': ['IAD', 'DFW', 'LAX', 'SEA'],
            'EU': ['LHR', 'FRA', 'AMS', 'CDG'],
            'APAC': ['NRT', 'SIN', 'SYD', 'HKG'],
            'SA': ['GRU', 'SCL'],
            'AF': ['CPT'],
            'ME': ['DXB']
        }
        
        return edge_locations.get(user_location, edge_locations['US'])


# Global CDN service instance
cdn_service = CDNService()
