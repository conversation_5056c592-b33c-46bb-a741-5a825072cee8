# services/chat/src/services/agent_integration_service.py
# Service for integrating agent responses with chat flow

import json
import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config
from ..services.hybrid_router import hybrid_router
from ..services.message_service import message_service
from ..services.notification_service import notification_service
from ..services.agent_typing_service import agent_typing_service

class AgentIntegrationService:
    """Service for integrating agent responses with chat flow"""
    
    def __init__(self):
        if shared_available:
            self.lambda_client = boto3.client('lambda')
        else:
            self.lambda_client = None
        
        self.agent_config = config.get_agent_config()
        self.websocket_config = config.get_websocket_config()
    
    def process_incoming_agent_response(self, webhook_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Process incoming agent response from webhook
        
        Returns:
            (success, response_data, error_message)
        """
        try:
            conversation_id = webhook_data['conversationId']
            agent_id = webhook_data['agentId']
            
            # Get tenant ID from conversation
            tenant_id = self._get_conversation_tenant(conversation_id)
            if not tenant_id:
                return False, {}, "Could not determine conversation tenant"
            
            # Stop any existing typing indicators for this agent
            agent_typing_service.stop_agent_typing_simulation(conversation_id, agent_id, tenant_id)
            
            # Route agent response through hybrid router
            success, routing_result, error_msg = hybrid_router.handle_agent_response(
                conversation_id=conversation_id,
                agent_id=agent_id,
                response_data=webhook_data,
                tenant_id=tenant_id
            )
            
            if not success:
                return False, {}, error_msg
            
            # Update conversation metadata
            self._update_conversation_metadata(
                conversation_id=conversation_id,
                agent_id=agent_id,
                agent_type=webhook_data.get('agentType'),
                tenant_id=tenant_id
            )
            
            lambda_logger.info("Agent response integrated successfully", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'message_id': routing_result.get('message_id'),
                'tenant_id': tenant_id
            })
            
            return True, routing_result, None
            
        except Exception as e:
            lambda_logger.error("Failed to process incoming agent response", extra={
                'webhook_data': webhook_data,
                'error': str(e)
            })
            return False, {}, f"Failed to process agent response: {str(e)}"
    
    def trigger_agent_processing(self, conversation_id: str, user_message: Dict[str, Any],
                                tenant_id: str, agent_type: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Trigger agent processing for user message
        
        Returns:
            (success, trigger_result, error_message)
        """
        try:
            # Determine which agent to use
            if not agent_type:
                agent_type = self._determine_agent_type(user_message['content'])
            
            agent_info = self.agent_config['supported_agents'].get(agent_type)
            if not agent_info:
                return False, {}, f"Unsupported agent type: {agent_type}"
            
            # Start typing indicator for agent
            typing_success, typing_error = agent_typing_service.start_agent_typing_simulation(
                conversation_id=conversation_id,
                agent_id=f"agent-{agent_type}",
                agent_type=agent_type,
                tenant_id=tenant_id,
                estimated_processing_time=self._estimate_processing_time(user_message['content'], agent_type)
            )
            
            # Route message to N8N for processing
            success, routing_result, error_msg = hybrid_router.route_message(
                conversation_id=conversation_id,
                user_id=user_message['userId'],
                tenant_id=tenant_id,
                content=user_message['content'],
                message_type=user_message.get('type', 'text'),
                attachments=user_message.get('attachments'),
                conversation_context={'agentType': agent_type}
            )
            
            if not success:
                # Stop typing indicator on failure
                agent_typing_service.stop_agent_typing_simulation(conversation_id, f"agent-{agent_type}", tenant_id)
                return False, {}, error_msg
            
            # Prepare trigger result
            trigger_result = {
                'conversation_id': conversation_id,
                'agent_type': agent_type,
                'agent_info': agent_info,
                'routing_method': routing_result.get('routing_method'),
                'typing_started': typing_success,
                'processing_started': True,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            lambda_logger.info("Agent processing triggered", extra={
                'conversation_id': conversation_id,
                'agent_type': agent_type,
                'routing_method': routing_result.get('routing_method'),
                'typing_started': typing_success
            })
            
            return True, trigger_result, None
            
        except Exception as e:
            lambda_logger.error("Failed to trigger agent processing", extra={
                'conversation_id': conversation_id,
                'user_message': user_message,
                'agent_type': agent_type,
                'error': str(e)
            })
            return False, {}, f"Failed to trigger agent processing: {str(e)}"
    
    def get_agent_conversation_history(self, conversation_id: str, agent_id: str,
                                     tenant_id: str, limit: int = 10) -> Tuple[bool, List[Dict[str, Any]], Optional[str]]:
        """
        Get conversation history for agent context
        
        Returns:
            (success, message_history, error_message)
        """
        try:
            # Get recent messages from conversation
            success, result, error_msg = message_service.get_conversation_messages(
                conversation_id=conversation_id,
                user_id=agent_id,  # Agent can access conversation
                tenant_id=tenant_id,
                limit=limit
            )
            
            if not success:
                return False, [], error_msg
            
            messages = result.get('messages', [])
            
            # Filter and format for agent context
            agent_history = []
            for message in messages:
                # Skip agent's own messages to avoid confusion
                if message.get('metadata', {}).get('isAgentResponse'):
                    continue
                
                agent_history.append({
                    'messageId': message.get('messageId'),
                    'content': message.get('content'),
                    'type': message.get('type'),
                    'userId': message.get('userId'),
                    'timestamp': message.get('timestamp'),
                    'attachments': message.get('attachments', [])
                })
            
            lambda_logger.debug("Agent conversation history retrieved", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'history_count': len(agent_history)
            })
            
            return True, agent_history, None
            
        except Exception as e:
            lambda_logger.error("Failed to get agent conversation history", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'error': str(e)
            })
            return False, [], f"Failed to get conversation history: {str(e)}"
    
    def _get_conversation_tenant(self, conversation_id: str) -> Optional[str]:
        """Get tenant ID for conversation"""
        try:
            # Extract from conversation ID pattern or call Agent Service
            if conversation_id.startswith('tenant-'):
                parts = conversation_id.split('-')
                if len(parts) >= 2:
                    return f"tenant-{parts[1]}"
            
            # Fallback
            return "default-tenant"
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation tenant", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return None
    
    def _determine_agent_type(self, content: str) -> str:
        """Determine which agent type to use based on content"""
        content_lower = content.lower()
        
        # Keywords for Forecaster
        forecaster_keywords = ['forecast', 'predict', 'demand', 'trend', 'analysis', 'future', 'projection']
        if any(keyword in content_lower for keyword in forecaster_keywords):
            return 'forecaster'
        
        # Default to Feedo for logistics optimization
        return 'feedo'

    def _estimate_processing_time(self, content: str, agent_type: str) -> float:
        """Estimate processing time based on content complexity and agent type"""
        try:
            # Base processing times by agent type (in seconds)
            base_times = {
                'feedo': 3.0,
                'forecaster': 5.0,
                'default': 2.5
            }

            base_time = base_times.get(agent_type, base_times['default'])

            # Adjust based on content complexity
            content_length = len(content)

            # Simple heuristics for complexity
            complexity_multiplier = 1.0

            if content_length > 200:
                complexity_multiplier += 0.5

            if content_length > 500:
                complexity_multiplier += 0.5

            # Check for complex keywords
            complex_keywords = ['analyze', 'forecast', 'optimize', 'calculate', 'predict', 'compare']
            if any(keyword in content.lower() for keyword in complex_keywords):
                complexity_multiplier += 0.3

            # Check for data requests
            data_keywords = ['report', 'data', 'statistics', 'metrics', 'dashboard']
            if any(keyword in content.lower() for keyword in data_keywords):
                complexity_multiplier += 0.4

            estimated_time = base_time * complexity_multiplier

            # Cap at reasonable limits
            return min(max(estimated_time, 1.0), 15.0)

        except Exception as e:
            lambda_logger.warning("Failed to estimate processing time", extra={
                'content_length': len(content) if content else 0,
                'agent_type': agent_type,
                'error': str(e)
            })
            return 3.0  # Default fallback
    

    
    def _update_conversation_metadata(self, conversation_id: str, agent_id: str,
                                    agent_type: str, tenant_id: str):
        """Update conversation metadata with agent interaction"""
        try:
            # This would update conversation metadata in Agent Service
            # For now, just log the action
            lambda_logger.debug("Conversation metadata updated", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_type': agent_type,
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.warning("Failed to update conversation metadata", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'error': str(e)
            })


# Global agent integration service instance
agent_integration_service = AgentIntegrationService()
