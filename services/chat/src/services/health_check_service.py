# services/chat/src/services/health_check_service.py
# Comprehensive health check service for system monitoring

import time
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from shared.logger import lambda_logger
from shared.database import DynamoD<PERSON><PERSON>
from shared.config import get_settings, get_database_config
from .cache_service import cache_service
from .connection_pool_service import connection_pool_service
from .monitoring_service import monitoring_service

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"


class IHealthCheckService(ABC):
    """Interface for health check service operations."""

    @abstractmethod
    def check_service_health(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        pass

    @abstractmethod
    def check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        pass

    @abstractmethod
    def check_websocket_health(self) -> Dict[str, Any]:
        """Check WebSocket service health."""
        pass

    @abstractmethod
    def check_external_dependencies(self) -> Dict[str, Any]:
        """Check external service dependencies."""
        pass

    @abstractmethod
    def get_service_metrics(self) -> Dict[str, Any]:
        """Get current service metrics."""
        pass


class HealthCheckService(IHealthCheckService):
    """Comprehensive health check service"""
    
    def __init__(self):
        self.settings = get_settings()
        self.health_config = self.settings.get('monitoring', {})
        self.enabled = self.health_config.get('health_check_enabled', True)
        self.timeout = self.health_config.get('health_check_timeout', 10)
        
        # Health check cache
        self.last_health_check = None
        self.health_cache_ttl = 30  # 30 seconds
        
        # Component health thresholds
        self.thresholds = {
            'response_time_warning': 1000,    # 1 second
            'response_time_critical': 5000,   # 5 seconds
            'error_rate_warning': 0.05,       # 5%
            'error_rate_critical': 0.10,      # 10%
            'memory_usage_warning': 0.80,     # 80%
            'memory_usage_critical': 0.90,    # 90%
            'cache_hit_rate_warning': 0.70,   # 70%
            'cache_hit_rate_critical': 0.50   # 50%
        }
    
    async def get_system_health(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Get comprehensive system health status"""
        try:
            # Check cache first
            if not force_refresh and self._is_health_cache_valid():
                return self.last_health_check
            
            start_time = time.time()
            
            # Run all health checks
            health_results = await self._run_all_health_checks()
            
            # Calculate overall health
            overall_status = self._calculate_overall_health(health_results)
            
            # Build health response
            health_response = {
                'status': overall_status.value,
                'timestamp': datetime.utcnow().isoformat(),
                'check_duration_ms': (time.time() - start_time) * 1000,
                'service': 'ChatService',
                'environment': config.environment,
                'version': config.get('version', '1.0.0'),
                'components': health_results,
                'summary': self._generate_health_summary(health_results)
            }
            
            # Cache the result
            self.last_health_check = health_response
            
            # Log health check
            lambda_logger.info("System health check completed", extra={
                'overall_status': overall_status.value,
                'check_duration_ms': health_response['check_duration_ms'],
                'components_checked': len(health_results)
            })
            
            return health_response
            
        except Exception as e:
            lambda_logger.error("Health check failed", extra={
                'error': str(e)
            })
            
            return {
                'status': HealthStatus.CRITICAL.value,
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e),
                'service': 'ChatService',
                'environment': config.environment
            }
    
    async def _run_all_health_checks(self) -> Dict[str, Dict[str, Any]]:
        """Run all component health checks"""
        health_checks = [
            ('database', self._check_database_health()),
            ('cache', self._check_cache_health()),
            ('connection_pool', self._check_connection_pool_health()),
            ('monitoring', self._check_monitoring_health()),
            ('external_services', self._check_external_services_health()),
            ('system_resources', self._check_system_resources()),
            ('business_metrics', self._check_business_metrics())
        ]
        
        results = {}
        
        for component_name, check_coroutine in health_checks:
            try:
                start_time = time.time()
                result = await asyncio.wait_for(check_coroutine, timeout=self.timeout)
                result['check_duration_ms'] = (time.time() - start_time) * 1000
                results[component_name] = result
                
            except asyncio.TimeoutError:
                results[component_name] = {
                    'status': HealthStatus.UNHEALTHY.value,
                    'message': f'Health check timed out after {self.timeout}s',
                    'check_duration_ms': self.timeout * 1000
                }
            except Exception as e:
                results[component_name] = {
                    'status': HealthStatus.CRITICAL.value,
                    'message': f'Health check failed: {str(e)}',
                    'error': str(e)
                }
        
        return results
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()

            # Test DynamoDB connection
            db_config = get_database_config()
            db_client = DynamoDBClient(db_config['table_name'])

            # Perform a simple query
            test_key = {'PK': 'HEALTH_CHECK', 'SK': 'TEST'}
            response = db_client.get_item(test_key)
            
            query_time = (time.time() - start_time) * 1000
            
            # Determine status based on response time
            if query_time > self.thresholds['response_time_critical']:
                status = HealthStatus.CRITICAL
                message = f"Database response time critical: {query_time:.2f}ms"
            elif query_time > self.thresholds['response_time_warning']:
                status = HealthStatus.DEGRADED
                message = f"Database response time high: {query_time:.2f}ms"
            else:
                status = HealthStatus.HEALTHY
                message = f"Database responsive: {query_time:.2f}ms"
            
            return {
                'status': status.value,
                'message': message,
                'metrics': {
                    'response_time_ms': query_time,
                    'connection_successful': True
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'Database connection failed: {str(e)}',
                'error': str(e),
                'metrics': {
                    'connection_successful': False
                }
            }
    
    async def _check_cache_health(self) -> Dict[str, Any]:
        """Check cache service health"""
        try:
            start_time = time.time()
            
            # Test cache operations
            test_key = 'health_check_test'
            test_value = {'timestamp': datetime.utcnow().isoformat()}
            
            # Test set operation
            cache_service.set(test_key, test_value, ttl=60)
            
            # Test get operation
            retrieved_value = cache_service.get(test_key)
            
            operation_time = (time.time() - start_time) * 1000
            
            # Get cache statistics
            cache_stats = cache_service.get_cache_stats()
            hit_rate = cache_stats.get('hit_rate', 0)
            
            # Determine status
            if operation_time > self.thresholds['response_time_critical']:
                status = HealthStatus.CRITICAL
                message = f"Cache response time critical: {operation_time:.2f}ms"
            elif hit_rate < self.thresholds['cache_hit_rate_critical']:
                status = HealthStatus.CRITICAL
                message = f"Cache hit rate critical: {hit_rate:.2%}"
            elif operation_time > self.thresholds['response_time_warning'] or hit_rate < self.thresholds['cache_hit_rate_warning']:
                status = HealthStatus.DEGRADED
                message = f"Cache performance degraded"
            else:
                status = HealthStatus.HEALTHY
                message = f"Cache healthy: {operation_time:.2f}ms, {hit_rate:.2%} hit rate"
            
            # Clean up test data
            cache_service.delete(test_key)
            
            return {
                'status': status.value,
                'message': message,
                'metrics': {
                    'response_time_ms': operation_time,
                    'hit_rate': hit_rate,
                    'total_operations': cache_stats.get('total_operations', 0),
                    'memory_usage': cache_stats.get('memory_usage', 0)
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'Cache service failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_connection_pool_health(self) -> Dict[str, Any]:
        """Check connection pool health"""
        try:
            pool_stats = connection_pool_service.get_pool_statistics()
            
            # Check WebSocket pool utilization
            ws_stats = pool_stats.get('websocket', {})
            ws_utilization = ws_stats.get('pool_utilization', 0)
            active_connections = ws_stats.get('active_connections', 0)
            
            # Check database pool utilization
            db_stats = pool_stats.get('database', {})
            db_utilization = db_stats.get('pool_utilization', {})
            
            # Determine status
            if ws_utilization > 95 or any(util > 95 for util in db_utilization.values()):
                status = HealthStatus.CRITICAL
                message = "Connection pool utilization critical"
            elif ws_utilization > 80 or any(util > 80 for util in db_utilization.values()):
                status = HealthStatus.DEGRADED
                message = "Connection pool utilization high"
            else:
                status = HealthStatus.HEALTHY
                message = f"Connection pools healthy: {active_connections} active WebSocket connections"
            
            return {
                'status': status.value,
                'message': message,
                'metrics': {
                    'websocket_utilization_percent': ws_utilization,
                    'active_websocket_connections': active_connections,
                    'database_pool_utilization': db_utilization,
                    'peak_connections': ws_stats.get('peak_connections', 0)
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'Connection pool check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_monitoring_health(self) -> Dict[str, Any]:
        """Check monitoring service health"""
        try:
            # Get real-time metrics
            realtime_metrics = monitoring_service.get_realtime_metrics()
            
            if not realtime_metrics:
                return {
                    'status': HealthStatus.DEGRADED.value,
                    'message': 'No real-time metrics available',
                    'metrics': {}
                }
            
            # Check metrics health
            error_rate = realtime_metrics.get('error_rate', 0)
            avg_response_time = realtime_metrics.get('avg_response_time_ms', 0)
            cache_hit_rate = realtime_metrics.get('cache_hit_rate', 1)
            
            # Determine status
            if (error_rate > self.thresholds['error_rate_critical'] or 
                avg_response_time > self.thresholds['response_time_critical']):
                status = HealthStatus.CRITICAL
                message = "System performance critical"
            elif (error_rate > self.thresholds['error_rate_warning'] or 
                  avg_response_time > self.thresholds['response_time_warning'] or
                  cache_hit_rate < self.thresholds['cache_hit_rate_warning']):
                status = HealthStatus.DEGRADED
                message = "System performance degraded"
            else:
                status = HealthStatus.HEALTHY
                message = "System performance healthy"
            
            return {
                'status': status.value,
                'message': message,
                'metrics': realtime_metrics
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'Monitoring check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_external_services_health(self) -> Dict[str, Any]:
        """Check external service dependencies"""
        try:
            # This would check external services like N8N, Redis, etc.
            # For now, we'll simulate the check
            
            external_services = {
                'n8n_webhook': True,  # Would actually test webhook endpoint
                'redis_cluster': True,  # Would test Redis connectivity
                'cdn_service': True   # Would test CDN availability
            }
            
            failed_services = [service for service, healthy in external_services.items() if not healthy]
            
            if failed_services:
                if len(failed_services) > len(external_services) / 2:
                    status = HealthStatus.CRITICAL
                    message = f"Multiple external services down: {', '.join(failed_services)}"
                else:
                    status = HealthStatus.DEGRADED
                    message = f"Some external services down: {', '.join(failed_services)}"
            else:
                status = HealthStatus.HEALTHY
                message = "All external services healthy"
            
            return {
                'status': status.value,
                'message': message,
                'metrics': {
                    'services_checked': len(external_services),
                    'services_healthy': len(external_services) - len(failed_services),
                    'failed_services': failed_services
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'External services check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource utilization"""
        try:
            # In a real Lambda environment, we'd check memory usage, etc.
            # For now, we'll simulate resource checks
            
            memory_usage = 0.65  # 65% memory usage (simulated)
            cpu_usage = 0.45     # 45% CPU usage (simulated)
            
            # Determine status
            if memory_usage > self.thresholds['memory_usage_critical'] or cpu_usage > 0.90:
                status = HealthStatus.CRITICAL
                message = "System resources critical"
            elif memory_usage > self.thresholds['memory_usage_warning'] or cpu_usage > 0.70:
                status = HealthStatus.DEGRADED
                message = "System resources high"
            else:
                status = HealthStatus.HEALTHY
                message = "System resources healthy"
            
            return {
                'status': status.value,
                'message': message,
                'metrics': {
                    'memory_usage_percent': memory_usage * 100,
                    'cpu_usage_percent': cpu_usage * 100,
                    'disk_usage_percent': 25.0  # Simulated
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'System resources check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_business_metrics(self) -> Dict[str, Any]:
        """Check business metrics health"""
        try:
            # Get recent business metrics
            realtime_metrics = monitoring_service.get_realtime_metrics()
            
            messages_per_minute = realtime_metrics.get('messages_per_minute', 0)
            active_connections = realtime_metrics.get('active_connections', 0)
            
            # Simple business health check
            if messages_per_minute == 0 and active_connections == 0:
                status = HealthStatus.DEGRADED
                message = "No user activity detected"
            elif messages_per_minute < 0:  # Impossible, but checking for data integrity
                status = HealthStatus.CRITICAL
                message = "Invalid business metrics detected"
            else:
                status = HealthStatus.HEALTHY
                message = f"Business metrics healthy: {messages_per_minute} messages/min, {active_connections} connections"
            
            return {
                'status': status.value,
                'message': message,
                'metrics': {
                    'messages_per_minute': messages_per_minute,
                    'active_connections': active_connections,
                    'health_status': realtime_metrics.get('health_status', 'unknown')
                }
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.CRITICAL.value,
                'message': f'Business metrics check failed: {str(e)}',
                'error': str(e)
            }
    
    def _calculate_overall_health(self, health_results: Dict[str, Dict[str, Any]]) -> HealthStatus:
        """Calculate overall system health from component results"""
        status_counts = {
            HealthStatus.HEALTHY: 0,
            HealthStatus.DEGRADED: 0,
            HealthStatus.UNHEALTHY: 0,
            HealthStatus.CRITICAL: 0
        }
        
        for component_health in health_results.values():
            status_str = component_health.get('status', 'critical')
            try:
                status = HealthStatus(status_str)
                status_counts[status] += 1
            except ValueError:
                status_counts[HealthStatus.CRITICAL] += 1
        
        # Determine overall status
        if status_counts[HealthStatus.CRITICAL] > 0:
            return HealthStatus.CRITICAL
        elif status_counts[HealthStatus.UNHEALTHY] > 0:
            return HealthStatus.UNHEALTHY
        elif status_counts[HealthStatus.DEGRADED] > 0:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY
    
    def _generate_health_summary(self, health_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Generate health summary statistics"""
        total_components = len(health_results)
        healthy_components = sum(1 for result in health_results.values() 
                               if result.get('status') == HealthStatus.HEALTHY.value)
        
        return {
            'total_components': total_components,
            'healthy_components': healthy_components,
            'health_percentage': (healthy_components / total_components * 100) if total_components > 0 else 0,
            'components_by_status': {
                status.value: sum(1 for result in health_results.values() 
                                if result.get('status') == status.value)
                for status in HealthStatus
            }
        }
    
    def _is_health_cache_valid(self) -> bool:
        """Check if cached health result is still valid"""
        if not self.last_health_check:
            return False
        
        try:
            last_check_time = datetime.fromisoformat(
                self.last_health_check['timestamp'].replace('Z', '+00:00')
            )
            return (datetime.utcnow() - last_check_time.replace(tzinfo=None)).total_seconds() < self.health_cache_ttl
        except Exception:
            return False
    
    def get_simple_health(self) -> Dict[str, str]:
        """Get simple health status for load balancers"""
        try:
            if self._is_health_cache_valid():
                status = self.last_health_check.get('status', 'critical')
            else:
                # Quick health check without full diagnostics
                status = 'healthy'  # Simplified check
            
            return {
                'status': status,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception:
            return {
                'status': 'critical',
                'timestamp': datetime.utcnow().isoformat()
            }


# Global health check service instance
health_check_service = HealthCheckService()
