# services/chat/src/services/agent_typing_service.py
# Service for simulating agent typing indicators

import json
import boto3
import asyncio
import threading
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class AgentTypingService:
    """Service for simulating agent typing indicators during processing"""
    
    def __init__(self):
        if shared_available:
            self.lambda_client = boto3.client('lambda')
        else:
            self.lambda_client = None
        
        self.agent_config = config.get_agent_config()
        self.websocket_config = config.get_websocket_config()
        
        # Active typing sessions
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Typing patterns for different agent types
        self.typing_patterns = {
            'feedo': {
                'initial_delay': 1.0,  # seconds before starting typing
                'typing_duration': 3.0,  # base typing duration
                'pause_duration': 1.5,  # pause between typing bursts
                'bursts': 2,  # number of typing bursts
                'variation': 0.5  # random variation factor
            },
            'forecaster': {
                'initial_delay': 1.5,
                'typing_duration': 4.0,  # longer for complex analysis
                'pause_duration': 2.0,
                'bursts': 3,
                'variation': 0.7
            },
            'default': {
                'initial_delay': 1.0,
                'typing_duration': 2.5,
                'pause_duration': 1.0,
                'bursts': 1,
                'variation': 0.3
            }
        }
    
    def start_agent_typing_simulation(self, conversation_id: str, agent_id: str, agent_type: str,
                                    tenant_id: str, estimated_processing_time: float = None) -> Tuple[bool, Optional[str]]:
        """
        Start typing simulation for agent
        
        Args:
            conversation_id: Conversation identifier
            agent_id: Agent identifier
            agent_type: Type of agent (feedo, forecaster, etc.)
            tenant_id: Tenant identifier
            estimated_processing_time: Estimated time for agent to respond
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.agent_config.get('typing_simulation_enabled', True):
                lambda_logger.debug("Agent typing simulation disabled")
                return True, None
            
            session_key = f"{conversation_id}:{agent_id}"
            
            # Stop any existing session for this agent
            if session_key in self.active_sessions:
                self.stop_agent_typing_simulation(conversation_id, agent_id, tenant_id)
            
            # Get typing pattern for agent type
            pattern = self.typing_patterns.get(agent_type, self.typing_patterns['default'])
            
            # Calculate typing schedule
            typing_schedule = self._calculate_typing_schedule(pattern, estimated_processing_time)
            
            # Create session
            session = {
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_type': agent_type,
                'tenant_id': tenant_id,
                'pattern': pattern,
                'schedule': typing_schedule,
                'started_at': datetime.utcnow(),
                'active': True
            }
            
            self.active_sessions[session_key] = session
            
            # Start typing simulation in background
            if shared_available:
                threading.Thread(
                    target=self._execute_typing_simulation,
                    args=(session_key,),
                    daemon=True
                ).start()
            
            lambda_logger.info("Agent typing simulation started", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_type': agent_type,
                'schedule_events': len(typing_schedule),
                'estimated_duration': sum(event['duration'] for event in typing_schedule)
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to start agent typing simulation", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_type': agent_type,
                'error': str(e)
            })
            return False, f"Failed to start typing simulation: {str(e)}"
    
    def stop_agent_typing_simulation(self, conversation_id: str, agent_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Stop typing simulation for agent
        
        Returns:
            (success, error_message)
        """
        try:
            session_key = f"{conversation_id}:{agent_id}"
            
            if session_key in self.active_sessions:
                session = self.active_sessions[session_key]
                session['active'] = False
                
                # Send final stop typing
                self._send_typing_indicator(
                    conversation_id=conversation_id,
                    agent_id=agent_id,
                    is_typing=False,
                    tenant_id=tenant_id
                )
                
                # Remove session
                del self.active_sessions[session_key]
                
                lambda_logger.debug("Agent typing simulation stopped", extra={
                    'conversation_id': conversation_id,
                    'agent_id': agent_id,
                    'duration': (datetime.utcnow() - session['started_at']).total_seconds()
                })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to stop agent typing simulation", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'error': str(e)
            })
            return False, f"Failed to stop typing simulation: {str(e)}"
    
    def _calculate_typing_schedule(self, pattern: Dict[str, Any], estimated_processing_time: float = None) -> List[Dict[str, Any]]:
        """Calculate typing schedule based on pattern and processing time"""
        import random
        
        schedule = []
        current_time = 0.0
        
        # Initial delay
        initial_delay = pattern['initial_delay'] * (1 + random.uniform(-0.2, 0.2))
        current_time += initial_delay
        
        # Calculate total available time for typing
        if estimated_processing_time:
            # Use 70% of processing time for typing simulation
            available_time = estimated_processing_time * 0.7
        else:
            # Default based on pattern
            available_time = pattern['typing_duration'] * pattern['bursts'] + pattern['pause_duration'] * (pattern['bursts'] - 1)
        
        # Create typing bursts
        bursts = pattern['bursts']
        burst_duration = (available_time - pattern['pause_duration'] * (bursts - 1)) / bursts
        
        for i in range(bursts):
            # Start typing
            schedule.append({
                'time': current_time,
                'action': 'start_typing',
                'duration': burst_duration * (1 + random.uniform(-pattern['variation'], pattern['variation']))
            })
            
            current_time += burst_duration
            
            # Pause between bursts (except for last burst)
            if i < bursts - 1:
                schedule.append({
                    'time': current_time,
                    'action': 'stop_typing',
                    'duration': pattern['pause_duration'] * (1 + random.uniform(-0.3, 0.3))
                })
                
                current_time += pattern['pause_duration']
        
        # Final stop
        schedule.append({
            'time': current_time,
            'action': 'stop_typing',
            'duration': 0
        })
        
        return schedule
    
    def _execute_typing_simulation(self, session_key: str):
        """Execute typing simulation in background thread"""
        try:
            session = self.active_sessions.get(session_key)
            if not session:
                return
            
            start_time = time.time()
            
            for event in session['schedule']:
                if not session.get('active', False):
                    break
                
                # Wait until event time
                elapsed = time.time() - start_time
                wait_time = event['time'] - elapsed
                
                if wait_time > 0:
                    time.sleep(wait_time)
                
                # Check if session is still active
                if not session.get('active', False):
                    break
                
                # Execute typing action
                is_typing = event['action'] == 'start_typing'
                
                self._send_typing_indicator(
                    conversation_id=session['conversation_id'],
                    agent_id=session['agent_id'],
                    is_typing=is_typing,
                    tenant_id=session['tenant_id'],
                    duration=event['duration'] if is_typing else None
                )
                
                lambda_logger.debug("Typing simulation event executed", extra={
                    'session_key': session_key,
                    'action': event['action'],
                    'duration': event['duration']
                })
            
            # Clean up session
            if session_key in self.active_sessions:
                del self.active_sessions[session_key]
                
        except Exception as e:
            lambda_logger.error("Error in typing simulation execution", extra={
                'session_key': session_key,
                'error': str(e)
            })
    
    def _send_typing_indicator(self, conversation_id: str, agent_id: str, is_typing: bool,
                             tenant_id: str, duration: float = None):
        """Send typing indicator via WebSocket"""
        try:
            # Prepare typing payload
            typing_payload = {
                'action': 'typing_indicator',
                'data': {
                    'conversationId': conversation_id,
                    'userId': agent_id,
                    'isTyping': is_typing,
                    'isAgent': True,
                    'agentType': self.active_sessions.get(f"{conversation_id}:{agent_id}", {}).get('agent_type'),
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            if is_typing and duration:
                typing_payload['data']['duration'] = int(duration)
            
            # Call WebSocket broadcast function
            if shared_available and self.lambda_client:
                broadcast_function_name = self.websocket_config.get('broadcast_function_name')
                
                if broadcast_function_name:
                    broadcast_payload = {
                        'message': typing_payload,
                        'targetType': 'conversation',
                        'targets': [conversation_id],
                        'senderUserId': agent_id,
                        'tenantId': tenant_id
                    }
                    
                    self.lambda_client.invoke(
                        FunctionName=broadcast_function_name,
                        InvocationType='Event',  # Async
                        Payload=json.dumps(broadcast_payload)
                    )
            
            lambda_logger.debug("Agent typing indicator sent", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'is_typing': is_typing,
                'duration': duration
            })
            
        except Exception as e:
            lambda_logger.warning("Failed to send agent typing indicator", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'is_typing': is_typing,
                'error': str(e)
            })
    
    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active typing sessions"""
        try:
            # Clean up expired sessions
            current_time = datetime.utcnow()
            expired_sessions = []
            
            for session_key, session in self.active_sessions.items():
                # Consider session expired after 5 minutes
                if (current_time - session['started_at']).total_seconds() > 300:
                    expired_sessions.append(session_key)
            
            for session_key in expired_sessions:
                if session_key in self.active_sessions:
                    del self.active_sessions[session_key]
            
            return dict(self.active_sessions)
            
        except Exception as e:
            lambda_logger.error("Failed to get active sessions", extra={
                'error': str(e)
            })
            return {}
    
    def get_typing_stats(self) -> Dict[str, Any]:
        """Get typing simulation statistics"""
        try:
            active_sessions = self.get_active_sessions()
            
            stats = {
                'active_sessions': len(active_sessions),
                'sessions_by_agent_type': {},
                'average_session_duration': 0,
                'total_sessions_started': len(active_sessions),  # This would be tracked separately in production
                'typing_patterns': list(self.typing_patterns.keys()),
                'simulation_enabled': self.agent_config.get('typing_simulation_enabled', True),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Calculate stats by agent type
            total_duration = 0
            for session in active_sessions.values():
                agent_type = session.get('agent_type', 'unknown')
                if agent_type not in stats['sessions_by_agent_type']:
                    stats['sessions_by_agent_type'][agent_type] = 0
                stats['sessions_by_agent_type'][agent_type] += 1
                
                duration = (datetime.utcnow() - session['started_at']).total_seconds()
                total_duration += duration
            
            if active_sessions:
                stats['average_session_duration'] = total_duration / len(active_sessions)
            
            return stats
            
        except Exception as e:
            lambda_logger.error("Failed to get typing stats", extra={
                'error': str(e)
            })
            return {
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global agent typing service instance
agent_typing_service = AgentTypingService()
