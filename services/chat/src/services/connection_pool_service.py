# services/chat/src/services/connection_pool_service.py
# Connection pooling service for WebSocket and database connections

import boto3
import time
import threading
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import weakref

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class ConnectionPoolService:
    """Service for managing connection pools for WebSocket and database connections"""
    
    def __init__(self):
        self.pool_config = config.get_connection_pool_config()
        
        # WebSocket connection pools
        self.websocket_pools = {
            'active_connections': {},      # connection_id -> connection_info
            'tenant_connections': defaultdict(set),  # tenant_id -> set of connection_ids
            'user_connections': defaultdict(set),    # user_id -> set of connection_ids
            'conversation_connections': defaultdict(set)  # conversation_id -> set of connection_ids
        }
        
        # Database connection pools
        self.db_pools = {
            'dynamodb_clients': [],
            'redis_clients': [],
            's3_clients': []
        }
        
        # Pool statistics
        self.pool_stats = {
            'websocket': {
                'total_connections': 0,
                'active_connections': 0,
                'peak_connections': 0,
                'connections_created': 0,
                'connections_closed': 0,
                'last_cleanup': datetime.utcnow()
            },
            'database': {
                'dynamodb_pool_size': 0,
                'redis_pool_size': 0,
                's3_pool_size': 0,
                'connections_reused': 0,
                'connections_created': 0
            }
        }
        
        # Configuration
        self.max_websocket_connections = self.pool_config.get('max_websocket_connections', 10000)
        self.max_db_pool_size = self.pool_config.get('max_db_pool_size', 50)
        self.connection_timeout = self.pool_config.get('connection_timeout', 300)  # 5 minutes
        self.cleanup_interval = self.pool_config.get('cleanup_interval', 60)  # 1 minute
        
        # Thread lock for thread safety
        self._lock = threading.Lock()
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    # WebSocket Connection Management
    def add_websocket_connection(self, connection_id: str, user_id: str, tenant_id: str,
                               conversation_id: str = None, metadata: Dict[str, Any] = None) -> bool:
        """Add WebSocket connection to pool"""
        try:
            with self._lock:
                # Check connection limits
                if len(self.websocket_pools['active_connections']) >= self.max_websocket_connections:
                    lambda_logger.warning("WebSocket connection limit reached", extra={
                        'current_connections': len(self.websocket_pools['active_connections']),
                        'max_connections': self.max_websocket_connections
                    })
                    return False
                
                # Create connection info
                connection_info = {
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'conversation_id': conversation_id,
                    'connected_at': datetime.utcnow(),
                    'last_activity': datetime.utcnow(),
                    'metadata': metadata or {}
                }
                
                # Add to pools
                self.websocket_pools['active_connections'][connection_id] = connection_info
                self.websocket_pools['tenant_connections'][tenant_id].add(connection_id)
                self.websocket_pools['user_connections'][user_id].add(connection_id)
                
                if conversation_id:
                    self.websocket_pools['conversation_connections'][conversation_id].add(connection_id)
                
                # Update statistics
                self.pool_stats['websocket']['total_connections'] += 1
                self.pool_stats['websocket']['active_connections'] = len(self.websocket_pools['active_connections'])
                self.pool_stats['websocket']['connections_created'] += 1
                
                if self.pool_stats['websocket']['active_connections'] > self.pool_stats['websocket']['peak_connections']:
                    self.pool_stats['websocket']['peak_connections'] = self.pool_stats['websocket']['active_connections']
                
                lambda_logger.info("WebSocket connection added to pool", extra={
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'conversation_id': conversation_id,
                    'active_connections': self.pool_stats['websocket']['active_connections']
                })
                
                return True
                
        except Exception as e:
            lambda_logger.error("Failed to add WebSocket connection to pool", extra={
                'connection_id': connection_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def remove_websocket_connection(self, connection_id: str) -> bool:
        """Remove WebSocket connection from pool"""
        try:
            with self._lock:
                connection_info = self.websocket_pools['active_connections'].get(connection_id)
                
                if not connection_info:
                    lambda_logger.debug("Connection not found in pool", extra={
                        'connection_id': connection_id
                    })
                    return False
                
                # Remove from all pools
                del self.websocket_pools['active_connections'][connection_id]
                
                tenant_id = connection_info['tenant_id']
                user_id = connection_info['user_id']
                conversation_id = connection_info.get('conversation_id')
                
                self.websocket_pools['tenant_connections'][tenant_id].discard(connection_id)
                self.websocket_pools['user_connections'][user_id].discard(connection_id)
                
                if conversation_id:
                    self.websocket_pools['conversation_connections'][conversation_id].discard(connection_id)
                
                # Update statistics
                self.pool_stats['websocket']['active_connections'] = len(self.websocket_pools['active_connections'])
                self.pool_stats['websocket']['connections_closed'] += 1
                
                lambda_logger.info("WebSocket connection removed from pool", extra={
                    'connection_id': connection_id,
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'active_connections': self.pool_stats['websocket']['active_connections']
                })
                
                return True
                
        except Exception as e:
            lambda_logger.error("Failed to remove WebSocket connection from pool", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return False
    
    def get_tenant_connections(self, tenant_id: str) -> List[str]:
        """Get all active connections for a tenant"""
        try:
            with self._lock:
                return list(self.websocket_pools['tenant_connections'].get(tenant_id, set()))
        except Exception as e:
            lambda_logger.error("Failed to get tenant connections", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def get_user_connections(self, user_id: str) -> List[str]:
        """Get all active connections for a user"""
        try:
            with self._lock:
                return list(self.websocket_pools['user_connections'].get(user_id, set()))
        except Exception as e:
            lambda_logger.error("Failed to get user connections", extra={
                'user_id': user_id,
                'error': str(e)
            })
            return []
    
    def get_conversation_connections(self, conversation_id: str) -> List[str]:
        """Get all active connections for a conversation"""
        try:
            with self._lock:
                return list(self.websocket_pools['conversation_connections'].get(conversation_id, set()))
        except Exception as e:
            lambda_logger.error("Failed to get conversation connections", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return []
    
    def update_connection_activity(self, connection_id: str) -> bool:
        """Update last activity timestamp for connection"""
        try:
            with self._lock:
                connection_info = self.websocket_pools['active_connections'].get(connection_id)
                
                if connection_info:
                    connection_info['last_activity'] = datetime.utcnow()
                    return True
                
                return False
                
        except Exception as e:
            lambda_logger.error("Failed to update connection activity", extra={
                'connection_id': connection_id,
                'error': str(e)
            })
            return False
    
    # Database Connection Management
    def get_dynamodb_client(self) -> Any:
        """Get DynamoDB client from pool or create new one"""
        try:
            with self._lock:
                # Try to reuse existing client
                if self.db_pools['dynamodb_clients']:
                    client = self.db_pools['dynamodb_clients'].pop()
                    self.pool_stats['database']['connections_reused'] += 1
                    lambda_logger.debug("Reused DynamoDB client from pool")
                    return client
                
                # Create new client if pool is empty
                if self.pool_stats['database']['dynamodb_pool_size'] < self.max_db_pool_size:
                    client = boto3.resource('dynamodb')
                    self.pool_stats['database']['dynamodb_pool_size'] += 1
                    self.pool_stats['database']['connections_created'] += 1
                    lambda_logger.debug("Created new DynamoDB client")
                    return client
                
                # Pool is full, create temporary client
                lambda_logger.warning("DynamoDB pool is full, creating temporary client")
                return boto3.resource('dynamodb')
                
        except Exception as e:
            lambda_logger.error("Failed to get DynamoDB client", extra={
                'error': str(e)
            })
            return boto3.resource('dynamodb')
    
    def return_dynamodb_client(self, client: Any) -> bool:
        """Return DynamoDB client to pool"""
        try:
            with self._lock:
                if len(self.db_pools['dynamodb_clients']) < self.max_db_pool_size:
                    self.db_pools['dynamodb_clients'].append(client)
                    lambda_logger.debug("Returned DynamoDB client to pool")
                    return True
                
                # Pool is full, let client be garbage collected
                lambda_logger.debug("DynamoDB pool is full, discarding client")
                return False
                
        except Exception as e:
            lambda_logger.error("Failed to return DynamoDB client to pool", extra={
                'error': str(e)
            })
            return False
    
    def get_s3_client(self) -> Any:
        """Get S3 client from pool or create new one"""
        try:
            with self._lock:
                # Try to reuse existing client
                if self.db_pools['s3_clients']:
                    client = self.db_pools['s3_clients'].pop()
                    self.pool_stats['database']['connections_reused'] += 1
                    lambda_logger.debug("Reused S3 client from pool")
                    return client
                
                # Create new client if pool is empty
                if self.pool_stats['database']['s3_pool_size'] < self.max_db_pool_size:
                    client = boto3.client('s3')
                    self.pool_stats['database']['s3_pool_size'] += 1
                    self.pool_stats['database']['connections_created'] += 1
                    lambda_logger.debug("Created new S3 client")
                    return client
                
                # Pool is full, create temporary client
                lambda_logger.warning("S3 pool is full, creating temporary client")
                return boto3.client('s3')
                
        except Exception as e:
            lambda_logger.error("Failed to get S3 client", extra={
                'error': str(e)
            })
            return boto3.client('s3')
    
    def return_s3_client(self, client: Any) -> bool:
        """Return S3 client to pool"""
        try:
            with self._lock:
                if len(self.db_pools['s3_clients']) < self.max_db_pool_size:
                    self.db_pools['s3_clients'].append(client)
                    lambda_logger.debug("Returned S3 client to pool")
                    return True
                
                # Pool is full, let client be garbage collected
                lambda_logger.debug("S3 pool is full, discarding client")
                return False
                
        except Exception as e:
            lambda_logger.error("Failed to return S3 client to pool", extra={
                'error': str(e)
            })
            return False
    
    # Pool Management
    def cleanup_stale_connections(self) -> int:
        """Clean up stale WebSocket connections"""
        try:
            with self._lock:
                current_time = datetime.utcnow()
                stale_connections = []
                
                # Find stale connections
                for connection_id, connection_info in self.websocket_pools['active_connections'].items():
                    last_activity = connection_info['last_activity']
                    if (current_time - last_activity).total_seconds() > self.connection_timeout:
                        stale_connections.append(connection_id)
                
                # Remove stale connections
                for connection_id in stale_connections:
                    self.remove_websocket_connection(connection_id)
                
                self.pool_stats['websocket']['last_cleanup'] = current_time
                
                if stale_connections:
                    lambda_logger.info("Cleaned up stale WebSocket connections", extra={
                        'stale_count': len(stale_connections),
                        'active_connections': self.pool_stats['websocket']['active_connections']
                    })
                
                return len(stale_connections)
                
        except Exception as e:
            lambda_logger.error("Failed to cleanup stale connections", extra={
                'error': str(e)
            })
            return 0
    
    def get_pool_statistics(self) -> Dict[str, Any]:
        """Get comprehensive pool statistics"""
        try:
            with self._lock:
                return {
                    'websocket': {
                        **self.pool_stats['websocket'],
                        'connections_by_tenant': {
                            tenant_id: len(connections) 
                            for tenant_id, connections in self.websocket_pools['tenant_connections'].items()
                        },
                        'pool_utilization': (
                            self.pool_stats['websocket']['active_connections'] / 
                            self.max_websocket_connections * 100
                        ) if self.max_websocket_connections > 0 else 0
                    },
                    'database': {
                        **self.pool_stats['database'],
                        'pool_utilization': {
                            'dynamodb': (self.pool_stats['database']['dynamodb_pool_size'] / self.max_db_pool_size * 100),
                            's3': (self.pool_stats['database']['s3_pool_size'] / self.max_db_pool_size * 100)
                        }
                    },
                    'configuration': {
                        'max_websocket_connections': self.max_websocket_connections,
                        'max_db_pool_size': self.max_db_pool_size,
                        'connection_timeout': self.connection_timeout,
                        'cleanup_interval': self.cleanup_interval
                    }
                }
                
        except Exception as e:
            lambda_logger.error("Failed to get pool statistics", extra={
                'error': str(e)
            })
            return {}
    
    def _start_cleanup_thread(self):
        """Start background thread for periodic cleanup"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    self.cleanup_stale_connections()
                except Exception as e:
                    lambda_logger.error("Error in cleanup thread", extra={
                        'error': str(e)
                    })
        
        # Start cleanup thread as daemon
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        
        lambda_logger.info("Connection pool cleanup thread started", extra={
            'cleanup_interval': self.cleanup_interval
        })


# Global connection pool service instance
connection_pool_service = ConnectionPoolService()
