# services/chat/src/services/file_notification_service.py
# Service for file sharing notifications via WebSocket

import json
import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class FileNotificationService:
    """Service for sending file-related notifications via WebSocket"""
    
    def __init__(self):
        if shared_available:
            self.lambda_client = boto3.client('lambda')
        else:
            self.lambda_client = None
        
        self.websocket_config = config.get_websocket_config()
        self.file_config = config.get_file_config()
    
    def notify_file_upload_started(self, user_id: str, tenant_id: str, conversation_id: str,
                                 file_info: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Notify that file upload has started
        
        Returns:
            (success, error_message)
        """
        try:
            notification_data = {
                'type': 'file_upload_started',
                'data': {
                    'fileId': file_info.get('fileId'),
                    'filename': file_info.get('filename'),
                    'fileSize': file_info.get('fileSize'),
                    'contentType': file_info.get('contentType'),
                    'uploadedBy': user_id,
                    'conversationId': conversation_id,
                    'timestamp': datetime.utcnow().isoformat(),
                    'status': 'uploading'
                }
            }
            
            # Send to conversation participants
            success, error_msg = self._send_to_conversation(
                conversation_id=conversation_id,
                notification=notification_data,
                sender_user_id=user_id,
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.debug("File upload started notification sent", extra={
                    'file_id': file_info.get('fileId'),
                    'conversation_id': conversation_id,
                    'user_id': user_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify file upload started", extra={
                'user_id': user_id,
                'conversation_id': conversation_id,
                'file_info': file_info,
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def notify_file_upload_completed(self, user_id: str, tenant_id: str, conversation_id: str,
                                   file_data: Dict[str, Any], message_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        Notify that file upload has completed
        
        Returns:
            (success, error_message)
        """
        try:
            notification_data = {
                'type': 'file_upload_completed',
                'data': {
                    'fileId': file_data.get('fileId'),
                    'filename': file_data.get('filename'),
                    'fileSize': file_data.get('fileSize'),
                    'contentType': file_data.get('contentType'),
                    'fileType': file_data.get('fileType'),
                    'uploadedBy': user_id,
                    'conversationId': conversation_id,
                    'messageId': message_id,
                    'downloadUrl': file_data.get('downloadUrl'),
                    'timestamp': datetime.utcnow().isoformat(),
                    'status': 'completed',
                    'metadata': {
                        'uploadedAt': file_data.get('uploadedAt'),
                        'checksum': file_data.get('metadata', {}).get('checksum')
                    }
                }
            }
            
            # Send to conversation participants
            success, error_msg = self._send_to_conversation(
                conversation_id=conversation_id,
                notification=notification_data,
                sender_user_id=user_id,
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.info("File upload completed notification sent", extra={
                    'file_id': file_data.get('fileId'),
                    'conversation_id': conversation_id,
                    'user_id': user_id,
                    'message_id': message_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify file upload completed", extra={
                'user_id': user_id,
                'conversation_id': conversation_id,
                'file_data': file_data,
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def notify_file_upload_failed(self, user_id: str, tenant_id: str, conversation_id: str,
                                file_info: Dict[str, Any], error_reason: str) -> Tuple[bool, Optional[str]]:
        """
        Notify that file upload has failed
        
        Returns:
            (success, error_message)
        """
        try:
            notification_data = {
                'type': 'file_upload_failed',
                'data': {
                    'fileId': file_info.get('fileId'),
                    'filename': file_info.get('filename'),
                    'uploadedBy': user_id,
                    'conversationId': conversation_id,
                    'timestamp': datetime.utcnow().isoformat(),
                    'status': 'failed',
                    'error': error_reason
                }
            }
            
            # Send only to the uploader
            success, error_msg = self._send_to_user(
                user_id=user_id,
                tenant_id=tenant_id,
                notification=notification_data
            )
            
            if success:
                lambda_logger.debug("File upload failed notification sent", extra={
                    'file_id': file_info.get('fileId'),
                    'conversation_id': conversation_id,
                    'user_id': user_id,
                    'error_reason': error_reason
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify file upload failed", extra={
                'user_id': user_id,
                'conversation_id': conversation_id,
                'file_info': file_info,
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def notify_file_shared(self, user_id: str, tenant_id: str, conversation_id: str,
                         file_data: Dict[str, Any], shared_with: List[str]) -> Tuple[bool, Optional[str]]:
        """
        Notify that file has been shared with specific users
        
        Returns:
            (success, error_message)
        """
        try:
            notification_data = {
                'type': 'file_shared',
                'data': {
                    'fileId': file_data.get('fileId'),
                    'filename': file_data.get('filename'),
                    'fileType': file_data.get('fileType'),
                    'sharedBy': user_id,
                    'conversationId': conversation_id,
                    'sharedWith': shared_with,
                    'downloadUrl': file_data.get('downloadUrl'),
                    'timestamp': datetime.utcnow().isoformat(),
                    'action': 'shared'
                }
            }
            
            # Send to specific users
            success_count = 0
            for target_user_id in shared_with:
                success, _ = self._send_to_user(
                    user_id=target_user_id,
                    tenant_id=tenant_id,
                    notification=notification_data
                )
                if success:
                    success_count += 1
            
            overall_success = success_count > 0
            
            if overall_success:
                lambda_logger.info("File shared notification sent", extra={
                    'file_id': file_data.get('fileId'),
                    'conversation_id': conversation_id,
                    'shared_by': user_id,
                    'shared_with_count': len(shared_with),
                    'successful_notifications': success_count
                })
            
            return overall_success, None if overall_success else "Failed to send any notifications"
            
        except Exception as e:
            lambda_logger.error("Failed to notify file shared", extra={
                'user_id': user_id,
                'conversation_id': conversation_id,
                'file_data': file_data,
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def notify_file_deleted(self, user_id: str, tenant_id: str, conversation_id: str,
                          file_info: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Notify that file has been deleted
        
        Returns:
            (success, error_message)
        """
        try:
            notification_data = {
                'type': 'file_deleted',
                'data': {
                    'fileId': file_info.get('fileId'),
                    'filename': file_info.get('filename'),
                    'deletedBy': user_id,
                    'conversationId': conversation_id,
                    'timestamp': datetime.utcnow().isoformat(),
                    'action': 'deleted'
                }
            }
            
            # Send to conversation participants
            success, error_msg = self._send_to_conversation(
                conversation_id=conversation_id,
                notification=notification_data,
                sender_user_id=user_id,
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.info("File deleted notification sent", extra={
                    'file_id': file_info.get('fileId'),
                    'conversation_id': conversation_id,
                    'deleted_by': user_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify file deleted", extra={
                'user_id': user_id,
                'conversation_id': conversation_id,
                'file_info': file_info,
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def notify_file_download(self, user_id: str, tenant_id: str, file_id: str,
                           filename: str, downloaded_by: str) -> Tuple[bool, Optional[str]]:
        """
        Notify file owner about download (optional feature)
        
        Returns:
            (success, error_message)
        """
        try:
            # Only notify if different user downloaded
            if user_id == downloaded_by:
                return True, None
            
            notification_data = {
                'type': 'file_downloaded',
                'data': {
                    'fileId': file_id,
                    'filename': filename,
                    'downloadedBy': downloaded_by,
                    'timestamp': datetime.utcnow().isoformat(),
                    'action': 'downloaded'
                }
            }
            
            # Send to file owner
            success, error_msg = self._send_to_user(
                user_id=user_id,
                tenant_id=tenant_id,
                notification=notification_data
            )
            
            if success:
                lambda_logger.debug("File download notification sent", extra={
                    'file_id': file_id,
                    'file_owner': user_id,
                    'downloaded_by': downloaded_by
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify file download", extra={
                'user_id': user_id,
                'file_id': file_id,
                'downloaded_by': downloaded_by,
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def _send_to_conversation(self, conversation_id: str, notification: Dict[str, Any],
                            sender_user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Send notification to all conversation participants"""
        try:
            if not shared_available or not self.lambda_client:
                lambda_logger.debug("WebSocket notification (mock)", extra={
                    'conversation_id': conversation_id,
                    'notification_type': notification['type']
                })
                return True, None
            
            # Call WebSocket broadcast function
            broadcast_function_name = self.websocket_config.get('broadcast_function_name')
            
            if not broadcast_function_name:
                return False, "WebSocket broadcast function not configured"
            
            broadcast_payload = {
                'message': notification,
                'targetType': 'conversation',
                'targets': [conversation_id],
                'senderUserId': sender_user_id,
                'tenantId': tenant_id
            }
            
            response = self.lambda_client.invoke(
                FunctionName=broadcast_function_name,
                InvocationType='Event',  # Async
                Payload=json.dumps(broadcast_payload)
            )
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to send conversation notification", extra={
                'conversation_id': conversation_id,
                'notification_type': notification['type'],
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"
    
    def _send_to_user(self, user_id: str, tenant_id: str, notification: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Send notification to specific user"""
        try:
            if not shared_available or not self.lambda_client:
                lambda_logger.debug("WebSocket notification to user (mock)", extra={
                    'user_id': user_id,
                    'notification_type': notification['type']
                })
                return True, None
            
            # Call WebSocket broadcast function
            broadcast_function_name = self.websocket_config.get('broadcast_function_name')
            
            if not broadcast_function_name:
                return False, "WebSocket broadcast function not configured"
            
            broadcast_payload = {
                'message': notification,
                'targetType': 'user',
                'targets': [user_id],
                'tenantId': tenant_id
            }
            
            response = self.lambda_client.invoke(
                FunctionName=broadcast_function_name,
                InvocationType='Event',  # Async
                Payload=json.dumps(broadcast_payload)
            )
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to send user notification", extra={
                'user_id': user_id,
                'notification_type': notification['type'],
                'error': str(e)
            })
            return False, f"Failed to send notification: {str(e)}"


# Global file notification service instance
file_notification_service = FileNotificationService()
