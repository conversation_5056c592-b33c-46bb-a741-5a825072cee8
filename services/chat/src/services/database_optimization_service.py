# services/chat/src/services/database_optimization_service.py
# DynamoDB optimization service with GSI and efficient queries

import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError

try:
    from shared.logger import lambda_logger
    from shared.database import get_dynamodb_table
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()
    
    def get_dynamodb_table(table_name):
        return None

from ..utils.config import config

class DatabaseOptimizationService:
    """Service for optimized DynamoDB queries and operations"""
    
    def __init__(self):
        self.table = get_dynamodb_table(config.table_name) if shared_available else None
        self.dynamodb = boto3.resource('dynamodb') if not shared_available else None
        
        # GSI configurations
        self.gsi_configs = {
            'UserConversationsIndex': {
                'pk': 'GSI1PK',  # USER#{tenant_id}#{user_id}
                'sk': 'GSI1SK',  # CONV#{timestamp}
                'projection': 'ALL'
            },
            'ConversationMessagesIndex': {
                'pk': 'GSI2PK',  # CONV#{conversation_id}
                'sk': 'GSI2SK',  # MSG#{timestamp}
                'projection': 'ALL'
            },
            'TenantResourcesIndex': {
                'pk': 'GSI3PK',  # TENANT#{tenant_id}
                'sk': 'GSI3SK',  # {resource_type}#{timestamp}
                'projection': 'ALL'
            },
            'UserActivityIndex': {
                'pk': 'GSI4PK',  # USER#{tenant_id}#{user_id}
                'sk': 'GSI4SK',  # ACTIVITY#{timestamp}
                'projection': 'KEYS_ONLY'
            },
            'FilesByConversationIndex': {
                'pk': 'GSI5PK',  # CONV#{conversation_id}
                'sk': 'GSI5SK',  # FILE#{timestamp}
                'projection': 'ALL'
            }
        }
        
        # Query optimization settings
        self.batch_size = 25  # DynamoDB batch limit
        self.max_page_size = 100
        self.default_page_size = 20
    
    # Optimized Conversation Queries
    def get_user_conversations_optimized(self, user_id: str, tenant_id: str, 
                                       limit: int = 20, last_evaluated_key: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get user conversations using optimized GSI query"""
        try:
            if not self.table:
                return False, {}, "Database not available"
            
            # Use GSI1 for user conversations
            gsi1_pk = f"USER#{tenant_id}#{user_id}"
            
            query_params = {
                'IndexName': 'UserConversationsIndex',
                'KeyConditionExpression': Key('GSI1PK').eq(gsi1_pk),
                'ScanIndexForward': False,  # Latest first
                'Limit': min(limit, self.max_page_size)
            }
            
            if last_evaluated_key:
                query_params['ExclusiveStartKey'] = self._decode_pagination_key(last_evaluated_key)
            
            response = self.table.query(**query_params)
            
            conversations = []
            for item in response.get('Items', []):
                # Transform DynamoDB item to conversation format
                conversation = self._transform_conversation_item(item)
                if conversation:
                    conversations.append(conversation)
            
            result = {
                'conversations': conversations,
                'lastEvaluatedKey': self._encode_pagination_key(response.get('LastEvaluatedKey')),
                'hasMore': 'LastEvaluatedKey' in response,
                'count': len(conversations)
            }
            
            lambda_logger.debug("User conversations retrieved with GSI", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'count': len(conversations),
                'has_more': result['hasMore']
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get user conversations", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get conversations: {str(e)}"
    
    def get_conversation_messages_optimized(self, conversation_id: str, tenant_id: str,
                                          limit: int = 50, last_evaluated_key: str = None,
                                          start_time: datetime = None, end_time: datetime = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get conversation messages using optimized GSI query"""
        try:
            if not self.table:
                return False, {}, "Database not available"
            
            # Use GSI2 for conversation messages
            gsi2_pk = f"CONV#{conversation_id}"
            
            # Build key condition
            key_condition = Key('GSI2PK').eq(gsi2_pk)
            
            # Add time range if specified
            if start_time and end_time:
                start_sk = f"MSG#{start_time.isoformat()}"
                end_sk = f"MSG#{end_time.isoformat()}"
                key_condition = key_condition & Key('GSI2SK').between(start_sk, end_sk)
            elif start_time:
                start_sk = f"MSG#{start_time.isoformat()}"
                key_condition = key_condition & Key('GSI2SK').gte(start_sk)
            elif end_time:
                end_sk = f"MSG#{end_time.isoformat()}"
                key_condition = key_condition & Key('GSI2SK').lte(end_sk)
            
            query_params = {
                'IndexName': 'ConversationMessagesIndex',
                'KeyConditionExpression': key_condition,
                'ScanIndexForward': False,  # Latest first
                'Limit': min(limit, self.max_page_size),
                'FilterExpression': Attr('tenantId').eq(tenant_id)  # Additional security
            }
            
            if last_evaluated_key:
                query_params['ExclusiveStartKey'] = self._decode_pagination_key(last_evaluated_key)
            
            response = self.table.query(**query_params)
            
            messages = []
            for item in response.get('Items', []):
                # Transform DynamoDB item to message format
                message = self._transform_message_item(item)
                if message:
                    messages.append(message)
            
            result = {
                'messages': messages,
                'lastEvaluatedKey': self._encode_pagination_key(response.get('LastEvaluatedKey')),
                'hasMore': 'LastEvaluatedKey' in response,
                'count': len(messages),
                'conversationId': conversation_id
            }
            
            lambda_logger.debug("Conversation messages retrieved with GSI", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'count': len(messages),
                'has_more': result['hasMore']
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation messages", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get messages: {str(e)}"
    
    def get_tenant_resources_optimized(self, tenant_id: str, resource_type: str = None,
                                     limit: int = 50, last_evaluated_key: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get tenant resources using optimized GSI query"""
        try:
            if not self.table:
                return False, {}, "Database not available"
            
            # Use GSI3 for tenant resources
            gsi3_pk = f"TENANT#{tenant_id}"
            
            # Build key condition
            key_condition = Key('GSI3PK').eq(gsi3_pk)
            
            if resource_type:
                # Filter by resource type
                key_condition = key_condition & Key('GSI3SK').begins_with(f"{resource_type}#")
            
            query_params = {
                'IndexName': 'TenantResourcesIndex',
                'KeyConditionExpression': key_condition,
                'ScanIndexForward': False,  # Latest first
                'Limit': min(limit, self.max_page_size)
            }
            
            if last_evaluated_key:
                query_params['ExclusiveStartKey'] = self._decode_pagination_key(last_evaluated_key)
            
            response = self.table.query(**query_params)
            
            resources = []
            for item in response.get('Items', []):
                # Transform DynamoDB item to resource format
                resource = self._transform_resource_item(item)
                if resource:
                    resources.append(resource)
            
            result = {
                'resources': resources,
                'lastEvaluatedKey': self._encode_pagination_key(response.get('LastEvaluatedKey')),
                'hasMore': 'LastEvaluatedKey' in response,
                'count': len(resources),
                'resourceType': resource_type
            }
            
            lambda_logger.debug("Tenant resources retrieved with GSI", extra={
                'tenant_id': tenant_id,
                'resource_type': resource_type,
                'count': len(resources)
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant resources", extra={
                'tenant_id': tenant_id,
                'resource_type': resource_type,
                'error': str(e)
            })
            return False, {}, f"Failed to get resources: {str(e)}"
    
    def get_user_activity_optimized(self, user_id: str, tenant_id: str,
                                  activity_type: str = None, days_back: int = 7) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get user activity using optimized GSI query"""
        try:
            if not self.table:
                return False, {}, "Database not available"
            
            # Use GSI4 for user activity
            gsi4_pk = f"USER#{tenant_id}#{user_id}"
            
            # Calculate time range
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=days_back)
            
            # Build key condition
            key_condition = Key('GSI4PK').eq(gsi4_pk)
            
            if activity_type:
                start_sk = f"ACTIVITY#{activity_type}#{start_time.isoformat()}"
                end_sk = f"ACTIVITY#{activity_type}#{end_time.isoformat()}"
                key_condition = key_condition & Key('GSI4SK').between(start_sk, end_sk)
            else:
                start_sk = f"ACTIVITY#{start_time.isoformat()}"
                key_condition = key_condition & Key('GSI4SK').gte(start_sk)
            
            query_params = {
                'IndexName': 'UserActivityIndex',
                'KeyConditionExpression': key_condition,
                'ScanIndexForward': False,  # Latest first
                'Limit': 100  # Activity queries can be larger
            }
            
            response = self.table.query(**query_params)
            
            activities = []
            for item in response.get('Items', []):
                # Transform DynamoDB item to activity format
                activity = self._transform_activity_item(item)
                if activity:
                    activities.append(activity)
            
            result = {
                'activities': activities,
                'count': len(activities),
                'activityType': activity_type,
                'daysBack': days_back,
                'period': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat()
                }
            }
            
            lambda_logger.debug("User activity retrieved with GSI", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'activity_type': activity_type,
                'count': len(activities)
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get user activity", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get activity: {str(e)}"
    
    def get_conversation_files_optimized(self, conversation_id: str, tenant_id: str,
                                       limit: int = 20, last_evaluated_key: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get conversation files using optimized GSI query"""
        try:
            if not self.table:
                return False, {}, "Database not available"
            
            # Use GSI5 for conversation files
            gsi5_pk = f"CONV#{conversation_id}"
            
            query_params = {
                'IndexName': 'FilesByConversationIndex',
                'KeyConditionExpression': Key('GSI5PK').eq(gsi5_pk),
                'ScanIndexForward': False,  # Latest first
                'Limit': min(limit, self.max_page_size),
                'FilterExpression': Attr('tenantId').eq(tenant_id)  # Additional security
            }
            
            if last_evaluated_key:
                query_params['ExclusiveStartKey'] = self._decode_pagination_key(last_evaluated_key)
            
            response = self.table.query(**query_params)
            
            files = []
            for item in response.get('Items', []):
                # Transform DynamoDB item to file format
                file_item = self._transform_file_item(item)
                if file_item:
                    files.append(file_item)
            
            result = {
                'files': files,
                'lastEvaluatedKey': self._encode_pagination_key(response.get('LastEvaluatedKey')),
                'hasMore': 'LastEvaluatedKey' in response,
                'count': len(files),
                'conversationId': conversation_id
            }
            
            lambda_logger.debug("Conversation files retrieved with GSI", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'count': len(files)
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation files", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get files: {str(e)}"
    
    # Batch Operations
    def batch_get_items_optimized(self, keys: List[Dict[str, str]]) -> Tuple[bool, List[Dict[str, Any]], Optional[str]]:
        """Optimized batch get operation"""
        try:
            if not self.table or not keys:
                return True, [], None
            
            items = []
            
            # Process in batches of 25 (DynamoDB limit)
            for i in range(0, len(keys), self.batch_size):
                batch_keys = keys[i:i + self.batch_size]
                
                response = self.table.meta.client.batch_get_item(
                    RequestItems={
                        self.table.table_name: {
                            'Keys': batch_keys
                        }
                    }
                )
                
                batch_items = response.get('Responses', {}).get(self.table.table_name, [])
                items.extend(batch_items)
                
                # Handle unprocessed keys
                unprocessed = response.get('UnprocessedKeys', {})
                if unprocessed:
                    lambda_logger.warning("Unprocessed keys in batch get", extra={
                        'unprocessed_count': len(unprocessed.get(self.table.table_name, {}).get('Keys', []))
                    })
            
            lambda_logger.debug("Batch get completed", extra={
                'requested_keys': len(keys),
                'retrieved_items': len(items)
            })
            
            return True, items, None
            
        except Exception as e:
            lambda_logger.error("Failed to batch get items", extra={
                'key_count': len(keys),
                'error': str(e)
            })
            return False, [], f"Batch get failed: {str(e)}"
    
    def batch_write_items_optimized(self, items: List[Dict[str, Any]], operation: str = 'put') -> Tuple[bool, Optional[str]]:
        """Optimized batch write operation"""
        try:
            if not self.table or not items:
                return True, None
            
            # Process in batches of 25 (DynamoDB limit)
            for i in range(0, len(items), self.batch_size):
                batch_items = items[i:i + self.batch_size]
                
                # Prepare batch request
                request_items = []
                for item in batch_items:
                    if operation == 'put':
                        request_items.append({'PutRequest': {'Item': item}})
                    elif operation == 'delete':
                        request_items.append({'DeleteRequest': {'Key': item}})
                
                response = self.table.meta.client.batch_write_item(
                    RequestItems={
                        self.table.table_name: request_items
                    }
                )
                
                # Handle unprocessed items
                unprocessed = response.get('UnprocessedItems', {})
                if unprocessed:
                    lambda_logger.warning("Unprocessed items in batch write", extra={
                        'unprocessed_count': len(unprocessed.get(self.table.table_name, []))
                    })
            
            lambda_logger.debug("Batch write completed", extra={
                'item_count': len(items),
                'operation': operation
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to batch write items", extra={
                'item_count': len(items),
                'operation': operation,
                'error': str(e)
            })
            return False, f"Batch write failed: {str(e)}"
    
    # Helper Methods
    def _transform_conversation_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform DynamoDB item to conversation format"""
        try:
            return {
                'conversationId': item.get('conversationId'),
                'title': item.get('title'),
                'status': item.get('status'),
                'participants': item.get('participants', []),
                'lastMessage': item.get('lastMessage'),
                'lastMessageAt': item.get('lastMessageAt'),
                'createdAt': item.get('createdAt'),
                'updatedAt': item.get('updatedAt'),
                'tenantId': item.get('tenantId'),
                'metadata': item.get('metadata', {})
            }
        except Exception:
            return None
    
    def _transform_message_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform DynamoDB item to message format"""
        try:
            return {
                'messageId': item.get('messageId'),
                'conversationId': item.get('conversationId'),
                'content': item.get('content'),
                'type': item.get('type'),
                'userId': item.get('userId'),
                'timestamp': item.get('timestamp'),
                'status': item.get('status'),
                'attachments': item.get('attachments', []),
                'metadata': item.get('metadata', {}),
                'tenantId': item.get('tenantId')
            }
        except Exception:
            return None
    
    def _transform_resource_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform DynamoDB item to resource format"""
        try:
            return {
                'resourceId': item.get('PK', '').split('#')[-1],
                'resourceType': item.get('GSI3SK', '').split('#')[0],
                'data': item,
                'createdAt': item.get('createdAt'),
                'updatedAt': item.get('updatedAt')
            }
        except Exception:
            return None
    
    def _transform_activity_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform DynamoDB item to activity format"""
        try:
            return {
                'activityId': item.get('PK'),
                'activityType': item.get('GSI4SK', '').split('#')[1] if '#' in item.get('GSI4SK', '') else 'unknown',
                'timestamp': item.get('timestamp'),
                'data': item.get('data', {}),
                'userId': item.get('userId'),
                'tenantId': item.get('tenantId')
            }
        except Exception:
            return None
    
    def _transform_file_item(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform DynamoDB item to file format"""
        try:
            return {
                'fileId': item.get('fileId'),
                'filename': item.get('filename'),
                'fileSize': item.get('fileSize'),
                'contentType': item.get('contentType'),
                'fileKey': item.get('fileKey'),
                'uploadedBy': item.get('uploadedBy'),
                'uploadedAt': item.get('uploadedAt'),
                'conversationId': item.get('conversationId'),
                'tenantId': item.get('tenantId'),
                'metadata': item.get('metadata', {})
            }
        except Exception:
            return None
    
    def _encode_pagination_key(self, key: Optional[Dict[str, Any]]) -> Optional[str]:
        """Encode pagination key for API response"""
        if not key:
            return None
        
        try:
            import base64
            import json
            key_str = json.dumps(key, sort_keys=True)
            return base64.b64encode(key_str.encode()).decode()
        except Exception:
            return None
    
    def _decode_pagination_key(self, encoded_key: str) -> Optional[Dict[str, Any]]:
        """Decode pagination key from API request"""
        try:
            import base64
            import json
            key_str = base64.b64decode(encoded_key.encode()).decode()
            return json.loads(key_str)
        except Exception:
            return None


# Global database optimization service instance
db_optimization_service = DatabaseOptimizationService()
