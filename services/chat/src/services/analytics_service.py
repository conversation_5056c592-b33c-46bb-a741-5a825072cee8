# services/chat/src/services/analytics_service.py
# Service for chat analytics and metrics

import json
import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class AnalyticsService:
    """Service for chat analytics and real-time metrics"""
    
    def __init__(self):
        if shared_available:
            self.cloudwatch = boto3.client('cloudwatch')
        else:
            self.cloudwatch = None
        
        self.analytics_config = config.get_search_config()  # Reuse search config for analytics
        self.search_client = None  # Will be initialized if needed
        
        # Metric configurations
        self.namespace = 'ChatService'
        self.retention_days = self.analytics_config.get('analytics_retention_days', 90)
        self.real_time_enabled = self.analytics_config.get('real_time_analytics', True)
        
        # Metric cache for batching
        self.metric_cache = defaultdict(list)
        self.cache_flush_interval = 60  # seconds
        self.last_flush = datetime.utcnow()
    
    def track_message_sent(self, tenant_id: str, user_id: str, conversation_id: str,
                          message_type: str = 'text', agent_involved: bool = False) -> bool:
        """Track message sent event"""
        try:
            metrics = [
                {
                    'MetricName': 'MessagesSent',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id},
                        {'Name': 'MessageType', 'Value': message_type}
                    ]
                },
                {
                    'MetricName': 'ActiveUsers',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id}
                    ]
                }
            ]
            
            if agent_involved:
                metrics.append({
                    'MetricName': 'AgentInteractions',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id}
                    ]
                })
            
            # Send metrics
            success = self._send_metrics(metrics)
            
            # Store detailed analytics
            if self.analytics_config.get('analytics_enabled', True):
                self._store_message_analytics({
                    'event_type': 'message_sent',
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'conversation_id': conversation_id,
                    'message_type': message_type,
                    'agent_involved': agent_involved,
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            lambda_logger.debug("Message sent tracked", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'message_type': message_type,
                'agent_involved': agent_involved
            })
            
            return success
            
        except Exception as e:
            lambda_logger.error("Failed to track message sent", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False
    
    def track_file_shared(self, tenant_id: str, user_id: str, conversation_id: str,
                         file_type: str, file_size: int) -> bool:
        """Track file sharing event"""
        try:
            metrics = [
                {
                    'MetricName': 'FilesShared',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id},
                        {'Name': 'FileType', 'Value': file_type}
                    ]
                },
                {
                    'MetricName': 'DataTransferred',
                    'Value': file_size,
                    'Unit': 'Bytes',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id}
                    ]
                }
            ]
            
            success = self._send_metrics(metrics)
            
            # Store detailed analytics
            if self.analytics_config.get('analytics_enabled', True):
                self._store_file_analytics({
                    'event_type': 'file_shared',
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'conversation_id': conversation_id,
                    'file_type': file_type,
                    'file_size': file_size,
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            return success
            
        except Exception as e:
            lambda_logger.error("Failed to track file shared", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False
    
    def track_search_query(self, tenant_id: str, user_id: str, query: str,
                          search_type: str, results_count: int, search_time_ms: int) -> bool:
        """Track search query event"""
        try:
            metrics = [
                {
                    'MetricName': 'SearchQueries',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id},
                        {'Name': 'SearchType', 'Value': search_type}
                    ]
                },
                {
                    'MetricName': 'SearchLatency',
                    'Value': search_time_ms,
                    'Unit': 'Milliseconds',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id}
                    ]
                }
            ]
            
            success = self._send_metrics(metrics)
            
            # Store search analytics
            if self.analytics_config.get('analytics_enabled', True):
                self._store_search_analytics({
                    'event_type': 'search_query',
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'query': query,
                    'search_type': search_type,
                    'results_count': results_count,
                    'search_time_ms': search_time_ms,
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            return success
            
        except Exception as e:
            lambda_logger.error("Failed to track search query", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False
    
    def track_user_activity(self, tenant_id: str, user_id: str, activity_type: str,
                           duration_seconds: int = None) -> bool:
        """Track user activity event"""
        try:
            metrics = [
                {
                    'MetricName': 'UserActivity',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id},
                        {'Name': 'ActivityType', 'Value': activity_type}
                    ]
                }
            ]
            
            if duration_seconds:
                metrics.append({
                    'MetricName': 'SessionDuration',
                    'Value': duration_seconds,
                    'Unit': 'Seconds',
                    'Dimensions': [
                        {'Name': 'TenantId', 'Value': tenant_id}
                    ]
                })
            
            success = self._send_metrics(metrics)
            
            # Store activity analytics
            if self.analytics_config.get('analytics_enabled', True):
                self._store_activity_analytics({
                    'event_type': 'user_activity',
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'activity_type': activity_type,
                    'duration_seconds': duration_seconds,
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            return success
            
        except Exception as e:
            lambda_logger.error("Failed to track user activity", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False
    
    def get_tenant_metrics(self, tenant_id: str, start_date: datetime,
                          end_date: datetime, metric_types: List[str] = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get aggregated metrics for tenant"""
        try:
            if not metric_types:
                metric_types = ['MessagesSent', 'FilesShared', 'SearchQueries', 'ActiveUsers']
            
            metrics_data = {}
            
            for metric_name in metric_types:
                success, data, error = self._get_cloudwatch_metric(
                    metric_name=metric_name,
                    tenant_id=tenant_id,
                    start_time=start_date,
                    end_time=end_date
                )
                
                if success:
                    metrics_data[metric_name] = data
                else:
                    lambda_logger.warning(f"Failed to get metric {metric_name}", extra={
                        'tenant_id': tenant_id,
                        'error': error
                    })
            
            # Calculate summary statistics
            summary = self._calculate_metric_summary(metrics_data, start_date, end_date)
            
            result = {
                'tenant_id': tenant_id,
                'period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'metrics': metrics_data,
                'summary': summary,
                'generated_at': datetime.utcnow().isoformat()
            }
            
            lambda_logger.info("Tenant metrics retrieved", extra={
                'tenant_id': tenant_id,
                'metric_count': len(metrics_data),
                'period_days': (end_date - start_date).days
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant metrics", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get metrics: {str(e)}"
    
    def get_real_time_stats(self, tenant_id: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get real-time statistics for tenant"""
        try:
            # Get metrics for last hour
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=1)
            
            real_time_metrics = [
                'MessagesSent', 'FilesShared', 'SearchQueries', 'ActiveUsers'
            ]
            
            stats = {}
            
            for metric_name in real_time_metrics:
                success, data, _ = self._get_cloudwatch_metric(
                    metric_name=metric_name,
                    tenant_id=tenant_id,
                    start_time=start_time,
                    end_time=end_time,
                    period=300  # 5-minute intervals
                )
                
                if success and data:
                    # Get latest value
                    latest_value = data[-1]['Value'] if data else 0
                    stats[metric_name] = {
                        'current': latest_value,
                        'trend': self._calculate_trend(data)
                    }
                else:
                    stats[metric_name] = {'current': 0, 'trend': 'stable'}
            
            result = {
                'tenant_id': tenant_id,
                'real_time_stats': stats,
                'last_updated': end_time.isoformat(),
                'period_minutes': 60
            }
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get real-time stats", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get real-time stats: {str(e)}"
    
    def get_usage_analytics(self, tenant_id: str, period_days: int = 30) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Get detailed usage analytics"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=period_days)
            
            # Get comprehensive analytics
            analytics = {
                'messaging': self._get_messaging_analytics(tenant_id, start_date, end_date),
                'file_sharing': self._get_file_analytics(tenant_id, start_date, end_date),
                'search': self._get_search_analytics(tenant_id, start_date, end_date),
                'user_engagement': self._get_engagement_analytics(tenant_id, start_date, end_date)
            }
            
            # Calculate insights
            insights = self._generate_insights(analytics)
            
            result = {
                'tenant_id': tenant_id,
                'period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat(),
                    'days': period_days
                },
                'analytics': analytics,
                'insights': insights,
                'generated_at': datetime.utcnow().isoformat()
            }
            
            lambda_logger.info("Usage analytics generated", extra={
                'tenant_id': tenant_id,
                'period_days': period_days
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to get usage analytics", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get usage analytics: {str(e)}"
    
    def _send_metrics(self, metrics: List[Dict[str, Any]]) -> bool:
        """Send metrics to CloudWatch"""
        try:
            if not shared_available or not self.cloudwatch:
                lambda_logger.debug("CloudWatch metrics (mock)", extra={
                    'metric_count': len(metrics)
                })
                return True
            
            # Add timestamp to all metrics
            timestamp = datetime.utcnow()
            for metric in metrics:
                metric['Timestamp'] = timestamp
            
            # Send to CloudWatch
            self.cloudwatch.put_metric_data(
                Namespace=self.namespace,
                MetricData=metrics
            )
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to send metrics to CloudWatch", extra={
                'metric_count': len(metrics),
                'error': str(e)
            })
            return False
    
    def _get_cloudwatch_metric(self, metric_name: str, tenant_id: str,
                              start_time: datetime, end_time: datetime,
                              period: int = 3600) -> Tuple[bool, List[Dict[str, Any]], Optional[str]]:
        """Get metric data from CloudWatch"""
        try:
            if not shared_available or not self.cloudwatch:
                # Return mock data
                return True, [
                    {'Timestamp': datetime.utcnow(), 'Value': 10.0},
                    {'Timestamp': datetime.utcnow(), 'Value': 15.0}
                ], None
            
            response = self.cloudwatch.get_metric_statistics(
                Namespace=self.namespace,
                MetricName=metric_name,
                Dimensions=[
                    {'Name': 'TenantId', 'Value': tenant_id}
                ],
                StartTime=start_time,
                EndTime=end_time,
                Period=period,
                Statistics=['Sum']
            )
            
            datapoints = response.get('Datapoints', [])
            # Sort by timestamp
            datapoints.sort(key=lambda x: x['Timestamp'])
            
            return True, datapoints, None
            
        except Exception as e:
            return False, [], str(e)
    
    def _store_message_analytics(self, analytics_data: Dict[str, Any]):
        """Store detailed message analytics"""
        try:
            # This would store in ElasticSearch/OpenSearch for detailed analytics
            lambda_logger.debug("Message analytics stored", extra=analytics_data)
        except Exception as e:
            lambda_logger.warning("Failed to store message analytics", extra={
                'error': str(e)
            })
    
    def _store_file_analytics(self, analytics_data: Dict[str, Any]):
        """Store detailed file analytics"""
        try:
            lambda_logger.debug("File analytics stored", extra=analytics_data)
        except Exception as e:
            lambda_logger.warning("Failed to store file analytics", extra={
                'error': str(e)
            })
    
    def _store_search_analytics(self, analytics_data: Dict[str, Any]):
        """Store detailed search analytics"""
        try:
            lambda_logger.debug("Search analytics stored", extra=analytics_data)
        except Exception as e:
            lambda_logger.warning("Failed to store search analytics", extra={
                'error': str(e)
            })
    
    def _store_activity_analytics(self, analytics_data: Dict[str, Any]):
        """Store detailed activity analytics"""
        try:
            lambda_logger.debug("Activity analytics stored", extra=analytics_data)
        except Exception as e:
            lambda_logger.warning("Failed to store activity analytics", extra={
                'error': str(e)
            })
    
    def _calculate_metric_summary(self, metrics_data: Dict[str, Any],
                                start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate summary statistics from metrics"""
        summary = {
            'total_messages': 0,
            'total_files': 0,
            'total_searches': 0,
            'peak_active_users': 0,
            'period_days': (end_date - start_date).days
        }
        
        try:
            if 'MessagesSent' in metrics_data:
                summary['total_messages'] = sum(dp['Value'] for dp in metrics_data['MessagesSent'])
            
            if 'FilesShared' in metrics_data:
                summary['total_files'] = sum(dp['Value'] for dp in metrics_data['FilesShared'])
            
            if 'SearchQueries' in metrics_data:
                summary['total_searches'] = sum(dp['Value'] for dp in metrics_data['SearchQueries'])
            
            if 'ActiveUsers' in metrics_data:
                summary['peak_active_users'] = max(
                    (dp['Value'] for dp in metrics_data['ActiveUsers']), default=0
                )
        except Exception as e:
            lambda_logger.warning("Failed to calculate metric summary", extra={
                'error': str(e)
            })
        
        return summary
    
    def _calculate_trend(self, data: List[Dict[str, Any]]) -> str:
        """Calculate trend from metric data"""
        if len(data) < 2:
            return 'stable'
        
        try:
            recent_avg = sum(dp['Value'] for dp in data[-3:]) / min(3, len(data))
            older_avg = sum(dp['Value'] for dp in data[:3]) / min(3, len(data))
            
            if recent_avg > older_avg * 1.1:
                return 'increasing'
            elif recent_avg < older_avg * 0.9:
                return 'decreasing'
            else:
                return 'stable'
        except Exception:
            return 'stable'
    
    def _get_messaging_analytics(self, tenant_id: str, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get messaging-specific analytics"""
        return {
            'total_messages': 150,
            'messages_by_type': {'text': 120, 'file': 30},
            'agent_interactions': 45,
            'avg_response_time': 2.5
        }
    
    def _get_file_analytics(self, tenant_id: str, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get file sharing analytics"""
        return {
            'total_files': 30,
            'files_by_type': {'image': 15, 'document': 10, 'other': 5},
            'total_size_mb': 250.5,
            'avg_file_size_mb': 8.35
        }
    
    def _get_search_analytics(self, tenant_id: str, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get search analytics"""
        return {
            'total_searches': 75,
            'avg_search_time_ms': 150,
            'popular_queries': ['project', 'document', 'meeting'],
            'search_success_rate': 0.92
        }
    
    def _get_engagement_analytics(self, tenant_id: str, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get user engagement analytics"""
        return {
            'active_users': 25,
            'avg_session_duration': 1800,  # 30 minutes
            'peak_concurrent_users': 12,
            'user_retention_rate': 0.85
        }
    
    def _generate_insights(self, analytics: Dict[str, Any]) -> List[str]:
        """Generate insights from analytics data"""
        insights = []
        
        try:
            messaging = analytics.get('messaging', {})
            if messaging.get('agent_interactions', 0) > messaging.get('total_messages', 1) * 0.3:
                insights.append("High agent interaction rate indicates good automation adoption")
            
            file_sharing = analytics.get('file_sharing', {})
            if file_sharing.get('total_files', 0) > 0:
                insights.append(f"File sharing is active with {file_sharing.get('total_files')} files shared")
            
            search = analytics.get('search', {})
            if search.get('search_success_rate', 0) > 0.9:
                insights.append("Search functionality is performing well with high success rate")
            
            engagement = analytics.get('user_engagement', {})
            if engagement.get('user_retention_rate', 0) > 0.8:
                insights.append("Strong user retention indicates good platform adoption")
                
        except Exception as e:
            lambda_logger.warning("Failed to generate insights", extra={
                'error': str(e)
            })
        
        return insights


# Global analytics service instance
analytics_service = AnalyticsService()
