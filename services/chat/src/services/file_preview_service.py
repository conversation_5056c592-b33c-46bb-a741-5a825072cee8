# services/chat/src/services/file_preview_service.py
# Service for file preview and thumbnail generation

import json
import boto3
import uuid
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from PIL import Image
import io

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config
from .cdn_service import cdn_service

class FilePreviewService:
    """Service for generating file previews and thumbnails"""
    
    def __init__(self):
        if shared_available:
            self.s3_client = boto3.client('s3')
        else:
            self.s3_client = None
        
        self.file_config = config.get_file_config()
        self.bucket_name = self.file_config.get('s3_bucket_name')
        
        # Preview configurations
        self.thumbnail_sizes = {
            'small': (150, 150),
            'medium': (300, 300),
            'large': (600, 600)
        }
        
        self.preview_sizes = {
            'small': (400, 400),
            'medium': (800, 800),
            'large': (1200, 1200)
        }
        
        # Supported file types for preview
        self.image_types = self.file_config.get('image_extensions', [])
        self.video_types = self.file_config.get('video_extensions', [])
        self.document_types = self.file_config.get('document_extensions', [])
    
    def generate_file_preview(self, file_id: str, file_key: str, content_type: str,
                            preview_type: str = 'thumbnail', size: str = 'medium') -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Generate file preview or thumbnail
        
        Args:
            file_id: File identifier
            file_key: S3 file key
            content_type: File content type
            preview_type: 'thumbnail' or 'preview'
            size: 'small', 'medium', or 'large'
        
        Returns:
            (success, preview_data, error_message)
        """
        try:
            # Determine file type
            file_extension = self._get_file_extension(file_key)
            
            if file_extension in self.image_types:
                return self._generate_image_preview(file_id, file_key, preview_type, size)
            elif file_extension in self.video_types:
                return self._generate_video_preview(file_id, file_key, preview_type, size)
            elif file_extension in self.document_types:
                return self._generate_document_preview(file_id, file_key, preview_type, size)
            else:
                return self._generate_generic_preview(file_id, file_key, content_type, preview_type)
            
        except Exception as e:
            lambda_logger.error("Failed to generate file preview", extra={
                'file_id': file_id,
                'file_key': file_key,
                'preview_type': preview_type,
                'error': str(e)
            })
            return False, {}, f"Failed to generate preview: {str(e)}"
    
    def get_file_metadata(self, file_key: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get detailed file metadata
        
        Returns:
            (success, metadata, error_message)
        """
        try:
            # Get S3 object metadata
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=file_key)
            
            metadata = {
                'fileSize': response['ContentLength'],
                'contentType': response['ContentType'],
                'lastModified': response['LastModified'].isoformat(),
                'etag': response['ETag'].strip('"'),
                'metadata': response.get('Metadata', {}),
                'storageClass': response.get('StorageClass', 'STANDARD')
            }
            
            # Add file-type specific metadata
            file_extension = self._get_file_extension(file_key)
            
            if file_extension in self.image_types:
                image_metadata = self._get_image_metadata(file_key)
                metadata.update(image_metadata)
            elif file_extension in self.video_types:
                video_metadata = self._get_video_metadata(file_key)
                metadata.update(video_metadata)
            
            lambda_logger.debug("File metadata retrieved", extra={
                'file_key': file_key,
                'file_size': metadata['fileSize'],
                'content_type': metadata['contentType']
            })
            
            return True, metadata, None
            
        except self.s3_client.exceptions.NoSuchKey:
            return False, {}, "File not found"
        except Exception as e:
            lambda_logger.error("Failed to get file metadata", extra={
                'file_key': file_key,
                'error': str(e)
            })
            return False, {}, f"Failed to get metadata: {str(e)}"
    
    def generate_secure_download_url(self, file_key: str, filename: str, user_id: str,
                                   tenant_id: str, expires_in: int = 3600) -> Tuple[bool, str, Optional[str]]:
        """
        Generate secure download URL with access logging
        
        Returns:
            (success, download_url, error_message)
        """
        try:
            # Determine file type for CDN optimization
            file_type = self._determine_file_type_from_filename(filename)

            # Try to generate CDN URL first
            cdn_success, cdn_url, cdn_error = cdn_service.generate_cdn_url(
                file_key=file_key,
                file_type=file_type,
                expires_in=expires_in,
                secure=True
            )

            if cdn_success and cdn_url:
                lambda_logger.debug("CDN download URL generated", extra={
                    'file_key': file_key,
                    'file_type': file_type,
                    'user_id': user_id,
                    'tenant_id': tenant_id
                })
                url = cdn_url
            else:
                # Fallback to S3 presigned URL
                lambda_logger.debug("Falling back to S3 presigned URL", extra={
                    'file_key': file_key,
                    'cdn_error': cdn_error
                })

                url = self.s3_client.generate_presigned_url(
                    'get_object',
                    Params={
                        'Bucket': self.bucket_name,
                        'Key': file_key,
                        'ResponseContentDisposition': f'attachment; filename="{filename}"',
                        'ResponseCacheControl': 'no-cache'
                    },
                    ExpiresIn=expires_in
                )
            
            # Log download URL generation
            lambda_logger.info("Secure download URL generated", extra={
                'file_key': file_key,
                'filename': filename,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'expires_in': expires_in
            })
            
            return True, url, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate download URL", extra={
                'file_key': file_key,
                'filename': filename,
                'user_id': user_id,
                'error': str(e)
            })
            return False, "", f"Failed to generate download URL: {str(e)}"
    
    def _generate_image_preview(self, file_id: str, file_key: str, preview_type: str, size: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Generate preview for image files"""
        try:
            # Download image from S3
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=file_key)
            image_data = response['Body'].read()
            
            # Open image with PIL
            image = Image.open(io.BytesIO(image_data))
            original_size = image.size
            
            # Determine target size
            target_sizes = self.thumbnail_sizes if preview_type == 'thumbnail' else self.preview_sizes
            target_size = target_sizes.get(size, target_sizes['medium'])
            
            # Create thumbnail/preview
            image.thumbnail(target_size, Image.Resampling.LANCZOS)
            
            # Save processed image
            output_buffer = io.BytesIO()
            image_format = 'JPEG' if image.mode == 'RGB' else 'PNG'
            image.save(output_buffer, format=image_format, quality=85, optimize=True)
            output_buffer.seek(0)
            
            # Upload processed image to S3
            preview_key = f"previews/{file_id}/{preview_type}_{size}.{image_format.lower()}"
            
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=preview_key,
                Body=output_buffer.getvalue(),
                ContentType=f'image/{image_format.lower()}',
                CacheControl='max-age=86400'  # 24 hours
            )
            
            # Generate preview URL
            preview_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': preview_key},
                ExpiresIn=86400  # 24 hours
            )
            
            preview_data = {
                'previewUrl': preview_url,
                'previewKey': preview_key,
                'previewType': preview_type,
                'size': size,
                'dimensions': {
                    'original': {'width': original_size[0], 'height': original_size[1]},
                    'preview': {'width': image.size[0], 'height': image.size[1]}
                },
                'format': image_format.lower(),
                'generatedAt': datetime.utcnow().isoformat()
            }
            
            lambda_logger.debug("Image preview generated", extra={
                'file_id': file_id,
                'preview_type': preview_type,
                'size': size,
                'original_size': original_size,
                'preview_size': image.size
            })
            
            return True, preview_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate image preview", extra={
                'file_id': file_id,
                'file_key': file_key,
                'error': str(e)
            })
            return False, {}, f"Failed to generate image preview: {str(e)}"
    
    def _generate_video_preview(self, file_id: str, file_key: str, preview_type: str, size: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Generate preview for video files (thumbnail from first frame)"""
        try:
            # For video thumbnails, we would use FFmpeg to extract first frame
            # For now, return a placeholder
            
            preview_data = {
                'previewUrl': f"https://via.placeholder.com/300x200/cccccc/666666?text=Video+Preview",
                'previewType': preview_type,
                'size': size,
                'isPlaceholder': True,
                'videoMetadata': {
                    'duration': '00:02:30',  # Would be extracted from video
                    'resolution': '1920x1080',
                    'codec': 'h264'
                },
                'generatedAt': datetime.utcnow().isoformat()
            }
            
            lambda_logger.debug("Video preview generated (placeholder)", extra={
                'file_id': file_id,
                'preview_type': preview_type,
                'size': size
            })
            
            return True, preview_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate video preview", extra={
                'file_id': file_id,
                'file_key': file_key,
                'error': str(e)
            })
            return False, {}, f"Failed to generate video preview: {str(e)}"
    
    def _generate_document_preview(self, file_id: str, file_key: str, preview_type: str, size: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Generate preview for document files"""
        try:
            # For document previews, we would use libraries like pdf2image for PDFs
            # For now, return a placeholder
            
            preview_data = {
                'previewUrl': f"https://via.placeholder.com/400x500/f8f9fa/6c757d?text=Document+Preview",
                'previewType': preview_type,
                'size': size,
                'isPlaceholder': True,
                'documentMetadata': {
                    'pageCount': 5,  # Would be extracted from document
                    'format': 'PDF',
                    'hasText': True
                },
                'generatedAt': datetime.utcnow().isoformat()
            }
            
            lambda_logger.debug("Document preview generated (placeholder)", extra={
                'file_id': file_id,
                'preview_type': preview_type,
                'size': size
            })
            
            return True, preview_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate document preview", extra={
                'file_id': file_id,
                'file_key': file_key,
                'error': str(e)
            })
            return False, {}, f"Failed to generate document preview: {str(e)}"
    
    def _generate_generic_preview(self, file_id: str, file_key: str, content_type: str, preview_type: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Generate generic preview for unsupported file types"""
        try:
            # Get file icon based on content type
            icon_url = self._get_file_icon_url(content_type)
            
            preview_data = {
                'previewUrl': icon_url,
                'previewType': preview_type,
                'isGeneric': True,
                'contentType': content_type,
                'generatedAt': datetime.utcnow().isoformat()
            }
            
            lambda_logger.debug("Generic preview generated", extra={
                'file_id': file_id,
                'content_type': content_type,
                'preview_type': preview_type
            })
            
            return True, preview_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate generic preview", extra={
                'file_id': file_id,
                'file_key': file_key,
                'error': str(e)
            })
            return False, {}, f"Failed to generate generic preview: {str(e)}"
    
    def _get_image_metadata(self, file_key: str) -> Dict[str, Any]:
        """Get image-specific metadata"""
        try:
            # Download and analyze image
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=file_key)
            image_data = response['Body'].read()
            
            image = Image.open(io.BytesIO(image_data))
            
            return {
                'imageMetadata': {
                    'width': image.size[0],
                    'height': image.size[1],
                    'mode': image.mode,
                    'format': image.format,
                    'hasTransparency': image.mode in ('RGBA', 'LA') or 'transparency' in image.info
                }
            }
            
        except Exception as e:
            lambda_logger.warning("Failed to get image metadata", extra={
                'file_key': file_key,
                'error': str(e)
            })
            return {'imageMetadata': {}}
    
    def _get_video_metadata(self, file_key: str) -> Dict[str, Any]:
        """Get video-specific metadata"""
        try:
            # This would use FFprobe to get video metadata
            # For now, return placeholder
            return {
                'videoMetadata': {
                    'duration': '00:00:00',
                    'resolution': 'unknown',
                    'codec': 'unknown',
                    'bitrate': 0
                }
            }
            
        except Exception as e:
            lambda_logger.warning("Failed to get video metadata", extra={
                'file_key': file_key,
                'error': str(e)
            })
            return {'videoMetadata': {}}
    
    def _get_file_extension(self, file_key: str) -> str:
        """Get file extension from file key"""
        return '.' + file_key.split('.')[-1].lower() if '.' in file_key else ''

    def _determine_file_type_from_filename(self, filename: str) -> str:
        """Determine file type from filename for CDN optimization"""
        extension = self._get_file_extension(filename)

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
        audio_extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma']
        document_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']

        if extension in image_extensions:
            return 'images'
        elif extension in video_extensions:
            return 'videos'
        elif extension in audio_extensions:
            return 'audio'
        elif extension in document_extensions:
            return 'documents'
        else:
            return 'documents'  # Default to documents
    
    def _get_file_icon_url(self, content_type: str) -> str:
        """Get file icon URL based on content type"""
        # This would return appropriate file icons
        # For now, return placeholder
        if content_type.startswith('application/pdf'):
            return "https://via.placeholder.com/64x64/dc3545/ffffff?text=PDF"
        elif content_type.startswith('application/msword'):
            return "https://via.placeholder.com/64x64/2b579a/ffffff?text=DOC"
        elif content_type.startswith('application/zip'):
            return "https://via.placeholder.com/64x64/6f42c1/ffffff?text=ZIP"
        else:
            return "https://via.placeholder.com/64x64/6c757d/ffffff?text=FILE"


# Global file preview service instance
file_preview_service = FilePreviewService()
