# services/chat/src/services/agent_response_processor.py
# Service for processing agent responses from N8N

import json
import uuid
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config
from ..services.message_service import message_service
from ..services.notification_service import notification_service

class AgentResponseProcessor:
    """Service for processing and validating agent responses"""
    
    def __init__(self):
        self.max_content_length = config.get_agent_config().get('max_response_length', 10000)
        self.supported_response_types = config.get_agent_config().get('supported_response_types', ['text', 'structured', 'file'])
        self.min_confidence_threshold = config.get_agent_config().get('min_confidence_threshold', 0.1)
    
    def process_agent_response(self, conversation_id: str, agent_id: str, agent_name: str,
                             agent_type: str, response_data: Dict[str, Any],
                             webhook_request_id: str = None, timestamp: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Process agent response and create chat message
        
        Returns:
            (success, processed_response, error_message)
        """
        try:
            # Validate response data
            is_valid, validation_error = self._validate_response_data(response_data)
            if not is_valid:
                return False, {}, validation_error
            
            # Extract response components
            content = response_data['content']
            response_type = response_data.get('type', 'text')
            confidence = response_data.get('confidence', 1.0)
            metadata = response_data.get('metadata', {})
            attachments = response_data.get('attachments', [])
            
            # Generate message ID
            message_id = f"agent-msg-{uuid.uuid4().hex[:12]}"
            
            # Prepare message data
            message_data = {
                'messageId': message_id,
                'conversationId': conversation_id,
                'userId': agent_id,  # Agent acts as user
                'content': content,
                'type': response_type,
                'status': 'sent',
                'timestamp': timestamp or datetime.utcnow().isoformat(),
                'attachments': attachments,
                'metadata': {
                    **metadata,
                    'isAgentResponse': True,
                    'agentId': agent_id,
                    'agentName': agent_name,
                    'agentType': agent_type,
                    'confidence': confidence,
                    'webhookRequestId': webhook_request_id,
                    'processingTimestamp': datetime.utcnow().isoformat()
                }
            }
            
            # Get conversation info to determine tenant
            tenant_id = self._get_conversation_tenant(conversation_id)
            if not tenant_id:
                return False, {}, "Could not determine conversation tenant"
            
            # Store message in database
            success, stored_message, error_msg = message_service.create_agent_message(
                conversation_id=conversation_id,
                agent_id=agent_id,
                tenant_id=tenant_id,
                content=content,
                message_type=response_type,
                attachments=attachments,
                metadata=message_data['metadata']
            )
            
            if not success:
                return False, {}, f"Failed to store agent message: {error_msg}"
            
            # Send real-time notification
            notification_success, notification_error = notification_service.notify_agent_response(
                conversation_id=conversation_id,
                response_data={
                    'messageId': message_id,
                    'content': content,
                    'type': response_type,
                    'agentId': agent_id,
                    'agentName': agent_name,
                    'agentType': agent_type,
                    'confidence': confidence,
                    'timestamp': message_data['timestamp'],
                    'metadata': metadata
                },
                tenant_id=tenant_id
            )
            
            if not notification_success:
                lambda_logger.warning("Failed to send agent response notification", extra={
                    'conversation_id': conversation_id,
                    'agent_id': agent_id,
                    'message_id': message_id,
                    'error': notification_error
                })
            
            # Prepare processed response
            processed_response = {
                'messageId': message_id,
                'conversationId': conversation_id,
                'agentId': agent_id,
                'agentName': agent_name,
                'agentType': agent_type,
                'content': content,
                'type': response_type,
                'confidence': confidence,
                'timestamp': message_data['timestamp'],
                'processingTime': self._calculate_processing_time(metadata),
                'notificationSent': notification_success,
                'webhookRequestId': webhook_request_id
            }
            
            lambda_logger.info("Agent response processed successfully", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_name': agent_name,
                'message_id': message_id,
                'response_type': response_type,
                'confidence': confidence,
                'notification_sent': notification_success
            })
            
            return True, processed_response, None
            
        except Exception as e:
            lambda_logger.error("Failed to process agent response", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_name': agent_name,
                'error': str(e)
            })
            return False, {}, f"Processing error: {str(e)}"
    
    def _validate_response_data(self, response_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Validate agent response data structure and content"""
        try:
            # Check required fields
            if 'content' not in response_data:
                return False, "Missing required field: content"
            
            content = response_data['content']
            
            # Validate content
            if not isinstance(content, str):
                return False, "Content must be a string"
            
            if len(content.strip()) == 0:
                return False, "Content cannot be empty"
            
            if len(content) > self.max_content_length:
                return False, f"Content too long. Max length: {self.max_content_length}"
            
            # Validate response type
            response_type = response_data.get('type', 'text')
            if response_type not in self.supported_response_types:
                return False, f"Unsupported response type: {response_type}. Supported: {self.supported_response_types}"
            
            # Validate confidence
            confidence = response_data.get('confidence')
            if confidence is not None:
                if not isinstance(confidence, (int, float)):
                    return False, "Confidence must be a number"
                
                if confidence < 0 or confidence > 1:
                    return False, "Confidence must be between 0 and 1"
                
                if confidence < self.min_confidence_threshold:
                    lambda_logger.warning("Low confidence agent response", extra={
                        'confidence': confidence,
                        'threshold': self.min_confidence_threshold
                    })
            
            # Validate metadata
            metadata = response_data.get('metadata')
            if metadata is not None and not isinstance(metadata, dict):
                return False, "Metadata must be a dictionary"
            
            # Validate attachments
            attachments = response_data.get('attachments')
            if attachments is not None:
                if not isinstance(attachments, list):
                    return False, "Attachments must be a list"
                
                for i, attachment in enumerate(attachments):
                    if not isinstance(attachment, dict):
                        return False, f"Attachment {i} must be a dictionary"
                    
                    if 'type' not in attachment or 'url' not in attachment:
                        return False, f"Attachment {i} missing required fields (type, url)"
            
            return True, None
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def _get_conversation_tenant(self, conversation_id: str) -> Optional[str]:
        """Get tenant ID for conversation"""
        try:
            # This would typically call the Agent Service to get conversation details
            # For now, we'll extract from conversation ID pattern or use a default
            
            # If conversation ID follows pattern: tenant-xxx-conv-yyy
            if conversation_id.startswith('tenant-'):
                parts = conversation_id.split('-')
                if len(parts) >= 2:
                    return f"tenant-{parts[1]}"
            
            # Fallback: call Agent Service
            # TODO: Implement actual Agent Service call
            return "default-tenant"
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation tenant", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return None
    
    def _calculate_processing_time(self, metadata: Dict[str, Any]) -> Optional[float]:
        """Calculate processing time from metadata"""
        try:
            # Check if processing time is provided in metadata
            processing_time = metadata.get('processingTime')
            if processing_time is not None:
                return float(processing_time)
            
            # Try to calculate from timestamps
            start_time = metadata.get('startTime')
            end_time = metadata.get('endTime')
            
            if start_time and end_time:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                return (end_dt - start_dt).total_seconds()
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Failed to calculate processing time", extra={
                'metadata': metadata,
                'error': str(e)
            })
            return None
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        try:
            # This would typically query database for stats
            # For now, return basic info
            return {
                'service': 'agent-response-processor',
                'version': '1.0.0',
                'config': {
                    'max_content_length': self.max_content_length,
                    'supported_response_types': self.supported_response_types,
                    'min_confidence_threshold': self.min_confidence_threshold
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get processing stats", extra={
                'error': str(e)
            })
            return {
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global agent response processor instance
agent_response_processor = AgentResponseProcessor()
