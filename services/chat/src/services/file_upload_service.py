# services/chat/src/services/file_upload_service.py
# Service for handling file uploads with S3 integration

import json
import boto3
import uuid
import hashlib
import mimetypes
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlparse

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config
from .search_indexing_service import search_indexing_service
from ..services.file_notification_service import file_notification_service

class FileUploadService:
    """Service for handling file uploads with validation and S3 storage"""
    
    def __init__(self):
        if shared_available:
            self.s3_client = boto3.client('s3')
        else:
            self.s3_client = None
        
        self.file_config = config.get_file_config()
        self.bucket_name = self.file_config.get('s3_bucket_name')
        
        # File type configurations
        self.allowed_extensions = self.file_config.get('allowed_extensions', [
            '.jpg', '.jpeg', '.png', '.gif', '.webp',  # Images
            '.pdf', '.doc', '.docx', '.txt', '.rtf',   # Documents
            '.mp4', '.mov', '.avi', '.webm',           # Videos
            '.mp3', '.wav', '.ogg', '.m4a',            # Audio
            '.zip', '.rar', '.7z',                     # Archives
            '.csv', '.xlsx', '.xls'                    # Data files
        ])
        
        self.max_file_size = self.file_config.get('max_file_size_mb', 50) * 1024 * 1024  # Convert to bytes
        self.virus_scan_enabled = self.file_config.get('virus_scan_enabled', False)
    
    def generate_presigned_upload_url(self, user_id: str, tenant_id: str, filename: str,
                                    content_type: str, file_size: int) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Generate presigned URL for direct S3 upload
        
        Returns:
            (success, upload_data, error_message)
        """
        try:
            # Validate file
            is_valid, validation_error = self._validate_file_upload(filename, content_type, file_size)
            if not is_valid:
                return False, {}, validation_error
            
            # Generate unique file key
            file_extension = self._get_file_extension(filename)
            file_id = f"file-{uuid.uuid4().hex[:12]}"
            file_key = f"chat-files/{tenant_id}/{user_id}/{file_id}{file_extension}"
            
            # Generate presigned URL
            presigned_data = self.s3_client.generate_presigned_post(
                Bucket=self.bucket_name,
                Key=file_key,
                Fields={
                    'Content-Type': content_type,
                    'x-amz-meta-user-id': user_id,
                    'x-amz-meta-tenant-id': tenant_id,
                    'x-amz-meta-upload-timestamp': datetime.utcnow().isoformat(),
                    'x-amz-meta-file-id': file_id
                },
                Conditions=[
                    {'Content-Type': content_type},
                    ['content-length-range', 1, self.max_file_size],
                    {'x-amz-meta-user-id': user_id},
                    {'x-amz-meta-tenant-id': tenant_id}
                ],
                ExpiresIn=3600  # 1 hour
            )
            
            # Prepare upload data
            upload_data = {
                'fileId': file_id,
                'uploadUrl': presigned_data['url'],
                'formData': presigned_data['fields'],
                'fileKey': file_key,
                'expiresIn': 3600,
                'maxFileSize': self.max_file_size,
                'allowedTypes': [content_type],
                'uploadInstructions': {
                    'method': 'POST',
                    'enctype': 'multipart/form-data',
                    'note': 'Include all formData fields in the POST request'
                }
            }
            
            lambda_logger.info("Presigned upload URL generated", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'filename': filename,
                'content_type': content_type,
                'file_size': file_size
            })
            
            return True, upload_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to generate presigned upload URL", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'filename': filename,
                'error': str(e)
            })
            return False, {}, f"Failed to generate upload URL: {str(e)}"
    
    def confirm_file_upload(self, file_id: str, user_id: str, tenant_id: str,
                          filename: str, conversation_id: str = None) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Confirm file upload and create file record
        
        Returns:
            (success, file_data, error_message)
        """
        try:
            # Construct file key
            file_extension = self._get_file_extension(filename)
            file_key = f"chat-files/{tenant_id}/{user_id}/{file_id}{file_extension}"
            
            # Verify file exists in S3
            try:
                response = self.s3_client.head_object(Bucket=self.bucket_name, Key=file_key)
                file_size = response['ContentLength']
                content_type = response['ContentType']
                last_modified = response['LastModified']
            except self.s3_client.exceptions.NoSuchKey:
                return False, {}, "File not found in storage"
            
            # Generate file metadata
            file_data = {
                'fileId': file_id,
                'filename': filename,
                'originalFilename': filename,
                'fileKey': file_key,
                'fileSize': file_size,
                'contentType': content_type,
                'fileType': self._determine_file_type(content_type),
                'uploadedBy': user_id,
                'tenantId': tenant_id,
                'conversationId': conversation_id,
                'uploadedAt': datetime.utcnow().isoformat(),
                'lastModified': last_modified.isoformat(),
                'status': 'uploaded',
                'downloadCount': 0,
                'isPublic': False,
                'metadata': {
                    'checksum': self._calculate_file_checksum(file_key),
                    'virusScanned': False,
                    'thumbnailGenerated': False
                }
            }
            
            # Store file record in database
            success, error_msg = self._store_file_record(file_data)
            if not success:
                return False, {}, error_msg
            
            # Generate download URL
            download_url = self._generate_download_url(file_key, filename)
            file_data['downloadUrl'] = download_url
            
            # Schedule post-upload processing
            self._schedule_post_upload_processing(file_data)

            # Send upload completion notification
            if conversation_id:
                file_notification_service.notify_file_upload_completed(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    conversation_id=conversation_id,
                    file_data=file_data
                )

            lambda_logger.info("File upload confirmed", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'filename': filename,
                'file_size': file_size,
                'content_type': content_type
            })

            # Index file for search (async, don't fail if indexing fails)
            try:
                search_indexing_service.index_file(file_data)
            except Exception as e:
                lambda_logger.warning("Failed to index file for search", extra={
                    'file_id': file_id,
                    'error': str(e)
                })

            return True, file_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to confirm file upload", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'filename': filename,
                'error': str(e)
            })
            return False, {}, f"Failed to confirm upload: {str(e)}"
    
    def get_file_info(self, file_id: str, user_id: str, tenant_id: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Get file information and metadata
        
        Returns:
            (success, file_info, error_message)
        """
        try:
            # Retrieve file record from database
            file_data = self._get_file_record(file_id, tenant_id)
            if not file_data:
                return False, {}, "File not found"
            
            # Check access permissions
            if not self._check_file_access(file_data, user_id, tenant_id):
                return False, {}, "Access denied"
            
            # Generate fresh download URL if needed
            if self._should_refresh_download_url(file_data):
                download_url = self._generate_download_url(file_data['fileKey'], file_data['filename'])
                file_data['downloadUrl'] = download_url
            
            lambda_logger.debug("File info retrieved", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id
            })
            
            return True, file_data, None
            
        except Exception as e:
            lambda_logger.error("Failed to get file info", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, {}, f"Failed to get file info: {str(e)}"
    
    def delete_file(self, file_id: str, user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Delete file from storage and database
        
        Returns:
            (success, error_message)
        """
        try:
            # Get file record
            file_data = self._get_file_record(file_id, tenant_id)
            if not file_data:
                return False, "File not found"
            
            # Check delete permissions
            if file_data['uploadedBy'] != user_id:
                return False, "Only file owner can delete"
            
            # Delete from S3
            try:
                self.s3_client.delete_object(Bucket=self.bucket_name, Key=file_data['fileKey'])
            except Exception as e:
                lambda_logger.warning("Failed to delete file from S3", extra={
                    'file_id': file_id,
                    'file_key': file_data['fileKey'],
                    'error': str(e)
                })
            
            # Mark as deleted in database
            success, error_msg = self._mark_file_deleted(file_id, tenant_id)
            if not success:
                return False, error_msg

            # Send deletion notification
            conversation_id = file_data.get('conversationId')
            if conversation_id:
                file_notification_service.notify_file_deleted(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    conversation_id=conversation_id,
                    file_info=file_data
                )

            lambda_logger.info("File deleted", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'filename': file_data.get('filename')
            })

            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to delete file", extra={
                'file_id': file_id,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Failed to delete file: {str(e)}"
    
    def _validate_file_upload(self, filename: str, content_type: str, file_size: int) -> Tuple[bool, Optional[str]]:
        """Validate file upload parameters"""
        try:
            # Check file size
            if file_size > self.max_file_size:
                return False, f"File too large. Maximum size: {self.max_file_size // (1024*1024)}MB"
            
            if file_size <= 0:
                return False, "Invalid file size"
            
            # Check file extension
            file_extension = self._get_file_extension(filename).lower()
            if file_extension not in self.allowed_extensions:
                return False, f"File type not allowed. Allowed: {', '.join(self.allowed_extensions)}"
            
            # Validate content type
            expected_content_type = mimetypes.guess_type(filename)[0]
            if expected_content_type and content_type != expected_content_type:
                lambda_logger.warning("Content type mismatch", extra={
                    'filename': filename,
                    'provided_type': content_type,
                    'expected_type': expected_content_type
                })
            
            # Check filename
            if not filename or len(filename) > 255:
                return False, "Invalid filename"
            
            return True, None
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename"""
        return '.' + filename.split('.')[-1].lower() if '.' in filename else ''
    
    def _determine_file_type(self, content_type: str) -> str:
        """Determine file type category from content type"""
        if content_type.startswith('image/'):
            return 'image'
        elif content_type.startswith('video/'):
            return 'video'
        elif content_type.startswith('audio/'):
            return 'audio'
        elif content_type in ['application/pdf', 'application/msword', 'text/plain']:
            return 'document'
        elif content_type in ['application/zip', 'application/x-rar-compressed']:
            return 'archive'
        else:
            return 'other'
    
    def _calculate_file_checksum(self, file_key: str) -> Optional[str]:
        """Calculate file checksum for integrity verification"""
        try:
            # This would download file and calculate checksum
            # For now, return placeholder
            return f"sha256-{uuid.uuid4().hex[:16]}"
        except Exception as e:
            lambda_logger.warning("Failed to calculate checksum", extra={
                'file_key': file_key,
                'error': str(e)
            })
            return None
    
    def _generate_download_url(self, file_key: str, filename: str) -> str:
        """Generate presigned download URL"""
        try:
            if shared_available and self.s3_client:
                url = self.s3_client.generate_presigned_url(
                    'get_object',
                    Params={
                        'Bucket': self.bucket_name,
                        'Key': file_key,
                        'ResponseContentDisposition': f'attachment; filename="{filename}"'
                    },
                    ExpiresIn=3600  # 1 hour
                )
                return url
            else:
                return f"https://{self.bucket_name}.s3.amazonaws.com/{file_key}"
        except Exception as e:
            lambda_logger.error("Failed to generate download URL", extra={
                'file_key': file_key,
                'error': str(e)
            })
            return ""
    
    def _store_file_record(self, file_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Store file record in database"""
        try:
            # This would store in DynamoDB
            # For now, just log
            lambda_logger.debug("File record stored", extra={
                'file_id': file_data['fileId'],
                'filename': file_data['filename']
            })
            return True, None
        except Exception as e:
            return False, f"Failed to store file record: {str(e)}"
    
    def _get_file_record(self, file_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get file record from database"""
        try:
            # This would query DynamoDB
            # For now, return mock data
            return {
                'fileId': file_id,
                'filename': 'example.pdf',
                'fileKey': f"chat-files/{tenant_id}/user/file-123.pdf",
                'uploadedBy': 'user-123',
                'tenantId': tenant_id,
                'status': 'uploaded'
            }
        except Exception as e:
            lambda_logger.error("Failed to get file record", extra={
                'file_id': file_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    def _check_file_access(self, file_data: Dict[str, Any], user_id: str, tenant_id: str) -> bool:
        """Check if user has access to file"""
        # Same tenant and either owner or file is public
        return (file_data['tenantId'] == tenant_id and 
                (file_data['uploadedBy'] == user_id or file_data.get('isPublic', False)))
    
    def _should_refresh_download_url(self, file_data: Dict[str, Any]) -> bool:
        """Check if download URL should be refreshed"""
        # Always refresh for security
        return True
    
    def _mark_file_deleted(self, file_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Mark file as deleted in database"""
        try:
            # This would update DynamoDB record
            lambda_logger.debug("File marked as deleted", extra={
                'file_id': file_id,
                'tenant_id': tenant_id
            })
            return True, None
        except Exception as e:
            return False, f"Failed to mark file deleted: {str(e)}"
    
    def _schedule_post_upload_processing(self, file_data: Dict[str, Any]):
        """Schedule post-upload processing (virus scan, thumbnail generation, etc.)"""
        try:
            # This would trigger async processing
            lambda_logger.debug("Post-upload processing scheduled", extra={
                'file_id': file_data['fileId'],
                'file_type': file_data['fileType']
            })
        except Exception as e:
            lambda_logger.warning("Failed to schedule post-upload processing", extra={
                'file_id': file_data['fileId'],
                'error': str(e)
            })


# Global file upload service instance
file_upload_service = FileUploadService()
