# services/chat/src/services/alerting_service.py
# Comprehensive alerting service with SNS, Slack, and email notifications

import boto3
import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class IAlertingService(ABC):
    """Interface for alerting service operations."""

    @abstractmethod
    def send_alert(
        self,
        alert_type: str,
        severity: str,
        message: str,
        tenant_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send alert notification."""
        pass

    @abstractmethod
    def send_performance_alert(
        self,
        metric_name: str,
        current_value: float,
        threshold: float,
        tenant_id: str
    ) -> bool:
        """Send performance-related alert."""
        pass

    @abstractmethod
    def send_error_alert(
        self,
        error_type: str,
        error_count: int,
        tenant_id: str,
        time_window_minutes: int = 5
    ) -> bool:
        """Send error-related alert."""
        pass

    @abstractmethod
    def check_alert_thresholds(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Check if any metrics exceed alert thresholds."""
        pass


class AlertingService(IAlertingService):
    """Comprehensive alerting service with multiple notification channels"""
    
    def __init__(self):
        self.alerting_config = config.get_monitoring_config()
        self.sns_client = self._initialize_sns_client()
        
        # Alert configuration
        self.alert_channels = {
            'sns_topic': self.alerting_config.get('alert_sns_topic'),
            'email': self.alerting_config.get('alert_email'),
            'slack_webhook': self.alerting_config.get('alert_slack_webhook')
        }
        
        # Alert templates
        self.alert_templates = {
            'high_response_time': {
                'title': '🚨 High Response Time Alert',
                'severity': AlertSeverity.WARNING,
                'description': 'API response time exceeded threshold'
            },
            'high_error_rate': {
                'title': '🔥 High Error Rate Alert',
                'severity': AlertSeverity.CRITICAL,
                'description': 'Error rate exceeded acceptable threshold'
            },
            'service_down': {
                'title': '💥 Service Down Alert',
                'severity': AlertSeverity.EMERGENCY,
                'description': 'Critical service is not responding'
            },
            'database_issues': {
                'title': '🗄️ Database Issues Alert',
                'severity': AlertSeverity.CRITICAL,
                'description': 'Database performance or connectivity issues detected'
            },
            'memory_usage_high': {
                'title': '💾 High Memory Usage Alert',
                'severity': AlertSeverity.WARNING,
                'description': 'Memory usage exceeded warning threshold'
            },
            'connection_limit': {
                'title': '🔗 Connection Limit Alert',
                'severity': AlertSeverity.WARNING,
                'description': 'WebSocket connection limit approaching maximum'
            },
            'cache_miss_rate': {
                'title': '📦 High Cache Miss Rate Alert',
                'severity': AlertSeverity.WARNING,
                'description': 'Cache miss rate is unusually high'
            },
            'security_incident': {
                'title': '🛡️ Security Incident Alert',
                'severity': AlertSeverity.EMERGENCY,
                'description': 'Potential security incident detected'
            }
        }
        
        # Alert suppression (to prevent spam)
        self.alert_suppression = {}
        self.suppression_window = 300  # 5 minutes
        
        self.enabled = self.alerting_config.get('alerts_enabled', True)
    
    def _initialize_sns_client(self):
        """Initialize SNS client"""
        try:
            if not self.alerting_config.get('alerts_enabled', True):
                lambda_logger.info("Alerting service disabled")
                return None
            
            return boto3.client('sns')
            
        except Exception as e:
            lambda_logger.warning("Failed to initialize SNS client", extra={
                'error': str(e)
            })
            return None
    
    def send_alert(self, alert_type: str, context: Dict[str, Any], 
                  custom_message: str = None, severity: AlertSeverity = None) -> bool:
        """Send alert through configured channels"""
        try:
            if not self.enabled:
                return True
            
            # Check alert suppression
            if self._is_alert_suppressed(alert_type, context):
                lambda_logger.debug("Alert suppressed", extra={
                    'alert_type': alert_type,
                    'context': context
                })
                return True
            
            # Get alert template
            template = self.alert_templates.get(alert_type, {
                'title': f'🚨 {alert_type.replace("_", " ").title()} Alert',
                'severity': severity or AlertSeverity.WARNING,
                'description': custom_message or f'Alert triggered for {alert_type}'
            })
            
            # Build alert message
            alert_message = self._build_alert_message(template, context, custom_message)
            
            # Send through all configured channels
            success = True
            
            # SNS notification
            if self.alert_channels['sns_topic']:
                success &= self._send_sns_alert(alert_message, template['severity'])
            
            # Email notification
            if self.alert_channels['email']:
                success &= self._send_email_alert(alert_message, template['severity'])
            
            # Slack notification
            if self.alert_channels['slack_webhook']:
                success &= self._send_slack_alert(alert_message, template['severity'])
            
            # Record alert suppression
            self._record_alert_suppression(alert_type, context)
            
            lambda_logger.info("Alert sent", extra={
                'alert_type': alert_type,
                'severity': template['severity'].value,
                'success': success
            })
            
            return success
            
        except Exception as e:
            lambda_logger.error("Failed to send alert", extra={
                'alert_type': alert_type,
                'context': context,
                'error': str(e)
            })
            return False
    
    def send_performance_alert(self, metric_name: str, current_value: float,
                             threshold: float, endpoint: str = None, tenant_id: str = None) -> bool:
        """Send performance-related alert"""
        context = {
            'metric_name': metric_name,
            'current_value': current_value,
            'threshold': threshold,
            'endpoint': endpoint,
            'tenant_id': tenant_id,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Determine alert type and severity
        if metric_name == 'response_time':
            alert_type = 'high_response_time'
            severity = AlertSeverity.CRITICAL if current_value > threshold * 2 else AlertSeverity.WARNING
        elif metric_name == 'error_rate':
            alert_type = 'high_error_rate'
            severity = AlertSeverity.CRITICAL
        elif metric_name == 'memory_usage':
            alert_type = 'memory_usage_high'
            severity = AlertSeverity.CRITICAL if current_value > 0.9 else AlertSeverity.WARNING
        else:
            alert_type = 'performance_issue'
            severity = AlertSeverity.WARNING
        
        return self.send_alert(alert_type, context, severity=severity)
    
    def send_infrastructure_alert(self, component: str, issue: str, 
                                details: Dict[str, Any] = None) -> bool:
        """Send infrastructure-related alert"""
        context = {
            'component': component,
            'issue': issue,
            'details': details or {},
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Determine alert type
        if 'database' in component.lower():
            alert_type = 'database_issues'
        elif 'connection' in issue.lower():
            alert_type = 'connection_limit'
        elif 'down' in issue.lower() or 'unavailable' in issue.lower():
            alert_type = 'service_down'
        else:
            alert_type = 'infrastructure_issue'
        
        return self.send_alert(alert_type, context, severity=AlertSeverity.CRITICAL)
    
    def send_security_alert(self, incident_type: str, source_ip: str = None,
                          user_id: str = None, details: Dict[str, Any] = None) -> bool:
        """Send security-related alert"""
        context = {
            'incident_type': incident_type,
            'source_ip': source_ip,
            'user_id': user_id,
            'details': details or {},
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return self.send_alert('security_incident', context, severity=AlertSeverity.EMERGENCY)
    
    def send_business_alert(self, metric: str, current_value: float,
                          expected_range: tuple, tenant_id: str = None) -> bool:
        """Send business metric alert"""
        context = {
            'metric': metric,
            'current_value': current_value,
            'expected_min': expected_range[0],
            'expected_max': expected_range[1],
            'tenant_id': tenant_id,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Determine severity based on deviation
        deviation = abs(current_value - ((expected_range[0] + expected_range[1]) / 2))
        range_size = expected_range[1] - expected_range[0]
        
        if deviation > range_size:
            severity = AlertSeverity.CRITICAL
        elif deviation > range_size * 0.5:
            severity = AlertSeverity.WARNING
        else:
            severity = AlertSeverity.INFO
        
        return self.send_alert('business_metric_anomaly', context, severity=severity)
    
    def _build_alert_message(self, template: Dict[str, Any], context: Dict[str, Any],
                           custom_message: str = None) -> Dict[str, Any]:
        """Build formatted alert message"""
        message = {
            'title': template['title'],
            'severity': template['severity'].value,
            'description': custom_message or template['description'],
            'timestamp': datetime.utcnow().isoformat(),
            'context': context,
            'environment': config.environment,
            'service': 'ChatService'
        }
        
        # Add severity-specific formatting
        if template['severity'] == AlertSeverity.EMERGENCY:
            message['title'] = f"🚨🚨 {message['title']} 🚨🚨"
        elif template['severity'] == AlertSeverity.CRITICAL:
            message['title'] = f"🔥 {message['title']}"
        elif template['severity'] == AlertSeverity.WARNING:
            message['title'] = f"⚠️ {message['title']}"
        
        return message
    
    def _send_sns_alert(self, alert_message: Dict[str, Any], severity: AlertSeverity) -> bool:
        """Send alert via SNS"""
        try:
            if not self.sns_client or not self.alert_channels['sns_topic']:
                return True
            
            # Format message for SNS
            sns_message = {
                'default': json.dumps(alert_message, indent=2),
                'email': self._format_email_message(alert_message),
                'sms': f"{alert_message['title']}: {alert_message['description']}"
            }
            
            # Send SNS message
            response = self.sns_client.publish(
                TopicArn=self.alert_channels['sns_topic'],
                Message=json.dumps(sns_message),
                MessageStructure='json',
                Subject=alert_message['title']
            )
            
            lambda_logger.debug("SNS alert sent", extra={
                'message_id': response.get('MessageId'),
                'severity': severity.value
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to send SNS alert", extra={
                'error': str(e)
            })
            return False
    
    def _send_email_alert(self, alert_message: Dict[str, Any], severity: AlertSeverity) -> bool:
        """Send alert via email (using SNS)"""
        try:
            if not self.sns_client or not self.alert_channels['email']:
                return True
            
            # Format email content
            email_content = self._format_email_message(alert_message)
            
            # Send email via SNS
            response = self.sns_client.publish(
                TopicArn=self.alert_channels['sns_topic'],
                Message=email_content,
                Subject=alert_message['title']
            )
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to send email alert", extra={
                'error': str(e)
            })
            return False
    
    def _send_slack_alert(self, alert_message: Dict[str, Any], severity: AlertSeverity) -> bool:
        """Send alert via Slack webhook"""
        try:
            if not self.alert_channels['slack_webhook']:
                return True
            
            # Format Slack message
            color_map = {
                AlertSeverity.INFO: '#36a64f',      # Green
                AlertSeverity.WARNING: '#ff9500',   # Orange
                AlertSeverity.CRITICAL: '#ff0000',  # Red
                AlertSeverity.EMERGENCY: '#8b0000'  # Dark Red
            }
            
            slack_payload = {
                'text': alert_message['title'],
                'attachments': [
                    {
                        'color': color_map.get(severity, '#ff9500'),
                        'fields': [
                            {
                                'title': 'Description',
                                'value': alert_message['description'],
                                'short': False
                            },
                            {
                                'title': 'Severity',
                                'value': severity.value.upper(),
                                'short': True
                            },
                            {
                                'title': 'Service',
                                'value': alert_message['service'],
                                'short': True
                            },
                            {
                                'title': 'Environment',
                                'value': alert_message['environment'],
                                'short': True
                            },
                            {
                                'title': 'Timestamp',
                                'value': alert_message['timestamp'],
                                'short': True
                            }
                        ]
                    }
                ]
            }
            
            # Add context fields
            if alert_message['context']:
                for key, value in alert_message['context'].items():
                    if key not in ['timestamp']:  # Skip redundant fields
                        slack_payload['attachments'][0]['fields'].append({
                            'title': key.replace('_', ' ').title(),
                            'value': str(value),
                            'short': True
                        })
            
            # Send to Slack
            response = requests.post(
                self.alert_channels['slack_webhook'],
                json=slack_payload,
                timeout=10
            )
            
            response.raise_for_status()
            
            lambda_logger.debug("Slack alert sent", extra={
                'severity': severity.value,
                'status_code': response.status_code
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to send Slack alert", extra={
                'error': str(e)
            })
            return False
    
    def _format_email_message(self, alert_message: Dict[str, Any]) -> str:
        """Format alert message for email"""
        email_content = f"""
{alert_message['title']}

Description: {alert_message['description']}
Severity: {alert_message['severity'].upper()}
Service: {alert_message['service']}
Environment: {alert_message['environment']}
Timestamp: {alert_message['timestamp']}

Context:
"""
        
        for key, value in alert_message['context'].items():
            email_content += f"  {key.replace('_', ' ').title()}: {value}\n"
        
        email_content += f"""

This alert was generated automatically by the ChatService monitoring system.
Please investigate and take appropriate action based on the severity level.
"""
        
        return email_content
    
    def _is_alert_suppressed(self, alert_type: str, context: Dict[str, Any]) -> bool:
        """Check if alert should be suppressed to prevent spam"""
        try:
            # Create suppression key
            suppression_key = f"{alert_type}:{context.get('endpoint', '')}:{context.get('tenant_id', '')}"
            
            current_time = datetime.utcnow()
            
            if suppression_key in self.alert_suppression:
                last_sent = self.alert_suppression[suppression_key]
                if (current_time - last_sent).total_seconds() < self.suppression_window:
                    return True
            
            return False
            
        except Exception as e:
            lambda_logger.error("Error checking alert suppression", extra={
                'error': str(e)
            })
            return False
    
    def _record_alert_suppression(self, alert_type: str, context: Dict[str, Any]):
        """Record alert suppression timestamp"""
        try:
            suppression_key = f"{alert_type}:{context.get('endpoint', '')}:{context.get('tenant_id', '')}"
            self.alert_suppression[suppression_key] = datetime.utcnow()
            
            # Clean up old suppression records
            current_time = datetime.utcnow()
            expired_keys = [
                key for key, timestamp in self.alert_suppression.items()
                if (current_time - timestamp).total_seconds() > self.suppression_window * 2
            ]
            
            for key in expired_keys:
                del self.alert_suppression[key]
                
        except Exception as e:
            lambda_logger.error("Error recording alert suppression", extra={
                'error': str(e)
            })
    
    def get_alert_status(self) -> Dict[str, Any]:
        """Get current alerting system status"""
        try:
            return {
                'enabled': self.enabled,
                'channels': {
                    'sns_configured': bool(self.alert_channels['sns_topic']),
                    'email_configured': bool(self.alert_channels['email']),
                    'slack_configured': bool(self.alert_channels['slack_webhook'])
                },
                'suppression_window_seconds': self.suppression_window,
                'active_suppressions': len(self.alert_suppression),
                'available_alert_types': list(self.alert_templates.keys())
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get alert status", extra={
                'error': str(e)
            })
            return {'enabled': False, 'error': str(e)}


# Global alerting service instance
alerting_service = AlertingService()
