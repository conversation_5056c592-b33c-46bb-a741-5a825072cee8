# services/chat/src/services/presence_service.py
# User presence management service

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

try:
    from shared.logger import lambda_logger
    from shared.database import DynamoDBClient
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()
    
    class MockDynamoDBClient:
        def __init__(self, table_name): self.table_name = table_name
        def put_item(self, item): pass
        def get_item(self, pk, sk): return None
        def query_gsi(self, index, pk, **kwargs): return []
        def update_item(self, **kwargs): pass
    
    DynamoDBClient = MockDynamoDBClient

from ..utils.config import config
from ..models.schema import UserPresenceSchema

class PresenceService:
    """Service for managing user presence and online status"""
    
    def __init__(self):
        if shared_available:
            self.presence_db = DynamoDBClient(config.get_table_name('user_presence'))
        else:
            self.presence_db = MockDynamoDBClient('user_presence')
    
    def update_user_presence(self, user_id: str, tenant_id: str, status: str,
                           connection_count_delta: int = 0,
                           metadata: Dict[str, Any] = None) -> Tuple[bool, Optional[str]]:
        """
        Update user presence status
        
        Args:
            user_id: User identifier
            tenant_id: Tenant identifier
            status: Presence status (online, offline, away, busy)
            connection_count_delta: Change in connection count (+1 for connect, -1 for disconnect)
            metadata: Additional presence metadata
        
        Returns:
            (success, error_message)
        """
        try:
            # Validate status
            valid_statuses = config.get_presence_config()['statuses']
            if status not in valid_statuses:
                return False, f"Invalid status. Must be one of: {valid_statuses}"
            
            # Get current presence or create new
            current_presence = self.get_user_presence(user_id, tenant_id)
            
            if current_presence[0] and current_presence[1]:
                # Update existing presence
                update_params = UserPresenceSchema.update_user_presence(
                    user_id, status, connection_count_delta
                )
                self.presence_db.update_item(**update_params)
                
                lambda_logger.debug("User presence updated", extra={
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'status': status,
                    'connection_delta': connection_count_delta
                })
            else:
                # Create new presence record
                presence_item = UserPresenceSchema.create_user_presence_item(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    status=status,
                    metadata=metadata
                )
                
                self.presence_db.put_item(presence_item)
                
                lambda_logger.info("User presence created", extra={
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'status': status
                })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to update user presence", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'status': status,
                'error': str(e)
            })
            return False, f"Failed to update presence: {str(e)}"
    
    def get_user_presence(self, user_id: str, tenant_id: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Get user presence status
        
        Returns:
            (success, presence_data, error_message)
        """
        try:
            presence_key = UserPresenceSchema.get_user_presence_key(user_id)
            presence = self.presence_db.get_item(presence_key['userId'], presence_key['userId'])
            
            if not presence:
                return False, None, "Presence not found"
            
            # Validate tenant access
            if presence.get('tenantId') != tenant_id:
                return False, None, "Access denied"
            
            # Check if presence is stale
            last_seen = datetime.fromisoformat(presence.get('lastSeen', ''))
            timeout_minutes = config.get_presence_config()['timeout_minutes']
            
            if datetime.utcnow() - last_seen > timedelta(minutes=timeout_minutes):
                # Update to offline if stale
                self.update_user_presence(user_id, tenant_id, 'offline')
                presence['status'] = 'offline'
            
            lambda_logger.debug("User presence retrieved", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'status': presence.get('status')
            })
            
            return True, presence, None
            
        except Exception as e:
            lambda_logger.error("Failed to get user presence", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, None, f"Failed to get presence: {str(e)}"
    
    def get_tenant_presence(self, tenant_id: str, limit: int = 100) -> Tuple[bool, List[Dict[str, Any]], Optional[str]]:
        """
        Get presence for all users in a tenant
        
        Returns:
            (success, presence_list, error_message)
        """
        try:
            # Limit page size
            limit = min(limit, config.max_page_size)
            
            # Query tenant presence using GSI
            query_params = UserPresenceSchema.get_tenant_presence_query(tenant_id, limit)
            presence_list = self.presence_db.query_gsi(**query_params)
            
            # Filter out stale presence records
            active_presence = []
            timeout_minutes = config.get_presence_config()['timeout_minutes']
            current_time = datetime.utcnow()
            
            for presence in presence_list:
                try:
                    last_seen = datetime.fromisoformat(presence.get('lastSeen', ''))
                    if current_time - last_seen <= timedelta(minutes=timeout_minutes):
                        active_presence.append(presence)
                    elif presence.get('status') != 'offline':
                        # Update stale presence to offline
                        user_id = presence.get('userId')
                        if user_id:
                            self.update_user_presence(user_id, tenant_id, 'offline')
                except Exception as e:
                    lambda_logger.warning("Failed to process presence record", extra={
                        'presence_user_id': presence.get('userId'),
                        'error': str(e)
                    })
            
            lambda_logger.debug("Tenant presence retrieved", extra={
                'tenant_id': tenant_id,
                'total_records': len(presence_list),
                'active_records': len(active_presence)
            })
            
            return True, active_presence, None
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant presence", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, [], f"Failed to get tenant presence: {str(e)}"
    
    def get_online_users(self, tenant_id: str) -> Tuple[bool, List[str], Optional[str]]:
        """
        Get list of online user IDs in a tenant
        
        Returns:
            (success, user_id_list, error_message)
        """
        try:
            success, presence_list, error_msg = self.get_tenant_presence(tenant_id)
            
            if not success:
                return False, [], error_msg
            
            # Filter for online users
            online_users = [
                presence['userId'] for presence in presence_list
                if presence.get('status') == 'online' and presence.get('connectionCount', 0) > 0
            ]
            
            lambda_logger.debug("Online users retrieved", extra={
                'tenant_id': tenant_id,
                'online_count': len(online_users)
            })
            
            return True, online_users, None
            
        except Exception as e:
            lambda_logger.error("Failed to get online users", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, [], f"Failed to get online users: {str(e)}"
    
    def is_user_online(self, user_id: str, tenant_id: str) -> bool:
        """Check if a specific user is online"""
        try:
            success, presence, error_msg = self.get_user_presence(user_id, tenant_id)
            
            if not success or not presence:
                return False
            
            return (
                presence.get('status') == 'online' and 
                presence.get('connectionCount', 0) > 0
            )
            
        except Exception as e:
            lambda_logger.error("Failed to check user online status", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def handle_user_connect(self, user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Handle user connection event"""
        return self.update_user_presence(
            user_id=user_id,
            tenant_id=tenant_id,
            status='online',
            connection_count_delta=1
        )
    
    def handle_user_disconnect(self, user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Handle user disconnection event"""
        try:
            # Get current presence to check connection count
            success, presence, error_msg = self.get_user_presence(user_id, tenant_id)
            
            if not success or not presence:
                return True, None  # Already offline or doesn't exist
            
            current_connections = presence.get('connectionCount', 0)
            
            if current_connections <= 1:
                # Last connection, set to offline
                return self.update_user_presence(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    status='offline',
                    connection_count_delta=-1
                )
            else:
                # Still has other connections, just decrement count
                return self.update_user_presence(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    status='online',
                    connection_count_delta=-1
                )
                
        except Exception as e:
            lambda_logger.error("Failed to handle user disconnect", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False, f"Failed to handle disconnect: {str(e)}"
    
    def cleanup_stale_presence(self, tenant_id: str = None) -> int:
        """Clean up stale presence records"""
        try:
            if tenant_id:
                # Clean up specific tenant
                success, presence_list, error_msg = self.get_tenant_presence(tenant_id, limit=1000)
                if not success:
                    return 0
            else:
                # Clean up all tenants (admin operation)
                # This would require a scan operation
                lambda_logger.warning("Global presence cleanup not implemented")
                return 0
            
            cleaned_count = 0
            timeout_minutes = config.get_presence_config()['timeout_minutes']
            current_time = datetime.utcnow()
            
            for presence in presence_list:
                try:
                    last_seen = datetime.fromisoformat(presence.get('lastSeen', ''))
                    if current_time - last_seen > timedelta(minutes=timeout_minutes):
                        user_id = presence.get('userId')
                        if user_id and presence.get('status') != 'offline':
                            self.update_user_presence(user_id, tenant_id, 'offline')
                            cleaned_count += 1
                except Exception as e:
                    lambda_logger.warning("Failed to clean presence record", extra={
                        'presence_user_id': presence.get('userId'),
                        'error': str(e)
                    })
            
            if cleaned_count > 0:
                lambda_logger.info("Stale presence records cleaned", extra={
                    'tenant_id': tenant_id,
                    'cleaned_count': cleaned_count
                })
            
            return cleaned_count
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup stale presence", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return 0


# Global presence service instance
presence_service = PresenceService()
