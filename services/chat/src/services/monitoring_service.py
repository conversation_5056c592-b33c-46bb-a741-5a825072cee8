# services/chat/src/services/monitoring_service.py
# Comprehensive monitoring service with CloudWatch integration

import boto3
import time
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading

from shared.logger import lambda_logger
from shared.config import get_settings
from ..config.chat_config import get_monitoring_config


class IMonitoringService(ABC):
    """Interface for monitoring service operations."""

    @abstractmethod
    def record_message_metrics(
        self,
        message_type: str,
        processing_time_ms: float,
        tenant_id: str,
        success: bool = True
    ) -> None:
        """Record message processing metrics."""
        pass

    @abstractmethod
    def record_connection_metrics(
        self,
        connection_type: str,
        tenant_id: str,
        user_id: str,
        action: str
    ) -> None:
        """Record connection metrics."""
        pass

    @abstractmethod
    def record_error_metrics(
        self,
        error_type: str,
        error_message: str,
        tenant_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Record error metrics."""
        pass

    @abstractmethod
    def get_performance_metrics(self, time_range_minutes: int = 60) -> Dict[str, Any]:
        """Get performance metrics for specified time range."""
        pass


class MonitoringService(IMonitoringService):
    """Comprehensive monitoring service with CloudWatch integration"""
    
    def __init__(self):
        self.monitoring_config = get_monitoring_config()
        self.cloudwatch = self._initialize_cloudwatch_client()
        
        # Metric namespaces
        self.namespaces = {
            'chat': 'ChatService/Application',
            'performance': 'ChatService/Performance',
            'business': 'ChatService/Business',
            'infrastructure': 'ChatService/Infrastructure',
            'security': 'ChatService/Security'
        }
        
        # Metric buffer for batch sending
        self.metric_buffer = defaultdict(list)
        self.buffer_lock = threading.Lock()
        self.buffer_size_limit = 20  # CloudWatch limit
        
        # Real-time metrics storage
        self.realtime_metrics = {
            'active_connections': 0,
            'messages_per_minute': deque(maxlen=60),  # Last 60 minutes
            'response_times': deque(maxlen=1000),     # Last 1000 requests
            'error_rates': deque(maxlen=60),          # Last 60 minutes
            'cache_hit_rates': deque(maxlen=60),      # Last 60 minutes
        }
        
        # Performance thresholds
        self.thresholds = {
            'response_time_warning': 2.0,      # 2 seconds
            'response_time_critical': 5.0,     # 5 seconds
            'error_rate_warning': 0.05,        # 5%
            'error_rate_critical': 0.10,       # 10%
            'memory_usage_warning': 0.80,      # 80%
            'memory_usage_critical': 0.90,     # 90%
            'connection_limit_warning': 0.80,  # 80% of max
            'connection_limit_critical': 0.95  # 95% of max
        }
        
        self.enabled = self.monitoring_config.get('enabled', True)
        
        # Start metric flushing thread
        self._start_metric_flusher()
    
    def _initialize_cloudwatch_client(self):
        """Initialize CloudWatch client"""
        try:
            if not self.monitoring_config.get('enabled', True):
                lambda_logger.info("Monitoring service disabled")
                return None
            
            return boto3.client('cloudwatch')
            
        except Exception as e:
            lambda_logger.warning("Failed to initialize CloudWatch client", extra={
                'error': str(e)
            })
            return None
    
    # Core Metric Recording
    def record_metric(self, metric_name: str, value: Union[int, float], 
                     unit: str = 'Count', namespace: str = 'chat',
                     dimensions: Dict[str, str] = None, timestamp: datetime = None) -> bool:
        """Record a metric with optional dimensions"""
        try:
            if not self.enabled or not self.cloudwatch:
                return True
            
            metric_data = {
                'MetricName': metric_name,
                'Value': value,
                'Unit': unit,
                'Timestamp': timestamp or datetime.utcnow()
            }
            
            if dimensions:
                metric_data['Dimensions'] = [
                    {'Name': key, 'Value': str(value)} 
                    for key, value in dimensions.items()
                ]
            
            # Add to buffer for batch sending
            with self.buffer_lock:
                namespace_key = self.namespaces.get(namespace, namespace)
                self.metric_buffer[namespace_key].append(metric_data)
                
                # Flush if buffer is full
                if len(self.metric_buffer[namespace_key]) >= self.buffer_size_limit:
                    self._flush_metrics(namespace_key)
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to record metric", extra={
                'metric_name': metric_name,
                'value': value,
                'namespace': namespace,
                'error': str(e)
            })
            return False
    
    # Business Metrics
    def record_message_sent(self, tenant_id: str, user_id: str, conversation_id: str,
                           message_type: str = 'text', agent_involved: bool = False) -> bool:
        """Record message sent metric"""
        dimensions = {
            'TenantId': tenant_id,
            'MessageType': message_type,
            'AgentInvolved': str(agent_involved)
        }
        
        # Update real-time counter
        current_minute = datetime.utcnow().replace(second=0, microsecond=0)
        self.realtime_metrics['messages_per_minute'].append({
            'timestamp': current_minute,
            'count': 1
        })
        
        return self.record_metric(
            'MessagesSent',
            1,
            'Count',
            'business',
            dimensions
        )
    
    def record_user_activity(self, tenant_id: str, user_id: str, activity_type: str,
                           duration_seconds: float = None) -> bool:
        """Record user activity metric"""
        dimensions = {
            'TenantId': tenant_id,
            'ActivityType': activity_type
        }
        
        # Record activity count
        success = self.record_metric(
            'UserActivity',
            1,
            'Count',
            'business',
            dimensions
        )
        
        # Record duration if provided
        if duration_seconds is not None:
            success &= self.record_metric(
                'ActivityDuration',
                duration_seconds,
                'Seconds',
                'business',
                dimensions
            )
        
        return success
    
    def record_file_operation(self, tenant_id: str, operation: str, file_size: int,
                            file_type: str, success: bool = True) -> bool:
        """Record file operation metric"""
        dimensions = {
            'TenantId': tenant_id,
            'Operation': operation,
            'FileType': file_type,
            'Success': str(success)
        }
        
        # Record operation count
        metric_success = self.record_metric(
            'FileOperations',
            1,
            'Count',
            'business',
            dimensions
        )
        
        # Record file size
        if success:
            metric_success &= self.record_metric(
                'FileSize',
                file_size,
                'Bytes',
                'business',
                dimensions
            )
        
        return metric_success
    
    def record_search_query(self, tenant_id: str, query_type: str, results_count: int,
                          search_time_ms: float, cache_hit: bool = False) -> bool:
        """Record search query metric"""
        dimensions = {
            'TenantId': tenant_id,
            'QueryType': query_type,
            'CacheHit': str(cache_hit)
        }
        
        # Record query count
        success = self.record_metric(
            'SearchQueries',
            1,
            'Count',
            'business',
            dimensions
        )
        
        # Record search performance
        success &= self.record_metric(
            'SearchLatency',
            search_time_ms,
            'Milliseconds',
            'performance',
            dimensions
        )
        
        # Record results count
        success &= self.record_metric(
            'SearchResults',
            results_count,
            'Count',
            'business',
            dimensions
        )
        
        return success
    
    # Performance Metrics
    def record_response_time(self, endpoint: str, method: str, response_time_ms: float,
                           status_code: int, tenant_id: str = None) -> bool:
        """Record API response time"""
        dimensions = {
            'Endpoint': endpoint,
            'Method': method,
            'StatusCode': str(status_code)
        }
        
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        # Update real-time storage
        self.realtime_metrics['response_times'].append({
            'timestamp': datetime.utcnow(),
            'value': response_time_ms,
            'endpoint': endpoint
        })
        
        # Check thresholds and record
        if response_time_ms > self.thresholds['response_time_critical']:
            self.record_metric('ResponseTimeCritical', 1, 'Count', 'performance', dimensions)
        elif response_time_ms > self.thresholds['response_time_warning']:
            self.record_metric('ResponseTimeWarning', 1, 'Count', 'performance', dimensions)
        
        return self.record_metric(
            'ResponseTime',
            response_time_ms,
            'Milliseconds',
            'performance',
            dimensions
        )
    
    def record_error(self, error_type: str, endpoint: str, tenant_id: str = None,
                    error_message: str = None, severity: str = 'error') -> bool:
        """Record error occurrence"""
        dimensions = {
            'ErrorType': error_type,
            'Endpoint': endpoint,
            'Severity': severity
        }
        
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        # Update real-time error rate
        current_minute = datetime.utcnow().replace(second=0, microsecond=0)
        self.realtime_metrics['error_rates'].append({
            'timestamp': current_minute,
            'count': 1,
            'type': error_type
        })
        
        return self.record_metric(
            'Errors',
            1,
            'Count',
            'chat',
            dimensions
        )
    
    def record_cache_operation(self, operation: str, cache_type: str, hit: bool,
                             latency_ms: float, tenant_id: str = None) -> bool:
        """Record cache operation metrics"""
        dimensions = {
            'Operation': operation,
            'CacheType': cache_type,
            'Hit': str(hit)
        }
        
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        # Update real-time cache hit rate
        self.realtime_metrics['cache_hit_rates'].append({
            'timestamp': datetime.utcnow(),
            'hit': hit,
            'type': cache_type
        })
        
        # Record cache operation
        success = self.record_metric(
            'CacheOperations',
            1,
            'Count',
            'performance',
            dimensions
        )
        
        # Record cache latency
        success &= self.record_metric(
            'CacheLatency',
            latency_ms,
            'Milliseconds',
            'performance',
            dimensions
        )
        
        return success
    
    def record_connection_metrics(self, active_connections: int, new_connections: int,
                                closed_connections: int, tenant_id: str = None) -> bool:
        """Record WebSocket connection metrics"""
        dimensions = {}
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        # Update real-time counter
        self.realtime_metrics['active_connections'] = active_connections
        
        # Record metrics
        success = self.record_metric(
            'ActiveConnections',
            active_connections,
            'Count',
            'infrastructure',
            dimensions
        )
        
        success &= self.record_metric(
            'NewConnections',
            new_connections,
            'Count',
            'infrastructure',
            dimensions
        )
        
        success &= self.record_metric(
            'ClosedConnections',
            closed_connections,
            'Count',
            'infrastructure',
            dimensions
        )
        
        return success
    
    def record_database_metrics(self, operation: str, table_name: str, latency_ms: float,
                              consumed_capacity: float = None, success: bool = True) -> bool:
        """Record database operation metrics"""
        dimensions = {
            'Operation': operation,
            'TableName': table_name,
            'Success': str(success)
        }
        
        # Record operation count
        metric_success = self.record_metric(
            'DatabaseOperations',
            1,
            'Count',
            'infrastructure',
            dimensions
        )
        
        # Record latency
        metric_success &= self.record_metric(
            'DatabaseLatency',
            latency_ms,
            'Milliseconds',
            'performance',
            dimensions
        )
        
        # Record consumed capacity if available
        if consumed_capacity is not None:
            metric_success &= self.record_metric(
                'ConsumedCapacity',
                consumed_capacity,
                'Count',
                'infrastructure',
                dimensions
            )
        
        return metric_success
    
    # Real-time Metrics
    def get_realtime_metrics(self) -> Dict[str, Any]:
        """Get current real-time metrics"""
        try:
            current_time = datetime.utcnow()
            
            # Calculate messages per minute (last 5 minutes)
            recent_messages = [
                msg for msg in self.realtime_metrics['messages_per_minute']
                if (current_time - msg['timestamp']).total_seconds() <= 300
            ]
            messages_per_minute = sum(msg['count'] for msg in recent_messages)
            
            # Calculate average response time (last 100 requests)
            recent_responses = list(self.realtime_metrics['response_times'])[-100:]
            avg_response_time = (
                sum(r['value'] for r in recent_responses) / len(recent_responses)
                if recent_responses else 0
            )
            
            # Calculate error rate (last 5 minutes)
            recent_errors = [
                err for err in self.realtime_metrics['error_rates']
                if (current_time - err['timestamp']).total_seconds() <= 300
            ]
            error_rate = len(recent_errors) / max(len(recent_messages), 1) if recent_messages else 0
            
            # Calculate cache hit rate (last 5 minutes)
            recent_cache_ops = [
                op for op in self.realtime_metrics['cache_hit_rates']
                if (current_time - op['timestamp']).total_seconds() <= 300
            ]
            cache_hit_rate = (
                sum(1 for op in recent_cache_ops if op['hit']) / len(recent_cache_ops)
                if recent_cache_ops else 0
            )
            
            return {
                'timestamp': current_time.isoformat(),
                'active_connections': self.realtime_metrics['active_connections'],
                'messages_per_minute': messages_per_minute,
                'avg_response_time_ms': avg_response_time,
                'error_rate': error_rate,
                'cache_hit_rate': cache_hit_rate,
                'health_status': self._calculate_health_status(
                    avg_response_time, error_rate, cache_hit_rate
                )
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get realtime metrics", extra={
                'error': str(e)
            })
            return {}
    
    def _calculate_health_status(self, response_time: float, error_rate: float, 
                               cache_hit_rate: float) -> str:
        """Calculate overall system health status"""
        if (response_time > self.thresholds['response_time_critical'] or
            error_rate > self.thresholds['error_rate_critical']):
            return 'critical'
        elif (response_time > self.thresholds['response_time_warning'] or
              error_rate > self.thresholds['error_rate_warning'] or
              cache_hit_rate < 0.7):
            return 'warning'
        else:
            return 'healthy'
    
    # Metric Management
    def _flush_metrics(self, namespace: str) -> bool:
        """Flush metrics buffer to CloudWatch"""
        try:
            if not self.cloudwatch or namespace not in self.metric_buffer:
                return True
            
            metrics_to_send = self.metric_buffer[namespace].copy()
            self.metric_buffer[namespace].clear()
            
            if not metrics_to_send:
                return True
            
            # Send metrics to CloudWatch
            response = self.cloudwatch.put_metric_data(
                Namespace=namespace,
                MetricData=metrics_to_send
            )
            
            lambda_logger.debug("Metrics flushed to CloudWatch", extra={
                'namespace': namespace,
                'metric_count': len(metrics_to_send)
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to flush metrics", extra={
                'namespace': namespace,
                'error': str(e)
            })
            return False
    
    def flush_all_metrics(self) -> bool:
        """Flush all buffered metrics"""
        try:
            with self.buffer_lock:
                success = True
                for namespace in list(self.metric_buffer.keys()):
                    success &= self._flush_metrics(namespace)
                return success
        except Exception as e:
            lambda_logger.error("Failed to flush all metrics", extra={
                'error': str(e)
            })
            return False
    
    def _start_metric_flusher(self):
        """Start background thread to periodically flush metrics"""
        def flush_worker():
            while True:
                try:
                    time.sleep(30)  # Flush every 30 seconds
                    self.flush_all_metrics()
                except Exception as e:
                    lambda_logger.error("Error in metric flusher thread", extra={
                        'error': str(e)
                    })
        
        # Start flusher thread as daemon
        flush_thread = threading.Thread(target=flush_worker, daemon=True)
        flush_thread.start()
        
        lambda_logger.info("Metric flusher thread started")


# Global monitoring service instance
monitoring_service = MonitoringService()
