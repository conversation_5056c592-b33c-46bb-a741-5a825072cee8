# services/chat/src/services/notification_service.py
# Notification service for real-time chat events

import json
import boto3
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    lambda_logger = MockLogger()

from ..config.chat_config import get_notification_config


class INotificationService(ABC):
    """Interface for notification service operations."""

    @abstractmethod
    def notify_new_message(
        self,
        conversation_id: str,
        message_data: Dict[str, Any],
        sender_user_id: str,
        tenant_id: str
    ) -> <PERSON><PERSON>[bool, Optional[str]]:
        """Send notification for new message."""
        pass

    @abstractmethod
    def notify_typing_indicator(
        self,
        conversation_id: str,
        user_id: str,
        tenant_id: str,
        is_typing: bool
    ) -> Tuple[bool, Optional[str]]:
        """Send typing indicator notification."""
        pass

    @abstractmethod
    def notify_presence_update(
        self,
        user_id: str,
        tenant_id: str,
        status: str,
        message: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Send presence update notification."""
        pass

    @abstractmethod
    def broadcast_to_conversation(
        self,
        conversation_id: str,
        tenant_id: str,
        event_type: str,
        data: Dict[str, Any],
        exclude_user_id: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Broadcast event to all conversation participants."""
        pass


class NotificationService(INotificationService):
    """Service for sending real-time notifications via WebSocket"""
    
    def __init__(self):
        self.config = get_notification_config()
        if shared_available:
            self.lambda_client = boto3.client('lambda')
        else:
            self.lambda_client = None
        
        # WebSocket configuration
        self.websocket_config = config.get_websocket_config()
        self.broadcast_function_name = self.websocket_config.get('broadcast_function_name')
    
    def notify_new_message(self, conversation_id: str, message_data: Dict[str, Any],
                          sender_user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Notify conversation participants about new message
        
        Returns:
            (success, error_message)
        """
        try:
            notification = {
                'action': 'new_message',
                'data': {
                    'messageId': message_data.get('messageId'),
                    'conversationId': conversation_id,
                    'content': message_data.get('content'),
                    'type': message_data.get('type', 'text'),
                    'userId': message_data.get('userId'),
                    'timestamp': message_data.get('timestamp'),
                    'status': 'sent',
                    'attachments': message_data.get('attachments', [])
                }
            }
            
            # Send via WebSocket broadcast
            success, error_msg = self._send_websocket_broadcast(
                message=notification,
                target_type='conversation',
                targets=[conversation_id],
                sender_user_id=sender_user_id,
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.info("New message notification sent", extra={
                    'conversation_id': conversation_id,
                    'message_id': message_data.get('messageId'),
                    'sender_user_id': sender_user_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify new message", extra={
                'conversation_id': conversation_id,
                'message_id': message_data.get('messageId'),
                'error': str(e)
            })
            return False, f"Failed to notify new message: {str(e)}"
    
    def notify_message_status_update(self, message_id: str, conversation_id: str,
                                   status: str, user_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Notify about message status update (delivered, read)
        
        Returns:
            (success, error_message)
        """
        try:
            notification = {
                'action': 'message_status_update',
                'data': {
                    'messageId': message_id,
                    'conversationId': conversation_id,
                    'status': status,
                    'userId': user_id,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            # Send to conversation participants
            success, error_msg = self._send_websocket_broadcast(
                message=notification,
                target_type='conversation',
                targets=[conversation_id],
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.debug("Message status update notification sent", extra={
                    'message_id': message_id,
                    'status': status,
                    'user_id': user_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify message status update", extra={
                'message_id': message_id,
                'status': status,
                'error': str(e)
            })
            return False, f"Failed to notify message status update: {str(e)}"
    
    def notify_typing_indicator(self, conversation_id: str, user_id: str, is_typing: bool,
                              tenant_id: str, sender_connection_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        Notify about typing indicator
        
        Returns:
            (success, error_message)
        """
        try:
            notification = {
                'action': 'typing_indicator',
                'data': {
                    'conversationId': conversation_id,
                    'userId': user_id,
                    'isTyping': is_typing,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            # Send to conversation participants
            success, error_msg = self._send_websocket_broadcast(
                message=notification,
                target_type='conversation',
                targets=[conversation_id],
                sender_connection_id=sender_connection_id,
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.debug("Typing indicator notification sent", extra={
                    'conversation_id': conversation_id,
                    'user_id': user_id,
                    'is_typing': is_typing
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify typing indicator", extra={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'error': str(e)
            })
            return False, f"Failed to notify typing indicator: {str(e)}"
    
    def notify_presence_update(self, user_id: str, status: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Notify about user presence update
        
        Returns:
            (success, error_message)
        """
        try:
            notification = {
                'action': 'presence_update',
                'data': {
                    'userId': user_id,
                    'status': status,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            # Send to tenant users (simplified - in real app would be contacts)
            success, error_msg = self._send_websocket_broadcast(
                message=notification,
                target_type='tenant',
                targets=[tenant_id],
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.debug("Presence update notification sent", extra={
                    'user_id': user_id,
                    'status': status,
                    'tenant_id': tenant_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify presence update", extra={
                'user_id': user_id,
                'status': status,
                'error': str(e)
            })
            return False, f"Failed to notify presence update: {str(e)}"
    
    def notify_agent_response(self, conversation_id: str, response_data: Dict[str, Any],
                            tenant_id: str) -> Tuple[bool, Optional[str]]:
        """
        Notify about agent response from N8N workflow
        
        Returns:
            (success, error_message)
        """
        try:
            notification = {
                'action': 'agent_response',
                'data': {
                    'conversationId': conversation_id,
                    'content': response_data.get('content'),
                    'type': response_data.get('type', 'text'),
                    'agentId': response_data.get('agentId'),
                    'agentName': response_data.get('agentName'),
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': response_data.get('metadata', {})
                }
            }
            
            # Send to conversation participants
            success, error_msg = self._send_websocket_broadcast(
                message=notification,
                target_type='conversation',
                targets=[conversation_id],
                tenant_id=tenant_id
            )
            
            if success:
                lambda_logger.info("Agent response notification sent", extra={
                    'conversation_id': conversation_id,
                    'agent_id': response_data.get('agentId'),
                    'tenant_id': tenant_id
                })
            
            return success, error_msg
            
        except Exception as e:
            lambda_logger.error("Failed to notify agent response", extra={
                'conversation_id': conversation_id,
                'agent_id': response_data.get('agentId'),
                'error': str(e)
            })
            return False, f"Failed to notify agent response: {str(e)}"
    
    def _send_websocket_broadcast(self, message: Dict[str, Any], target_type: str,
                                targets: List[str], tenant_id: str,
                                sender_user_id: str = None, 
                                sender_connection_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        Send message via WebSocket broadcast function
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.broadcast_function_name:
                return False, "WebSocket broadcast function not configured"
            
            # Prepare broadcast payload
            broadcast_payload = {
                'message': message,
                'targetType': target_type,
                'targets': targets,
                'tenantId': tenant_id
            }
            
            if sender_user_id:
                broadcast_payload['senderUserId'] = sender_user_id
            
            if sender_connection_id:
                broadcast_payload['senderConnectionId'] = sender_connection_id
            
            # Invoke WebSocket broadcast function
            if shared_available and self.lambda_client:
                response = self.lambda_client.invoke(
                    FunctionName=self.broadcast_function_name,
                    InvocationType='Event',  # Async invocation
                    Payload=json.dumps(broadcast_payload)
                )
                
                lambda_logger.debug("WebSocket broadcast invoked", extra={
                    'function_name': self.broadcast_function_name,
                    'target_type': target_type,
                    'targets': targets
                })
                
                return True, None
            else:
                # Mock success for development
                lambda_logger.debug("Mock WebSocket broadcast", extra={
                    'target_type': target_type,
                    'targets': targets
                })
                return True, None
                
        except Exception as e:
            lambda_logger.error("Failed to send WebSocket broadcast", extra={
                'function_name': self.broadcast_function_name,
                'target_type': target_type,
                'targets': targets,
                'error': str(e)
            })
            return False, f"WebSocket broadcast failed: {str(e)}"


# Global notification service instance
notification_service = NotificationService()
