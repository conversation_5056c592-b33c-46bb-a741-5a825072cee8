# services/chat/src/services/search_indexing_service.py
# Service for automatic indexing of messages and files

import json
import boto3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class SearchIndexingService:
    """Service for automatic indexing of messages and files in search engine"""
    
    def __init__(self):
        self.search_config = config.get_search_config()
        self.search_client = self._initialize_search_client()
        
        # Index configurations
        self.message_index = self.search_config.get('message_index', 'chat-messages')
        self.file_index = self.search_config.get('file_index', 'chat-files')
        
        # Indexing settings
        self.batch_size = 100
        self.max_retries = 3
        self.enabled = self.search_config.get('enabled', True)
    
    def _initialize_search_client(self):
        """Initialize search client (reuse from search_service)"""
        try:
            from ..services.search_service import search_service
            return search_service.search_client
        except Exception as e:
            lambda_logger.warning("Failed to initialize search client for indexing", extra={
                'error': str(e)
            })
            return None
    
    def index_message(self, message_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Index a single message
        
        Args:
            message_data: Message data to index
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                lambda_logger.debug("Search indexing disabled or client unavailable")
                return True, None
            
            # Prepare message document for indexing
            document = self._prepare_message_document(message_data)
            
            # Index document
            response = self.search_client.index(
                index=self.message_index,
                id=message_data.get('messageId'),
                body=document
            )
            
            lambda_logger.debug("Message indexed", extra={
                'message_id': message_data.get('messageId'),
                'conversation_id': message_data.get('conversationId'),
                'tenant_id': message_data.get('tenantId'),
                'index_result': response.get('result')
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to index message", extra={
                'message_id': message_data.get('messageId'),
                'error': str(e)
            })
            return False, f"Failed to index message: {str(e)}"
    
    def index_file(self, file_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Index a single file
        
        Args:
            file_data: File data to index
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                lambda_logger.debug("Search indexing disabled or client unavailable")
                return True, None
            
            # Prepare file document for indexing
            document = self._prepare_file_document(file_data)
            
            # Index document
            response = self.search_client.index(
                index=self.file_index,
                id=file_data.get('fileId'),
                body=document
            )
            
            lambda_logger.debug("File indexed", extra={
                'file_id': file_data.get('fileId'),
                'filename': file_data.get('filename'),
                'tenant_id': file_data.get('tenantId'),
                'index_result': response.get('result')
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to index file", extra={
                'file_id': file_data.get('fileId'),
                'error': str(e)
            })
            return False, f"Failed to index file: {str(e)}"
    
    def batch_index_messages(self, messages: List[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Index multiple messages in batch
        
        Returns:
            (success, batch_result, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                return True, {'indexed': 0, 'failed': 0}, None
            
            if not messages:
                return True, {'indexed': 0, 'failed': 0}, None
            
            # Prepare bulk request
            bulk_body = []
            for message in messages:
                # Add index action
                bulk_body.append({
                    'index': {
                        '_index': self.message_index,
                        '_id': message.get('messageId')
                    }
                })
                # Add document
                bulk_body.append(self._prepare_message_document(message))
            
            # Execute bulk request
            response = self.search_client.bulk(body=bulk_body)
            
            # Process results
            result = self._process_bulk_response(response)
            
            lambda_logger.info("Batch message indexing completed", extra={
                'total_messages': len(messages),
                'indexed': result['indexed'],
                'failed': result['failed']
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to batch index messages", extra={
                'message_count': len(messages),
                'error': str(e)
            })
            return False, {'indexed': 0, 'failed': len(messages)}, f"Batch indexing failed: {str(e)}"
    
    def batch_index_files(self, files: List[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Index multiple files in batch
        
        Returns:
            (success, batch_result, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                return True, {'indexed': 0, 'failed': 0}, None
            
            if not files:
                return True, {'indexed': 0, 'failed': 0}, None
            
            # Prepare bulk request
            bulk_body = []
            for file_data in files:
                # Add index action
                bulk_body.append({
                    'index': {
                        '_index': self.file_index,
                        '_id': file_data.get('fileId')
                    }
                })
                # Add document
                bulk_body.append(self._prepare_file_document(file_data))
            
            # Execute bulk request
            response = self.search_client.bulk(body=bulk_body)
            
            # Process results
            result = self._process_bulk_response(response)
            
            lambda_logger.info("Batch file indexing completed", extra={
                'total_files': len(files),
                'indexed': result['indexed'],
                'failed': result['failed']
            })
            
            return True, result, None
            
        except Exception as e:
            lambda_logger.error("Failed to batch index files", extra={
                'file_count': len(files),
                'error': str(e)
            })
            return False, {'indexed': 0, 'failed': len(files)}, f"Batch indexing failed: {str(e)}"
    
    def delete_message_from_index(self, message_id: str) -> Tuple[bool, Optional[str]]:
        """
        Delete message from search index
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                return True, None
            
            response = self.search_client.delete(
                index=self.message_index,
                id=message_id,
                ignore=[404]  # Ignore if document doesn't exist
            )
            
            lambda_logger.debug("Message deleted from index", extra={
                'message_id': message_id,
                'result': response.get('result')
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to delete message from index", extra={
                'message_id': message_id,
                'error': str(e)
            })
            return False, f"Failed to delete message: {str(e)}"
    
    def delete_file_from_index(self, file_id: str) -> Tuple[bool, Optional[str]]:
        """
        Delete file from search index
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                return True, None
            
            response = self.search_client.delete(
                index=self.file_index,
                id=file_id,
                ignore=[404]  # Ignore if document doesn't exist
            )
            
            lambda_logger.debug("File deleted from index", extra={
                'file_id': file_id,
                'result': response.get('result')
            })
            
            return True, None
            
        except Exception as e:
            lambda_logger.error("Failed to delete file from index", extra={
                'file_id': file_id,
                'error': str(e)
            })
            return False, f"Failed to delete file: {str(e)}"
    
    def create_indices(self) -> Tuple[bool, Optional[str]]:
        """
        Create search indices with proper mappings
        
        Returns:
            (success, error_message)
        """
        try:
            if not self.enabled or not self.search_client:
                return True, None
            
            # Create message index
            message_mapping = self._get_message_index_mapping()
            success_msg = self._create_index_if_not_exists(self.message_index, message_mapping)
            
            # Create file index
            file_mapping = self._get_file_index_mapping()
            success_file = self._create_index_if_not_exists(self.file_index, file_mapping)
            
            if success_msg and success_file:
                lambda_logger.info("Search indices created successfully", extra={
                    'message_index': self.message_index,
                    'file_index': self.file_index
                })
                return True, None
            else:
                return False, "Failed to create one or more indices"
            
        except Exception as e:
            lambda_logger.error("Failed to create search indices", extra={
                'error': str(e)
            })
            return False, f"Failed to create indices: {str(e)}"
    
    def _prepare_message_document(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare message document for indexing"""
        
        # Extract and clean content
        content = message_data.get('content', '')
        if isinstance(content, dict):
            content = json.dumps(content)
        
        document = {
            'messageId': message_data.get('messageId'),
            'conversationId': message_data.get('conversationId'),
            'userId': message_data.get('userId'),
            'tenantId': message_data.get('tenantId'),
            'content': content,
            'type': message_data.get('type', 'text'),
            'timestamp': message_data.get('timestamp', datetime.utcnow().isoformat()),
            'isFromAgent': message_data.get('isFromAgent', False),
            'agentId': message_data.get('agentId'),
            'agentType': message_data.get('agentType'),
            'status': message_data.get('status', 'sent'),
            'attachments': message_data.get('attachments', []),
            'metadata': message_data.get('metadata', {}),
            'indexed_at': datetime.utcnow().isoformat()
        }
        
        # Add searchable attachment content
        if message_data.get('attachments'):
            attachment_text = []
            for attachment in message_data.get('attachments', []):
                if attachment.get('filename'):
                    attachment_text.append(attachment['filename'])
                if attachment.get('content'):
                    attachment_text.append(str(attachment['content']))
            
            if attachment_text:
                document['attachment_content'] = ' '.join(attachment_text)
        
        return document
    
    def _prepare_file_document(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare file document for indexing"""
        
        document = {
            'fileId': file_data.get('fileId'),
            'filename': file_data.get('filename'),
            'originalFilename': file_data.get('originalFilename'),
            'fileKey': file_data.get('fileKey'),
            'fileSize': file_data.get('fileSize'),
            'contentType': file_data.get('contentType'),
            'fileType': file_data.get('fileType'),
            'uploadedBy': file_data.get('uploadedBy'),
            'tenantId': file_data.get('tenantId'),
            'conversationId': file_data.get('conversationId'),
            'uploadedAt': file_data.get('uploadedAt'),
            'status': file_data.get('status', 'uploaded'),
            'metadata': file_data.get('metadata', {}),
            'indexed_at': datetime.utcnow().isoformat()
        }
        
        # Add extracted text content if available
        if file_data.get('extractedText'):
            document['content'] = file_data['extractedText']
        
        # Add searchable metadata
        metadata = file_data.get('metadata', {})
        if metadata:
            searchable_metadata = []
            for key, value in metadata.items():
                if isinstance(value, (str, int, float)):
                    searchable_metadata.append(f"{key}:{value}")
            
            if searchable_metadata:
                document['searchable_metadata'] = ' '.join(searchable_metadata)
        
        return document
    
    def _process_bulk_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Process bulk indexing response"""
        
        result = {
            'indexed': 0,
            'failed': 0,
            'errors': []
        }
        
        if response.get('errors'):
            for item in response.get('items', []):
                if 'index' in item:
                    index_result = item['index']
                    if index_result.get('status') in [200, 201]:
                        result['indexed'] += 1
                    else:
                        result['failed'] += 1
                        if index_result.get('error'):
                            result['errors'].append({
                                'id': index_result.get('_id'),
                                'error': index_result['error']
                            })
        else:
            # No errors, count all as successful
            result['indexed'] = len(response.get('items', []))
        
        return result
    
    def _create_index_if_not_exists(self, index_name: str, mapping: Dict[str, Any]) -> bool:
        """Create index if it doesn't exist"""
        try:
            # Check if index exists
            if self.search_client.indices.exists(index=index_name):
                lambda_logger.debug(f"Index {index_name} already exists")
                return True
            
            # Create index with mapping
            self.search_client.indices.create(
                index=index_name,
                body=mapping
            )
            
            lambda_logger.info(f"Index {index_name} created successfully")
            return True
            
        except Exception as e:
            lambda_logger.error(f"Failed to create index {index_name}", extra={
                'error': str(e)
            })
            return False
    
    def _get_message_index_mapping(self) -> Dict[str, Any]:
        """Get mapping for message index"""
        return {
            "mappings": {
                "properties": {
                    "messageId": {"type": "keyword"},
                    "conversationId": {"type": "keyword"},
                    "userId": {"type": "keyword"},
                    "tenantId": {"type": "keyword"},
                    "content": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword", "ignore_above": 256}
                        }
                    },
                    "type": {"type": "keyword"},
                    "timestamp": {"type": "date"},
                    "isFromAgent": {"type": "boolean"},
                    "agentId": {"type": "keyword"},
                    "agentType": {"type": "keyword"},
                    "status": {"type": "keyword"},
                    "attachments": {"type": "object"},
                    "metadata": {"type": "object"},
                    "attachment_content": {"type": "text"},
                    "indexed_at": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "standard": {
                            "type": "standard",
                            "stopwords": "_english_"
                        }
                    }
                }
            }
        }
    
    def _get_file_index_mapping(self) -> Dict[str, Any]:
        """Get mapping for file index"""
        return {
            "mappings": {
                "properties": {
                    "fileId": {"type": "keyword"},
                    "filename": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword", "ignore_above": 256}
                        }
                    },
                    "originalFilename": {"type": "text"},
                    "fileKey": {"type": "keyword"},
                    "fileSize": {"type": "long"},
                    "contentType": {"type": "keyword"},
                    "fileType": {"type": "keyword"},
                    "uploadedBy": {"type": "keyword"},
                    "tenantId": {"type": "keyword"},
                    "conversationId": {"type": "keyword"},
                    "uploadedAt": {"type": "date"},
                    "status": {"type": "keyword"},
                    "metadata": {"type": "object"},
                    "content": {"type": "text"},
                    "searchable_metadata": {"type": "text"},
                    "indexed_at": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }


# Global search indexing service instance
search_indexing_service = SearchIndexingService()
