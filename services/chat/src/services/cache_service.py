# services/chat/src/services/cache_service.py
# Redis caching service for performance optimization

import json
import redis
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import hashlib

try:
    from shared.logger import lambda_logger
    shared_available = True
except ImportError:
    shared_available = False
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    lambda_logger = MockLogger()

from ..utils.config import config

class CacheService:
    """Redis-based caching service for performance optimization"""
    
    def __init__(self):
        self.cache_config = config.get_cache_config()
        self.redis_client = self._initialize_redis_client()
        
        # Cache TTL configurations (in seconds)
        self.ttl_config = {
            'user_session': 3600,      # 1 hour
            'conversation': 1800,      # 30 minutes
            'message': 900,            # 15 minutes
            'file_metadata': 3600,     # 1 hour
            'search_results': 300,     # 5 minutes
            'analytics': 600,          # 10 minutes
            'presence': 300,           # 5 minutes
            'typing': 30,              # 30 seconds
            'agent_config': 7200,      # 2 hours
            'user_preferences': 3600   # 1 hour
        }
        
        # Cache key prefixes
        self.key_prefixes = {
            'session': 'sess',
            'conversation': 'conv',
            'message': 'msg',
            'file': 'file',
            'search': 'search',
            'analytics': 'analytics',
            'presence': 'presence',
            'typing': 'typing',
            'agent': 'agent',
            'user': 'user'
        }
        
        self.enabled = self.cache_config.get('enabled', True)
    
    def _initialize_redis_client(self):
        """Initialize Redis client"""
        try:
            if not self.cache_config.get('enabled', True):
                lambda_logger.info("Cache service disabled")
                return None
            
            redis_config = self.cache_config.get('redis', {})
            
            client = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                password=redis_config.get('password'),
                db=redis_config.get('db', 0),
                decode_responses=True,
                socket_timeout=redis_config.get('timeout', 5),
                socket_connect_timeout=redis_config.get('connect_timeout', 5),
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            client.ping()
            
            lambda_logger.info("Redis cache client initialized", extra={
                'host': redis_config.get('host'),
                'port': redis_config.get('port'),
                'db': redis_config.get('db', 0)
            })
            
            return client
            
        except Exception as e:
            lambda_logger.warning("Failed to initialize Redis client", extra={
                'error': str(e)
            })
            return None
    
    def _generate_cache_key(self, prefix: str, *args) -> str:
        """Generate cache key with prefix and arguments"""
        key_parts = [self.key_prefixes.get(prefix, prefix)]
        key_parts.extend(str(arg) for arg in args)
        return ':'.join(key_parts)
    
    def _serialize_value(self, value: Any) -> str:
        """Serialize value for Redis storage"""
        if isinstance(value, (dict, list)):
            return json.dumps(value, default=str)
        return str(value)
    
    def _deserialize_value(self, value: str) -> Any:
        """Deserialize value from Redis"""
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    # Session Management
    def cache_user_session(self, user_id: str, tenant_id: str, session_data: Dict[str, Any]) -> bool:
        """Cache user session data"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('session', tenant_id, user_id)
            serialized_data = self._serialize_value(session_data)
            
            self.redis_client.setex(
                cache_key,
                self.ttl_config['user_session'],
                serialized_data
            )
            
            lambda_logger.debug("User session cached", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'cache_key': cache_key
            })
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to cache user session", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def get_user_session(self, user_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get cached user session data"""
        try:
            if not self.enabled or not self.redis_client:
                return None
            
            cache_key = self._generate_cache_key('session', tenant_id, user_id)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                return self._deserialize_value(cached_data)
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Failed to get user session from cache", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    def invalidate_user_session(self, user_id: str, tenant_id: str) -> bool:
        """Invalidate cached user session"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('session', tenant_id, user_id)
            self.redis_client.delete(cache_key)
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to invalidate user session", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    # Conversation Caching
    def cache_conversation(self, conversation_id: str, tenant_id: str, conversation_data: Dict[str, Any]) -> bool:
        """Cache conversation data"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('conversation', tenant_id, conversation_id)
            serialized_data = self._serialize_value(conversation_data)
            
            self.redis_client.setex(
                cache_key,
                self.ttl_config['conversation'],
                serialized_data
            )
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to cache conversation", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def get_conversation(self, conversation_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get cached conversation data"""
        try:
            if not self.enabled or not self.redis_client:
                return None
            
            cache_key = self._generate_cache_key('conversation', tenant_id, conversation_id)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                return self._deserialize_value(cached_data)
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Failed to get conversation from cache", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    # Message Caching
    def cache_recent_messages(self, conversation_id: str, tenant_id: str, messages: List[Dict[str, Any]]) -> bool:
        """Cache recent messages for a conversation"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('message', tenant_id, conversation_id, 'recent')
            serialized_data = self._serialize_value(messages)
            
            self.redis_client.setex(
                cache_key,
                self.ttl_config['message'],
                serialized_data
            )
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to cache recent messages", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def get_recent_messages(self, conversation_id: str, tenant_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached recent messages"""
        try:
            if not self.enabled or not self.redis_client:
                return None
            
            cache_key = self._generate_cache_key('message', tenant_id, conversation_id, 'recent')
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                return self._deserialize_value(cached_data)
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Failed to get recent messages from cache", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    # Search Results Caching
    def cache_search_results(self, query_hash: str, tenant_id: str, results: Dict[str, Any]) -> bool:
        """Cache search results"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('search', tenant_id, query_hash)
            serialized_data = self._serialize_value(results)
            
            self.redis_client.setex(
                cache_key,
                self.ttl_config['search_results'],
                serialized_data
            )
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to cache search results", extra={
                'query_hash': query_hash,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def get_search_results(self, query_hash: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get cached search results"""
        try:
            if not self.enabled or not self.redis_client:
                return None
            
            cache_key = self._generate_cache_key('search', tenant_id, query_hash)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                return self._deserialize_value(cached_data)
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Failed to get search results from cache", extra={
                'query_hash': query_hash,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    def generate_search_hash(self, query: str, filters: Dict[str, Any], page: int, page_size: int) -> str:
        """Generate hash for search query caching"""
        search_params = {
            'query': query,
            'filters': filters,
            'page': page,
            'page_size': page_size
        }
        
        search_string = json.dumps(search_params, sort_keys=True)
        return hashlib.md5(search_string.encode()).hexdigest()
    
    # Presence & Typing Indicators
    def set_user_presence(self, user_id: str, tenant_id: str, status: str) -> bool:
        """Set user presence status"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('presence', tenant_id, user_id)
            presence_data = {
                'status': status,
                'last_seen': datetime.utcnow().isoformat()
            }
            
            self.redis_client.setex(
                cache_key,
                self.ttl_config['presence'],
                self._serialize_value(presence_data)
            )
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to set user presence", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def get_user_presence(self, user_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get user presence status"""
        try:
            if not self.enabled or not self.redis_client:
                return None
            
            cache_key = self._generate_cache_key('presence', tenant_id, user_id)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                return self._deserialize_value(cached_data)
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Failed to get user presence", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    def set_typing_indicator(self, user_id: str, conversation_id: str, tenant_id: str, is_typing: bool) -> bool:
        """Set typing indicator"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            cache_key = self._generate_cache_key('typing', tenant_id, conversation_id, user_id)
            
            if is_typing:
                self.redis_client.setex(cache_key, self.ttl_config['typing'], 'true')
            else:
                self.redis_client.delete(cache_key)
            
            return True
            
        except Exception as e:
            lambda_logger.warning("Failed to set typing indicator", extra={
                'user_id': user_id,
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return False
    
    def get_typing_users(self, conversation_id: str, tenant_id: str) -> List[str]:
        """Get users currently typing in conversation"""
        try:
            if not self.enabled or not self.redis_client:
                return []
            
            pattern = self._generate_cache_key('typing', tenant_id, conversation_id, '*')
            typing_keys = self.redis_client.keys(pattern)
            
            # Extract user IDs from keys
            typing_users = []
            for key in typing_keys:
                parts = key.split(':')
                if len(parts) >= 4:
                    typing_users.append(parts[-1])  # Last part is user_id
            
            return typing_users
            
        except Exception as e:
            lambda_logger.warning("Failed to get typing users", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    # Cache Management
    def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate cache keys matching pattern"""
        try:
            if not self.enabled or not self.redis_client:
                return 0
            
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            
            return 0
            
        except Exception as e:
            lambda_logger.warning("Failed to invalidate cache pattern", extra={
                'pattern': pattern,
                'error': str(e)
            })
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            if not self.enabled or not self.redis_client:
                return {'enabled': False}
            
            info = self.redis_client.info()
            
            return {
                'enabled': True,
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0)
            }
            
        except Exception as e:
            lambda_logger.warning("Failed to get cache stats", extra={
                'error': str(e)
            })
            return {'enabled': False, 'error': str(e)}
    
    def _calculate_hit_rate(self, info: Dict[str, Any]) -> float:
        """Calculate cache hit rate"""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        total = hits + misses
        
        if total == 0:
            return 0.0
        
        return round((hits / total) * 100, 2)
    
    def flush_cache(self, tenant_id: str = None) -> bool:
        """Flush cache (all or tenant-specific)"""
        try:
            if not self.enabled or not self.redis_client:
                return True
            
            if tenant_id:
                # Flush only tenant-specific keys
                pattern = f"*:{tenant_id}:*"
                deleted = self.invalidate_pattern(pattern)
                lambda_logger.info("Tenant cache flushed", extra={
                    'tenant_id': tenant_id,
                    'keys_deleted': deleted
                })
            else:
                # Flush all cache
                self.redis_client.flushdb()
                lambda_logger.info("All cache flushed")
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to flush cache", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False


# Global cache service instance
cache_service = CacheService()
