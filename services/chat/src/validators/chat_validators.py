# services/chat/src/validators/chat_validators.py
# Chat service specific validators using shared validation

from typing import Dict, Any, List, Optional
import json

from shared.validators import (
    validate_uuid, 
    validate_required_fields,
    ValidationException
)
from shared.validation_manager import ValidationManager

class ChatMessageValidator:
    """Validator for chat message requests"""
    
    @staticmethod
    def validate_send_message(body: Dict[str, Any]) -> Dict[str, Any]:
        """Validate send message request"""
        # Validate required fields
        validate_required_fields(body, ['conversationId', 'content'])
        
        # Validate conversation ID format
        conversation_id = validate_uuid(body['conversationId'], 'Conversation ID')
        
        # Validate content
        content = body['content']
        if not isinstance(content, str):
            raise ValidationException("Content must be a string")
        
        content = content.strip()
        if not content:
            raise ValidationException("Content cannot be empty")
        
        if len(content) > 4000:
            raise ValidationException("Content exceeds maximum length of 4000 characters")
        
        # Validate message type
        message_type = body.get('type', 'text')
        valid_types = ['text', 'image', 'file', 'audio', 'video']
        if message_type not in valid_types:
            raise ValidationException(f"Invalid message type. Must be one of: {', '.join(valid_types)}")
        
        # Validate attachments
        attachments = body.get('attachments', [])
        if not isinstance(attachments, list):
            raise ValidationException("Attachments must be a list")
        
        if len(attachments) > 10:
            raise ValidationException("Maximum 10 attachments allowed per message")
        
        # Validate each attachment
        for i, attachment in enumerate(attachments):
            ChatMessageValidator._validate_attachment(attachment, i)
        
        return {
            'conversationId': conversation_id,
            'content': content,
            'type': message_type,
            'attachments': attachments,
            'metadata': body.get('metadata', {})
        }
    
    @staticmethod
    def _validate_attachment(attachment: Dict[str, Any], index: int) -> None:
        """Validate individual attachment"""
        if not isinstance(attachment, dict):
            raise ValidationException(f"Attachment {index} must be an object")
        
        # Required fields for attachment
        required_fields = ['type', 'url', 'name']
        for field in required_fields:
            if field not in attachment:
                raise ValidationException(f"Attachment {index} missing required field: {field}")
        
        # Validate attachment type
        attachment_type = attachment['type']
        valid_attachment_types = ['image', 'document', 'audio', 'video']
        if attachment_type not in valid_attachment_types:
            raise ValidationException(f"Attachment {index} has invalid type. Must be one of: {', '.join(valid_attachment_types)}")
        
        # Validate URL format (basic check)
        url = attachment['url']
        if not isinstance(url, str) or not url.startswith(('http://', 'https://')):
            raise ValidationException(f"Attachment {index} has invalid URL format")
        
        # Validate file name
        name = attachment['name']
        if not isinstance(name, str) or not name.strip():
            raise ValidationException(f"Attachment {index} has invalid name")
        
        # Validate file size if provided
        if 'size' in attachment:
            size = attachment['size']
            if not isinstance(size, int) or size <= 0:
                raise ValidationException(f"Attachment {index} has invalid size")
            
            # 50MB limit
            if size > 50 * 1024 * 1024:
                raise ValidationException(f"Attachment {index} exceeds maximum size of 50MB")

class ConversationValidator:
    """Validator for conversation requests"""
    
    @staticmethod
    def validate_get_messages(query_params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate get messages request"""
        validated = {}
        
        # Validate limit
        if 'limit' in query_params:
            try:
                limit = int(query_params['limit'])
                if limit <= 0 or limit > 100:
                    raise ValidationException("Limit must be between 1 and 100")
                validated['limit'] = limit
            except ValueError:
                raise ValidationException("Limit must be a valid integer")
        else:
            validated['limit'] = 25  # Default
        
        # Validate cursor for pagination
        if 'cursor' in query_params:
            cursor = query_params['cursor']
            if not isinstance(cursor, str) or not cursor.strip():
                raise ValidationException("Cursor must be a non-empty string")
            validated['cursor'] = cursor
        
        # Validate message type filter
        if 'type' in query_params:
            message_type = query_params['type']
            valid_types = ['text', 'image', 'file', 'audio', 'video']
            if message_type not in valid_types:
                raise ValidationException(f"Invalid message type filter. Must be one of: {', '.join(valid_types)}")
            validated['type'] = message_type
        
        return validated
    
    @staticmethod
    def validate_conversation_id(conversation_id: str) -> str:
        """Validate conversation ID"""
        return validate_uuid(conversation_id, 'Conversation ID')

class PresenceValidator:
    """Validator for presence requests"""
    
    @staticmethod
    def validate_update_presence(body: Dict[str, Any]) -> Dict[str, Any]:
        """Validate update presence request"""
        # Validate required fields
        validate_required_fields(body, ['status'])
        
        # Validate status
        status = body['status']
        valid_statuses = ['online', 'away', 'busy', 'offline']
        if status not in valid_statuses:
            raise ValidationException(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
        
        # Validate optional message
        message = body.get('message', '')
        if message and len(message) > 100:
            raise ValidationException("Presence message cannot exceed 100 characters")
        
        return {
            'status': status,
            'message': message.strip() if message else ''
        }

class TypingValidator:
    """Validator for typing indicator requests"""
    
    @staticmethod
    def validate_typing_indicator(body: Dict[str, Any]) -> Dict[str, Any]:
        """Validate typing indicator request"""
        # Validate required fields
        validate_required_fields(body, ['conversationId', 'isTyping'])
        
        # Validate conversation ID
        conversation_id = validate_uuid(body['conversationId'], 'Conversation ID')
        
        # Validate isTyping boolean
        is_typing = body['isTyping']
        if not isinstance(is_typing, bool):
            raise ValidationException("isTyping must be a boolean")
        
        return {
            'conversationId': conversation_id,
            'isTyping': is_typing
        }

class SearchValidator:
    """Validator for search requests"""
    
    @staticmethod
    def validate_search_messages(query_params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate search messages request"""
        # Validate required query
        if 'q' not in query_params:
            raise ValidationException("Search query 'q' is required")
        
        query = query_params['q'].strip()
        if not query:
            raise ValidationException("Search query cannot be empty")
        
        if len(query) < 2:
            raise ValidationException("Search query must be at least 2 characters")
        
        if len(query) > 100:
            raise ValidationException("Search query cannot exceed 100 characters")
        
        validated = {'query': query}
        
        # Validate limit
        if 'limit' in query_params:
            try:
                limit = int(query_params['limit'])
                if limit <= 0 or limit > 50:
                    raise ValidationException("Search limit must be between 1 and 50")
                validated['limit'] = limit
            except ValueError:
                raise ValidationException("Search limit must be a valid integer")
        else:
            validated['limit'] = 20  # Default
        
        # Validate conversation filter
        if 'conversationId' in query_params:
            conversation_id = validate_uuid(query_params['conversationId'], 'Conversation ID')
            validated['conversationId'] = conversation_id
        
        # Validate date range
        if 'from_date' in query_params:
            from_date = query_params['from_date']
            # Basic ISO date validation
            if not isinstance(from_date, str) or len(from_date) < 10:
                raise ValidationException("from_date must be a valid ISO date string")
            validated['from_date'] = from_date
        
        if 'to_date' in query_params:
            to_date = query_params['to_date']
            # Basic ISO date validation
            if not isinstance(to_date, str) or len(to_date) < 10:
                raise ValidationException("to_date must be a valid ISO date string")
            validated['to_date'] = to_date
        
        return validated

# Validation manager instance for chat service
chat_validation_manager = ValidationManager("chat")

def validate_chat_request(validator_class, method_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Generic validation helper"""
    try:
        validator_method = getattr(validator_class, method_name)
        return validator_method(data)
    except AttributeError:
        raise ValidationException(f"Validator method {method_name} not found")
    except Exception as e:
        if isinstance(e, ValidationException):
            raise
        raise ValidationException(f"Validation error: {str(e)}")
