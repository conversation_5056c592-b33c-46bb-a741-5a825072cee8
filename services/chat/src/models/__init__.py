# services/chat/src/models/__init__.py
# Models package for chat service

"""
Models package for chat service.
Exports unified models that use shared layer as source of truth.
"""

# Import chat-specific models (extended from shared)
from .message import Message, MessageCreateRequest
from .conversation import Conversation, ConversationCreateRequest
from .connection import WebSocketConnection, ConnectionInfo
from .notification import NotificationEvent, NotificationPreferences

# Re-export shared models for convenience
from shared.models import (
    UserInfo, UserRole, UserStatus,
    TenantInfo, TenantStatus, TenantPlan,
    CustomerInfo, CustomerStatus,
    SubscriptionInfo, SubscriptionStatus, BillingInterval
)

__all__ = [
    # Chat-specific models
    'Message', 'MessageCreateRequest',
    'Conversation', 'ConversationCreateRequest',
    'WebSocketConnection', 'ConnectionInfo',
    'NotificationEvent', 'NotificationPreferences',

    # Shared models (re-exported for convenience)
    'UserInfo', 'UserRole', 'UserStatus',
    'TenantInfo', 'TenantStatus', 'TenantPlan',
    'CustomerInfo', 'CustomerStatus',
    'SubscriptionInfo', 'SubscriptionStatus', 'BillingInterval'
]
