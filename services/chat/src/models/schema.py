# services/chat/src/models/schema.py
# DynamoDB schema definitions for Chat Service entities

"""
DynamoDB Schema Design for Chat Service

This module defines the schema patterns for storing chat-related entities
in dedicated DynamoDB tables optimized for real-time messaging.

TABLES:
1. chat-messages - Real-time chat messages with history
2. conversation-states - Conversation metadata and states
3. user-presence - User online/offline status

ENTITIES:
1. ChatMessage - Individual chat messages
2. ConversationState - Conversation metadata and participants
3. UserPresence - User presence and activity status

ACCESS PATTERNS:
1. Get messages in conversation → PK: conversationId, SK: timestamp
2. Get message by ID → GSI1: messageId
3. Get user messages → GSI2: userId + timestamp
4. Get conversation state → PK: conversationId
5. Get tenant conversations → GSI1: tenantId
6. Get user presence → PK: userId
7. Get tenant presence → GSI1: tenantId

INDEXES USED:
- Primary: conversationId, timestamp (for chat-messages)
- GSI1: messageId (for direct message lookup)
- GSI2: userId + timestamp (for user message history)
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import uuid


class ChatMessageSchema:
    """Schema patterns for ChatMessage entities"""
    
    @staticmethod
    def create_message_item(
        conversation_id: str,
        user_id: str,
        tenant_id: str,
        content: str,
        message_type: str = "text",
        message_id: str = None,
        attachments: List[Dict[str, Any]] = None,
        reply_to_message_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a chat message item for DynamoDB"""
        
        timestamp = datetime.utcnow()
        message_id = message_id or str(uuid.uuid4())
        
        # TTL: 1 year from creation
        ttl = int((timestamp + timedelta(days=365)).timestamp())
        
        item = {
            # Primary key
            'conversationId': conversation_id,
            'timestamp': timestamp.isoformat(),
            
            # Message data
            'messageId': message_id,
            'userId': user_id,
            'tenantId': tenant_id,
            'content': content,
            'type': message_type,
            'status': 'sent',
            
            # Metadata
            'createdAt': timestamp.isoformat(),
            'updatedAt': timestamp.isoformat(),
            'ttl': ttl,
            
            # Entity type for filtering
            'EntityType': 'ChatMessage'
        }
        
        # Optional fields
        if attachments:
            item['attachments'] = attachments
        
        if reply_to_message_id:
            item['replyToMessageId'] = reply_to_message_id
        
        if metadata:
            item['metadata'] = metadata
        
        return item
    
    @staticmethod
    def get_message_key(message_id: str) -> Dict[str, str]:
        """Get primary key for message lookup by ID (using GSI)"""
        return {
            'messageId': message_id
        }
    
    @staticmethod
    def get_conversation_messages_query(conversation_id: str, limit: int = 50, 
                                      last_evaluated_key: str = None) -> Dict[str, Any]:
        """Get query parameters for conversation messages"""
        query_params = {
            'KeyConditionExpression': 'conversationId = :conversation_id',
            'ExpressionAttributeValues': {
                ':conversation_id': conversation_id
            },
            'ScanIndexForward': False,  # Most recent first
            'Limit': limit
        }
        
        if last_evaluated_key:
            query_params['ExclusiveStartKey'] = {
                'conversationId': conversation_id,
                'timestamp': last_evaluated_key
            }
        
        return query_params
    
    @staticmethod
    def get_user_messages_query(user_id: str, limit: int = 50) -> Dict[str, Any]:
        """Get query parameters for user messages (using GSI2)"""
        return {
            'IndexName': 'UserIndex',
            'KeyConditionExpression': 'userId = :user_id',
            'ExpressionAttributeValues': {
                ':user_id': user_id
            },
            'ScanIndexForward': False,  # Most recent first
            'Limit': limit
        }
    
    @staticmethod
    def update_message_status(message_id: str, status: str) -> Dict[str, Any]:
        """Get update parameters for message status"""
        return {
            'Key': {'messageId': message_id},
            'UpdateExpression': 'SET #status = :status, updatedAt = :updated_at',
            'ExpressionAttributeNames': {
                '#status': 'status'
            },
            'ExpressionAttributeValues': {
                ':status': status,
                ':updated_at': datetime.utcnow().isoformat()
            }
        }


class ConversationStateSchema:
    """Schema patterns for ConversationState entities"""
    
    @staticmethod
    def create_conversation_state_item(
        conversation_id: str,
        tenant_id: str,
        agent_id: str,
        participants: List[str],
        status: str = "active",
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a conversation state item for DynamoDB"""
        
        timestamp = datetime.utcnow()
        
        # TTL: 1 year from creation
        ttl = int((timestamp + timedelta(days=365)).timestamp())
        
        item = {
            # Primary key
            'conversationId': conversation_id,
            
            # Conversation data
            'tenantId': tenant_id,
            'agentId': agent_id,
            'participants': participants,
            'status': status,
            'messageCount': 0,
            'lastMessageAt': timestamp.isoformat(),
            
            # Metadata
            'createdAt': timestamp.isoformat(),
            'updatedAt': timestamp.isoformat(),
            'ttl': ttl,
            
            # Entity type for filtering
            'EntityType': 'ConversationState'
        }
        
        if metadata:
            item['metadata'] = metadata
        
        return item
    
    @staticmethod
    def get_conversation_state_key(conversation_id: str) -> Dict[str, str]:
        """Get primary key for conversation state"""
        return {
            'conversationId': conversation_id
        }
    
    @staticmethod
    def get_tenant_conversations_query(tenant_id: str, limit: int = 50) -> Dict[str, Any]:
        """Get query parameters for tenant conversations"""
        return {
            'IndexName': 'TenantIndex',
            'KeyConditionExpression': 'tenantId = :tenant_id',
            'ExpressionAttributeValues': {
                ':tenant_id': tenant_id
            },
            'ScanIndexForward': False,  # Most recent first
            'Limit': limit
        }
    
    @staticmethod
    def update_conversation_activity(conversation_id: str, message_count_increment: int = 1) -> Dict[str, Any]:
        """Get update parameters for conversation activity"""
        return {
            'Key': {'conversationId': conversation_id},
            'UpdateExpression': 'SET lastMessageAt = :last_message_at, updatedAt = :updated_at ADD messageCount :increment',
            'ExpressionAttributeValues': {
                ':last_message_at': datetime.utcnow().isoformat(),
                ':updated_at': datetime.utcnow().isoformat(),
                ':increment': message_count_increment
            }
        }


class UserPresenceSchema:
    """Schema patterns for UserPresence entities"""
    
    @staticmethod
    def create_user_presence_item(
        user_id: str,
        tenant_id: str,
        status: str = "online",
        last_seen: datetime = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a user presence item for DynamoDB"""
        
        timestamp = datetime.utcnow()
        last_seen = last_seen or timestamp
        
        # TTL: 7 days from last activity
        ttl = int((timestamp + timedelta(days=7)).timestamp())
        
        item = {
            # Primary key
            'userId': user_id,
            
            # Presence data
            'tenantId': tenant_id,
            'status': status,  # online, offline, away, busy
            'lastSeen': last_seen.isoformat(),
            'connectionCount': 1 if status == 'online' else 0,
            
            # Metadata
            'createdAt': timestamp.isoformat(),
            'updatedAt': timestamp.isoformat(),
            'ttl': ttl,
            
            # Entity type for filtering
            'EntityType': 'UserPresence'
        }
        
        if metadata:
            item['metadata'] = metadata
        
        return item
    
    @staticmethod
    def get_user_presence_key(user_id: str) -> Dict[str, str]:
        """Get primary key for user presence"""
        return {
            'userId': user_id
        }
    
    @staticmethod
    def get_tenant_presence_query(tenant_id: str, limit: int = 100) -> Dict[str, Any]:
        """Get query parameters for tenant user presence"""
        return {
            'IndexName': 'TenantIndex',
            'KeyConditionExpression': 'tenantId = :tenant_id',
            'ExpressionAttributeValues': {
                ':tenant_id': tenant_id
            },
            'Limit': limit
        }
    
    @staticmethod
    def update_user_presence(user_id: str, status: str, connection_count_delta: int = 0) -> Dict[str, Any]:
        """Get update parameters for user presence"""
        update_expression = 'SET #status = :status, lastSeen = :last_seen, updatedAt = :updated_at'
        expression_values = {
            ':status': status,
            ':last_seen': datetime.utcnow().isoformat(),
            ':updated_at': datetime.utcnow().isoformat()
        }
        
        if connection_count_delta != 0:
            update_expression += ' ADD connectionCount :delta'
            expression_values[':delta'] = connection_count_delta
        
        return {
            'Key': {'userId': user_id},
            'UpdateExpression': update_expression,
            'ExpressionAttributeNames': {
                '#status': 'status'
            },
            'ExpressionAttributeValues': expression_values
        }


class ChatMessageSchema:
    """Schema patterns for ChatMessage entities - additional methods"""

    @staticmethod
    def update_message_status(conversation_id: str, timestamp: str, status: str) -> Dict[str, Any]:
        """Get update parameters for message status"""
        return {
            'Key': {
                'conversationId': conversation_id,
                'timestamp': timestamp
            },
            'UpdateExpression': 'SET #status = :status, updatedAt = :updated_at',
            'ExpressionAttributeNames': {
                '#status': 'status'
            },
            'ExpressionAttributeValues': {
                ':status': status,
                ':updated_at': datetime.utcnow().isoformat()
            }
        }
