# services/chat/src/models/chat_message.py
# Chat message models for real-time messaging

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import uuid
import json

class ChatMessage:
    """Model for chat messages in real-time conversations"""
    
    def __init__(self, conversation_id: str, user_id: str, tenant_id: str,
                 content: str, message_type: str = 'text', message_id: str = None):
        self.message_id = message_id or str(uuid.uuid4())
        self.conversation_id = conversation_id
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.content = content
        self.message_type = message_type
        
        # Timestamps
        self.timestamp = datetime.utcnow()
        self.created_at = self.timestamp
        self.updated_at = self.timestamp
        
        # Message state
        self.status = 'sent'  # sent, delivered, read, failed
        self.attachments = []
        self.reply_to_message_id = None
        self.metadata = {}
        
        # Flags
        self.is_edited = False
        self.is_deleted = False
        self.edit_history = []
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        """Create ChatMessage from dictionary"""
        message = cls(
            conversation_id=data['conversationId'],
            user_id=data['userId'],
            tenant_id=data['tenantId'],
            content=data['content'],
            message_type=data.get('type', 'text'),
            message_id=data.get('messageId')
        )
        
        # Set optional fields
        if 'timestamp' in data:
            message.timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
        if 'createdAt' in data:
            message.created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
        if 'updatedAt' in data:
            message.updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
        
        message.status = data.get('status', 'sent')
        message.attachments = data.get('attachments', [])
        message.reply_to_message_id = data.get('replyToMessageId')
        message.metadata = data.get('metadata', {})
        message.is_edited = data.get('isEdited', False)
        message.is_deleted = data.get('isDeleted', False)
        message.edit_history = data.get('editHistory', [])
        
        return message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'messageId': self.message_id,
            'conversationId': self.conversation_id,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'content': self.content,
            'type': self.message_type,
            'timestamp': self.timestamp.isoformat(),
            'createdAt': self.created_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            'status': self.status,
            'attachments': self.attachments,
            'replyToMessageId': self.reply_to_message_id,
            'metadata': self.metadata,
            'isEdited': self.is_edited,
            'isDeleted': self.is_deleted,
            'editHistory': self.edit_history
        }
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format"""
        # TTL: 1 year from creation
        ttl = int((self.created_at + timedelta(days=365)).timestamp())
        
        item = {
            # Primary key for chat messages table
            'conversationId': self.conversation_id,
            'timestamp': self.timestamp.isoformat(),
            
            # Message data
            'messageId': self.message_id,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'content': self.content,
            'type': self.message_type,
            'status': self.status,
            
            # Timestamps
            'createdAt': self.created_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            
            # Optional fields
            'attachments': self.attachments,
            'metadata': self.metadata,
            'isEdited': self.is_edited,
            'isDeleted': self.is_deleted,
            'editHistory': self.edit_history,
            
            # DynamoDB specific
            'ttl': ttl,
            'EntityType': 'ChatMessage'
        }
        
        if self.reply_to_message_id:
            item['replyToMessageId'] = self.reply_to_message_id
        
        return item
    
    def add_attachment(self, attachment: Dict[str, Any]):
        """Add attachment to message"""
        self.attachments.append(attachment)
        self.updated_at = datetime.utcnow()
    
    def update_status(self, status: str):
        """Update message status"""
        valid_statuses = ['sent', 'delivered', 'read', 'failed']
        if status not in valid_statuses:
            raise ValueError(f"Invalid status. Must be one of: {valid_statuses}")
        
        self.status = status
        self.updated_at = datetime.utcnow()
    
    def edit_content(self, new_content: str, edited_by: str = None):
        """Edit message content"""
        # Save edit history
        edit_record = {
            'previousContent': self.content,
            'editedAt': datetime.utcnow().isoformat(),
            'editedBy': edited_by or self.user_id
        }
        self.edit_history.append(edit_record)
        
        # Update content
        self.content = new_content
        self.is_edited = True
        self.updated_at = datetime.utcnow()
    
    def soft_delete(self, deleted_by: str = None):
        """Soft delete message"""
        self.is_deleted = True
        self.content = "[Message deleted]"
        self.updated_at = datetime.utcnow()
        
        # Add to metadata
        self.metadata['deletedAt'] = datetime.utcnow().isoformat()
        self.metadata['deletedBy'] = deleted_by or self.user_id
    
    def has_attachments(self) -> bool:
        """Check if message has attachments"""
        return len(self.attachments) > 0
    
    def is_reply(self) -> bool:
        """Check if message is a reply"""
        return self.reply_to_message_id is not None
    
    def get_age(self) -> timedelta:
        """Get message age"""
        return datetime.utcnow() - self.created_at
    
    def can_be_edited(self, max_edit_time_minutes: int = 60) -> bool:
        """Check if message can still be edited"""
        if self.is_deleted:
            return False
        
        age = self.get_age()
        return age.total_seconds() < (max_edit_time_minutes * 60)
    
    def __str__(self) -> str:
        return f"ChatMessage(id={self.message_id}, conversation={self.conversation_id}, type={self.message_type})"
    
    def __repr__(self) -> str:
        return self.__str__()


class MessageAttachment:
    """Model for message attachments"""
    
    def __init__(self, file_name: str, file_type: str, file_size: int,
                 s3_key: str, attachment_id: str = None):
        self.attachment_id = attachment_id or str(uuid.uuid4())
        self.file_name = file_name
        self.file_type = file_type
        self.file_size = file_size
        self.s3_key = s3_key
        self.uploaded_at = datetime.utcnow()
        self.download_count = 0
        self.metadata = {}
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageAttachment':
        """Create MessageAttachment from dictionary"""
        attachment = cls(
            file_name=data['fileName'],
            file_type=data['fileType'],
            file_size=data['fileSize'],
            s3_key=data['s3Key'],
            attachment_id=data.get('attachmentId')
        )
        
        if 'uploadedAt' in data:
            attachment.uploaded_at = datetime.fromisoformat(data['uploadedAt'].replace('Z', '+00:00'))
        
        attachment.download_count = data.get('downloadCount', 0)
        attachment.metadata = data.get('metadata', {})
        
        return attachment
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'attachmentId': self.attachment_id,
            'fileName': self.file_name,
            'fileType': self.file_type,
            'fileSize': self.file_size,
            's3Key': self.s3_key,
            'uploadedAt': self.uploaded_at.isoformat(),
            'downloadCount': self.download_count,
            'metadata': self.metadata
        }
    
    def increment_download_count(self):
        """Increment download counter"""
        self.download_count += 1
    
    def get_file_size_mb(self) -> float:
        """Get file size in MB"""
        return self.file_size / (1024 * 1024)
    
    def is_image(self) -> bool:
        """Check if attachment is an image"""
        image_types = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
        return self.file_type.lower() in image_types
    
    def is_document(self) -> bool:
        """Check if attachment is a document"""
        doc_types = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt']
        return self.file_type.lower() in doc_types
    
    def is_audio(self) -> bool:
        """Check if attachment is audio"""
        audio_types = ['mp3', 'wav', 'ogg', 'm4a', 'aac']
        return self.file_type.lower() in audio_types
    
    def is_video(self) -> bool:
        """Check if attachment is video"""
        video_types = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']
        return self.file_type.lower() in video_types


class ConversationState:
    """Model for conversation state and metadata"""
    
    def __init__(self, conversation_id: str, tenant_id: str, agent_id: str,
                 participants: List[str], status: str = 'active'):
        self.conversation_id = conversation_id
        self.tenant_id = tenant_id
        self.agent_id = agent_id
        self.participants = participants
        self.status = status
        
        # Timestamps
        self.created_at = datetime.utcnow()
        self.updated_at = self.created_at
        self.last_message_at = self.created_at
        
        # Counters
        self.message_count = 0
        self.unread_count = 0
        
        # Metadata
        self.metadata = {}
        self.tags = []
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationState':
        """Create ConversationState from dictionary"""
        state = cls(
            conversation_id=data['conversationId'],
            tenant_id=data['tenantId'],
            agent_id=data['agentId'],
            participants=data['participants'],
            status=data.get('status', 'active')
        )
        
        # Set timestamps
        if 'createdAt' in data:
            state.created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
        if 'updatedAt' in data:
            state.updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
        if 'lastMessageAt' in data:
            state.last_message_at = datetime.fromisoformat(data['lastMessageAt'].replace('Z', '+00:00'))
        
        # Set counters
        state.message_count = data.get('messageCount', 0)
        state.unread_count = data.get('unreadCount', 0)
        
        # Set metadata
        state.metadata = data.get('metadata', {})
        state.tags = data.get('tags', [])
        
        return state
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'conversationId': self.conversation_id,
            'tenantId': self.tenant_id,
            'agentId': self.agent_id,
            'participants': self.participants,
            'status': self.status,
            'createdAt': self.created_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            'lastMessageAt': self.last_message_at.isoformat(),
            'messageCount': self.message_count,
            'unreadCount': self.unread_count,
            'metadata': self.metadata,
            'tags': self.tags
        }
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format"""
        # TTL: 1 year from creation
        ttl = int((self.created_at + timedelta(days=365)).timestamp())
        
        return {
            # Primary key
            'conversationId': self.conversation_id,
            
            # State data
            'tenantId': self.tenant_id,
            'agentId': self.agent_id,
            'participants': self.participants,
            'status': self.status,
            'messageCount': self.message_count,
            'unreadCount': self.unread_count,
            
            # Timestamps
            'createdAt': self.created_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            'lastMessageAt': self.last_message_at.isoformat(),
            
            # Metadata
            'metadata': self.metadata,
            'tags': self.tags,
            
            # DynamoDB specific
            'ttl': ttl,
            'EntityType': 'ConversationState'
        }
    
    def add_participant(self, user_id: str):
        """Add participant to conversation"""
        if user_id not in self.participants:
            self.participants.append(user_id)
            self.updated_at = datetime.utcnow()
    
    def remove_participant(self, user_id: str):
        """Remove participant from conversation"""
        if user_id in self.participants:
            self.participants.remove(user_id)
            self.updated_at = datetime.utcnow()
    
    def update_activity(self, message_count_increment: int = 1):
        """Update conversation activity"""
        self.message_count += message_count_increment
        self.last_message_at = datetime.utcnow()
        self.updated_at = self.last_message_at
    
    def update_status(self, status: str):
        """Update conversation status"""
        valid_statuses = ['active', 'closed', 'archived', 'paused']
        if status not in valid_statuses:
            raise ValueError(f"Invalid status. Must be one of: {valid_statuses}")
        
        self.status = status
        self.updated_at = datetime.utcnow()
    
    def is_participant(self, user_id: str) -> bool:
        """Check if user is participant"""
        return user_id in self.participants
    
    def get_duration(self) -> timedelta:
        """Get conversation duration"""
        return self.last_message_at - self.created_at
