# services/chat/src/models/user_presence.py
# User presence models for real-time status tracking

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import json

class UserPresence:
    """Model for user presence and online status"""
    
    def __init__(self, user_id: str, tenant_id: str, status: str = 'online'):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.status = status
        
        # Timestamps
        self.created_at = datetime.utcnow()
        self.updated_at = self.created_at
        self.last_seen = self.created_at
        
        # Connection tracking
        self.connection_count = 1 if status == 'online' else 0
        self.connection_ids = []
        
        # Activity tracking
        self.last_activity_type = 'connect'
        self.activity_metadata = {}
        
        # Metadata
        self.metadata = {}
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserPresence':
        """Create UserPresence from dictionary"""
        presence = cls(
            user_id=data['userId'],
            tenant_id=data['tenantId'],
            status=data.get('status', 'online')
        )
        
        # Set timestamps
        if 'createdAt' in data:
            presence.created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
        if 'updatedAt' in data:
            presence.updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
        if 'lastSeen' in data:
            presence.last_seen = datetime.fromisoformat(data['lastSeen'].replace('Z', '+00:00'))
        
        # Set connection data
        presence.connection_count = data.get('connectionCount', 0)
        presence.connection_ids = data.get('connectionIds', [])
        
        # Set activity data
        presence.last_activity_type = data.get('lastActivityType', 'connect')
        presence.activity_metadata = data.get('activityMetadata', {})
        
        # Set metadata
        presence.metadata = data.get('metadata', {})
        
        return presence
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'status': self.status,
            'createdAt': self.created_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            'lastSeen': self.last_seen.isoformat(),
            'connectionCount': self.connection_count,
            'connectionIds': self.connection_ids,
            'lastActivityType': self.last_activity_type,
            'activityMetadata': self.activity_metadata,
            'metadata': self.metadata
        }
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format"""
        # TTL: 7 days from last activity
        ttl = int((self.last_seen + timedelta(days=7)).timestamp())
        
        return {
            # Primary key
            'userId': self.user_id,
            
            # Presence data
            'tenantId': self.tenant_id,
            'status': self.status,
            'connectionCount': self.connection_count,
            'connectionIds': self.connection_ids,
            
            # Timestamps
            'createdAt': self.created_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            'lastSeen': self.last_seen.isoformat(),
            
            # Activity tracking
            'lastActivityType': self.last_activity_type,
            'activityMetadata': self.activity_metadata,
            
            # Metadata
            'metadata': self.metadata,
            
            # DynamoDB specific
            'ttl': ttl,
            'EntityType': 'UserPresence'
        }
    
    def update_status(self, status: str, activity_type: str = None, 
                     activity_metadata: Dict[str, Any] = None):
        """Update presence status"""
        valid_statuses = ['online', 'offline', 'away', 'busy']
        if status not in valid_statuses:
            raise ValueError(f"Invalid status. Must be one of: {valid_statuses}")
        
        self.status = status
        self.updated_at = datetime.utcnow()
        self.last_seen = self.updated_at
        
        if activity_type:
            self.last_activity_type = activity_type
        
        if activity_metadata:
            self.activity_metadata.update(activity_metadata)
    
    def add_connection(self, connection_id: str):
        """Add WebSocket connection"""
        if connection_id not in self.connection_ids:
            self.connection_ids.append(connection_id)
            self.connection_count = len(self.connection_ids)
            
            # Update to online if first connection
            if self.connection_count == 1:
                self.update_status('online', 'connect')
    
    def remove_connection(self, connection_id: str):
        """Remove WebSocket connection"""
        if connection_id in self.connection_ids:
            self.connection_ids.remove(connection_id)
            self.connection_count = len(self.connection_ids)
            
            # Update to offline if no connections
            if self.connection_count == 0:
                self.update_status('offline', 'disconnect')
    
    def is_online(self) -> bool:
        """Check if user is online"""
        return self.status == 'online' and self.connection_count > 0
    
    def is_stale(self, timeout_minutes: int = 5) -> bool:
        """Check if presence is stale"""
        timeout_delta = timedelta(minutes=timeout_minutes)
        return datetime.utcnow() - self.last_seen > timeout_delta
    
    def get_idle_time(self) -> timedelta:
        """Get time since last activity"""
        return datetime.utcnow() - self.last_seen
    
    def get_status_display(self) -> str:
        """Get human-readable status"""
        status_map = {
            'online': 'Online',
            'offline': 'Offline',
            'away': 'Away',
            'busy': 'Busy'
        }
        return status_map.get(self.status, 'Unknown')
    
    def __str__(self) -> str:
        return f"UserPresence(user={self.user_id}, status={self.status}, connections={self.connection_count})"
    
    def __repr__(self) -> str:
        return self.__str__()


class TypingIndicator:
    """Model for typing indicators in conversations"""
    
    def __init__(self, conversation_id: str, user_id: str, tenant_id: str, is_typing: bool = True):
        self.conversation_id = conversation_id
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.is_typing = is_typing
        
        # Timestamps
        self.started_at = datetime.utcnow()
        self.updated_at = self.started_at
        
        # Auto-expire after 30 seconds
        self.expires_at = self.started_at + timedelta(seconds=30)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TypingIndicator':
        """Create TypingIndicator from dictionary"""
        indicator = cls(
            conversation_id=data['conversationId'],
            user_id=data['userId'],
            tenant_id=data['tenantId'],
            is_typing=data.get('isTyping', True)
        )
        
        if 'startedAt' in data:
            indicator.started_at = datetime.fromisoformat(data['startedAt'].replace('Z', '+00:00'))
        if 'updatedAt' in data:
            indicator.updated_at = datetime.fromisoformat(data['updatedAt'].replace('Z', '+00:00'))
        if 'expiresAt' in data:
            indicator.expires_at = datetime.fromisoformat(data['expiresAt'].replace('Z', '+00:00'))
        
        return indicator
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'conversationId': self.conversation_id,
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'isTyping': self.is_typing,
            'startedAt': self.started_at.isoformat(),
            'updatedAt': self.updated_at.isoformat(),
            'expiresAt': self.expires_at.isoformat()
        }
    
    def update_typing(self, is_typing: bool):
        """Update typing status"""
        self.is_typing = is_typing
        self.updated_at = datetime.utcnow()
        
        if is_typing:
            # Extend expiration
            self.expires_at = self.updated_at + timedelta(seconds=30)
        else:
            # Stop typing immediately
            self.expires_at = self.updated_at
    
    def is_expired(self) -> bool:
        """Check if typing indicator has expired"""
        return datetime.utcnow() > self.expires_at
    
    def get_duration(self) -> timedelta:
        """Get typing duration"""
        return self.updated_at - self.started_at
    
    def __str__(self) -> str:
        return f"TypingIndicator(conversation={self.conversation_id}, user={self.user_id}, typing={self.is_typing})"
    
    def __repr__(self) -> str:
        return self.__str__()


class ActivityEvent:
    """Model for user activity events"""
    
    def __init__(self, user_id: str, tenant_id: str, activity_type: str, 
                 conversation_id: str = None, metadata: Dict[str, Any] = None):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.activity_type = activity_type
        self.conversation_id = conversation_id
        self.metadata = metadata or {}
        
        # Timestamp
        self.timestamp = datetime.utcnow()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ActivityEvent':
        """Create ActivityEvent from dictionary"""
        event = cls(
            user_id=data['userId'],
            tenant_id=data['tenantId'],
            activity_type=data['activityType'],
            conversation_id=data.get('conversationId'),
            metadata=data.get('metadata', {})
        )
        
        if 'timestamp' in data:
            event.timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
        
        return event
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'activityType': self.activity_type,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }
        
        if self.conversation_id:
            result['conversationId'] = self.conversation_id
        
        return result
    
    def is_recent(self, minutes: int = 5) -> bool:
        """Check if activity is recent"""
        return datetime.utcnow() - self.timestamp <= timedelta(minutes=minutes)
    
    def __str__(self) -> str:
        return f"ActivityEvent(user={self.user_id}, type={self.activity_type}, time={self.timestamp})"
    
    def __repr__(self) -> str:
        return self.__str__()
