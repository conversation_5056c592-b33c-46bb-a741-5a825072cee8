# services/chat/tests/test_agent_integration.py
# Tests for agent integration with chat system

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.handlers.agent_webhook import handler as webhook_handler
from src.handlers.agent_typing import (
    start_agent_typing_handler, stop_agent_typing_handler,
    get_agent_typing_status_handler, get_conversation_typing_handler
)
from src.services.agent_response_processor import AgentResponseProcessor
from src.services.agent_integration_service import AgentIntegrationService
from src.services.agent_typing_service import AgentTypingService

class TestAgentWebhookHandler:
    """Test suite for agent webhook handler"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.base_event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'POST',
            'path': '/chat/webhook/agent-response',
            'headers': {'X-N8N-Signature': 'sha256=test-signature'}
        }
        
        self.valid_webhook_payload = {
            'conversationId': 'conv-test-123',
            'agentId': 'agent-feedo',
            'agentName': 'Feedo',
            'agentType': 'feedo',
            'response': {
                'content': 'Based on your logistics data, I recommend optimizing route efficiency.',
                'type': 'text',
                'confidence': 0.95,
                'metadata': {
                    'processingTime': 2.5,
                    'dataSourcesUsed': ['inventory', 'routes']
                }
            },
            'requestId': 'n8n-req-123',
            'timestamp': datetime.utcnow().isoformat()
        }
    
    @patch('src.handlers.agent_webhook.validate_webhook_signature')
    @patch('src.handlers.agent_webhook.agent_response_processor')
    def test_webhook_handler_success(self, mock_processor, mock_validate):
        """Test successful webhook processing"""
        # Setup mocks
        mock_validate.return_value = True
        mock_processor.process_agent_response.return_value = (True, {
            'messageId': 'msg-123',
            'timestamp': datetime.utcnow().isoformat(),
            'processingTime': 2.5
        }, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps(self.valid_webhook_payload)
        }
        
        # Execute
        response = webhook_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['status'] == 'success'
        assert body['conversationId'] == 'conv-test-123'
        assert body['agentId'] == 'agent-feedo'
        assert 'messageId' in body
        
        # Verify processor call
        mock_processor.process_agent_response.assert_called_once()
    
    @patch('src.handlers.agent_webhook.validate_webhook_signature')
    def test_webhook_handler_invalid_signature(self, mock_validate):
        """Test webhook with invalid signature"""
        # Setup mocks
        mock_validate.return_value = False
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps(self.valid_webhook_payload)
        }
        
        # Execute
        response = webhook_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 401
        body = json.loads(response['body'])
        assert 'Invalid signature' in body['error']
    
    def test_webhook_handler_missing_fields(self):
        """Test webhook with missing required fields"""
        # Invalid payload missing conversationId
        invalid_payload = {
            'agentId': 'agent-feedo',
            'response': {'content': 'test'}
        }
        
        with patch('src.handlers.agent_webhook.validate_webhook_signature', return_value=True):
            event = {
                **self.base_event,
                'body': json.dumps(invalid_payload)
            }
            
            response = webhook_handler(event, None)
            
            assert response['statusCode'] == 400
            body = json.loads(response['body'])
            assert 'Missing required field: conversationId' in body['error']


class TestAgentResponseProcessor:
    """Test suite for AgentResponseProcessor"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.processor = AgentResponseProcessor()
        self.processor.max_content_length = 1000
        self.processor.supported_response_types = ['text', 'structured']
        self.processor.min_confidence_threshold = 0.1
    
    def test_validate_response_data_success(self):
        """Test successful response validation"""
        valid_response = {
            'content': 'Valid response content',
            'type': 'text',
            'confidence': 0.8,
            'metadata': {'key': 'value'}
        }
        
        is_valid, error_msg = self.processor._validate_response_data(valid_response)
        
        assert is_valid is True
        assert error_msg is None
    
    def test_validate_response_data_missing_content(self):
        """Test validation with missing content"""
        invalid_response = {
            'type': 'text',
            'confidence': 0.8
        }
        
        is_valid, error_msg = self.processor._validate_response_data(invalid_response)
        
        assert is_valid is False
        assert 'Missing required field: content' in error_msg
    
    def test_validate_response_data_invalid_confidence(self):
        """Test validation with invalid confidence"""
        invalid_response = {
            'content': 'Valid content',
            'confidence': 1.5  # Invalid: > 1
        }
        
        is_valid, error_msg = self.processor._validate_response_data(invalid_response)
        
        assert is_valid is False
        assert 'Confidence must be between 0 and 1' in error_msg
    
    def test_validate_response_data_unsupported_type(self):
        """Test validation with unsupported response type"""
        invalid_response = {
            'content': 'Valid content',
            'type': 'unsupported_type'
        }
        
        is_valid, error_msg = self.processor._validate_response_data(invalid_response)
        
        assert is_valid is False
        assert 'Unsupported response type' in error_msg


class TestAgentIntegrationService:
    """Test suite for AgentIntegrationService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = AgentIntegrationService()
        self.service.lambda_client = Mock()
    
    def test_determine_agent_type_forecaster(self):
        """Test agent type determination for forecaster keywords"""
        content = "Can you forecast demand for next quarter?"
        
        agent_type = self.service._determine_agent_type(content)
        
        assert agent_type == 'forecaster'
    
    def test_determine_agent_type_feedo_default(self):
        """Test agent type determination defaults to feedo"""
        content = "Optimize my delivery routes"
        
        agent_type = self.service._determine_agent_type(content)
        
        assert agent_type == 'feedo'
    
    def test_estimate_processing_time_simple(self):
        """Test processing time estimation for simple content"""
        content = "Hello"
        agent_type = 'feedo'
        
        estimated_time = self.service._estimate_processing_time(content, agent_type)
        
        assert 1.0 <= estimated_time <= 15.0
        assert estimated_time <= 4.0  # Should be relatively quick for simple content
    
    def test_estimate_processing_time_complex(self):
        """Test processing time estimation for complex content"""
        content = "Please analyze and forecast the demand patterns for our logistics network across multiple regions, including seasonal variations and market trends. Generate a comprehensive report with optimization recommendations."
        agent_type = 'forecaster'
        
        estimated_time = self.service._estimate_processing_time(content, agent_type)
        
        assert estimated_time > 5.0  # Should be longer for complex content
        assert estimated_time <= 15.0  # But capped at maximum


class TestAgentTypingService:
    """Test suite for AgentTypingService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.typing_service = AgentTypingService()
        self.typing_service.lambda_client = Mock()
        
        self.conversation_id = 'conv-test-123'
        self.agent_id = 'agent-feedo'
        self.agent_type = 'feedo'
        self.tenant_id = 'tenant-test-123'
    
    def test_calculate_typing_schedule(self):
        """Test typing schedule calculation"""
        pattern = {
            'initial_delay': 1.0,
            'typing_duration': 3.0,
            'pause_duration': 1.5,
            'bursts': 2,
            'variation': 0.5
        }
        
        schedule = self.typing_service._calculate_typing_schedule(pattern, 10.0)
        
        # Should have start/stop events for each burst plus final stop
        assert len(schedule) >= 3  # At least: start, stop, start, final_stop
        
        # First event should be start_typing after initial delay
        assert schedule[0]['action'] == 'start_typing'
        assert schedule[0]['time'] > 0  # After initial delay
        
        # Last event should be stop_typing
        assert schedule[-1]['action'] == 'stop_typing'
    
    def test_start_typing_simulation_success(self):
        """Test successful typing simulation start"""
        with patch.object(self.typing_service, '_execute_typing_simulation'):
            success, error_msg = self.typing_service.start_agent_typing_simulation(
                conversation_id=self.conversation_id,
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                tenant_id=self.tenant_id,
                estimated_processing_time=5.0
            )
            
            assert success is True
            assert error_msg is None
            
            # Check session was created
            session_key = f"{self.conversation_id}:{self.agent_id}"
            assert session_key in self.typing_service.active_sessions
    
    def test_stop_typing_simulation_success(self):
        """Test successful typing simulation stop"""
        # Setup active session
        session_key = f"{self.conversation_id}:{self.agent_id}"
        self.typing_service.active_sessions[session_key] = {
            'conversation_id': self.conversation_id,
            'agent_id': self.agent_id,
            'active': True,
            'started_at': datetime.utcnow()
        }
        
        with patch.object(self.typing_service, '_send_typing_indicator'):
            success, error_msg = self.typing_service.stop_agent_typing_simulation(
                conversation_id=self.conversation_id,
                agent_id=self.agent_id,
                tenant_id=self.tenant_id
            )
            
            assert success is True
            assert error_msg is None
            
            # Check session was removed
            assert session_key not in self.typing_service.active_sessions
    
    def test_get_typing_stats(self):
        """Test typing statistics retrieval"""
        # Setup some active sessions
        self.typing_service.active_sessions = {
            'conv1:agent1': {
                'agent_type': 'feedo',
                'started_at': datetime.utcnow()
            },
            'conv2:agent2': {
                'agent_type': 'forecaster',
                'started_at': datetime.utcnow()
            }
        }
        
        stats = self.typing_service.get_typing_stats()
        
        assert stats['active_sessions'] == 2
        assert 'feedo' in stats['sessions_by_agent_type']
        assert 'forecaster' in stats['sessions_by_agent_type']
        assert 'timestamp' in stats


class TestAgentTypingHandlers:
    """Test suite for agent typing handlers"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_user_context = {
            'user_id': 'test-user-123',
            'tenant_id': 'test-tenant-123',
            'email': '<EMAIL>'
        }
        
        self.base_event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'POST',
            'path': '/chat/agent-typing/start'
        }
    
    @patch('src.handlers.agent_typing.extract_user_context')
    @patch('src.handlers.agent_typing.agent_typing_service')
    def test_start_agent_typing_handler_success(self, mock_service, mock_auth):
        """Test successful start typing handler"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_service.start_agent_typing_simulation.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': 'conv-123',
                'agentId': 'agent-feedo',
                'agentType': 'feedo',
                'estimatedProcessingTime': 5.0
            })
        }
        
        # Execute
        response = start_agent_typing_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['status'] == 'started'
        assert body['conversationId'] == 'conv-123'
        assert body['agentId'] == 'agent-feedo'
        
        # Verify service call
        mock_service.start_agent_typing_simulation.assert_called_once()
    
    @patch('src.handlers.agent_typing.extract_user_context')
    @patch('src.handlers.agent_typing.agent_typing_service')
    def test_stop_agent_typing_handler_success(self, mock_service, mock_auth):
        """Test successful stop typing handler"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_service.stop_agent_typing_simulation.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'path': '/chat/agent-typing/stop',
            'body': json.dumps({
                'conversationId': 'conv-123',
                'agentId': 'agent-feedo'
            })
        }
        
        # Execute
        response = stop_agent_typing_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['status'] == 'stopped'
        assert body['conversationId'] == 'conv-123'
        
        # Verify service call
        mock_service.stop_agent_typing_simulation.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
