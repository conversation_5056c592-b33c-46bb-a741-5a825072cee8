# services/chat/tests/test_search_and_analytics.py
# Tests for search and analytics functionality

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.services.search_service import SearchService
from src.services.analytics_service import AnalyticsService
from src.services.search_indexing_service import SearchIndexingService
from src.handlers.search import (
    search_messages_handler, search_files_handler,
    search_combined_handler, search_suggestions_handler
)
from src.handlers.analytics import (
    get_tenant_metrics_handler, get_real_time_stats_handler,
    get_usage_analytics_handler, track_event_handler
)
from src.handlers.search_indexing import (
    index_message_handler, index_file_handler,
    batch_index_handler, create_indices_handler
)

class TestSearchService:
    """Test suite for SearchService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = SearchService()
        self.service.search_client = Mock()
        
        self.tenant_id = 'tenant-test-123'
        self.user_id = 'user-test-123'
        self.query = 'test search query'
    
    def test_search_messages_success(self):
        """Test successful message search"""
        # Mock search response
        mock_response = {
            'hits': {
                'total': {'value': 5},
                'hits': [
                    {
                        '_source': {
                            'messageId': 'msg-1',
                            'content': 'test message content',
                            'userId': self.user_id,
                            'tenantId': self.tenant_id
                        },
                        '_score': 1.5,
                        'highlight': {'content': ['<em>test</em> message content']}
                    }
                ]
            },
            'took': 15
        }
        self.service.search_client.search.return_value = mock_response
        
        # Execute search
        success, results, error_msg = self.service.search_messages(
            query=self.query,
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            filters={},
            page=1,
            page_size=20
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert results['total'] == 5
        assert len(results['messages']) == 1
        assert results['messages'][0]['messageId'] == 'msg-1'
        assert results['messages'][0]['searchScore'] == 1.5
        assert 'highlights' in results['messages'][0]
    
    def test_search_files_success(self):
        """Test successful file search"""
        # Mock search response
        mock_response = {
            'hits': {
                'total': {'value': 3},
                'hits': [
                    {
                        '_source': {
                            'fileId': 'file-1',
                            'filename': 'test-document.pdf',
                            'fileType': 'document',
                            'tenantId': self.tenant_id
                        },
                        '_score': 2.0
                    }
                ]
            },
            'took': 10
        }
        self.service.search_client.search.return_value = mock_response
        
        # Execute search
        success, results, error_msg = self.service.search_files(
            query=self.query,
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            filters={'file_type': 'document'},
            page=1,
            page_size=20
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert results['total'] == 3
        assert len(results['files']) == 1
        assert results['files'][0]['fileId'] == 'file-1'
        assert results['files'][0]['searchScore'] == 2.0
    
    def test_search_combined_success(self):
        """Test successful combined search"""
        with patch.object(self.service, 'search_messages') as mock_search_messages:
            with patch.object(self.service, 'search_files') as mock_search_files:
                # Mock individual search results
                mock_search_messages.return_value = (True, {
                    'messages': [{'messageId': 'msg-1'}],
                    'total': 2,
                    'has_more': False
                }, None)
                
                mock_search_files.return_value = (True, {
                    'files': [{'fileId': 'file-1'}],
                    'total': 1,
                    'has_more': False
                }, None)
                
                # Execute combined search
                success, results, error_msg = self.service.search_combined(
                    query=self.query,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id,
                    filters={},
                    page=1,
                    page_size=20
                )
                
                # Assertions
                assert success is True
                assert error_msg is None
                assert results['total']['messages'] == 2
                assert results['total']['files'] == 1
                assert results['total']['combined'] == 3
                assert len(results['messages']) == 1
                assert len(results['files']) == 1
    
    def test_get_search_suggestions_success(self):
        """Test search suggestions generation"""
        success, suggestions, error_msg = self.service.get_search_suggestions(
            partial_query='test',
            tenant_id=self.tenant_id,
            suggestion_type='all'
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
    
    def test_search_client_unavailable(self):
        """Test behavior when search client is unavailable"""
        self.service.search_client = None
        
        success, results, error_msg = self.service.search_messages(
            query=self.query,
            tenant_id=self.tenant_id,
            user_id=self.user_id
        )
        
        assert success is False
        assert 'Search service not available' in error_msg


class TestAnalyticsService:
    """Test suite for AnalyticsService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = AnalyticsService()
        self.service.cloudwatch = Mock()
        
        self.tenant_id = 'tenant-test-123'
        self.user_id = 'user-test-123'
    
    def test_track_message_sent_success(self):
        """Test successful message tracking"""
        success = self.service.track_message_sent(
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            conversation_id='conv-123',
            message_type='text',
            agent_involved=True
        )
        
        assert success is True
        # Verify CloudWatch metrics were sent
        self.service.cloudwatch.put_metric_data.assert_called_once()
    
    def test_track_file_shared_success(self):
        """Test successful file sharing tracking"""
        success = self.service.track_file_shared(
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            conversation_id='conv-123',
            file_type='document',
            file_size=1024000
        )
        
        assert success is True
        self.service.cloudwatch.put_metric_data.assert_called_once()
    
    def test_track_search_query_success(self):
        """Test successful search query tracking"""
        success = self.service.track_search_query(
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            query='test query',
            search_type='messages',
            results_count=5,
            search_time_ms=150
        )
        
        assert success is True
        self.service.cloudwatch.put_metric_data.assert_called_once()
    
    def test_get_tenant_metrics_success(self):
        """Test successful tenant metrics retrieval"""
        # Mock CloudWatch response
        mock_datapoints = [
            {'Timestamp': datetime.utcnow(), 'Sum': 10.0},
            {'Timestamp': datetime.utcnow(), 'Sum': 15.0}
        ]
        self.service.cloudwatch.get_metric_statistics.return_value = {
            'Datapoints': mock_datapoints
        }
        
        start_date = datetime.utcnow() - timedelta(days=7)
        end_date = datetime.utcnow()
        
        success, results, error_msg = self.service.get_tenant_metrics(
            tenant_id=self.tenant_id,
            start_date=start_date,
            end_date=end_date,
            metric_types=['MessagesSent']
        )
        
        assert success is True
        assert error_msg is None
        assert 'metrics' in results
        assert 'summary' in results
        assert results['tenant_id'] == self.tenant_id
    
    def test_get_real_time_stats_success(self):
        """Test successful real-time stats retrieval"""
        # Mock CloudWatch response
        mock_datapoints = [
            {'Timestamp': datetime.utcnow(), 'Sum': 5.0}
        ]
        self.service.cloudwatch.get_metric_statistics.return_value = {
            'Datapoints': mock_datapoints
        }
        
        success, results, error_msg = self.service.get_real_time_stats(self.tenant_id)
        
        assert success is True
        assert error_msg is None
        assert 'real_time_stats' in results
        assert results['tenant_id'] == self.tenant_id
    
    def test_get_usage_analytics_success(self):
        """Test successful usage analytics retrieval"""
        success, results, error_msg = self.service.get_usage_analytics(
            tenant_id=self.tenant_id,
            period_days=30
        )
        
        assert success is True
        assert error_msg is None
        assert 'analytics' in results
        assert 'insights' in results
        assert results['tenant_id'] == self.tenant_id


class TestSearchIndexingService:
    """Test suite for SearchIndexingService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = SearchIndexingService()
        self.service.search_client = Mock()
        
        self.message_data = {
            'messageId': 'msg-test-123',
            'conversationId': 'conv-test-123',
            'userId': 'user-test-123',
            'tenantId': 'tenant-test-123',
            'content': 'Test message content',
            'type': 'text',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        self.file_data = {
            'fileId': 'file-test-123',
            'filename': 'test-document.pdf',
            'fileSize': 1024000,
            'contentType': 'application/pdf',
            'tenantId': 'tenant-test-123',
            'uploadedBy': 'user-test-123'
        }
    
    def test_index_message_success(self):
        """Test successful message indexing"""
        # Mock search client response
        self.service.search_client.index.return_value = {'result': 'created'}
        
        success, error_msg = self.service.index_message(self.message_data)
        
        assert success is True
        assert error_msg is None
        self.service.search_client.index.assert_called_once()
    
    def test_index_file_success(self):
        """Test successful file indexing"""
        # Mock search client response
        self.service.search_client.index.return_value = {'result': 'created'}
        
        success, error_msg = self.service.index_file(self.file_data)
        
        assert success is True
        assert error_msg is None
        self.service.search_client.index.assert_called_once()
    
    def test_batch_index_messages_success(self):
        """Test successful batch message indexing"""
        messages = [self.message_data, {**self.message_data, 'messageId': 'msg-2'}]
        
        # Mock bulk response
        mock_response = {
            'errors': False,
            'items': [
                {'index': {'status': 201}},
                {'index': {'status': 201}}
            ]
        }
        self.service.search_client.bulk.return_value = mock_response
        
        success, result, error_msg = self.service.batch_index_messages(messages)
        
        assert success is True
        assert error_msg is None
        assert result['indexed'] == 2
        assert result['failed'] == 0
    
    def test_batch_index_files_success(self):
        """Test successful batch file indexing"""
        files = [self.file_data, {**self.file_data, 'fileId': 'file-2'}]
        
        # Mock bulk response
        mock_response = {
            'errors': False,
            'items': [
                {'index': {'status': 201}},
                {'index': {'status': 201}}
            ]
        }
        self.service.search_client.bulk.return_value = mock_response
        
        success, result, error_msg = self.service.batch_index_files(files)
        
        assert success is True
        assert error_msg is None
        assert result['indexed'] == 2
        assert result['failed'] == 0
    
    def test_delete_message_from_index_success(self):
        """Test successful message deletion from index"""
        self.service.search_client.delete.return_value = {'result': 'deleted'}
        
        success, error_msg = self.service.delete_message_from_index('msg-123')
        
        assert success is True
        assert error_msg is None
        self.service.search_client.delete.assert_called_once()
    
    def test_create_indices_success(self):
        """Test successful index creation"""
        # Mock indices operations
        self.service.search_client.indices.exists.return_value = False
        self.service.search_client.indices.create.return_value = {'acknowledged': True}
        
        success, error_msg = self.service.create_indices()
        
        assert success is True
        assert error_msg is None
        # Should create both message and file indices
        assert self.service.search_client.indices.create.call_count == 2
    
    def test_indexing_disabled(self):
        """Test behavior when indexing is disabled"""
        self.service.enabled = False
        
        success, error_msg = self.service.index_message(self.message_data)
        
        assert success is True
        assert error_msg is None
        # Should not call search client when disabled
        self.service.search_client.index.assert_not_called()


class TestSearchHandlers:
    """Test suite for search handlers"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_user_context = {
            'user_id': 'test-user-123',
            'tenant_id': 'test-tenant-123',
            'email': '<EMAIL>'
        }
        
        self.base_event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'GET',
            'path': '/chat/search/messages'
        }
    
    @patch('src.handlers.search.extract_user_context')
    @patch('src.handlers.search.search_service')
    def test_search_messages_handler_success(self, mock_service, mock_auth):
        """Test successful message search handler"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_service.search_messages.return_value = (True, {
            'messages': [{'messageId': 'msg-1'}],
            'total': 1,
            'has_more': False,
            'search_time': 15
        }, None)
        
        # Test event
        event = {
            **self.base_event,
            'queryStringParameters': {
                'q': 'test query',
                'page': '1',
                'size': '20'
            }
        }
        
        # Execute
        response = search_messages_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'messages' in body
        assert 'search_metadata' in body
        
        # Verify service call
        mock_service.search_messages.assert_called_once()
    
    @patch('src.handlers.search.extract_user_context')
    @patch('src.handlers.search.search_service')
    def test_search_files_handler_success(self, mock_service, mock_auth):
        """Test successful file search handler"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_service.search_files.return_value = (True, {
            'files': [{'fileId': 'file-1'}],
            'total': 1,
            'has_more': False,
            'search_time': 10
        }, None)
        
        # Test event
        event = {
            **self.base_event,
            'path': '/chat/search/files',
            'queryStringParameters': {
                'q': 'test',
                'file_type': 'document'
            }
        }
        
        # Execute
        response = search_files_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'files' in body
        assert 'search_metadata' in body
    
    @patch('src.handlers.search.extract_user_context')
    @patch('src.handlers.search.search_service')
    def test_search_suggestions_handler_success(self, mock_service, mock_auth):
        """Test successful search suggestions handler"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_service.get_search_suggestions.return_value = (True, [
            'test suggestion 1',
            'test suggestion 2'
        ], None)
        
        # Test event
        event = {
            **self.base_event,
            'path': '/chat/search/suggestions',
            'queryStringParameters': {
                'q': 'te',
                'type': 'all'
            }
        }
        
        # Execute
        response = search_suggestions_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'suggestions' in body
        assert len(body['suggestions']) == 2


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
