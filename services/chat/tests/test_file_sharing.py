# services/chat/tests/test_file_sharing.py
# Tests for file sharing functionality

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.handlers.file_sharing import (
    request_upload_url_handler, confirm_upload_handler,
    get_file_info_handler, delete_file_handler, list_conversation_files_handler
)
from src.handlers.file_preview import (
    generate_preview_handler, get_download_url_handler,
    get_file_metadata_handler, batch_generate_previews_handler
)
from src.services.file_upload_service import FileUploadService
from src.services.file_preview_service import FilePreviewService
from src.services.file_notification_service import FileNotificationService

class TestFileUploadService:
    """Test suite for FileUploadService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = FileUploadService()
        self.service.s3_client = Mock()
        self.service.bucket_name = 'test-bucket'
        self.service.max_file_size = 50 * 1024 * 1024  # 50MB
        
        self.user_id = 'user-test-123'
        self.tenant_id = 'tenant-test-123'
        self.filename = 'test-document.pdf'
        self.content_type = 'application/pdf'
        self.file_size = 1024000  # 1MB
    
    def test_validate_file_upload_success(self):
        """Test successful file validation"""
        is_valid, error_msg = self.service._validate_file_upload(
            filename=self.filename,
            content_type=self.content_type,
            file_size=self.file_size
        )
        
        assert is_valid is True
        assert error_msg is None
    
    def test_validate_file_upload_too_large(self):
        """Test file validation with oversized file"""
        large_file_size = 100 * 1024 * 1024  # 100MB
        
        is_valid, error_msg = self.service._validate_file_upload(
            filename=self.filename,
            content_type=self.content_type,
            file_size=large_file_size
        )
        
        assert is_valid is False
        assert 'File too large' in error_msg
    
    def test_validate_file_upload_invalid_extension(self):
        """Test file validation with invalid extension"""
        invalid_filename = 'malicious.exe'
        
        is_valid, error_msg = self.service._validate_file_upload(
            filename=invalid_filename,
            content_type='application/octet-stream',
            file_size=self.file_size
        )
        
        assert is_valid is False
        assert 'File type not allowed' in error_msg
    
    def test_generate_presigned_upload_url_success(self):
        """Test successful presigned URL generation"""
        # Mock S3 response
        mock_presigned_data = {
            'url': 'https://test-bucket.s3.amazonaws.com/',
            'fields': {
                'key': 'chat-files/tenant-test-123/user-test-123/file-123.pdf',
                'Content-Type': self.content_type
            }
        }
        self.service.s3_client.generate_presigned_post.return_value = mock_presigned_data
        
        # Execute
        success, upload_data, error_msg = self.service.generate_presigned_upload_url(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            filename=self.filename,
            content_type=self.content_type,
            file_size=self.file_size
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert 'fileId' in upload_data
        assert 'uploadUrl' in upload_data
        assert 'formData' in upload_data
        assert upload_data['maxFileSize'] == self.service.max_file_size
    
    def test_confirm_file_upload_success(self):
        """Test successful file upload confirmation"""
        file_id = 'file-test-123'
        
        # Mock S3 head_object response
        mock_head_response = {
            'ContentLength': self.file_size,
            'ContentType': self.content_type,
            'LastModified': datetime.utcnow()
        }
        self.service.s3_client.head_object.return_value = mock_head_response
        
        # Mock file record storage
        with patch.object(self.service, '_store_file_record', return_value=(True, None)):
            # Execute
            success, file_data, error_msg = self.service.confirm_file_upload(
                file_id=file_id,
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                filename=self.filename,
                conversation_id='conv-123'
            )
            
            # Assertions
            assert success is True
            assert error_msg is None
            assert file_data['fileId'] == file_id
            assert file_data['filename'] == self.filename
            assert file_data['fileSize'] == self.file_size
            assert file_data['uploadedBy'] == self.user_id
    
    def test_confirm_file_upload_file_not_found(self):
        """Test file upload confirmation when file not found in S3"""
        file_id = 'file-not-found'
        
        # Mock S3 NoSuchKey exception
        from botocore.exceptions import ClientError
        self.service.s3_client.head_object.side_effect = ClientError(
            {'Error': {'Code': 'NoSuchKey'}}, 'HeadObject'
        )
        
        # Execute
        success, file_data, error_msg = self.service.confirm_file_upload(
            file_id=file_id,
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            filename=self.filename
        )
        
        # Assertions
        assert success is False
        assert 'File not found in storage' in error_msg
    
    def test_delete_file_success(self):
        """Test successful file deletion"""
        file_id = 'file-to-delete'
        
        # Mock file record
        mock_file_data = {
            'fileId': file_id,
            'filename': self.filename,
            'fileKey': f'chat-files/{self.tenant_id}/{self.user_id}/{file_id}.pdf',
            'uploadedBy': self.user_id,
            'tenantId': self.tenant_id
        }
        
        with patch.object(self.service, '_get_file_record', return_value=mock_file_data):
            with patch.object(self.service, '_mark_file_deleted', return_value=(True, None)):
                # Execute
                success, error_msg = self.service.delete_file(
                    file_id=file_id,
                    user_id=self.user_id,
                    tenant_id=self.tenant_id
                )
                
                # Assertions
                assert success is True
                assert error_msg is None
                
                # Verify S3 delete was called
                self.service.s3_client.delete_object.assert_called_once()
    
    def test_delete_file_permission_denied(self):
        """Test file deletion with permission denied"""
        file_id = 'file-not-owned'
        
        # Mock file record owned by different user
        mock_file_data = {
            'fileId': file_id,
            'uploadedBy': 'other-user',
            'tenantId': self.tenant_id
        }
        
        with patch.object(self.service, '_get_file_record', return_value=mock_file_data):
            # Execute
            success, error_msg = self.service.delete_file(
                file_id=file_id,
                user_id=self.user_id,
                tenant_id=self.tenant_id
            )
            
            # Assertions
            assert success is False
            assert 'Only file owner can delete' in error_msg


class TestFilePreviewService:
    """Test suite for FilePreviewService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = FilePreviewService()
        self.service.s3_client = Mock()
        self.service.bucket_name = 'test-bucket'
        
        self.file_id = 'file-preview-123'
        self.file_key = 'chat-files/tenant/user/file-123.jpg'
        self.content_type = 'image/jpeg'
    
    def test_get_file_extension(self):
        """Test file extension extraction"""
        assert self.service._get_file_extension('document.pdf') == '.pdf'
        assert self.service._get_file_extension('image.JPG') == '.jpg'
        assert self.service._get_file_extension('no-extension') == ''
    
    def test_determine_file_type(self):
        """Test file type determination"""
        assert self.service._determine_file_type('image/jpeg') == 'image'
        assert self.service._determine_file_type('video/mp4') == 'video'
        assert self.service._determine_file_type('audio/mp3') == 'audio'
        assert self.service._determine_file_type('application/pdf') == 'document'
        assert self.service._determine_file_type('application/zip') == 'archive'
        assert self.service._determine_file_type('unknown/type') == 'other'
    
    def test_get_file_metadata_success(self):
        """Test successful file metadata retrieval"""
        # Mock S3 head_object response
        mock_response = {
            'ContentLength': 1024000,
            'ContentType': 'image/jpeg',
            'LastModified': datetime.utcnow(),
            'ETag': '"abc123"',
            'Metadata': {'custom': 'value'},
            'StorageClass': 'STANDARD'
        }
        self.service.s3_client.head_object.return_value = mock_response
        
        # Execute
        success, metadata, error_msg = self.service.get_file_metadata(self.file_key)
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert metadata['fileSize'] == 1024000
        assert metadata['contentType'] == 'image/jpeg'
        assert metadata['etag'] == 'abc123'
    
    def test_generate_secure_download_url_success(self):
        """Test secure download URL generation"""
        filename = 'test-file.pdf'
        user_id = 'user-123'
        tenant_id = 'tenant-123'
        
        # Mock presigned URL
        mock_url = 'https://test-bucket.s3.amazonaws.com/signed-url'
        self.service.s3_client.generate_presigned_url.return_value = mock_url
        
        # Execute
        success, download_url, error_msg = self.service.generate_secure_download_url(
            file_key=self.file_key,
            filename=filename,
            user_id=user_id,
            tenant_id=tenant_id,
            expires_in=3600
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert download_url == mock_url
        
        # Verify S3 call
        self.service.s3_client.generate_presigned_url.assert_called_once_with(
            'get_object',
            Params={
                'Bucket': self.service.bucket_name,
                'Key': self.file_key,
                'ResponseContentDisposition': f'attachment; filename="{filename}"',
                'ResponseCacheControl': 'no-cache'
            },
            ExpiresIn=3600
        )


class TestFileNotificationService:
    """Test suite for FileNotificationService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = FileNotificationService()
        self.service.lambda_client = Mock()
        
        self.user_id = 'user-test-123'
        self.tenant_id = 'tenant-test-123'
        self.conversation_id = 'conv-test-123'
        self.file_info = {
            'fileId': 'file-123',
            'filename': 'test.pdf',
            'fileSize': 1024000,
            'contentType': 'application/pdf'
        }
    
    def test_notify_file_upload_completed_success(self):
        """Test successful upload completion notification"""
        with patch.object(self.service, '_send_to_conversation', return_value=(True, None)):
            # Execute
            success, error_msg = self.service.notify_file_upload_completed(
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                conversation_id=self.conversation_id,
                file_data=self.file_info,
                message_id='msg-123'
            )
            
            # Assertions
            assert success is True
            assert error_msg is None
    
    def test_notify_file_upload_failed(self):
        """Test upload failure notification"""
        error_reason = 'File too large'
        
        with patch.object(self.service, '_send_to_user', return_value=(True, None)):
            # Execute
            success, error_msg = self.service.notify_file_upload_failed(
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                conversation_id=self.conversation_id,
                file_info=self.file_info,
                error_reason=error_reason
            )
            
            # Assertions
            assert success is True
            assert error_msg is None
    
    def test_notify_file_shared(self):
        """Test file sharing notification"""
        shared_with = ['user-456', 'user-789']
        
        with patch.object(self.service, '_send_to_user', return_value=(True, None)):
            # Execute
            success, error_msg = self.service.notify_file_shared(
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                conversation_id=self.conversation_id,
                file_data=self.file_info,
                shared_with=shared_with
            )
            
            # Assertions
            assert success is True
            assert error_msg is None
    
    def test_notify_file_deleted(self):
        """Test file deletion notification"""
        with patch.object(self.service, '_send_to_conversation', return_value=(True, None)):
            # Execute
            success, error_msg = self.service.notify_file_deleted(
                user_id=self.user_id,
                tenant_id=self.tenant_id,
                conversation_id=self.conversation_id,
                file_info=self.file_info
            )
            
            # Assertions
            assert success is True
            assert error_msg is None


class TestFileSharingHandlers:
    """Test suite for file sharing handlers"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_user_context = {
            'user_id': 'test-user-123',
            'tenant_id': 'test-tenant-123',
            'email': '<EMAIL>'
        }
        
        self.base_event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'POST',
            'path': '/chat/files/upload-url'
        }
    
    @patch('src.handlers.file_sharing.extract_user_context')
    @patch('src.handlers.file_sharing.file_upload_service')
    def test_request_upload_url_handler_success(self, mock_service, mock_auth):
        """Test successful upload URL request"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_service.generate_presigned_upload_url.return_value = (True, {
            'fileId': 'file-123',
            'uploadUrl': 'https://example.com/upload',
            'formData': {'key': 'value'}
        }, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'filename': 'test.pdf',
                'contentType': 'application/pdf',
                'fileSize': 1024000,
                'conversationId': 'conv-123'
            })
        }
        
        # Execute
        response = request_upload_url_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert 'fileId' in body
        assert 'uploadUrl' in body
        assert body['conversationId'] == 'conv-123'
        
        # Verify service call
        mock_service.generate_presigned_upload_url.assert_called_once()
    
    @patch('src.handlers.file_sharing.extract_user_context')
    @patch('src.handlers.file_sharing.file_upload_service')
    @patch('src.handlers.file_sharing.message_service')
    def test_confirm_upload_handler_success(self, mock_message_service, mock_file_service, mock_auth):
        """Test successful upload confirmation"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_file_service.confirm_file_upload.return_value = (True, {
            'fileId': 'file-123',
            'filename': 'test.pdf',
            'fileSize': 1024000,
            'downloadUrl': 'https://example.com/download'
        }, None)
        mock_message_service.send_message.return_value = (True, {'messageId': 'msg-123'}, None)
        
        # Test event
        event = {
            **self.base_event,
            'path': '/chat/files/confirm-upload',
            'body': json.dumps({
                'fileId': 'file-123',
                'filename': 'test.pdf',
                'conversationId': 'conv-123',
                'messageContent': 'Here is the file'
            })
        }
        
        # Execute
        response = confirm_upload_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['fileId'] == 'file-123'
        assert body['uploadConfirmed'] is True
        assert body['messageSent'] is True
        
        # Verify service calls
        mock_file_service.confirm_file_upload.assert_called_once()
        mock_message_service.send_message.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
