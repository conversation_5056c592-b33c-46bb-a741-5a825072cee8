# services/chat/tests/conftest.py
# Pytest configuration and fixtures for Chat Service tests

import pytest
import os
import sys
from unittest.mock import MagicMock, <PERSON>ck
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Mock shared modules before any imports
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Configure mock responses
mock_logger = MagicMock()
mock_logger.info = Mock()
mock_logger.error = Mock()
mock_logger.debug = Mock()
mock_logger.warning = Mock()
sys.modules['shared.logger'].lambda_logger = mock_logger

mock_api_response = MagicMock()
mock_api_response.success = Mock(return_value={'statusCode': 200, 'body': '{}'})
mock_api_response.error = Mock(return_value={'statusCode': 400, 'body': '{"error": "test error"}'})
sys.modules['shared.responses'].APIResponse = mock_api_response

mock_auth = MagicMock()
mock_auth.extract_user_context = Mock(return_value={
    'user_id': 'test-user',
    'tenant_id': 'test-tenant',
    'email': '<EMAIL>'
})
sys.modules['shared.auth'].extract_user_context = mock_auth.extract_user_context

mock_db_client = MagicMock()
sys.modules['shared.database'].DynamoDBClient = mock_db_client

@pytest.fixture
def mock_user_context():
    """Mock user context from JWT"""
    return {
        'user_id': 'test-user-123',
        'tenant_id': 'test-tenant-123',
        'email': '<EMAIL>',
        'role': 'USER'
    }

@pytest.fixture
def mock_conversation_id():
    """Mock conversation ID"""
    return 'conv-test-123'

@pytest.fixture
def mock_message_id():
    """Mock message ID"""
    return 'msg-test-123'

@pytest.fixture
def mock_message_data():
    """Mock message data"""
    return {
        'messageId': 'msg-test-123',
        'conversationId': 'conv-test-123',
        'userId': 'test-user-123',
        'tenantId': 'test-tenant-123',
        'content': 'Test message content',
        'type': 'text',
        'status': 'sent',
        'timestamp': datetime.utcnow().isoformat(),
        'createdAt': datetime.utcnow().isoformat(),
        'updatedAt': datetime.utcnow().isoformat(),
        'attachments': [],
        'metadata': {},
        'isEdited': False,
        'isDeleted': False
    }

@pytest.fixture
def mock_conversation_data():
    """Mock conversation data"""
    return {
        'conversationId': 'conv-test-123',
        'tenantId': 'test-tenant-123',
        'agentId': 'agent-test-123',
        'participants': ['test-user-123'],
        'status': 'active',
        'messageCount': 5,
        'createdAt': datetime.utcnow().isoformat(),
        'updatedAt': datetime.utcnow().isoformat(),
        'lastMessageAt': datetime.utcnow().isoformat()
    }

@pytest.fixture
def mock_http_event():
    """Mock HTTP event structure"""
    def _create_event(method='POST', path='/chat/messages', body=None, path_params=None, query_params=None):
        event = {
            'requestContext': {
                'requestId': 'test-request-123',
                'authorizer': {
                    'userId': 'test-user-123',
                    'tenantId': 'test-tenant-123',
                    'email': '<EMAIL>'
                }
            },
            'httpMethod': method,
            'path': path,
            'headers': {
                'Authorization': 'Bearer test-token',
                'Content-Type': 'application/json'
            },
            'pathParameters': path_params or {},
            'queryStringParameters': query_params or {}
        }
        
        if body:
            event['body'] = body if isinstance(body, str) else str(body)
        
        return event
    
    return _create_event

@pytest.fixture
def mock_dynamodb_client():
    """Mock DynamoDB client"""
    client = Mock()
    client.put_item = Mock()
    client.get_item = Mock()
    client.query = Mock()
    client.query_gsi = Mock()
    client.update_item = Mock()
    client.delete_item = Mock()
    return client

@pytest.fixture
def mock_lambda_client():
    """Mock Lambda client for function invocations"""
    client = Mock()
    client.invoke = Mock()
    return client

@pytest.fixture
def mock_s3_client():
    """Mock S3 client"""
    client = Mock()
    client.generate_presigned_url = Mock()
    client.put_object = Mock()
    client.get_object = Mock()
    return client

@pytest.fixture
def sample_messages():
    """Sample message list for testing"""
    return [
        {
            'messageId': 'msg-1',
            'conversationId': 'conv-test-123',
            'userId': 'test-user-123',
            'content': 'Hello, how can I help you?',
            'type': 'text',
            'status': 'sent',
            'timestamp': '2024-01-01T10:00:00Z'
        },
        {
            'messageId': 'msg-2',
            'conversationId': 'conv-test-123',
            'userId': 'agent-123',
            'content': 'I need help with logistics planning',
            'type': 'text',
            'status': 'delivered',
            'timestamp': '2024-01-01T10:01:00Z'
        },
        {
            'messageId': 'msg-3',
            'conversationId': 'conv-test-123',
            'userId': 'test-user-123',
            'content': 'Can you analyze this data?',
            'type': 'text',
            'status': 'read',
            'timestamp': '2024-01-01T10:02:00Z'
        }
    ]

@pytest.fixture
def mock_websocket_connection():
    """Mock WebSocket connection"""
    connection = Mock()
    connection.connection_id = 'conn-test-123'
    connection.user_id = 'test-user-123'
    connection.tenant_id = 'test-tenant-123'
    connection.connected_at = datetime.utcnow().isoformat()
    return connection

@pytest.fixture(autouse=True)
def setup_environment():
    """Setup test environment variables"""
    os.environ.update({
        'ENVIRONMENT': 'test',
        'REGION': 'us-east-1',
        'DYNAMODB_TABLE': 'test-main-table',
        'CHAT_MESSAGES_TABLE': 'test-chat-messages',
        'CONVERSATION_STATES_TABLE': 'test-conversation-states',
        'USER_PRESENCE_TABLE': 'test-user-presence',
        'S3_BUCKET': 'test-chat-files',
        'WEBSOCKET_API_ENDPOINT': 'wss://test.execute-api.us-east-1.amazonaws.com/test'
    })
    
    yield
    
    # Cleanup
    for key in ['ENVIRONMENT', 'REGION', 'DYNAMODB_TABLE', 'CHAT_MESSAGES_TABLE', 
                'CONVERSATION_STATES_TABLE', 'USER_PRESENCE_TABLE', 'S3_BUCKET', 
                'WEBSOCKET_API_ENDPOINT']:
        os.environ.pop(key, None)

@pytest.fixture
def mock_config():
    """Mock configuration"""
    config = Mock()
    config.default_page_size = 50
    config.max_page_size = 100
    config.max_message_length = 4000
    config.get_table_name = Mock(side_effect=lambda name: f'test-{name}')
    config.get_message_config = Mock(return_value={
        'supported_types': ['text', 'image', 'document', 'audio'],
        'max_attachments': 5
    })
    config.get_websocket_config = Mock(return_value={
        'broadcast_function_name': 'test-websocket-broadcast'
    })
    config.get_presence_config = Mock(return_value={
        'statuses': ['online', 'offline', 'away', 'busy'],
        'timeout_minutes': 5
    })
    return config

# Test utilities
def create_mock_event(method='POST', path='/chat/messages', body=None, user_context=None):
    """Utility to create mock HTTP events"""
    user_ctx = user_context or {
        'user_id': 'test-user-123',
        'tenant_id': 'test-tenant-123',
        'email': '<EMAIL>'
    }
    
    return {
        'requestContext': {
            'requestId': 'test-request-123',
            'authorizer': user_ctx
        },
        'httpMethod': method,
        'path': path,
        'headers': {'Authorization': 'Bearer test-token'},
        'body': body
    }

def assert_success_response(response, expected_status=200):
    """Utility to assert successful API response"""
    assert response['statusCode'] == expected_status
    assert 'body' in response
    
def assert_error_response(response, expected_status=400):
    """Utility to assert error API response"""
    assert response['statusCode'] == expected_status
    assert 'body' in response
    body = response['body']
    if isinstance(body, str):
        import json
        body = json.loads(body)
    assert 'error' in body
