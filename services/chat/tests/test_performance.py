# services/chat/tests/test_performance.py
# Performance and load testing for chat system

import pytest
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List
import json

# Mock shared imports
import sys
from unittest.mock import MagicMock, patch
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.services.cache_service import CacheService
from src.services.database_optimization_service import DatabaseOptimizationService
from src.services.cdn_service import CDNService
from src.services.connection_pool_service import ConnectionPoolService

class PerformanceTestSuite:
    """Comprehensive performance testing suite"""
    
    def __init__(self):
        self.cache_service = CacheService()
        self.db_service = DatabaseOptimizationService()
        self.cdn_service = CDNService()
        self.pool_service = ConnectionPoolService()
        
        # Test configuration
        self.test_config = {
            'concurrent_users': 100,
            'messages_per_user': 50,
            'test_duration_seconds': 300,  # 5 minutes
            'ramp_up_time': 60,  # 1 minute
            'acceptable_response_time': 2.0,  # 2 seconds
            'acceptable_error_rate': 0.01,  # 1%
            'target_throughput': 1000  # messages per second
        }
        
        # Performance metrics
        self.metrics = {
            'response_times': [],
            'error_count': 0,
            'success_count': 0,
            'throughput': 0,
            'memory_usage': [],
            'cpu_usage': [],
            'cache_hit_rate': 0,
            'db_query_times': [],
            'websocket_connections': 0
        }
    
    def test_cache_performance(self):
        """Test cache service performance under load"""
        print("\n🔥 Testing Cache Performance...")
        
        # Test data
        test_data = {
            'user_session': {'user_id': 'test-user', 'session_data': {'key': 'value'}},
            'conversation': {'conversation_id': 'test-conv', 'participants': ['user1', 'user2']},
            'messages': [{'message_id': f'msg-{i}', 'content': f'Test message {i}'} for i in range(100)]
        }
        
        # Performance test scenarios
        scenarios = [
            ('cache_user_sessions', self._test_cache_user_sessions, test_data),
            ('cache_conversations', self._test_cache_conversations, test_data),
            ('cache_messages', self._test_cache_messages, test_data),
            ('cache_search_results', self._test_cache_search_results, test_data)
        ]
        
        results = {}
        
        for scenario_name, test_func, data in scenarios:
            print(f"  📊 Testing {scenario_name}...")
            
            start_time = time.time()
            success_count = 0
            error_count = 0
            response_times = []
            
            # Run concurrent operations
            with ThreadPoolExecutor(max_workers=50) as executor:
                futures = []
                
                for i in range(1000):  # 1000 operations
                    future = executor.submit(test_func, data, i)
                    futures.append(future)
                
                for future in as_completed(futures):
                    try:
                        operation_time = future.result()
                        response_times.append(operation_time)
                        success_count += 1
                    except Exception as e:
                        error_count += 1
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Calculate metrics
            avg_response_time = statistics.mean(response_times) if response_times else 0
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else 0
            throughput = success_count / total_time if total_time > 0 else 0
            error_rate = error_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
            
            results[scenario_name] = {
                'avg_response_time': avg_response_time,
                'p95_response_time': p95_response_time,
                'throughput': throughput,
                'error_rate': error_rate,
                'success_count': success_count,
                'error_count': error_count,
                'total_time': total_time
            }
            
            print(f"    ✅ Avg Response Time: {avg_response_time:.3f}s")
            print(f"    ✅ P95 Response Time: {p95_response_time:.3f}s")
            print(f"    ✅ Throughput: {throughput:.1f} ops/sec")
            print(f"    ✅ Error Rate: {error_rate:.2%}")
        
        # Overall cache performance assessment
        overall_avg_time = statistics.mean([r['avg_response_time'] for r in results.values()])
        overall_throughput = sum([r['throughput'] for r in results.values()])
        overall_error_rate = statistics.mean([r['error_rate'] for r in results.values()])
        
        print(f"\n📈 Cache Performance Summary:")
        print(f"  Overall Avg Response Time: {overall_avg_time:.3f}s")
        print(f"  Overall Throughput: {overall_throughput:.1f} ops/sec")
        print(f"  Overall Error Rate: {overall_error_rate:.2%}")
        
        # Performance assertions
        assert overall_avg_time < 0.1, f"Cache response time too high: {overall_avg_time:.3f}s"
        assert overall_error_rate < 0.01, f"Cache error rate too high: {overall_error_rate:.2%}"
        assert overall_throughput > 1000, f"Cache throughput too low: {overall_throughput:.1f} ops/sec"
        
        return results
    
    def test_database_performance(self):
        """Test database optimization performance"""
        print("\n🗄️ Testing Database Performance...")
        
        # Test scenarios
        scenarios = [
            ('get_user_conversations', self._test_user_conversations_query),
            ('get_conversation_messages', self._test_conversation_messages_query),
            ('get_tenant_resources', self._test_tenant_resources_query),
            ('batch_operations', self._test_batch_operations)
        ]
        
        results = {}
        
        for scenario_name, test_func in scenarios:
            print(f"  📊 Testing {scenario_name}...")
            
            response_times = []
            success_count = 0
            error_count = 0
            
            # Run multiple iterations
            for i in range(100):
                try:
                    start_time = time.time()
                    test_func(i)
                    end_time = time.time()
                    
                    response_times.append(end_time - start_time)
                    success_count += 1
                except Exception as e:
                    error_count += 1
            
            # Calculate metrics
            avg_response_time = statistics.mean(response_times) if response_times else 0
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else 0
            error_rate = error_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
            
            results[scenario_name] = {
                'avg_response_time': avg_response_time,
                'p95_response_time': p95_response_time,
                'error_rate': error_rate,
                'success_count': success_count,
                'error_count': error_count
            }
            
            print(f"    ✅ Avg Response Time: {avg_response_time:.3f}s")
            print(f"    ✅ P95 Response Time: {p95_response_time:.3f}s")
            print(f"    ✅ Error Rate: {error_rate:.2%}")
        
        return results
    
    def test_cdn_performance(self):
        """Test CDN service performance"""
        print("\n🌐 Testing CDN Performance...")
        
        # Test scenarios
        test_files = [
            ('small_image.jpg', 'images', 1024),      # 1KB
            ('medium_doc.pdf', 'documents', 1048576), # 1MB
            ('large_video.mp4', 'videos', 10485760),  # 10MB
            ('thumbnail.jpg', 'thumbnails', 512)      # 512B
        ]
        
        results = {}
        
        for filename, file_type, file_size in test_files:
            print(f"  📊 Testing {filename} ({file_type})...")
            
            response_times = []
            success_count = 0
            error_count = 0
            
            # Test URL generation performance
            for i in range(100):
                try:
                    start_time = time.time()
                    
                    # Test CDN URL generation
                    success, url, error = self.cdn_service.generate_cdn_url(
                        file_key=f"test/{filename}",
                        file_type=file_type,
                        expires_in=3600
                    )
                    
                    end_time = time.time()
                    
                    if success:
                        response_times.append(end_time - start_time)
                        success_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
            
            # Calculate metrics
            avg_response_time = statistics.mean(response_times) if response_times else 0
            error_rate = error_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
            
            results[filename] = {
                'file_type': file_type,
                'file_size': file_size,
                'avg_response_time': avg_response_time,
                'error_rate': error_rate,
                'success_count': success_count,
                'error_count': error_count
            }
            
            print(f"    ✅ Avg Response Time: {avg_response_time:.3f}s")
            print(f"    ✅ Error Rate: {error_rate:.2%}")
        
        return results
    
    def test_connection_pool_performance(self):
        """Test connection pool performance under load"""
        print("\n🔗 Testing Connection Pool Performance...")
        
        # Simulate concurrent WebSocket connections
        connection_times = []
        success_count = 0
        error_count = 0
        
        def simulate_websocket_connection(connection_id):
            try:
                start_time = time.time()
                
                # Add connection
                success = self.pool_service.add_websocket_connection(
                    connection_id=f"conn-{connection_id}",
                    user_id=f"user-{connection_id % 100}",
                    tenant_id=f"tenant-{connection_id % 10}",
                    conversation_id=f"conv-{connection_id % 50}"
                )
                
                if success:
                    # Simulate activity
                    time.sleep(0.001)  # 1ms activity
                    self.pool_service.update_connection_activity(f"conn-{connection_id}")
                    
                    # Remove connection
                    self.pool_service.remove_websocket_connection(f"conn-{connection_id}")
                
                end_time = time.time()
                return end_time - start_time, success
                
            except Exception as e:
                return 0, False
        
        print("  📊 Testing concurrent WebSocket connections...")
        
        # Test with concurrent connections
        with ThreadPoolExecutor(max_workers=100) as executor:
            futures = []
            
            for i in range(1000):  # 1000 connections
                future = executor.submit(simulate_websocket_connection, i)
                futures.append(future)
            
            for future in as_completed(futures):
                try:
                    conn_time, success = future.result()
                    connection_times.append(conn_time)
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    error_count += 1
        
        # Calculate metrics
        avg_connection_time = statistics.mean(connection_times) if connection_times else 0
        p95_connection_time = statistics.quantiles(connection_times, n=20)[18] if len(connection_times) > 20 else 0
        error_rate = error_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
        
        # Get pool statistics
        pool_stats = self.pool_service.get_pool_statistics()
        
        results = {
            'avg_connection_time': avg_connection_time,
            'p95_connection_time': p95_connection_time,
            'error_rate': error_rate,
            'success_count': success_count,
            'error_count': error_count,
            'pool_stats': pool_stats
        }
        
        print(f"    ✅ Avg Connection Time: {avg_connection_time:.3f}s")
        print(f"    ✅ P95 Connection Time: {p95_connection_time:.3f}s")
        print(f"    ✅ Error Rate: {error_rate:.2%}")
        print(f"    ✅ Successful Connections: {success_count}")
        
        return results
    
    def test_end_to_end_performance(self):
        """Test end-to-end system performance"""
        print("\n🎯 Testing End-to-End Performance...")
        
        # Simulate complete chat flow
        def simulate_chat_flow(user_id):
            try:
                start_time = time.time()
                
                # 1. User connects via WebSocket
                connection_id = f"conn-{user_id}-{int(time.time())}"
                tenant_id = f"tenant-{user_id % 10}"
                conversation_id = f"conv-{user_id % 50}"
                
                # Add to connection pool
                self.pool_service.add_websocket_connection(
                    connection_id, f"user-{user_id}", tenant_id, conversation_id
                )
                
                # 2. Cache user session
                self.cache_service.cache_user_session(
                    f"user-{user_id}", tenant_id, {'session': 'data'}
                )
                
                # 3. Get cached conversation
                self.cache_service.get_conversation(conversation_id, tenant_id)
                
                # 4. Send message (simulate)
                time.sleep(0.01)  # Simulate message processing
                
                # 5. Generate CDN URL for file
                self.cdn_service.generate_cdn_url(
                    f"files/test-{user_id}.jpg", 'images', 3600
                )
                
                # 6. Update connection activity
                self.pool_service.update_connection_activity(connection_id)
                
                # 7. Disconnect
                self.pool_service.remove_websocket_connection(connection_id)
                
                end_time = time.time()
                return end_time - start_time
                
            except Exception as e:
                return None
        
        # Run concurrent chat flows
        flow_times = []
        success_count = 0
        error_count = 0
        
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = []
            
            for i in range(200):  # 200 concurrent users
                future = executor.submit(simulate_chat_flow, i)
                futures.append(future)
            
            for future in as_completed(futures):
                try:
                    flow_time = future.result()
                    if flow_time is not None:
                        flow_times.append(flow_time)
                        success_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    error_count += 1
        
        # Calculate metrics
        avg_flow_time = statistics.mean(flow_times) if flow_times else 0
        p95_flow_time = statistics.quantiles(flow_times, n=20)[18] if len(flow_times) > 20 else 0
        error_rate = error_count / (success_count + error_count) if (success_count + error_count) > 0 else 0
        
        results = {
            'avg_flow_time': avg_flow_time,
            'p95_flow_time': p95_flow_time,
            'error_rate': error_rate,
            'success_count': success_count,
            'error_count': error_count,
            'throughput': success_count / max(flow_times) if flow_times else 0
        }
        
        print(f"    ✅ Avg Flow Time: {avg_flow_time:.3f}s")
        print(f"    ✅ P95 Flow Time: {p95_flow_time:.3f}s")
        print(f"    ✅ Error Rate: {error_rate:.2%}")
        print(f"    ✅ Throughput: {results['throughput']:.1f} flows/sec")
        
        return results
    
    # Helper methods for specific tests
    def _test_cache_user_sessions(self, data, iteration):
        start_time = time.time()
        self.cache_service.cache_user_session(
            f"user-{iteration}", f"tenant-{iteration % 10}", data['user_session']
        )
        self.cache_service.get_user_session(f"user-{iteration}", f"tenant-{iteration % 10}")
        return time.time() - start_time
    
    def _test_cache_conversations(self, data, iteration):
        start_time = time.time()
        self.cache_service.cache_conversation(
            f"conv-{iteration}", f"tenant-{iteration % 10}", data['conversation']
        )
        self.cache_service.get_conversation(f"conv-{iteration}", f"tenant-{iteration % 10}")
        return time.time() - start_time
    
    def _test_cache_messages(self, data, iteration):
        start_time = time.time()
        self.cache_service.cache_recent_messages(
            f"conv-{iteration}", f"tenant-{iteration % 10}", data['messages']
        )
        self.cache_service.get_recent_messages(f"conv-{iteration}", f"tenant-{iteration % 10}")
        return time.time() - start_time
    
    def _test_cache_search_results(self, data, iteration):
        start_time = time.time()
        query_hash = f"hash-{iteration}"
        self.cache_service.cache_search_results(
            query_hash, f"tenant-{iteration % 10}", {'results': 'test'}
        )
        self.cache_service.get_search_results(query_hash, f"tenant-{iteration % 10}")
        return time.time() - start_time
    
    def _test_user_conversations_query(self, iteration):
        self.db_service.get_user_conversations_optimized(
            f"user-{iteration}", f"tenant-{iteration % 10}", limit=20
        )
    
    def _test_conversation_messages_query(self, iteration):
        self.db_service.get_conversation_messages_optimized(
            f"conv-{iteration}", f"tenant-{iteration % 10}", limit=50
        )
    
    def _test_tenant_resources_query(self, iteration):
        self.db_service.get_tenant_resources_optimized(
            f"tenant-{iteration % 10}", resource_type='messages', limit=50
        )
    
    def _test_batch_operations(self, iteration):
        # Simulate batch operations
        keys = [{'PK': f'test-{i}', 'SK': f'item-{i}'} for i in range(25)]
        self.db_service.batch_get_items_optimized(keys)


def test_performance_suite():
    """Run complete performance test suite"""
    print("🚀 Starting Performance Test Suite...")
    
    suite = PerformanceTestSuite()
    
    # Run all performance tests
    results = {
        'cache_performance': suite.test_cache_performance(),
        'database_performance': suite.test_database_performance(),
        'cdn_performance': suite.test_cdn_performance(),
        'connection_pool_performance': suite.test_connection_pool_performance(),
        'end_to_end_performance': suite.test_end_to_end_performance()
    }
    
    print("\n📊 Performance Test Suite Results:")
    print("=" * 50)
    
    for test_name, test_results in results.items():
        print(f"\n{test_name.upper()}:")
        if isinstance(test_results, dict):
            for key, value in test_results.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value}")
                elif isinstance(value, dict):
                    print(f"  {key}: {json.dumps(value, indent=4)}")
    
    print("\n✅ Performance Test Suite Completed!")
    return results


if __name__ == '__main__':
    test_performance_suite()
