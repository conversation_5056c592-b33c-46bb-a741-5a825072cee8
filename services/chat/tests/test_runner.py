#!/usr/bin/env python3
# services/chat/tests/test_runner.py
# Basic test runner for Chat Service message flow validation

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any, List
from unittest.mock import Mock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Mock shared modules
from unittest.mock import MagicMock, Mock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

class ChatServiceTestRunner:
    """Basic test runner for Chat Service functionality"""
    
    def __init__(self):
        self.test_results = []
        self.setup_mocks()
    
    def setup_mocks(self):
        """Setup mock responses for testing"""
        # Mock logger
        mock_logger = MagicMock()
        sys.modules['shared.logger'].lambda_logger = mock_logger
        
        # Mock API responses
        mock_api = MagicMock()
        mock_api.success = Mock(side_effect=lambda data, code=200: {
            'statusCode': code, 
            'body': json.dumps(data)
        })
        mock_api.error = Mock(side_effect=lambda msg, code=400: {
            'statusCode': code, 
            'body': json.dumps({'error': msg})
        })
        sys.modules['shared.responses'].APIResponse = mock_api
        
        # Mock auth
        mock_auth = Mock(return_value={
            'user_id': 'test-user-123',
            'tenant_id': 'test-tenant-123',
            'email': '<EMAIL>'
        })
        sys.modules['shared.auth'].extract_user_context = mock_auth
        
        # Mock DynamoDB
        mock_db = MagicMock()
        sys.modules['shared.database'].DynamoDBClient = mock_db
    
    def run_test(self, test_name: str, test_func) -> bool:
        """Run a single test and record results"""
        print(f"\n🧪 Running test: {test_name}")
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                print(f"✅ PASSED ({end_time - start_time:.3f}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'PASSED',
                    'duration': end_time - start_time,
                    'error': None
                })
                return True
            else:
                print(f"❌ FAILED ({end_time - start_time:.3f}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'FAILED',
                    'duration': end_time - start_time,
                    'error': 'Test returned False'
                })
                return False
                
        except Exception as e:
            end_time = time.time()
            print(f"💥 ERROR ({end_time - start_time:.3f}s): {str(e)}")
            self.test_results.append({
                'name': test_name,
                'status': 'ERROR',
                'duration': end_time - start_time,
                'error': str(e)
            })
            return False
    
    def test_send_message_handler(self) -> bool:
        """Test send message handler"""
        from handlers.send_message import handler
        
        # Mock dependencies
        with Mock() as mock_service:
            mock_service.create_message.return_value = (True, {
                'messageId': 'msg-123',
                'conversationId': 'conv-123',
                'content': 'Test message',
                'type': 'text',
                'timestamp': datetime.utcnow().isoformat()
            }, None)
            
            event = {
                'requestContext': {'requestId': 'test-123'},
                'httpMethod': 'POST',
                'path': '/chat/messages',
                'body': json.dumps({
                    'conversationId': 'conv-123',
                    'content': 'Test message',
                    'type': 'text'
                })
            }
            
            response = handler(event, None)
            return response['statusCode'] == 201
    
    def test_get_messages_handler(self) -> bool:
        """Test get messages handler"""
        from handlers.get_messages import handler
        
        event = {
            'requestContext': {'requestId': 'test-123'},
            'httpMethod': 'GET',
            'path': '/chat/messages/conv-123',
            'pathParameters': {'conversationId': 'conv-123'},
            'queryStringParameters': {'limit': '50'}
        }
        
        response = handler(event, None)
        return response['statusCode'] == 200
    
    def test_message_status_handler(self) -> bool:
        """Test message status update handler"""
        from handlers.message_status import handler
        
        event = {
            'requestContext': {'requestId': 'test-123'},
            'httpMethod': 'PATCH',
            'path': '/chat/messages/msg-123/status',
            'pathParameters': {'messageId': 'msg-123'},
            'body': json.dumps({'status': 'read'})
        }
        
        response = handler(event, None)
        return response['statusCode'] == 200
    
    def test_message_service_creation(self) -> bool:
        """Test MessageService instantiation"""
        from services.message_service import MessageService
        
        service = MessageService()
        return service is not None
    
    def test_hybrid_router_logic(self) -> bool:
        """Test HybridRouter routing logic"""
        from services.hybrid_router import HybridRouter
        
        router = HybridRouter()
        
        # Test simple message (should use WebSocket)
        use_ws, reason = router.should_use_websocket(
            'text', 
            'Hello there!', 
            {'agentType': 'feedo'}
        )
        
        if not use_ws:
            print(f"Expected WebSocket routing, got: {reason}")
            return False
        
        # Test complex message (should use N8N)
        use_ws, reason = router.should_use_websocket(
            'text',
            'Please analyze this complex data and generate a comprehensive report',
            {'agentType': 'forecaster'}
        )
        
        if use_ws:
            print(f"Expected N8N routing, got: {reason}")
            return False
        
        return True
    
    def test_notification_service(self) -> bool:
        """Test NotificationService functionality"""
        from services.notification_service import NotificationService
        
        service = NotificationService()
        
        # Test notification creation
        message_data = {
            'messageId': 'msg-123',
            'content': 'Test notification',
            'type': 'text',
            'userId': 'user-123',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        success, error = service.notify_new_message(
            'conv-123', message_data, 'user-123', 'tenant-123'
        )
        
        return success
    
    def test_chat_message_models(self) -> bool:
        """Test ChatMessage model functionality"""
        from models.chat_message import ChatMessage
        
        # Create message
        message = ChatMessage(
            conversation_id='conv-123',
            user_id='user-123',
            tenant_id='tenant-123',
            content='Test message'
        )
        
        # Test serialization
        message_dict = message.to_dict()
        dynamodb_item = message.to_dynamodb_item()
        
        # Test deserialization
        restored_message = ChatMessage.from_dict(message_dict)
        
        return (
            message.content == 'Test message' and
            message_dict['content'] == 'Test message' and
            dynamodb_item['content'] == 'Test message' and
            restored_message.content == 'Test message'
        )
    
    def test_user_presence_models(self) -> bool:
        """Test UserPresence model functionality"""
        from models.user_presence import UserPresence
        
        presence = UserPresence('user-123', 'tenant-123', 'online')
        
        # Test status update
        presence.update_status('away')
        
        # Test serialization
        presence_dict = presence.to_dict()
        dynamodb_item = presence.to_dynamodb_item()
        
        return (
            presence.status == 'away' and
            presence_dict['status'] == 'away' and
            dynamodb_item['status'] == 'away'
        )
    
    def test_schema_patterns(self) -> bool:
        """Test DynamoDB schema patterns"""
        from models.schema import MessageSchema, ConversationStateSchema, UserPresenceSchema

        # Test message schema
        message_item = MessageSchema.create_message_item(
            conversation_id='conv-123',
            user_id='user-123',
            tenant_id='tenant-123',
            content='Test message'
        )

        # Test conversation schema
        conv_item = ConversationStateSchema.create_conversation_state_item(
            conversation_id='conv-123',
            tenant_id='tenant-123',
            participants=['user-123']
        )

        # Test presence schema
        presence_item = UserPresenceSchema.create_user_presence_item(
            user_id='user-123',
            tenant_id='tenant-123'
        )

        return (
            message_item['content'] == 'Test message' and
            conv_item['conversationId'] == 'conv-123' and
            presence_item['userId'] == 'user-123'
        )

    def test_presence_service(self) -> bool:
        """Test PresenceService functionality"""
        from services.presence_service import PresenceService

        service = PresenceService()

        # Mock the database
        service.presence_db = Mock()
        service.presence_db.get_item.return_value = None
        service.presence_db.put_item.return_value = None

        # Test user connect
        success, error = service.handle_user_connect(
            user_id='user-123',
            tenant_id='tenant-123',
            connection_count_delta=1
        )

        return success and error is None

    def test_realtime_service(self) -> bool:
        """Test RealtimeService functionality"""
        from services.realtime_service import RealtimeService

        service = RealtimeService()
        service.lambda_client = Mock()

        # Test activity notification
        success, error = service.notify_user_activity(
            user_id='user-123',
            tenant_id='tenant-123',
            activity_type='message_sent'
        )

        return success and error is None

    def test_typing_validation(self) -> bool:
        """Test typing message validation"""
        from utils.validation import WebSocketValidator

        # Valid typing message
        valid_message = {
            'conversationId': 'conv-123',
            'isTyping': True,
            'duration': 30
        }

        is_valid, error = WebSocketValidator.validate_typing_message(valid_message)

        if not is_valid:
            print(f"Typing validation failed: {error}")
            return False

        # Invalid typing message
        invalid_message = {
            'isTyping': True
            # Missing conversationId
        }

        is_valid, error = WebSocketValidator.validate_typing_message(invalid_message)

        return not is_valid  # Should be invalid

    def test_agent_integration_service(self) -> bool:
        """Test AgentIntegrationService functionality"""
        from services.agent_integration_service import AgentIntegrationService

        service = AgentIntegrationService()

        # Test agent type determination
        feedo_content = "Optimize my delivery routes"
        forecaster_content = "Forecast demand for next quarter"

        feedo_type = service._determine_agent_type(feedo_content)
        forecaster_type = service._determine_agent_type(forecaster_content)

        if feedo_type != 'feedo' or forecaster_type != 'forecaster':
            print(f"Agent type detection failed: {feedo_type}, {forecaster_type}")
            return False

        # Test processing time estimation
        simple_time = service._estimate_processing_time("Hi", "feedo")
        complex_time = service._estimate_processing_time("Complex analysis request with multiple parameters", "forecaster")

        return 1.0 <= simple_time <= 15.0 and complex_time > simple_time

    def test_agent_typing_service(self) -> bool:
        """Test AgentTypingService functionality"""
        from services.agent_typing_service import AgentTypingService

        service = AgentTypingService()

        # Test typing schedule calculation
        pattern = {
            'initial_delay': 1.0,
            'typing_duration': 3.0,
            'pause_duration': 1.5,
            'bursts': 2,
            'variation': 0.5
        }

        schedule = service._calculate_typing_schedule(pattern, 10.0)

        if len(schedule) < 3:  # Should have multiple events
            print(f"Typing schedule too short: {len(schedule)}")
            return False

        # Test stats
        stats = service.get_typing_stats()

        return 'active_sessions' in stats and 'timestamp' in stats

    def test_agent_response_processor(self) -> bool:
        """Test AgentResponseProcessor functionality"""
        from services.agent_response_processor import AgentResponseProcessor

        processor = AgentResponseProcessor()

        # Test valid response validation
        valid_response = {
            'content': 'Valid response content',
            'type': 'text',
            'confidence': 0.8
        }

        is_valid, error = processor._validate_response_data(valid_response)
        if not is_valid:
            print(f"Valid response validation failed: {error}")
            return False

        # Test invalid response validation
        invalid_response = {
            'type': 'text',
            'confidence': 1.5  # Invalid confidence
        }

        is_valid, error = processor._validate_response_data(invalid_response)

        return not is_valid  # Should be invalid

    def test_hybrid_router_agent_integration(self) -> bool:
        """Test HybridRouter agent response handling"""
        from services.hybrid_router import HybridRouter

        router = HybridRouter()

        # Mock the agent response processor
        router.agent_response_processor = Mock()
        router.agent_response_processor.process_agent_response.return_value = (True, {
            'messageId': 'test-msg-123',
            'timestamp': '2024-01-01T10:00:00Z',
            'processingTime': 2.5,
            'notificationSent': True
        }, None)

        # Test agent response handling
        response_data = {
            'agentName': 'Feedo',
            'agentType': 'feedo',
            'response': {
                'content': 'Test response',
                'type': 'text'
            }
        }

        success, result, error = router.handle_agent_response(
            conversation_id='test-conv',
            agent_id='test-agent',
            response_data=response_data,
            tenant_id='test-tenant'
        )

        return success and result.get('routing_method') == 'agent_response'

    def test_file_upload_service(self) -> bool:
        """Test FileUploadService functionality"""
        from services.file_upload_service import FileUploadService

        service = FileUploadService()

        # Test file validation
        valid_result = service._validate_file_upload(
            filename='test.pdf',
            content_type='application/pdf',
            file_size=1024000
        )

        invalid_result = service._validate_file_upload(
            filename='test.exe',
            content_type='application/octet-stream',
            file_size=1024000
        )

        if not valid_result[0] or invalid_result[0]:
            print(f"File validation failed: valid={valid_result}, invalid={invalid_result}")
            return False

        # Test file extension extraction
        extension = service._get_file_extension('document.PDF')
        return extension == '.pdf'

    def test_file_preview_service(self) -> bool:
        """Test FilePreviewService functionality"""
        from services.file_preview_service import FilePreviewService

        service = FilePreviewService()

        # Test file type determination
        image_type = service._determine_file_type('image/jpeg')
        video_type = service._determine_file_type('video/mp4')
        document_type = service._determine_file_type('application/pdf')

        if image_type != 'image' or video_type != 'video' or document_type != 'document':
            print(f"File type determination failed: {image_type}, {video_type}, {document_type}")
            return False

        # Test file extension extraction
        extension = service._get_file_extension('image.JPG')
        return extension == '.jpg'

    def test_file_notification_service(self) -> bool:
        """Test FileNotificationService functionality"""
        from services.file_notification_service import FileNotificationService

        service = FileNotificationService()

        # Test notification preparation (mock scenario)
        file_info = {
            'fileId': 'test-file-123',
            'filename': 'test.pdf',
            'fileSize': 1024000
        }

        # Test would normally send notifications
        # For now, just verify service instantiation
        return hasattr(service, 'notify_file_upload_completed')

    def test_file_sharing_integration(self) -> bool:
        """Test file sharing integration"""
        from services.file_upload_service import file_upload_service
        from services.file_preview_service import file_preview_service
        from services.file_notification_service import file_notification_service

        # Test service instances exist
        services_exist = all([
            file_upload_service is not None,
            file_preview_service is not None,
            file_notification_service is not None
        ])

        if not services_exist:
            print("File sharing service instances not found")
            return False

        # Test configuration
        file_config = file_upload_service.file_config
        required_config = ['s3_bucket_name', 'max_file_size_mb', 'allowed_extensions']

        config_valid = all(key in file_config for key in required_config)

        return config_valid

    def test_search_service(self) -> bool:
        """Test SearchService functionality"""
        from services.search_service import SearchService

        service = SearchService()

        # Test search configuration
        search_config = service.search_config
        required_config = ['type', 'enabled', 'message_index', 'file_index']

        config_valid = all(key in search_config for key in required_config)

        if not config_valid:
            print(f"Search service config missing keys: {required_config}")
            return False

        # Test search query building
        try:
            query = service._build_message_search_query(
                query='test',
                tenant_id='test-tenant',
                user_id='test-user',
                filters={},
                page=1,
                page_size=20
            )

            # Verify query structure
            required_query_keys = ['query', 'sort', 'from', 'size']
            query_valid = all(key in query for key in required_query_keys)

            return query_valid

        except Exception as e:
            print(f"Search query building failed: {e}")
            return False

    def test_analytics_service(self) -> bool:
        """Test AnalyticsService functionality"""
        from services.analytics_service import AnalyticsService

        service = AnalyticsService()

        # Test analytics configuration
        analytics_config = service.analytics_config

        if not analytics_config:
            print("Analytics config not found")
            return False

        # Test metric tracking (mock scenario)
        try:
            success = service.track_message_sent(
                tenant_id='test-tenant',
                user_id='test-user',
                conversation_id='test-conv',
                message_type='text',
                agent_involved=False
            )

            # Should succeed even with mock CloudWatch
            return success

        except Exception as e:
            print(f"Analytics tracking failed: {e}")
            return False

    def test_search_indexing_service(self) -> bool:
        """Test SearchIndexingService functionality"""
        from services.search_indexing_service import SearchIndexingService

        service = SearchIndexingService()

        # Test document preparation
        message_data = {
            'messageId': 'test-msg-123',
            'conversationId': 'test-conv-123',
            'userId': 'test-user',
            'tenantId': 'test-tenant',
            'content': 'Test message content',
            'type': 'text',
            'timestamp': '2024-01-01T00:00:00Z'
        }

        try:
            document = service._prepare_message_document(message_data)

            # Verify document structure
            required_fields = ['messageId', 'conversationId', 'content', 'tenantId']
            document_valid = all(field in document for field in required_fields)

            if not document_valid:
                print(f"Message document missing fields: {required_fields}")
                return False

            # Test file document preparation
            file_data = {
                'fileId': 'test-file-123',
                'filename': 'test.pdf',
                'fileSize': 1024,
                'contentType': 'application/pdf',
                'tenantId': 'test-tenant',
                'uploadedBy': 'test-user'
            }

            file_document = service._prepare_file_document(file_data)

            required_file_fields = ['fileId', 'filename', 'fileSize', 'tenantId']
            file_document_valid = all(field in file_document for field in required_file_fields)

            return file_document_valid

        except Exception as e:
            print(f"Search indexing document preparation failed: {e}")
            return False

    def test_search_integration(self) -> bool:
        """Test search system integration"""
        from services.search_service import search_service
        from services.analytics_service import analytics_service
        from services.search_indexing_service import search_indexing_service

        # Test service instances exist
        services_exist = all([
            search_service is not None,
            analytics_service is not None,
            search_indexing_service is not None
        ])

        if not services_exist:
            print("Search system service instances not found")
            return False

        # Test search configuration consistency
        search_config = search_service.search_config
        indexing_config = search_indexing_service.search_config

        # Should use same configuration
        config_consistent = (
            search_config.get('message_index') == indexing_config.get('message_index') and
            search_config.get('file_index') == indexing_config.get('file_index')
        )

        return config_consistent

    def test_performance_suite(self) -> bool:
        """Test performance and optimization components"""
        try:
            # Import performance test suite
            from test_performance import PerformanceTestSuite

            suite = PerformanceTestSuite()

            # Test cache performance
            cache_results = suite.test_cache_performance()
            cache_performance_good = all(
                result['error_rate'] < 0.01 and result['avg_response_time'] < 0.1
                for result in cache_results.values()
                if isinstance(result, dict) and 'error_rate' in result
            )

            if not cache_performance_good:
                print("Cache performance below expectations")
                return False

            # Test database performance
            db_results = suite.test_database_performance()
            db_performance_good = all(
                result['error_rate'] < 0.01 and result['avg_response_time'] < 1.0
                for result in db_results.values()
                if isinstance(result, dict) and 'error_rate' in result
            )

            if not db_performance_good:
                print("Database performance below expectations")
                return False

            # Test connection pool performance
            pool_results = suite.test_connection_pool_performance()
            pool_performance_good = (
                pool_results.get('error_rate', 1.0) < 0.01 and
                pool_results.get('avg_connection_time', 10.0) < 1.0
            )

            if not pool_performance_good:
                print("Connection pool performance below expectations")
                return False

            # Test end-to-end performance
            e2e_results = suite.test_end_to_end_performance()
            e2e_performance_good = (
                e2e_results.get('error_rate', 1.0) < 0.05 and
                e2e_results.get('avg_flow_time', 10.0) < 5.0
            )

            if not e2e_performance_good:
                print("End-to-end performance below expectations")
                return False

            print("Performance suite passed all benchmarks")
            return True

        except Exception as e:
            print(f"Performance testing failed: {e}")
            return False

    def test_optimization_services(self) -> bool:
        """Test optimization services functionality"""
        from services.cache_service import CacheService
        from services.database_optimization_service import DatabaseOptimizationService
        from services.cdn_service import CDNService
        from services.connection_pool_service import ConnectionPoolService

        try:
            # Test cache service
            cache_service = CacheService()
            cache_stats = cache_service.get_cache_stats()

            if not isinstance(cache_stats, dict):
                print("Cache service stats not available")
                return False

            # Test database optimization service
            db_service = DatabaseOptimizationService()

            # Test GSI configurations
            gsi_configs = db_service.gsi_configs
            required_gsis = ['UserConversationsIndex', 'ConversationMessagesIndex', 'TenantResourcesIndex']

            if not all(gsi in gsi_configs for gsi in required_gsis):
                print("Required GSI configurations missing")
                return False

            # Test CDN service
            cdn_service = CDNService()
            cdn_stats = cdn_service.get_cache_statistics()

            if not isinstance(cdn_stats[0], bool):  # Returns (success, stats, error)
                print("CDN service not properly configured")
                return False

            # Test connection pool service
            pool_service = ConnectionPoolService()
            pool_stats = pool_service.get_pool_statistics()

            if not isinstance(pool_stats, dict):
                print("Connection pool service not working")
                return False

            print("All optimization services working correctly")
            return True

        except Exception as e:
            print(f"Optimization services test failed: {e}")
            return False

    def test_monitoring_system(self) -> bool:
        """Test monitoring and observability system"""
        try:
            # Test monitoring service basic functionality
            from services.monitoring_service import monitoring_service
            from services.health_check_service import health_check_service

            # Test monitoring service
            success = monitoring_service.record_metric('test_metric', 100, 'Count')
            if not success:
                print("Monitoring service metric recording failed")
                return False

            # Test health check service
            health_status = health_check_service.get_simple_health()
            if not isinstance(health_status, dict) or 'status' not in health_status:
                print("Health check service failed")
                return False

            print("Monitoring system tests passed")
            return True

        except Exception as e:
            print(f"Monitoring system test failed: {e}")
            return False

    def test_alerting_system(self) -> bool:
        """Test alerting system functionality"""
        try:
            # Test alerting service basic functionality
            from services.alerting_service import alerting_service

            # Test alert status
            alert_status = alerting_service.get_alert_status()
            if not isinstance(alert_status, dict) or 'enabled' not in alert_status:
                print("Alerting service status check failed")
                return False

            # Test basic alert sending
            context = {'test': 'data'}
            success = alerting_service.send_alert('test_alert', context)
            if not success:
                print("Alerting service send alert failed")
                return False

            print("Alerting system tests passed")
            return True

        except Exception as e:
            print(f"Alerting system test failed: {e}")
            return False

    def test_structured_logging(self) -> bool:
        """Test structured logging functionality"""
        try:
            # Test structured logging using shared logger
            from shared.logger import lambda_logger, log_api_request, log_api_response, log_business_operation

            # Test basic logging
            lambda_logger.info("Test structured logging")

            # Test business operation logging
            log_business_operation(
                lambda_logger,
                operation="test_operation",
                entity_type="test",
                entity_id="test-123",
                tenant_id="tenant-123",
                user_id="user-123",
                status="completed"
            )

            # Test health handlers
            from handlers.health import health_check_handler

            # Test health check handler
            test_event = {
                'httpMethod': 'GET',
                'path': '/health',
                'headers': {},
                'requestContext': {}
            }

            class MockContext:
                aws_request_id = 'test-request-123'

            response = health_check_handler(test_event, MockContext())
            if not isinstance(response, dict) or 'statusCode' not in response:
                print("Health handler test failed")
                return False

            print("Structured logging tests passed")
            return True

        except Exception as e:
            print(f"Structured logging test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting Chat Service Test Suite")
        print("=" * 50)
        
        tests = [
            ("Handler: Send Message", self.test_send_message_handler),
            ("Handler: Get Messages", self.test_get_messages_handler),
            ("Handler: Message Status", self.test_message_status_handler),
            ("Service: Message Service", self.test_message_service_creation),
            ("Service: Hybrid Router", self.test_hybrid_router_logic),
            ("Service: Notification", self.test_notification_service),
            ("Service: Presence", self.test_presence_service),
            ("Service: Realtime", self.test_realtime_service),
            ("Service: Agent Integration", self.test_agent_integration_service),
            ("Service: Agent Typing", self.test_agent_typing_service),
            ("Service: Agent Response Processor", self.test_agent_response_processor),
            ("Service: File Upload", self.test_file_upload_service),
            ("Service: File Preview", self.test_file_preview_service),
            ("Service: File Notification", self.test_file_notification_service),
            ("Service: Search", self.test_search_service),
            ("Service: Analytics", self.test_analytics_service),
            ("Service: Search Indexing", self.test_search_indexing_service),
            ("Integration: Hybrid Router + Agents", self.test_hybrid_router_agent_integration),
            ("Integration: File Sharing", self.test_file_sharing_integration),
            ("Integration: Search System", self.test_search_integration),
            ("Performance: Optimization Services", self.test_optimization_services),
            ("Performance: Full Suite", self.test_performance_suite),
            ("Monitoring: System Health", self.test_monitoring_system),
            ("Monitoring: Alerting", self.test_alerting_system),
            ("Monitoring: Structured Logging", self.test_structured_logging),
            ("Models: Chat Message", self.test_chat_message_models),
            ("Models: User Presence", self.test_user_presence_models),
            ("Schema: DynamoDB Patterns", self.test_schema_patterns),
            ("Validation: Typing Messages", self.test_typing_validation)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if self.run_test(test_name, test_func):
                passed += 1
        
        # Generate report
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! Chat Service is ready for deployment.")
        else:
            print(f"\n⚠️  {total - passed} tests failed. Review and fix issues before deployment.")
        
        # Detailed results
        print("\n📋 DETAILED RESULTS:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASSED' else "❌" if result['status'] == 'FAILED' else "💥"
            print(f"{status_icon} {result['name']} ({result['duration']:.3f}s)")
            if result['error']:
                print(f"   Error: {result['error']}")
        
        return passed == total


def main():
    """Main test runner entry point"""
    runner = ChatServiceTestRunner()
    success = runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
