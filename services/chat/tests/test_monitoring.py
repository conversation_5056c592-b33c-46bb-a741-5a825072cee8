# services/chat/tests/test_monitoring.py
# Comprehensive tests for monitoring and observability system

import pytest
import asyncio
import json
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Mock shared imports
import sys
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.services.monitoring_service import MonitoringService, monitoring_service
from src.services.alerting_service import AlertingService, AlertSeverity, alerting_service
from src.services.health_check_service import HealthCheckService, HealthStatus, health_check_service
from src.utils.structured_logger import StructuredLogger, correlation_context_manager
from src.handlers.health import (
    health_check_handler, detailed_health_handler, status_handler,
    metrics_handler, readiness_handler, liveness_handler, version_handler
)

class TestMonitoringService:
    """Test monitoring service functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.monitoring = MonitoringService()
    
    def test_record_metric_basic(self):
        """Test basic metric recording"""
        success = self.monitoring.record_metric(
            metric_name='test_metric',
            value=100,
            unit='Count',
            namespace='chat'
        )
        
        assert success is True
    
    def test_record_metric_with_dimensions(self):
        """Test metric recording with dimensions"""
        dimensions = {
            'TenantId': 'tenant-123',
            'Environment': 'test'
        }
        
        success = self.monitoring.record_metric(
            metric_name='test_metric_with_dims',
            value=50.5,
            unit='Milliseconds',
            namespace='performance',
            dimensions=dimensions
        )
        
        assert success is True
    
    def test_record_message_sent(self):
        """Test message sent metric recording"""
        success = self.monitoring.record_message_sent(
            tenant_id='tenant-123',
            user_id='user-456',
            conversation_id='conv-789',
            message_type='text',
            agent_involved=True
        )
        
        assert success is True
    
    def test_record_response_time(self):
        """Test response time metric recording"""
        success = self.monitoring.record_response_time(
            endpoint='/messages',
            method='POST',
            response_time_ms=150.5,
            status_code=200,
            tenant_id='tenant-123'
        )
        
        assert success is True
    
    def test_record_error(self):
        """Test error metric recording"""
        success = self.monitoring.record_error(
            error_type='ValidationError',
            endpoint='/messages',
            tenant_id='tenant-123',
            error_message='Invalid message format',
            severity='error'
        )
        
        assert success is True
    
    def test_record_cache_operation(self):
        """Test cache operation metric recording"""
        success = self.monitoring.record_cache_operation(
            operation='get',
            cache_type='redis',
            hit=True,
            latency_ms=5.2,
            tenant_id='tenant-123'
        )
        
        assert success is True
    
    def test_record_database_metrics(self):
        """Test database metrics recording"""
        success = self.monitoring.record_database_metrics(
            operation='query',
            table_name='ChatMessages',
            latency_ms=25.8,
            consumed_capacity=2.5,
            success=True
        )
        
        assert success is True
    
    def test_get_realtime_metrics(self):
        """Test real-time metrics retrieval"""
        # Add some test data
        self.monitoring.realtime_metrics['active_connections'] = 150
        self.monitoring.realtime_metrics['messages_per_minute'].append({
            'timestamp': datetime.utcnow(),
            'count': 10
        })
        
        metrics = self.monitoring.get_realtime_metrics()
        
        assert isinstance(metrics, dict)
        assert 'timestamp' in metrics
        assert 'active_connections' in metrics
        assert metrics['active_connections'] == 150
    
    def test_flush_metrics(self):
        """Test metric buffer flushing"""
        # Add metrics to buffer
        self.monitoring.record_metric('test_flush', 1, 'Count')
        
        # Flush metrics
        success = self.monitoring.flush_all_metrics()
        
        assert success is True

class TestAlertingService:
    """Test alerting service functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.alerting = AlertingService()
    
    def test_send_alert_basic(self):
        """Test basic alert sending"""
        context = {
            'metric_name': 'response_time',
            'current_value': 3000,
            'threshold': 2000
        }
        
        success = self.alerting.send_alert(
            alert_type='high_response_time',
            context=context
        )
        
        assert success is True
    
    def test_send_performance_alert(self):
        """Test performance alert"""
        success = self.alerting.send_performance_alert(
            metric_name='response_time',
            current_value=3500.0,
            threshold=2000.0,
            endpoint='/messages',
            tenant_id='tenant-123'
        )
        
        assert success is True
    
    def test_send_infrastructure_alert(self):
        """Test infrastructure alert"""
        details = {
            'connection_count': 9500,
            'max_connections': 10000
        }
        
        success = self.alerting.send_infrastructure_alert(
            component='websocket_pool',
            issue='connection limit approaching',
            details=details
        )
        
        assert success is True
    
    def test_send_security_alert(self):
        """Test security alert"""
        details = {
            'failed_attempts': 5,
            'time_window': '5 minutes'
        }
        
        success = self.alerting.send_security_alert(
            incident_type='brute_force_attempt',
            source_ip='*************',
            user_id='user-456',
            details=details
        )
        
        assert success is True
    
    def test_alert_suppression(self):
        """Test alert suppression mechanism"""
        context = {'endpoint': '/test', 'tenant_id': 'tenant-123'}
        
        # Send first alert
        success1 = self.alerting.send_alert('test_alert', context)
        
        # Send second alert immediately (should be suppressed)
        success2 = self.alerting.send_alert('test_alert', context)
        
        assert success1 is True
        assert success2 is True  # Still returns True but should be suppressed
    
    def test_get_alert_status(self):
        """Test alert status retrieval"""
        status = self.alerting.get_alert_status()
        
        assert isinstance(status, dict)
        assert 'enabled' in status
        assert 'channels' in status
        assert 'available_alert_types' in status

class TestHealthCheckService:
    """Test health check service functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.health_check = HealthCheckService()
    
    @pytest.mark.asyncio
    async def test_get_system_health(self):
        """Test comprehensive system health check"""
        health_status = await self.health_check.get_system_health()
        
        assert isinstance(health_status, dict)
        assert 'status' in health_status
        assert 'timestamp' in health_status
        assert 'components' in health_status
        assert 'summary' in health_status
        
        # Check that all expected components are tested
        expected_components = [
            'database', 'cache', 'connection_pool', 'monitoring',
            'external_services', 'system_resources', 'business_metrics'
        ]
        
        for component in expected_components:
            assert component in health_status['components']
    
    @pytest.mark.asyncio
    async def test_database_health_check(self):
        """Test database health check"""
        result = await self.health_check._check_database_health()
        
        assert isinstance(result, dict)
        assert 'status' in result
        assert 'message' in result
        assert 'metrics' in result
    
    @pytest.mark.asyncio
    async def test_cache_health_check(self):
        """Test cache health check"""
        result = await self.health_check._check_cache_health()
        
        assert isinstance(result, dict)
        assert 'status' in result
        assert 'message' in result
    
    def test_get_simple_health(self):
        """Test simple health check"""
        health = self.health_check.get_simple_health()
        
        assert isinstance(health, dict)
        assert 'status' in health
        assert 'timestamp' in health
    
    def test_health_cache_validity(self):
        """Test health check caching"""
        # First call should not use cache
        is_valid_before = self.health_check._is_health_cache_valid()
        assert is_valid_before is False
        
        # Set a cached result
        self.health_check.last_health_check = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Should now be valid
        is_valid_after = self.health_check._is_health_cache_valid()
        assert is_valid_after is True

class TestStructuredLogger:
    """Test structured logging functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.logger = StructuredLogger()
    
    def test_basic_logging(self):
        """Test basic logging methods"""
        # Test all log levels
        self.logger.debug("Debug message", extra={'key': 'value'})
        self.logger.info("Info message", extra={'key': 'value'})
        self.logger.warning("Warning message", extra={'key': 'value'})
        self.logger.error("Error message", extra={'key': 'value'})
        self.logger.critical("Critical message", extra={'key': 'value'})
        
        # Should not raise exceptions
        assert True
    
    def test_request_lifecycle_logging(self):
        """Test request lifecycle logging"""
        correlation_id = self.logger.log_request_start(
            method='POST',
            endpoint='/messages',
            user_id='user-123',
            tenant_id='tenant-456'
        )
        
        assert isinstance(correlation_id, str)
        assert len(correlation_id) > 0
        
        # Log request end
        self.logger.log_request_end(
            status_code=200,
            response_size=1024,
            correlation_id=correlation_id
        )
    
    def test_business_event_logging(self):
        """Test business event logging"""
        self.logger.log_business_event(
            event_type='message_sent',
            entity_type='message',
            entity_id='msg-123',
            action='created',
            metadata={'size': 1024, 'type': 'text'}
        )
    
    def test_security_event_logging(self):
        """Test security event logging"""
        self.logger.log_security_event(
            event_type='failed_login',
            severity='high',
            source_ip='*************',
            user_id='user-123',
            details={'attempts': 3}
        )
    
    def test_correlation_context_manager(self):
        """Test correlation context manager"""
        with correlation_context_manager(
            correlation_id='test-correlation-123',
            user_id='user-456',
            tenant_id='tenant-789'
        ) as corr_id:
            assert corr_id == 'test-correlation-123'
            
            # Log within context
            self.logger.info("Test message within context")

class TestHealthHandlers:
    """Test health check handlers"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mock_context = Mock()
        self.mock_context.aws_request_id = 'test-request-123'
    
    def test_health_check_handler(self):
        """Test basic health check handler"""
        event = {
            'httpMethod': 'GET',
            'path': '/health',
            'headers': {},
            'requestContext': {}
        }
        
        response = health_check_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] in [200, 503]
    
    def test_detailed_health_handler(self):
        """Test detailed health check handler"""
        event = {
            'httpMethod': 'GET',
            'path': '/health/detailed',
            'headers': {},
            'requestContext': {},
            'queryStringParameters': {'refresh': 'true'}
        }
        
        response = detailed_health_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert 'body' in response
        
        # Parse response body
        body = json.loads(response['body'])
        assert 'status' in body
        assert 'components' in body
    
    def test_status_handler(self):
        """Test status handler"""
        event = {
            'httpMethod': 'GET',
            'path': '/status',
            'headers': {},
            'requestContext': {}
        }
        
        response = status_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] == 200
    
    def test_metrics_handler_json(self):
        """Test metrics handler with JSON format"""
        event = {
            'httpMethod': 'GET',
            'path': '/metrics',
            'headers': {},
            'requestContext': {},
            'queryStringParameters': {'format': 'json'}
        }
        
        response = metrics_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] == 200
        
        # Check content type
        headers = response.get('headers', {})
        assert 'Content-Type' in headers
    
    def test_metrics_handler_prometheus(self):
        """Test metrics handler with Prometheus format"""
        event = {
            'httpMethod': 'GET',
            'path': '/metrics',
            'headers': {},
            'requestContext': {},
            'queryStringParameters': {'format': 'prometheus'}
        }
        
        response = metrics_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] == 200
        
        # Check content type for Prometheus
        headers = response.get('headers', {})
        assert headers.get('Content-Type') == 'text/plain; version=0.0.4'
    
    def test_readiness_handler(self):
        """Test readiness handler"""
        event = {
            'httpMethod': 'GET',
            'path': '/ready',
            'headers': {},
            'requestContext': {}
        }
        
        response = readiness_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] in [200, 503]
    
    def test_liveness_handler(self):
        """Test liveness handler"""
        event = {
            'httpMethod': 'GET',
            'path': '/live',
            'headers': {},
            'requestContext': {}
        }
        
        response = liveness_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] == 200
    
    def test_version_handler(self):
        """Test version handler"""
        event = {
            'httpMethod': 'GET',
            'path': '/version',
            'headers': {},
            'requestContext': {}
        }
        
        response = version_handler(event, self.mock_context)
        
        assert isinstance(response, dict)
        assert 'statusCode' in response
        assert response['statusCode'] == 200
        
        # Parse response body
        body = json.loads(response['body'])
        assert 'service' in body
        assert 'version' in body
        assert 'features' in body

class TestMonitoringIntegration:
    """Test monitoring system integration"""
    
    def test_monitoring_alerting_integration(self):
        """Test integration between monitoring and alerting"""
        # Record a high response time
        monitoring_service.record_response_time(
            endpoint='/test',
            method='GET',
            response_time_ms=3000,  # High response time
            status_code=200,
            tenant_id='tenant-123'
        )
        
        # This should trigger an alert in a real system
        # For testing, we just verify the metric was recorded
        metrics = monitoring_service.get_realtime_metrics()
        assert isinstance(metrics, dict)
    
    def test_health_monitoring_integration(self):
        """Test integration between health checks and monitoring"""
        # Get health status
        health = health_check_service.get_simple_health()
        
        # Get monitoring metrics
        metrics = monitoring_service.get_realtime_metrics()
        
        # Both should return valid data
        assert isinstance(health, dict)
        assert isinstance(metrics, dict)
    
    def test_end_to_end_monitoring_flow(self):
        """Test complete monitoring flow"""
        # 1. Record business event
        monitoring_service.record_message_sent(
            tenant_id='tenant-123',
            user_id='user-456',
            conversation_id='conv-789',
            message_type='text'
        )
        
        # 2. Record performance metric
        monitoring_service.record_response_time(
            endpoint='/messages',
            method='POST',
            response_time_ms=150,
            status_code=201,
            tenant_id='tenant-123'
        )
        
        # 3. Get real-time metrics
        metrics = monitoring_service.get_realtime_metrics()
        
        # 4. Check health
        health = health_check_service.get_simple_health()
        
        # All should work together
        assert isinstance(metrics, dict)
        assert isinstance(health, dict)


def test_monitoring_suite():
    """Run complete monitoring test suite"""
    print("🔍 Starting Monitoring Test Suite...")
    
    # Test monitoring service
    monitoring_tests = TestMonitoringService()
    monitoring_tests.setup_method()
    
    print("  📊 Testing monitoring service...")
    monitoring_tests.test_record_metric_basic()
    monitoring_tests.test_record_message_sent()
    monitoring_tests.test_get_realtime_metrics()
    
    # Test alerting service
    alerting_tests = TestAlertingService()
    alerting_tests.setup_method()
    
    print("  🚨 Testing alerting service...")
    alerting_tests.test_send_alert_basic()
    alerting_tests.test_send_performance_alert()
    alerting_tests.test_alert_suppression()
    
    # Test health check service
    health_tests = TestHealthCheckService()
    health_tests.setup_method()
    
    print("  🏥 Testing health check service...")
    health_tests.test_get_simple_health()
    health_tests.test_health_cache_validity()
    
    # Test structured logger
    logger_tests = TestStructuredLogger()
    logger_tests.setup_method()
    
    print("  📝 Testing structured logger...")
    logger_tests.test_basic_logging()
    logger_tests.test_request_lifecycle_logging()
    
    # Test health handlers
    handler_tests = TestHealthHandlers()
    handler_tests.setup_method()
    
    print("  🌐 Testing health handlers...")
    handler_tests.test_health_check_handler()
    handler_tests.test_status_handler()
    handler_tests.test_metrics_handler_json()
    
    # Test integration
    integration_tests = TestMonitoringIntegration()
    
    print("  🔗 Testing monitoring integration...")
    integration_tests.test_monitoring_alerting_integration()
    integration_tests.test_health_monitoring_integration()
    
    print("✅ Monitoring Test Suite Completed!")
    return True


if __name__ == '__main__':
    test_monitoring_suite()
