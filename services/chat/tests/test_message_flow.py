# services/chat/tests/test_message_flow.py
# Basic tests for real-time message flow

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.handlers.send_message import handler as send_message_handler
from src.handlers.get_messages import handler as get_messages_handler
from src.handlers.message_status import handler as message_status_handler
from src.services.message_service import MessageService
from src.services.hybrid_router import HybridRouter
from src.services.notification_service import NotificationService

class TestMessageFlow:
    """Test suite for real-time message flow"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_user_context = {
            'user_id': 'test-user-123',
            'tenant_id': 'test-tenant-123',
            'email': '<EMAIL>'
        }
        
        self.mock_conversation_id = 'conv-test-123'
        self.mock_message_id = 'msg-test-123'
        
        # Mock event structure
        self.base_event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'POST',
            'path': '/chat/messages',
            'headers': {'Authorization': 'Bearer test-token'}
        }
    
    @patch('src.handlers.send_message.extract_user_context')
    @patch('src.handlers.send_message.message_service')
    @patch('src.handlers.send_message.hybrid_router')
    @patch('src.handlers.send_message.notification_service')
    def test_send_message_success(self, mock_notification, mock_router, mock_message_service, mock_auth):
        """Test successful message sending"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        
        mock_message_service.create_message.return_value = (True, {
            'messageId': self.mock_message_id,
            'conversationId': self.mock_conversation_id,
            'content': 'Hello, agent!',
            'type': 'text',
            'userId': self.mock_user_context['user_id'],
            'timestamp': datetime.utcnow().isoformat()
        }, None)
        
        mock_router.route_message.return_value = (True, {
            'routing_method': 'websocket',
            'response_type': 'immediate'
        }, None)
        
        mock_notification.notify_new_message.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': self.mock_conversation_id,
                'content': 'Hello, agent!',
                'type': 'text'
            })
        }
        
        # Execute
        response = send_message_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 201
        body = json.loads(response['body'])
        assert body['message']['messageId'] == self.mock_message_id
        assert body['message']['content'] == 'Hello, agent!'
        assert body['routing']['routing_method'] == 'websocket'
        assert body['notification']['websocket_sent'] is True
        
        # Verify service calls
        mock_message_service.create_message.assert_called_once()
        mock_router.route_message.assert_called_once()
        mock_notification.notify_new_message.assert_called_once()
    
    @patch('src.handlers.send_message.extract_user_context')
    def test_send_message_missing_content(self, mock_auth):
        """Test message sending with missing content"""
        mock_auth.return_value = self.mock_user_context
        
        event = {
            **self.base_event,
            'body': json.dumps({
                'conversationId': self.mock_conversation_id
                # Missing content
            })
        }
        
        response = send_message_handler(event, None)
        
        assert response['statusCode'] == 400
        body = json.loads(response['body'])
        assert 'Missing required field: content' in body['error']
    
    @patch('src.handlers.get_messages.extract_user_context')
    @patch('src.handlers.get_messages.message_service')
    def test_get_messages_success(self, mock_message_service, mock_auth):
        """Test successful message retrieval"""
        mock_auth.return_value = self.mock_user_context
        
        mock_messages = [
            {
                'messageId': 'msg-1',
                'content': 'Hello',
                'timestamp': '2024-01-01T10:00:00Z'
            },
            {
                'messageId': 'msg-2',
                'content': 'How are you?',
                'timestamp': '2024-01-01T10:01:00Z'
            }
        ]
        
        mock_message_service.get_conversation_messages.return_value = (True, {
            'messages': mock_messages,
            'count': 2,
            'hasMore': False,
            'lastEvaluatedKey': None
        }, None)
        
        event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'GET',
            'path': f'/chat/messages/{self.mock_conversation_id}',
            'pathParameters': {'conversationId': self.mock_conversation_id},
            'queryStringParameters': {'limit': '50'}
        }
        
        response = get_messages_handler(event, None)
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['conversationId'] == self.mock_conversation_id
        assert len(body['messages']) == 2
        assert body['pagination']['count'] == 2
        assert body['pagination']['hasMore'] is False
    
    @patch('src.handlers.message_status.extract_user_context')
    @patch('src.handlers.message_status.message_service')
    @patch('src.handlers.message_status.notification_service')
    def test_update_message_status_success(self, mock_notification, mock_message_service, mock_auth):
        """Test successful message status update"""
        mock_auth.return_value = self.mock_user_context
        
        mock_message_service.update_message_status.return_value = (True, None)
        mock_message_service.get_message.return_value = (True, {
            'messageId': self.mock_message_id,
            'conversationId': self.mock_conversation_id,
            'status': 'read',
            'updatedAt': datetime.utcnow().isoformat()
        }, None)
        
        mock_notification.notify_message_status_update.return_value = (True, None)
        
        event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'PATCH',
            'path': f'/chat/messages/{self.mock_message_id}/status',
            'pathParameters': {'messageId': self.mock_message_id},
            'body': json.dumps({'status': 'read'})
        }
        
        response = message_status_handler(event, None)
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['messageId'] == self.mock_message_id
        assert body['status'] == 'read'
        assert body['notification']['sent'] is True
    
    def test_message_service_create_message(self):
        """Test MessageService create_message method"""
        service = MessageService()
        
        # Mock the database
        service.messages_db = Mock()
        service.conversation_states_db = Mock()
        
        # Mock successful creation
        service.messages_db.put_item.return_value = None
        service.conversation_states_db.update_item.return_value = None
        
        success, message_data, error = service.create_message(
            conversation_id=self.mock_conversation_id,
            user_id=self.mock_user_context['user_id'],
            tenant_id=self.mock_user_context['tenant_id'],
            content='Test message',
            message_type='text'
        )
        
        assert success is True
        assert message_data is not None
        assert message_data['content'] == 'Test message'
        assert message_data['type'] == 'text'
        assert error is None
    
    def test_hybrid_router_websocket_decision(self):
        """Test HybridRouter routing decision for WebSocket"""
        router = HybridRouter()
        
        # Mock database
        router.main_db = Mock()
        
        # Test simple text message (should use WebSocket)
        use_websocket, reason = router.should_use_websocket(
            message_type='text',
            content='Hello there!',
            conversation_context={'agentType': 'feedo'}
        )
        
        assert use_websocket is True
        assert 'WebSocket' in reason
    
    def test_hybrid_router_n8n_decision(self):
        """Test HybridRouter routing decision for N8N"""
        router = HybridRouter()
        
        # Test complex content (should use N8N)
        use_websocket, reason = router.should_use_websocket(
            message_type='text',
            content='Please analyze this complex data and generate a detailed report',
            conversation_context={'agentType': 'forecaster'}
        )
        
        assert use_websocket is False
        assert 'N8N' in reason or 'Forecaster' in reason
    
    def test_notification_service_new_message(self):
        """Test NotificationService new message notification"""
        service = NotificationService()
        
        # Mock lambda client
        service.lambda_client = Mock()
        service.broadcast_function_name = 'test-broadcast-function'
        
        message_data = {
            'messageId': self.mock_message_id,
            'content': 'Test notification',
            'type': 'text',
            'userId': self.mock_user_context['user_id'],
            'timestamp': datetime.utcnow().isoformat()
        }
        
        success, error = service.notify_new_message(
            conversation_id=self.mock_conversation_id,
            message_data=message_data,
            sender_user_id=self.mock_user_context['user_id'],
            tenant_id=self.mock_user_context['tenant_id']
        )
        
        assert success is True
        assert error is None


class TestMessageFlowIntegration:
    """Integration tests for message flow"""
    
    def test_end_to_end_message_flow(self):
        """Test complete message flow from send to status update"""
        # This would be a more complex integration test
        # that tests the entire flow in a controlled environment
        pass
    
    def test_websocket_broadcast_integration(self):
        """Test WebSocket broadcast integration"""
        # Test that messages are properly broadcast to connected users
        pass
    
    def test_n8n_routing_integration(self):
        """Test N8N routing integration"""
        # Test that complex messages are properly routed to N8N
        pass


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
