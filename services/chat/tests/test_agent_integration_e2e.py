# services/chat/tests/test_agent_integration_e2e.py
# End-to-end integration tests for agent-chat system

import pytest
import json
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.services.agent_integration_service import agent_integration_service
from src.services.hybrid_router import hybrid_router
from src.services.agent_typing_service import agent_typing_service
from src.handlers.agent_webhook import handler as webhook_handler

class TestAgentChatIntegrationE2E:
    """End-to-end integration tests for agent-chat system"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.conversation_id = 'conv-e2e-test-123'
        self.tenant_id = 'tenant-e2e-test-123'
        self.user_id = 'user-e2e-test-123'
        
        # Mock external dependencies
        self.mock_message_service = Mock()
        self.mock_notification_service = Mock()
        self.mock_lambda_client = Mock()
        
        # Setup successful responses
        self.mock_message_service.create_agent_message.return_value = (True, {
            'messageId': 'msg-123',
            'timestamp': datetime.utcnow().isoformat()
        }, None)
        
        self.mock_notification_service.notify_agent_response.return_value = (True, None)
    
    @patch('src.services.agent_integration_service.message_service')
    @patch('src.services.agent_integration_service.notification_service')
    @patch('src.services.agent_integration_service.hybrid_router')
    def test_complete_agent_response_flow(self, mock_router, mock_notification, mock_message):
        """Test complete flow from user message to agent response"""
        # Setup mocks
        mock_message.create_agent_message.return_value = (True, {
            'messageId': 'msg-123',
            'timestamp': datetime.utcnow().isoformat()
        }, None)
        mock_notification.notify_agent_response.return_value = (True, None)
        mock_router.handle_agent_response.return_value = (True, {
            'message_id': 'msg-123',
            'routing_method': 'agent_response'
        }, None)
        
        # Step 1: User sends message (triggers agent processing)
        user_message = {
            'userId': self.user_id,
            'content': 'Can you optimize my delivery routes for tomorrow?',
            'type': 'text',
            'attachments': []
        }
        
        success, trigger_result, error_msg = agent_integration_service.trigger_agent_processing(
            conversation_id=self.conversation_id,
            user_message=user_message,
            tenant_id=self.tenant_id
        )
        
        assert success is True
        assert trigger_result['agent_type'] == 'feedo'  # Should detect logistics optimization
        assert trigger_result['processing_started'] is True
        
        # Step 2: Simulate N8N webhook response
        webhook_data = {
            'conversationId': self.conversation_id,
            'agentId': 'agent-feedo',
            'agentName': 'Feedo',
            'agentType': 'feedo',
            'response': {
                'content': 'I\'ve analyzed your delivery routes and found 3 optimization opportunities that could save 15% in travel time.',
                'type': 'text',
                'confidence': 0.92,
                'metadata': {
                    'processingTime': 3.2,
                    'optimizations': ['route_consolidation', 'traffic_avoidance', 'fuel_efficiency']
                }
            },
            'requestId': 'n8n-req-456',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        success, response_result, error_msg = agent_integration_service.process_incoming_agent_response(webhook_data)
        
        assert success is True
        assert response_result['routing_method'] == 'agent_response'
        assert response_result['agent_type'] == 'feedo'
        
        # Verify router was called
        mock_router.handle_agent_response.assert_called_once()
    
    def test_agent_typing_simulation_lifecycle(self):
        """Test complete agent typing simulation lifecycle"""
        agent_id = 'agent-feedo'
        agent_type = 'feedo'
        
        # Step 1: Start typing simulation
        success, error_msg = agent_typing_service.start_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id=agent_id,
            agent_type=agent_type,
            tenant_id=self.tenant_id,
            estimated_processing_time=5.0
        )
        
        assert success is True
        assert error_msg is None
        
        # Verify session was created
        session_key = f"{self.conversation_id}:{agent_id}"
        active_sessions = agent_typing_service.get_active_sessions()
        assert session_key in active_sessions
        
        session = active_sessions[session_key]
        assert session['conversation_id'] == self.conversation_id
        assert session['agent_id'] == agent_id
        assert session['agent_type'] == agent_type
        assert session['active'] is True
        
        # Step 2: Check typing stats
        stats = agent_typing_service.get_typing_stats()
        assert stats['active_sessions'] == 1
        assert 'feedo' in stats['sessions_by_agent_type']
        
        # Step 3: Stop typing simulation
        success, error_msg = agent_typing_service.stop_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id=agent_id,
            tenant_id=self.tenant_id
        )
        
        assert success is True
        assert error_msg is None
        
        # Verify session was removed
        active_sessions = agent_typing_service.get_active_sessions()
        assert session_key not in active_sessions
    
    @patch('src.handlers.agent_webhook.validate_webhook_signature')
    @patch('src.handlers.agent_webhook.agent_response_processor')
    def test_webhook_to_chat_integration(self, mock_processor, mock_validate):
        """Test webhook handler integration with chat system"""
        # Setup mocks
        mock_validate.return_value = True
        mock_processor.process_agent_response.return_value = (True, {
            'messageId': 'msg-webhook-123',
            'conversationId': self.conversation_id,
            'agentId': 'agent-forecaster',
            'timestamp': datetime.utcnow().isoformat(),
            'processingTime': 4.1,
            'notificationSent': True
        }, None)
        
        # Webhook payload
        webhook_payload = {
            'conversationId': self.conversation_id,
            'agentId': 'agent-forecaster',
            'agentName': 'Forecaster',
            'agentType': 'forecaster',
            'response': {
                'content': 'Based on historical data, I predict a 20% increase in demand next quarter.',
                'type': 'structured',
                'confidence': 0.87,
                'metadata': {
                    'forecastPeriod': '2024-Q2',
                    'dataPoints': 1250,
                    'accuracy': 0.94
                }
            },
            'requestId': 'n8n-forecast-789',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Create webhook event
        event = {
            'requestContext': {'requestId': 'webhook-test-123'},
            'httpMethod': 'POST',
            'path': '/chat/webhook/agent-response',
            'headers': {'X-N8N-Signature': 'sha256=valid-signature'},
            'body': json.dumps(webhook_payload)
        }
        
        # Execute webhook handler
        response = webhook_handler(event, None)
        
        # Verify response
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['status'] == 'success'
        assert body['conversationId'] == self.conversation_id
        assert body['agentId'] == 'agent-forecaster'
        assert 'messageId' in body
        
        # Verify processor was called with correct parameters
        mock_processor.process_agent_response.assert_called_once()
        call_args = mock_processor.process_agent_response.call_args
        assert call_args[1]['conversation_id'] == self.conversation_id
        assert call_args[1]['agent_id'] == 'agent-forecaster'
        assert call_args[1]['agent_type'] == 'forecaster'
    
    def test_multiple_agents_typing_concurrently(self):
        """Test multiple agents typing in same conversation"""
        agents = [
            ('agent-feedo', 'feedo'),
            ('agent-forecaster', 'forecaster')
        ]
        
        # Start typing for both agents
        for agent_id, agent_type in agents:
            success, error_msg = agent_typing_service.start_agent_typing_simulation(
                conversation_id=self.conversation_id,
                agent_id=agent_id,
                agent_type=agent_type,
                tenant_id=self.tenant_id,
                estimated_processing_time=3.0
            )
            assert success is True
        
        # Verify both sessions are active
        active_sessions = agent_typing_service.get_active_sessions()
        assert len(active_sessions) == 2
        
        # Check stats
        stats = agent_typing_service.get_typing_stats()
        assert stats['active_sessions'] == 2
        assert stats['sessions_by_agent_type']['feedo'] == 1
        assert stats['sessions_by_agent_type']['forecaster'] == 1
        
        # Stop one agent
        success, error_msg = agent_typing_service.stop_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id='agent-feedo',
            tenant_id=self.tenant_id
        )
        assert success is True
        
        # Verify only one session remains
        active_sessions = agent_typing_service.get_active_sessions()
        assert len(active_sessions) == 1
        
        remaining_session = list(active_sessions.values())[0]
        assert remaining_session['agent_id'] == 'agent-forecaster'
        
        # Clean up
        agent_typing_service.stop_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id='agent-forecaster',
            tenant_id=self.tenant_id
        )
    
    def test_agent_type_detection_accuracy(self):
        """Test accuracy of agent type detection"""
        test_cases = [
            # Forecaster keywords
            ("Can you forecast demand for next month?", "forecaster"),
            ("I need trend analysis for our products", "forecaster"),
            ("Predict future sales patterns", "forecaster"),
            ("What's the market projection?", "forecaster"),
            
            # Feedo (logistics) keywords - default
            ("Optimize my delivery routes", "feedo"),
            ("How can I reduce shipping costs?", "feedo"),
            ("Improve warehouse efficiency", "feedo"),
            ("General logistics question", "feedo"),
            ("Random question without keywords", "feedo")  # Default
        ]
        
        for content, expected_agent_type in test_cases:
            detected_type = agent_integration_service._determine_agent_type(content)
            assert detected_type == expected_agent_type, f"Failed for content: '{content}'"
    
    def test_processing_time_estimation_ranges(self):
        """Test processing time estimation for different content types"""
        test_cases = [
            # (content, agent_type, expected_min, expected_max)
            ("Hi", "feedo", 1.0, 4.0),  # Simple content
            ("Can you help with logistics?", "feedo", 2.0, 5.0),  # Medium content
            ("Please analyze our entire supply chain network across multiple regions and provide detailed optimization recommendations with cost-benefit analysis and implementation timeline", "feedo", 4.0, 15.0),  # Complex content
            ("Forecast demand", "forecaster", 3.0, 8.0),  # Forecaster base time
            ("Generate comprehensive forecast report with data analysis", "forecaster", 5.0, 15.0)  # Complex forecaster
        ]
        
        for content, agent_type, expected_min, expected_max in test_cases:
            estimated_time = agent_integration_service._estimate_processing_time(content, agent_type)
            assert expected_min <= estimated_time <= expected_max, \
                f"Time {estimated_time} not in range [{expected_min}, {expected_max}] for: '{content}'"
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery scenarios"""
        # Test invalid agent type
        with pytest.raises(Exception):
            agent_integration_service.trigger_agent_processing(
                conversation_id=self.conversation_id,
                user_message={'userId': self.user_id, 'content': 'test'},
                tenant_id=self.tenant_id,
                agent_type='invalid_agent_type'
            )
        
        # Test stopping non-existent typing session
        success, error_msg = agent_typing_service.stop_agent_typing_simulation(
            conversation_id='non-existent-conv',
            agent_id='non-existent-agent',
            tenant_id=self.tenant_id
        )
        # Should succeed gracefully
        assert success is True
        
        # Test duplicate typing session start
        agent_id = 'agent-test'
        agent_type = 'feedo'
        
        # Start first session
        success1, _ = agent_typing_service.start_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id=agent_id,
            agent_type=agent_type,
            tenant_id=self.tenant_id
        )
        assert success1 is True
        
        # Start second session (should replace first)
        success2, _ = agent_typing_service.start_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id=agent_id,
            agent_type=agent_type,
            tenant_id=self.tenant_id
        )
        assert success2 is True
        
        # Should still have only one session
        active_sessions = agent_typing_service.get_active_sessions()
        session_count = sum(1 for key in active_sessions.keys() if agent_id in key)
        assert session_count == 1
        
        # Clean up
        agent_typing_service.stop_agent_typing_simulation(
            conversation_id=self.conversation_id,
            agent_id=agent_id,
            tenant_id=self.tenant_id
        )


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
