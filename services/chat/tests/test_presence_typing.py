# services/chat/tests/test_presence_typing.py
# Tests for presence and typing functionality

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Mock shared imports
import sys
from unittest.mock import MagicMock
sys.modules['shared'] = MagicMock()
sys.modules['shared.logger'] = MagicMock()
sys.modules['shared.responses'] = MagicMock()
sys.modules['shared.auth'] = MagicMock()
sys.modules['shared.database'] = MagicMock()

# Import after mocking
from src.handlers.presence import (
    connect_handler, disconnect_handler, get_user_presence_handler,
    update_user_status_handler, get_tenant_presence_handler
)
from src.services.presence_service import PresenceService
from src.services.realtime_service import RealtimeService

class TestPresenceHandlers:
    """Test suite for presence handlers"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_user_context = {
            'user_id': 'test-user-123',
            'tenant_id': 'test-tenant-123',
            'email': '<EMAIL>'
        }
        
        self.base_event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'POST',
            'path': '/chat/presence/connect',
            'headers': {'Authorization': 'Bearer test-token'}
        }
    
    @patch('src.handlers.presence.extract_user_context')
    @patch('src.handlers.presence.presence_service')
    def test_connect_handler_success(self, mock_presence_service, mock_auth):
        """Test successful user connection"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_presence_service.handle_user_connect.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'body': json.dumps({
                'userId': self.mock_user_context['user_id'],
                'connectionId': 'conn-123',
                'metadata': {'userAgent': 'test-browser'}
            })
        }
        
        # Execute
        response = connect_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['userId'] == self.mock_user_context['user_id']
        assert body['status'] == 'online'
        assert body['connectionId'] == 'conn-123'
        
        # Verify service call
        mock_presence_service.handle_user_connect.assert_called_once()
    
    @patch('src.handlers.presence.extract_user_context')
    @patch('src.handlers.presence.presence_service')
    def test_disconnect_handler_success(self, mock_presence_service, mock_auth):
        """Test successful user disconnection"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_presence_service.handle_user_disconnect.return_value = (True, None)
        
        # Test event
        event = {
            **self.base_event,
            'path': '/chat/presence/disconnect',
            'body': json.dumps({
                'userId': self.mock_user_context['user_id'],
                'connectionId': 'conn-123'
            })
        }
        
        # Execute
        response = disconnect_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['userId'] == self.mock_user_context['user_id']
        assert body['connectionId'] == 'conn-123'
        
        # Verify service call
        mock_presence_service.handle_user_disconnect.assert_called_once()
    
    @patch('src.handlers.presence.extract_user_context')
    @patch('src.handlers.presence.presence_service')
    def test_get_user_presence_success(self, mock_presence_service, mock_auth):
        """Test successful get user presence"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_presence_data = {
            'status': 'online',
            'lastSeen': datetime.utcnow().isoformat(),
            'connectionCount': 2
        }
        mock_presence_service.get_user_presence.return_value = (True, mock_presence_data, None)
        
        # Test event
        event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'GET',
            'path': '/chat/presence/user-123',
            'pathParameters': {'userId': 'user-123'}
        }
        
        # Execute
        response = get_user_presence_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['userId'] == 'user-123'
        assert body['presence']['status'] == 'online'
        assert body['presence']['connectionCount'] == 2
    
    @patch('src.handlers.presence.extract_user_context')
    @patch('src.handlers.presence.presence_service')
    def test_update_user_status_success(self, mock_presence_service, mock_auth):
        """Test successful user status update"""
        # Setup mocks
        mock_auth.return_value = self.mock_user_context
        mock_presence_service.update_user_presence.return_value = (True, None)
        
        # Test event
        event = {
            'requestContext': {'requestId': 'test-request-123'},
            'httpMethod': 'PATCH',
            'path': f'/chat/presence/{self.mock_user_context["user_id"]}',
            'pathParameters': {'userId': self.mock_user_context['user_id']},
            'body': json.dumps({'status': 'away'})
        }
        
        # Execute
        response = update_user_status_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['userId'] == self.mock_user_context['user_id']
        assert body['status'] == 'away'
    
    @patch('src.handlers.presence.extract_user_context')
    def test_connect_handler_access_denied(self, mock_auth):
        """Test access denied when trying to update other user's presence"""
        mock_auth.return_value = self.mock_user_context
        
        # Test event with different user ID
        event = {
            **self.base_event,
            'body': json.dumps({
                'userId': 'other-user-123',
                'connectionId': 'conn-123'
            })
        }
        
        # Execute
        response = connect_handler(event, None)
        
        # Assertions
        assert response['statusCode'] == 403
        body = json.loads(response['body'])
        assert 'Access denied' in body['error']


class TestPresenceService:
    """Test suite for PresenceService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = PresenceService()
        self.service.presence_db = Mock()
        self.service.notification_service = Mock()
    
    def test_handle_user_connect(self):
        """Test user connection handling"""
        # Mock database operations
        self.service.presence_db.get_item.return_value = None  # No existing presence
        self.service.presence_db.put_item.return_value = None
        
        # Execute
        success, error_msg = self.service.handle_user_connect(
            user_id='user-123',
            tenant_id='tenant-123',
            connection_count_delta=1
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        self.service.presence_db.put_item.assert_called_once()
    
    def test_handle_user_disconnect(self):
        """Test user disconnection handling"""
        # Mock existing presence
        existing_presence = {
            'userId': 'user-123',
            'status': 'online',
            'connectionCount': 2
        }
        self.service.presence_db.get_item.return_value = existing_presence
        self.service.presence_db.update_item.return_value = None
        
        # Execute
        success, error_msg = self.service.handle_user_disconnect(
            user_id='user-123',
            tenant_id='tenant-123',
            connection_count_delta=-1
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        self.service.presence_db.update_item.assert_called_once()
    
    def test_update_user_presence(self):
        """Test user presence status update"""
        # Mock existing presence
        existing_presence = {
            'userId': 'user-123',
            'status': 'online'
        }
        self.service.presence_db.get_item.return_value = existing_presence
        self.service.presence_db.update_item.return_value = None
        
        # Execute
        success, error_msg = self.service.update_user_presence(
            user_id='user-123',
            tenant_id='tenant-123',
            status='away'
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        self.service.presence_db.update_item.assert_called_once()
    
    def test_is_user_online(self):
        """Test online status check"""
        # Mock online user
        online_presence = {
            'userId': 'user-123',
            'status': 'online',
            'connectionCount': 1,
            'lastSeen': datetime.utcnow().isoformat()
        }
        self.service.presence_db.get_item.return_value = online_presence
        
        # Execute
        is_online = self.service.is_user_online('user-123', 'tenant-123')
        
        # Assertions
        assert is_online is True
    
    def test_is_user_offline(self):
        """Test offline status check"""
        # Mock offline user
        offline_presence = {
            'userId': 'user-123',
            'status': 'offline',
            'connectionCount': 0,
            'lastSeen': (datetime.utcnow() - timedelta(minutes=10)).isoformat()
        }
        self.service.presence_db.get_item.return_value = offline_presence
        
        # Execute
        is_online = self.service.is_user_online('user-123', 'tenant-123')
        
        # Assertions
        assert is_online is False


class TestRealtimeService:
    """Test suite for RealtimeService"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.service = RealtimeService()
        self.service.lambda_client = Mock()
    
    @patch('src.services.realtime_service.presence_service')
    def test_get_conversation_realtime_info(self, mock_presence_service):
        """Test getting conversation realtime info"""
        # Mock presence data
        mock_presence_service.get_user_presence.return_value = (True, {
            'status': 'online',
            'lastSeen': datetime.utcnow().isoformat(),
            'connectionCount': 1
        }, None)
        mock_presence_service.is_user_online.return_value = True
        
        # Mock conversation participants
        with patch.object(self.service, '_get_conversation_participants', return_value=['user1', 'user2']):
            with patch.object(self.service, '_get_typing_indicators', return_value={'typingUsers': [], 'count': 0}):
                # Execute
                success, realtime_info, error_msg = self.service.get_conversation_realtime_info(
                    conversation_id='conv-123',
                    user_id='user-123',
                    tenant_id='tenant-123'
                )
        
        # Assertions
        assert success is True
        assert error_msg is None
        assert realtime_info['conversationId'] == 'conv-123'
        assert 'presence' in realtime_info
        assert 'typing' in realtime_info
        assert 'activity' in realtime_info
    
    @patch('src.services.realtime_service.presence_service')
    def test_notify_user_activity(self, mock_presence_service):
        """Test user activity notification"""
        # Mock presence update
        mock_presence_service.update_user_presence.return_value = (True, None)
        
        # Execute
        success, error_msg = self.service.notify_user_activity(
            user_id='user-123',
            tenant_id='tenant-123',
            activity_type='message_sent',
            conversation_id='conv-123'
        )
        
        # Assertions
        assert success is True
        assert error_msg is None
        mock_presence_service.update_user_presence.assert_called_once()


class TestTypingIntegration:
    """Test suite for typing indicators integration"""
    
    def test_typing_message_validation(self):
        """Test typing message validation"""
        from src.utils.validation import WebSocketValidator
        
        # Valid typing message
        valid_message = {
            'conversationId': 'conv-123',
            'isTyping': True,
            'duration': 30
        }
        
        is_valid, error_msg = WebSocketValidator.validate_typing_message(valid_message)
        assert is_valid is True
        assert error_msg is None
        
        # Invalid typing message (missing conversationId)
        invalid_message = {
            'isTyping': True
        }
        
        is_valid, error_msg = WebSocketValidator.validate_typing_message(invalid_message)
        assert is_valid is False
        assert 'conversationId' in error_msg
    
    def test_typing_duration_validation(self):
        """Test typing duration validation"""
        from src.utils.validation import WebSocketValidator
        
        # Invalid duration (too short)
        message = {
            'conversationId': 'conv-123',
            'isTyping': True,
            'duration': 2
        }
        
        is_valid, error_msg = WebSocketValidator.validate_typing_message(message)
        assert is_valid is False
        assert 'duration' in error_msg
        
        # Invalid duration (too long)
        message['duration'] = 400
        
        is_valid, error_msg = WebSocketValidator.validate_typing_message(message)
        assert is_valid is False
        assert 'duration' in error_msg


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
