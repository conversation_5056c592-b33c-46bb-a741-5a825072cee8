# 💬 **CHAT SERVICE - DOCUMENTACIÓN TÉCNICA COMPLETA**

## **📋 ÍNDICE**
1. [Resumen Ejecutivo](#resumen-ejecutivo)
2. [Arquitectura del Servicio](#arquitectura-del-servicio)
3. [Componentes Principales](#componentes-principales)
4. [Handlers y Endpoints](#handlers-y-endpoints)
5. [Servicios Internos](#servicios-internos)
6. [Modelos de Datos](#modelos-de-datos)
7. [<PERSON>lu<PERSON> de <PERSON>](#flujo-de-datos)
8. [Dependencias](#dependencias)
9. [Configuración](#configuración)
10. [Seguridad](#seguridad)
11. [Monitoreo y Logging](#monitoreo-y-logging)
12. [Deployment](#deployment)

---

## **📊 RESUMEN EJECUTIVO**

### **🎯 Propósito**
El Chat Service es el núcleo de gestión de mensajería del sistema **agent-scl**. Proporciona infraestructura completa para:
- Procesamiento de mensajes de chat
- Gestión de conversaciones
- Integración con agentes IA
- Búsqueda y analytics de mensajes
- File sharing y previews
- Gestión de presencia y typing indicators

### **🏗️ Arquitectura**
- **Patrón**: Domain-Driven Design con CQRS
- **Deployment**: AWS Lambda + API Gateway REST
- **Storage**: DynamoDB (tabla unificada) + S3 (archivos)
- **Integration**: Message Orchestrator, WebSocket Service, Agent Service

### **📈 Métricas Clave**
- **Latencia**: < 200ms para operaciones de mensaje
- **Throughput**: 1,000+ mensajes/segundo
- **Disponibilidad**: 99.9% SLA
- **Escalabilidad**: Auto-scaling basado en carga

---

## **🏗️ ARQUITECTURA DEL SERVICIO**

### **📦 Estructura de Directorios**
```
services/chat/
├── src/
│   ├── handlers/           # Lambda handlers para endpoints
│   ├── services/          # Lógica de negocio
│   ├── models/            # Modelos de datos específicos
│   ├── validators/        # Validación de datos
│   ├── middleware/        # Middleware personalizado
│   ├── config/            # Configuración y dependencias
│   ├── common/            # Utilidades compartidas
│   └── utils/             # Utilidades específicas
├── tests/                 # Tests unitarios e integración
├── scripts/               # Scripts de benchmark y utilidades
├── serverless.yml         # Configuración de deployment
└── requirements.txt       # Dependencias Python
```

### **🔄 Patrón Arquitectónico**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Message       │    │   Chat Service   │    │   WebSocket     │
│   Orchestrator  │◄──►│   (Core Logic)   │◄──►│   Service       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Agent         │    │   DynamoDB       │    │   S3 Bucket     │
│   Service       │◄──►│   (Unified)      │◄──►│   (Files)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## **🧩 COMPONENTES PRINCIPALES**

### **1. 💬 Message Service**
**Archivo**: `src/services/message_service.py`

**Responsabilidades**:
- CRUD completo de mensajes
- Gestión de conversaciones
- Paginación y filtrado
- Validación de contenido
- Integración con search indexing

**Métodos Principales**:
```python
create_message(conversation_id, user_id, content, attachments)
get_message_by_id(message_id, tenant_id)
list_conversation_messages(conversation_id, limit, cursor)
update_message(message_id, updates)
delete_message(message_id, user_id)
search_messages(query, filters)
```

### **2. 🤖 Agent Integration Service**
**Archivo**: `src/services/agent_integration_service.py`

**Responsabilidades**:
- Coordinación con Agent Service
- Procesamiento de respuestas de agentes
- Routing inteligente de mensajes
- Gestión de contexto de conversación

**Métodos Principales**:
```python
process_agent_response(agent_response, conversation_context)
route_message_to_agent(message, agent_config)
update_agent_context(conversation_id, context_data)
handle_agent_typing(agent_id, conversation_id, status)
```

### **3. 📁 File Upload Service**
**Archivo**: `src/services/file_upload_service.py`

**Responsabilidades**:
- Upload de archivos a S3
- Generación de URLs presignadas
- Validación de tipos de archivo
- Gestión de metadatos de archivos

### **4. 🔍 Search Service**
**Archivo**: `src/services/search_service.py`

**Responsabilidades**:
- Indexación de mensajes para búsqueda
- Búsqueda full-text
- Filtros avanzados
- Ranking de resultados

### **5. 👁️ Presence Service**
**Archivo**: `src/services/presence_service.py`

**Responsabilidades**:
- Gestión de estado online/offline
- Typing indicators
- Last seen timestamps
- Coordinación con WebSocket Service

---

## **🎯 HANDLERS Y ENDPOINTS**

### **Core Message Handlers**

#### **1. 📨 Process Message Handler**
**Archivo**: `src/handlers/process_message.py`
**Endpoint**: `POST /chat/messages/process`
```python
# Funcionalidad:
- Recibe mensajes del Message Orchestrator
- Valida estructura y contenido
- Persiste mensaje en DynamoDB
- Coordina entrega en tiempo real
- Actualiza índices de búsqueda
```

#### **2. 🤖 Process Agent Response Handler**
**Archivo**: `src/handlers/process_agent_response.py`
**Endpoint**: `POST /chat/agent-responses`
```python
# Funcionalidad:
- Procesa respuestas de agentes IA
- Formatea contenido para usuarios
- Gestiona attachments y media
- Coordina notificaciones en tiempo real
```

#### **3. 📋 Get Messages Handler**
**Archivo**: `src/handlers/get_messages.py`
**Endpoint**: `GET /chat/conversations/{conversationId}/messages`
```python
# Funcionalidad:
- Recupera mensajes de conversación
- Paginación eficiente
- Filtrado por fecha/tipo
- Incluye metadatos de presencia
```

### **File Sharing Handlers**

#### **4. 📁 File Sharing Handler**
**Archivo**: `src/handlers/file_sharing.py`
**Endpoint**: `POST /chat/files/upload`
```python
# Funcionalidad:
- Genera URLs presignadas para S3
- Valida tipos y tamaños de archivo
- Crea registros de metadatos
- Integra con message flow
```

#### **5. 👁️ File Preview Handler**
**Archivo**: `src/handlers/file_preview.py`
**Endpoint**: `GET /chat/files/{fileId}/preview`
```python
# Funcionalidad:
- Genera previews de archivos
- Optimización de imágenes
- Extracción de metadatos
- Caching inteligente
```

### **Search and Analytics Handlers**

#### **6. 🔍 Search Handler**
**Archivo**: `src/handlers/search.py`
**Endpoint**: `GET /chat/search`
```python
# Funcionalidad:
- Búsqueda full-text en mensajes
- Filtros avanzados (fecha, usuario, tipo)
- Ranking por relevancia
- Paginación de resultados
```

#### **7. 📊 Analytics Handler**
**Archivo**: `src/handlers/analytics.py`
**Endpoint**: `GET /chat/analytics`
```python
# Funcionalidad:
- Métricas de uso de chat
- Estadísticas de conversaciones
- Análisis de engagement
- Reportes de actividad
```

---

## **🔧 SERVICIOS INTERNOS**

### **1. 📊 Analytics Service**
**Archivo**: `src/services/analytics_service.py`
- Tracking de métricas de chat
- Análisis de patrones de uso
- Generación de reportes
- Integración con CloudWatch

### **2. 🚨 Alerting Service**
**Archivo**: `src/services/alerting_service.py`
- Monitoreo de performance
- Detección de anomalías
- Alertas automáticas
- Escalación de incidentes

### **3. 💾 Cache Service**
**Archivo**: `src/services/cache_service.py`
- Caching de mensajes frecuentes
- Optimización de queries
- TTL inteligente
- Invalidación selectiva

### **4. 🏥 Health Check Service**
**Archivo**: `src/services/health_check_service.py`
- Monitoreo de dependencias
- Health checks automáticos
- Status de servicios externos
- Métricas de disponibilidad

### **5. 📡 Realtime Service**
**Archivo**: `src/services/realtime_service.py`
- Coordinación con WebSocket Service
- Broadcasting de eventos
- Gestión de notificaciones
- Sincronización de estado

---

## **📊 MODELOS DE DATOS**

### **1. 💬 Chat Message**
**Archivo**: `src/models/chat_message.py`
```python
@dataclass
class ChatMessage:
    message_id: str
    conversation_id: str
    tenant_id: str
    sender_id: str
    sender_type: str  # 'user' | 'agent'
    content: str
    message_type: str  # 'text' | 'file' | 'image' | 'system'
    attachments: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    status: str  # 'sent' | 'delivered' | 'read'
    metadata: Dict[str, Any]
```

### **2. 👁️ User Presence**
**Archivo**: `src/models/user_presence.py`
```python
@dataclass
class UserPresence:
    user_id: str
    tenant_id: str
    status: str  # 'online' | 'offline' | 'away'
    last_seen: datetime
    typing_in: Optional[str]  # conversation_id if typing
    metadata: Dict[str, Any]
```

### **3. 🗄️ DynamoDB Schema**
**Tabla Unificada**: `agent-scl-dev`

**Message Records**:
```
PK: "MESSAGE#{message_id}"
SK: "METADATA"
GSI1PK: "CONVERSATION#{conversation_id}"
GSI1SK: "TIMESTAMP#{created_at}"
GSI2PK: "TENANT#{tenant_id}"
GSI2SK: "MESSAGE#{message_id}"
```

**Conversation Records**:
```
PK: "CONVERSATION#{conversation_id}"
SK: "METADATA"
GSI1PK: "TENANT#{tenant_id}"
GSI1SK: "CONVERSATION#{conversation_id}"
```

**File Records**:
```
PK: "FILE#{file_id}"
SK: "METADATA"
GSI1PK: "MESSAGE#{message_id}"
GSI1SK: "FILE#{file_id}"
```

---

## **🔄 FLUJO DE DATOS**

### **1. 💬 Flujo de Procesamiento de Mensajes**
```mermaid
sequenceDiagram
    participant Orchestrator as Message Orchestrator
    participant ChatAPI as Chat Service API
    participant ProcessHandler as Process Message Handler
    participant MessageService as Message Service
    participant DynamoDB
    parameter RealtimeService as Realtime Service
    participant WebSocket as WebSocket Service
    participant SearchService as Search Service

    Orchestrator->>ChatAPI: POST /chat/messages/process
    ChatAPI->>ProcessHandler: Route to handler
    ProcessHandler->>ProcessHandler: Validate message structure
    ProcessHandler->>MessageService: create_message()
    MessageService->>DynamoDB: Store message record
    DynamoDB-->>MessageService: Message stored
    MessageService-->>ProcessHandler: Message created

    ProcessHandler->>RealtimeService: notify_realtime_delivery()
    RealtimeService->>WebSocket: POST /websocket/orchestrator/broadcast
    WebSocket-->>RealtimeService: Delivery confirmation

    ProcessHandler->>SearchService: index_message()
    SearchService->>DynamoDB: Update search indexes

    ProcessHandler-->>ChatAPI: Success response
    ChatAPI-->>Orchestrator: 200 OK
```

### **2. 🤖 Flujo de Respuesta de Agentes**
```mermaid
sequenceDiagram
    participant Agent as Agent Service
    participant ChatAPI as Chat Service API
    participant AgentHandler as Process Agent Response Handler
    participant AgentService as Agent Integration Service
    participant MessageService as Message Service
    participant DynamoDB
    participant RealtimeService as Realtime Service
    participant WebSocket as WebSocket Service

    Agent->>ChatAPI: POST /chat/agent-responses
    ChatAPI->>AgentHandler: Route to handler
    AgentHandler->>AgentService: process_agent_response()
    AgentService->>AgentService: Format agent content
    AgentService->>MessageService: create_message(sender_type='agent')
    MessageService->>DynamoDB: Store agent message
    DynamoDB-->>MessageService: Message stored

    AgentService->>RealtimeService: notify_agent_response()
    RealtimeService->>WebSocket: Broadcast to conversation participants
    WebSocket-->>RealtimeService: Delivery confirmation

    AgentHandler-->>ChatAPI: Success response
    ChatAPI-->>Agent: 200 OK
```

### **3. 📁 Flujo de File Sharing**
```mermaid
sequenceDiagram
    participant Client
    participant ChatAPI as Chat Service API
    participant FileHandler as File Sharing Handler
    participant FileService as File Upload Service
    participant S3
    participant MessageService as Message Service
    participant DynamoDB

    Client->>ChatAPI: POST /chat/files/upload
    ChatAPI->>FileHandler: Route to handler
    FileHandler->>FileService: generate_presigned_url()
    FileService->>S3: Generate presigned URL
    S3-->>FileService: Presigned URL
    FileService-->>FileHandler: Upload URL + metadata

    FileHandler->>DynamoDB: Store file metadata
    FileHandler-->>ChatAPI: Upload URL response
    ChatAPI-->>Client: Presigned URL + file_id

    Client->>S3: Upload file directly
    S3-->>Client: Upload complete

    Client->>ChatAPI: POST /chat/messages/process (with file attachment)
    ChatAPI->>MessageService: create_message(attachments=[file_id])
    MessageService->>DynamoDB: Store message with file reference
```

---

## **🔗 DEPENDENCIAS**

### **📚 Shared Layer Dependencies**
```python
# Modelos Unificados
from shared.models.conversation import Conversation
from shared.models.message import Message
from shared.models.user import User
from shared.models.tenant import Tenant

# Servicios Compartidos
from shared.database import DynamoDBClient
from shared.logger import lambda_logger, log_business_operation
from shared.auth import AuthContext, validate_jwt_token
from shared.config import get_settings, get_database_config
from shared.exceptions import ValidationError, AuthenticationError
from shared.utils import parse_request_body, validate_required_fields
from shared.responses import APIResponse
```

### **🌐 External Service Dependencies**
```yaml
# Message Orchestrator
ORCHESTRATOR_SERVICE_URL: agent-scl-orchestrator-dev

# WebSocket Service (para notificaciones en tiempo real)
WEBSOCKET_SERVICE_URL: agent-scl-websocket-dev

# Agent Service (para coordinación con agentes)
AGENT_SERVICE_URL: agent-scl-agent-dev

# Auth Service (para validación de tokens)
JWT_SECRET_NAME: agent-scl/dev/jwt-secret
```

### **☁️ AWS Service Dependencies**
```yaml
# API Gateway REST
- REST API Gateway
- Custom Domain (opcional)
- Route53 (para DNS)

# Lambda Functions
- Runtime: Python 3.11
- Memory: 256-512 MB
- Timeout: 30 seconds

# DynamoDB
- Table: agent-scl-dev (unified table)
- GSI1: ConversationIndex (conversation_id, timestamp)
- GSI2: TenantIndex (tenant_id, message_id)
- GSI3: SearchIndex (tenant_id, search_terms)

# S3 Bucket
- Bucket: agent-scl-files-dev
- Versioning: enabled
- Encryption: AES-256

# Secrets Manager
- JWT Secret: agent-scl/dev/jwt-secret

# CloudWatch
- Logs: /aws/lambda/agent-scl-chat-dev-*
- Metrics: Custom metrics for messages
- Alarms: Message rate, error rate
```

---

## **⚙️ CONFIGURACIÓN**

### **🔧 Environment Variables**
```yaml
# Core Configuration
STAGE: dev
REGION: us-east-1
PROJECT_NAME: agent-scl
DYNAMODB_TABLE: agent-scl-dev

# Security
JWT_SECRET_NAME: agent-scl/dev/jwt-secret

# Service Integration
WEBSOCKET_SERVICE_URL: https://api.agent-scl.com/dev
ORCHESTRATOR_SERVICE_URL: https://api.agent-scl.com/dev
AGENT_SERVICE_URL: https://api.agent-scl.com/dev

# File Storage
S3_BUCKET: agent-scl-files-dev
S3_REGION: us-east-1
MAX_FILE_SIZE_MB: 50
ALLOWED_FILE_TYPES: "jpg,jpeg,png,gif,pdf,doc,docx,txt"

# Performance Tuning
MESSAGE_CACHE_TTL_SECONDS: 300
SEARCH_INDEX_BATCH_SIZE: 100
MAX_MESSAGES_PER_REQUEST: 100
CONVERSATION_HISTORY_LIMIT: 1000

# Analytics
ANALYTICS_RETENTION_DAYS: 90
METRICS_AGGREGATION_INTERVAL: 300
```

### **📊 DynamoDB Configuration**
```yaml
# Table Configuration
BillingMode: PAY_PER_REQUEST
StreamSpecification:
  StreamViewType: NEW_AND_OLD_IMAGES

# Global Secondary Indexes
GSI1:
  IndexName: ConversationIndex
  KeySchema:
    - AttributeName: GSI1PK (conversation_id)
    - AttributeName: GSI1SK (timestamp)

GSI2:
  IndexName: TenantIndex
  KeySchema:
    - AttributeName: GSI2PK (tenant_id)
    - AttributeName: GSI2SK (message_id)

GSI3:
  IndexName: SearchIndex
  KeySchema:
    - AttributeName: GSI3PK (tenant_id)
    - AttributeName: GSI3SK (search_terms)

# TTL Configuration
TimeToLiveSpecification:
  AttributeName: ttl
  Enabled: true
```

### **🗄️ S3 Configuration**
```yaml
# Bucket Configuration
Versioning:
  Status: Enabled

Encryption:
  ServerSideEncryptionConfiguration:
    - ServerSideEncryptionByDefault:
        SSEAlgorithm: AES256

# Lifecycle Configuration
LifecycleConfiguration:
  Rules:
    - Status: Enabled
      Transitions:
        - Days: 30
          StorageClass: STANDARD_IA
        - Days: 90
          StorageClass: GLACIER

# CORS Configuration
CorsConfiguration:
  CorsRules:
    - AllowedHeaders: ["*"]
      AllowedMethods: [GET, PUT, POST]
      AllowedOrigins: ["https://app.agent-scl.com"]
      MaxAge: 3600
```

---

## **🔐 SEGURIDAD**

### **🛡️ Autenticación y Autorización**
```python
# JWT Token Validation
- Validación en todos los endpoints protegidos
- Extracción de user_id y tenant_id del token
- Verificación de expiración y firma
- Rate limiting por usuario autenticado

# Tenant Isolation
- Todas las operaciones filtradas por tenant_id
- Prevención de cross-tenant data leakage
- Validación de permisos en cada operación
- Audit trail por tenant
```

### **🔒 Medidas de Seguridad**
```yaml
# Input Validation
- Sanitización de contenido de mensajes
- Validación de tipos de archivo
- Prevención de XSS y injection attacks
- Límites de tamaño de contenido

# File Security
- Validación de tipos MIME
- Escaneo de malware (futuro)
- URLs presignadas con TTL corto
- Encriptación en S3

# Data Protection
- Encriptación en tránsito (TLS 1.2+)
- Encriptación en reposo (DynamoDB + S3)
- Tokenización de datos sensibles
- Retention policies configurables

# API Security
- Rate limiting por endpoint
- CORS configurado correctamente
- Headers de seguridad obligatorios
- Logging de accesos sospechosos
```

### **🚨 Monitoring de Seguridad**
```python
# Alertas Automáticas
- Intentos de acceso no autorizados
- Uploads de archivos sospechosos
- Patrones de uso anómalos
- Fallos de validación repetidos

# Audit Logging
- Todas las operaciones de mensajes
- Accesos a archivos
- Cambios de configuración
- Operaciones administrativas
```

---

## **📊 MONITOREO Y LOGGING**

### **📈 Métricas Clave**
```yaml
# Message Metrics
- chat.messages.created: Mensajes creados por minuto
- chat.messages.processed: Mensajes procesados exitosamente
- chat.messages.failed: Mensajes fallidos
- chat.conversations.active: Conversaciones activas

# Performance Metrics
- chat.api.response_time: Tiempo de respuesta promedio
- chat.database.query_time: Tiempo de queries DynamoDB
- chat.file.upload_time: Tiempo de upload de archivos
- chat.search.response_time: Tiempo de búsqueda

# Business Metrics
- chat.users.active: Usuarios activos por día
- chat.files.uploaded: Archivos subidos por día
- chat.search.queries: Búsquedas realizadas
- chat.agent.responses: Respuestas de agentes procesadas

# Error Metrics
- chat.errors.validation: Errores de validación
- chat.errors.database: Errores de base de datos
- chat.errors.external: Errores de servicios externos
- chat.errors.file_upload: Errores de upload
```

### **🔍 Logging Strategy**
```python
# Structured Logging
{
    "timestamp": "2024-01-15T10:30:00Z",
    "level": "INFO",
    "service": "chat",
    "function": "process_message",
    "operation": "create_message",
    "tenant_id": "tenant-123",
    "user_id": "user-456",
    "conversation_id": "conv-789",
    "message_id": "msg-abc",
    "message_type": "text",
    "content_length": 45,
    "has_attachments": false,
    "processing_time_ms": 120,
    "correlation_id": "req-xyz"
}
```

### **🚨 Alertas y Notificaciones**
```yaml
# Critical Alerts (PagerDuty)
- Error rate > 5%
- Message processing failures > 10/minute
- Database connection failures
- S3 upload failures > 20%

# Warning Alerts (Slack)
- Response time > 500ms
- File upload rate > 1000/hour
- Search query failures > 5%
- Agent response delays > 30s

# Info Notifications (Dashboard)
- Daily message volume summary
- Popular file types uploaded
- Search query trends
- User engagement metrics
```

---

## **🚀 DEPLOYMENT**

### **📦 Deployment Configuration**
```yaml
# Serverless Framework
service: agent-scl-chat
frameworkVersion: '3'
provider:
  name: aws
  runtime: python3.11
  region: us-east-1
  stage: dev

# Lambda Functions
functions:
  processMessage: # Core message processing
  processAgentResponse: # Agent response handling
  getMessages: # Message retrieval
  markAsRead: # Message status updates
  fileSharing: # File upload coordination
  search: # Message search
  analytics: # Usage analytics
```

### **🔄 CI/CD Pipeline**
```yaml
# GitHub Actions Workflow
stages:
  1. Code Quality:
     - Linting (flake8, black)
     - Type checking (mypy)
     - Security scan (bandit)
     - Dependency check

  2. Testing:
     - Unit tests (pytest)
     - Integration tests
     - Performance tests
     - File upload tests

  3. Deployment:
     - Deploy to dev environment
     - Smoke tests
     - Deploy to staging
     - Production deployment (manual approval)
```

### **📋 Deployment Checklist**
```markdown
Pre-Deployment:
- [ ] Shared layer deployed and accessible
- [ ] Auth service deployed with JWT secret
- [ ] DynamoDB table created with correct schema
- [ ] S3 bucket configured with proper permissions
- [ ] WebSocket service deployed and functional
- [ ] Orchestrator service deployed

Post-Deployment:
- [ ] Message processing test successful
- [ ] File upload functionality verified
- [ ] Search functionality working
- [ ] Agent integration confirmed
- [ ] Monitoring dashboards updated
- [ ] Performance baselines established
```

---

## **🎯 CONCLUSIONES Y MEJORES PRÁCTICAS**

### **✅ Fortalezas del Diseño**
- **Modularidad**: Separación clara de responsabilidades
- **Escalabilidad**: Auto-scaling y optimización de queries
- **Resilencia**: Manejo robusto de fallos y retry logic
- **Performance**: Caching inteligente y indexación optimizada
- **Seguridad**: Autenticación robusta y validación comprehensiva
- **Observabilidad**: Logging detallado y métricas actionables

### **🔄 Áreas de Mejora Futuras**
- **Machine Learning**: Análisis de sentimientos en mensajes
- **Real-time Analytics**: Dashboards en tiempo real
- **Advanced Search**: Búsqueda semántica con embeddings
- **Message Encryption**: Encriptación end-to-end
- **Multi-region**: Replicación cross-region para DR

### **📚 Recursos Adicionales**
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [DynamoDB Design Patterns](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/bp-general-nosql-design.html)
- [S3 Security Best Practices](https://docs.aws.amazon.com/AmazonS3/latest/userguide/security-best-practices.html)

---

**📝 Documento generado el**: 2024-08-29
**🔄 Última actualización**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
