graph TB
    %% External Systems
    Orchestrator[🎯 Message Orchestrator<br/>HTTP Client]
    ChatService[💬 Chat Service<br/>Agent Responses]
    N8NFeedo[🔗 N8N Feedo Workflow<br/>Logistics Agent]
    N8NForecaster[📊 N8N Forecaster Workflow<br/>Forecasting Agent]
    Client[👤 Client Applications<br/>Web/Mobile]
    
    %% API Gateway
    APIGateway[🌐 API Gateway<br/>REST API]
    
    %% Lambda Functions - Agent Management
    CreateAgentHandler[🤖 Create Agent Handler<br/>POST /agents]
    ListAgentsHandler[📋 List Agents Handler<br/>GET /agents]
    UpdateAgentHandler[🔧 Update Agent Handler<br/>PUT /agents/{id}]
    DeleteAgentHandler[🗑️ Delete Agent Handler<br/>DELETE /agents/{id}]
    GetAgentHandler[👁️ Get Agent Handler<br/>GET /agents/{id}]
    UpdateAgentStatusHandler[⚡ Update Agent Status<br/>PUT /agents/{id}/status]
    
    %% Lambda Functions - Message Processing
    ProcessMessageHandler[📨 Process Message Handler<br/>POST /messages/process]
    SendMessageHandler[📤 Send Message Handler<br/>POST /messages/send]
    ReceiveWebhookHandler[🔗 Receive Webhook Handler<br/>POST /webhooks/{agentType}]
    
    %% Lambda Functions - Conversation Management
    CreateConversationHandler[💬 Create Conversation Handler<br/>POST /conversations]
    ListConversationsHandler[📋 List Conversations Handler<br/>GET /conversations]
    GetConversationHandler[👁️ Get Conversation Handler<br/>GET /conversations/{id}]
    CloseConversationHandler[🔒 Close Conversation Handler<br/>PUT /conversations/{id}/close]
    ArchiveConversationHandler[📦 Archive Conversation Handler<br/>PUT /conversations/{id}/archive]
    ListMessagesHandler[📄 List Messages Handler<br/>GET /conversations/{id}/messages]
    
    %% Core Services
    AgentManagementService[🤖 Agent Management Service<br/>CRUD & Configuration]
    HybridRouter[🔀 Hybrid Router Service<br/>Intelligent Routing]
    ConversationService[💬 Conversation Management<br/>Context & History]
    N8NIntegrationService[🔗 N8N Integration Service<br/>Webhook Management]
    AgentAnalyticsService[📊 Agent Analytics Service<br/>Performance Metrics]
    CacheService[💾 Cache Service<br/>Performance Optimization]
    DatabaseService[🗄️ Database Service<br/>DynamoDB Abstraction]
    MetricsService[📈 Metrics Service<br/>CloudWatch Integration]
    EventService[🎯 Event Service<br/>Audit & Notifications]
    
    %% Storage & External
    DynamoDB[(🗄️ DynamoDB<br/>agent-scl-dev)]
    SSM[📋 SSM Parameter Store<br/>N8N Webhook URLs]
    SecretsManager[🔑 Secrets Manager<br/>JWT Secret]
    CloudWatch[📊 CloudWatch<br/>Logs & Metrics]
    
    %% Client Connections
    Client -->|HTTP REST| APIGateway
    Orchestrator -->|HTTP POST| APIGateway
    ChatService -->|HTTP POST| APIGateway
    
    %% N8N Webhook Connections
    N8NFeedo -.->|Webhook Response| APIGateway
    N8NForecaster -.->|Webhook Response| APIGateway
    
    %% API Gateway Routing - Agent Management
    APIGateway --> CreateAgentHandler
    APIGateway --> ListAgentsHandler
    APIGateway --> UpdateAgentHandler
    APIGateway --> DeleteAgentHandler
    APIGateway --> GetAgentHandler
    APIGateway --> UpdateAgentStatusHandler
    
    %% API Gateway Routing - Message Processing
    APIGateway --> ProcessMessageHandler
    APIGateway --> SendMessageHandler
    APIGateway --> ReceiveWebhookHandler
    
    %% API Gateway Routing - Conversation Management
    APIGateway --> CreateConversationHandler
    APIGateway --> ListConversationsHandler
    APIGateway --> GetConversationHandler
    APIGateway --> CloseConversationHandler
    APIGateway --> ArchiveConversationHandler
    APIGateway --> ListMessagesHandler
    
    %% Handler Dependencies - Agent Management
    CreateAgentHandler --> AgentManagementService
    ListAgentsHandler --> AgentManagementService
    UpdateAgentHandler --> AgentManagementService
    DeleteAgentHandler --> AgentManagementService
    GetAgentHandler --> AgentManagementService
    UpdateAgentStatusHandler --> AgentManagementService
    
    %% Handler Dependencies - Message Processing
    ProcessMessageHandler --> HybridRouter
    ProcessMessageHandler --> N8NIntegrationService
    ProcessMessageHandler --> ConversationService
    
    SendMessageHandler --> N8NIntegrationService
    SendMessageHandler --> ConversationService
    
    ReceiveWebhookHandler --> N8NIntegrationService
    ReceiveWebhookHandler --> ConversationService
    
    %% Handler Dependencies - Conversation Management
    CreateConversationHandler --> ConversationService
    CreateConversationHandler --> AgentManagementService
    
    ListConversationsHandler --> ConversationService
    GetConversationHandler --> ConversationService
    CloseConversationHandler --> ConversationService
    ArchiveConversationHandler --> ConversationService
    ListMessagesHandler --> ConversationService
    
    %% Service Dependencies
    AgentManagementService --> DatabaseService
    AgentManagementService --> CacheService
    AgentManagementService --> MetricsService
    
    HybridRouter --> AgentAnalyticsService
    HybridRouter --> DatabaseService
    HybridRouter --> CacheService
    
    ConversationService --> DatabaseService
    ConversationService --> CacheService
    ConversationService --> EventService
    
    N8NIntegrationService --> SSM
    N8NIntegrationService --> DatabaseService
    N8NIntegrationService --> MetricsService
    
    AgentAnalyticsService --> DatabaseService
    AgentAnalyticsService --> MetricsService
    
    %% External Service Integration
    N8NIntegrationService --> N8NFeedo
    N8NIntegrationService --> N8NForecaster
    ReceiveWebhookHandler --> ChatService
    
    %% Storage Dependencies
    DatabaseService --> DynamoDB
    CacheService --> DynamoDB
    MetricsService --> CloudWatch
    EventService --> CloudWatch
    
    %% Security
    CreateAgentHandler --> SecretsManager
    ProcessMessageHandler --> SecretsManager
    ReceiveWebhookHandler --> SecretsManager
    
    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handler fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef n8n fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    
    class Client,Orchestrator,ChatService client
    class APIGateway gateway
    class CreateAgentHandler,ListAgentsHandler,UpdateAgentHandler,DeleteAgentHandler,GetAgentHandler,UpdateAgentStatusHandler,ProcessMessageHandler,SendMessageHandler,ReceiveWebhookHandler,CreateConversationHandler,ListConversationsHandler,GetConversationHandler,CloseConversationHandler,ArchiveConversationHandler,ListMessagesHandler handler
    class AgentManagementService,HybridRouter,ConversationService,N8NIntegrationService,AgentAnalyticsService,CacheService,DatabaseService,MetricsService,EventService service
    class DynamoDB storage
    class SSM,SecretsManager,CloudWatch external
    class N8NFeedo,N8NForecaster n8n