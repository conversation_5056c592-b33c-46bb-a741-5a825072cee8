# 🤖 **Agent Service - Agent-SCL Platform**

## **🚀 Descripción General**

El Agent Service es el núcleo de gestión de agentes IA del ecosistema **agent-scl**. Proporciona infraestructura completa para gestión de agentes, routing inteligente, integración con N8N workflows, y coordinación de conversaciones híbridas entre usuarios y agentes especializados.

### **✨ Características Principales**
- 🤖 **Gestión de Agentes**: CRUD completo con configuraciones avanzadas
- 🔀 **Routing Inteligente**: Asignación automática basada en contenido
- 🔗 **Integración N8N**: Workflows Feedo y Forecaster
- 💬 **Conversaciones Híbridas**: Coordinación usuario-agente-humano
- 📊 **Analytics Avanzados**: Métricas de performance por agente
- ⚡ **Escalación Automática**: Transferencia a agentes humanos
- 🎯 **Contexto Inteligente**: Mantenimiento de contexto conversacional
- 🔐 **Seguridad Robusta**: Autenticación y tenant isolation

---

## **📋 Tabla de Contenidos**
1. [Instalación y Setup](#instalación-y-setup)
2. [Configuración](#configuración)
3. [API Reference](#api-reference)
4. [Agentes Disponibles](#agentes-disponibles)
5. [Routing Inteligente](#routing-inteligente)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)

---

## **🛠️ Instalación y Setup**

### **Prerrequisitos**
```bash
# Servicios requeridos (deben estar desplegados)
✅ agent-scl-shared-layer-dev
✅ agent-scl-auth-dev
✅ agent-scl-orchestrator-dev
✅ agent-scl-chat-dev

# Herramientas de desarrollo
- Python 3.11+
- Serverless Framework 3.x
- AWS CLI configurado
- Node.js 18+ (para Serverless)
```

### **Instalación Local**
```bash
# 1. Clonar y navegar al directorio
cd services/agent

# 2. Instalar dependencias Python
pip install -r requirements.txt

# 3. Instalar Serverless plugins
npm install

# 4. Configurar variables de entorno
export STAGE=dev
export REGION=us-east-1
```

### **Configuración de N8N Webhooks**
```bash
# Verificar webhooks N8N configurados
aws ssm get-parameter --name "/agent-scl/dev/n8n-feedo-webhook-url"
aws ssm get-parameter --name "/agent-scl/dev/n8n-forecaster-webhook-url"

# JWT Secret (ya debe existir del auth service)
aws secretsmanager describe-secret --secret-id "agent-scl/dev/jwt-secret"

# Verificar tabla DynamoDB
aws dynamodb describe-table --table-name "agent-scl-dev"
```

## 📊 Esquema de Datos

### Agent Entity
```json
{
  "agentId": "uuid",
  "name": "string",
  "description": "string",
  "webhookUrl": "string",
  "documentWebhookUrl": "string",
  "bearerToken": "string",
  "secret": "string",
  "status": "active|inactive",
  "inputParameters": {},
  "metadata": {},
  "createdAt": "ISO8601",
  "updatedAt": "ISO8601"
}
```

### Conversation Entity
```json
{
  "conversationId": "uuid",
  "tenantId": "string",
  "userId": "string",
  "agentId": "string",
  "status": "active|closed|archived",
  "webhookUrl": "string",
  "metadata": {},
  "createdAt": "ISO8601",
  "updatedAt": "ISO8601"
}
```

### Message Entity
```json
{
  "messageId": "uuid",
  "conversationId": "string",
  "tenantId": "string",
  "userId": "string",
  "direction": "outbound|inbound",
  "type": "text|document|audio",
  "content": "string",
  "attachments": [],
  "timestamp": "ISO8601",
  "metadata": {}
}
```

## 🔗 API Endpoints

### Agent Management
```
POST   /agents                    - Crear agente (admin)
GET    /agents                    - Listar agentes activos
GET    /agents/{agentId}          - Obtener agente específico (admin)
PUT    /agents/{agentId}          - Actualizar agente (admin)
DELETE /agents/{agentId}          - Eliminar agente (admin)
PATCH  /agents/{agentId}/status   - Activar/desactivar agente (admin)
```

### Conversation Management
```
POST   /conversations                           - Crear conversación
GET    /conversations                           - Listar conversaciones del usuario
GET    /conversations/{conversationId}          - Obtener conversación específica
PATCH  /conversations/{conversationId}/close    - Cerrar conversación
PATCH  /conversations/{conversationId}/archive  - Archivar conversación
```

### Message Management
```
GET    /conversations/{id}/messages             - Listar mensajes (paginado)
POST   /conversations/{id}/messages             - Enviar mensaje
POST   /webhook/conversation/{conversationId}   - Recibir mensaje asíncrono
```

## 🔐 Seguridad

### Autenticación
- JWT Authorizers en todos los endpoints protegidos
- Extracción de contexto de usuario (userId, tenantId, userName)

### Autorización
- Endpoints de administración requieren rol admin
- Aislamiento completo de datos por tenant
- Validación de acceso a conversaciones propias

### Multi-tenancy
- Todos los datos aislados por tenant
- Estructura S3 con prefijos por tenant
- Validación de acceso a archivos por tenant

## 📁 Estructura S3

```
bucket/
├── tenants/
│   └── {tenantId}/
│       └── conversations/
│           └── {conversationId}/
│               └── messages/
│                   └── {messageId}/
│                       └── {filename}
```

## 🔄 Integración n8n

### Payload para Texto
```json
{
  "userId": "string",
  "tenantId": "string",
  "chatId": "string",
  "message": "string",
  "userName": "string"
}
```

### Payload para Documentos/Audio
```
Content-Type: multipart/form-data

userId: string
tenantId: string
chatId: string
fileType: "document|audio"
fileName: string
file: binary
```

## 🚀 Deployment

```bash
# Desde el directorio del servicio
cd services/agent

# Deploy
serverless deploy --stage dev

# Logs
serverless logs -f createAgent --stage dev
```

---

## **🤖 Agentes Disponibles**

### **🚛 Feedo Agent (Logistics)**
```yaml
Tipo: feedo
Especialización: Gestión logística y optimización
Capacidades:
  - Optimización de rutas de entrega
  - Tracking de envíos en tiempo real
  - Gestión de inventario logístico
  - Análisis de costos de transporte
  - Planificación de capacidad de almacén

Webhook: ${ssm:/agent-scl/dev/n8n-feedo-webhook-url}
Timeout: 30 segundos
Formato de entrada: JSON con contexto logístico
```

### **📈 Forecaster Agent (Predicciones)**
```yaml
Tipo: forecaster
Especialización: Análisis predictivo y forecasting
Capacidades:
  - Predicciones de demanda
  - Análisis de tendencias de mercado
  - Forecasting de inventario
  - Análisis de estacionalidad
  - Optimización de stock

Webhook: ${ssm:/agent-scl/dev/n8n-forecaster-webhook-url}
Timeout: 30 segundos
Formato de entrada: JSON con datos históricos
```

---

## **🔀 Routing Inteligente**

### **Estrategias de Routing**
```python
# Routing basado en contenido
def determine_agent_routing_strategy(content, context):
    keywords_feedo = ["logistics", "delivery", "shipping", "warehouse"]
    keywords_forecaster = ["forecast", "prediction", "demand", "inventory"]

    if any(keyword in content.lower() for keyword in keywords_forecaster):
        return "forecaster"
    elif any(keyword in content.lower() for keyword in keywords_feedo):
        return "feedo"
    else:
        return "auto"  # Routing automático basado en analytics
```

### **Escalación Automática**
```yaml
Criterios de escalación:
  - Timeout de agente > 120 segundos
  - Confianza de respuesta < 0.7
  - Usuario solicita agente humano
  - Error en workflow N8N

Proceso de escalación:
  1. Marcar conversación como "escalated"
  2. Notificar a pool de agentes humanos
  3. Transferir contexto completo
  4. Actualizar métricas de agente
```

---

## **💡 Ejemplos de Uso**

### **Cliente JavaScript**
```javascript
class AgentClient {
    constructor(token) {
        this.token = token;
        this.baseUrl = 'https://api.agent-scl.com/dev';
    }

    async createConversation(agentType, initialMessage) {
        const response = await fetch(`${this.baseUrl}/agent/conversations`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agent_type: agentType,
                initial_message: initialMessage
            })
        });
        return response.json();
    }

    async sendMessage(conversationId, message) {
        const response = await fetch(`${this.baseUrl}/agent/conversations/${conversationId}/messages`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                content: message,
                type: 'text'
            })
        });
        return response.json();
    }
}

// Uso
const client = new AgentClient('your-jwt-token');
const conversation = await client.createConversation('forecaster', 'I need Q4 inventory predictions');
```

---

## **🧪 Testing**

### **Unit Tests**
```bash
# Ejecutar todos los tests
pytest src/tests/

# Tests específicos de agentes
pytest src/tests/test_agent_management.py
pytest src/tests/test_hybrid_router.py
pytest src/tests/test_n8n_integration.py

# Con coverage
pytest --cov=src src/tests/
```

### **Integration Tests**
```bash
# Tests de integración con N8N
pytest src/tests/integration/test_n8n_workflows.py

# Tests de routing completo
pytest src/tests/integration/test_agent_routing_e2e.py
```

---

## **🛠️ Troubleshooting**

### **Problemas Comunes**

#### **Agentes no responden**
```bash
# Verificar webhooks N8N
aws ssm get-parameter --name "/agent-scl/dev/n8n-feedo-webhook-url"
aws ssm get-parameter --name "/agent-scl/dev/n8n-forecaster-webhook-url"

# Test de conectividad
curl -X POST https://n8n.example.com/webhook/feedo \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

#### **Routing incorrecto**
```bash
# Verificar logs de routing
aws logs tail /aws/lambda/agent-scl-agent-dev-processMessage --follow

# Verificar métricas de agentes
aws cloudwatch get-metric-statistics \
  --namespace "AgentSCL/Agents" \
  --metric-name "RoutingAccuracy"
```

---

## **📚 Documentación Adicional**

- 📖 [Documentación Técnica Completa](./TECHNICAL_DOCUMENTATION.md)
- 🔧 [Guía de Configuración N8N](./docs/n8n-setup.md)
- 🚀 [Guía de Deployment](./docs/deployment.md)
- 🐛 [Troubleshooting Avanzado](./docs/troubleshooting.md)

---

## **📝 Changelog**

### **v1.0.0 (2024-08-29) - ✅ DEPLOYED**
- ✅ Gestión completa de agentes IA
- ✅ Routing inteligente basado en contenido
- ✅ Integración robusta con N8N workflows
- ✅ Conversaciones híbridas usuario-agente-humano
- ✅ Analytics y métricas de performance
- ✅ Escalación automática a agentes humanos
- ✅ Seguridad y tenant isolation

### **Próximas Versiones**
- 🔄 Machine Learning para routing predictivo
- 🔄 Multi-agent coordination
- 🔄 Advanced analytics con ML
- 🔄 Real-time optimization

---

**📝 Documento actualizado**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
**🔗 Repositorio**: [agent-scl/services/agent](https://github.com/agent-scl/services/agent)
```

## 🧪 Testing

```bash
# Ejecutar tests unitarios
python -m pytest tests/

# Ejecutar tests de integración
python -m pytest tests/integration/
```

## 📝 Variables de Entorno

```
PROJECT_NAME=agent-scl
ENVIRONMENT=dev
REGION=us-east-1
DYNAMODB_TABLE=agent-scl-main-dev
S3_BUCKET=agent-scl-agent-files-dev
```

## 🔧 Configuración

Ver `src/utils/config.py` para configuración detallada:
- URLs de webhooks n8n
- Límites de archivos
- Configuración de rate limiting
- Timeouts y reintentos
