# services/agent/serverless.yml
# Agent service configuration for managing Feedo/Forecaster agents

service: ${self:custom.projectName}-agent

# Custom configuration
custom:
  # Project configuration
  projectName: agent-scl
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}

  # Stage-specific configuration
  stageConfig:
    dev:
      dynamodb:
        tableName: ${self:custom.projectName}-dev
    staging:
      dynamodb:
        tableName: ${self:custom.projectName}-staging
    prod:
      dynamodb:
        tableName: ${self:custom.projectName}-prod

# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  memorySize: 256
  timeout: 30
  
  # Environment variables
  environment:
    STAGE: ${self:custom.stage}
    REGION: ${self:custom.region}
    PROJECT_NAME: ${self:custom.projectName}
    DYNAMODB_TABLE: ${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}
    JWT_SECRET_NAME: ${self:custom.projectName}/${self:custom.stage}/jwt-secret
    CHAT_SERVICE_URL: ${cf:${self:custom.projectName}-chat-${self:custom.stage}.ServiceEndpoint, ''}
    ORCHESTRATOR_SERVICE_URL: ${cf:${self:custom.projectName}-orchestrator-${self:custom.stage}.ServiceEndpoint, ''}
    N8N_FEEDO_WEBHOOK_URL: ${ssm:/${self:custom.projectName}/${self:custom.stage}/n8n-feedo-webhook-url}
    N8N_FORECASTER_WEBHOOK_URL: ${ssm:/${self:custom.projectName}/${self:custom.stage}/n8n-forecaster-webhook-url}
  
  # IAM role statements
  iam:
    role:
      statements:
        # DynamoDB permissions
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
            - dynamodb:BatchGetItem
            - dynamodb:BatchWriteItem
          Resource:
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}
            - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.stageConfig.${self:custom.stage}.dynamodb.tableName}/index/*

        # CloudWatch permissions
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:${self:custom.region}:*:*
        
        # Secrets Manager permissions for JWT
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource:
            - arn:aws:secretsmanager:${self:custom.region}:*:secret:${self:custom.projectName}/${self:custom.stage}/*

        # SSM permissions for N8N webhooks
        - Effect: Allow
          Action:
            - ssm:GetParameter
            - ssm:GetParameters
          Resource:
            - arn:aws:ssm:${self:custom.region}:*:parameter/${self:custom.projectName}/${self:custom.stage}/*

# Functions
functions:
  # Process message from orchestrator
  processMessage:
    handler: src.handlers.process_message.handler
    description: Process messages from orchestrator to agents
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /agent/messages/process
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: agent-process-message
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Create agent
  createAgent:
    handler: src.handlers.create_agent.handler
    description: Create a new agent
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /agents
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: agent-create-agent
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # List agents
  listAgents:
    handler: src.handlers.list_agents.handler
    description: List agents for tenant
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /agents
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: agent-list-agents
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Get agent
  getAgent:
    handler: src.handlers.get_agent.handler
    description: Get agent details
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /agents/{agentId}
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
#            identitySource: method.request.header.Authorization
#            resultTtlInSeconds: 300
    environment:
      FUNCTION_NAME: agent-get-agent
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

  # Webhook receiver for agent responses
  webhookReceiver:
    handler: src.handlers.webhook_receiver.handler
    description: Receive responses from N8N agents
    timeout: 30
    memorySize: 512
    events:
      - http:
          path: /agent/webhook/{agentType}
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
            allowCredentials: false
    environment:
      FUNCTION_NAME: agent-webhook-receiver
    layers:
#      - Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

# Plugins
plugins:
  - serverless-python-requirements

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'
    - '!src/**/.pytest_cache/**'
    - '!src/**/tests/**'

# Resources
resources:
  Outputs:
    ServiceEndpoint:
      Description: "Agent Service endpoint"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-agent-${self:custom.stage}-ServiceEndpoint
