# services/agent/src/utils/file_validator.py
# File validation utilities for Agent Service

import os
import mimetypes
from typing import Dict, Any, List, Tuple, Optional


class FileValidator:
    """File validation for agent attachments"""
    
    # Allowed file types and their configurations
    ALLOWED_FILE_TYPES = {
        'document': {
            'extensions': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.xls', '.xlsx', '.ppt', '.pptx', '.csv'],
            'mime_types': [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain',
                'application/rtf',
                'application/vnd.oasis.opendocument.text',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/csv'
            ],
            'max_size_mb': 50,  # 50MB max for documents
            'description': 'Documents (PDF, Word, Excel, PowerPoint, Text)'
        },
        'audio': {
            'extensions': ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac'],
            'mime_types': [
                'audio/mpeg',
                'audio/wav',
                'audio/ogg',
                'audio/mp4',
                'audio/aac',
                'audio/flac'
            ],
            'max_size_mb': 25,  # 25MB max for audio
            'description': 'Audio files (MP3, WAV, OGG, M4A, AAC, FLAC)'
        }
    }
    
    # Global limits
    MAX_FILENAME_LENGTH = 255
    MIN_FILENAME_LENGTH = 1
    
    @classmethod
    def validate_file(
        cls,
        filename: str,
        file_size: int,
        file_type: str,
        content_type: str = None
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate file for upload
        Returns: (is_valid, error_message)
        """
        # Validate file type
        if file_type not in cls.ALLOWED_FILE_TYPES:
            return False, f"Unsupported file type: {file_type}. Allowed types: {', '.join(cls.ALLOWED_FILE_TYPES.keys())}"
        
        type_config = cls.ALLOWED_FILE_TYPES[file_type]
        
        # Validate filename
        is_valid, error = cls._validate_filename(filename)
        if not is_valid:
            return False, error
        
        # Validate file extension
        is_valid, error = cls._validate_extension(filename, type_config['extensions'])
        if not is_valid:
            return False, error
        
        # Validate file size
        is_valid, error = cls._validate_size(file_size, type_config['max_size_mb'])
        if not is_valid:
            return False, error
        
        # Validate MIME type if provided
        if content_type:
            is_valid, error = cls._validate_mime_type(content_type, type_config['mime_types'])
            if not is_valid:
                return False, error
        
        return True, None
    
    @classmethod
    def get_file_type_from_extension(cls, filename: str) -> Optional[str]:
        """Determine file type from extension"""
        _, ext = os.path.splitext(filename.lower())
        
        for file_type, config in cls.ALLOWED_FILE_TYPES.items():
            if ext in config['extensions']:
                return file_type
        
        return None
    
    @classmethod
    def get_allowed_extensions(cls, file_type: str = None) -> List[str]:
        """Get list of allowed extensions"""
        if file_type and file_type in cls.ALLOWED_FILE_TYPES:
            return cls.ALLOWED_FILE_TYPES[file_type]['extensions']
        
        # Return all allowed extensions
        all_extensions = []
        for config in cls.ALLOWED_FILE_TYPES.values():
            all_extensions.extend(config['extensions'])
        
        return list(set(all_extensions))
    
    @classmethod
    def get_max_file_size(cls, file_type: str) -> int:
        """Get maximum file size in bytes for file type"""
        if file_type in cls.ALLOWED_FILE_TYPES:
            return cls.ALLOWED_FILE_TYPES[file_type]['max_size_mb'] * 1024 * 1024
        return 0
    
    @classmethod
    def get_file_type_info(cls) -> Dict[str, Any]:
        """Get information about all supported file types"""
        info = {}
        for file_type, config in cls.ALLOWED_FILE_TYPES.items():
            info[file_type] = {
                'description': config['description'],
                'extensions': config['extensions'],
                'max_size_mb': config['max_size_mb'],
                'max_size_bytes': config['max_size_mb'] * 1024 * 1024
            }
        return info
    
    @classmethod
    def _validate_filename(cls, filename: str) -> Tuple[bool, Optional[str]]:
        """Validate filename"""
        if not filename or len(filename.strip()) == 0:
            return False, "Filename cannot be empty"
        
        if len(filename) < cls.MIN_FILENAME_LENGTH:
            return False, f"Filename too short (minimum {cls.MIN_FILENAME_LENGTH} character)"
        
        if len(filename) > cls.MAX_FILENAME_LENGTH:
            return False, f"Filename too long (maximum {cls.MAX_FILENAME_LENGTH} characters)"
        
        # Check for dangerous characters
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0']
        for char in dangerous_chars:
            if char in filename:
                return False, f"Filename contains invalid character: {char}"
        
        # Check for reserved names (Windows)
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        name_without_ext = os.path.splitext(filename)[0].upper()
        if name_without_ext in reserved_names:
            return False, f"Filename uses reserved name: {name_without_ext}"
        
        return True, None
    
    @classmethod
    def _validate_extension(cls, filename: str, allowed_extensions: List[str]) -> Tuple[bool, Optional[str]]:
        """Validate file extension"""
        _, ext = os.path.splitext(filename.lower())
        
        if not ext:
            return False, "File must have an extension"
        
        if ext not in allowed_extensions:
            return False, f"File extension '{ext}' not allowed. Allowed extensions: {', '.join(allowed_extensions)}"
        
        return True, None
    
    @classmethod
    def _validate_size(cls, file_size: int, max_size_mb: int) -> Tuple[bool, Optional[str]]:
        """Validate file size"""
        if file_size <= 0:
            return False, "File size must be greater than 0"
        
        max_size_bytes = max_size_mb * 1024 * 1024
        if file_size > max_size_bytes:
            return False, f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size ({max_size_mb}MB)"
        
        return True, None
    
    @classmethod
    def _validate_mime_type(cls, content_type: str, allowed_mime_types: List[str]) -> Tuple[bool, Optional[str]]:
        """Validate MIME type"""
        if content_type not in allowed_mime_types:
            return False, f"MIME type '{content_type}' not allowed. Allowed types: {', '.join(allowed_mime_types)}"
        
        return True, None
    
    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """Sanitize filename for safe storage"""
        import re
        
        # Remove path components
        filename = os.path.basename(filename)
        
        # Replace dangerous characters with underscores
        filename = re.sub(r'[<>:"|?*\0]', '_', filename)
        
        # Replace multiple consecutive spaces/underscores with single underscore
        filename = re.sub(r'[_\s]+', '_', filename)
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Ensure it's not empty
        if not filename:
            filename = 'unnamed_file'
        
        # Truncate if too long
        if len(filename) > cls.MAX_FILENAME_LENGTH:
            name, ext = os.path.splitext(filename)
            max_name_length = cls.MAX_FILENAME_LENGTH - len(ext)
            filename = name[:max_name_length] + ext
        
        return filename


# Convenience functions
def validate_document(filename: str, file_size: int, content_type: str = None) -> Tuple[bool, Optional[str]]:
    """Validate document file"""
    return FileValidator.validate_file(filename, file_size, 'document', content_type)


def validate_audio(filename: str, file_size: int, content_type: str = None) -> Tuple[bool, Optional[str]]:
    """Validate audio file"""
    return FileValidator.validate_file(filename, file_size, 'audio', content_type)


def get_supported_file_types() -> Dict[str, Any]:
    """Get information about supported file types"""
    return FileValidator.get_file_type_info()
