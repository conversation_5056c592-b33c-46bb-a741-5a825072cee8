# services/agent/src/utils/config.py
# Agent service specific configuration (extends shared config)

import os
from typing import Dict, Any, Optional

try:
    from shared.config import get_config, get_environment_variable
    from shared.logger import lambda_logger
    
    # Use shared config as base
    shared_config_available = True
    
except ImportError:
    # Fallback for local development
    shared_config_available = False
    
    def get_environment_variable(key: str, default: Any = None) -> Any:
        return os.environ.get(key, default)
    
    class MockLogger:
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    
    lambda_logger = MockLogger()


class AgentConfig:
    """Agent service specific configuration"""
    
    def __init__(self):
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """Load agent service configuration"""
        try:
            if shared_config_available:
                # Use shared config as base
                base_config = get_config()
                self._config.update(base_config)
            
            # Agent-specific configuration
            self._config.update({
                # Service identification
                'service_name': 'agent-service',
                'service_version': '1.0.0',
                
                # Database configuration
                'dynamodb_table': get_environment_variable('DYNAMODB_TABLE', 'agent-scl-main-dev'),
                
                # S3 configuration
                's3_bucket': get_environment_variable('S3_BUCKET', 'agent-scl-agent-files-dev'),
                's3_region': get_environment_variable('AWS_REGION', 'us-east-1'),
                
                # Webhook configuration
                'webhook_base_url': get_environment_variable(
                    'WEBHOOK_BASE_URL', 
                    'https://api.agent-scl.com/dev/agent'
                ),
                'webhook_secret_salt': get_environment_variable(
                    'WEBHOOK_SECRET_SALT', 
                    'default-webhook-salt-change-in-production'
                ),
                
                # File upload limits
                'max_file_size_mb': int(get_environment_variable('MAX_FILE_SIZE_MB', '10')),
                'allowed_file_types': {
                    'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
                    'audio': ['.mp3', '.wav', '.m4a', '.ogg', '.flac']
                },
                
                # Pagination limits
                'max_page_size': int(get_environment_variable('MAX_PAGE_SIZE', '100')),
                'default_page_size': int(get_environment_variable('DEFAULT_PAGE_SIZE', '50')),
                
                # Rate limiting (requests per minute)
                'rate_limits': {
                    'admin_operations': int(get_environment_variable('ADMIN_RATE_LIMIT', '60')),
                    'user_operations': int(get_environment_variable('USER_RATE_LIMIT', '120')),
                    'webhook_operations': int(get_environment_variable('WEBHOOK_RATE_LIMIT', '300')),
                    'read_operations': int(get_environment_variable('READ_RATE_LIMIT', '240'))
                },
                
                # N8N integration
                'n8n_webhook_timeout': int(get_environment_variable('N8N_WEBHOOK_TIMEOUT', '30')),
                'n8n_retry_attempts': int(get_environment_variable('N8N_RETRY_ATTEMPTS', '3')),
                
                # Security settings
                'require_https_webhooks': get_environment_variable('REQUIRE_HTTPS_WEBHOOKS', 'true').lower() == 'true',
                'webhook_signature_validation': get_environment_variable('WEBHOOK_SIGNATURE_VALIDATION', 'true').lower() == 'true',
                
                # Environment
                'environment': get_environment_variable('ENVIRONMENT', 'dev'),
                'stage': get_environment_variable('STAGE', 'dev'),
                'region': get_environment_variable('AWS_REGION', 'us-east-1'),
                
                # Logging
                'log_level': get_environment_variable('LOG_LEVEL', 'INFO'),
                'enable_debug_logging': get_environment_variable('DEBUG_LOGGING', 'false').lower() == 'true'
            })
            
        except Exception as e:
            lambda_logger.warning(f"Error loading agent config: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self._config.get(key, default)
    
    def get_dynamodb_table(self) -> str:
        """Get DynamoDB table name"""
        return self.get('dynamodb_table')
    
    def get_s3_bucket(self) -> str:
        """Get S3 bucket name"""
        return self.get('s3_bucket')
    
    def get_s3_region(self) -> str:
        """Get S3 region"""
        return self.get('s3_region')
    
    def get_webhook_base_url(self) -> str:
        """Get webhook base URL"""
        return self.get('webhook_base_url')
    
    def get_webhook_secret_salt(self) -> str:
        """Get webhook secret salt"""
        return self.get('webhook_secret_salt')
    
    def get_max_file_size_bytes(self) -> int:
        """Get maximum file size in bytes"""
        return self.get('max_file_size_mb', 10) * 1024 * 1024
    
    def get_allowed_file_types(self, file_category: str = None) -> Dict[str, list]:
        """Get allowed file types"""
        allowed_types = self.get('allowed_file_types', {})
        if file_category:
            return allowed_types.get(file_category, [])
        return allowed_types
    
    def get_max_page_size(self) -> int:
        """Get maximum page size for pagination"""
        return self.get('max_page_size', 100)
    
    def get_default_page_size(self) -> int:
        """Get default page size for pagination"""
        return self.get('default_page_size', 50)
    
    def get_rate_limit(self, operation_type: str) -> int:
        """Get rate limit for operation type"""
        rate_limits = self.get('rate_limits', {})
        return rate_limits.get(operation_type, 60)
    
    def get_n8n_webhook_timeout(self) -> int:
        """Get N8N webhook timeout in seconds"""
        return self.get('n8n_webhook_timeout', 30)
    
    def get_n8n_retry_attempts(self) -> int:
        """Get N8N retry attempts"""
        return self.get('n8n_retry_attempts', 3)
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.get('environment', 'dev').lower() in ['prod', 'production']
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.get('environment', 'dev').lower() in ['dev', 'development', 'local']
    
    def require_https_webhooks(self) -> bool:
        """Check if HTTPS is required for webhooks"""
        return self.get('require_https_webhooks', True)
    
    def enable_webhook_signature_validation(self) -> bool:
        """Check if webhook signature validation is enabled"""
        return self.get('webhook_signature_validation', True)
    
    def enable_debug_logging(self) -> bool:
        """Check if debug logging is enabled"""
        return self.get('enable_debug_logging', False)
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration (for debugging)"""
        # Return copy to prevent modification
        return self._config.copy()


# Global config instance
config = AgentConfig()


# Convenience functions for backward compatibility
def get_webhook_base_url() -> str:
    """Get webhook base URL"""
    return config.get_webhook_base_url()


def get_max_page_size() -> int:
    """Get maximum page size"""
    return config.get_max_page_size()


def get_s3_bucket() -> str:
    """Get S3 bucket name"""
    return config.get_s3_bucket()


def get_dynamodb_table() -> str:
    """Get DynamoDB table name"""
    return config.get_dynamodb_table()


# Export commonly used values
max_page_size = config.get_max_page_size()
webhook_secret_salt = config.get_webhook_secret_salt()
s3_bucket = config.get_s3_bucket()
dynamodb_table = config.get_dynamodb_table()
