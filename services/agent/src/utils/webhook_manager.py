# services/agent/src/utils/webhook_manager.py
# Webhook management utilities for Agent Service

import hashlib
import hmac
import secrets
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urljoin

try:
    from shared.logger import lambda_logger
except ImportError:
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    
    lambda_logger = MockLogger()

from .config import config


class WebhookManager:
    """Manages webhook URL generation and validation for conversations"""
    
    @staticmethod
    def generate_conversation_webhook_url(conversation_id: str, base_url: str = None) -> str:
        """
        Generate a unique webhook URL for a conversation.
        
        Args:
            conversation_id: The conversation UUID
            base_url: Base URL for the webhook service (optional, uses config if not provided)
            
        Returns:
            Complete webhook URL for the conversation
        """
        if not base_url:
            base_url = config.get_webhook_base_url()
        
        # Construct the webhook path
        webhook_path = f"/webhook/conversation/{conversation_id}"
        
        # Join base URL with path
        webhook_url = urljoin(base_url.rstrip('/') + '/', webhook_path.lstrip('/'))
        
        lambda_logger.debug("Generated conversation webhook URL", extra={
            'conversation_id': conversation_id,
            'webhook_url': webhook_url
        })
        
        return webhook_url
    
    @staticmethod
    def generate_webhook_secret(conversation_id: str, agent_id: str) -> str:
        """
        Generate a secure webhook secret for conversation validation.
        
        Args:
            conversation_id: The conversation UUID
            agent_id: The agent UUID
            
        Returns:
            Secure webhook secret string
        """
        # Create a deterministic but secure secret based on conversation and agent
        secret_data = f"{conversation_id}:{agent_id}:{config.webhook_secret_salt}"
        
        # Generate a secure hash
        secret = hashlib.sha256(secret_data.encode()).hexdigest()[:32]
        
        return secret
    
    @staticmethod
    def validate_webhook_signature(payload: str, signature: str, secret: str) -> bool:
        """
        Validate webhook signature for security.
        
        Args:
            payload: Raw webhook payload
            signature: Provided signature (usually in headers)
            secret: Webhook secret for validation
            
        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # Generate expected signature
            expected_signature = hmac.new(
                secret.encode(),
                payload.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures securely
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            lambda_logger.warning("Webhook signature validation error", extra={
                'error': str(e)
            })
            return False
    
    @staticmethod
    def create_webhook_payload_for_agent(
        conversation_id: str,
        user_id: str,
        tenant_id: str,
        message: str,
        user_name: str = None,
        message_type: str = 'text',
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create standardized webhook payload to send to agents.
        
        Args:
            conversation_id: The conversation UUID
            user_id: User ID sending the message
            tenant_id: Tenant ID
            message: Message content
            user_name: Optional user name
            message_type: Type of message (text, document, audio)
            metadata: Optional metadata
            
        Returns:
            Standardized webhook payload for agent
        """
        payload = {
            'userId': user_id,
            'tenantId': tenant_id,
            'chatId': conversation_id,  # n8n expects 'chatId'
            'message': message,
            'messageType': message_type,
            'timestamp': lambda_logger.get_current_timestamp()
        }
        
        # Add optional fields
        if user_name:
            payload['userName'] = user_name
        
        if metadata:
            payload['metadata'] = metadata
        
        return payload
    
    @staticmethod
    def extract_conversation_id_from_webhook_url(webhook_url: str) -> Optional[str]:
        """
        Extract conversation ID from webhook URL.
        
        Args:
            webhook_url: The webhook URL
            
        Returns:
            Conversation ID if found, None otherwise
        """
        try:
            # Expected format: .../webhook/conversation/{conversationId}
            parts = webhook_url.split('/')
            
            # Find the conversation ID after 'conversation' in the path
            for i, part in enumerate(parts):
                if part == 'conversation' and i + 1 < len(parts):
                    conversation_id = parts[i + 1]
                    
                    # Validate it looks like a UUID
                    import uuid
                    uuid.UUID(conversation_id)
                    
                    return conversation_id
            
            return None
            
        except Exception as e:
            lambda_logger.warning("Error extracting conversation ID from webhook URL", extra={
                'webhook_url': webhook_url,
                'error': str(e)
            })
            return None
    
    @staticmethod
    def validate_webhook_url_format(webhook_url: str) -> Tuple[bool, Optional[str]]:
        """
        Validate webhook URL format.
        
        Args:
            webhook_url: The webhook URL to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            from urllib.parse import urlparse
            
            # Parse URL
            parsed = urlparse(webhook_url)
            
            # Check basic URL structure
            if not all([parsed.scheme, parsed.netloc]):
                return False, "Invalid URL structure"
            
            # Check if it's HTTPS (security requirement)
            if parsed.scheme != 'https':
                return False, "Webhook URL must use HTTPS"
            
            # Check if it contains the expected path pattern
            if '/webhook/conversation/' not in parsed.path:
                return False, "Invalid webhook path format"
            
            # Try to extract conversation ID
            conversation_id = WebhookManager.extract_conversation_id_from_webhook_url(webhook_url)
            if not conversation_id:
                return False, "Invalid conversation ID in webhook URL"
            
            return True, None
            
        except Exception as e:
            return False, f"Webhook URL validation error: {str(e)}"
    
    @staticmethod
    def generate_webhook_headers(secret: str, payload: str) -> Dict[str, str]:
        """
        Generate headers for webhook requests.
        
        Args:
            secret: Webhook secret
            payload: Request payload
            
        Returns:
            Dictionary of headers to include in webhook request
        """
        # Generate signature
        signature = hmac.new(
            secret.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        
        headers = {
            'Content-Type': 'application/json',
            'X-Webhook-Signature': signature,
            'X-Webhook-Source': 'agent-service',
            'User-Agent': f'AgentService-Webhook/1.0'
        }
        
        return headers
    
    @staticmethod
    def create_webhook_test_payload() -> Dict[str, Any]:
        """
        Create a test payload for webhook validation.
        
        Returns:
            Test webhook payload
        """
        return {
            'userId': 'test-user-id',
            'tenantId': 'test-tenant-id',
            'chatId': 'test-conversation-id',
            'message': 'Test webhook message',
            'messageType': 'text',
            'timestamp': lambda_logger.get_current_timestamp(),
            'test': True
        }


class WebhookSecurityManager:
    """Manages webhook security and rate limiting"""
    
    @staticmethod
    def is_webhook_rate_limited(conversation_id: str, source_ip: str) -> Tuple[bool, Optional[str]]:
        """
        Check if webhook is rate limited.
        
        Args:
            conversation_id: The conversation ID
            source_ip: Source IP address
            
        Returns:
            Tuple of (is_limited, reason)
        """
        # TODO: Implement rate limiting logic
        # This could use Redis or DynamoDB to track request rates
        # For now, return False (not limited)
        return False, None
    
    @staticmethod
    def validate_webhook_source(source_ip: str, allowed_ips: list = None) -> bool:
        """
        Validate webhook source IP.
        
        Args:
            source_ip: Source IP address
            allowed_ips: List of allowed IP addresses/ranges
            
        Returns:
            True if source is allowed, False otherwise
        """
        if not allowed_ips:
            # If no IP restrictions configured, allow all
            return True
        
        # TODO: Implement IP validation logic
        # This could include CIDR range checking
        return source_ip in allowed_ips
    
    @staticmethod
    def log_webhook_security_event(event_type: str, conversation_id: str, source_ip: str, details: Dict[str, Any] = None):
        """
        Log webhook security events for monitoring.
        
        Args:
            event_type: Type of security event
            conversation_id: The conversation ID
            source_ip: Source IP address
            details: Additional event details
        """
        lambda_logger.warning("Webhook security event", extra={
            'event_type': event_type,
            'conversation_id': conversation_id,
            'source_ip': source_ip,
            'details': details or {},
            'security_event': True
        })
