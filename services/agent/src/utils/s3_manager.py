# services/agent/src/utils/s3_manager.py
# S3 file management utilities for Agent Service

import os
import boto3
import uuid
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from botocore.exceptions import ClientError, NoCredentialsError
import mimetypes


class S3Manager:
    """S3 file manager with multi-tenant support for agent attachments"""
    
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.bucket_name = os.environ.get('S3_BUCKET')
        
        if not self.bucket_name:
            raise ValueError("S3_BUCKET environment variable is required")
    
    def generate_file_key(
        self,
        tenant_id: str,
        conversation_id: str,
        message_id: str,
        filename: str
    ) -> str:
        """
        Generate S3 key with multi-tenant structure
        Format: tenants/{tenant_id}/conversations/{conversation_id}/messages/{message_id}/{filename}
        """
        # Sanitize filename
        safe_filename = self._sanitize_filename(filename)
        
        return f"tenants/{tenant_id}/conversations/{conversation_id}/messages/{message_id}/{safe_filename}"
    
    def upload_file(
        self,
        file_content: bytes,
        tenant_id: str,
        conversation_id: str,
        message_id: str,
        filename: str,
        content_type: str = None
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Upload file to S3 with tenant isolation
        Returns: (success, s3_key, error_message)
        """
        try:
            s3_key = self.generate_file_key(tenant_id, conversation_id, message_id, filename)
            
            # Auto-detect content type if not provided
            if not content_type:
                content_type, _ = mimetypes.guess_type(filename)
                if not content_type:
                    content_type = 'application/octet-stream'
            
            # Upload with metadata
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=file_content,
                ContentType=content_type,
                Metadata={
                    'tenant_id': tenant_id,
                    'conversation_id': conversation_id,
                    'message_id': message_id,
                    'original_filename': filename,
                    'uploaded_at': datetime.utcnow().isoformat()
                },
                ServerSideEncryption='AES256'
            )
            
            return True, s3_key, None
            
        except ClientError as e:
            error_message = f"S3 upload failed: {str(e)}"
            return False, "", error_message
        except Exception as e:
            error_message = f"Unexpected error during upload: {str(e)}"
            return False, "", error_message
    
    def generate_presigned_url(
        self,
        s3_key: str,
        tenant_id: str,
        expiration: int = 3600,
        operation: str = 'get_object'
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Generate presigned URL for file access
        Returns: (success, presigned_url, error_message)
        """
        try:
            # Validate tenant access to the file
            if not self._validate_tenant_access(s3_key, tenant_id):
                return False, None, "Access denied: File does not belong to tenant"
            
            presigned_url = self.s3_client.generate_presigned_url(
                operation,
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            
            return True, presigned_url, None
            
        except ClientError as e:
            error_message = f"Failed to generate presigned URL: {str(e)}"
            return False, None, error_message
        except Exception as e:
            error_message = f"Unexpected error generating URL: {str(e)}"
            return False, None, error_message
    
    def generate_upload_presigned_url(
        self,
        tenant_id: str,
        conversation_id: str,
        message_id: str,
        filename: str,
        content_type: str = None,
        expiration: int = 3600
    ) -> Tuple[bool, Optional[str], Optional[str], Optional[str]]:
        """
        Generate presigned URL for file upload
        Returns: (success, presigned_url, s3_key, error_message)
        """
        try:
            s3_key = self.generate_file_key(tenant_id, conversation_id, message_id, filename)
            
            # Auto-detect content type if not provided
            if not content_type:
                content_type, _ = mimetypes.guess_type(filename)
                if not content_type:
                    content_type = 'application/octet-stream'
            
            presigned_url = self.s3_client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key,
                    'ContentType': content_type,
                    'Metadata': {
                        'tenant_id': tenant_id,
                        'conversation_id': conversation_id,
                        'message_id': message_id,
                        'original_filename': filename
                    },
                    'ServerSideEncryption': 'AES256'
                },
                ExpiresIn=expiration
            )
            
            return True, presigned_url, s3_key, None
            
        except ClientError as e:
            error_message = f"Failed to generate upload URL: {str(e)}"
            return False, None, None, error_message
        except Exception as e:
            error_message = f"Unexpected error generating upload URL: {str(e)}"
            return False, None, None, error_message
    
    def delete_file(
        self,
        s3_key: str,
        tenant_id: str
    ) -> Tuple[bool, Optional[str]]:
        """
        Delete file from S3 with tenant validation
        Returns: (success, error_message)
        """
        try:
            # Validate tenant access to the file
            if not self._validate_tenant_access(s3_key, tenant_id):
                return False, "Access denied: File does not belong to tenant"
            
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            
            return True, None
            
        except ClientError as e:
            error_message = f"Failed to delete file: {str(e)}"
            return False, error_message
        except Exception as e:
            error_message = f"Unexpected error deleting file: {str(e)}"
            return False, error_message
    
    def get_file_metadata(
        self,
        s3_key: str,
        tenant_id: str
    ) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Get file metadata from S3
        Returns: (success, metadata, error_message)
        """
        try:
            # Validate tenant access to the file
            if not self._validate_tenant_access(s3_key, tenant_id):
                return False, None, "Access denied: File does not belong to tenant"
            
            response = self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            
            metadata = {
                'content_type': response.get('ContentType'),
                'content_length': response.get('ContentLength'),
                'last_modified': response.get('LastModified').isoformat() if response.get('LastModified') else None,
                'etag': response.get('ETag'),
                'metadata': response.get('Metadata', {})
            }
            
            return True, metadata, None
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                return False, None, "File not found"
            error_message = f"Failed to get file metadata: {str(e)}"
            return False, None, error_message
        except Exception as e:
            error_message = f"Unexpected error getting metadata: {str(e)}"
            return False, None, error_message
    
    def list_conversation_files(
        self,
        tenant_id: str,
        conversation_id: str,
        max_keys: int = 100
    ) -> Tuple[bool, list, Optional[str]]:
        """
        List all files in a conversation
        Returns: (success, file_list, error_message)
        """
        try:
            prefix = f"tenants/{tenant_id}/conversations/{conversation_id}/"
            
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_keys
            )
            
            files = []
            for obj in response.get('Contents', []):
                files.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'filename': obj['Key'].split('/')[-1]
                })
            
            return True, files, None
            
        except ClientError as e:
            error_message = f"Failed to list files: {str(e)}"
            return False, [], error_message
        except Exception as e:
            error_message = f"Unexpected error listing files: {str(e)}"
            return False, [], error_message
    
    def cleanup_conversation_files(
        self,
        tenant_id: str,
        conversation_id: str
    ) -> Tuple[bool, int, Optional[str]]:
        """
        Delete all files in a conversation (for cleanup)
        Returns: (success, deleted_count, error_message)
        """
        try:
            # List all files first
            success, files, error = self.list_conversation_files(tenant_id, conversation_id, max_keys=1000)
            if not success:
                return False, 0, error
            
            if not files:
                return True, 0, None
            
            # Delete files in batches
            deleted_count = 0
            for file_info in files:
                success, error = self.delete_file(file_info['key'], tenant_id)
                if success:
                    deleted_count += 1
            
            return True, deleted_count, None
            
        except Exception as e:
            error_message = f"Unexpected error during cleanup: {str(e)}"
            return False, 0, error_message
    
    def _validate_tenant_access(self, s3_key: str, tenant_id: str) -> bool:
        """Validate that the S3 key belongs to the specified tenant"""
        expected_prefix = f"tenants/{tenant_id}/"
        return s3_key.startswith(expected_prefix)
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for S3 storage"""
        # Remove or replace problematic characters
        import re
        
        # Keep only alphanumeric, dots, hyphens, underscores
        sanitized = re.sub(r'[^a-zA-Z0-9.\-_]', '_', filename)
        
        # Ensure it doesn't start with a dot or hyphen
        if sanitized.startswith('.') or sanitized.startswith('-'):
            sanitized = 'file_' + sanitized
        
        # Limit length
        if len(sanitized) > 100:
            name, ext = os.path.splitext(sanitized)
            sanitized = name[:95] + ext
        
        return sanitized


# Singleton instance
s3_manager = S3Manager()
