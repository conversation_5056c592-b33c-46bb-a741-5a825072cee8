# services/agent/src/utils/validation.py
# Agent-specific validation utilities (extends shared validation)

from typing import Dict, Any, Optional, List, Tuple

# Use shared validation as base
try:
    from shared.validation_manager import ValidationManager
    from shared.validators import (
        validate_string, validate_email, validate_url, validate_uuid,
        sanitize_string, sanitize_html, ValidationResult
    )
    from shared.exceptions import ValidationException

    # For backward compatibility, alias ValidationException as ValidationError
    ValidationError = ValidationException

    # Create validation manager instance for agent service
    agent_validation_manager = ValidationManager("agent-service")

except ImportError:
    # Fallback for local development
    class ValidationException(Exception):
        pass

    ValidationError = ValidationException
    agent_validation_manager = None


class Validator:
    """Agent-specific validation utilities (uses shared when available)"""

    @staticmethod
    def validate_uuid(value: str, field_name: str = "ID") -> str:
        """Validate UUID format"""
        if agent_validation_manager:
            # Use shared validator
            result = validate_uuid(value)
            if not result.is_valid:
                raise ValidationError(f"{field_name}: {result.error_message}")
            return result.value
        else:
            # Fallback implementation
            try:
                import uuid
                uuid.UUID(value)
                return value
            except (ValueError, TypeError):
                raise ValidationError(f"{field_name} must be a valid UUID")

    @staticmethod
    def validate_required_string(value: Any, field_name: str, min_length: int = 1, max_length: int = None) -> str:
        """Validate required string field"""
        if agent_validation_manager:
            # Use shared validator
            result = validate_string(value, min_length=min_length, max_length=max_length, required=True)
            if not result.is_valid:
                raise ValidationError(f"{field_name}: {result.error_message}")
            return result.value
        else:
            # Fallback implementation
            if not value:
                raise ValidationError(f"{field_name} is required")
            if not isinstance(value, str):
                raise ValidationError(f"{field_name} must be a string")
            value = value.strip()
            if len(value) < min_length:
                raise ValidationError(f"{field_name} must be at least {min_length} characters long")
            if max_length and len(value) > max_length:
                raise ValidationError(f"{field_name} must be no more than {max_length} characters long")
            return value

    @staticmethod
    def validate_optional_string(value: Any, field_name: str, max_length: int = None) -> Optional[str]:
        """Validate optional string field"""
        if value is None or value == "":
            return None
        return Validator.validate_required_string(value, field_name, min_length=0, max_length=max_length)

    @staticmethod
    def validate_url(value: str, field_name: str = "URL") -> str:
        """Validate URL format"""
        if agent_validation_manager:
            # Use shared validator
            result = validate_url(value)
            if not result.is_valid:
                raise ValidationError(f"{field_name}: {result.error_message}")
            return result.value
        else:
            # Fallback implementation
            if not value:
                raise ValidationError(f"{field_name} is required")
            from urllib.parse import urlparse
            try:
                result = urlparse(value)
                if not all([result.scheme, result.netloc]):
                    raise ValidationError(f"{field_name} must be a valid URL")
                if result.scheme not in ['http', 'https']:
                    raise ValidationError(f"{field_name} must use HTTP or HTTPS protocol")
                return value
            except Exception:
                raise ValidationError(f"{field_name} must be a valid URL")

    @staticmethod
    def validate_email(value: str, field_name: str = "Email") -> str:
        """Validate email format"""
        if agent_validation_manager:
            # Use shared validator
            result = validate_email(value)
            if not result.is_valid:
                raise ValidationError(f"{field_name}: {result.error_message}")
            return result.value
        else:
            # Fallback implementation
            if not value:
                raise ValidationError(f"{field_name} is required")
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, value):
                raise ValidationError(f"{field_name} must be a valid email address")
            return value.lower()

    @staticmethod
    def validate_status(value: str, allowed_statuses: List[str], field_name: str = "Status") -> str:
        """Validate status against allowed values"""
        if not value:
            raise ValidationError(f"{field_name} is required")
        if value not in allowed_statuses:
            raise ValidationError(f"{field_name} must be one of: {', '.join(allowed_statuses)}")
        return value

    @staticmethod
    def validate_dict(value: Any, field_name: str, required: bool = True) -> Optional[Dict[str, Any]]:
        """Validate dictionary field"""
        if not required and (value is None or value == {}):
            return {}
        if required and not value:
            raise ValidationError(f"{field_name} is required")
        if not isinstance(value, dict):
            raise ValidationError(f"{field_name} must be a dictionary")
        return value

    @staticmethod
    def validate_list(value: Any, field_name: str, required: bool = True) -> Optional[List[Any]]:
        """Validate list field"""
        if not required and (value is None or value == []):
            return []
        if required and not value:
            raise ValidationError(f"{field_name} is required")
        if not isinstance(value, list):
            raise ValidationError(f"{field_name} must be a list")
        return value
    
    @staticmethod
    def validate_integer(value: Any, field_name: str, min_value: int = None, max_value: int = None) -> int:
        """Validate integer field"""
        if value is None:
            raise ValidationError(f"{field_name} is required")
        
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} must be an integer")
        
        if min_value is not None and int_value < min_value:
            raise ValidationError(f"{field_name} must be at least {min_value}")
        
        if max_value is not None and int_value > max_value:
            raise ValidationError(f"{field_name} must be no more than {max_value}")
        
        return int_value
    
    @staticmethod
    def validate_pagination_params(page: Any = None, limit: Any = None, max_limit: int = 100) -> Tuple[int, int]:
        """Validate pagination parameters"""
        # Validate page
        if page is None:
            page = 1
        else:
            page = Validator.validate_integer(page, "page", min_value=1)
        
        # Validate limit
        if limit is None:
            limit = 50
        else:
            limit = Validator.validate_integer(limit, "limit", min_value=1, max_value=max_limit)
        
        return page, limit


class AgentValidator:
    """Validation utilities specific to Agent entities"""
    
    @staticmethod
    def validate_agent_create_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate agent creation request"""
        validated = {}
        
        # Required fields
        validated['name'] = Validator.validate_required_string(
            data.get('name'), 'Agent name', min_length=2, max_length=100
        )
        
        validated['webhookUrl'] = Validator.validate_url(
            data.get('webhookUrl'), 'Webhook URL'
        )
        
        validated['bearerToken'] = Validator.validate_required_string(
            data.get('bearerToken'), 'Bearer token', min_length=10
        )
        
        validated['secret'] = Validator.validate_required_string(
            data.get('secret'), 'Secret', min_length=8
        )
        
        # Optional fields
        validated['description'] = Validator.validate_optional_string(
            data.get('description'), 'Description', max_length=500
        )
        
        validated['documentWebhookUrl'] = data.get('documentWebhookUrl')
        if validated['documentWebhookUrl']:
            validated['documentWebhookUrl'] = Validator.validate_url(
                validated['documentWebhookUrl'], 'Document webhook URL'
            )
        
        validated['inputParameters'] = Validator.validate_dict(
            data.get('inputParameters'), 'Input parameters', required=False
        )
        
        validated['metadata'] = Validator.validate_dict(
            data.get('metadata'), 'Metadata', required=False
        )
        
        return validated
    
    @staticmethod
    def validate_agent_update_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate agent update request"""
        validated = {}
        
        # All fields are optional for updates
        if 'name' in data:
            validated['name'] = Validator.validate_required_string(
                data['name'], 'Agent name', min_length=2, max_length=100
            )
        
        if 'description' in data:
            validated['description'] = Validator.validate_optional_string(
                data['description'], 'Description', max_length=500
            )
        
        if 'webhookUrl' in data:
            validated['webhookUrl'] = Validator.validate_url(
                data['webhookUrl'], 'Webhook URL'
            )
        
        if 'documentWebhookUrl' in data:
            if data['documentWebhookUrl']:
                validated['documentWebhookUrl'] = Validator.validate_url(
                    data['documentWebhookUrl'], 'Document webhook URL'
                )
            else:
                validated['documentWebhookUrl'] = None
        
        if 'bearerToken' in data:
            validated['bearerToken'] = Validator.validate_required_string(
                data['bearerToken'], 'Bearer token', min_length=10
            )
        
        if 'secret' in data:
            validated['secret'] = Validator.validate_required_string(
                data['secret'], 'Secret', min_length=8
            )
        
        if 'inputParameters' in data:
            validated['inputParameters'] = Validator.validate_dict(
                data['inputParameters'], 'Input parameters', required=False
            )
        
        if 'metadata' in data:
            validated['metadata'] = Validator.validate_dict(
                data['metadata'], 'Metadata', required=False
            )
        
        return validated


class ConversationValidator:
    """Validation utilities specific to Conversation entities"""
    
    @staticmethod
    def validate_conversation_create_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate conversation creation request"""
        validated = {}
        
        # Required fields
        validated['agentId'] = data.get('agentId')
        if not validated['agentId']:
            raise ValidationError("Agent ID is required")
        
        Validator.validate_uuid(validated['agentId'], "Agent ID")
        
        # Optional fields
        validated['metadata'] = Validator.validate_dict(
            data.get('metadata'), 'Metadata', required=False
        )
        
        return validated


class MessageValidator:
    """Validation utilities specific to Message entities"""

    @staticmethod
    def validate_message_send_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate message send request"""
        validated = {}

        # Message type
        message_type = data.get('type', 'text')
        validated['type'] = Validator.validate_status(
            message_type, ['text', 'document', 'audio'], 'Message type'
        )

        # Content validation based on type
        if validated['type'] == 'text':
            validated['content'] = Validator.validate_required_string(
                data.get('content'), 'Message content', min_length=1, max_length=10000
            )
        else:
            validated['content'] = Validator.validate_optional_string(
                data.get('content'), 'Message content', max_length=1000
            )

        # Attachments
        validated['attachments'] = Validator.validate_list(
            data.get('attachments'), 'Attachments', required=False
        )

        # For document/audio messages, attachments are required
        if validated['type'] in ['document', 'audio'] and not validated['attachments']:
            raise ValidationError(f"Attachments are required for {validated['type']} messages")

        # Metadata
        validated['metadata'] = Validator.validate_dict(
            data.get('metadata'), 'Metadata', required=False
        )

        return validated


class RequestValidator:
    """Enhanced request validation utilities"""

    @staticmethod
    def validate_agent_dependencies(agent_id: str) -> Tuple[bool, Optional[str]]:
        """
        Validate if agent can be safely deleted (check dependencies)
        Returns: (can_delete, reason_if_not)
        """
        try:
            # TODO: Implement when conversation management is ready
            # This would check for:
            # 1. Active conversations using this agent
            # 2. Scheduled messages
            # 3. Webhook configurations

            # For now, return True (safe to delete)
            return True, None
        except Exception as e:
            return False, f"Error checking dependencies: {str(e)}"

    @staticmethod
    def validate_webhook_url_accessibility(webhook_url: str) -> Tuple[bool, Optional[str]]:
        """
        Validate if webhook URL is accessible (basic connectivity check)
        Returns: (is_accessible, error_message)
        """
        try:
            import requests
            from urllib.parse import urlparse

            # Basic URL structure validation
            parsed = urlparse(webhook_url)
            if not all([parsed.scheme, parsed.netloc]):
                return False, "Invalid URL structure"

            # Check if it's HTTPS (security requirement)
            if parsed.scheme != 'https':
                return False, "Webhook URL must use HTTPS"

            # Basic connectivity check (with timeout)
            try:
                response = requests.head(webhook_url, timeout=5, allow_redirects=True)
                if response.status_code in [200, 201, 202, 204, 405]:  # 405 is OK for HEAD on POST endpoints
                    return True, None
                else:
                    return False, f"Webhook returned status code: {response.status_code}"
            except requests.exceptions.Timeout:
                return False, "Webhook URL timeout (>5 seconds)"
            except requests.exceptions.ConnectionError:
                return False, "Cannot connect to webhook URL"
            except requests.exceptions.RequestException as e:
                return False, f"Webhook validation error: {str(e)}"

        except ImportError:
            # If requests is not available, skip validation
            return True, "Webhook validation skipped (requests not available)"
        except Exception as e:
            return False, f"Unexpected error validating webhook: {str(e)}"

    @staticmethod
    def validate_agent_name_uniqueness(name: str, exclude_agent_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        Validate if agent name is unique
        Returns: (is_unique, error_message)
        """
        try:
            # TODO: Implement when database service supports name-based queries
            # This would check if another agent with the same name exists

            # For now, return True (assume unique)
            return True, None
        except Exception as e:
            return False, f"Error checking name uniqueness: {str(e)}"

    @staticmethod
    def sanitize_agent_metadata(metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize and validate agent metadata"""
        if not metadata:
            return {}

        sanitized = {}
        max_key_length = 50
        max_value_length = 500
        max_metadata_size = 10  # Maximum number of metadata fields

        if len(metadata) > max_metadata_size:
            raise ValidationError(f"Metadata cannot have more than {max_metadata_size} fields")

        for key, value in metadata.items():
            # Sanitize key
            if not isinstance(key, str):
                raise ValidationError("Metadata keys must be strings")

            if len(key) > max_key_length:
                raise ValidationError(f"Metadata key '{key}' is too long (max {max_key_length} characters)")

            # Sanitize value
            if isinstance(value, str):
                if len(value) > max_value_length:
                    raise ValidationError(f"Metadata value for '{key}' is too long (max {max_value_length} characters)")
                sanitized[key] = value
            elif isinstance(value, (int, float, bool)):
                sanitized[key] = value
            elif isinstance(value, (list, dict)):
                # Convert to string representation for storage
                str_value = str(value)
                if len(str_value) > max_value_length:
                    raise ValidationError(f"Metadata value for '{key}' is too complex")
                sanitized[key] = str_value
            else:
                raise ValidationError(f"Metadata value for '{key}' has unsupported type: {type(value)}")

        return sanitized
