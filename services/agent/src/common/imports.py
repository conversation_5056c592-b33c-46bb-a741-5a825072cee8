# services/agent/src/common/imports.py
# Common imports for agent service handlers and services

"""
Common imports for agent service.
Centralizes all shared layer imports and agent-specific utilities.
"""

import json
import uuid
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

# Shared layer imports
from shared.logger import lambda_logger, log_api_request, log_api_response, log_business_operation
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, AuthContext
from shared.middleware.resilience_middleware import rate_limit, user_resilience
from shared.decorators import measure_performance
from shared.exceptions import (
    ValidationException,
    AuthorizationException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.validation import validate_required_fields
from shared.validators import validate_uuid
from shared.config import get_settings, get_database_config
from shared.database import DynamoDBClient
from shared.dependency_injection import container

# Agent service specific imports
from ..config.dependencies import configure_dependencies


# Common validation helpers
def validate_tenant_access(auth_context: AuthContext, tenant_id: str) -> bool:
    """Validate that user has access to tenant."""
    if auth_context.tenant_id != tenant_id:
        raise AuthorizationException(f"Access denied to tenant {tenant_id}")
    return True


def extract_path_parameter(event: Dict[str, Any], param_name: str) -> str:
    """Extract path parameter from event."""
    path_params = event.get('pathParameters') or {}
    param_value = path_params.get(param_name)
    if not param_value:
        raise ValidationException(f"Missing required path parameter: {param_name}")
    return param_value


def extract_query_parameter(event: Dict[str, Any], param_name: str, default: Any = None) -> Any:
    """Extract query parameter from event."""
    query_params = event.get('queryStringParameters') or {}
    return query_params.get(param_name, default)


def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
    """Parse and validate JSON request body."""
    body_str = event.get('body')
    if not body_str:
        raise ValidationException("Request body is required")
    
    try:
        return json.loads(body_str)
    except json.JSONDecodeError:
        raise ValidationException("Invalid JSON in request body")


# Agent service specific response helpers
def success_response(data: Any, status_code: int = 200) -> Dict[str, Any]:
    """Create success response for agent operations."""
    return APIResponse.success(data, status_code)


def error_response(message: str, status_code: int = 400) -> Dict[str, Any]:
    """Create error response for agent operations."""
    return APIResponse.error(message, status_code)


# Agent service specific logging helpers
def log_agent_operation(
    operation: str,
    agent_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log agent operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="agent",
        entity_id=agent_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details=details or {}
    )


def log_conversation_operation(
    operation: str,
    conversation_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log conversation operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="conversation",
        entity_id=conversation_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details=details or {}
    )


def log_message_operation(
    operation: str,
    message_id: str,
    conversation_id: str,
    tenant_id: str,
    user_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """Log message operation for auditing."""
    log_business_operation(
        lambda_logger,
        operation=operation,
        entity_type="message",
        entity_id=message_id,
        tenant_id=tenant_id,
        user_id=user_id,
        status=status,
        details={
            'conversation_id': conversation_id,
            **(details or {})
        }
    )


# Agent service specific error handling
def handle_agent_error(error: Exception, operation: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle agent service specific errors."""
    if isinstance(error, ValidationException):
        lambda_logger.warning(f"Validation error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 400)
    
    elif isinstance(error, AuthorizationException):
        lambda_logger.warning(f"Authorization error in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 403)
    
    elif isinstance(error, ResourceNotFoundException):
        lambda_logger.warning(f"Resource not found in {operation}", extra={
            'error': str(error),
            'context': context
        })
        return error_response(str(error), 404)
    
    else:
        lambda_logger.error(f"Unexpected error in {operation}", extra={
            'error': str(error),
            'error_type': type(error).__name__,
            'context': context
        })
        return error_response("Internal server error", 500)


# Agent service specific decorator
def agent_handler(operation_name: str, require_auth: bool = True):
    """Decorator for agent service handlers."""
    def decorator(func):
        # Apply shared decorators in correct order
        decorated_func = func
        
        if require_auth:
            decorated_func = require_auth(decorated_func)
        
        decorated_func = rate_limit(requests_per_minute=120)(decorated_func)
        decorated_func = user_resilience(operation_name)(decorated_func)
        decorated_func = measure_performance(f"agent_{operation_name}")(decorated_func)
        
        return decorated_func
    
    return decorator


# Service getters using dependency injection
def get_database_service():
    """Get database service instance."""
    from ..services.database_service import IDatabaseService
    return container.get(IDatabaseService)


def get_optimized_query_service():
    """Get optimized query service instance."""
    from ..services.optimized_queries import IOptimizedQueryService
    return container.get(IOptimizedQueryService)


def get_cache_service():
    """Get cache service instance."""
    from ..services.cache_service import ICacheService
    return container.get(ICacheService)


def get_event_service():
    """Get event service instance."""
    from ..services.event_service import IEventService
    return container.get(IEventService)


def get_metrics_service():
    """Get metrics service instance."""
    from ..services.metrics_service import IMetricsService
    return container.get(IMetricsService)


def get_hybrid_router():
    """Get hybrid router instance."""
    from ..services.hybrid_router import IHybridRouter
    return container.get(IHybridRouter)


# Export all common utilities
__all__ = [
    # Core imports
    'json', 'uuid', 'datetime', 'timezone',
    'Dict', 'Any', 'List', 'Optional', 'Tuple',
    
    # Shared layer
    'lambda_logger', 'log_api_request', 'log_api_response', 'log_business_operation',
    'APIResponse', 'handle_cors_preflight',
    'require_auth', 'AuthContext',
    'rate_limit', 'user_resilience', 'measure_performance',
    'ValidationException', 'AuthorizationException', 'ResourceNotFoundException', 'BusinessLogicException',
    'validate_required_fields', 'validate_uuid',
    'get_settings', 'get_database_config',
    'DynamoDBClient', 'container',
    
    # Agent service utilities
    'validate_tenant_access', 'extract_path_parameter', 'extract_query_parameter',
    'parse_request_body', 'success_response', 'error_response',
    'log_agent_operation', 'log_conversation_operation', 'log_message_operation',
    'handle_agent_error', 'agent_handler',
    
    # Service getters
    'get_database_service', 'get_optimized_query_service', 'get_cache_service',
    'get_event_service', 'get_metrics_service', 'get_hybrid_router'
]
