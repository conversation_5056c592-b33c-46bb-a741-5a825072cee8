# services/agent/src/config/agent_config.py
# Agent service specific configuration using shared config

"""
Agent service specific configuration.
Extends shared configuration with agent-specific settings.
"""

from typing import Dict, Any, Optional
from shared.config import get_settings, get_database_config
from shared.logger import lambda_logger


def get_agent_config() -> Dict[str, Any]:
    """Get agent service specific configuration"""
    settings = get_settings()
    
    return {
        # Agent configuration
        'max_agents_per_tenant': settings.get('max_agents_per_tenant', 10),
        'agent_timeout_seconds': settings.get('agent_timeout_seconds', 30),
        'webhook_timeout_seconds': settings.get('webhook_timeout_seconds', 15),
        'webhook_retry_attempts': settings.get('webhook_retry_attempts', 3),
        'webhook_retry_delay_seconds': settings.get('webhook_retry_delay_seconds', 2),
        
        # Conversation configuration
        'max_conversations_per_user': settings.get('max_conversations_per_user', 100),
        'conversation_timeout_hours': settings.get('conversation_timeout_hours', 24),
        'auto_archive_conversations_days': settings.get('auto_archive_conversations_days', 30),
        'max_messages_per_conversation': settings.get('max_messages_per_conversation', 1000),
        
        # Message configuration
        'max_message_length': settings.get('max_message_length', 4000),
        'max_attachment_size_mb': settings.get('max_attachment_size_mb', 50),
        'supported_attachment_types': settings.get('supported_attachment_types', [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain', 'text/csv',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]),
        
        # Performance configuration
        'query_batch_size': settings.get('query_batch_size', 25),
        'cache_ttl_seconds': settings.get('cache_ttl_seconds', 300),
        'cache_enabled': settings.get('cache_enabled', True),
        'metrics_enabled': settings.get('metrics_enabled', True),
        
        # N8N integration configuration
        'n8n_webhook_base_url': settings.get('n8n_webhook_base_url'),
        'n8n_api_key': settings.get('n8n_api_key'),
        'n8n_timeout_seconds': settings.get('n8n_timeout_seconds', 30),
        'n8n_retry_attempts': settings.get('n8n_retry_attempts', 3),
        
        # File handling configuration
        's3_bucket_name': settings.get('s3_bucket_name'),
        's3_upload_timeout_seconds': settings.get('s3_upload_timeout_seconds', 60),
        'max_file_upload_size_mb': settings.get('max_file_upload_size_mb', 100),
        'allowed_file_extensions': settings.get('allowed_file_extensions', [
            '.pdf', '.doc', '.docx', '.txt', '.csv', '.xlsx', '.xls',
            '.jpg', '.jpeg', '.png', '.gif', '.webp'
        ]),
        
        # Security configuration
        'webhook_signature_verification': settings.get('webhook_signature_verification', True),
        'rate_limit_per_agent': settings.get('rate_limit_per_agent', 100),  # requests per minute
        'rate_limit_per_tenant': settings.get('rate_limit_per_tenant', 1000),  # requests per minute
        'max_concurrent_conversations': settings.get('max_concurrent_conversations', 50),
        
        # Monitoring configuration
        'detailed_logging': settings.get('detailed_logging', False),
        'performance_monitoring': settings.get('performance_monitoring', True),
        'error_tracking': settings.get('error_tracking', True),
        'audit_logging': settings.get('audit_logging', True),
        
        # External service configuration
        'external_api_timeout': settings.get('external_api_timeout', 30),
        'external_api_retry_attempts': settings.get('external_api_retry_attempts', 3),
        'external_api_retry_delay': settings.get('external_api_retry_delay', 1),
    }


def get_webhook_config() -> Dict[str, Any]:
    """Get webhook specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('webhooks_enabled', True),
        'timeout_seconds': settings.get('webhook_timeout_seconds', 15),
        'retry_attempts': settings.get('webhook_retry_attempts', 3),
        'retry_delay_seconds': settings.get('webhook_retry_delay_seconds', 2),
        'max_payload_size_mb': settings.get('webhook_max_payload_size_mb', 10),
        
        # Security settings
        'signature_verification': settings.get('webhook_signature_verification', True),
        'allowed_hosts': settings.get('webhook_allowed_hosts', []),
        'blocked_hosts': settings.get('webhook_blocked_hosts', [
            'localhost', '127.0.0.1', '0.0.0.0', '::1'
        ]),
        
        # Headers and authentication
        'default_headers': settings.get('webhook_default_headers', {
            'Content-Type': 'application/json',
            'User-Agent': 'Agent-SCL-Platform/1.0'
        }),
        'auth_header_name': settings.get('webhook_auth_header_name', 'Authorization'),
        
        # Rate limiting
        'rate_limit_per_webhook': settings.get('webhook_rate_limit', 60),  # requests per minute
        'burst_limit': settings.get('webhook_burst_limit', 10),
        
        # Monitoring
        'log_requests': settings.get('webhook_log_requests', True),
        'log_responses': settings.get('webhook_log_responses', True),
        'track_performance': settings.get('webhook_track_performance', True),
    }


def get_file_config() -> Dict[str, Any]:
    """Get file handling configuration"""
    settings = get_settings()
    
    return {
        # S3 configuration
        's3_bucket_name': settings.get('s3_bucket_name'),
        's3_region': settings.get('s3_region', 'us-east-1'),
        's3_upload_timeout': settings.get('s3_upload_timeout_seconds', 60),
        's3_download_timeout': settings.get('s3_download_timeout_seconds', 30),
        
        # File size limits
        'max_file_size_mb': settings.get('max_file_upload_size_mb', 100),
        'max_total_size_per_request_mb': settings.get('max_total_size_per_request_mb', 200),
        'max_files_per_request': settings.get('max_files_per_request', 10),
        
        # File type restrictions
        'allowed_extensions': settings.get('allowed_file_extensions', [
            '.pdf', '.doc', '.docx', '.txt', '.csv', '.xlsx', '.xls',
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.mp3'
        ]),
        'blocked_extensions': settings.get('blocked_file_extensions', [
            '.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js'
        ]),
        
        # Content type validation
        'validate_content_type': settings.get('validate_file_content_type', True),
        'allowed_mime_types': settings.get('allowed_mime_types', [
            'application/pdf', 'text/plain', 'text/csv',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'audio/mpeg', 'audio/wav'
        ]),
        
        # Security settings
        'virus_scanning': settings.get('file_virus_scanning', True),
        'content_scanning': settings.get('file_content_scanning', True),
        'quarantine_suspicious_files': settings.get('quarantine_suspicious_files', True),
        
        # Storage settings
        'storage_class': settings.get('s3_storage_class', 'STANDARD'),
        'encryption_enabled': settings.get('s3_encryption_enabled', True),
        'versioning_enabled': settings.get('s3_versioning_enabled', False),
        'lifecycle_policy_days': settings.get('s3_lifecycle_policy_days', 90),
        
        # Processing settings
        'auto_extract_text': settings.get('auto_extract_text', True),
        'generate_thumbnails': settings.get('generate_thumbnails', True),
        'compress_images': settings.get('compress_images', True),
        'max_image_width': settings.get('max_image_width', 2048),
        'max_image_height': settings.get('max_image_height', 2048),
    }


def get_cache_config() -> Dict[str, Any]:
    """Get cache specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('cache_enabled', True),
        'redis_enabled': settings.get('redis_enabled', True),
        'memory_cache_enabled': settings.get('memory_cache_enabled', True),
        
        # TTL settings (in seconds)
        'agent_cache_ttl': settings.get('agent_cache_ttl', 900),  # 15 minutes
        'conversation_cache_ttl': settings.get('conversation_cache_ttl', 600),  # 10 minutes
        'message_cache_ttl': settings.get('message_cache_ttl', 300),  # 5 minutes
        'user_cache_ttl': settings.get('user_cache_ttl', 1800),  # 30 minutes
        'webhook_response_cache_ttl': settings.get('webhook_response_cache_ttl', 60),  # 1 minute
        
        # Performance settings
        'max_cache_size': settings.get('max_cache_size', 1000),
        'cache_batch_size': settings.get('cache_batch_size', 25),
        'cache_compression': settings.get('cache_compression', True),
        
        # Redis specific (if available)
        'redis_host': settings.get('redis_host'),
        'redis_port': settings.get('redis_port', 6379),
        'redis_password': settings.get('redis_password'),
        'redis_ssl': settings.get('redis_ssl', True),
        'redis_timeout': settings.get('redis_timeout', 5),
        'redis_max_connections': settings.get('redis_max_connections', 50),
    }


def get_monitoring_config() -> Dict[str, Any]:
    """Get monitoring specific configuration"""
    settings = get_settings()
    
    return {
        'enabled': settings.get('monitoring_enabled', True),
        'cloudwatch_enabled': settings.get('cloudwatch_enabled', True),
        'custom_metrics_enabled': settings.get('custom_metrics_enabled', True),
        'detailed_monitoring': settings.get('detailed_monitoring', True),
        
        # Metric collection settings
        'metric_buffer_size': settings.get('metric_buffer_size', 20),
        'metric_flush_interval': settings.get('metric_flush_interval', 30),
        'realtime_metrics_enabled': settings.get('realtime_metrics_enabled', True),
        
        # Performance thresholds
        'response_time_warning_ms': settings.get('response_time_warning_ms', 2000),
        'response_time_critical_ms': settings.get('response_time_critical_ms', 5000),
        'error_rate_warning': settings.get('error_rate_warning', 0.05),
        'error_rate_critical': settings.get('error_rate_critical', 0.10),
        'memory_usage_warning': settings.get('memory_usage_warning', 0.80),
        'memory_usage_critical': settings.get('memory_usage_critical', 0.90),
        
        # Alerting configuration
        'alerts_enabled': settings.get('alerts_enabled', True),
        'alert_sns_topic': settings.get('alert_sns_topic'),
        'alert_email': settings.get('alert_email'),
        'alert_slack_webhook': settings.get('alert_slack_webhook'),
        
        # Health check configuration
        'health_check_enabled': settings.get('health_check_enabled', True),
        'health_check_interval': settings.get('health_check_interval', 60),
        'health_check_timeout': settings.get('health_check_timeout', 10),
    }


# Legacy support - maintain backward compatibility
class AgentConfig:
    """Legacy agent configuration class for backward compatibility"""
    
    def __init__(self):
        self._config = get_agent_config()
        lambda_logger.warning("AgentConfig class is deprecated. Use get_agent_config() function instead.")
    
    def __getattr__(self, name):
        return self._config.get(name)
    
    def get(self, key, default=None):
        return self._config.get(key, default)


# Create legacy instance for backward compatibility
config = AgentConfig()
