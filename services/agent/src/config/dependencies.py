# services/agent/src/config/dependencies.py
# Dependency injection configuration for agent service

"""
Dependency injection configuration for agent service.
Registers all services and their implementations.
"""

from shared.dependency_injection import container
from shared.logger import lambda_logger

# Import service interfaces and implementations
from ..services.database_service import IDatabaseService, DatabaseService
from ..services.optimized_queries import IOptimizedQueryService, OptimizedQueryService
from ..services.cache_service import ICacheService, AgentCacheService
from ..services.event_service import IEventService, AgentEventService
from ..services.metrics_service import IMetricsService, AgentMetricsService
from ..services.hybrid_router import IHybridRouter, HybridRouter


def configure_dependencies():
    """
    Configure dependency injection for agent service.

    This function registers all service implementations with their interfaces
    in the dependency injection container.
    """
    try:
        # Register database service as singleton
        container.register_singleton(IDatabaseService, DatabaseService)

        # Register optimized query service as singleton
        container.register_singleton(IOptimizedQueryService, OptimizedQueryService)

        # Register cache service as singleton
        container.register_singleton(ICacheService, AgentCacheService)

        # Register event service as singleton
        container.register_singleton(IEventService, AgentEventService)

        # Register metrics service as singleton
        container.register_singleton(IMetricsService, AgentMetricsService)

        # Register hybrid router as singleton
        container.register_singleton(IHybridRouter, HybridRouter)

        lambda_logger.info("Agent service dependencies configured successfully")

    except Exception as e:
        lambda_logger.error(f"Failed to configure agent service dependencies: {str(e)}")
        raise


# Auto-configure dependencies when module is imported
configure_dependencies()
