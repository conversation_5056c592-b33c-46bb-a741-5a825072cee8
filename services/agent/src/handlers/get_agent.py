# services/agent/src/handlers/get_agent.py
# Handler for getting specific agent by ID

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    class ValidationException(Exception): pass
    class ResourceNotFoundException(Exception): pass

from ..services.database_service import database_service
from ..services.metrics_service import agent_metrics_service


@require_auth
@rate_limit(requests_per_minute=120)  # Standard limit for read operations
@user_resilience("get_agent")
@measure_performance("agent_get_agent")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get specific agent by ID.

    GET /agents/{agentId}

    Returns agent details if it belongs to the authenticated tenant.
    Accessible by any authenticated user within the tenant.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)
        
        # Extract and validate path parameters
        path_params = event.get('pathParameters') or {}
        agent_id = path_params.get('agentId')

        if not agent_id:
            return APIResponse.validation_error(
                "Missing required path parameter: agentId",
                validation_errors=[{"field": "agentId", "message": "Agent ID is required"}]
            )

        # Validate agent ID format (UUID)
        try:
            uuid.UUID(agent_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid agent ID format",
                validation_errors=[{"field": "agentId", "message": "Agent ID must be a valid UUID"}]
            )

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            event.get('path', f'/agents/{agent_id}'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing get agent request", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        # Get agent from database (with caching optimization)
        agent = database_service.get_agent(agent_id)
        if not agent:
            lambda_logger.warning("Agent not found", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Verify agent belongs to the authenticated tenant (multi-tenancy security)
        if agent.get('TenantId') != auth_context.tenant_id:
            lambda_logger.warning("Agent access denied - different tenant", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_tenant': agent.get('TenantId'),
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")
        
        # Record metrics
        agent_metrics_service.record_api_latency(
            endpoint="get_agent",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=200
        )

        # Format response data (exclude sensitive information for security)
        response_data = {
            'agentId': agent.get('AgentId'),
            'name': agent.get('Name'),
            'description': agent.get('Description'),
            'webhookUrl': agent.get('WebhookUrl'),
            'documentWebhookUrl': agent.get('DocumentWebhookUrl'),
            'status': agent.get('Status'),
            'inputParameters': agent.get('InputParameters', {}),
            'metadata': agent.get('Metadata', {}),
            'createdAt': agent.get('CreatedAt'),
            'updatedAt': agent.get('UpdatedAt'),
            'createdBy': agent.get('CreatedBy')
            # Note: bearerToken and secret are excluded for security
        }

        lambda_logger.info("Agent retrieved successfully", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'agent_name': agent.get('Name'),
            'agent_status': agent.get('Status'),
            'requested_by': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        response = APIResponse.success(
            data=response_data,
            message="Agent retrieved successfully"
        )

        log_api_response(response, "get_agent")
        return response

    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in get_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 404)
        log_api_response(response, "get_agent")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in get_agent", extra={
            'request_id': request_id,
            'error': str(e)
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "get_agent")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in get_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "get_agent")
        return response
