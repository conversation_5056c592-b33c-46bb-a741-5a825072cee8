# services/agent/src/handlers/list_conversations.py
# Handler for listing user conversations with filtering

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    class ValidationException(Exception): pass

from ..services.database_service import database_service
from ..services.metrics_service import agent_metrics_service
from ..services.cache_service import agent_cache_service
from ..services.optimized_queries import optimized_query_service
from ..utils.validation import Validator
from ..utils.config import config


@require_auth
@rate_limit(requests_per_minute=180)  # Higher limit for read operations
@user_resilience("list_conversations")
@measure_performance("agent_list_conversations")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    List conversations for the authenticated user.

    GET /conversations

    Query parameters:
    - status: Filter by conversation status (active, closed, archived)
    - agentId: Filter by specific agent
    - limit: Number of results to return (default: 50, max: 100)

    Returns conversations belonging to the authenticated user within their tenant.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)

        # Extract and validate query parameters
        query_params = event.get('queryStringParameters') or {}
        
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            event.get('path', '/conversations'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing list conversations request", extra={
            'request_id': request_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'query_params': query_params
        })

        # Validate and parse query parameters
        try:
            # Status filter
            status_filter = query_params.get('status')
            if status_filter:
                allowed_statuses = ['active', 'closed', 'archived']
                if status_filter not in allowed_statuses:
                    return APIResponse.validation_error(
                        f"Invalid status filter. Allowed values: {', '.join(allowed_statuses)}",
                        validation_errors=[{
                            "field": "status",
                            "message": f"Status must be one of: {', '.join(allowed_statuses)}"
                        }]
                    )

            # Agent ID filter
            agent_id_filter = query_params.get('agentId')
            if agent_id_filter:
                try:
                    uuid.UUID(agent_id_filter)
                except ValueError:
                    return APIResponse.validation_error(
                        "Invalid agent ID format",
                        validation_errors=[{
                            "field": "agentId",
                            "message": "Agent ID must be a valid UUID"
                        }]
                    )

            # Limit parameter
            limit = min(int(query_params.get('limit', 50)), 100)  # Max 100 conversations
            if limit < 1:
                limit = 50

        except ValueError as e:
            return APIResponse.validation_error(
                f"Invalid query parameters: {str(e)}",
                validation_errors=[{
                    "field": "limit",
                    "message": "Limit must be a valid number"
                }]
            )
        
        # Try to get conversations from cache first
        cached_conversations = agent_cache_service.get_user_conversations_cache(
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            status_filter=status_filter
        )

        if cached_conversations and not agent_id_filter:
            # Use cached data if available and no agent filter
            conversations = cached_conversations
            lambda_logger.debug("Using cached conversations", extra={
                'request_id': request_id,
                'user_id': auth_context.user_id,
                'cached_count': len(cached_conversations)
            })
        else:
            # Get conversations from database using optimized queries
            conversations = optimized_query_service.get_user_conversations_optimized(
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                status_filter=status_filter,
                limit=limit * 2  # Get more to account for filtering
            )

            # Cache the result if no agent filter
            if not agent_id_filter and conversations:
                agent_cache_service.set_user_conversations_cache(
                    tenant_id=auth_context.tenant_id,
                    user_id=auth_context.user_id,
                    conversations=conversations,
                    status_filter=status_filter,
                    ttl_minutes=3
                )
        
        # Apply filters
        filtered_conversations = []
        for conversation in conversations:
            # Status filter
            if status_filter and conversation.get('Status') != status_filter:
                continue
            
            # Agent ID filter
            if agent_id_filter and conversation.get('AgentId') != agent_id_filter:
                continue
            
            # Tenant security check (extra safety)
            if conversation.get('TenantId') != auth_context.tenant_id:
                lambda_logger.warning("Conversation tenant mismatch detected", extra={
                    'conversation_id': conversation.get('ConversationId'),
                    'conversation_tenant': conversation.get('TenantId'),
                    'user_tenant': auth_context.tenant_id
                })
                continue
            
            filtered_conversations.append(conversation)
        
        # Apply limit to filtered conversations
        limited_conversations = filtered_conversations[:limit]
        
        # Get agent information for each conversation
        conversation_data = []
        for conversation in limited_conversations:
            agent_id = conversation.get('AgentId')
            agent = database_service.get_agent(agent_id) if agent_id else None
            
            conversation_item = {
                'conversationId': conversation.get('ConversationId'),
                'agentId': agent_id,
                'agentName': agent.get('Name') if agent else 'Unknown Agent',
                'agentStatus': agent.get('Status') if agent else 'unknown',
                'status': conversation.get('Status'),
                'metadata': conversation.get('Metadata', {}),
                'createdAt': conversation.get('CreatedAt'),
                'updatedAt': conversation.get('UpdatedAt')
            }
            
            conversation_data.append(conversation_item)
        
        # Record metrics
        agent_metrics_service.record_api_latency(
            endpoint="list_conversations",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=200
        )

        # Format response data
        response_data = {
            'conversations': conversation_data,
            'count': len(conversation_data),
            'totalFiltered': len(filtered_conversations),
            'limit': limit,
            'filters': {
                'status': status_filter,
                'agentId': agent_id_filter
            }
        }
        
        lambda_logger.info("Conversations retrieved successfully", extra={
            'request_id': request_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'total_filtered': len(filtered_conversations),
            'returned_conversations': len(conversation_data),
            'limit': limit,
            'filters_applied': bool(status_filter or agent_id_filter)
        })

        response = APIResponse.success(
            data=response_data,
            message="Conversations retrieved successfully"
        )

        log_api_response(response, "list_conversations")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in list_conversations", extra={
            'request_id': request_id,
            'error': str(e),
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "list_conversations")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in list_conversations", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "list_conversations")
        return response
