# services/agent/src/handlers/delete_agent.py
# Handler for deleting agents with dependency validation

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    class ValidationException(Exception): pass
    class ResourceNotFoundException(Exception): pass
    class ConflictException(Exception): pass

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..services.metrics_service import agent_metrics_service
from ..services.optimized_queries import optimized_query_service


@require_auth
@rate_limit(requests_per_minute=30)  # Lower limit for delete operations
@user_resilience("delete_agent")
@measure_performance("agent_delete_agent")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Delete agent by ID.

    DELETE /agents/{agentId}

    Deletes an agent if:
    1. It belongs to the authenticated tenant
    2. It has no active conversations

    Accessible by any authenticated user within the tenant that owns the agent.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)
        
        # Extract and validate path parameters
        path_params = event.get('pathParameters') or {}
        agent_id = path_params.get('agentId')

        if not agent_id:
            return APIResponse.validation_error(
                "Missing required path parameter: agentId",
                validation_errors=[{"field": "agentId", "message": "Agent ID is required"}]
            )

        # Validate agent ID format (UUID)
        try:
            uuid.UUID(agent_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid agent ID format",
                validation_errors=[{"field": "agentId", "message": "Agent ID must be a valid UUID"}]
            )

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'DELETE'),
            event.get('path', f'/agents/{agent_id}'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing delete agent request", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        # Check if agent exists and belongs to tenant
        existing_agent = database_service.get_agent(agent_id)
        if not existing_agent:
            lambda_logger.warning("Agent not found for deletion", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Verify agent belongs to the authenticated tenant (multi-tenancy security)
        if existing_agent.get('TenantId') != auth_context.tenant_id:
            lambda_logger.warning("Agent deletion denied - different tenant", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_tenant': existing_agent.get('TenantId'),
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")
        
        # Check for active conversations using this agent
        try:
            active_conversations_count = optimized_query_service.get_agent_conversations_count(
                agent_id=agent_id,
                status='active'
            )

            if active_conversations_count > 0:
                lambda_logger.warning("Cannot delete agent with active conversations", extra={
                    'request_id': request_id,
                    'agent_id': agent_id,
                    'agent_name': existing_agent.get('Name'),
                    'active_conversations': active_conversations_count,
                    'user_id': auth_context.user_id
                })
                raise ConflictException(
                    f"Cannot delete agent '{existing_agent.get('Name')}' because it has {active_conversations_count} active conversation(s). "
                    f"Please close or archive all conversations before deleting the agent."
                )

            lambda_logger.info("Dependency check passed for agent deletion", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_name': existing_agent.get('Name'),
                'active_conversations': active_conversations_count
            })

        except ConflictException:
            # Re-raise conflict exceptions
            raise
        except Exception as e:
            lambda_logger.warning("Could not perform dependency check", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'error': str(e)
            })
            # For safety, assume there might be dependencies and warn user
            lambda_logger.warning("Proceeding with deletion despite dependency check failure")

        # Store agent info for logging and events before deletion
        agent_name = existing_agent.get('Name')
        agent_status = existing_agent.get('Status')

        # Perform deletion
        success, error_msg = database_service.delete_agent(agent_id)
        if not success:
            lambda_logger.error("Failed to delete agent", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'error': error_msg,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(f"Failed to delete agent: {error_msg}", 500)
        
        # Log agent deleted event
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        agent_event_service.log_agent_deleted(
            agent_id=agent_id,
            agent_name=agent_name,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            client_ip=client_ip
        )

        # Record metrics
        agent_metrics_service.record_agent_deleted(auth_context.tenant_id)
        agent_metrics_service.record_api_latency(
            endpoint="delete_agent",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=200
        )

        # Log successful deletion
        lambda_logger.info("Agent deleted successfully", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'agent_name': agent_name,
            'agent_status': agent_status,
            'deleted_by': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        # Return success response with minimal data
        response_data = {
            'agentId': agent_id,
            'name': agent_name,
            'status': agent_status,
            'deletedAt': lambda_logger.get_current_timestamp(),
            'deletedBy': auth_context.user_id
        }

        response = APIResponse.success(
            data=response_data,
            message="Agent deleted successfully"
        )

        log_api_response(response, "delete_agent")
        return response

    except ConflictException as e:
        lambda_logger.warning("Conflict error in delete_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 409)
        log_api_response(response, "delete_agent")
        return response

    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in delete_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 404)
        log_api_response(response, "delete_agent")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in delete_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "delete_agent")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in delete_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "delete_agent")
        return response
