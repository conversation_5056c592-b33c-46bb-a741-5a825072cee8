# services/agent/src/handlers/update_agent.py
# Handler for updating agent configuration

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth
    from shared.request_utils import parse_json_body

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    def parse_json_body(event):
        try:
            body = event.get('body', '{}')
            return json.loads(body) if body else {}
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

    class ValidationException(Exception): pass
    class ResourceNotFoundException(Exception): pass

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..services.metrics_service import agent_metrics_service
from ..utils.validation import AgentValidator, ValidationError


@require_auth
@rate_limit(requests_per_minute=60)  # Lower limit for update operations
@user_resilience("update_agent")
@measure_performance("agent_update_agent")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update agent configuration.

    PUT /agents/{agentId}

    Request body (all fields optional):
    {
        "name": "string",
        "description": "string",
        "webhookUrl": "string",
        "documentWebhookUrl": "string",
        "inputParameters": {},
        "bearerToken": "string",
        "secret": "string",
        "metadata": {}
    }

    Accessible by any authenticated user within the tenant that owns the agent.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)
        
        # Extract and validate path parameters
        path_params = event.get('pathParameters') or {}
        agent_id = path_params.get('agentId')

        if not agent_id:
            return APIResponse.validation_error(
                "Missing required path parameter: agentId",
                validation_errors=[{"field": "agentId", "message": "Agent ID is required"}]
            )

        # Validate agent ID format (UUID)
        try:
            uuid.UUID(agent_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid agent ID format",
                validation_errors=[{"field": "agentId", "message": "Agent ID must be a valid UUID"}]
            )

        # Parse and validate request body
        try:
            body = parse_json_body(event)
        except ValidationException as e:
            return APIResponse.validation_error(str(e))

        if not body:
            return APIResponse.validation_error(
                "Request body is required for update operation",
                validation_errors=[{"field": "body", "message": "At least one field must be provided for update"}]
            )

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'PUT'),
            event.get('path', f'/agents/{agent_id}'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing update agent request", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'update_fields': list(body.keys())
        })

        # Validate update request
        try:
            validated_data = AgentValidator.validate_agent_update_request(body)
        except ValidationError as e:
            return APIResponse.validation_error(str(e))
        except Exception as e:
            return APIResponse.validation_error(f"Validation error: {str(e)}")

        # Check if agent exists and belongs to tenant
        existing_agent = database_service.get_agent(agent_id)
        if not existing_agent:
            lambda_logger.warning("Agent not found for update", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Verify agent belongs to the authenticated tenant (multi-tenancy security)
        if existing_agent.get('TenantId') != auth_context.tenant_id:
            lambda_logger.warning("Agent update denied - different tenant", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_tenant': existing_agent.get('TenantId'),
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")
        
        # Store original values for change tracking
        original_values = {
            'name': existing_agent.get('Name'),
            'description': existing_agent.get('Description'),
            'webhookUrl': existing_agent.get('WebhookUrl'),
            'documentWebhookUrl': existing_agent.get('DocumentWebhookUrl'),
            'status': existing_agent.get('Status')
        }

        # Perform update
        success, error_msg = database_service.update_agent(agent_id, validated_data)
        if not success:
            lambda_logger.error("Failed to update agent", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'error': error_msg,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(f"Failed to update agent: {error_msg}", 500)

        # Get updated agent for response
        updated_agent = database_service.get_agent(agent_id)

        # Track changes for events
        changes = {}
        for field, original_value in original_values.items():
            new_value = updated_agent.get(field.title().replace('Url', 'URL'))
            if original_value != new_value:
                changes[field] = {'from': original_value, 'to': new_value}

        # Log agent updated event
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        agent_event_service.log_agent_updated(
            agent_id=agent_id,
            agent_name=updated_agent.get('Name'),
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            changes=changes,
            client_ip=client_ip
        )

        # Record metrics
        agent_metrics_service.record_api_latency(
            endpoint="update_agent",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=200
        )

        # Format response data (exclude sensitive information)
        response_data = {
            'agentId': updated_agent.get('AgentId'),
            'name': updated_agent.get('Name'),
            'description': updated_agent.get('Description'),
            'webhookUrl': updated_agent.get('WebhookUrl'),
            'documentWebhookUrl': updated_agent.get('DocumentWebhookUrl'),
            'status': updated_agent.get('Status'),
            'inputParameters': updated_agent.get('InputParameters', {}),
            'metadata': updated_agent.get('Metadata', {}),
            'createdAt': updated_agent.get('CreatedAt'),
            'updatedAt': updated_agent.get('UpdatedAt'),
            'updatedBy': auth_context.user_id,
            'changesApplied': list(changes.keys())
            # Note: bearerToken and secret are excluded for security
        }

        lambda_logger.info("Agent updated successfully", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'agent_name': updated_agent.get('Name'),
            'updated_fields': list(validated_data.keys()),
            'changes_applied': list(changes.keys()),
            'updated_by': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        response = APIResponse.success(
            data=response_data,
            message="Agent updated successfully"
        )

        log_api_response(response, "update_agent")
        return response

    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in update_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 404)
        log_api_response(response, "update_agent")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in update_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "update_agent")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in update_agent", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "update_agent")
        return response
