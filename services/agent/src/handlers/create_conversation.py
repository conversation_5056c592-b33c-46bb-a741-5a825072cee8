# services/agent/src/handlers/create_conversation.py
# Handler for creating new conversations with agents

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth
    from shared.request_utils import parse_json_body

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    def parse_json_body(event):
        try:
            body = event.get('body', '{}')
            return json.loads(body) if body else {}
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

    class ValidationException(Exception): pass
    class ResourceNotFoundException(Exception): pass

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..services.metrics_service import agent_metrics_service
from ..services.cache_service import agent_cache_service
from ..models.conversation import ConversationCreateRequest
from ..utils.validation import ConversationValidator, ValidationError
from ..utils.config import config


@require_auth
@rate_limit(requests_per_minute=120)  # Standard limit for conversation creation
@user_resilience("create_conversation")
@measure_performance("agent_create_conversation")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Create a new conversation with an agent.

    POST /conversations

    Request body:
    {
        "agentId": "uuid",
        "metadata": {}  // optional
    }

    Creates a conversation between the authenticated user and the specified agent.
    The agent must be active and belong to the same tenant as the user.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)

        # Parse and validate request body
        try:
            body = parse_json_body(event)
        except ValidationException as e:
            return APIResponse.validation_error(str(e))

        if not body:
            return APIResponse.validation_error(
                "Request body is required",
                validation_errors=[{"field": "body", "message": "Request body with agentId is required"}]
            )
        
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'POST'),
            event.get('path', '/conversations'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing create conversation request", extra={
            'request_id': request_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'agent_id': body.get('agentId')
        })

        # Validate request
        try:
            validated_data = ConversationValidator.validate_conversation_create_request(body)
        except ValidationError as e:
            return APIResponse.validation_error(str(e))

        agent_id = validated_data['agentId']

        # Validate agent ID format (UUID)
        try:
            uuid.UUID(agent_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid agent ID format",
                validation_errors=[{"field": "agentId", "message": "Agent ID must be a valid UUID"}]
            )

        # Verify that the agent exists and belongs to tenant
        agent = database_service.get_agent(agent_id)
        if not agent:
            lambda_logger.warning("Agent not found for conversation creation", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Verify agent belongs to the authenticated tenant (multi-tenancy security)
        if agent.get('TenantId') != auth_context.tenant_id:
            lambda_logger.warning("Conversation creation denied - agent from different tenant", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_tenant': agent.get('TenantId'),
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Check if agent is active
        if agent.get('Status') != 'active':
            lambda_logger.warning("Attempt to create conversation with inactive agent", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_status': agent.get('Status'),
                'agent_name': agent.get('Name'),
                'user_id': auth_context.user_id
            })
            return APIResponse.error(f"Agent '{agent.get('Name')}' is not currently active", 400)

        # Check if agent has valid webhook configuration
        if not agent.get('WebhookUrl'):
            lambda_logger.warning("Agent missing webhook configuration", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_name': agent.get('Name'),
                'user_id': auth_context.user_id
            })
            return APIResponse.error(f"Agent '{agent.get('Name')}' is not properly configured", 400)
        
        # Create conversation request object
        conversation_request = ConversationCreateRequest(
            validated_data,
            auth_context.tenant_id,
            auth_context.user_id
        )

        # Validate conversation request
        is_valid, error_msg = conversation_request.validate()
        if not is_valid:
            return APIResponse.validation_error(error_msg)

        # Convert to conversation entity
        conversation = conversation_request.to_conversation()

        # Generate unique webhook URL for this conversation
        webhook_base_url = config.get_webhook_base_url()
        conversation.generate_webhook_url(webhook_base_url)

        # Save to database
        success, error_msg = database_service.create_conversation(conversation.to_dict())
        if not success:
            lambda_logger.error("Failed to create conversation", extra={
                'request_id': request_id,
                'error': error_msg,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id,
                'agent_id': agent_id
            })
            return APIResponse.error(f"Failed to create conversation: {error_msg}", 500)

        # Invalidate user conversations cache
        agent_cache_service.invalidate_user_conversations_cache(
            auth_context.tenant_id,
            auth_context.user_id
        )
        
        # Log conversation created event
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        agent_event_service.log_conversation_created(
            conversation_id=conversation.conversation_id,
            agent_id=agent_id,
            agent_name=agent.get('Name'),
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            webhook_url=conversation.webhook_url,
            client_ip=client_ip
        )

        # Record metrics
        agent_metrics_service.record_conversation_created(
            tenant_id=auth_context.tenant_id,
            agent_id=agent_id
        )
        agent_metrics_service.record_api_latency(
            endpoint="create_conversation",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=201
        )

        # Format response data
        response_data = {
            'conversationId': conversation.conversation_id,
            'agentId': conversation.agent_id,
            'agentName': agent.get('Name'),
            'status': conversation.status,
            'webhookUrl': conversation.webhook_url,
            'metadata': conversation.metadata,
            'createdAt': conversation.created_at.isoformat(),
            'createdBy': auth_context.user_id
        }

        lambda_logger.info("Conversation created successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation.conversation_id,
            'agent_id': agent_id,
            'agent_name': agent.get('Name'),
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'webhook_url': conversation.webhook_url
        })

        response = APIResponse.success(
            data=response_data,
            message="Conversation created successfully",
            status_code=201
        )

        log_api_response(response, "create_conversation")
        return response

    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in create_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 404)
        log_api_response(response, "create_conversation")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in create_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "create_conversation")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in create_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "create_conversation")
        return response
