# services/agent/src/handlers/list_messages.py
# Handler for listing conversation messages with pagination

import json
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, AuthorizationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth
    from shared.request_utils import extract_path_parameters, extract_query_parameters
except ImportError:
    # Fallback for local development
    from ..utils.response import success_response, error_response, not_found_response, forbidden_response
    from ..utils.auth import extract_user_context
    from ..utils.validation import Validator
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"
    
    lambda_logger = MockLogger()
    
    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}
    
    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return success_response(data, message, status_code)
        
        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return error_response(message, status_code)
        
        @staticmethod
        def not_found(message="Not found"):
            return not_found_response(message)
        
        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return error_response(message, 400, details=validation_errors)
    
    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass
    
    def extract_path_parameters(event, required_params=None):
        path_params = event.get('pathParameters') or {}
        if required_params:
            for param in required_params:
                if not path_params.get(param):
                    raise ValidationException(f"Missing required path parameter: {param}")
        return path_params
    
    def extract_query_parameters(event):
        return event.get('queryStringParameters') or {}

from ..services.database_service import database_service
from ..utils.validation import Validator
from ..utils.error_handler import handle_agent_service_errors, AgentValidationError
from ..utils.config import config


@require_auth
@rate_limit(requests_per_minute=240)  # Higher limit for read operations
@user_resilience("list_messages")
@measure_performance("agent_list_messages")
@handle_agent_service_errors
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    List messages for a conversation with pagination.
    
    GET /conversations/{conversationId}/messages
    
    Query parameters:
    - limit: Number of messages to return (default: 50, max: 100)
    - page: Page number for pagination (default: 1)
    - direction: Filter by message direction (inbound, outbound)
    - type: Filter by message type (text, document, audio)
    
    Required permissions: authenticated user (can only see messages from own conversations)
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            # Fallback to manual extraction for local development
            user_id, tenant_id, user_name = extract_user_context(event)
            if not user_id or not tenant_id:
                return APIResponse.error("Authentication required", 401)
            
            auth_context = type('AuthContext', (), {
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_name': user_name,
                'role': 'user'
            })()
        
        # Extract and validate path parameters
        try:
            path_params = extract_path_parameters(event, required_params=['conversationId'])
            conversation_id = path_params['conversationId']
        except ValidationException as e:
            return APIResponse.validation_error(str(e))
        
        # Extract and validate query parameters
        query_params = extract_query_parameters(event)
        
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            event.get('path', f'/conversations/{conversation_id}/messages'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )
        
        start_time = lambda_logger.get_current_timestamp()
        
        lambda_logger.info("Processing list messages request", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'query_params': query_params
        })
        
        # Validate conversation ID format (UUID)
        try:
            import uuid
            uuid.UUID(conversation_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid conversation ID format",
                validation_errors=[{"field": "conversationId", "message": "Conversation ID must be a valid UUID"}]
            )
        
        # Verify conversation exists and user has access
        conversation = database_service.get_conversation(auth_context.tenant_id, conversation_id)
        if not conversation:
            lambda_logger.warning("Conversation not found for list messages", extra={
                'conversation_id': conversation_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")
        
        # Verify user has access to this conversation
        conversation_user_id = conversation.get('UserId')
        conversation_tenant_id = conversation.get('TenantId')
        
        # Check tenant access (primary security check)
        if conversation_tenant_id != auth_context.tenant_id:
            lambda_logger.warning("Unauthorized message list attempt - tenant mismatch", extra={
                'conversation_id': conversation_id,
                'conversation_tenant': conversation_tenant_id,
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise AuthorizationException("Access denied to this conversation")
        
        # Check user access (secondary security check)
        if conversation_user_id != auth_context.user_id:
            lambda_logger.warning("Unauthorized message list attempt - user mismatch", extra={
                'conversation_id': conversation_id,
                'conversation_user': conversation_user_id,
                'requesting_user': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise AuthorizationException("Access denied to this conversation")
        
        # Validate and parse query parameters
        try:
            # Direction filter
            direction_filter = query_params.get('direction')
            if direction_filter:
                allowed_directions = ['inbound', 'outbound']
                if direction_filter not in allowed_directions:
                    raise AgentValidationError(
                        f"Invalid direction filter. Allowed values: {', '.join(allowed_directions)}"
                    )
            
            # Type filter
            type_filter = query_params.get('type')
            if type_filter:
                allowed_types = ['text', 'document', 'audio']
                if type_filter not in allowed_types:
                    raise AgentValidationError(
                        f"Invalid type filter. Allowed values: {', '.join(allowed_types)}"
                    )
            
            # Pagination parameters
            page, limit = Validator.validate_pagination_params(
                query_params.get('page'),
                query_params.get('limit'),
                max_limit=config.max_page_size
            )
            
        except Exception as e:
            if isinstance(e, AgentValidationError):
                raise
            raise AgentValidationError(f"Invalid query parameters: {str(e)}")
        
        # Get messages from database
        messages = database_service.list_conversation_messages(
            auth_context.tenant_id,
            conversation_id,
            limit=limit
        )
        
        # Apply filters
        filtered_messages = []
        for message in messages:
            # Direction filter
            if direction_filter and message.get('Direction') != direction_filter:
                continue
            
            # Type filter
            if type_filter and message.get('Type') != type_filter:
                continue
            
            # Security check (extra safety)
            if message.get('TenantId') != auth_context.tenant_id:
                lambda_logger.warning("Message tenant mismatch detected", extra={
                    'message_id': message.get('MessageId'),
                    'message_tenant': message.get('TenantId'),
                    'user_tenant': auth_context.tenant_id
                })
                continue
            
            filtered_messages.append(message)
        
        # Sort messages by timestamp (newest first)
        filtered_messages.sort(key=lambda x: x.get('Timestamp', ''), reverse=True)
        
        # Apply pagination
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_messages = filtered_messages[start_index:end_index]
        
        # Format message data for response
        message_data = []
        for message in paginated_messages:
            message_item = {
                'messageId': message.get('MessageId'),
                'conversationId': message.get('ConversationId'),
                'direction': message.get('Direction'),
                'type': message.get('Type'),
                'content': message.get('Content'),
                'attachments': message.get('Attachments', []),
                'metadata': message.get('Metadata', {}),
                'timestamp': message.get('Timestamp')
            }
            
            message_data.append(message_item)
        
        # Calculate pagination metadata
        total_messages = len(filtered_messages)
        total_pages = (total_messages + limit - 1) // limit  # Ceiling division
        has_next_page = page < total_pages
        has_previous_page = page > 1
        
        # Format response data
        response_data = {
            'messages': message_data,
            'conversation': {
                'conversationId': conversation.get('ConversationId'),
                'agentId': conversation.get('AgentId'),
                'status': conversation.get('Status')
            },
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total_messages,
                'totalPages': total_pages,
                'hasNextPage': has_next_page,
                'hasPreviousPage': has_previous_page
            },
            'filters': {
                'direction': direction_filter,
                'type': type_filter
            }
        }
        
        lambda_logger.info("Messages retrieved successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'total_messages': total_messages,
            'returned_messages': len(message_data),
            'page': page,
            'filters_applied': bool(direction_filter or type_filter)
        })
        
        response = APIResponse.success(
            data=response_data,
            message="Messages retrieved successfully"
        )
        
        log_api_response(response, "list_messages")
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Authorization error in list_messages", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 403)
        log_api_response(response, "list_messages")
        return response
        
    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in list_messages", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None
        })
        response = APIResponse.not_found(str(e))
        log_api_response(response, "list_messages")
        return response
        
    except Exception as e:
        # The @handle_agent_service_errors decorator will handle this
        raise
