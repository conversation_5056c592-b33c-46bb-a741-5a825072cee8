# services/agent/src/handlers/create_agent.py
# Handler for creating new agents

from ..common.imports import *




@agent_handler("create_agent", require_auth=True)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Create a new agent.

    POST /agents

    Request body:
    {
        "name": "string",
        "description": "string",
        "webhookUrl": "string",
        "documentWebhookUrl": "string",  // optional
        "inputParameters": {},           // optional
        "bearerToken": "string",         // optional
        "secret": "string",              // optional
        "metadata": {}                   // optional
    }

    Required permissions: admin user
    """

    try:
        # Extract auth context (set by @require_auth decorator)
        auth_context = event.get('auth_context')
        if not auth_context:
            return error_response("Authentication required", 401)

        # Check admin permissions
        if not hasattr(auth_context, 'role') or auth_context.role != 'admin':
            return error_response("Admin privileges required", 403)

        # Parse and validate request body
        body = parse_request_body(event)

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'POST'),
            event.get('path', '/agents'),
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        # Validate required fields
        validate_required_fields(body, ['name', 'webhookUrl'])

        # Get services using dependency injection
        database_service = get_database_service()
        metrics_service = get_metrics_service()
        event_service = get_event_service()

        # Prepare agent data
        agent_data = {
            'name': body['name'],
            'description': body.get('description', ''),
            'webhook_url': body['webhookUrl'],
            'document_webhook_url': body.get('documentWebhookUrl'),
            'input_parameters': body.get('inputParameters', {}),
            'bearer_token': body.get('bearerToken'),
            'secret': body.get('secret'),
            'metadata': body.get('metadata', {}),
            'tenant_id': auth_context.tenant_id,
            'created_by': auth_context.user_id
        }

        # Log operation start
        log_agent_operation(
            operation="create_agent",
            agent_id="pending",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            status="started",
            details={'agent_name': agent_data['name']}
        )

        # Save to database
        success, error_msg = database_service.create_agent(agent_data)
        if not success:
            log_agent_operation(
                operation="create_agent",
                agent_id="failed",
                tenant_id=auth_context.tenant_id,
                user_id=auth_context.user_id,
                status="failed",
                details={'error': error_msg}
            )
            return error_response(f"Failed to create agent: {error_msg}", 500)

        # Record metrics
        metrics_service.record_agent_operation(
            operation="create",
            success=True,
            duration_ms=0,  # Will be calculated by decorator
            tenant_id=auth_context.tenant_id
        )

        # Get created agent ID (assuming it's returned by the service)
        agent_id = str(uuid.uuid4())  # This should come from the database service

        # Publish event
        event_service.publish_agent_created(
            agent_data={'agent_id': agent_id, **agent_data},
            user_id=auth_context.user_id,
            tenant_id=auth_context.tenant_id
        )

        # Log operation completion
        log_agent_operation(
            operation="create_agent",
            agent_id=agent_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            status="completed",
            details={'agent_name': agent_data['name']}
        )

        # Format response data
        response_data = {
            'agentId': agent_id,
            'name': agent_data['name'],
            'description': agent_data['description'],
            'webhookUrl': agent_data['webhook_url'],
            'documentWebhookUrl': agent_data.get('document_webhook_url'),
            'status': 'active',
            'inputParameters': agent_data['input_parameters'],
            'metadata': agent_data['metadata'],
            'createdAt': datetime.now(timezone.utc).isoformat(),
            'createdBy': auth_context.user_id
        }

        return success_response(response_data, 201)

    except Exception as e:
        return handle_agent_error(e, "create_agent", {
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
