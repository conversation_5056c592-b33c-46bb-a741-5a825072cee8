# services/agent/src/handlers/update_agent_status.py
# Handler for updating agent status (activate/deactivate)

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth
    from shared.request_utils import parse_json_body

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    def parse_json_body(event):
        try:
            body = event.get('body', '{}')
            return json.loads(body) if body else {}
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

    class ValidationException(Exception): pass
    class ResourceNotFoundException(Exception): pass

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..services.metrics_service import agent_metrics_service
from ..services.cache_service import agent_cache_service


@require_auth
@rate_limit(requests_per_minute=60)  # Standard limit for status updates
@user_resilience("update_agent_status")
@measure_performance("agent_update_status")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update agent status (activate/deactivate).

    PATCH /agents/{agentId}/status

    Request body:
    {
        "status": "active" | "inactive"
    }

    Accessible by any authenticated user within the tenant that owns the agent.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)
        
        # Extract and validate path parameters
        path_params = event.get('pathParameters') or {}
        agent_id = path_params.get('agentId')

        if not agent_id:
            return APIResponse.validation_error(
                "Missing required path parameter: agentId",
                validation_errors=[{"field": "agentId", "message": "Agent ID is required"}]
            )

        # Validate agent ID format (UUID)
        try:
            uuid.UUID(agent_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid agent ID format",
                validation_errors=[{"field": "agentId", "message": "Agent ID must be a valid UUID"}]
            )

        # Parse and validate request body
        try:
            body = parse_json_body(event)
        except ValidationException as e:
            return APIResponse.validation_error(str(e))

        if not body:
            return APIResponse.validation_error(
                "Request body is required",
                validation_errors=[{"field": "body", "message": "Request body with status field is required"}]
            )

        # Validate status in request body
        new_status = body.get('status')
        if not new_status:
            return APIResponse.validation_error(
                "Status is required",
                validation_errors=[{"field": "status", "message": "Status field is required"}]
            )

        # Validate status value
        allowed_statuses = ['active', 'inactive']
        if new_status not in allowed_statuses:
            return APIResponse.validation_error(
                f"Invalid status value",
                validation_errors=[{
                    "field": "status",
                    "message": f"Status must be one of: {', '.join(allowed_statuses)}"
                }]
            )

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'PATCH'),
            event.get('path', f'/agents/{agent_id}/status'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing update agent status request", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'requested_status': new_status
        })
        
        # Check if agent exists and belongs to tenant
        existing_agent = database_service.get_agent(agent_id)
        if not existing_agent:
            lambda_logger.warning("Agent not found for status update", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Verify agent belongs to the authenticated tenant (multi-tenancy security)
        if existing_agent.get('TenantId') != auth_context.tenant_id:
            lambda_logger.warning("Agent status update denied - different tenant", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'agent_tenant': existing_agent.get('TenantId'),
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException(f"Agent with ID {agent_id} not found")

        # Check if status is already the requested value
        current_status = existing_agent.get('Status')
        if current_status == new_status:
            lambda_logger.info("Agent status already set to requested value", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'current_status': current_status,
                'requested_status': new_status,
                'user_id': auth_context.user_id
            })

            # Return current agent data
            response_data = {
                'agentId': existing_agent.get('AgentId'),
                'name': existing_agent.get('Name'),
                'status': existing_agent.get('Status'),
                'previousStatus': current_status,
                'updatedAt': existing_agent.get('UpdatedAt'),
                'updatedBy': auth_context.user_id
            }

            return APIResponse.success(
                data=response_data,
                message=f"Agent status is already {new_status}"
            )
        
        # Perform status update
        success, error_msg = database_service.update_agent_status(agent_id, new_status)
        if not success:
            lambda_logger.error("Failed to update agent status", extra={
                'request_id': request_id,
                'agent_id': agent_id,
                'new_status': new_status,
                'error': error_msg,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(f"Failed to update agent status: {error_msg}", 500)

        # Get updated agent for response
        updated_agent = database_service.get_agent(agent_id)

        # Invalidate cache after status change
        agent_cache_service.invalidate_agent_cache(agent_id)

        # Log agent status changed event
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        agent_event_service.log_agent_status_changed(
            agent_id=agent_id,
            agent_name=updated_agent.get('Name'),
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            old_status=current_status,
            new_status=new_status,
            client_ip=client_ip
        )

        # Record metrics
        agent_metrics_service.record_agent_status_change(
            tenant_id=auth_context.tenant_id,
            old_status=current_status,
            new_status=new_status
        )
        agent_metrics_service.record_api_latency(
            endpoint="update_agent_status",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=200
        )

        # Format response data
        response_data = {
            'agentId': updated_agent.get('AgentId'),
            'name': updated_agent.get('Name'),
            'status': updated_agent.get('Status'),
            'previousStatus': current_status,
            'updatedAt': updated_agent.get('UpdatedAt'),
            'updatedBy': auth_context.user_id
        }

        lambda_logger.info("Agent status updated successfully", extra={
            'request_id': request_id,
            'agent_id': agent_id,
            'agent_name': updated_agent.get('Name'),
            'previous_status': current_status,
            'new_status': new_status,
            'updated_by': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        response = APIResponse.success(
            data=response_data,
            message=f"Agent status updated to {new_status}"
        )

        log_api_response(response, "update_agent_status")
        return response

    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in update_agent_status", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 404)
        log_api_response(response, "update_agent_status")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in update_agent_status", extra={
            'request_id': request_id,
            'error': str(e),
            'agent_id': agent_id if 'agent_id' in locals() else None
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "update_agent_status")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in update_agent_status", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'agent_id': agent_id if 'agent_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "update_agent_status")
        return response
