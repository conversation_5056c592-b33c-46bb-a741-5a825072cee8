# services/agent/src/handlers/send_message.py
# Handler for sending messages to agents (NOT for user-to-user chat)
# NOTE: This handler now works with the unified message orchestrator

import json
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, AuthorizationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth
    from shared.request_utils import extract_path_parameters, parse_json_body
except ImportError:
    # Fallback for local development
    from ..utils.response import success_response, error_response, not_found_response, forbidden_response
    from ..utils.auth import extract_user_context
    from ..utils.validation import MessageValidator, ValidationError
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"
    
    lambda_logger = MockLogger()
    
    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}
    
    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return success_response(data, message, status_code)
        
        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return error_response(message, status_code)
        
        @staticmethod
        def not_found(message="Not found"):
            return not_found_response(message)
        
        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return error_response(message, 400, details=validation_errors)
    
    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass
    
    def extract_path_parameters(event, required_params=None):
        path_params = event.get('pathParameters') or {}
        if required_params:
            for param in required_params:
                if not path_params.get(param):
                    raise ValidationException(f"Missing required path parameter: {param}")
        return path_params
    
    def parse_json_body(event):
        try:
            body = event.get('body', '{}')
            return json.loads(body) if body else {}
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..models.message import Message
from ..utils.validation import MessageValidator, ValidationError
from ..utils.error_handler import handle_agent_service_errors, AgentValidationError
from ..utils.webhook_manager import WebhookManager
from ..utils.config import config


@require_auth
@rate_limit(requests_per_minute=120)  # Standard limit for message sending
@user_resilience("send_agent_message")
@measure_performance("agent_send_message")
@handle_agent_service_errors
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Send a message to an AGENT (Feedo/Forecaster) in a conversation.

    This handler is for agent communication, NOT user-to-user chat.
    For user-to-user chat, use Chat Service endpoints.

    POST /conversations/{conversationId}/messages
    
    Request body:
    {
        "content": "string",           // Message content
        "type": "text",                // Message type: text, document, audio
        "attachments": [],             // Optional file attachments
        "metadata": {}                 // Optional metadata
    }
    
    Required permissions: authenticated user (can only send to own conversations)
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            # Fallback to manual extraction for local development
            user_id, tenant_id, user_name = extract_user_context(event)
            if not user_id or not tenant_id:
                return APIResponse.error("Authentication required", 401)
            
            auth_context = type('AuthContext', (), {
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_name': user_name,
                'role': 'user'
            })()
        
        # Extract and validate path parameters
        try:
            path_params = extract_path_parameters(event, required_params=['conversationId'])
            conversation_id = path_params['conversationId']
        except ValidationException as e:
            return APIResponse.validation_error(str(e))
        
        # Parse and validate request body
        try:
            body = parse_json_body(event)
        except ValidationException as e:
            return APIResponse.validation_error(str(e))
        
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'POST'),
            event.get('path', f'/conversations/{conversation_id}/messages'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )
        
        start_time = lambda_logger.get_current_timestamp()
        
        lambda_logger.info("Processing send message request", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'message_type': body.get('type', 'text'),
            'has_attachments': bool(body.get('attachments'))
        })
        
        # Validate conversation ID format (UUID)
        try:
            import uuid
            uuid.UUID(conversation_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid conversation ID format",
                validation_errors=[{"field": "conversationId", "message": "Conversation ID must be a valid UUID"}]
            )
        
        # Validate message request
        try:
            validated_data = MessageValidator.validate_message_send_request(body)
        except ValidationError as e:
            raise AgentValidationError(str(e))
        
        # Verify conversation exists and user has access
        conversation = database_service.get_conversation(auth_context.tenant_id, conversation_id)
        if not conversation:
            lambda_logger.warning("Conversation not found for send message", extra={
                'conversation_id': conversation_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")
        
        # Verify user has access to this conversation
        conversation_user_id = conversation.get('UserId')
        conversation_tenant_id = conversation.get('TenantId')
        
        # Check tenant access (primary security check)
        if conversation_tenant_id != auth_context.tenant_id:
            lambda_logger.warning("Unauthorized message send attempt - tenant mismatch", extra={
                'conversation_id': conversation_id,
                'conversation_tenant': conversation_tenant_id,
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise AuthorizationException("Access denied to this conversation")
        
        # Check user access (secondary security check)
        if conversation_user_id != auth_context.user_id:
            lambda_logger.warning("Unauthorized message send attempt - user mismatch", extra={
                'conversation_id': conversation_id,
                'conversation_user': conversation_user_id,
                'requesting_user': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise AuthorizationException("Access denied to this conversation")
        
        # Check if conversation is active (can only send messages to active conversations)
        conversation_status = conversation.get('Status')
        if conversation_status != 'active':
            lambda_logger.warning("Attempt to send message to inactive conversation", extra={
                'conversation_id': conversation_id,
                'conversation_status': conversation_status,
                'user_id': auth_context.user_id
            })
            raise AgentValidationError(f"Cannot send messages to {conversation_status} conversation")
        
        # Get agent information
        agent_id = conversation.get('AgentId')
        agent = database_service.get_agent(agent_id) if agent_id else None
        
        if not agent:
            lambda_logger.error("Agent not found for conversation", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException("Agent not found for this conversation")
        
        # Check if agent is active
        if agent.get('Status') != 'active':
            lambda_logger.warning("Attempt to send message to inactive agent", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_status': agent.get('Status'),
                'user_id': auth_context.user_id
            })
            raise AgentValidationError(f"Agent '{agent.get('Name')}' is not currently active")
        
        # Create message entity
        message = Message(
            conversation_id=conversation_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            direction='outbound',  # Message from user to agent
            message_type=validated_data['type'],
            content=validated_data['content'],
            attachments=validated_data.get('attachments', []),
            metadata={
                'agent_id': agent_id,
                'user_name': getattr(auth_context, 'user_name', None),
                **validated_data.get('metadata', {})
            }
        )
        
        # Save message to database
        success, error_msg = database_service.create_message(message.to_dict())
        if not success:
            lambda_logger.error("Failed to save message", extra={
                'conversation_id': conversation_id,
                'error': error_msg,
                'user_id': auth_context.user_id
            })
            return APIResponse.error(f"Failed to save message: {error_msg}", 500)
        
        # Send message to agent via webhook (asynchronous)
        try:
            webhook_url = agent.get('WebhookUrl')
            if webhook_url:
                # Create webhook payload
                webhook_payload = WebhookManager.create_webhook_payload_for_agent(
                    conversation_id=conversation_id,
                    user_id=auth_context.user_id,
                    tenant_id=auth_context.tenant_id,
                    message=validated_data['content'],
                    user_name=getattr(auth_context, 'user_name', None),
                    message_type=validated_data['type'],
                    metadata=validated_data.get('metadata', {})
                )
                
                # TODO: Implement actual webhook sending (async)
                # This would typically be done via SQS or SNS for reliability
                lambda_logger.info("Webhook payload prepared for agent", extra={
                    'conversation_id': conversation_id,
                    'agent_id': agent_id,
                    'webhook_url': webhook_url,
                    'message_id': message.message_id
                })
                
            else:
                lambda_logger.warning("Agent has no webhook URL configured", extra={
                    'agent_id': agent_id,
                    'agent_name': agent.get('Name'),
                    'conversation_id': conversation_id
                })
                
        except Exception as e:
            # Don't fail the message save if webhook fails
            lambda_logger.error("Failed to send webhook to agent", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'error': str(e),
                'message_id': message.message_id
            })
        
        # Format response data
        response_data = {
            'messageId': message.message_id,
            'conversationId': conversation_id,
            'direction': message.direction,
            'type': message.message_type,
            'content': message.content,
            'attachments': message.attachments,
            'metadata': message.metadata,
            'timestamp': message.timestamp.isoformat(),
            'agent': {
                'agentId': agent_id,
                'name': agent.get('Name'),
                'status': agent.get('Status')
            }
        }
        
        # Log message sent event
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        agent_event_service.log_message_sent(
            message_id=message.message_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            message_type=message.message_type,
            has_attachments=bool(message.attachments),
            client_ip=client_ip
        )

        lambda_logger.info("Message sent successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'message_id': message.message_id,
            'agent_id': agent_id,
            'agent_name': agent.get('Name'),
            'message_type': message.message_type,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })
        
        response = APIResponse.success(
            data=response_data,
            message="Message sent successfully",
            status_code=201
        )
        
        log_api_response(response, "send_message")
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Authorization error in send_message", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 403)
        log_api_response(response, "send_message")
        return response
        
    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in send_message", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None
        })
        response = APIResponse.not_found(str(e))
        log_api_response(response, "send_message")
        return response
        
    except Exception as e:
        # The @handle_agent_service_errors decorator will handle this
        raise
