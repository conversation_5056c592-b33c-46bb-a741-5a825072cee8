# services/agent/src/handlers/list_agents.py
# Handler for listing active agents

from ..common.imports import *
@agent_handler("list_agents", require_auth=True)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    List all active agents for the authenticated tenant.

    GET /agents

    Query parameters:
    - status: Filter by status (optional, defaults to 'active')
    - limit: Maximum number of agents to return (optional, max 100)

    Returns only agents belonging to the authenticated tenant.
    """

    try:
        # Extract auth context (set by @require_auth decorator)
        auth_context = event.get('auth_context')
        if not auth_context:
            return error_response("Authentication required", 401)

        # Parse query parameters
        status_filter = extract_query_parameter(event, 'status', 'active')
        limit = min(int(extract_query_parameter(event, 'limit', 50)), 100)  # Max 100 agents

        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            event.get('path', '/agents'),
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        # Get services using dependency injection
        database_service = get_database_service()
        cache_service = get_cache_service()
        metrics_service = get_metrics_service()

        # Get agents from database (with caching optimization)
        agents = database_service.list_active_agents(auth_context.tenant_id)

        # Filter agents by status if needed
        if status_filter != 'active':
            agents = [agent for agent in agents if agent.get('Status') == status_filter]

        # Apply limit
        limited_agents = agents[:limit]

        # Format response data
        response_data = []
        for agent in limited_agents:
            response_data.append({
                'agentId': agent.get('AgentId'),
                'name': agent.get('Name'),
                'description': agent.get('Description'),
                'status': agent.get('Status'),
                'webhookUrl': agent.get('WebhookUrl'),
                'documentWebhookUrl': agent.get('DocumentWebhookUrl'),
                'inputParameters': agent.get('InputParameters', {}),
                'metadata': agent.get('Metadata', {}),
                'createdAt': agent.get('CreatedAt'),
                'updatedAt': agent.get('UpdatedAt'),
                'createdBy': agent.get('CreatedBy')
            })

        # Record metrics
        metrics_service.record_agent_operation(
            operation="list",
            success=True,
            duration_ms=0,  # Will be calculated by decorator
            tenant_id=auth_context.tenant_id
        )

        return success_response({
            'agents': response_data,
            'count': len(response_data),
            'totalCount': len(agents),
            'limit': limit,
                'status': status_filter
            })

    except Exception as e:
        return handle_agent_error(e, "list_agents", {
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
