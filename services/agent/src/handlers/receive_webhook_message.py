# services/agent/src/handlers/receive_webhook_message.py
# Handler for receiving asynchronous messages from agents via webhooks

import json
from typing import Dict, Any
from datetime import datetime

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.request_utils import extract_path_parameters, parse_json_body
except ImportError:
    # Fallback for local development
    from ..utils.response import success_response, error_response, not_found_response, rate_limit_response
    from ..utils.validation import Validator
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"
    
    lambda_logger = MockLogger()
    
    def rate_limit(**kwargs): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}
    
    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return success_response(data, message, status_code)
        
        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return error_response(message, status_code)
        
        @staticmethod
        def not_found(message="Not found"):
            return not_found_response(message)
        
        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return error_response(message, 400, details=validation_errors)
    
    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass
    
    def extract_path_parameters(event, required_params=None):
        path_params = event.get('pathParameters') or {}
        if required_params:
            for param in required_params:
                if not path_params.get(param):
                    raise ValidationException(f"Missing required path parameter: {param}")
        return path_params
    
    def parse_json_body(event):
        try:
            body = event.get('body', '{}')
            return json.loads(body) if body else {}
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..models.message import Message
from ..utils.error_handler import handle_agent_service_errors, AgentValidationError


# Rate limiting for webhook endpoints (higher limit but still controlled)
@rate_limit(requests_per_minute=300)  # Higher limit for webhook operations
@measure_performance("agent_receive_webhook_message")
@handle_agent_service_errors
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Receive asynchronous messages from agents via conversation-specific webhooks.
    
    POST /webhook/conversation/{conversationId}
    
    Expected payload from agent:
    {
        "message": "string",           // Message content from agent
        "messageType": "text",         // Type: text, document, audio
        "attachments": [],             // Optional file attachments
        "metadata": {}                 // Optional metadata from agent
    }
    
    No JWT authorization required - validation is done via conversation existence and status.
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    source_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
    
    try:
        # Extract and validate path parameters
        try:
            path_params = extract_path_parameters(event, required_params=['conversationId'])
            conversation_id = path_params['conversationId']
        except ValidationException as e:
            return APIResponse.validation_error(str(e))
        
        # Parse request body
        try:
            body = parse_json_body(event)
        except ValidationException as e:
            return APIResponse.validation_error(str(e))
        
        lambda_logger.info("Processing webhook message", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'source_ip': source_ip,
            'message_type': body.get('messageType', 'text'),
            'has_attachments': bool(body.get('attachments'))
        })
        
        # Validate conversation ID format (UUID)
        try:
            import uuid
            uuid.UUID(conversation_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid conversation ID format",
                validation_errors=[{"field": "conversationId", "message": "Conversation ID must be a valid UUID"}]
            )
        
        # Validate required fields in webhook payload
        message_content = body.get('message')
        if not message_content:
            return APIResponse.validation_error(
                "Message content is required",
                validation_errors=[{"field": "message", "message": "Message content cannot be empty"}]
            )
        
        message_type = body.get('messageType', 'text')
        allowed_types = ['text', 'document', 'audio']
        if message_type not in allowed_types:
            return APIResponse.validation_error(
                f"Invalid message type",
                validation_errors=[{
                    "field": "messageType", 
                    "message": f"Message type must be one of: {', '.join(allowed_types)}"
                }]
            )
        
        # Get conversation to validate it exists and extract tenant/user info
        # We need to try different tenant IDs since we don't have auth context
        # This is a security consideration - we'll need to implement a lookup mechanism
        conversation = None
        conversation_tenant_id = None
        
        # For now, we'll implement a simple lookup by scanning
        # TODO: Optimize this with a GSI or separate lookup table
        try:
            # This is a simplified approach - in production, we'd need a more efficient lookup
            # We could use a GSI on conversation ID or a separate lookup table
            
            # For now, we'll assume we can extract tenant from conversation metadata
            # This would need to be implemented in the database service
            conversation_data = database_service.get_conversation_by_id_global(conversation_id)
            if conversation_data:
                conversation = conversation_data['conversation']
                conversation_tenant_id = conversation_data['tenant_id']
        except Exception as e:
            lambda_logger.warning("Error looking up conversation", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
        
        if not conversation:
            lambda_logger.warning("Conversation not found for webhook", extra={
                'conversation_id': conversation_id,
                'source_ip': source_ip
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")
        
        # Check if conversation is active (can only receive messages for active conversations)
        conversation_status = conversation.get('Status')
        if conversation_status != 'active':
            lambda_logger.warning("Webhook message for inactive conversation", extra={
                'conversation_id': conversation_id,
                'conversation_status': conversation_status,
                'source_ip': source_ip
            })
            raise AgentValidationError(f"Cannot send messages to {conversation_status} conversation")
        
        # Extract conversation details
        user_id = conversation.get('UserId')
        agent_id = conversation.get('AgentId')
        
        # Create message entity
        message = Message(
            conversation_id=conversation_id,
            tenant_id=conversation_tenant_id,
            user_id=user_id,
            direction='inbound',  # Message from agent to user
            message_type=message_type,
            content=message_content,
            attachments=body.get('attachments', []),
            metadata={
                'source': 'webhook',
                'agent_id': agent_id,
                'webhook_ip': source_ip,
                'webhook_timestamp': datetime.utcnow().isoformat(),
                **body.get('metadata', {})
            }
        )
        
        # Save message to database
        success, error_msg = database_service.create_message(message.to_dict())
        if not success:
            lambda_logger.error("Failed to save webhook message", extra={
                'conversation_id': conversation_id,
                'error': error_msg,
                'source_ip': source_ip
            })
            return APIResponse.error(f"Failed to save message: {error_msg}", 500)
        
        # Log message received event
        agent_event_service.log_message_received(
            message_id=message.message_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            tenant_id=conversation_tenant_id,
            user_id=user_id,
            message_type=message_type,
            source_ip=source_ip
        )

        # Log successful webhook message receipt
        lambda_logger.info("Webhook message saved successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'message_id': message.message_id,
            'agent_id': agent_id,
            'user_id': user_id,
            'tenant_id': conversation_tenant_id,
            'message_type': message_type,
            'source_ip': source_ip
        })
        
        # Return minimal response (don't expose internal details)
        response_data = {
            'messageId': message.message_id,
            'conversationId': conversation_id,
            'status': 'received',
            'timestamp': message.timestamp.isoformat()
        }
        
        response = APIResponse.success(
            data=response_data,
            message="Message received successfully"
        )
        
        log_api_response(response, "receive_webhook_message")
        return response
        
    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in webhook", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'source_ip': source_ip
        })
        response = APIResponse.not_found(str(e))
        log_api_response(response, "receive_webhook_message")
        return response
        
    except Exception as e:
        # The @handle_agent_service_errors decorator will handle this
        raise
