# services/agent/src/handlers/archive_conversation.py
# Handler for archiving conversations

import json
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, AuthorizationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth
    from shared.request_utils import extract_path_parameters
except ImportError:
    # Fallback for local development
    from ..utils.response import success_response, error_response, not_found_response, forbidden_response
    from ..utils.auth import extract_user_context
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"
    
    lambda_logger = MockLogger()
    
    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}
    
    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return success_response(data, message, status_code)
        
        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return error_response(message, status_code)
        
        @staticmethod
        def not_found(message="Not found"):
            return not_found_response(message)
        
        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return error_response(message, 400, details=validation_errors)
    
    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass
    
    def extract_path_parameters(event, required_params=None):
        path_params = event.get('pathParameters') or {}
        if required_params:
            for param in required_params:
                if not path_params.get(param):
                    raise ValidationException(f"Missing required path parameter: {param}")
        return path_params

from ..services.database_service import database_service
from ..services.event_service import agent_event_service
from ..utils.error_handler import handle_agent_service_errors, AgentValidationError


@require_auth
@rate_limit(requests_per_minute=60)  # Lower limit for state-changing operations
@user_resilience("archive_conversation")
@measure_performance("agent_archive_conversation")
@handle_agent_service_errors
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Archive a conversation.
    
    PATCH /conversations/{conversationId}/archive
    
    Required permissions: authenticated user (can only archive own conversations)
    
    Note: Archived conversations preserve data but are hidden from normal listings.
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            # Fallback to manual extraction for local development
            user_id, tenant_id, user_name = extract_user_context(event)
            if not user_id or not tenant_id:
                return APIResponse.error("Authentication required", 401)
            
            auth_context = type('AuthContext', (), {
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_name': user_name,
                'role': 'user'
            })()
        
        # Extract and validate path parameters
        try:
            path_params = extract_path_parameters(event, required_params=['conversationId'])
            conversation_id = path_params['conversationId']
        except ValidationException as e:
            return APIResponse.validation_error(str(e))
        
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'PATCH'),
            event.get('path', f'/conversations/{conversation_id}/archive'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )
        
        start_time = lambda_logger.get_current_timestamp()
        
        lambda_logger.info("Processing archive conversation request", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })
        
        # Validate conversation ID format (UUID)
        try:
            import uuid
            uuid.UUID(conversation_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid conversation ID format",
                validation_errors=[{"field": "conversationId", "message": "Conversation ID must be a valid UUID"}]
            )
        
        # Get conversation from database
        conversation = database_service.get_conversation(auth_context.tenant_id, conversation_id)
        if not conversation:
            lambda_logger.warning("Conversation not found for archiving", extra={
                'conversation_id': conversation_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")
        
        # Verify user has access to this conversation
        conversation_user_id = conversation.get('UserId')
        conversation_tenant_id = conversation.get('TenantId')
        
        # Check tenant access (primary security check)
        if conversation_tenant_id != auth_context.tenant_id:
            lambda_logger.warning("Unauthorized conversation archive attempt - tenant mismatch", extra={
                'conversation_id': conversation_id,
                'conversation_tenant': conversation_tenant_id,
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise AuthorizationException("Access denied to this conversation")
        
        # Check user access (secondary security check)
        if conversation_user_id != auth_context.user_id:
            lambda_logger.warning("Unauthorized conversation archive attempt - user mismatch", extra={
                'conversation_id': conversation_id,
                'conversation_user': conversation_user_id,
                'requesting_user': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise AuthorizationException("Access denied to this conversation")
        
        # Check if conversation is already archived
        current_status = conversation.get('Status')
        if current_status == 'archived':
            lambda_logger.info("Conversation already archived", extra={
                'conversation_id': conversation_id,
                'current_status': current_status,
                'user_id': auth_context.user_id
            })
            
            # Return current conversation data
            response_data = {
                'conversationId': conversation.get('ConversationId'),
                'status': conversation.get('Status'),
                'updatedAt': conversation.get('UpdatedAt')
            }
            
            return APIResponse.success(
                data=response_data,
                message="Conversation is already archived"
            )
        
        # Archive conversation (can archive from any status)
        success, error_msg = database_service.update_conversation_status(
            auth_context.tenant_id, 
            conversation_id, 
            'archived'
        )
        if not success:
            lambda_logger.error("Failed to archive conversation", extra={
                'conversation_id': conversation_id,
                'error': error_msg,
                'user_id': auth_context.user_id
            })
            return APIResponse.error(f"Failed to archive conversation: {error_msg}", 500)
        
        # Get updated conversation for response
        updated_conversation = database_service.get_conversation(auth_context.tenant_id, conversation_id)
        
        # Get agent information for response
        agent_id = updated_conversation.get('AgentId')
        agent = database_service.get_agent(agent_id) if agent_id else None
        
        # Format response data
        response_data = {
            'conversationId': updated_conversation.get('ConversationId'),
            'agentId': agent_id,
            'agentName': agent.get('Name') if agent else 'Unknown Agent',
            'status': updated_conversation.get('Status'),
            'previousStatus': current_status,
            'updatedAt': updated_conversation.get('UpdatedAt'),
            'archivedAt': updated_conversation.get('UpdatedAt')  # Same as updatedAt for archive operation
        }
        
        # Log conversation status changed event
        client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
        agent_event_service.log_conversation_status_changed(
            conversation_id=conversation_id,
            agent_id=agent_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            old_status=current_status,
            new_status='archived',
            client_ip=client_ip
        )

        lambda_logger.info("Conversation archived successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'agent_id': agent_id,
            'agent_name': agent.get('Name') if agent else 'Unknown',
            'previous_status': current_status,
            'new_status': 'archived',
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })
        
        response = APIResponse.success(
            data=response_data,
            message="Conversation archived successfully"
        )
        
        log_api_response(response, "archive_conversation")
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Authorization error in archive_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 403)
        log_api_response(response, "archive_conversation")
        return response
        
    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in archive_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None
        })
        response = APIResponse.not_found(str(e))
        log_api_response(response, "archive_conversation")
        return response
        
    except Exception as e:
        # The @handle_agent_service_errors decorator will handle this
        raise
