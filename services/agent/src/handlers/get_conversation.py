# services/agent/src/handlers/get_conversation.py
# Handler for getting specific conversation by ID

import uuid
from typing import Dict, Any

try:
    from shared.responses import APIResponse, handle_cors_preflight
    from shared.middleware.resilience_middleware import rate_limit, user_resilience
    from shared.metrics import measure_performance
    from shared.exceptions import ValidationException, ResourceNotFoundException
    from shared.logger import lambda_logger, log_api_request, log_api_response
    from shared.auth import require_auth

    shared_available = True

except ImportError:
    # Fallback for local development
    shared_available = False

    import json

    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"

    lambda_logger = MockLogger()

    def require_auth(func): return func
    def rate_limit(**kwargs): return lambda func: func
    def user_resilience(name): return lambda func: func
    def measure_performance(name): return lambda func: func
    def handle_cors_preflight(): return {"statusCode": 200}

    class APIResponse:
        @staticmethod
        def success(data=None, message="Success", status_code=200):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": True, "message": message, "data": data})
            }

        @staticmethod
        def error(message="Error", status_code=500, details=None):
            return {
                "statusCode": status_code,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({"success": False, "message": message, "details": details})
            }

        @staticmethod
        def validation_error(message="Validation error", validation_errors=None):
            return {
                "statusCode": 400,
                "headers": {"Content-Type": "application/json"},
                "body": json.dumps({
                    "success": False,
                    "message": message,
                    "validation_errors": validation_errors
                })
            }

    def log_api_request(*args, **kwargs): pass
    def log_api_response(*args, **kwargs): pass

    class ValidationException(Exception): pass
    class ResourceNotFoundException(Exception): pass

from ..services.database_service import database_service
from ..services.metrics_service import agent_metrics_service


@require_auth
@rate_limit(requests_per_minute=180)  # Higher limit for read operations
@user_resilience("get_conversation")
@measure_performance("agent_get_conversation")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get specific conversation by ID.

    GET /conversations/{conversationId}

    Returns conversation details if it belongs to the authenticated user.
    Includes agent information and conversation metadata.
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')

    try:
        # Extract auth context (from shared auth middleware)
        auth_context = event.get('auth_context')
        if not auth_context:
            return APIResponse.error("Authentication required", 401)

        # Extract and validate path parameters
        path_params = event.get('pathParameters') or {}
        conversation_id = path_params.get('conversationId')

        if not conversation_id:
            return APIResponse.validation_error(
                "Missing required path parameter: conversationId",
                validation_errors=[{"field": "conversationId", "message": "Conversation ID is required"}]
            )

        # Validate conversation ID format (UUID)
        try:
            uuid.UUID(conversation_id)
        except ValueError:
            return APIResponse.validation_error(
                "Invalid conversation ID format",
                validation_errors=[{"field": "conversationId", "message": "Conversation ID must be a valid UUID"}]
            )
        
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            event.get('path', f'/conversations/{conversation_id}'),
            request_id=request_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        start_time = lambda_logger.get_current_timestamp()

        lambda_logger.info("Processing get conversation request", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        # Get conversation from database
        conversation = database_service.get_conversation(auth_context.tenant_id, conversation_id)
        if not conversation:
            lambda_logger.warning("Conversation not found", extra={
                'request_id': request_id,
                'conversation_id': conversation_id,
                'user_id': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")

        # Verify user has access to this conversation
        conversation_user_id = conversation.get('UserId')
        conversation_tenant_id = conversation.get('TenantId')

        # Check tenant access (primary security check)
        if conversation_tenant_id != auth_context.tenant_id:
            lambda_logger.warning("Unauthorized conversation access attempt - tenant mismatch", extra={
                'request_id': request_id,
                'conversation_id': conversation_id,
                'conversation_tenant': conversation_tenant_id,
                'user_tenant': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")

        # Check user access (secondary security check)
        if conversation_user_id != auth_context.user_id:
            lambda_logger.warning("Unauthorized conversation access attempt - user mismatch", extra={
                'request_id': request_id,
                'conversation_id': conversation_id,
                'conversation_user': conversation_user_id,
                'requesting_user': auth_context.user_id,
                'tenant_id': auth_context.tenant_id
            })
            raise ResourceNotFoundException(f"Conversation with ID {conversation_id} not found")
        
        # Get agent information (with caching)
        agent_id = conversation.get('AgentId')
        agent = database_service.get_agent(agent_id) if agent_id else None

        # Record metrics
        agent_metrics_service.record_api_latency(
            endpoint="get_conversation",
            latency_ms=(lambda_logger.get_current_timestamp() - start_time) * 1000,
            status_code=200
        )

        # Format response data
        response_data = {
            'conversationId': conversation.get('ConversationId'),
            'agentId': agent_id,
            'agentName': agent.get('Name') if agent else 'Unknown Agent',
            'agentDescription': agent.get('Description') if agent else None,
            'agentStatus': agent.get('Status') if agent else 'unknown',
            'status': conversation.get('Status'),
            'webhookUrl': conversation.get('WebhookUrl'),
            'metadata': conversation.get('Metadata', {}),
            'createdAt': conversation.get('CreatedAt'),
            'updatedAt': conversation.get('UpdatedAt'),
            'lastMessageAt': conversation.get('LastMessageAt'),
            'userId': conversation.get('UserId')
        }

        lambda_logger.info("Conversation retrieved successfully", extra={
            'request_id': request_id,
            'conversation_id': conversation_id,
            'agent_id': agent_id,
            'agent_name': agent.get('Name') if agent else 'Unknown',
            'conversation_status': conversation.get('Status'),
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })

        response = APIResponse.success(
            data=response_data,
            message="Conversation retrieved successfully"
        )

        log_api_response(response, "get_conversation")
        return response

    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in get_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 404)
        log_api_response(response, "get_conversation")
        return response

    except ValidationException as e:
        lambda_logger.warning("Validation error in get_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None
        })
        response = APIResponse.validation_error(str(e))
        log_api_response(response, "get_conversation")
        return response

    except Exception as e:
        lambda_logger.error("Unexpected error in get_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__,
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None,
            'tenant_id': getattr(auth_context, 'tenant_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error("Internal server error", 500)
        log_api_response(response, "get_conversation")
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Authorization error in get_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None,
            'user_id': getattr(auth_context, 'user_id', None) if 'auth_context' in locals() else None
        })
        response = APIResponse.error(str(e), 403)
        log_api_response(response, "get_conversation")
        return response
        
    except ResourceNotFoundException as e:
        lambda_logger.info("Resource not found in get_conversation", extra={
            'request_id': request_id,
            'error': str(e),
            'conversation_id': conversation_id if 'conversation_id' in locals() else None
        })
        response = APIResponse.not_found(str(e))
        log_api_response(response, "get_conversation")
        return response
        
    except Exception as e:
        # The @handle_agent_service_errors decorator will handle this
        raise
