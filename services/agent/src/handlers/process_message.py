# services/agent/src/handlers/process_message.py
# Handler for processing messages from the orchestrator

"""
Process Message Handler for Agent Service

This handler receives messages from the Message Orchestrator and processes them
for agent communication. It handles:
1. Messages from users to agents (<PERSON>edo, Forecaster)
2. Routing to appropriate agent webhooks
3. Processing agent responses
4. Sending responses back through the orchestrator

POST /agent/messages/process
{
    "message": {
        "message_id": "msg-123",
        "conversation_id": "conv-456",
        "tenant_id": "tenant-789",
        "sender_id": "user-123",
        "sender_type": "user",
        "content": "I need help with logistics forecasting"
    },
    "conversation": {
        "conversation_id": "conv-456",
        "conversation_type": "user_to_agent",
        "tenant_id": "tenant-789",
        "agent_id": "forecaster",
        "agent_type": "forecaster"
    },
    "source": "orchestrator"
}
"""

from ..common.imports import *


@agent_handler("process_message", require_auth=False)  # Auth handled by orchestrator
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process a message from the orchestrator for agent communication.
    
    This handler is called by the Message Orchestrator when a message
    needs to be processed by an agent.
    """
    
    try:
        # Parse request body
        body = parse_request_body(event)
        
        # Validate required fields
        validate_required_fields(body, ['message', 'conversation', 'source'])
        
        # Verify this request is from the orchestrator
        if body.get('source') != 'orchestrator':
            return error_response("This endpoint is only accessible from the orchestrator", 403)
        
        message_data = body['message']
        conversation_data = body['conversation']
        
        # Validate message and conversation data
        validate_required_fields(message_data, [
            'message_id', 'conversation_id', 'tenant_id', 'sender_id', 'content'
        ])
        
        validate_required_fields(conversation_data, [
            'conversation_id', 'conversation_type', 'tenant_id'
        ])
        
        # Create unified model objects
        conversation = Conversation.from_dict(conversation_data)
        message = Message.from_dict(message_data)
        
        # Validate this is an agent conversation
        if conversation.conversation_type != ConversationType.USER_TO_AGENT:
            return error_response("This handler only processes user-to-agent messages", 400)
        
        # Get services
        hybrid_router = get_hybrid_router()
        database_service = get_database_service()
        
        # Log message processing start
        log_agent_operation(
            operation="process_agent_message",
            agent_id=conversation.agent_id or "unknown",
            tenant_id=conversation.tenant_id,
            user_id=message.sender_id,
            status="started",
            details={
                'message_id': message.message_id,
                'conversation_id': conversation.conversation_id,
                'agent_type': conversation.agent_type,
                'content_length': len(message.content)
            }
        )
        
        # Store message in database
        success, stored_message, error_msg = database_service.store_message(
            message=message,
            conversation=conversation
        )
        
        if not success:
            log_agent_operation(
                operation="process_agent_message",
                agent_id=conversation.agent_id or "unknown",
                tenant_id=conversation.tenant_id,
                user_id=message.sender_id,
                status="failed",
                details={'error': f"Failed to store message: {error_msg}"}
            )
            return error_response(f"Failed to store message: {error_msg}", 500)
        
        # Route message to appropriate agent
        routing_success, routing_result, routing_error = hybrid_router.route_message_to_agent(
            message_data=message.to_dict(),
            agent_id=conversation.agent_id,
            conversation_context={
                'conversation_id': conversation.conversation_id,
                'tenant_id': conversation.tenant_id,
                'user_context': {
                    'user_id': message.sender_id,
                    'user_name': message.sender_name
                }
            },
            tenant_id=conversation.tenant_id
        )
        
        if routing_success:
            # Update message status
            message.mark_as_sent()
            
            # Update message in database
            database_service.update_message_status(
                message_id=message.message_id,
                status=message.status,
                tenant_id=conversation.tenant_id
            )
            
            # Log successful processing
            log_agent_operation(
                operation="process_agent_message",
                agent_id=conversation.agent_id or "unknown",
                tenant_id=conversation.tenant_id,
                user_id=message.sender_id,
                status="completed",
                details={
                    'message_id': message.message_id,
                    'conversation_id': conversation.conversation_id,
                    'agent_type': conversation.agent_type,
                    'routing_method': routing_result.get('routing_method'),
                    'agent_response_expected': True
                }
            )
            
            return success_response({
                'message': "Message processed and routed to agent successfully",
                'result': {
                    'message_id': message.message_id,
                    'conversation_id': conversation.conversation_id,
                    'agent_id': conversation.agent_id,
                    'agent_type': conversation.agent_type,
                    'routing_result': routing_result,
                    'status': message.status.value,
                    'timestamp': message.updated_at.isoformat()
                }
            }, 200)
        else:
            # Mark message as failed
            message.mark_as_failed(routing_error or "Agent routing failed")
            
            # Update message in database
            database_service.update_message_status(
                message_id=message.message_id,
                status=message.status,
                tenant_id=conversation.tenant_id,
                error_message=message.error_message
            )
            
            # Log processing failure
            log_agent_operation(
                operation="process_agent_message",
                agent_id=conversation.agent_id or "unknown",
                tenant_id=conversation.tenant_id,
                user_id=message.sender_id,
                status="failed",
                details={
                    'message_id': message.message_id,
                    'conversation_id': conversation.conversation_id,
                    'error': routing_error
                }
            )
            
            return error_response(f"Failed to route message to agent: {routing_error}", 500)
    
    except Exception as e:
        return handle_agent_error(e, "process_message", {
            'message_data': locals().get('message_data'),
            'conversation_data': locals().get('conversation_data')
        })
