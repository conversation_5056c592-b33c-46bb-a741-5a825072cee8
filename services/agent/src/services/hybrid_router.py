# services/agent/src/services/hybrid_router.py
# Hybrid routing service for agent communication (moved from chat service)

import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from shared.logger import lambda_logger
from shared.database import DynamoDBClient
from shared.config import get_settings

from ..config.agent_config import get_agent_config


class IHybridRouter(ABC):
    """Interface for hybrid routing operations."""
    
    @abstractmethod
    def route_message_to_agent(
        self,
        message_data: Dict[str, Any],
        agent_id: str,
        conversation_context: Dict[str, Any],
        tenant_id: str
    ) -> <PERSON><PERSON>[bool, Dict[str, Any], Optional[str]]:
        """Route message to appropriate agent (Feedo/Forecaster)."""
        pass
    
    @abstractmethod
    def determine_agent_routing_strategy(
        self,
        conversation_id: str,
        message_content: str,
        user_context: Dict[str, Any],
        tenant_id: str
    ) -> str:
        """Determine which agent should handle the message."""
        pass
    
    @abstractmethod
    def escalate_to_human_agent(
        self,
        conversation_id: str,
        reason: str,
        tenant_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> <PERSON><PERSON>[bool, Optional[str]]:
        """Escalate conversation to human agent."""
        pass
    
    @abstractmethod
    def get_available_agents(self, tenant_id: str, skill_requirements: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get available agents for routing."""
        pass


class HybridRouter(IHybridRouter):
    """
    Service for routing messages to appropriate agents.
    
    This service determines whether a message should go to:
    - Feedo agent (for logistics/operational queries)
    - Forecaster agent (for predictive analytics)
    - Human agent (for complex issues requiring human intervention)
    """
    
    def __init__(self):
        self.config = get_agent_config()
        self.db_client = DynamoDBClient(self.config.get('dynamodb_table_name', 'agent-scl-main-table'))
        
        # Agent routing configuration
        self.feedo_keywords = [
            'logistics', 'shipping', 'delivery', 'warehouse', 'inventory',
            'tracking', 'order', 'fulfillment', 'supply chain'
        ]
        
        self.forecaster_keywords = [
            'forecast', 'prediction', 'analytics', 'trends', 'demand',
            'planning', 'projection', 'analysis', 'insights', 'metrics'
        ]
        
        self.human_escalation_keywords = [
            'human', 'agent', 'help', 'support', 'escalate', 'manager',
            'complaint', 'urgent', 'emergency', 'critical'
        ]
    
    def route_message_to_agent(
        self,
        message_data: Dict[str, Any],
        agent_id: str,
        conversation_context: Dict[str, Any],
        tenant_id: str
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to specific agent."""
        try:
            # Get agent configuration
            agent = self._get_agent_config(agent_id, tenant_id)
            if not agent:
                return False, {}, f"Agent {agent_id} not found"
            
            # Determine routing strategy
            strategy = self.determine_agent_routing_strategy(
                conversation_context.get('conversation_id', ''),
                message_data.get('content', ''),
                conversation_context.get('user_context', {}),
                tenant_id
            )
            
            # Route based on strategy
            if strategy == 'feedo':
                return self._route_to_feedo(message_data, agent, conversation_context)
            elif strategy == 'forecaster':
                return self._route_to_forecaster(message_data, agent, conversation_context)
            elif strategy == 'human':
                return self._route_to_human(message_data, conversation_context)
            else:
                # Default to Feedo for general logistics queries
                return self._route_to_feedo(message_data, agent, conversation_context)
                
        except Exception as e:
            lambda_logger.error(f"Failed to route message to agent: {str(e)}")
            return False, {}, str(e)
    
    def determine_agent_routing_strategy(
        self,
        conversation_id: str,
        message_content: str,
        user_context: Dict[str, Any],
        tenant_id: str
    ) -> str:
        """Determine which agent should handle the message."""
        try:
            content_lower = message_content.lower()
            
            # Check for human escalation keywords first
            if any(keyword in content_lower for keyword in self.human_escalation_keywords):
                return 'human'
            
            # Check for Forecaster keywords
            if any(keyword in content_lower for keyword in self.forecaster_keywords):
                return 'forecaster'
            
            # Check for Feedo keywords
            if any(keyword in content_lower for keyword in self.feedo_keywords):
                return 'feedo'
            
            # Check conversation history for context
            conversation_context = self._get_conversation_context(conversation_id, tenant_id)
            if conversation_context:
                previous_agent = conversation_context.get('last_agent_used')
                if previous_agent in ['feedo', 'forecaster']:
                    return previous_agent
            
            # Default to Feedo for general logistics platform
            return 'feedo'
            
        except Exception as e:
            lambda_logger.error(f"Failed to determine routing strategy: {str(e)}")
            return 'feedo'  # Safe default
    
    def escalate_to_human_agent(
        self,
        conversation_id: str,
        reason: str,
        tenant_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[str]]:
        """Escalate conversation to human agent."""
        try:
            escalation_data = {
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'reason': reason,
                'timestamp': datetime.utcnow().isoformat(),
                'metadata': metadata or {},
                'status': 'pending_human_assignment'
            }
            
            # Store escalation in database
            success = self._store_escalation(escalation_data)
            if not success:
                return False, "Failed to store escalation"
            
            # Notify human agents (this would integrate with notification system)
            self._notify_human_agents(escalation_data)
            
            lambda_logger.info(f"Escalated conversation {conversation_id} to human agent")
            return True, None
            
        except Exception as e:
            lambda_logger.error(f"Failed to escalate to human agent: {str(e)}")
            return False, str(e)
    
    def get_available_agents(self, tenant_id: str, skill_requirements: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get available agents for routing."""
        try:
            # Get all active agents for tenant
            agents = self._get_tenant_agents(tenant_id)
            
            # Filter by skill requirements if provided
            if skill_requirements:
                filtered_agents = []
                for agent in agents:
                    agent_skills = agent.get('skills', [])
                    if any(skill in agent_skills for skill in skill_requirements):
                        filtered_agents.append(agent)
                agents = filtered_agents
            
            # Filter by availability
            available_agents = [
                agent for agent in agents
                if agent.get('status') == 'active' and agent.get('available', True)
            ]
            
            return available_agents
            
        except Exception as e:
            lambda_logger.error(f"Failed to get available agents: {str(e)}")
            return []
    
    def _route_to_feedo(
        self,
        message_data: Dict[str, Any],
        agent: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to Feedo agent."""
        try:
            # Determine webhook URL based on message type
            message_type = message_data.get('message_type', 'text')

            if message_type == 'document':
                webhook_url = agent.get('document_webhook_url') or agent.get('webhook_url')
            else:
                webhook_url = agent.get('webhook_url')

            if not webhook_url:
                return False, {}, "Feedo webhook URL not configured"

            # Prepare headers with authentication if available
            headers = {'Content-Type': 'application/json'}
            if agent.get('bearer_token'):
                headers['Authorization'] = f"Bearer {agent.get('bearer_token')}"

            # Build payload with agent-specific parameters
            payload = {
                'message': message_data,
                'conversation_context': conversation_context,
                'agent_type': 'feedo',
                'timestamp': datetime.utcnow().isoformat()
            }

            # Add input parameters if configured
            if agent.get('input_parameters'):
                payload.update(agent.get('input_parameters'))

            response = requests.post(
                webhook_url,
                json=payload,
                timeout=self.config.get('webhook_timeout_seconds', 30),
                headers=headers
            )

            if response.status_code == 200:
                return True, {
                    'agent_type': 'feedo',
                    'response': response.json(),
                    'routing_method': 'webhook',
                    'webhook_url': webhook_url
                }, None
            else:
                return False, {}, f"Feedo webhook failed: {response.status_code} - {response.text}"

        except Exception as e:
            lambda_logger.error(f"Failed to route to Feedo: {str(e)}")
            return False, {}, str(e)
    
    def _route_to_forecaster(
        self,
        message_data: Dict[str, Any],
        agent: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to Forecaster agent."""
        try:
            # Determine webhook URL based on message type
            message_type = message_data.get('message_type', 'text')

            if message_type == 'document':
                webhook_url = agent.get('document_webhook_url') or agent.get('webhook_url')
            else:
                webhook_url = agent.get('forecaster_webhook_url') or agent.get('webhook_url')

            if not webhook_url:
                return False, {}, "Forecaster webhook URL not configured"

            # Prepare headers with authentication if available
            headers = {'Content-Type': 'application/json'}
            if agent.get('bearer_token'):
                headers['Authorization'] = f"Bearer {agent.get('bearer_token')}"

            # Build payload with agent-specific parameters
            payload = {
                'message': message_data,
                'conversation_context': conversation_context,
                'agent_type': 'forecaster',
                'timestamp': datetime.utcnow().isoformat()
            }

            # Add input parameters if configured
            if agent.get('input_parameters'):
                payload.update(agent.get('input_parameters'))

            response = requests.post(
                webhook_url,
                json=payload,
                timeout=self.config.get('webhook_timeout_seconds', 30),
                headers=headers
            )

            if response.status_code == 200:
                return True, {
                    'agent_type': 'forecaster',
                    'response': response.json(),
                    'routing_method': 'webhook',
                    'webhook_url': webhook_url
                }, None
            else:
                return False, {}, f"Forecaster webhook failed: {response.status_code} - {response.text}"

        except Exception as e:
            lambda_logger.error(f"Failed to route to Forecaster: {str(e)}")
            return False, {}, str(e)
    
    def _route_to_human(
        self,
        message_data: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """Route message to human agent."""
        try:
            conversation_id = conversation_context.get('conversation_id')
            tenant_id = conversation_context.get('tenant_id')
            
            # Escalate to human
            success, error = self.escalate_to_human_agent(
                conversation_id=conversation_id,
                reason="User requested human agent",
                tenant_id=tenant_id,
                metadata={'original_message': message_data}
            )
            
            if success:
                return True, {
                    'agent_type': 'human',
                    'status': 'escalated',
                    'routing_method': 'escalation'
                }, None
            else:
                return False, {}, error
                
        except Exception as e:
            lambda_logger.error(f"Failed to route to human: {str(e)}")
            return False, {}, str(e)
    
    def _get_agent_config(self, agent_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get agent configuration from database with status validation."""
        try:
            result = self.db_client.get_item(
                pk=f"TENANT#{tenant_id}",
                sk=f"AGENT#{agent_id}"
            )

            if not result:
                lambda_logger.warning(f"Agent {agent_id} not found for tenant {tenant_id}")
                return None

            # Validate agent is active and has valid webhook configuration
            agent_status = result.get('status', 'inactive')
            webhook_url = result.get('webhook_url')

            if agent_status != 'active':
                lambda_logger.warning(f"Agent {agent_id} is not active (status: {agent_status})")
                return None

            if not webhook_url or webhook_url.strip() == '':
                lambda_logger.warning(f"Agent {agent_id} has no valid webhook URL configured")
                return None

            return result
        except Exception as e:
            lambda_logger.error(f"Failed to get agent config: {str(e)}")
            return None
    
    def _get_conversation_context(self, conversation_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation context from database."""
        try:
            result = self.db_client.get_item(
                pk=f"TENANT#{tenant_id}",
                sk=f"CONVERSATION#{conversation_id}"
            )
            return result
        except Exception as e:
            lambda_logger.error(f"Failed to get conversation context: {str(e)}")
            return None
    
    def _get_tenant_agents(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get all agents for a tenant."""
        try:
            # This would query all agents for the tenant
            # For now, return empty list
            return []
        except Exception as e:
            lambda_logger.error(f"Failed to get tenant agents: {str(e)}")
            return []
    
    def _store_escalation(self, escalation_data: Dict[str, Any]) -> bool:
        """Store escalation data in database."""
        try:
            # This would store the escalation in the database
            lambda_logger.info(f"Storing escalation: {escalation_data}")
            return True
        except Exception as e:
            lambda_logger.error(f"Failed to store escalation: {str(e)}")
            return False
    
    def _notify_human_agents(self, escalation_data: Dict[str, Any]) -> None:
        """Notify human agents of escalation."""
        try:
            # This would send notifications to human agents
            lambda_logger.info(f"Notifying human agents of escalation: {escalation_data}")
        except Exception as e:
            lambda_logger.error(f"Failed to notify human agents: {str(e)}")


# Create singleton instance
hybrid_router = HybridRouter()
