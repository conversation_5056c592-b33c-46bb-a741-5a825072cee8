# services/agent/src/services/cache_service.py
# Cache service for Agent Service performance optimization

import json
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

try:
    from shared.cache import CacheManager
    from shared.logger import lambda_logger
    from shared.metrics import metrics_manager
    
    shared_available = True
    
except ImportError:
    # Fallback for local development
    shared_available = False
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    lambda_logger = MockLogger()
    
    class MockCacheManager:
        def __init__(self, *args, **kwargs): pass
        def get(self, key): return None
        def set(self, key, value, ttl=None): pass
        def delete(self, key): pass
        def clear_pattern(self, pattern): pass
    
    CacheManager = MockCacheManager


class ICacheService(ABC):
    """Interface for cache service operations."""

    @abstractmethod
    def get_agent_cache(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get cached agent data."""
        pass

    @abstractmethod
    def set_agent_cache(self, agent_id: str, agent_data: Dict[str, Any], ttl_minutes: int = 15) -> bool:
        """Set agent data in cache."""
        pass

    @abstractmethod
    def get_conversation_cache(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get cached conversation data."""
        pass

    @abstractmethod
    def set_conversation_cache(self, conversation_id: str, conversation_data: Dict[str, Any], ttl_minutes: int = 10) -> bool:
        """Set conversation data in cache."""
        pass

    @abstractmethod
    def invalidate_agent_cache(self, agent_id: str) -> bool:
        """Invalidate agent cache."""
        pass

    @abstractmethod
    def warm_up_cache(self) -> None:
        """Warm up frequently accessed cache entries."""
        pass


class AgentCacheService(ICacheService):
    """Cache service for Agent Service operations"""
    
    def __init__(self):
        self.service_name = "agent-service"
        if shared_available:
            self.cache_manager = CacheManager(
                service_name=self.service_name,
                default_ttl_minutes=15  # 15 minutes default TTL
            )
        else:
            self.cache_manager = MockCacheManager()
    
    # Agent Caching
    def get_active_agents_cache(self) -> Optional[List[Dict[str, Any]]]:
        """Get active agents from cache"""
        try:
            cache_key = "active_agents"
            cached_data = self.cache_manager.get(cache_key)
            
            if cached_data:
                lambda_logger.debug("Active agents retrieved from cache", extra={
                    'cache_key': cache_key,
                    'count': len(cached_data) if isinstance(cached_data, list) else 0
                })
                return cached_data
            
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get active agents from cache", extra={
                'error': str(e)
            })
            return None
    
    def set_active_agents_cache(self, agents: List[Dict[str, Any]], ttl_minutes: int = 5):
        """Set active agents in cache"""
        try:
            cache_key = "active_agents"
            self.cache_manager.set(cache_key, agents, ttl_minutes=ttl_minutes)
            
            lambda_logger.debug("Active agents cached", extra={
                'cache_key': cache_key,
                'count': len(agents),
                'ttl_minutes': ttl_minutes
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cache active agents", extra={
                'error': str(e),
                'count': len(agents)
            })
    
    def get_agent_cache(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get specific agent from cache"""
        try:
            cache_key = f"agent:{agent_id}"
            cached_data = self.cache_manager.get(cache_key)
            
            if cached_data:
                lambda_logger.debug("Agent retrieved from cache", extra={
                    'cache_key': cache_key,
                    'agent_id': agent_id
                })
                return cached_data
            
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get agent from cache", extra={
                'agent_id': agent_id,
                'error': str(e)
            })
            return None
    
    def set_agent_cache(self, agent_id: str, agent_data: Dict[str, Any], ttl_minutes: int = 10):
        """Set specific agent in cache"""
        try:
            cache_key = f"agent:{agent_id}"
            self.cache_manager.set(cache_key, agent_data, ttl_minutes=ttl_minutes)
            
            lambda_logger.debug("Agent cached", extra={
                'cache_key': cache_key,
                'agent_id': agent_id,
                'ttl_minutes': ttl_minutes
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cache agent", extra={
                'agent_id': agent_id,
                'error': str(e)
            })
    
    def invalidate_agent_cache(self, agent_id: str):
        """Invalidate specific agent cache"""
        try:
            cache_key = f"agent:{agent_id}"
            self.cache_manager.delete(cache_key)
            
            # Also invalidate active agents cache since it might contain this agent
            self.cache_manager.delete("active_agents")
            
            lambda_logger.debug("Agent cache invalidated", extra={
                'agent_id': agent_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to invalidate agent cache", extra={
                'agent_id': agent_id,
                'error': str(e)
            })
    
    # Conversation Caching
    def get_user_conversations_cache(self, tenant_id: str, user_id: str, 
                                   status_filter: str = None) -> Optional[List[Dict[str, Any]]]:
        """Get user conversations from cache"""
        try:
            cache_key = f"user_conversations:{tenant_id}:{user_id}"
            if status_filter:
                cache_key += f":{status_filter}"
            
            cached_data = self.cache_manager.get(cache_key)
            
            if cached_data:
                lambda_logger.debug("User conversations retrieved from cache", extra={
                    'cache_key': cache_key,
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'count': len(cached_data) if isinstance(cached_data, list) else 0
                })
                return cached_data
            
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get user conversations from cache", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return None
    
    def set_user_conversations_cache(self, tenant_id: str, user_id: str, 
                                   conversations: List[Dict[str, Any]], 
                                   status_filter: str = None, ttl_minutes: int = 3):
        """Set user conversations in cache"""
        try:
            cache_key = f"user_conversations:{tenant_id}:{user_id}"
            if status_filter:
                cache_key += f":{status_filter}"
            
            self.cache_manager.set(cache_key, conversations, ttl_minutes=ttl_minutes)
            
            lambda_logger.debug("User conversations cached", extra={
                'cache_key': cache_key,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'count': len(conversations),
                'ttl_minutes': ttl_minutes
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cache user conversations", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
    
    def invalidate_user_conversations_cache(self, tenant_id: str, user_id: str):
        """Invalidate user conversations cache"""
        try:
            # Clear all variations of user conversations cache
            patterns = [
                f"user_conversations:{tenant_id}:{user_id}",
                f"user_conversations:{tenant_id}:{user_id}:*"
            ]
            
            for pattern in patterns:
                self.cache_manager.clear_pattern(pattern)
            
            lambda_logger.debug("User conversations cache invalidated", extra={
                'tenant_id': tenant_id,
                'user_id': user_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to invalidate user conversations cache", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
    
    # Configuration Caching
    def get_agent_config_cache(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get agent configuration from cache"""
        try:
            cache_key = f"agent_config:{agent_id}"
            cached_data = self.cache_manager.get(cache_key)
            
            if cached_data:
                lambda_logger.debug("Agent config retrieved from cache", extra={
                    'cache_key': cache_key,
                    'agent_id': agent_id
                })
                return cached_data
            
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get agent config from cache", extra={
                'agent_id': agent_id,
                'error': str(e)
            })
            return None
    
    def set_agent_config_cache(self, agent_id: str, config_data: Dict[str, Any], ttl_minutes: int = 30):
        """Set agent configuration in cache"""
        try:
            cache_key = f"agent_config:{agent_id}"
            
            # Only cache essential config data
            cached_config = {
                'webhook_url': config_data.get('WebhookUrl'),
                'document_webhook_url': config_data.get('DocumentWebhookUrl'),
                'status': config_data.get('Status'),
                'input_parameters': config_data.get('InputParameters', {}),
                'bearer_token': config_data.get('BearerToken'),  # Encrypted
                'secret': config_data.get('Secret')  # Encrypted
            }
            
            self.cache_manager.set(cache_key, cached_config, ttl_minutes=ttl_minutes)
            
            lambda_logger.debug("Agent config cached", extra={
                'cache_key': cache_key,
                'agent_id': agent_id,
                'ttl_minutes': ttl_minutes
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cache agent config", extra={
                'agent_id': agent_id,
                'error': str(e)
            })
    
    # Cache Statistics and Management
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            if shared_available:
                stats = self.cache_manager.get_stats()
                return {
                    'service': self.service_name,
                    'cache_stats': stats,
                    'timestamp': datetime.utcnow().isoformat()
                }
            else:
                return {
                    'service': self.service_name,
                    'cache_stats': {'status': 'mock_mode'},
                    'timestamp': datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            lambda_logger.error("Failed to get cache stats", extra={
                'error': str(e)
            })
            return {
                'service': self.service_name,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def clear_all_cache(self):
        """Clear all cache for agent service"""
        try:
            patterns = [
                "active_agents",
                "agent:*",
                "user_conversations:*",
                "agent_config:*"
            ]
            
            for pattern in patterns:
                self.cache_manager.clear_pattern(pattern)
            
            lambda_logger.info("All agent service cache cleared")
            
        except Exception as e:
            lambda_logger.error("Failed to clear all cache", extra={
                'error': str(e)
            })
    
    def warm_up_cache(self):
        """Warm up frequently accessed cache entries"""
        try:
            # This would be called during service startup or periodically
            # to pre-populate cache with frequently accessed data
            
            lambda_logger.info("Cache warm-up initiated")
            
            # TODO: Implement cache warm-up logic
            # - Pre-load active agents
            # - Pre-load frequently accessed agent configs
            # - Pre-load recent conversations for active users
            
            lambda_logger.info("Cache warm-up completed")
            
        except Exception as e:
            lambda_logger.error("Failed to warm up cache", extra={
                'error': str(e)
            })


# Global cache service instance
agent_cache_service = AgentCacheService()
