# services/agent/src/services/metrics_service.py
# CloudWatch metrics service for Agent Service monitoring

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from functools import wraps

try:
    from shared.metrics import metrics_manager, MetricType
    from shared.logger import lambda_logger
    import boto3
    
    shared_available = True
    cloudwatch = boto3.client('cloudwatch')
    
except ImportError:
    # Fallback for local development
    shared_available = False
    cloudwatch = None
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    lambda_logger = MockLogger()
    
    class MetricType:
        COUNT = "Count"
        GAUGE = "Gauge"
        TIMER = "Timer"

from ..config.agent_config import get_monitoring_config


class IMetricsService(ABC):
    """Interface for metrics service operations."""

    @abstractmethod
    def record_agent_operation(self, operation: str, success: bool, duration_ms: float, tenant_id: str) -> None:
        """Record agent operation metrics."""
        pass

    @abstractmethod
    def record_conversation_metrics(self, operation: str, conversation_id: str, tenant_id: str) -> None:
        """Record conversation metrics."""
        pass

    @abstractmethod
    def record_message_metrics(self, message_type: str, size_bytes: int, tenant_id: str) -> None:
        """Record message metrics."""
        pass

    @abstractmethod
    def get_service_health_metrics(self) -> Dict[str, Any]:
        """Get service health metrics."""
        pass


class AgentMetricsService(IMetricsService):
    """CloudWatch metrics service for Agent Service"""
    
    def __init__(self):
        self.service_name = "agent-service"
        self.namespace = "AgentService"
        self.environment = config.get('environment', 'dev')
    
    def _put_metric(self, metric_name: str, value: float, unit: str = 'Count', 
                   dimensions: Dict[str, str] = None):
        """Put custom metric to CloudWatch"""
        try:
            if not cloudwatch:
                lambda_logger.debug(f"Mock metric: {metric_name} = {value}")
                return
            
            # Default dimensions
            default_dimensions = [
                {'Name': 'Service', 'Value': self.service_name},
                {'Name': 'Environment', 'Value': self.environment}
            ]
            
            # Add custom dimensions
            if dimensions:
                for key, value in dimensions.items():
                    default_dimensions.append({'Name': key, 'Value': str(value)})
            
            cloudwatch.put_metric_data(
                Namespace=self.namespace,
                MetricData=[
                    {
                        'MetricName': metric_name,
                        'Value': value,
                        'Unit': unit,
                        'Dimensions': default_dimensions,
                        'Timestamp': datetime.utcnow()
                    }
                ]
            )
            
            lambda_logger.debug("Metric sent to CloudWatch", extra={
                'metric_name': metric_name,
                'value': value,
                'unit': unit,
                'dimensions': dimensions
            })
            
        except Exception as e:
            lambda_logger.error("Failed to put metric to CloudWatch", extra={
                'metric_name': metric_name,
                'value': value,
                'error': str(e)
            })
    
    # Agent Metrics
    def record_agent_created(self, tenant_id: str):
        """Record agent creation metric"""
        self._put_metric(
            'AgentCreated',
            1,
            'Count',
            {'TenantId': tenant_id}
        )
        
        if shared_available:
            metrics_manager.business.record_business_event(
                'agent_created',
                tenant_id=tenant_id
            )
    
    def record_agent_deleted(self, tenant_id: str):
        """Record agent deletion metric"""
        self._put_metric(
            'AgentDeleted',
            1,
            'Count',
            {'TenantId': tenant_id}
        )
        
        if shared_available:
            metrics_manager.business.record_business_event(
                'agent_deleted',
                tenant_id=tenant_id
            )
    
    def record_agent_status_change(self, tenant_id: str, old_status: str, new_status: str):
        """Record agent status change metric"""
        self._put_metric(
            'AgentStatusChange',
            1,
            'Count',
            {
                'TenantId': tenant_id,
                'OldStatus': old_status,
                'NewStatus': new_status
            }
        )
    
    def record_active_agents_count(self, count: int):
        """Record current active agents count"""
        self._put_metric(
            'ActiveAgentsCount',
            count,
            'Count'
        )
    
    # Conversation Metrics
    def record_conversation_created(self, tenant_id: str, agent_id: str):
        """Record conversation creation metric"""
        self._put_metric(
            'ConversationCreated',
            1,
            'Count',
            {'TenantId': tenant_id, 'AgentId': agent_id}
        )
        
        if shared_available:
            metrics_manager.business.record_business_event(
                'conversation_created',
                tenant_id=tenant_id
            )
    
    def record_conversation_status_change(self, tenant_id: str, old_status: str, new_status: str):
        """Record conversation status change metric"""
        self._put_metric(
            'ConversationStatusChange',
            1,
            'Count',
            {
                'TenantId': tenant_id,
                'OldStatus': old_status,
                'NewStatus': new_status
            }
        )
    
    def record_active_conversations_count(self, tenant_id: str, count: int):
        """Record current active conversations count for tenant"""
        self._put_metric(
            'ActiveConversationsCount',
            count,
            'Count',
            {'TenantId': tenant_id}
        )
    
    # Message Metrics
    def record_message_sent(self, tenant_id: str, agent_id: str, message_type: str):
        """Record message sent metric"""
        self._put_metric(
            'MessageSent',
            1,
            'Count',
            {
                'TenantId': tenant_id,
                'AgentId': agent_id,
                'MessageType': message_type,
                'Direction': 'outbound'
            }
        )
        
        if shared_available:
            metrics_manager.business.record_business_event(
                'message_sent',
                tenant_id=tenant_id
            )
    
    def record_message_received(self, tenant_id: str, agent_id: str, message_type: str):
        """Record message received metric"""
        self._put_metric(
            'MessageReceived',
            1,
            'Count',
            {
                'TenantId': tenant_id,
                'AgentId': agent_id,
                'MessageType': message_type,
                'Direction': 'inbound'
            }
        )
        
        if shared_available:
            metrics_manager.business.record_business_event(
                'message_received',
                tenant_id=tenant_id
            )
    
    def record_messages_per_conversation(self, tenant_id: str, count: int):
        """Record messages per conversation metric"""
        self._put_metric(
            'MessagesPerConversation',
            count,
            'Count',
            {'TenantId': tenant_id}
        )
    
    # Performance Metrics
    def record_api_latency(self, endpoint: str, latency_ms: float, status_code: int):
        """Record API endpoint latency"""
        self._put_metric(
            'APILatency',
            latency_ms,
            'Milliseconds',
            {
                'Endpoint': endpoint,
                'StatusCode': str(status_code)
            }
        )
        
        if shared_available:
            metrics_manager.performance.record_api_latency(
                endpoint,
                latency_ms,
                status_code
            )
    
    def record_database_query_time(self, operation: str, duration_ms: float):
        """Record database query performance"""
        self._put_metric(
            'DatabaseQueryTime',
            duration_ms,
            'Milliseconds',
            {'Operation': operation}
        )
    
    def record_cache_hit_rate(self, cache_type: str, hit_rate: float):
        """Record cache hit rate"""
        self._put_metric(
            'CacheHitRate',
            hit_rate,
            'Percent',
            {'CacheType': cache_type}
        )
    
    # Error Metrics
    def record_error(self, error_type: str, endpoint: str = None, tenant_id: str = None):
        """Record error occurrence"""
        dimensions = {'ErrorType': error_type}
        if endpoint:
            dimensions['Endpoint'] = endpoint
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        self._put_metric(
            'ErrorCount',
            1,
            'Count',
            dimensions
        )
        
        if shared_available:
            metrics_manager.error.record_error(
                error_type,
                endpoint or 'unknown',
                tenant_id
            )
    
    def record_webhook_error(self, agent_id: str, error_type: str):
        """Record webhook error"""
        self._put_metric(
            'WebhookError',
            1,
            'Count',
            {
                'AgentId': agent_id,
                'ErrorType': error_type
            }
        )
    
    # Security Metrics
    def record_unauthorized_access(self, resource_type: str, tenant_id: str = None):
        """Record unauthorized access attempt"""
        dimensions = {'ResourceType': resource_type}
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        self._put_metric(
            'UnauthorizedAccess',
            1,
            'Count',
            dimensions
        )
        
        if shared_available:
            metrics_manager.security.record_security_event(
                'unauthorized_access',
                'warning',
                tenant_id
            )
    
    def record_rate_limit_exceeded(self, endpoint: str, tenant_id: str = None):
        """Record rate limit exceeded"""
        dimensions = {'Endpoint': endpoint}
        if tenant_id:
            dimensions['TenantId'] = tenant_id
        
        self._put_metric(
            'RateLimitExceeded',
            1,
            'Count',
            dimensions
        )
    
    # Business Metrics
    def record_tenant_usage(self, tenant_id: str, conversations_count: int, messages_count: int):
        """Record tenant usage metrics"""
        self._put_metric(
            'TenantConversations',
            conversations_count,
            'Count',
            {'TenantId': tenant_id}
        )
        
        self._put_metric(
            'TenantMessages',
            messages_count,
            'Count',
            {'TenantId': tenant_id}
        )
    
    # Health Metrics
    def record_health_check(self, component: str, healthy: bool, response_time_ms: float = None):
        """Record health check results"""
        self._put_metric(
            'HealthCheck',
            1 if healthy else 0,
            'Count',
            {'Component': component, 'Status': 'healthy' if healthy else 'unhealthy'}
        )
        
        if response_time_ms is not None:
            self._put_metric(
                'HealthCheckResponseTime',
                response_time_ms,
                'Milliseconds',
                {'Component': component}
            )


def measure_execution_time(metric_name: str, dimensions: Dict[str, str] = None):
    """Decorator to measure execution time of functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                success = False
                raise
            finally:
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                # Record metric
                agent_metrics_service._put_metric(
                    metric_name,
                    duration_ms,
                    'Milliseconds',
                    dimensions
                )
                
                # Also record success/failure
                agent_metrics_service._put_metric(
                    f"{metric_name}Success",
                    1 if success else 0,
                    'Count',
                    dimensions
                )
        
        return wrapper
    return decorator


# Global metrics service instance
agent_metrics_service = AgentMetricsService()
