# services/agent/src/services/database_service.py
# Database service for Agent Service using shared DynamoDB client

import os
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Import shared database client
try:
    from shared.database import DynamoDBClient
    from shared.logger import lambda_logger
except ImportError:
    # Fallback for local development
    import boto3
    from boto3.dynamodb.conditions import Key, Attr
    
    class DynamoDBClient:
        def __init__(self):
            self.dynamodb = boto3.resource('dynamodb')
            self.table = self.dynamodb.Table(os.environ.get('DYNAMODB_TABLE'))
        
        def get_item(self, pk: str, sk: str, tenant_id: str = None, consistent_read: bool = False):
            response = self.table.get_item(Key={'PK': pk, 'SK': sk})
            return response.get('Item')
        
        def put_item(self, item: Dict[str, Any], tenant_id: str = None):
            self.table.put_item(Item=item)
        
        def query(self, **kwargs):
            return self.table.query(**kwargs)
        
        def delete_item(self, pk: str, sk: str, tenant_id: str = None):
            self.table.delete_item(Key={'PK': pk, 'SK': sk})
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    lambda_logger = MockLogger()

from ..models.schema import AgentSchema, ConversationSchema, MessageSchema


class IDatabaseService(ABC):
    """Interface for database service operations."""

    @abstractmethod
    def create_agent(self, agent_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Create a new agent in the database."""
        pass

    @abstractmethod
    def get_agent_by_id(self, agent_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get agent by ID."""
        pass

    @abstractmethod
    def list_active_agents(self, tenant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """List active agents."""
        pass

    @abstractmethod
    def update_agent(self, agent_id: str, tenant_id: str, updates: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Update agent data."""
        pass

    @abstractmethod
    def delete_agent(self, agent_id: str, tenant_id: str) -> Tuple[bool, Optional[str]]:
        """Delete an agent."""
        pass

    @abstractmethod
    def create_conversation(self, conversation_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Create a new conversation."""
        pass

    @abstractmethod
    def get_conversation_by_id(self, conversation_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation by ID."""
        pass

    @abstractmethod
    def list_user_conversations(self, user_id: str, tenant_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """List conversations for a user."""
        pass

    @abstractmethod
    def create_message(self, message_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Create a new message."""
        pass

    @abstractmethod
    def list_conversation_messages(self, conversation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """List messages in a conversation."""
        pass


class DatabaseService(IDatabaseService):
    """Database service for Agent Service operations"""
    
    def __init__(self):
        self.db_client = DynamoDBClient()
    
    # Agent operations
    def create_agent(self, agent_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Create a new agent"""
        try:
            item = AgentSchema.create_agent_item(**agent_data)
            self.db_client.put_item(item)
            
            lambda_logger.info("Agent created successfully", extra={
                'agent_id': agent_data['agent_id'],
                'name': agent_data['name']
            })
            
            return True, None
        except Exception as e:
            error_msg = f"Failed to create agent: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'agent_id': agent_data.get('agent_id'),
                'error': str(e)
            })
            return False, error_msg
    
    def get_agent(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get agent by ID with caching"""
        try:
            # Try cache first
            from .cache_service import agent_cache_service
            cached_agent = agent_cache_service.get_agent_cache(agent_id)
            if cached_agent:
                return cached_agent

            # Query database
            key = AgentSchema.get_agent_key(agent_id)
            item = self.db_client.get_item(key['PK'], key['SK'])

            if item:
                # Cache the result
                agent_cache_service.set_agent_cache(agent_id, item, ttl_minutes=10)
                lambda_logger.debug("Agent retrieved and cached", extra={'agent_id': agent_id})

            return item
        except Exception as e:
            lambda_logger.error("Failed to get agent", extra={
                'agent_id': agent_id,
                'error': str(e)
            })
            return None
    
    def list_active_agents(self) -> List[Dict[str, Any]]:
        """List all active agents with caching optimization"""
        try:
            # Use optimized query service for better performance
            from .optimized_queries import optimized_query_service
            return optimized_query_service.get_active_agents_cached()
        except Exception as e:
            lambda_logger.error("Failed to list active agents with optimization", extra={
                'error': str(e)
            })
            # Fallback to original query
            try:
                query_params = AgentSchema.get_active_agents_query()
                response = self.db_client.query(**query_params)

                agents = response.get('Items', [])
                lambda_logger.info("Active agents retrieved (fallback)", extra={
                    'count': len(agents)
                })

                return agents
            except Exception as fallback_error:
                lambda_logger.error("Fallback query also failed", extra={
                    'error': str(fallback_error)
                })
                return []
    
    def update_agent(self, agent_id: str, updates: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Update agent"""
        try:
            # Get existing agent first
            existing_agent = self.get_agent(agent_id)
            if not existing_agent:
                return False, "Agent not found"
            
            # Update fields
            for key, value in updates.items():
                if key in ['name', 'description', 'webhookUrl', 'documentWebhookUrl', 
                          'bearerToken', 'secret', 'inputParameters', 'metadata']:
                    existing_agent[key.title().replace('Url', 'URL')] = value
            
            existing_agent['UpdatedAt'] = datetime.utcnow().isoformat()
            
            # Save updated agent
            self.db_client.put_item(existing_agent)
            
            lambda_logger.info("Agent updated successfully", extra={
                'agent_id': agent_id,
                'updated_fields': list(updates.keys())
            })
            
            return True, None
        except Exception as e:
            error_msg = f"Failed to update agent: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'agent_id': agent_id,
                'error': str(e)
            })
            return False, error_msg
    
    def update_agent_status(self, agent_id: str, status: str) -> Tuple[bool, Optional[str]]:
        """Update agent status"""
        try:
            # Get existing agent first
            existing_agent = self.get_agent(agent_id)
            if not existing_agent:
                return False, "Agent not found"
            
            # Update status and GSI1PK for status-based queries
            existing_agent['Status'] = status
            existing_agent['GSI1PK'] = f'AGENT_STATUS#{status}'
            existing_agent['UpdatedAt'] = datetime.utcnow().isoformat()
            
            # Save updated agent
            self.db_client.put_item(existing_agent)
            
            lambda_logger.info("Agent status updated", extra={
                'agent_id': agent_id,
                'new_status': status
            })
            
            return True, None
        except Exception as e:
            error_msg = f"Failed to update agent status: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'agent_id': agent_id,
                'status': status,
                'error': str(e)
            })
            return False, error_msg
    
    def delete_agent(self, agent_id: str) -> Tuple[bool, Optional[str]]:
        """Delete agent"""
        try:
            key = AgentSchema.get_agent_key(agent_id)
            self.db_client.delete_item(key['PK'], key['SK'])
            
            lambda_logger.info("Agent deleted", extra={'agent_id': agent_id})
            return True, None
        except Exception as e:
            error_msg = f"Failed to delete agent: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'agent_id': agent_id,
                'error': str(e)
            })
            return False, error_msg
    
    # Conversation operations
    def create_conversation(self, conversation_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Create a new conversation"""
        try:
            item = ConversationSchema.create_conversation_item(**conversation_data)
            self.db_client.put_item(item, conversation_data['tenant_id'])
            
            lambda_logger.info("Conversation created", extra={
                'conversation_id': conversation_data['conversation_id'],
                'tenant_id': conversation_data['tenant_id'],
                'user_id': conversation_data['user_id'],
                'agent_id': conversation_data['agent_id']
            })
            
            return True, None
        except Exception as e:
            error_msg = f"Failed to create conversation: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'conversation_id': conversation_data.get('conversation_id'),
                'error': str(e)
            })
            return False, error_msg
    
    def get_conversation(self, tenant_id: str, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation by ID"""
        try:
            key = ConversationSchema.get_conversation_key(tenant_id, conversation_id)
            item = self.db_client.get_item(key['PK'], key['SK'], tenant_id)
            
            if item:
                lambda_logger.debug("Conversation retrieved", extra={
                    'conversation_id': conversation_id,
                    'tenant_id': tenant_id
                })
            
            return item
        except Exception as e:
            lambda_logger.error("Failed to get conversation", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None

    def get_conversation_by_id_global(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get conversation by ID across all tenants (for webhook validation)
        WARNING: This is a security-sensitive operation - use only for webhook validation
        """
        try:
            # Use GSI1 to search by conversation ID across tenants
            # This is less efficient but necessary for webhook validation
            items = self.db_client.query_gsi(
                'GSI1',
                f'CONVERSATION#{conversation_id}',
                None,  # No sort key condition
                filter_expression="EntityType = :entity_type",
                expression_attribute_values={':entity_type': 'Conversation'},
                limit=1
            )

            if items:
                conversation = items[0]
                return {
                    'conversation': conversation,
                    'tenant_id': conversation.get('TenantId')
                }

            return None

        except Exception as e:
            lambda_logger.error("Failed to get conversation globally", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return None

    def list_user_conversations(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """List conversations for a user"""
        try:
            query_params = ConversationSchema.get_user_conversations_query(user_id)
            query_params['Limit'] = limit
            
            response = self.db_client.query(**query_params)
            conversations = response.get('Items', [])
            
            lambda_logger.info("User conversations retrieved", extra={
                'user_id': user_id,
                'count': len(conversations)
            })
            
            return conversations
        except Exception as e:
            lambda_logger.error("Failed to list user conversations", extra={
                'user_id': user_id,
                'error': str(e)
            })
            return []
    
    def update_conversation_status(self, tenant_id: str, conversation_id: str, status: str) -> Tuple[bool, Optional[str]]:
        """Update conversation status"""
        try:
            # Get existing conversation first
            existing_conversation = self.get_conversation(tenant_id, conversation_id)
            if not existing_conversation:
                return False, "Conversation not found"
            
            # Update status
            existing_conversation['Status'] = status
            existing_conversation['UpdatedAt'] = datetime.utcnow().isoformat()
            
            # Save updated conversation
            self.db_client.put_item(existing_conversation, tenant_id)
            
            lambda_logger.info("Conversation status updated", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'new_status': status
            })
            
            return True, None
        except Exception as e:
            error_msg = f"Failed to update conversation status: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'status': status,
                'error': str(e)
            })
            return False, error_msg
    
    # Message operations
    def create_message(self, message_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Create a new message"""
        try:
            item = MessageSchema.create_message_item(**message_data)
            self.db_client.put_item(item, message_data['tenant_id'])
            
            lambda_logger.info("Message created", extra={
                'message_id': message_data['message_id'],
                'conversation_id': message_data['conversation_id'],
                'tenant_id': message_data['tenant_id'],
                'direction': message_data['direction']
            })
            
            return True, None
        except Exception as e:
            error_msg = f"Failed to create message: {str(e)}"
            lambda_logger.error(error_msg, extra={
                'message_id': message_data.get('message_id'),
                'error': str(e)
            })
            return False, error_msg
    
    def list_conversation_messages(
        self,
        conversation_id: str,
        limit: int = 50,
        last_evaluated_key: Dict[str, Any] = None
    ) -> Tuple[List[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """List messages in a conversation with pagination"""
        try:
            query_params = MessageSchema.get_conversation_messages_query(
                conversation_id, limit, last_evaluated_key
            )
            
            response = self.db_client.query(**query_params)
            messages = response.get('Items', [])
            next_key = response.get('LastEvaluatedKey')
            
            lambda_logger.info("Conversation messages retrieved", extra={
                'conversation_id': conversation_id,
                'count': len(messages),
                'has_more': next_key is not None
            })
            
            return messages, next_key
        except Exception as e:
            lambda_logger.error("Failed to list conversation messages", extra={
                'conversation_id': conversation_id,
                'error': str(e)
            })
            return [], None


# Singleton instance
database_service = DatabaseService()
