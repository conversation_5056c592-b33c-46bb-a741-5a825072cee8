# services/agent/src/services/optimized_queries.py
# Optimized DynamoDB queries for Agent Service

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

try:
    from shared.database import DynamoDBClient
    from shared.logger import lambda_logger
    from shared.metrics import metrics_manager
    
    shared_available = True
    
except ImportError:
    # Fallback for local development
    shared_available = False
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
    
    lambda_logger = MockLogger()

from ..config.agent_config import get_agent_config


class IOptimizedQueryService(ABC):
    """Interface for optimized query service operations."""

    @abstractmethod
    def get_active_agents_cached(self, cache_ttl_minutes: int = 5) -> List[Dict[str, Any]]:
        """Get active agents with caching optimization."""
        pass

    @abstractmethod
    def get_user_conversations_optimized(
        self,
        tenant_id: str,
        user_id: str,
        status_filter: str = 'active',
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get user conversations with optimization."""
        pass

    @abstractmethod
    def get_conversation_messages_optimized(
        self,
        conversation_id: str,
        limit: int = 50,
        last_evaluated_key: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], Optional[str]]:
        """Get conversation messages with pagination."""
        pass

    @abstractmethod
    def get_agent_conversations_count(self, agent_id: str, status: str = 'active') -> int:
        """Get count of conversations for an agent."""
        pass


class OptimizedQueryService(IOptimizedQueryService):
    """Optimized query service for Agent Service operations"""
    
    def __init__(self):
        self.config = get_agent_config()
        self.table_name = self.config.get('dynamodb_table_name', 'agent-scl-main-table')
        if shared_available:
            self.db_client = DynamoDBClient(self.table_name)
        else:
            self.db_client = None
    
    # Agent Queries
    def get_active_agents_cached(self, cache_ttl_minutes: int = 5) -> List[Dict[str, Any]]:
        """
        Get active agents with caching optimization
        Uses GSI1 for efficient querying
        """
        try:
            if not self.db_client:
                return []
            
            # Check cache first (if available)
            cache_key = "active_agents"
            if shared_available:
                cached_result = metrics_manager.cache.get(cache_key)
                if cached_result:
                    lambda_logger.info("Active agents retrieved from cache")
                    return cached_result
            
            # Query GSI1 for active agents
            items = self.db_client.query_gsi(
                'GSI1',
                'AGENT#ACTIVE',  # GSI1PK for active agents
                None,  # No sort key condition
                filter_expression="EntityType = :entity_type",
                expression_attribute_values={':entity_type': 'Agent'},
                limit=100  # Reasonable limit for active agents
            )
            
            # Cache the result
            if shared_available:
                metrics_manager.cache.set(
                    cache_key, 
                    items, 
                    ttl_minutes=cache_ttl_minutes
                )
            
            lambda_logger.info("Active agents retrieved from database", extra={
                'count': len(items),
                'cached': True
            })
            
            return items
            
        except Exception as e:
            lambda_logger.error("Failed to get active agents", extra={
                'error': str(e)
            })
            return []
    
    def get_agent_by_name(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """
        Get agent by name using GSI2
        Optimized for agent name lookups
        """
        try:
            if not self.db_client:
                return None
            
            # Query GSI2 for agent by name
            items = self.db_client.query_gsi(
                'GSI2',
                f'AGENT_NAME#{agent_name.lower()}',  # GSI2PK for agent names
                None,  # No sort key condition
                filter_expression="EntityType = :entity_type",
                expression_attribute_values={':entity_type': 'Agent'},
                limit=1
            )
            
            return items[0] if items else None
            
        except Exception as e:
            lambda_logger.error("Failed to get agent by name", extra={
                'agent_name': agent_name,
                'error': str(e)
            })
            return None
    
    # Conversation Queries
    def get_user_conversations_optimized(self, tenant_id: str, user_id: str, 
                                       status_filter: str = None, 
                                       limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get user conversations with optimized querying
        Uses GSI1 for efficient user-based queries
        """
        try:
            if not self.db_client:
                return []
            
            # Build GSI1PK for user conversations
            gsi1_pk = f'USER#{user_id}#CONVERSATIONS'
            
            # Add status filter to sort key if specified
            sort_key_condition = None
            if status_filter:
                sort_key_condition = f'STATUS#{status_filter}'
            
            # Query GSI1
            items = self.db_client.query_gsi(
                'GSI1',
                gsi1_pk,
                sort_key_condition,
                filter_expression="EntityType = :entity_type AND TenantId = :tenant_id",
                expression_attribute_values={
                    ':entity_type': 'Conversation',
                    ':tenant_id': tenant_id
                },
                limit=limit,
                scan_index_forward=False  # Most recent first
            )
            
            lambda_logger.info("User conversations retrieved optimized", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'status_filter': status_filter,
                'count': len(items)
            })
            
            return items
            
        except Exception as e:
            lambda_logger.error("Failed to get user conversations optimized", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def get_agent_conversations_count(self, agent_id: str, status: str = 'active') -> int:
        """
        Get count of conversations for an agent
        Optimized for dependency checking
        """
        try:
            if not self.db_client:
                return 0
            
            # Query GSI2 for agent conversations
            items = self.db_client.query_gsi(
                'GSI2',
                f'AGENT#{agent_id}#CONVERSATIONS',
                f'STATUS#{status}',
                filter_expression="EntityType = :entity_type",
                expression_attribute_values={':entity_type': 'Conversation'},
                select='COUNT'  # Only count, don't return items
            )
            
            return len(items)
            
        except Exception as e:
            lambda_logger.error("Failed to get agent conversations count", extra={
                'agent_id': agent_id,
                'status': status,
                'error': str(e)
            })
            return 0
    
    # Message Queries
    def get_conversation_messages_optimized(self, tenant_id: str, conversation_id: str,
                                          direction_filter: str = None,
                                          type_filter: str = None,
                                          limit: int = 50,
                                          last_evaluated_key: str = None) -> Dict[str, Any]:
        """
        Get conversation messages with optimized pagination
        Uses efficient DynamoDB pagination
        """
        try:
            if not self.db_client:
                return {'items': [], 'last_evaluated_key': None}
            
            # Build query parameters
            pk = f'TENANT#{tenant_id}'
            sk_prefix = f'CONVERSATION#{conversation_id}#MESSAGE#'
            
            # Build filter expression
            filter_expressions = ["EntityType = :entity_type"]
            expression_values = {':entity_type': 'Message'}
            
            if direction_filter:
                filter_expressions.append("Direction = :direction")
                expression_values[':direction'] = direction_filter
            
            if type_filter:
                filter_expressions.append("#type = :msg_type")
                expression_values[':msg_type'] = type_filter
            
            filter_expression = " AND ".join(filter_expressions)
            
            # Query with pagination
            result = self.db_client.query_with_pagination(
                pk,
                sk_prefix,
                filter_expression=filter_expression,
                expression_attribute_values=expression_values,
                expression_attribute_names={'#type': 'Type'} if type_filter else None,
                limit=limit,
                last_evaluated_key=last_evaluated_key,
                scan_index_forward=False  # Most recent first
            )
            
            lambda_logger.info("Conversation messages retrieved optimized", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'direction_filter': direction_filter,
                'type_filter': type_filter,
                'count': len(result['items'])
            })
            
            return result
            
        except Exception as e:
            lambda_logger.error("Failed to get conversation messages optimized", extra={
                'conversation_id': conversation_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return {'items': [], 'last_evaluated_key': None}
    
    # Analytics Queries
    def get_tenant_usage_stats(self, tenant_id: str, days: int = 30) -> Dict[str, Any]:
        """
        Get tenant usage statistics
        Optimized for analytics and reporting
        """
        try:
            if not self.db_client:
                return {}
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Query for conversations in date range
            conversations = self.db_client.query_gsi(
                'GSI1',
                f'TENANT#{tenant_id}#CONVERSATIONS',
                None,
                filter_expression="EntityType = :entity_type AND CreatedAt BETWEEN :start_date AND :end_date",
                expression_attribute_values={
                    ':entity_type': 'Conversation',
                    ':start_date': start_date.isoformat(),
                    ':end_date': end_date.isoformat()
                }
            )
            
            # Query for messages in date range
            messages = self.db_client.query_gsi(
                'GSI1',
                f'TENANT#{tenant_id}#MESSAGES',
                None,
                filter_expression="EntityType = :entity_type AND Timestamp BETWEEN :start_date AND :end_date",
                expression_attribute_values={
                    ':entity_type': 'Message',
                    ':start_date': start_date.isoformat(),
                    ':end_date': end_date.isoformat()
                }
            )
            
            # Calculate statistics
            stats = {
                'tenant_id': tenant_id,
                'period_days': days,
                'total_conversations': len(conversations),
                'total_messages': len(messages),
                'active_conversations': len([c for c in conversations if c.get('Status') == 'active']),
                'messages_by_direction': {
                    'inbound': len([m for m in messages if m.get('Direction') == 'inbound']),
                    'outbound': len([m for m in messages if m.get('Direction') == 'outbound'])
                },
                'messages_by_type': {
                    'text': len([m for m in messages if m.get('Type') == 'text']),
                    'document': len([m for m in messages if m.get('Type') == 'document']),
                    'audio': len([m for m in messages if m.get('Type') == 'audio'])
                }
            }
            
            lambda_logger.info("Tenant usage stats calculated", extra={
                'tenant_id': tenant_id,
                'days': days,
                'stats': stats
            })
            
            return stats
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant usage stats", extra={
                'tenant_id': tenant_id,
                'days': days,
                'error': str(e)
            })
            return {}
    
    # Health Check Queries
    def health_check_queries(self) -> Dict[str, Any]:
        """
        Perform health check queries to validate database performance
        """
        try:
            start_time = datetime.utcnow()
            
            # Test basic query
            test_result = self.db_client.query_gsi(
                'GSI1',
                'HEALTH_CHECK',
                None,
                limit=1
            ) if self.db_client else []
            
            end_time = datetime.utcnow()
            query_time_ms = (end_time - start_time).total_seconds() * 1000
            
            health_status = {
                'database_accessible': True,
                'query_time_ms': query_time_ms,
                'performance_ok': query_time_ms < 1000,  # Less than 1 second
                'timestamp': end_time.isoformat()
            }
            
            return health_status
            
        except Exception as e:
            return {
                'database_accessible': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global optimized query service instance
optimized_query_service = OptimizedQueryService()
