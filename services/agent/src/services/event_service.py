# services/agent/src/services/event_service.py
# Event service for Agent Service audit logging and event publishing

import json
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from shared.logger import lambda_logger, audit_log
    from shared.events import EventPublisher, EventType
    from shared.metrics import metrics_manager
    
    # Event publisher instance
    event_publisher = EventPublisher("agent-service")
    shared_available = True
    
except ImportError:
    # Fallback for local development
    shared_available = False
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def get_current_timestamp(self): return "2024-01-01T00:00:00Z"
    
    lambda_logger = MockLogger()
    
    def audit_log(*args, **kwargs): pass
    
    class MockEventPublisher:
        def publish_event(self, *args, **kwargs): pass
    
    event_publisher = MockEventPublisher()


class IEventService(ABC):
    """Interface for event service operations."""

    @abstractmethod
    def publish_agent_created(self, agent_data: Dict[str, Any], user_id: str, tenant_id: str) -> bool:
        """Publish agent created event."""
        pass

    @abstractmethod
    def publish_conversation_started(self, conversation_data: Dict[str, Any], user_id: str, tenant_id: str) -> bool:
        """Publish conversation started event."""
        pass

    @abstractmethod
    def publish_message_sent(self, message_data: Dict[str, Any], user_id: str, tenant_id: str) -> bool:
        """Publish message sent event."""
        pass

    @abstractmethod
    def publish_agent_response_received(self, response_data: Dict[str, Any], user_id: str, tenant_id: str) -> bool:
        """Publish agent response received event."""
        pass


class AgentEventService(IEventService):
    """Event service for Agent Service operations"""
    
    def __init__(self):
        self.service_name = "agent-service"
    
    # Agent Events
    def log_agent_created(self, agent_id: str, agent_name: str, tenant_id: str, user_id: str, 
                         client_ip: str = None, agent_data: Dict[str, Any] = None):
        """Log agent creation event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='agent_created',
                    resource_type='agent',
                    resource_id=agent_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={
                        'agent_name': agent_name,
                        'status': 'active',
                        'webhook_configured': bool(agent_data.get('webhookUrl') if agent_data else False)
                    }
                )
            
            # Publish event
            event_data = {
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'webhook_url': agent_data.get('webhookUrl') if agent_data else None,
                'status': 'active',
                'created_at': datetime.utcnow().isoformat()
            }
            
            if shared_available:
                event_publisher.publish_event(
                    event_type='agent.created',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )
            
            lambda_logger.info("Agent created event logged", extra={
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id,
                'user_id': user_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log agent created event", extra={
                'error': str(e),
                'agent_id': agent_id
            })
    
    def log_agent_updated(self, agent_id: str, agent_name: str, tenant_id: str, user_id: str,
                         changes: Dict[str, Any], client_ip: str = None):
        """Log agent update event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='agent_updated',
                    resource_type='agent',
                    resource_id=agent_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes=changes
                )
            
            # Publish event
            event_data = {
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'changes': changes,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            if shared_available:
                event_publisher.publish_event(
                    event_type='agent.updated',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )
            
            lambda_logger.info("Agent updated event logged", extra={
                'agent_id': agent_id,
                'changes': list(changes.keys()),
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log agent updated event", extra={
                'error': str(e),
                'agent_id': agent_id
            })
    
    def log_agent_deleted(self, agent_id: str, agent_name: str, tenant_id: str, user_id: str,
                         client_ip: str = None):
        """Log agent deletion event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='agent_deleted',
                    resource_type='agent',
                    resource_id=agent_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={'status': 'deleted'}
                )
            
            # Publish event
            event_data = {
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'deleted_at': datetime.utcnow().isoformat()
            }
            
            if shared_available:
                event_publisher.publish_event(
                    event_type='agent.deleted',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )
            
            lambda_logger.info("Agent deleted event logged", extra={
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log agent deleted event", extra={
                'error': str(e),
                'agent_id': agent_id
            })
    
    def log_agent_status_changed(self, agent_id: str, agent_name: str, tenant_id: str, user_id: str,
                                old_status: str, new_status: str, client_ip: str = None):
        """Log agent status change event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='agent_status_changed',
                    resource_type='agent',
                    resource_id=agent_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={
                        'previous_status': old_status,
                        'new_status': new_status
                    }
                )
            
            # Publish event
            event_data = {
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'previous_status': old_status,
                'new_status': new_status,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            if shared_available:
                event_publisher.publish_event(
                    event_type='agent.status_changed',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )
            
            lambda_logger.info("Agent status changed event logged", extra={
                'agent_id': agent_id,
                'old_status': old_status,
                'new_status': new_status,
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log agent status changed event", extra={
                'error': str(e),
                'agent_id': agent_id
            })
    
    # Conversation Events
    def log_conversation_created(self, conversation_id: str, agent_id: str, agent_name: str,
                               tenant_id: str, user_id: str, webhook_url: str, client_ip: str = None):
        """Log conversation creation event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='conversation_created',
                    resource_type='conversation',
                    resource_id=conversation_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={
                        'agent_id': agent_id,
                        'agent_name': agent_name,
                        'status': 'active',
                        'webhook_generated': True
                    }
                )
            
            # Publish event
            event_data = {
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'agent_name': agent_name,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'webhook_url': webhook_url,
                'status': 'active',
                'created_at': datetime.utcnow().isoformat()
            }
            
            if shared_available:
                event_publisher.publish_event(
                    event_type='conversation.created',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )
            
            lambda_logger.info("Conversation created event logged", extra={
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'tenant_id': tenant_id,
                'user_id': user_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log conversation created event", extra={
                'error': str(e),
                'conversation_id': conversation_id
            })
    
    def log_conversation_status_changed(self, conversation_id: str, agent_id: str, tenant_id: str,
                                      user_id: str, old_status: str, new_status: str, client_ip: str = None):
        """Log conversation status change event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='conversation_status_changed',
                    resource_type='conversation',
                    resource_id=conversation_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={
                        'previous_status': old_status,
                        'new_status': new_status,
                        'agent_id': agent_id
                    }
                )
            
            # Publish event
            event_data = {
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'previous_status': old_status,
                'new_status': new_status,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            if shared_available:
                event_publisher.publish_event(
                    event_type='conversation.status_changed',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )
            
            lambda_logger.info("Conversation status changed event logged", extra={
                'conversation_id': conversation_id,
                'old_status': old_status,
                'new_status': new_status,
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log conversation status changed event", extra={
                'error': str(e),
                'conversation_id': conversation_id
            })


    # Message Events
    def log_message_sent(self, message_id: str, conversation_id: str, agent_id: str, tenant_id: str,
                        user_id: str, message_type: str, has_attachments: bool = False, client_ip: str = None):
        """Log message sent event"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='message_sent',
                    resource_type='message',
                    resource_id=message_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={
                        'conversation_id': conversation_id,
                        'agent_id': agent_id,
                        'message_type': message_type,
                        'direction': 'outbound',
                        'has_attachments': has_attachments
                    }
                )

            # Publish event
            event_data = {
                'message_id': message_id,
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'message_type': message_type,
                'direction': 'outbound',
                'has_attachments': has_attachments,
                'sent_at': datetime.utcnow().isoformat()
            }

            if shared_available:
                event_publisher.publish_event(
                    event_type='message.sent',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )

            lambda_logger.info("Message sent event logged", extra={
                'message_id': message_id,
                'conversation_id': conversation_id,
                'message_type': message_type,
                'tenant_id': tenant_id
            })

        except Exception as e:
            lambda_logger.error("Failed to log message sent event", extra={
                'error': str(e),
                'message_id': message_id
            })

    def log_message_received(self, message_id: str, conversation_id: str, agent_id: str, tenant_id: str,
                           user_id: str, message_type: str, source_ip: str = None):
        """Log message received event (from agent)"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='message_received',
                    resource_type='message',
                    resource_id=message_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=source_ip,
                    changes={
                        'conversation_id': conversation_id,
                        'agent_id': agent_id,
                        'message_type': message_type,
                        'direction': 'inbound',
                        'source': 'webhook'
                    }
                )

            # Publish event
            event_data = {
                'message_id': message_id,
                'conversation_id': conversation_id,
                'agent_id': agent_id,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'message_type': message_type,
                'direction': 'inbound',
                'source': 'webhook',
                'received_at': datetime.utcnow().isoformat()
            }

            if shared_available:
                event_publisher.publish_event(
                    event_type='message.received',
                    data=event_data,
                    source=self.service_name,
                    tenant_id=tenant_id
                )

            lambda_logger.info("Message received event logged", extra={
                'message_id': message_id,
                'conversation_id': conversation_id,
                'message_type': message_type,
                'tenant_id': tenant_id
            })

        except Exception as e:
            lambda_logger.error("Failed to log message received event", extra={
                'error': str(e),
                'message_id': message_id
            })

    # Security Events
    def log_unauthorized_access_attempt(self, resource_type: str, resource_id: str,
                                      attempted_action: str, user_id: str = None,
                                      tenant_id: str = None, client_ip: str = None):
        """Log unauthorized access attempt"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action='unauthorized_access_attempt',
                    resource_type=resource_type,
                    resource_id=resource_id,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    client_ip=client_ip,
                    changes={
                        'attempted_action': attempted_action,
                        'access_denied': True
                    }
                )

            # Record security metrics
            if shared_available:
                metrics_manager.security.record_security_event(
                    'unauthorized_access_attempt',
                    'warning',
                    tenant_id
                )

            lambda_logger.warning("Unauthorized access attempt logged", extra={
                'resource_type': resource_type,
                'resource_id': resource_id,
                'attempted_action': attempted_action,
                'user_id': user_id,
                'tenant_id': tenant_id,
                'client_ip': client_ip
            })

        except Exception as e:
            lambda_logger.error("Failed to log unauthorized access attempt", extra={
                'error': str(e),
                'resource_type': resource_type,
                'resource_id': resource_id
            })

    def log_webhook_security_event(self, event_type: str, conversation_id: str,
                                 source_ip: str, details: Dict[str, Any] = None):
        """Log webhook security events"""
        try:
            # Audit log
            if shared_available:
                audit_log(
                    action=f'webhook_{event_type}',
                    resource_type='webhook',
                    resource_id=conversation_id,
                    user_id=None,
                    tenant_id=None,  # May not be available for security events
                    client_ip=source_ip,
                    changes=details or {}
                )

            # Record security metrics
            if shared_available:
                metrics_manager.security.record_security_event(
                    f'webhook_{event_type}',
                    'warning'
                )

            lambda_logger.warning("Webhook security event logged", extra={
                'event_type': event_type,
                'conversation_id': conversation_id,
                'source_ip': source_ip,
                'details': details
            })

        except Exception as e:
            lambda_logger.error("Failed to log webhook security event", extra={
                'error': str(e),
                'event_type': event_type,
                'conversation_id': conversation_id
            })


# Global event service instance
agent_event_service = AgentEventService()
