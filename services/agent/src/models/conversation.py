# services/agent/src/models/conversation.py
# Conversation entity model and data structures

from typing import Dict, Any, Optional
from datetime import datetime
import uuid


class Conversation:
    """Conversation entity model for managing user-agent conversations"""
    
    def __init__(
        self,
        conversation_id: str = None,
        tenant_id: str = None,
        user_id: str = None,
        agent_id: str = None,
        status: str = "active",
        webhook_url: str = None,
        created_at: datetime = None,
        updated_at: datetime = None,
        metadata: Dict[str, Any] = None
    ):
        self.conversation_id = conversation_id or str(uuid.uuid4())
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.agent_id = agent_id
        self.status = status  # active, closed, archived
        self.webhook_url = webhook_url
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation to dictionary for DynamoDB storage"""
        return {
            'PK': f'TENANT#{self.tenant_id}',
            'SK': f'CONVERSATION#{self.conversation_id}',
            'GSI1PK': f'USER#{self.user_id}',
            'GSI1SK': f'CONVERSATION#{self.conversation_id}',
            'GSI2PK': f'AGENT#{self.agent_id}',
            'GSI2SK': f'CONVERSATION#{self.conversation_id}',
            'EntityType': 'Conversation',
            'ConversationId': self.conversation_id,
            'TenantId': self.tenant_id,
            'UserId': self.user_id,
            'AgentId': self.agent_id,
            'Status': self.status,
            'WebhookUrl': self.webhook_url,
            'CreatedAt': self.created_at.isoformat(),
            'UpdatedAt': self.updated_at.isoformat(),
            'Metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create conversation from DynamoDB item"""
        return cls(
            conversation_id=data.get('ConversationId'),
            tenant_id=data.get('TenantId'),
            user_id=data.get('UserId'),
            agent_id=data.get('AgentId'),
            status=data.get('Status', 'active'),
            webhook_url=data.get('WebhookUrl'),
            created_at=datetime.fromisoformat(data.get('CreatedAt')) if data.get('CreatedAt') else None,
            updated_at=datetime.fromisoformat(data.get('UpdatedAt')) if data.get('UpdatedAt') else None,
            metadata=data.get('Metadata', {})
        )
    
    def is_active(self) -> bool:
        """Check if conversation is active"""
        return self.status == 'active'
    
    def close(self) -> None:
        """Close conversation"""
        self.status = 'closed'
        self.updated_at = datetime.utcnow()
    
    def archive(self) -> None:
        """Archive conversation"""
        self.status = 'archived'
        self.updated_at = datetime.utcnow()
    
    def generate_webhook_url(self, base_url: str) -> str:
        """Generate unique webhook URL for this conversation"""
        from ..utils.webhook_manager import WebhookManager

        self.webhook_url = WebhookManager.generate_conversation_webhook_url(
            self.conversation_id,
            base_url
        )
        self.updated_at = datetime.utcnow()
        return self.webhook_url

    def generate_webhook_secret(self, agent_id: str) -> str:
        """Generate secure webhook secret for this conversation"""
        from ..utils.webhook_manager import WebhookManager

        return WebhookManager.generate_webhook_secret(
            self.conversation_id,
            agent_id
        )


class ConversationCreateRequest:
    """Request model for creating new conversations"""
    
    def __init__(self, data: Dict[str, Any], tenant_id: str, user_id: str):
        self.agent_id = data.get('agentId')
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.metadata = data.get('metadata', {})
    
    def validate(self) -> tuple[bool, str]:
        """Validate conversation creation request"""
        if not self.agent_id or not self.agent_id.strip():
            return False, "Agent ID is required"
        
        if not self.tenant_id or not self.tenant_id.strip():
            return False, "Tenant ID is required"
        
        if not self.user_id or not self.user_id.strip():
            return False, "User ID is required"
        
        return True, ""
    
    def to_conversation(self) -> Conversation:
        """Convert request to Conversation entity"""
        return Conversation(
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            agent_id=self.agent_id,
            metadata=self.metadata,
            status='active'
        )
