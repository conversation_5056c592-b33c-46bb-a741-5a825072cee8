# services/agent/src/models/__init__.py
# Models package for agent service

"""
Models package for agent service.
Exports unified models that use shared layer as source of truth.
"""

# Import agent-specific models (extended from shared)
from .agent import Agent, AgentCreateRequest
from .conversation import Conversation, ConversationCreateRequest
from .message import Message, MessageCreateRequest
from .schema import AgentSchema, ConversationSchema, MessageSchema

# Re-export shared models for convenience
from shared.models import (
    UserInfo, UserRole, UserStatus,
    TenantInfo, TenantStatus, TenantPlan,
    CustomerInfo, CustomerStatus,
    SubscriptionInfo, SubscriptionStatus, BillingInterval
)

__all__ = [
    # Agent-specific models
    'Agent', 'AgentCreateRequest',
    'Conversation', 'ConversationCreateRequest',
    'Message', 'MessageCreateRequest',
    'AgentSchema', 'ConversationSchema', 'MessageSchema',

    # Shared models (re-exported for convenience)
    'UserInfo', 'UserRole', 'UserStatus',
    'TenantInfo', 'TenantStatus', 'TenantPlan',
    'CustomerInfo', 'CustomerStatus',
    'SubscriptionInfo', 'SubscriptionStatus', 'BillingInterval'
]
