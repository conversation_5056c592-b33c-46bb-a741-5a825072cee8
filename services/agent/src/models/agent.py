# services/agent/src/models/agent.py
# Agent entity model and data structures

from typing import Dict, Any, Optional
from datetime import datetime
import uuid


class Agent:
    """Agent entity model for managing external agents (Feedo, Forecaster)"""
    
    def __init__(
        self,
        agent_id: str = None,
        name: str = None,
        description: str = None,
        webhook_url: str = None,
        document_webhook_url: str = None,
        bearer_token: str = None,
        secret: str = None,
        status: str = "inactive",
        input_parameters: Dict[str, Any] = None,
        metadata: Dict[str, Any] = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.webhook_url = webhook_url
        self.document_webhook_url = document_webhook_url
        self.bearer_token = bearer_token
        self.secret = secret
        self.status = status  # active, inactive
        self.input_parameters = input_parameters or {}
        self.metadata = metadata or {}
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary for DynamoDB storage"""
        return {
            'PK': f'AGENT#{self.agent_id}',
            'SK': f'AGENT#{self.agent_id}',
            'GSI1PK': f'AGENT_STATUS#{self.status}',
            'GSI1SK': f'AGENT#{self.agent_id}',
            'EntityType': 'Agent',
            'AgentId': self.agent_id,
            'Name': self.name,
            'Description': self.description,
            'WebhookUrl': self.webhook_url,
            'DocumentWebhookUrl': self.document_webhook_url,
            'BearerToken': self.bearer_token,
            'Secret': self.secret,
            'Status': self.status,
            'InputParameters': self.input_parameters,
            'Metadata': self.metadata,
            'CreatedAt': self.created_at.isoformat(),
            'UpdatedAt': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Agent':
        """Create agent from DynamoDB item"""
        return cls(
            agent_id=data.get('AgentId'),
            name=data.get('Name'),
            description=data.get('Description'),
            webhook_url=data.get('WebhookUrl'),
            document_webhook_url=data.get('DocumentWebhookUrl'),
            bearer_token=data.get('BearerToken'),
            secret=data.get('Secret'),
            status=data.get('Status', 'inactive'),
            input_parameters=data.get('InputParameters', {}),
            metadata=data.get('Metadata', {}),
            created_at=datetime.fromisoformat(data.get('CreatedAt')) if data.get('CreatedAt') else None,
            updated_at=datetime.fromisoformat(data.get('UpdatedAt')) if data.get('UpdatedAt') else None
        )
    
    def is_active(self) -> bool:
        """Check if agent is active and has valid webhook configuration"""
        return (
            self.status == 'active' and
            self.webhook_url is not None and
            self.webhook_url.strip() != ''
        )
    
    def update_status(self, status: str) -> None:
        """Update agent status and timestamp"""
        self.status = status
        self.updated_at = datetime.utcnow()
    
    def update_fields(self, **kwargs) -> None:
        """Update agent fields and timestamp"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()


class AgentCreateRequest:
    """Request model for creating new agents"""
    
    def __init__(self, data: Dict[str, Any]):
        self.name = data.get('name')
        self.description = data.get('description')
        self.webhook_url = data.get('webhookUrl')
        self.document_webhook_url = data.get('documentWebhookUrl')
        self.bearer_token = data.get('bearerToken')
        self.secret = data.get('secret')
        self.input_parameters = data.get('inputParameters', {})
        self.metadata = data.get('metadata', {})
    
    def validate(self) -> tuple[bool, str]:
        """Validate agent creation request"""
        if not self.name or not self.name.strip():
            return False, "Agent name is required"
        
        if not self.webhook_url or not self.webhook_url.strip():
            return False, "Webhook URL is required"
        
        if not self.bearer_token or not self.bearer_token.strip():
            return False, "Bearer token is required"
        
        if not self.secret or not self.secret.strip():
            return False, "Secret is required"
        
        return True, ""
    
    def to_agent(self) -> Agent:
        """Convert request to Agent entity"""
        return Agent(
            name=self.name,
            description=self.description,
            webhook_url=self.webhook_url,
            document_webhook_url=self.document_webhook_url,
            bearer_token=self.bearer_token,
            secret=self.secret,
            input_parameters=self.input_parameters,
            metadata=self.metadata,
            status='active'  # New agents are active by default
        )
