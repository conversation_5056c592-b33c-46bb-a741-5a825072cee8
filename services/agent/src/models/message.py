# services/agent/src/models/message.py
# Message entity model and data structures

from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid


class Message:
    """Message entity model for managing conversation messages"""
    
    def __init__(
        self,
        message_id: str = None,
        conversation_id: str = None,
        tenant_id: str = None,
        user_id: str = None,
        direction: str = None,  # outbound, inbound
        message_type: str = "text",  # text, document, audio
        content: str = None,
        attachments: List[Dict[str, Any]] = None,
        timestamp: datetime = None,
        metadata: Dict[str, Any] = None
    ):
        self.message_id = message_id or str(uuid.uuid4())
        self.conversation_id = conversation_id
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.direction = direction
        self.message_type = message_type
        self.content = content
        self.attachments = attachments or []
        self.timestamp = timestamp or datetime.utcnow()
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for DynamoDB storage"""
        return {
            'PK': f'CONVERSATION#{self.conversation_id}',
            'SK': f'MESSAGE#{self.timestamp.isoformat()}#{self.message_id}',
            'GSI1PK': f'TENANT#{self.tenant_id}',
            'GSI1SK': f'MESSAGE#{self.timestamp.isoformat()}#{self.message_id}',
            'EntityType': 'Message',
            'MessageId': self.message_id,
            'ConversationId': self.conversation_id,
            'TenantId': self.tenant_id,
            'UserId': self.user_id,
            'Direction': self.direction,
            'MessageType': self.message_type,
            'Content': self.content,
            'Attachments': self.attachments,
            'Timestamp': self.timestamp.isoformat(),
            'Metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from DynamoDB item"""
        return cls(
            message_id=data.get('MessageId'),
            conversation_id=data.get('ConversationId'),
            tenant_id=data.get('TenantId'),
            user_id=data.get('UserId'),
            direction=data.get('Direction'),
            message_type=data.get('MessageType', 'text'),
            content=data.get('Content'),
            attachments=data.get('Attachments', []),
            timestamp=datetime.fromisoformat(data.get('Timestamp')) if data.get('Timestamp') else None,
            metadata=data.get('Metadata', {})
        )
    
    def is_outbound(self) -> bool:
        """Check if message is outbound (from user to agent)"""
        return self.direction == 'outbound'
    
    def is_inbound(self) -> bool:
        """Check if message is inbound (from agent to user)"""
        return self.direction == 'inbound'
    
    def has_attachments(self) -> bool:
        """Check if message has attachments"""
        return len(self.attachments) > 0


class MessageSendRequest:
    """Request model for sending messages to agents"""
    
    def __init__(self, data: Dict[str, Any], conversation_id: str, tenant_id: str, user_id: str):
        self.conversation_id = conversation_id
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.message_type = data.get('type', 'text')
        self.content = data.get('content', '')
        self.attachments = data.get('attachments', [])
        self.metadata = data.get('metadata', {})
    
    def validate(self) -> tuple[bool, str]:
        """Validate message send request"""
        if self.message_type not in ['text', 'document', 'audio']:
            return False, "Invalid message type. Must be 'text', 'document', or 'audio'"
        
        if self.message_type == 'text' and not self.content.strip():
            return False, "Text content is required for text messages"
        
        if self.message_type in ['document', 'audio'] and not self.attachments:
            return False, f"Attachments are required for {self.message_type} messages"
        
        return True, ""
    
    def to_message(self) -> Message:
        """Convert request to Message entity"""
        return Message(
            conversation_id=self.conversation_id,
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            direction='outbound',
            message_type=self.message_type,
            content=self.content,
            attachments=self.attachments,
            metadata=self.metadata
        )


class N8nMessagePayload:
    """Payload model for sending messages to n8n webhooks"""
    
    def __init__(
        self,
        user_id: str,
        tenant_id: str,
        chat_id: str,
        message: str = None,
        user_name: str = None,
        file_type: str = None,
        file_name: str = None
    ):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.chat_id = chat_id
        self.message = message
        self.user_name = user_name
        self.file_type = file_type
        self.file_name = file_name
    
    def to_json_payload(self) -> Dict[str, Any]:
        """Convert to JSON payload for text messages"""
        return {
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'chatId': self.chat_id,
            'message': self.message,
            'userName': self.user_name
        }
    
    def to_multipart_payload(self) -> Dict[str, Any]:
        """Convert to multipart payload for document/audio messages"""
        return {
            'userId': self.user_id,
            'tenantId': self.tenant_id,
            'chatId': self.chat_id,
            'fileType': self.file_type,
            'fileName': self.file_name
        }
