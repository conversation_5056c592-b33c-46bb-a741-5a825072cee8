# services/agent/src/models/schema.py
# DynamoDB schema definitions for Agent Service entities

"""
DynamoDB Single-Table Design for Agent Service

This module defines the schema patterns for storing agent-related entities
in the existing DynamoDB single-table structure.

ENTITIES:
1. Agent - External agent configurations (<PERSON>edo, Forecaster)
2. Conversation - User-agent conversation sessions
3. Message - Individual messages within conversations

ACCESS PATTERNS:
1. Get agent by ID → PK: AGENT#{agent_id}, SK: AGENT#{agent_id}
2. List active agents → GSI1: AGENT_STATUS#active
3. Get conversation by ID → PK: TENANT#{tenant_id}, SK: CONVERSATION#{conversation_id}
4. List conversations by user → GSI1: USER#{user_id}
5. List conversations by agent → GSI2: AGENT#{agent_id}
6. Get messages in conversation → PK: CONVERSATION#{conversation_id}, SK: MESSAGE#{timestamp}#{message_id}
7. List messages by tenant → GSI1: TENANT#{tenant_id}

INDEXES USED:
- Primary: PK, SK
- GSI1: For user-based queries and status filtering
- GSI2: For agent-based queries and cross-references
- GSI3: Available for future use (e.g., time-based queries)
"""

from typing import Dict, Any, List
from datetime import datetime


class AgentSchema:
    """Schema patterns for Agent entities"""
    
    @staticmethod
    def create_agent_item(
        agent_id: str,
        name: str,
        description: str,
        webhook_url: str,
        document_webhook_url: str,
        bearer_token: str,
        secret: str,
        status: str = "active",
        input_parameters: Dict[str, Any] = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create DynamoDB item for Agent entity"""
        current_time = datetime.utcnow().isoformat()
        
        return {
            # Primary keys
            'PK': f'AGENT#{agent_id}',
            'SK': f'AGENT#{agent_id}',
            
            # GSI1 - For status-based queries
            'GSI1PK': f'AGENT_STATUS#{status}',
            'GSI1SK': f'AGENT#{agent_id}',
            
            # GSI2 - For name-based queries (future use)
            'GSI2PK': f'AGENT_NAME#{name.upper().replace(" ", "_")}',
            'GSI2SK': f'AGENT#{agent_id}',
            
            # Entity metadata
            'EntityType': 'Agent',
            'AgentId': agent_id,
            'Name': name,
            'Description': description,
            'WebhookUrl': webhook_url,
            'DocumentWebhookUrl': document_webhook_url,
            'BearerToken': bearer_token,
            'Secret': secret,
            'Status': status,
            'InputParameters': input_parameters or {},
            'Metadata': metadata or {},
            'CreatedAt': current_time,
            'UpdatedAt': current_time
        }
    
    @staticmethod
    def get_agent_key(agent_id: str) -> Dict[str, str]:
        """Get primary key for agent lookup"""
        return {
            'PK': f'AGENT#{agent_id}',
            'SK': f'AGENT#{agent_id}'
        }
    
    @staticmethod
    def get_active_agents_query() -> Dict[str, Any]:
        """Get query parameters for listing active agents"""
        return {
            'IndexName': 'GSI1',
            'KeyConditionExpression': 'GSI1PK = :status',
            'ExpressionAttributeValues': {
                ':status': 'AGENT_STATUS#active'
            }
        }


class ConversationSchema:
    """Schema patterns for Conversation entities"""
    
    @staticmethod
    def create_conversation_item(
        conversation_id: str,
        tenant_id: str,
        user_id: str,
        agent_id: str,
        webhook_url: str = None,
        status: str = "active",
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create DynamoDB item for Conversation entity"""
        current_time = datetime.utcnow().isoformat()
        
        return {
            # Primary keys - Tenant-based partitioning for multi-tenancy
            'PK': f'TENANT#{tenant_id}',
            'SK': f'CONVERSATION#{conversation_id}',
            
            # GSI1 - For user-based queries
            'GSI1PK': f'USER#{user_id}',
            'GSI1SK': f'CONVERSATION#{conversation_id}',
            
            # GSI2 - For agent-based queries
            'GSI2PK': f'AGENT#{agent_id}',
            'GSI2SK': f'CONVERSATION#{conversation_id}',
            
            # Entity metadata
            'EntityType': 'Conversation',
            'ConversationId': conversation_id,
            'TenantId': tenant_id,
            'UserId': user_id,
            'AgentId': agent_id,
            'Status': status,
            'WebhookUrl': webhook_url,
            'Metadata': metadata or {},
            'CreatedAt': current_time,
            'UpdatedAt': current_time
        }
    
    @staticmethod
    def get_conversation_key(tenant_id: str, conversation_id: str) -> Dict[str, str]:
        """Get primary key for conversation lookup"""
        return {
            'PK': f'TENANT#{tenant_id}',
            'SK': f'CONVERSATION#{conversation_id}'
        }
    
    @staticmethod
    def get_user_conversations_query(user_id: str) -> Dict[str, Any]:
        """Get query parameters for listing user conversations"""
        return {
            'IndexName': 'GSI1',
            'KeyConditionExpression': 'GSI1PK = :user_id',
            'ExpressionAttributeValues': {
                ':user_id': f'USER#{user_id}'
            },
            'ScanIndexForward': False  # Most recent first
        }
    
    @staticmethod
    def get_agent_conversations_query(agent_id: str) -> Dict[str, Any]:
        """Get query parameters for listing agent conversations"""
        return {
            'IndexName': 'GSI2',
            'KeyConditionExpression': 'GSI2PK = :agent_id',
            'ExpressionAttributeValues': {
                ':agent_id': f'AGENT#{agent_id}'
            },
            'ScanIndexForward': False  # Most recent first
        }


class MessageSchema:
    """Schema patterns for Message entities"""
    
    @staticmethod
    def create_message_item(
        message_id: str,
        conversation_id: str,
        tenant_id: str,
        user_id: str,
        direction: str,  # outbound, inbound
        message_type: str = "text",  # text, document, audio
        content: str = None,
        attachments: List[Dict[str, Any]] = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create DynamoDB item for Message entity"""
        current_time = datetime.utcnow()
        timestamp_iso = current_time.isoformat()
        
        return {
            # Primary keys - Conversation-based partitioning with timestamp sorting
            'PK': f'CONVERSATION#{conversation_id}',
            'SK': f'MESSAGE#{timestamp_iso}#{message_id}',
            
            # GSI1 - For tenant-based queries
            'GSI1PK': f'TENANT#{tenant_id}',
            'GSI1SK': f'MESSAGE#{timestamp_iso}#{message_id}',
            
            # GSI2 - For user-based queries
            'GSI2PK': f'USER#{user_id}',
            'GSI2SK': f'MESSAGE#{timestamp_iso}#{message_id}',
            
            # Entity metadata
            'EntityType': 'Message',
            'MessageId': message_id,
            'ConversationId': conversation_id,
            'TenantId': tenant_id,
            'UserId': user_id,
            'Direction': direction,
            'MessageType': message_type,
            'Content': content,
            'Attachments': attachments or [],
            'Timestamp': timestamp_iso,
            'Metadata': metadata or {}
        }
    
    @staticmethod
    def get_conversation_messages_query(
        conversation_id: str,
        limit: int = 50,
        last_evaluated_key: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Get query parameters for listing conversation messages"""
        query_params = {
            'KeyConditionExpression': 'PK = :conversation_id AND begins_with(SK, :message_prefix)',
            'ExpressionAttributeValues': {
                ':conversation_id': f'CONVERSATION#{conversation_id}',
                ':message_prefix': 'MESSAGE#'
            },
            'ScanIndexForward': False,  # Most recent first
            'Limit': limit
        }
        
        if last_evaluated_key:
            query_params['ExclusiveStartKey'] = last_evaluated_key
        
        return query_params
    
    @staticmethod
    def get_tenant_messages_query(
        tenant_id: str,
        limit: int = 100,
        last_evaluated_key: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Get query parameters for listing tenant messages"""
        query_params = {
            'IndexName': 'GSI1',
            'KeyConditionExpression': 'GSI1PK = :tenant_id AND begins_with(GSI1SK, :message_prefix)',
            'ExpressionAttributeValues': {
                ':tenant_id': f'TENANT#{tenant_id}',
                ':message_prefix': 'MESSAGE#'
            },
            'ScanIndexForward': False,  # Most recent first
            'Limit': limit
        }
        
        if last_evaluated_key:
            query_params['ExclusiveStartKey'] = last_evaluated_key
        
        return query_params


class WebhookSchema:
    """Schema patterns for Webhook tracking (optional)"""
    
    @staticmethod
    def create_webhook_log_item(
        webhook_id: str,
        conversation_id: str,
        tenant_id: str,
        direction: str,  # incoming, outgoing
        status: str,  # success, failed, pending
        payload: Dict[str, Any] = None,
        response: Dict[str, Any] = None,
        error_message: str = None
    ) -> Dict[str, Any]:
        """Create DynamoDB item for Webhook log (for debugging/auditing)"""
        current_time = datetime.utcnow().isoformat()
        
        return {
            # Primary keys
            'PK': f'WEBHOOK_LOG#{conversation_id}',
            'SK': f'WEBHOOK#{current_time}#{webhook_id}',
            
            # GSI1 - For tenant-based queries
            'GSI1PK': f'TENANT#{tenant_id}',
            'GSI1SK': f'WEBHOOK#{current_time}#{webhook_id}',
            
            # Entity metadata
            'EntityType': 'WebhookLog',
            'WebhookId': webhook_id,
            'ConversationId': conversation_id,
            'TenantId': tenant_id,
            'Direction': direction,
            'Status': status,
            'Payload': payload or {},
            'Response': response or {},
            'ErrorMessage': error_message,
            'Timestamp': current_time,
            
            # TTL for automatic cleanup (30 days)
            'TTL': int(datetime.utcnow().timestamp()) + (30 * 24 * 60 * 60)
        }
