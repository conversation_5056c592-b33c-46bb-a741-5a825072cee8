sequenceDiagram
    participant Admin as 👨‍💼 Admin User
    participant API as 🌐 Agent Service API
    participant CAH as 🤖 Create Agent Handler
    participant AMS as 🤖 Agent Management Service
    participant DB as 🗄️ DynamoDB
    participant SSM as 📋 SSM Parameter Store
    participant N8N as 🔗 N8N Workflow
    participant User as 👤 End User
    participant CCH as 💬 Create Conversation Handler
    participant CS as 💬 Conversation Service
    participant HR as 🔀 Hybrid Router
    participant CW as 📊 CloudWatch

    Note over Admin: Admin wants to create a new agent
    Admin->>API: POST /agent/agents
    Note over Admin,API: {name: "Customer Support Agent", type: "support", webhook_url: "..."}
    
    API->>CAH: Route to Create Agent Handler
    CAH->>CAH: Validate agent configuration
    CAH->>CAH: Check agent type permissions
    
    CAH->>AMS: create_agent(agent_data, tenant_id)
    AMS->>AMS: Generate unique agent_id
    AMS->>AMS: Validate webhook URL format
    
    AMS->>DB: Store agent record
    Note over DB: PK: AGENT#{agent_id}<br/>SK: METADATA<br/>GSI1PK: TENANT#{tenant_id}
    DB-->>AMS: Agent stored successfully
    
    AMS->>SSM: Store webhook configuration (if needed)
    SSM-->>AMS: Configuration stored
    
    AMS->>N8N: Test webhook connectivity (optional)
    N8N-->>AMS: Webhook test successful
    
    AMS->>CW: Log agent creation event
    AMS-->>CAH: Agent created successfully
    CAH-->>API: 201 Created {agent_id, status: "active"}
    API-->>Admin: Agent creation response
    
    rect rgb(245, 245, 255)
        Note over User: User starts conversation with agent
        User->>API: POST /agent/conversations
        Note over User,API: {agent_type: "support", initial_message: "I need help"}
        
        API->>CCH: Route to Create Conversation Handler
        CCH->>HR: determine_best_agent(agent_type, tenant_id)
        HR->>DB: Query available agents of type "support"
        DB-->>HR: List of available support agents
        HR-->>CCH: Assign agent_id: "support-001"
        
        CCH->>CS: create_conversation(user_id, agent_id, context)
        CS->>CS: Generate conversation_id
        CS->>DB: Store conversation record
        Note over DB: PK: AGENT_CONVERSATION#{conversation_id}<br/>SK: METADATA<br/>agent_id: support-001
        
        CS->>DB: Store initial message
        CS->>CW: Log conversation creation
        CS-->>CCH: Conversation created
        CCH-->>API: 201 Created {conversation_id, agent_id}
        API-->>User: Conversation started
    end
    
    rect rgb(255, 255, 245)
        Note over Admin: Admin monitors agent performance
        Admin->>API: GET /agent/agents/{agent_id}/metrics
        API->>AMS: get_agent_metrics(agent_id, period)
        AMS->>DB: Query agent performance data
        Note over DB: Query agent messages and response times
        DB-->>AMS: Performance metrics
        AMS-->>API: {success_rate: 0.92, avg_response_time: 2.1s, conversations: 45}
        API-->>Admin: Agent performance data
    end
    
    rect rgb(245, 255, 245)
        Note over User: User ends conversation
        User->>API: PUT /agent/conversations/{conversation_id}/close
        API->>CS: close_conversation(conversation_id, user_id)
        
        CS->>DB: Update conversation status to "closed"
        CS->>DB: Calculate conversation metrics
        Note over DB: Duration, message count, satisfaction score
        
        CS->>AMS: update_agent_metrics(agent_id, conversation_metrics)
        AMS->>DB: Update agent performance counters
        
        CS->>CW: Log conversation closure
        CS-->>API: Conversation closed successfully
        API-->>User: 200 OK {status: "closed"}
    end
    
    rect rgb(255, 245, 245)
        Note over Admin: Admin updates agent configuration
        Admin->>API: PUT /agent/agents/{agent_id}
        Note over Admin,API: {webhook_url: "new-url", capabilities: [...]}
        
        API->>AMS: update_agent(agent_id, updates, tenant_id)
        AMS->>AMS: Validate configuration changes
        AMS->>DB: Update agent record
        AMS->>SSM: Update webhook configuration
        AMS->>N8N: Test new webhook (if changed)
        AMS->>CW: Log configuration change
        AMS-->>API: Agent updated successfully
        API-->>Admin: 200 OK {updated_fields: [...]}
    end
    
    Note over Admin,CW: Agent lifecycle management completed