sequenceDiagram
    participant M<PERSON> as 🎯 Message Orchestrator
    participant API as 🌐 Agent Service API
    participant PMH as 📨 Process Message Handler
    participant HR as 🔀 Hybrid Router
    participant AA as 📊 Agent Analytics
    participant DB as 🗄️ DynamoDB
    participant N8N as 🔗 N8N Integration Service
    participant SSM as 📋 SSM Parameter Store
    participant Feedo as 🚛 N8N Feedo Workflow
    participant Forecaster as 📈 N8N Forecaster Workflow
    participant WH as 🔗 Webhook Handler
    participant CS as 💬 Chat Service
    participant CW as 📊 CloudWatch

    Note over MO: User message: "I need help with inventory forecasting for Q4"
    MO->>API: POST /agent/messages/process
    Note over MO,API: {message: {...}, conversation: {agent_type: "auto"}}
    
    API->>PMH: Route to Process Message Handler
    PMH->>PMH: Validate message structure
    PMH->>PMH: Extract content and context
    
    Note over PMH: Determine which agent should handle this
    PMH->>HR: determine_agent_routing_strategy(content, context)
    HR->>HR: Analyze message content: "inventory forecasting"
    HR->>AA: get_agent_recommendations(keywords, context)
    
    AA->>DB: Query agent performance metrics
    Note over DB: Query GSI2: AgentTypeIndex
    DB-->>AA: {forecaster: {success_rate: 0.95, avg_response: 2.1s}}
    AA-->>HR: Recommend 'forecaster' (confidence: 0.95)
    
    HR->>DB: Check forecaster agent availability
    DB-->>HR: Forecaster: available, 3/10 concurrent conversations
    HR-->>PMH: Route to 'forecaster' agent
    
    Note over PMH: Send message to Forecaster agent
    PMH->>N8N: route_message_to_agent(message, "forecaster")
    N8N->>SSM: Get forecaster webhook URL
    SSM-->>N8N: https://n8n.example.com/webhook/forecaster
    
    N8N->>N8N: Format message for N8N workflow
    N8N->>Forecaster: POST webhook with formatted message
    Note over N8N,Forecaster: {user_message: "inventory forecasting Q4", context: {...}}
    Forecaster-->>N8N: 200 OK (processing started)
    
    N8N->>DB: Store agent interaction record
    Note over DB: PK: AGENT_MESSAGE#{message_id}<br/>agent_id: forecaster<br/>status: processing
    
    N8N-->>PMH: Agent processing initiated
    PMH->>CW: Log routing decision and metrics
    PMH-->>API: 200 OK {status: "processing", agent: "forecaster"}
    API-->>MO: Success response
    
    Note over Forecaster: N8N workflow processes forecasting request
    Note over Forecaster: Generates inventory forecast analysis
    
    rect rgb(245, 255, 245)
        Note over Forecaster: Forecaster completes analysis (async)
        Forecaster->>API: POST /agent/webhooks/forecaster
        Note over Forecaster,API: {response: "Based on Q3 data...", confidence: 0.92}
        
        API->>WH: Route to Webhook Handler
        WH->>WH: Validate webhook signature
        WH->>WH: Format agent response for chat
        
        WH->>DB: Update agent interaction status
        Note over DB: Update status: completed<br/>response_time: 3.2s
        
        WH->>CS: POST /chat/agent-responses
        Note over WH,CS: {agent_response: {...}, conversation_context: {...}}
        CS-->>WH: Response delivered to chat
        
        WH->>CW: Log agent response metrics
        WH-->>API: 200 OK (webhook processed)
        API-->>Forecaster: Webhook acknowledged
    end
    
    rect rgb(255, 245, 245)
        Note over PMH: Alternative: If agent timeout or error
        Note over PMH: Timeout after 30 seconds
        PMH->>HR: escalate_to_human_agent(conversation_id, "timeout")
        HR->>DB: Create escalation record
        HR->>CS: Notify human agent assignment
        Note over HR: Human agent takes over conversation
    end
    
    Note over MO,CW: Agent routing and processing completed