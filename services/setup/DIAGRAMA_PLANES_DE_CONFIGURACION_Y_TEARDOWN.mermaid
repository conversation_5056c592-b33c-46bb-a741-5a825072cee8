graph TD
    %% Plan Configuration
    subgraph "📋 Plan Configurations"
        PlanStarter[🥉 Plan Starter<br/>- 5 users max<br/>- 2 agents max<br/>- 10GB storage<br/>- 10K API calls/month<br/>- 8 AWS resources]
        PlanPro[🥈 Plan Professional<br/>- 25 users max<br/>- 10 agents max<br/>- 100GB storage<br/>- 100K API calls/month<br/>- 15 AWS resources]
        PlanEnterprise[🥇 Plan Enterprise<br/>- 100 users max<br/>- 50 agents max<br/>- 1TB storage<br/>- 1M API calls/month<br/>- 25 AWS resources]
    end
    
    %% Resource Mapping
    subgraph "🏗️ Resource Mapping by Plan"
        StarterResources[📦 Starter Resources<br/>- 3 DynamoDB tables<br/>- 2 S3 buckets<br/>- 2 IAM roles<br/>- 5 CloudWatch alarms]
        ProResources[📦 Pro Resources<br/>- 5 DynamoDB tables<br/>- 4 S3 buckets<br/>- 4 IAM roles<br/>- 15 CloudWatch alarms]
        EnterpriseResources[📦 Enterprise Resources<br/>- 8 DynamoDB tables<br/>- 6 S3 buckets<br/>- 6 IAM roles<br/>- 25 CloudWatch alarms]
    end
    
    %% CloudFormation Templates
    subgraph "☁️ CloudFormation Templates"
        StarterTemplate[📄 starter-template.yaml<br/>Basic infrastructure<br/>Cost-optimized]
        ProTemplate[📄 pro-template.yaml<br/>Advanced features<br/>Performance-optimized]
        EnterpriseTemplate[📄 enterprise-template.yaml<br/>Full features<br/>Enterprise-grade]
    end
    
    %% Teardown Process
    subgraph "🗑️ Teardown Process"
        TeardownRequest[🚨 Teardown Request<br/>Admin initiated]
        TeardownValidation[✅ Validation<br/>- Admin permissions<br/>- Tenant exists<br/>- Active resources]
        TeardownOptions[⚙️ Teardown Options<br/>- Preserve data: Yes/No<br/>- Force delete: Yes/No<br/>- Backup first: Yes/No]
        
        subgraph "🔄 Teardown Execution"
            PreservePath[💾 Preserve Data Path<br/>- Retain DynamoDB tables<br/>- Retain S3 buckets<br/>- Delete compute resources]
            CompletePath[🗑️ Complete Teardown<br/>- Delete all resources<br/>- Clean CloudFormation<br/>- Remove IAM roles]
        end
        
        TeardownCompletion[✅ Teardown Complete<br/>- Resources cleaned<br/>- Audit trail logged<br/>- Notification sent]
    end
    
    %% Resource Lifecycle
    subgraph "🔄 Resource Lifecycle"
        ResourceCreation[🛠️ Resource Creation<br/>CloudFormation Stack]
        ResourceMonitoring[📊 Resource Monitoring<br/>CloudWatch Metrics]
        ResourceOptimization[⚡ Resource Optimization<br/>Cost & Performance]
        ResourceTeardown[🗑️ Resource Teardown<br/>Clean Deletion]
    end
    
    %% Plan Connections
    PlanStarter --> StarterResources
    PlanPro --> ProResources
    PlanEnterprise --> EnterpriseResources
    
    %% Template Connections
    StarterResources --> StarterTemplate
    ProResources --> ProTemplate
    EnterpriseResources --> EnterpriseTemplate
    
    %% Teardown Flow
    TeardownRequest --> TeardownValidation
    TeardownValidation --> TeardownOptions
    TeardownOptions --> PreservePath
    TeardownOptions --> CompletePath
    PreservePath --> TeardownCompletion
    CompletePath --> TeardownCompletion
    
    %% Lifecycle Flow
    ResourceCreation --> ResourceMonitoring
    ResourceMonitoring --> ResourceOptimization
    ResourceOptimization --> ResourceTeardown
    
    %% Cross-connections
    StarterTemplate --> ResourceCreation
    ProTemplate --> ResourceCreation
    EnterpriseTemplate --> ResourceCreation
    
    ResourceTeardown --> TeardownRequest
    
    %% Styling
    classDef plan fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef resource fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef template fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef teardown fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef lifecycle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    
    class PlanStarter,PlanPro,PlanEnterprise plan
    class StarterResources,ProResources,EnterpriseResources resource
    class StarterTemplate,ProTemplate,EnterpriseTemplate template
    class TeardownRequest,TeardownValidation,TeardownOptions,PreservePath,CompletePath,TeardownCompletion teardown
    class ResourceCreation,ResourceMonitoring,ResourceOptimization,ResourceTeardown lifecycle