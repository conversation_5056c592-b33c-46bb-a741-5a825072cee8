graph TB
    %% External Systems
    OrchestratorService[🎯 Orchestrator Service<br/>Registration Completion]
    AdminUser[👨‍💼 Admin User<br/>Tenant Management]
    CloudFormation[☁️ AWS CloudFormation<br/>Infrastructure as Code]
    
    %% API Gateway
    APIGateway[🌐 API Gateway<br/>REST API]
    
    %% Lambda Functions - Setup Management
    TenantSetupHandler[🛠️ Tenant Setup Handler<br/>POST /setup/tenant/{id}]
    TenantTeardownHandler[🗑️ Tenant Teardown Handler<br/>DELETE /setup/tenant/{id}]
    SetupStatusHandler[📊 Setup Status Handler<br/>GET /setup/status/{id}]
    HealthCheckHandler[🏥 Health Check Handler<br/>GET /setup/health]
    
    %% Lambda Functions - Maintenance
    CleanupFailedStacksHandler[🧹 Cleanup Failed Stacks<br/>Scheduled Cleanup]
    MonitorSetupJobsHandler[📊 Monitor Setup Jobs<br/>Scheduled Monitoring]
    
    %% Core Services
    SetupService[🛠️ Setup Service<br/>Main Setup Orchestration]
    CloudFormationService[☁️ CloudFormation Service<br/>Stack Management]
    JobManager[📋 Job Manager<br/>Async Job Tracking]
    PlanConfigManager[📋 Plan Configuration Manager<br/>Plan-based Resource Config]
    ResourceTemplateGenerator[📄 Resource Template Generator<br/>Dynamic CF Templates]
    ResourceMonitor[🔍 Resource Monitor<br/>Resource State Monitoring]
    NotificationService[📧 Notification Service<br/>Progress Notifications]
    SecurityManager[🔐 Security Manager<br/>IAM & Security Config]
    
    %% AWS Services
    DynamoDB[(🗄️ DynamoDB<br/>agent-scl-dev)]
    S3Templates[📦 S3 Bucket<br/>CF Templates Storage]
    IAMService[🔐 AWS IAM<br/>Roles & Policies]
    CloudWatchService[📊 AWS CloudWatch<br/>Monitoring & Alarms]
    
    %% Tenant Resources (Created by CF)
    TenantDynamoDB[(🗄️ Tenant DynamoDB<br/>Per-Tenant Tables)]
    TenantS3[📦 Tenant S3 Buckets<br/>Per-Tenant Storage]
    TenantIAM[🔐 Tenant IAM Roles<br/>Per-Tenant Security]
    TenantCloudWatch[📊 Tenant CloudWatch<br/>Per-Tenant Monitoring]
    
    %% External Connections
    OrchestratorService -->|Setup Request| APIGateway
    AdminUser -->|Teardown Request| APIGateway
    
    %% API Gateway Routing
    APIGateway --> TenantSetupHandler
    APIGateway --> TenantTeardownHandler
    APIGateway --> SetupStatusHandler
    APIGateway --> HealthCheckHandler
    
    %% Scheduled Functions
    CleanupFailedStacksHandler --> SetupService
    MonitorSetupJobsHandler --> JobManager
    
    %% Handler Dependencies - Setup Management
    TenantSetupHandler --> SetupService
    TenantSetupHandler --> JobManager
    TenantSetupHandler --> PlanConfigManager
    
    TenantTeardownHandler --> SetupService
    TenantTeardownHandler --> JobManager
    TenantTeardownHandler --> SecurityManager
    
    SetupStatusHandler --> JobManager
    SetupStatusHandler --> CloudFormationService
    
    HealthCheckHandler --> SetupService
    HealthCheckHandler --> CloudFormationService
    
    %% Service Dependencies - Core
    SetupService --> JobManager
    SetupService --> CloudFormationService
    SetupService --> PlanConfigManager
    SetupService --> ResourceTemplateGenerator
    SetupService --> NotificationService
    
    CloudFormationService --> CloudFormation
    CloudFormationService --> S3Templates
    CloudFormationService --> ResourceMonitor
    
    JobManager --> DynamoDB
    JobManager --> NotificationService
    
    PlanConfigManager --> ResourceTemplateGenerator
    ResourceTemplateGenerator --> S3Templates
    
    ResourceMonitor --> CloudWatchService
    SecurityManager --> IAMService
    
    %% CloudFormation Resource Creation
    CloudFormation --> TenantDynamoDB
    CloudFormation --> TenantS3
    CloudFormation --> TenantIAM
    CloudFormation --> TenantCloudWatch
    
    %% Monitoring and Logging
    SetupService --> CloudWatchService
    JobManager --> CloudWatchService
    CloudFormationService --> CloudWatchService
    
    %% Storage Dependencies
    SetupService --> DynamoDB
    JobManager --> DynamoDB
    ResourceTemplateGenerator --> S3Templates
    
    %% Security Integration
    SecurityManager --> IAMService
    SetupService --> SecurityManager
    CloudFormationService --> SecurityManager
    
    %% Styling
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handler fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef aws fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef tenant fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef cf fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class OrchestratorService,AdminUser external
    class APIGateway gateway
    class TenantSetupHandler,TenantTeardownHandler,SetupStatusHandler,HealthCheckHandler,CleanupFailedStacksHandler,MonitorSetupJobsHandler handler
    class SetupService,CloudFormationService,JobManager,PlanConfigManager,ResourceTemplateGenerator,ResourceMonitor,NotificationService,SecurityManager service
    class IAMService,CloudWatchService aws
    class DynamoDB,S3Templates storage
    class TenantDynamoDB,TenantS3,TenantIAM,TenantCloudWatch tenant
    class CloudFormation cf