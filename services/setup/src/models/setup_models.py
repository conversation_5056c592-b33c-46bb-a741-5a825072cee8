#!/usr/bin/env python3
# services/setup/src/models/setup_models.py
# Setup data models

"""
Setup data models for the tenant resource configuration service.
Defines the data structures used throughout the setup process.
"""

import os
from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class SetupJobStatus(Enum):
    """Setup job status enumeration."""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class ResourceType(Enum):
    """Resource type enumeration."""
    DYNAMODB_TABLE = "DYNAMODB_TABLE"
    S3_BUCKET = "S3_BUCKET"
    IAM_ROLE = "IAM_ROLE"
    IAM_POLICY = "IAM_POLICY"
    CLOUDWATCH_ALARM = "CLOUDWATCH_ALARM"
    CLOUDFORMATION_STACK = "CLOUDFORMATION_STACK"


@dataclass
class TenantPlan:
    """Tenant plan configuration."""
    plan_id: str
    plan_name: str
    max_users: int
    max_agents: int
    max_storage_gb: int
    max_api_calls_per_month: int
    features: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class ResourceConfig:
    """Configuration for a specific resource."""
    resource_type: ResourceType
    resource_name: str
    configuration: Dict[str, Any]
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'resource_type': self.resource_type.value,
            'resource_name': self.resource_name,
            'configuration': self.configuration,
            'dependencies': self.dependencies
        }


@dataclass
class SetupJob:
    """Setup job tracking record."""
    tenant_id: str
    job_id: str
    status: SetupJobStatus
    created_at: datetime
    updated_at: datetime
    
    # Job configuration
    plan_config: TenantPlan
    resources: List[ResourceConfig]
    
    # Progress tracking
    total_resources: int = 0
    completed_resources: int = 0
    failed_resources: int = 0
    
    # CloudFormation details
    stack_name: Optional[str] = None
    stack_id: Optional[str] = None
    stack_status: Optional[str] = None
    
    # Error handling
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Audit trail
    execution_log: Optional[List[Dict[str, Any]]] = None
    
    def __post_init__(self):
        if self.execution_log is None:
            self.execution_log = []
        if self.total_resources == 0:
            self.total_resources = len(self.resources)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for DynamoDB storage."""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['status'] = self.status.value
        
        # Convert plan config
        data['plan_config'] = self.plan_config.to_dict()
        
        # Convert resources
        data['resources'] = [resource.to_dict() for resource in self.resources]
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SetupJob':
        """Create from dictionary (DynamoDB response)."""
        # Convert ISO strings back to datetime objects
        created_at = datetime.fromisoformat(data['created_at'])
        updated_at = datetime.fromisoformat(data['updated_at'])
        status = SetupJobStatus(data['status'])
        
        # Convert plan config
        plan_config = TenantPlan(**data['plan_config'])
        
        # Convert resources
        resources = []
        for resource_data in data['resources']:
            resource = ResourceConfig(
                resource_type=ResourceType(resource_data['resource_type']),
                resource_name=resource_data['resource_name'],
                configuration=resource_data['configuration'],
                dependencies=resource_data.get('dependencies', [])
            )
            resources.append(resource)
        
        return cls(
            tenant_id=data['tenant_id'],
            job_id=data['job_id'],
            status=status,
            created_at=created_at,
            updated_at=updated_at,
            plan_config=plan_config,
            resources=resources,
            total_resources=data.get('total_resources', len(resources)),
            completed_resources=data.get('completed_resources', 0),
            failed_resources=data.get('failed_resources', 0),
            stack_name=data.get('stack_name'),
            stack_id=data.get('stack_id'),
            stack_status=data.get('stack_status'),
            error_message=data.get('error_message'),
            error_details=data.get('error_details'),
            execution_log=data.get('execution_log', [])
        )
    
    def add_log_entry(self, action: str, status: str, details: Optional[Dict[str, Any]] = None) -> None:
        """Add entry to execution log."""
        if self.execution_log is None:
            self.execution_log = []
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'action': action,
            'status': status,
            'details': details or {}
        }
        self.execution_log.append(log_entry)
    
    def get_progress_percentage(self) -> float:
        """Get setup progress as percentage."""
        if self.total_resources == 0:
            return 0.0
        return (self.completed_resources / self.total_resources) * 100.0


@dataclass
class SetupRequest:
    """Setup request data."""
    tenant_id: str
    plan_id: str
    configuration: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class TeardownRequest:
    """Teardown request data."""
    tenant_id: str
    force: bool = False
    preserve_data: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


# Plan configurations
PLAN_CONFIGURATIONS = {
    'plan_starter': TenantPlan(
        plan_id='plan_starter',
        plan_name='Starter',
        max_users=5,
        max_agents=2,
        max_storage_gb=10,
        max_api_calls_per_month=10000,
        features=['basic_analytics', 'email_support']
    ),
    'plan_pro': TenantPlan(
        plan_id='plan_pro',
        plan_name='Professional',
        max_users=25,
        max_agents=5,
        max_storage_gb=100,
        max_api_calls_per_month=100000,
        features=['advanced_analytics', 'priority_support', 'custom_integrations']
    ),
    'plan_enterprise': TenantPlan(
        plan_id='plan_enterprise',
        plan_name='Enterprise',
        max_users=100,
        max_agents=20,
        max_storage_gb=1000,
        max_api_calls_per_month=1000000,
        features=['enterprise_analytics', 'dedicated_support', 'custom_integrations', 'sla_guarantee']
    )
}


def get_plan_config(plan_id: str) -> Optional[TenantPlan]:
    """Get plan configuration by ID."""
    return PLAN_CONFIGURATIONS.get(plan_id)


def generate_resource_configs(tenant_id: str, plan_config: TenantPlan) -> List[ResourceConfig]:
    """Generate resource configurations for a tenant based on plan."""
    resources = []
    
    # DynamoDB tables
    tables = [
        {
            'name': f"{tenant_id}-data",
            'config': {
                'BillingMode': 'PAY_PER_REQUEST',
                'AttributeDefinitions': [
                    {'AttributeName': 'pk', 'AttributeType': 'S'},
                    {'AttributeName': 'sk', 'AttributeType': 'S'}
                ],
                'KeySchema': [
                    {'AttributeName': 'pk', 'KeyType': 'HASH'},
                    {'AttributeName': 'sk', 'KeyType': 'RANGE'}
                ],
                'StreamSpecification': {'StreamViewType': 'NEW_AND_OLD_IMAGES'},
                'PointInTimeRecoverySpecification': {'PointInTimeRecoveryEnabled': True},
                'SSESpecification': {'SSEEnabled': True}
            }
        },
        {
            'name': f"{tenant_id}-conversations",
            'config': {
                'BillingMode': 'PAY_PER_REQUEST',
                'AttributeDefinitions': [
                    {'AttributeName': 'conversation_id', 'AttributeType': 'S'},
                    {'AttributeName': 'timestamp', 'AttributeType': 'S'}
                ],
                'KeySchema': [
                    {'AttributeName': 'conversation_id', 'KeyType': 'HASH'},
                    {'AttributeName': 'timestamp', 'KeyType': 'RANGE'}
                ],
                'TimeToLiveSpecification': {'AttributeName': 'ttl', 'Enabled': True}
            }
        },
        {
            'name': f"{tenant_id}-analytics",
            'config': {
                'BillingMode': 'PAY_PER_REQUEST',
                'AttributeDefinitions': [
                    {'AttributeName': 'metric_type', 'AttributeType': 'S'},
                    {'AttributeName': 'timestamp', 'AttributeType': 'S'}
                ],
                'KeySchema': [
                    {'AttributeName': 'metric_type', 'KeyType': 'HASH'},
                    {'AttributeName': 'timestamp', 'KeyType': 'RANGE'}
                ]
            }
        }
    ]
    
    for table in tables:
        resources.append(ResourceConfig(
            resource_type=ResourceType.DYNAMODB_TABLE,
            resource_name=table['name'],
            configuration=table['config']
        ))
    
    # S3 buckets
    buckets = [
        {
            'name': f"{tenant_id}-data-{os.environ.get('REGION', 'us-east-1')}",
            'config': {
                'VersioningConfiguration': {'Status': 'Enabled'},
                'BucketEncryption': {
                    'ServerSideEncryptionConfiguration': [{
                        'ServerSideEncryptionByDefault': {'SSEAlgorithm': 'AES256'}
                    }]
                },
                'LifecycleConfiguration': {
                    'Rules': [{
                        'Id': 'DeleteOldVersions',
                        'Status': 'Enabled',
                        'NoncurrentVersionExpirationInDays': 30
                    }]
                }
            }
        },
        {
            'name': f"{tenant_id}-processed-{os.environ.get('REGION', 'us-east-1')}",
            'config': {
                'BucketEncryption': {
                    'ServerSideEncryptionConfiguration': [{
                        'ServerSideEncryptionByDefault': {'SSEAlgorithm': 'AES256'}
                    }]
                }
            }
        }
    ]
    
    # Add reports bucket for Pro and Enterprise plans
    if plan_config.plan_id in ['plan_pro', 'plan_enterprise']:
        buckets.append({
            'name': f"{tenant_id}-reports-{os.environ.get('REGION', 'us-east-1')}",
            'config': {
                'BucketEncryption': {
                    'ServerSideEncryptionConfiguration': [{
                        'ServerSideEncryptionByDefault': {'SSEAlgorithm': 'AES256'}
                    }]
                }
            }
        })
    
    for bucket in buckets:
        resources.append(ResourceConfig(
            resource_type=ResourceType.S3_BUCKET,
            resource_name=bucket['name'],
            configuration=bucket['config']
        ))
    
    # IAM role for tenant
    role_config = {
        'AssumeRolePolicyDocument': {
            'Version': '2012-10-17',
            'Statement': [{
                'Effect': 'Allow',
                'Principal': {'Service': 'lambda.amazonaws.com'},
                'Action': 'sts:AssumeRole'
            }]
        },
        'Policies': [
            {
                'PolicyName': f"{tenant_id}-data-access",
                'PolicyDocument': {
                    'Version': '2012-10-17',
                    'Statement': [
                        {
                            'Effect': 'Allow',
                            'Action': [
                                'dynamodb:GetItem',
                                'dynamodb:PutItem',
                                'dynamodb:UpdateItem',
                                'dynamodb:DeleteItem',
                                'dynamodb:Query',
                                'dynamodb:Scan'
                            ],
                            'Resource': [
                                f"arn:aws:dynamodb:*:*:table/{tenant_id}-*"
                            ]
                        },
                        {
                            'Effect': 'Allow',
                            'Action': [
                                's3:GetObject',
                                's3:PutObject',
                                's3:DeleteObject',
                                's3:ListBucket'
                            ],
                            'Resource': [
                                f"arn:aws:s3:::{tenant_id}-*",
                                f"arn:aws:s3:::{tenant_id}-*/*"
                            ]
                        }
                    ]
                }
            }
        ]
    }
    
    resources.append(ResourceConfig(
        resource_type=ResourceType.IAM_ROLE,
        resource_name=f"tenant-{tenant_id}-role",
        configuration=role_config
    ))
    
    return resources
