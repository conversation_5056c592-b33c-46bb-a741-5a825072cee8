#!/usr/bin/env python3
# services/setup/src/services/setup_service.py
# Main setup service for tenant resource configuration

"""
Main setup service that handles tenant resource configuration.
Manages CloudFormation stacks, DynamoDB tables, S3 buckets, and IAM roles.
"""

import os
import uuid
import boto3
from datetime import datetime
from typing import Dict, Any, Optional, List

from shared.logger import lambda_logger, audit_log
from shared.exceptions import ValidationException, ExternalServiceException, ResourceNotFoundException
from ..models.setup_models import (
    SetupJob, SetupJobStatus, SetupRequest, TeardownRequest,
    get_plan_config, generate_resource_configs
)
from .job_manager import SetupJobManager
from .cloudformation_service import CloudFormationService


class SetupService:
    """Main setup service for tenant resource configuration."""
    
    def __init__(self):
        self.job_manager = SetupJobManager()
        self.cf_service = CloudFormationService()
        self.region = os.environ.get('REGION', 'us-east-1')
        self.project_name = os.environ.get('PROJECT_NAME', 'agent-scl')
        self.stage = os.environ.get('STAGE', 'dev')
    
    def setup_tenant(self, request: SetupRequest) -> SetupJob:
        """
        Setup resources for a new tenant.
        
        Args:
            request: Setup request with tenant and plan information
            
        Returns:
            SetupJob with tracking information
        """
        try:
            tenant_id = request.tenant_id
            plan_id = request.plan_id
            
            lambda_logger.info("Starting tenant setup", extra={
                'tenant_id': tenant_id,
                'plan_id': plan_id
            })
            
            # Validate plan
            plan_config = get_plan_config(plan_id)
            if not plan_config:
                raise ValidationException(f"Invalid plan ID: {plan_id}")
            
            # Check if tenant already has setup in progress
            existing_job = self.job_manager.get_active_job(tenant_id)
            if existing_job:
                lambda_logger.warning("Tenant setup already in progress", extra={
                    'tenant_id': tenant_id,
                    'existing_job_id': existing_job.job_id,
                    'status': existing_job.status.value
                })
                return existing_job
            
            # Generate resource configurations
            resources = generate_resource_configs(tenant_id, plan_config)
            
            # Create setup job
            job_id = f"setup_{uuid.uuid4().hex[:12]}"
            setup_job = SetupJob(
                tenant_id=tenant_id,
                job_id=job_id,
                status=SetupJobStatus.PENDING,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                plan_config=plan_config,
                resources=resources
            )
            
            # Save job
            self.job_manager.save_job(setup_job)
            
            # Start setup process
            setup_job = self._execute_setup(setup_job)
            
            audit_log("tenant_setup_started", {
                'tenant_id': tenant_id,
                'job_id': job_id,
                'plan_id': plan_id,
                'total_resources': len(resources)
            })
            
            lambda_logger.info("Tenant setup initiated", extra={
                'tenant_id': tenant_id,
                'job_id': job_id,
                'status': setup_job.status.value,
                'total_resources': len(resources)
            })
            
            return setup_job
            
        except Exception as e:
            lambda_logger.error("Failed to setup tenant", extra={
                'tenant_id': request.tenant_id,
                'plan_id': request.plan_id,
                'error': str(e)
            })
            raise
    
    def teardown_tenant(self, request: TeardownRequest) -> Dict[str, Any]:
        """
        Teardown resources for a tenant.
        
        Args:
            request: Teardown request with tenant information
            
        Returns:
            Dict with teardown status
        """
        try:
            tenant_id = request.tenant_id
            
            lambda_logger.info("Starting tenant teardown", extra={
                'tenant_id': tenant_id,
                'force': request.force,
                'preserve_data': request.preserve_data
            })
            
            # Get existing setup job
            setup_job = self.job_manager.get_latest_job(tenant_id)
            if not setup_job:
                raise ResourceNotFoundException(f"No setup found for tenant: {tenant_id}")
            
            # Create teardown job
            job_id = f"teardown_{uuid.uuid4().hex[:12]}"
            teardown_job = SetupJob(
                tenant_id=tenant_id,
                job_id=job_id,
                status=SetupJobStatus.PENDING,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                plan_config=setup_job.plan_config,
                resources=setup_job.resources
            )
            
            # Save teardown job
            self.job_manager.save_job(teardown_job)
            
            # Execute teardown
            result = self._execute_teardown(teardown_job, request)
            
            audit_log("tenant_teardown_started", {
                'tenant_id': tenant_id,
                'job_id': job_id,
                'force': request.force,
                'preserve_data': request.preserve_data
            })
            
            lambda_logger.info("Tenant teardown initiated", extra={
                'tenant_id': tenant_id,
                'job_id': job_id,
                'result': result
            })
            
            return result
            
        except Exception as e:
            lambda_logger.error("Failed to teardown tenant", extra={
                'tenant_id': request.tenant_id,
                'error': str(e)
            })
            raise
    
    def get_setup_status(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get setup status for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Dict with setup status information
        """
        try:
            # Get latest job
            job = self.job_manager.get_latest_job(tenant_id)
            if not job:
                return {
                    'tenant_id': tenant_id,
                    'status': 'NOT_FOUND',
                    'message': 'No setup job found for tenant'
                }
            
            # Get CloudFormation stack status if available
            stack_status = None
            if job.stack_name:
                try:
                    stack_status = self.cf_service.get_stack_status(job.stack_name)
                except:
                    pass  # Stack might not exist yet
            
            return {
                'tenant_id': tenant_id,
                'job_id': job.job_id,
                'status': job.status.value,
                'progress_percentage': job.get_progress_percentage(),
                'total_resources': job.total_resources,
                'completed_resources': job.completed_resources,
                'failed_resources': job.failed_resources,
                'created_at': job.created_at.isoformat(),
                'updated_at': job.updated_at.isoformat(),
                'stack_name': job.stack_name,
                'stack_status': stack_status,
                'error_message': job.error_message,
                'execution_log': job.execution_log[-10:] if job.execution_log else []  # Last 10 entries
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get setup status", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    def _execute_setup(self, setup_job: SetupJob) -> SetupJob:
        """Execute the setup process."""
        try:
            # Update status to in progress
            setup_job.status = SetupJobStatus.IN_PROGRESS
            setup_job.updated_at = datetime.utcnow()
            setup_job.add_log_entry("setup_started", "success", {
                'total_resources': setup_job.total_resources
            })
            self.job_manager.save_job(setup_job)
            
            # Generate CloudFormation template
            template = self.cf_service.generate_template(
                setup_job.tenant_id,
                setup_job.plan_config,
                setup_job.resources
            )
            
            # Create CloudFormation stack
            stack_name = f"tenant-{setup_job.tenant_id}-{self.stage}"
            setup_job.stack_name = stack_name
            
            stack_result = self.cf_service.create_stack(
                stack_name,
                template,
                {
                    'TenantId': setup_job.tenant_id,
                    'PlanId': setup_job.plan_config.plan_id,
                    'Environment': self.stage
                }
            )
            
            setup_job.stack_id = stack_result.get('StackId')
            setup_job.add_log_entry("cloudformation_stack_created", "success", {
                'stack_name': stack_name,
                'stack_id': setup_job.stack_id
            })
            
            # For now, mark as completed (in real implementation, would monitor stack creation)
            setup_job.status = SetupJobStatus.COMPLETED
            setup_job.completed_resources = setup_job.total_resources
            setup_job.updated_at = datetime.utcnow()
            setup_job.add_log_entry("setup_completed", "success", {
                'completed_resources': setup_job.completed_resources
            })
            
            self.job_manager.save_job(setup_job)
            
            return setup_job
            
        except Exception as e:
            # Mark as failed
            setup_job.status = SetupJobStatus.FAILED
            setup_job.error_message = str(e)
            setup_job.updated_at = datetime.utcnow()
            setup_job.add_log_entry("setup_failed", "error", {
                'error': str(e)
            })
            self.job_manager.save_job(setup_job)
            
            lambda_logger.error("Setup execution failed", extra={
                'tenant_id': setup_job.tenant_id,
                'job_id': setup_job.job_id,
                'error': str(e)
            })
            raise
    
    def _execute_teardown(self, teardown_job: SetupJob, request: TeardownRequest) -> Dict[str, Any]:
        """Execute the teardown process."""
        try:
            # Update status to in progress
            teardown_job.status = SetupJobStatus.IN_PROGRESS
            teardown_job.updated_at = datetime.utcnow()
            teardown_job.add_log_entry("teardown_started", "success", {
                'force': request.force,
                'preserve_data': request.preserve_data
            })
            self.job_manager.save_job(teardown_job)
            
            # Delete CloudFormation stack if exists
            if teardown_job.stack_name:
                try:
                    self.cf_service.delete_stack(teardown_job.stack_name)
                    teardown_job.add_log_entry("cloudformation_stack_deleted", "success", {
                        'stack_name': teardown_job.stack_name
                    })
                except Exception as e:
                    teardown_job.add_log_entry("cloudformation_stack_deletion_failed", "warning", {
                        'stack_name': teardown_job.stack_name,
                        'error': str(e)
                    })
            
            # Mark as completed
            teardown_job.status = SetupJobStatus.COMPLETED
            teardown_job.completed_resources = teardown_job.total_resources
            teardown_job.updated_at = datetime.utcnow()
            teardown_job.add_log_entry("teardown_completed", "success", {
                'completed_resources': teardown_job.completed_resources
            })
            
            self.job_manager.save_job(teardown_job)
            
            return {
                'success': True,
                'job_id': teardown_job.job_id,
                'message': 'Tenant teardown completed successfully',
                'resources_removed': teardown_job.total_resources
            }
            
        except Exception as e:
            # Mark as failed
            teardown_job.status = SetupJobStatus.FAILED
            teardown_job.error_message = str(e)
            teardown_job.updated_at = datetime.utcnow()
            teardown_job.add_log_entry("teardown_failed", "error", {
                'error': str(e)
            })
            self.job_manager.save_job(teardown_job)
            
            lambda_logger.error("Teardown execution failed", extra={
                'tenant_id': teardown_job.tenant_id,
                'job_id': teardown_job.job_id,
                'error': str(e)
            })
            raise
