#!/usr/bin/env python3
# services/setup/src/services/cloudformation_service.py
# CloudFormation service for managing tenant resources

"""
CloudFormation service for creating and managing tenant resource stacks.
Handles template generation and stack lifecycle management.
"""

import os
import json
import boto3
from typing import Dict, Any, List, Optional
from botocore.exceptions import ClientError

from shared.logger import lambda_logger
from shared.exceptions import ExternalServiceException, ValidationException
from ..models.setup_models import TenantPlan, ResourceConfig, ResourceType


class CloudFormationService:
    """Service for managing CloudFormation stacks."""
    
    def __init__(self):
        self.cf_client = boto3.client('cloudformation')
        self.s3_client = boto3.client('s3')
        self.region = os.environ.get('REGION', 'us-east-1')
        self.templates_bucket = os.environ.get('TEMPLATES_BUCKET')
    
    def generate_template(self, tenant_id: str, plan_config: TenantPlan,
                         resources: List[ResourceConfig]) -> Dict[str, Any]:
        """
        Generate CloudFormation template for tenant resources.
        
        Args:
            tenant_id: Tenant identifier
            plan_config: Tenant plan configuration
            resources: List of resources to create
            
        Returns:
            CloudFormation template as dictionary
        """
        try:
            template = {
                "AWSTemplateFormatVersion": "2010-09-09",
                "Description": f"Resources for tenant {tenant_id} ({plan_config.plan_name} plan)",
                "Parameters": {
                    "TenantId": {
                        "Type": "String",
                        "Description": "Tenant identifier",
                        "Default": tenant_id
                    },
                    "PlanId": {
                        "Type": "String",
                        "Description": "Plan identifier",
                        "Default": plan_config.plan_id
                    },
                    "Environment": {
                        "Type": "String",
                        "Description": "Environment (dev, staging, prod)",
                        "Default": os.environ.get('STAGE', 'dev')
                    }
                },
                "Resources": {},
                "Outputs": {}
            }
            
            # Generate resources
            for resource in resources:
                cf_resource = self._generate_cf_resource(resource, tenant_id)
                if cf_resource:
                    resource_name = self._sanitize_resource_name(resource.resource_name)
                    template["Resources"][resource_name] = cf_resource
                    
                    # Add output for resource
                    template["Outputs"][f"{resource_name}Name"] = {
                        "Description": f"Name of {resource.resource_type.value}",
                        "Value": {"Ref": resource_name},
                        "Export": {
                            "Name": f"{tenant_id}-{resource.resource_type.value.lower()}-{resource_name.lower()}"
                        }
                    }
            
            lambda_logger.info("CloudFormation template generated", extra={
                'tenant_id': tenant_id,
                'plan_id': plan_config.plan_id,
                'resource_count': len(template["Resources"])
            })
            
            return template
            
        except Exception as e:
            lambda_logger.error("Failed to generate CloudFormation template", extra={
                'tenant_id': tenant_id,
                'plan_id': plan_config.plan_id,
                'error': str(e)
            })
            raise
    
    def create_stack(self, stack_name: str, template: Dict[str, Any],
                    parameters: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Create CloudFormation stack.
        
        Args:
            stack_name: Name of the stack
            template: CloudFormation template
            parameters: Stack parameters
            
        Returns:
            Stack creation response
        """
        try:
            # Convert parameters to CloudFormation format
            cf_parameters = []
            if parameters:
                for key, value in parameters.items():
                    cf_parameters.append({
                        'ParameterKey': key,
                        'ParameterValue': value
                    })
            
            # Create stack
            response = self.cf_client.create_stack(
                StackName=stack_name,
                TemplateBody=json.dumps(template, indent=2),
                Parameters=cf_parameters,
                Capabilities=['CAPABILITY_IAM', 'CAPABILITY_NAMED_IAM'],
                Tags=[
                    {'Key': 'Service', 'Value': 'setup'},
                    {'Key': 'Environment', 'Value': os.environ.get('STAGE', 'dev')},
                    {'Key': 'TenantId', 'Value': parameters.get('TenantId', 'unknown') if parameters else 'unknown'}
                ],
                EnableTerminationProtection=False
            )
            
            lambda_logger.info("CloudFormation stack created", extra={
                'stack_name': stack_name,
                'stack_id': response['StackId']
            })
            
            return response
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            lambda_logger.error("Failed to create CloudFormation stack", extra={
                'stack_name': stack_name,
                'error_code': error_code,
                'error_message': error_message
            })
            
            if error_code == 'AlreadyExistsException':
                raise ValidationException(f"Stack {stack_name} already exists")
            else:
                raise ExternalServiceException(f"CloudFormation error: {error_message}")
    
    def delete_stack(self, stack_name: str) -> Dict[str, Any]:
        """
        Delete CloudFormation stack.
        
        Args:
            stack_name: Name of the stack to delete
            
        Returns:
            Stack deletion response
        """
        try:
            response = self.cf_client.delete_stack(StackName=stack_name)
            
            lambda_logger.info("CloudFormation stack deletion initiated", extra={
                'stack_name': stack_name
            })
            
            return response
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            lambda_logger.error("Failed to delete CloudFormation stack", extra={
                'stack_name': stack_name,
                'error_code': error_code,
                'error_message': error_message
            })
            
            if error_code == 'ValidationError' and 'does not exist' in error_message:
                lambda_logger.warning("Stack does not exist, considering deletion successful", extra={
                    'stack_name': stack_name
                })
                return {'message': 'Stack does not exist'}
            else:
                raise ExternalServiceException(f"CloudFormation error: {error_message}")
    
    def get_stack_status(self, stack_name: str) -> Optional[str]:
        """
        Get CloudFormation stack status.
        
        Args:
            stack_name: Name of the stack
            
        Returns:
            Stack status or None if not found
        """
        try:
            response = self.cf_client.describe_stacks(StackName=stack_name)
            
            if response['Stacks']:
                return response['Stacks'][0]['StackStatus']
            
            return None
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            
            if error_code == 'ValidationError':
                return None  # Stack doesn't exist
            else:
                lambda_logger.error("Failed to get stack status", extra={
                    'stack_name': stack_name,
                    'error': str(e)
                })
                raise
    
    def _generate_cf_resource(self, resource: ResourceConfig, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Generate CloudFormation resource definition."""
        try:
            if resource.resource_type == ResourceType.DYNAMODB_TABLE:
                return self._generate_dynamodb_table(resource)
            elif resource.resource_type == ResourceType.S3_BUCKET:
                return self._generate_s3_bucket(resource)
            elif resource.resource_type == ResourceType.IAM_ROLE:
                return self._generate_iam_role(resource)
            else:
                lambda_logger.warning("Unsupported resource type", extra={
                    'resource_type': resource.resource_type.value,
                    'resource_name': resource.resource_name
                })
                return None
                
        except Exception as e:
            lambda_logger.error("Failed to generate CloudFormation resource", extra={
                'resource_type': resource.resource_type.value,
                'resource_name': resource.resource_name,
                'error': str(e)
            })
            return None
    
    def _generate_dynamodb_table(self, resource: ResourceConfig) -> Dict[str, Any]:
        """Generate DynamoDB table resource."""
        cf_resource = {
            "Type": "AWS::DynamoDB::Table",
            "Properties": {
                "TableName": resource.resource_name,
                **resource.configuration
            }
        }
        
        # Add default tags
        if "Tags" not in cf_resource["Properties"]:
            cf_resource["Properties"]["Tags"] = []
        
        cf_resource["Properties"]["Tags"].extend([
            {"Key": "Service", "Value": "tenant-data"},
            {"Key": "Environment", "Value": {"Ref": "Environment"}},
            {"Key": "TenantId", "Value": {"Ref": "TenantId"}}
        ])
        
        return cf_resource
    
    def _generate_s3_bucket(self, resource: ResourceConfig) -> Dict[str, Any]:
        """Generate S3 bucket resource."""
        cf_resource = {
            "Type": "AWS::S3::Bucket",
            "Properties": {
                "BucketName": resource.resource_name,
                **resource.configuration
            }
        }
        
        # Add default tags
        if "Tags" not in cf_resource["Properties"]:
            cf_resource["Properties"]["Tags"] = []
        
        cf_resource["Properties"]["Tags"].extend([
            {"Key": "Service", "Value": "tenant-storage"},
            {"Key": "Environment", "Value": {"Ref": "Environment"}},
            {"Key": "TenantId", "Value": {"Ref": "TenantId"}}
        ])
        
        # Add public access block by default
        if "PublicAccessBlockConfiguration" not in cf_resource["Properties"]:
            cf_resource["Properties"]["PublicAccessBlockConfiguration"] = {
                "BlockPublicAcls": True,
                "BlockPublicPolicy": True,
                "IgnorePublicAcls": True,
                "RestrictPublicBuckets": True
            }
        
        return cf_resource
    
    def _generate_iam_role(self, resource: ResourceConfig) -> Dict[str, Any]:
        """Generate IAM role resource."""
        cf_resource = {
            "Type": "AWS::IAM::Role",
            "Properties": {
                "RoleName": resource.resource_name,
                **resource.configuration
            }
        }
        
        # Add default tags
        if "Tags" not in cf_resource["Properties"]:
            cf_resource["Properties"]["Tags"] = []
        
        cf_resource["Properties"]["Tags"].extend([
            {"Key": "Service", "Value": "tenant-access"},
            {"Key": "Environment", "Value": {"Ref": "Environment"}},
            {"Key": "TenantId", "Value": {"Ref": "TenantId"}}
        ])
        
        return cf_resource
    
    def _sanitize_resource_name(self, name: str) -> str:
        """Sanitize resource name for CloudFormation."""
        # Remove invalid characters and ensure it starts with a letter
        sanitized = ''.join(c for c in name if c.isalnum() or c in '-_')
        if sanitized and not sanitized[0].isalpha():
            sanitized = 'Resource' + sanitized
        return sanitized or 'Resource'
