#!/usr/bin/env python3
# services/setup/src/services/job_manager.py
# Setup job manager for DynamoDB operations

"""
Setup job manager for handling CRUD operations on setup jobs.
Manages job state and provides query capabilities.
"""

import os
import boto3
from typing import Optional, List, Dict, Any
from datetime import datetime
from botocore.exceptions import ClientError

from shared.logger import lambda_logger
from shared.exceptions import ResourceNotFoundException, ValidationException
from ..models.setup_models import SetupJob, SetupJobStatus


class SetupJobManager:
    """Manages setup jobs in DynamoDB."""
    
    def __init__(self):
        self.dynamodb = boto3.resource('dynamodb')
        self.table_name = os.environ.get('SETUP_JOBS_TABLE')
        if not self.table_name:
            raise ValueError("SETUP_JOBS_TABLE environment variable not set")
        
        self.table = self.dynamodb.Table(self.table_name)
    
    def save_job(self, job: SetupJob) -> None:
        """Save setup job to DynamoDB."""
        try:
            item = job.to_dict()
            
            lambda_logger.info("Saving setup job", extra={
                'tenant_id': job.tenant_id,
                'job_id': job.job_id,
                'status': job.status.value
            })
            
            self.table.put_item(Item=item)
            
        except ClientError as e:
            lambda_logger.error("Failed to save setup job", extra={
                'tenant_id': job.tenant_id,
                'job_id': job.job_id,
                'error': str(e)
            })
            raise
    
    def get_job(self, tenant_id: str, job_id: str) -> Optional[SetupJob]:
        """Get setup job by tenant ID and job ID."""
        try:
            response = self.table.get_item(
                Key={
                    'tenant_id': tenant_id,
                    'job_id': job_id
                }
            )
            
            if 'Item' not in response:
                return None
            
            return SetupJob.from_dict(response['Item'])
            
        except ClientError as e:
            lambda_logger.error("Failed to get setup job", extra={
                'tenant_id': tenant_id,
                'job_id': job_id,
                'error': str(e)
            })
            raise
    
    def get_job_by_id(self, job_id: str) -> Optional[SetupJob]:
        """Get setup job by job ID only."""
        try:
            response = self.table.query(
                IndexName='JobIdIndex',
                KeyConditionExpression=boto3.dynamodb.conditions.Key('job_id').eq(job_id)
            )
            
            if not response['Items']:
                return None
            
            return SetupJob.from_dict(response['Items'][0])
            
        except ClientError as e:
            lambda_logger.error("Failed to get setup job by ID", extra={
                'job_id': job_id,
                'error': str(e)
            })
            raise
    
    def get_latest_job(self, tenant_id: str) -> Optional[SetupJob]:
        """Get the latest setup job for a tenant."""
        try:
            response = self.table.query(
                KeyConditionExpression=boto3.dynamodb.conditions.Key('tenant_id').eq(tenant_id),
                ScanIndexForward=False,  # Sort by job_id descending
                Limit=1
            )
            
            if not response['Items']:
                return None
            
            return SetupJob.from_dict(response['Items'][0])
            
        except ClientError as e:
            lambda_logger.error("Failed to get latest setup job", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    def get_active_job(self, tenant_id: str) -> Optional[SetupJob]:
        """Get active setup job for a tenant (PENDING or IN_PROGRESS)."""
        try:
            response = self.table.query(
                KeyConditionExpression=boto3.dynamodb.conditions.Key('tenant_id').eq(tenant_id),
                FilterExpression=boto3.dynamodb.conditions.Attr('status').is_in(['PENDING', 'IN_PROGRESS']),
                ScanIndexForward=False,  # Sort by job_id descending
                Limit=1
            )
            
            if not response['Items']:
                return None
            
            return SetupJob.from_dict(response['Items'][0])
            
        except ClientError as e:
            lambda_logger.error("Failed to get active setup job", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    def get_jobs_by_tenant(self, tenant_id: str, limit: int = 10) -> List[SetupJob]:
        """Get all setup jobs for a tenant."""
        try:
            response = self.table.query(
                KeyConditionExpression=boto3.dynamodb.conditions.Key('tenant_id').eq(tenant_id),
                ScanIndexForward=False,  # Sort by job_id descending (newest first)
                Limit=limit
            )
            
            jobs = []
            for item in response['Items']:
                jobs.append(SetupJob.from_dict(item))
            
            return jobs
            
        except ClientError as e:
            lambda_logger.error("Failed to get jobs by tenant", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    def get_jobs_by_status(self, status: SetupJobStatus, limit: int = 100) -> List[SetupJob]:
        """Get setup jobs by status."""
        try:
            response = self.table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('status').eq(status.value),
                Limit=limit
            )
            
            jobs = []
            for item in response['Items']:
                jobs.append(SetupJob.from_dict(item))
            
            return jobs
            
        except ClientError as e:
            lambda_logger.error("Failed to get jobs by status", extra={
                'status': status.value,
                'error': str(e)
            })
            raise
    
    def update_job_status(self, tenant_id: str, job_id: str, status: SetupJobStatus,
                         updates: Optional[Dict[str, Any]] = None) -> SetupJob:
        """Update setup job status and other fields."""
        try:
            # Get current job
            job = self.get_job(tenant_id, job_id)
            if not job:
                raise ResourceNotFoundException(f"Setup job not found: {tenant_id}/{job_id}")
            
            # Update status and timestamp
            job.status = status
            job.updated_at = datetime.utcnow()
            
            # Apply additional updates
            if updates:
                for key, value in updates.items():
                    if hasattr(job, key):
                        setattr(job, key, value)
            
            # Save updated job
            self.save_job(job)
            
            lambda_logger.info("Setup job status updated", extra={
                'tenant_id': tenant_id,
                'job_id': job_id,
                'status': status.value,
                'updates': updates
            })
            
            return job
            
        except Exception as e:
            lambda_logger.error("Failed to update job status", extra={
                'tenant_id': tenant_id,
                'job_id': job_id,
                'status': status.value,
                'error': str(e)
            })
            raise
    
    def delete_job(self, tenant_id: str, job_id: str) -> None:
        """Delete setup job."""
        try:
            lambda_logger.info("Deleting setup job", extra={
                'tenant_id': tenant_id,
                'job_id': job_id
            })
            
            self.table.delete_item(
                Key={
                    'tenant_id': tenant_id,
                    'job_id': job_id
                }
            )
            
        except ClientError as e:
            lambda_logger.error("Failed to delete setup job", extra={
                'tenant_id': tenant_id,
                'job_id': job_id,
                'error': str(e)
            })
            raise
    
    def get_job_statistics(self) -> Dict[str, Any]:
        """Get setup job statistics."""
        try:
            # This is a simplified version - in production you'd use more efficient queries
            response = self.table.scan()
            
            stats = {
                'total_jobs': 0,
                'by_status': {},
                'completed_today': 0,
                'failed_today': 0,
                'active_jobs': 0
            }
            
            today = datetime.utcnow().date()
            
            for item in response['Items']:
                job = SetupJob.from_dict(item)
                stats['total_jobs'] += 1
                
                status_value = job.status.value
                stats['by_status'][status_value] = stats['by_status'].get(status_value, 0) + 1
                
                # Check if completed/failed today
                if job.updated_at.date() == today:
                    if job.status == SetupJobStatus.COMPLETED:
                        stats['completed_today'] += 1
                    elif job.status == SetupJobStatus.FAILED:
                        stats['failed_today'] += 1
                
                # Count active jobs
                if job.status in [SetupJobStatus.PENDING, SetupJobStatus.IN_PROGRESS]:
                    stats['active_jobs'] += 1
            
            return stats
            
        except ClientError as e:
            lambda_logger.error("Failed to get job statistics", extra={
                'error': str(e)
            })
            raise
    
    def cleanup_old_jobs(self, days_old: int = 30) -> int:
        """Cleanup old completed/failed jobs."""
        try:
            from datetime import timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            cutoff_iso = cutoff_date.isoformat()
            
            # Find old jobs
            response = self.table.scan(
                FilterExpression=(
                    boto3.dynamodb.conditions.Attr('updated_at').lt(cutoff_iso) &
                    boto3.dynamodb.conditions.Attr('status').is_in(['COMPLETED', 'FAILED', 'CANCELLED'])
                )
            )
            
            deleted_count = 0
            for item in response['Items']:
                try:
                    self.table.delete_item(
                        Key={
                            'tenant_id': item['tenant_id'],
                            'job_id': item['job_id']
                        }
                    )
                    deleted_count += 1
                except Exception as e:
                    lambda_logger.warning("Failed to delete old job", extra={
                        'tenant_id': item['tenant_id'],
                        'job_id': item['job_id'],
                        'error': str(e)
                    })
            
            lambda_logger.info("Old jobs cleaned up", extra={
                'days_old': days_old,
                'deleted_count': deleted_count
            })
            
            return deleted_count
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup old jobs", extra={
                'days_old': days_old,
                'error': str(e)
            })
            raise
