#!/usr/bin/env python3
# services/setup/src/handlers/health.py
# Health check handler

"""
Health check handler for the setup service.
Provides service health status and basic diagnostics.
"""

import os
from typing import Any, Dict, Optional
from datetime import datetime

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger


@rate_limit(requests_per_minute=60)  # Higher limit for health checks
@measure_performance("setup_health")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Health check endpoint.
    
    GET /health
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    try:
        # Check environment variables
        required_env_vars = [
            'SETUP_JOBS_TABLE',
            'TEMPLATES_BUCKET',
            'REGION',
            'PROJECT_NAME',
            'STAGE'
        ]
        
        env_status = {}
        all_env_ok = True
        
        for var in required_env_vars:
            value = os.environ.get(var)
            env_status[var] = {
                'configured': value is not None,
                'value': value[:20] + '...' if value and len(value) > 20 else value
            }
            if not value:
                all_env_ok = False
        
        # Check DynamoDB table access
        dynamodb_status = {'accessible': False, 'error': None}
        try:
            from ..services.job_manager import SetupJobManager
            job_manager = SetupJobManager()
            # Try to get table description
            table_description = job_manager.table.meta.client.describe_table(
                TableName=job_manager.table_name
            )
            dynamodb_status['accessible'] = True
            dynamodb_status['table_status'] = table_description['Table']['TableStatus']
        except Exception as e:
            dynamodb_status['error'] = str(e)
        
        # Check S3 bucket access
        s3_status = {'accessible': False, 'error': None}
        try:
            import boto3
            s3_client = boto3.client('s3')
            bucket_name = os.environ.get('TEMPLATES_BUCKET')
            if bucket_name:
                s3_client.head_bucket(Bucket=bucket_name)
                s3_status['accessible'] = True
                s3_status['bucket_name'] = bucket_name
        except Exception as e:
            s3_status['error'] = str(e)
        
        # Check CloudFormation access
        cf_status = {'accessible': False, 'error': None}
        try:
            import boto3
            cf_client = boto3.client('cloudformation')
            # Try to list stacks (limited to 1 for performance)
            cf_client.list_stacks(MaxItems=1)
            cf_status['accessible'] = True
        except Exception as e:
            cf_status['error'] = str(e)
        
        # Overall health status
        is_healthy = (
            all_env_ok and 
            dynamodb_status['accessible'] and 
            s3_status['accessible'] and 
            cf_status['accessible']
        )
        
        # Prepare health data
        health_data = {
            'service': 'setup',
            'status': 'healthy' if is_healthy else 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'environment': os.environ.get('STAGE', 'unknown'),
            'region': os.environ.get('REGION', 'unknown'),
            'checks': {
                'environment_variables': {
                    'status': 'pass' if all_env_ok else 'fail',
                    'details': env_status
                },
                'dynamodb': {
                    'status': 'pass' if dynamodb_status['accessible'] else 'fail',
                    'details': dynamodb_status
                },
                's3': {
                    'status': 'pass' if s3_status['accessible'] else 'fail',
                    'details': s3_status
                },
                'cloudformation': {
                    'status': 'pass' if cf_status['accessible'] else 'fail',
                    'details': cf_status
                }
            }
        }
        
        # Add context information if available
        if context:
            health_data['lambda'] = {
                'function_name': context.function_name,
                'function_version': context.function_version,
                'memory_limit': context.memory_limit_in_mb,
                'remaining_time': context.get_remaining_time_in_millis()
            }
        
        # Add service statistics if DynamoDB is accessible
        if dynamodb_status['accessible']:
            try:
                from ..services.job_manager import SetupJobManager
                job_manager = SetupJobManager()
                stats = job_manager.get_job_statistics()
                health_data['statistics'] = stats
            except Exception as e:
                health_data['statistics'] = {'error': str(e)}
        
        # Create response
        status_code = 200 if is_healthy else 503
        message = "Service is healthy" if is_healthy else "Service is unhealthy"
        
        response = APIResponse.success(
            data=health_data,
            message=message
        )
        
        # Override status code for unhealthy service
        if not is_healthy:
            response['statusCode'] = 503
        
        lambda_logger.info("Health check completed", extra={
            'status': health_data['status'],
            'all_env_ok': all_env_ok,
            'dynamodb_accessible': dynamodb_status['accessible'],
            's3_accessible': s3_status['accessible'],
            'cf_accessible': cf_status['accessible']
        })
        
        return response
        
    except Exception as e:
        lambda_logger.error("Health check failed", extra={
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        # Return unhealthy status
        health_data = {
            'service': 'setup',
            'status': 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }
        
        response = APIResponse.error(
            message="Health check failed",
            details=health_data,
            status_code=503
        )
        
        return response
