#!/usr/bin/env python3
# services/setup/src/handlers/tenant_setup.py
# Tenant setup handler

"""
Tenant setup handler for configuring tenant resources.
Handles the creation of DynamoDB tables, S3 buckets, and IAM roles.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException
from shared.validators import validate_required_fields

from ..models.setup_models import SetupRequest
from ..services.setup_service import SetupService


def validate_setup_request(data: Dict[str, Any], tenant_id: str) -> None:
    """Validate setup request data."""
    # Validate required fields
    validate_required_fields(data, ['plan_id'])
    
    plan_id = data.get('plan_id', '')
    
    # Validate plan ID format
    if not plan_id.startswith('plan_'):
        raise ValidationException("Invalid plan ID format")
    
    # Validate tenant ID format
    if not tenant_id or len(tenant_id) < 3:
        raise ValidationException("Invalid tenant ID")


@rate_limit(requests_per_minute=5)  # Strict rate limiting for setup operations
@measure_performance("setup_tenant_setup")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Setup tenant resources.
    
    POST /setup/tenant/{tenant_id}
    {
        "plan_id": "plan_pro",
        "configuration": {
            "custom_settings": "value"
        }
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "tenant_setup")
        
        # Extract tenant_id from path parameters
        path_parameters = event.get('pathParameters', {})
        tenant_id = path_parameters.get('tenant_id')
        
        if not tenant_id:
            raise ValidationException("Tenant ID is required")
        
        # Parse request body
        body = event.get('body', '{}')
        if isinstance(body, str):
            data = json.loads(body)
        else:
            data = body
        
        plan_id = data.get('plan_id')
        
        lambda_logger.info("Processing tenant setup request", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'plan_id': plan_id
        })
        
        # Validate request
        validate_setup_request(data, tenant_id)
        
        # Create setup request object
        setup_request = SetupRequest(
            tenant_id=tenant_id,
            plan_id=data['plan_id'],
            configuration=data.get('configuration')
        )
        
        # Execute setup
        setup_service = SetupService()
        setup_job = setup_service.setup_tenant(setup_request)
        
        # Prepare response data
        response_data = {
            'tenant_id': tenant_id,
            'job_id': setup_job.job_id,
            'status': setup_job.status.value,
            'plan_id': setup_job.plan_config.plan_id,
            'plan_name': setup_job.plan_config.plan_name,
            'total_resources': setup_job.total_resources,
            'progress_percentage': setup_job.get_progress_percentage(),
            'created_at': setup_job.created_at.isoformat(),
            'stack_name': setup_job.stack_name,
            'estimated_completion_time': '5-10 minutes',
            'next_steps': [
                "Setup process has been initiated",
                "Monitor progress using the status endpoint",
                "Resources will be available once setup completes"
            ]
        }
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Tenant setup initiated successfully"
        )
        
        # Log API response
        log_api_response(response, "tenant_setup")
        
        lambda_logger.info("Tenant setup initiated successfully", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'job_id': setup_job.job_id,
            'status': setup_job.status.value,
            'total_resources': setup_job.total_resources
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Tenant setup validation failed", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "tenant_setup")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Tenant setup platform error", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Setup failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "tenant_setup")
        return response
        
    except Exception as e:
        lambda_logger.error("Tenant setup unexpected error", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "tenant_setup")
        return response
