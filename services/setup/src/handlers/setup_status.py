#!/usr/bin/env python3
# services/setup/src/handlers/setup_status.py
# Setup status handler

"""
Setup status handler for monitoring tenant setup progress.
Provides real-time status updates for setup and teardown operations.
"""

from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException

from ..services.setup_service import SetupService


@rate_limit(requests_per_minute=30)  # Higher limit for status checks
@measure_performance("setup_status")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get setup status for a tenant.
    
    GET /setup/status/{tenant_id}
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "setup_status")
        
        # Extract tenant_id from path parameters
        path_parameters = event.get('pathParameters', {})
        tenant_id = path_parameters.get('tenant_id')
        
        if not tenant_id:
            raise ValidationException("Tenant ID is required")
        
        if len(tenant_id) < 3:
            raise ValidationException("Invalid tenant ID format")
        
        lambda_logger.info("Getting setup status", extra={
            'request_id': request_id,
            'tenant_id': tenant_id
        })
        
        # Get status through setup service
        setup_service = SetupService()
        status_data = setup_service.get_setup_status(tenant_id)
        
        # Enhance status data with additional information
        enhanced_status = {
            **status_data,
            'request_id': request_id,
            'timestamp': lambda_logger.get_timestamp()
        }
        
        # Add status-specific information
        status = status_data.get('status', 'NOT_FOUND')
        
        if status == 'NOT_FOUND':
            enhanced_status['message'] = 'No setup found for this tenant'
            enhanced_status['next_steps'] = [
                'Initiate setup by calling POST /setup/tenant/{tenant_id}',
                'Ensure tenant ID is correct'
            ]
        elif status == 'PENDING':
            enhanced_status['message'] = 'Setup is queued and will start shortly'
            enhanced_status['next_steps'] = [
                'Setup will begin automatically',
                'Check back in a few minutes for progress updates'
            ]
        elif status == 'IN_PROGRESS':
            progress = enhanced_status.get('progress_percentage', 0)
            enhanced_status['message'] = f'Setup is in progress ({progress:.1f}% complete)'
            enhanced_status['next_steps'] = [
                'Setup is actively running',
                'Resources are being created',
                'Check back periodically for updates'
            ]
        elif status == 'COMPLETED':
            enhanced_status['message'] = 'Setup completed successfully'
            enhanced_status['next_steps'] = [
                'All tenant resources are ready',
                'You can now use the platform services',
                'Access your tenant dashboard'
            ]
        elif status == 'FAILED':
            enhanced_status['message'] = 'Setup failed - manual intervention may be required'
            enhanced_status['next_steps'] = [
                'Review error details below',
                'Contact support if the issue persists',
                'Consider retrying the setup process'
            ]
        elif status == 'CANCELLED':
            enhanced_status['message'] = 'Setup was cancelled'
            enhanced_status['next_steps'] = [
                'Setup can be restarted if needed',
                'Contact support if cancellation was unexpected'
            ]
        
        # Add resource breakdown if available
        if status_data.get('total_resources', 0) > 0:
            enhanced_status['resource_breakdown'] = {
                'total': status_data.get('total_resources', 0),
                'completed': status_data.get('completed_resources', 0),
                'failed': status_data.get('failed_resources', 0),
                'remaining': status_data.get('total_resources', 0) - status_data.get('completed_resources', 0)
            }
        
        # Add CloudFormation information if available
        if status_data.get('stack_name'):
            enhanced_status['cloudformation'] = {
                'stack_name': status_data.get('stack_name'),
                'stack_status': status_data.get('stack_status'),
                'console_url': f"https://console.aws.amazon.com/cloudformation/home?region={lambda_logger.get_region()}#/stacks/stackinfo?stackId={status_data.get('stack_name')}"
            }
        
        # Create success response
        response = APIResponse.success(
            data=enhanced_status,
            message="Setup status retrieved successfully"
        )
        
        # Log API response
        log_api_response(response, "setup_status")
        
        lambda_logger.info("Setup status retrieved", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'status': status,
            'progress_percentage': enhanced_status.get('progress_percentage', 0)
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Setup status validation failed", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "setup_status")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Setup status platform error", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to get setup status",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "setup_status")
        return response
        
    except Exception as e:
        lambda_logger.error("Setup status unexpected error", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "setup_status")
        return response
