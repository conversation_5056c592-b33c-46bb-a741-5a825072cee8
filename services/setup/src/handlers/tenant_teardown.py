#!/usr/bin/env python3
# services/setup/src/handlers/tenant_teardown.py
# Tenant teardown handler

"""
Tenant teardown handler for removing tenant resources.
Handles the cleanup of DynamoDB tables, S3 buckets, and IAM roles.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException

from ..models.setup_models import TeardownRequest
from ..services.setup_service import SetupService


def validate_teardown_request(data: Dict[str, Any], tenant_id: str) -> None:
    """Validate teardown request data."""
    # Validate tenant ID format
    if not tenant_id or len(tenant_id) < 3:
        raise ValidationException("Invalid tenant ID")
    
    # Validate optional parameters
    force = data.get('force', False)
    preserve_data = data.get('preserve_data', True)
    
    if not isinstance(force, bool):
        raise ValidationException("Force parameter must be boolean")
    
    if not isinstance(preserve_data, bool):
        raise ValidationException("Preserve data parameter must be boolean")


@rate_limit(requests_per_minute=3)  # Very strict rate limiting for teardown
@measure_performance("setup_tenant_teardown")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Teardown tenant resources.
    
    DELETE /setup/tenant/{tenant_id}
    {
        "force": false,
        "preserve_data": true
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "tenant_teardown")
        
        # Extract tenant_id from path parameters
        path_parameters = event.get('pathParameters', {})
        tenant_id = path_parameters.get('tenant_id')
        
        if not tenant_id:
            raise ValidationException("Tenant ID is required")
        
        # Parse request body (optional for DELETE)
        body = event.get('body', '{}')
        if isinstance(body, str):
            data = json.loads(body) if body else {}
        else:
            data = body or {}
        
        force = data.get('force', False)
        preserve_data = data.get('preserve_data', True)
        
        lambda_logger.info("Processing tenant teardown request", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'force': force,
            'preserve_data': preserve_data
        })
        
        # Validate request
        validate_teardown_request(data, tenant_id)
        
        # Create teardown request object
        teardown_request = TeardownRequest(
            tenant_id=tenant_id,
            force=force,
            preserve_data=preserve_data
        )
        
        # Execute teardown
        setup_service = SetupService()
        teardown_result = setup_service.teardown_tenant(teardown_request)
        
        # Prepare response data
        response_data = {
            'tenant_id': tenant_id,
            'teardown_initiated': True,
            'job_id': teardown_result.get('job_id'),
            'force': force,
            'preserve_data': preserve_data,
            'resources_removed': teardown_result.get('resources_removed', 0),
            'estimated_completion_time': '5-10 minutes',
            'warnings': [],
            'next_steps': []
        }
        
        # Add warnings and next steps based on configuration
        if preserve_data:
            response_data['warnings'].append("Data preservation enabled - some resources may remain")
            response_data['next_steps'].extend([
                "Data will be preserved in S3 buckets",
                "Manual cleanup may be required for preserved resources"
            ])
        else:
            response_data['warnings'].append("Data preservation disabled - all data will be deleted")
            response_data['next_steps'].append("All tenant data will be permanently deleted")
        
        if force:
            response_data['warnings'].append("Force mode enabled - ignoring dependency checks")
        
        response_data['next_steps'].extend([
            "Monitor teardown progress using the status endpoint",
            "Teardown process will complete in background"
        ])
        
        # Create success response
        response = APIResponse.success(
            data=response_data,
            message="Tenant teardown initiated successfully"
        )
        
        # Log API response
        log_api_response(response, "tenant_teardown")
        
        lambda_logger.info("Tenant teardown initiated successfully", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'job_id': teardown_result.get('job_id'),
            'force': force,
            'preserve_data': preserve_data
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Tenant teardown validation failed", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "tenant_teardown")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Tenant teardown platform error", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Teardown failed",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "tenant_teardown")
        return response
        
    except Exception as e:
        lambda_logger.error("Tenant teardown unexpected error", extra={
            'request_id': request_id,
            'tenant_id': tenant_id if 'tenant_id' in locals() else None,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "tenant_teardown")
        return response
