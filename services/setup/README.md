# 🛠️ **Setup Service - Agent-SCL Platform**

## **🚀 Descripción General**

El Setup Service es el motor de aprovisionamiento de infraestructura del ecosistema **agent-scl**. Proporciona infraestructura completa para aprovisionamiento automático de recursos AWS por tenant, gestión de CloudFormation stacks, configuración basada en planes de suscripción, tracking de progreso en tiempo real y teardown seguro de recursos.

### **✨ Características Principales**
- 🛠️ **Infrastructure as Code**: CloudFormation para gestión de recursos
- 📋 **Configuración por Planes**: Recursos específicos según suscripción
- ⏱️ **Setup Asíncrono**: Jobs de setup con tracking en tiempo real
- 🔒 **Aislamiento Completo**: Recursos completamente separados por tenant
- 🗑️ **Teardown Seguro**: Eliminación controlada con opciones de preservación
- 📊 **Monitoreo Avanzado**: Tracking detallado de progreso y métricas
- 🔐 **Seguridad Robusta**: IAM roles y políticas específicas por tenant
- ⚡ **Auto-Rollback**: Cleanup automático en caso de fallos

---

## **📋 Tabla de Contenidos**
1. [Instalación y Setup](#instalación-y-setup)
2. [Configuración](#configuración)
3. [Planes Disponibles](#planes-disponibles)
4. [API Reference](#api-reference)
5. [CloudFormation Templates](#cloudformation-templates)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)

---

## **🛠️ Instalación y Setup**

### **Prerrequisitos**
```bash
# Servicios requeridos (deben estar desplegados)
✅ agent-scl-shared-layer-dev
✅ agent-scl-orchestrator-dev

# Herramientas de desarrollo
- Python 3.11+
- Serverless Framework 3.x
- AWS CLI configurado
- Node.js 18+ (para Serverless)
```

### **Instalación Local**
```bash
# 1. Clonar y navegar al directorio
cd services/setup

# 2. Instalar dependencias Python
pip install -r requirements.txt

# 3. Instalar Serverless plugins
npm install

# 4. Configurar variables de entorno
export STAGE=dev
export REGION=us-east-1
```

### **Configuración de CloudFormation**
```bash
# Crear bucket para templates CloudFormation
aws s3 mb s3://agent-scl-cf-templates-dev --region us-east-1

# Verificar permisos de CloudFormation
aws iam get-role --role-name CloudFormationExecutionRole

# Verificar tabla DynamoDB
aws dynamodb describe-table --table-name "agent-scl-dev"
```

## 🔧 **API Endpoints**

### **POST /setup/tenant/{tenant_id}**
Setup resources for a new tenant.

**Request:**
```json
{
  "plan_id": "plan_pro",
  "configuration": {
    "custom_settings": "value"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant_123",
    "job_id": "setup_abc123def456",
    "status": "IN_PROGRESS",
    "plan_id": "plan_pro",
    "plan_name": "Professional",
    "total_resources": 8,
    "progress_percentage": 25.0,
    "stack_name": "tenant-tenant_123-dev",
    "estimated_completion_time": "5-10 minutes"
  }
}
```

### **DELETE /setup/tenant/{tenant_id}**
Teardown resources for a tenant.

**Request:**
```json
{
  "force": false,
  "preserve_data": true
}
```

### **GET /setup/status/{tenant_id}**
Get setup status and progress.

**Response:**
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant_123",
    "job_id": "setup_abc123def456",
    "status": "COMPLETED",
    "progress_percentage": 100.0,
    "total_resources": 8,
    "completed_resources": 8,
    "failed_resources": 0,
    "stack_name": "tenant-tenant_123-dev",
    "stack_status": "CREATE_COMPLETE"
  }
}
```

### **GET /health**
Service health check.

## 🗄️ **Data Models**

### **SetupJob**
```python
{
  "tenant_id": "tenant_123",
  "job_id": "setup_abc123def456",
  "status": "IN_PROGRESS",
  "plan_config": {
    "plan_id": "plan_pro",
    "max_users": 25,
    "max_storage_gb": 100
  },
  "total_resources": 8,
  "completed_resources": 3,
  "stack_name": "tenant-tenant_123-dev",
  "execution_log": [...]
}
```

## 📊 **Subscription Plans**

### **Starter Plan**
- **Users**: 5 max
- **Agents**: 2 max
- **Storage**: 10 GB
- **API Calls**: 10,000/month
- **Resources**: Basic DynamoDB + S3

### **Professional Plan**
- **Users**: 25 max
- **Agents**: 5 max
- **Storage**: 100 GB
- **API Calls**: 100,000/month
- **Resources**: Enhanced DynamoDB + S3 + Reports

### **Enterprise Plan**
- **Users**: 100 max
- **Agents**: 20 max
- **Storage**: 1,000 GB
- **API Calls**: 1,000,000/month
- **Resources**: Full suite + Advanced monitoring

## ☁️ **CloudFormation Templates**

### **Generated Resources**

**DynamoDB Tables:**
- `{tenant_id}-data` - Main data storage
- `{tenant_id}-conversations` - Chat conversations
- `{tenant_id}-analytics` - Usage analytics

**S3 Buckets:**
- `{tenant_id}-data-{region}` - Raw data storage
- `{tenant_id}-processed-{region}` - Processed files
- `{tenant_id}-reports-{region}` - Generated reports (Pro+)

**IAM Roles:**
- `tenant-{tenant_id}-role` - Tenant access role

### **Template Features**
- **Encryption**: All resources encrypted at rest
- **Versioning**: S3 bucket versioning enabled
- **Lifecycle**: Automatic cleanup policies
- **Tagging**: Consistent resource tagging
- **Monitoring**: CloudWatch integration

## 🚀 **Deployment**

### **Environment Variables**
```bash
# DynamoDB
SETUP_JOBS_TABLE=agent-scl-setup-jobs-dev

# S3
TEMPLATES_BUCKET=agent-scl-setup-templates-dev

# AWS
STAGE=dev
REGION=us-east-1
PROJECT_NAME=agent-scl
```

### **Deploy Commands**
```bash
# Install dependencies
npm install

# Deploy to dev
serverless deploy --stage dev

# Deploy to production
serverless deploy --stage prod
```

## 🧪 **Testing**

### **Unit Tests**
```bash
# Run unit tests
pytest tests/unit/

# Test specific component
pytest tests/unit/test_setup_service.py
```

### **Integration Tests**
```bash
# Run integration tests
pytest tests/integration/

# Test CloudFormation templates
pytest tests/integration/test_cloudformation.py
```

### **Manual Testing**
```bash
# Setup tenant
curl -X POST https://api.dev.com/setup/tenant/test_tenant \
  -H "Content-Type: application/json" \
  -d '{"plan_id": "plan_pro"}'

# Check status
curl https://api.dev.com/setup/status/test_tenant

# Teardown
curl -X DELETE https://api.dev.com/setup/tenant/test_tenant \
  -H "Content-Type: application/json" \
  -d '{"force": false, "preserve_data": true}'
```

## 📊 **Monitoring**

### **CloudWatch Metrics**
- `SetupJobsStarted` - Number of setup jobs initiated
- `SetupJobsCompleted` - Number of successful setups
- `SetupJobsFailed` - Number of failed setups
- `SetupDuration` - Time to complete setup
- `ResourcesCreated` - Total resources created

### **Alarms**
- High setup failure rate (>10% in 10 minutes)
- Long setup times (>15 minutes)
- CloudFormation stack failures
- DynamoDB throttling

## 🔒 **Security**

### **IAM Permissions**
- **Least Privilege**: Minimal required permissions
- **Resource Isolation**: Tenant-specific resource access
- **Encryption**: All data encrypted at rest and in transit
- **Audit Logging**: All operations logged

### **Resource Isolation**
- **Naming Convention**: Tenant ID in all resource names
- **Access Policies**: Tenant-specific IAM policies
- **Network Isolation**: VPC isolation (future)

## 🐛 **Troubleshooting**

### **Common Issues**

**Setup Job Stuck in PENDING**
- Check CloudFormation service limits
- Verify IAM permissions
- Review CloudWatch logs

**CloudFormation Stack Creation Fails**
- Check resource naming conflicts
- Verify service quotas
- Review stack events in console

**Resource Access Denied**
- Verify IAM role permissions
- Check resource policies
- Confirm tenant ID format

### **Debug Commands**
```bash
# Check service health
curl https://api.dev.com/setup/health

# Get setup job details
aws dynamodb get-item \
  --table-name agent-scl-setup-jobs-dev \
  --key '{"tenant_id": {"S": "tenant_123"}, "job_id": {"S": "setup_abc123"}}'

# Check CloudFormation stack
aws cloudformation describe-stacks \
  --stack-name tenant-tenant_123-dev

# Check CloudWatch logs
aws logs filter-log-events \
  --log-group-name /aws/lambda/setup-dev-setupTenant \
  --start-time 1692000000000
```

## 📝 **Development**

### **Adding New Resource Types**
1. Update `ResourceType` enum
2. Add resource configuration in `generate_resource_configs()`
3. Implement CloudFormation generator in `cloudformation_service.py`
4. Update tests
5. Update documentation

### **Adding New Plans**
1. Add plan to `PLAN_CONFIGURATIONS`
2. Update resource generation logic
3. Add plan-specific resources
4. Update tests
5. Update pricing documentation

## 🔄 **Future Enhancements**

- [ ] Advanced monitoring with CloudWatch dashboards
- [ ] Custom CloudFormation templates per tenant
- [ ] Blue-green deployment for resource updates
- [ ] Automated backup and disaster recovery
- [ ] Multi-region resource deployment
- [ ] Resource usage optimization recommendations
- [ ] Integration with AWS Config for compliance
- [ ] Automated cost optimization

---

## **📚 Documentación Adicional**

- 📖 [Documentación Técnica Completa](./TECHNICAL_DOCUMENTATION.md)
- 🔧 [Guía de CloudFormation Templates](./docs/cloudformation.md)
- 🚀 [Guía de Deployment](./docs/deployment.md)
- 🐛 [Troubleshooting Avanzado](./docs/troubleshooting.md)

---

## **📝 Changelog**

### **v1.0.0 (2024-08-29) - ✅ DEPLOYED**
- ✅ Infrastructure as Code con CloudFormation
- ✅ Setup asíncrono con tracking en tiempo real
- ✅ Configuración basada en planes de suscripción
- ✅ Aislamiento completo de recursos por tenant
- ✅ Teardown seguro con opciones de preservación
- ✅ Monitoreo avanzado y métricas detalladas
- ✅ Auto-rollback en caso de fallos

### **Próximas Versiones**
- 🔄 Multi-region setup de recursos
- 🔄 Advanced templates con AWS CDK
- 🔄 Cost optimization automática
- 🔄 Self-healing infrastructure

---

**📝 Documento actualizado**: 2024-08-29
**👨‍💻 Mantenido por**: Agent-SCL Development Team
**🔗 Repositorio**: [agent-scl/services/setup](https://github.com/agent-scl/services/setup)
