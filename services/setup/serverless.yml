service: agent-scl-setup

custom:
  serviceName: setup
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn

provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}

  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName

    # Setup-specific tables
    SETUP_JOBS_TABLE: ${self:custom.projectName}-setup-jobs-${self:custom.stage}

    # S3 bucket for templates
    TEMPLATES_BUCKET: ${self:custom.projectName}-setup-templates-${self:custom.stage}

  # IAM role statements
  iamRoleStatements:
    # Main DynamoDB table permissions
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"

    # Setup jobs table permissions
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-setup-jobs-${self:custom.stage}
        - arn:aws:dynamodb:${self:custom.region}:*:table/${self:custom.projectName}-setup-jobs-${self:custom.stage}/index/*
        
    # CloudFormation permissions
    - Effect: Allow
      Action:
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DeleteStack
        - cloudformation:DescribeStacks
        - cloudformation:DescribeStackEvents
        - cloudformation:DescribeStackResources
        - cloudformation:GetTemplate
        - cloudformation:ValidateTemplate
      Resource: 
        - arn:aws:cloudformation:${self:custom.region}:*:stack/tenant-*
    
    # S3 permissions for templates and tenant buckets
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
        - s3:DeleteObject
        - s3:ListBucket
        - s3:CreateBucket
        - s3:DeleteBucket
        - s3:PutBucketPolicy
        - s3:PutBucketVersioning
        - s3:PutBucketEncryption
        - s3:PutLifecycleConfiguration
      Resource:
        - arn:aws:s3:::${self:custom.projectName}-setup-templates-${self:custom.stage}
        - arn:aws:s3:::${self:custom.projectName}-setup-templates-${self:custom.stage}/*
        - arn:aws:s3:::*-data-${self:custom.region}
        - arn:aws:s3:::*-data-${self:custom.region}/*
        - arn:aws:s3:::*-processed-${self:custom.region}
        - arn:aws:s3:::*-processed-${self:custom.region}/*
        - arn:aws:s3:::*-reports-${self:custom.region}
        - arn:aws:s3:::*-reports-${self:custom.region}/*
    
    # DynamoDB permissions for tenant tables
    - Effect: Allow
      Action:
        - dynamodb:CreateTable
        - dynamodb:DeleteTable
        - dynamodb:DescribeTable
        - dynamodb:UpdateTable
        - dynamodb:PutItem
        - dynamodb:GetItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - arn:aws:dynamodb:${self:custom.region}:*:table/*-data
        - arn:aws:dynamodb:${self:custom.region}:*:table/*-conversations
        - arn:aws:dynamodb:${self:custom.region}:*:table/*-analytics
    
    # IAM permissions for creating tenant roles
    - Effect: Allow
      Action:
        - iam:CreateRole
        - iam:DeleteRole
        - iam:AttachRolePolicy
        - iam:DetachRolePolicy
        - iam:PutRolePolicy
        - iam:DeleteRolePolicy
        - iam:GetRole
        - iam:ListRolePolicies
        - iam:CreatePolicy
        - iam:DeletePolicy
        - iam:GetPolicy
      Resource:
        - arn:aws:iam::*:role/tenant-*
        - arn:aws:iam::*:policy/tenant-*
    
    # CloudWatch permissions
    - Effect: Allow
      Action:
        - cloudwatch:PutMetricData
        - cloudwatch:CreateAlarm
        - cloudwatch:DeleteAlarms
        - cloudwatch:DescribeAlarms
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"

# Lambda functions
functions:
  # Tenant setup endpoints
  setupTenant:
    handler: src/handlers/tenant_setup.handler
    timeout: 300  # 5 minutes for CloudFormation operations
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /setup/tenant/{tenant_id}
          method: post
          cors: true
  
  teardownTenant:
    handler: src/handlers/tenant_teardown.handler
    timeout: 300  # 5 minutes for cleanup operations
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /setup/tenant/{tenant_id}
          method: delete
          cors: true
  
  setupStatus:
    handler: src/handlers/setup_status.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /setup/status/{tenant_id}
          method: get
          cors: true
  
  # Health check
  health:
    handler: src/handlers/health.handler
    timeout: 10
    memorySize: 128
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /health
          method: get
          cors: true

# Resources
resources:
  Resources:
    # DynamoDB table for setup job tracking
    SetupJobsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.projectName}-setup-jobs-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: tenant_id
            AttributeType: S
          - AttributeName: job_id
            AttributeType: S
          - AttributeName: created_at
            AttributeType: S
        KeySchema:
          - AttributeName: tenant_id
            KeyType: HASH
          - AttributeName: job_id
            KeyType: RANGE
        GlobalSecondaryIndexes:
          - IndexName: JobIdIndex
            KeySchema:
              - AttributeName: job_id
                KeyType: HASH
            Projection:
              ProjectionType: ALL
          - IndexName: CreatedAtIndex
            KeySchema:
              - AttributeName: created_at
                KeyType: HASH
            Projection:
              ProjectionType: ALL
        StreamSpecification:
          StreamViewType: NEW_AND_OLD_IMAGES
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
        SSESpecification:
          SSEEnabled: true
        Tags:
          - Key: Service
            Value: setup
          - Key: Environment
            Value: ${self:custom.stage}
          - Key: Project
            Value: ${self:custom.projectName}
    
    # S3 bucket for CloudFormation templates
    TemplatesBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.projectName}-setup-templates-${self:custom.stage}
        VersioningConfiguration:
          Status: Enabled
        BucketEncryption:
          ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        Tags:
          - Key: Service
            Value: setup
          - Key: Environment
            Value: ${self:custom.stage}
          - Key: Project
            Value: ${self:custom.projectName}

  # Outputs
  Outputs:
    ServiceEndpoint:
      Description: "Setup Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: "ApiGatewayRestApi"
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
            - "/setup"
      Export:
        Name: sls-${self:custom.projectName}-setup-${self:custom.stage}-ServiceEndpoint
    
    SetupJobsTableName:
      Description: "DynamoDB table name for setup jobs"
      Value: ${self:custom.projectName}-setup-jobs-${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-setup-${self:custom.stage}-SetupJobsTableName
    
    TemplatesBucketName:
      Description: "S3 bucket name for CloudFormation templates"
      Value: ${self:custom.projectName}-setup-templates-${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-setup-${self:custom.stage}-TemplatesBucketName

plugins:
  - serverless-python-requirements

package:
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.pytest_cache/**'
    - '!tests/**'
    - '!*.md'
    - '!.env*'
