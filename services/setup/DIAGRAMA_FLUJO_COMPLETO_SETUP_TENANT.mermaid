sequenceDiagram
    participant OS as 🎯 Orchestrator Service
    participant API as 🌐 Setup Service API
    participant TSH as 🛠️ Tenant Setup Handler
    participant SS as 🛠️ Setup Service
    participant PCM as 📋 Plan Config Manager
    participant RTG as 📄 Resource Template Generator
    participant JM as 📋 Job Manager
    participant CFS as ☁️ CloudFormation Service
    participant S3 as 📦 S3 Templates
    participant CF as ☁️ CloudFormation
    participant DB as 🗄️ DynamoDB
    participant CW as 📊 CloudWatch

    Note over OS: Tenant registration completed successfully
    OS->>API: POST /setup/tenant/tenant-123
    Note over OS,API: {plan_id: "plan_pro", configuration: {...}}
    
    API->>TSH: Route to Tenant Setup Handler
    TSH->>TSH: Validate setup request
    TSH->>TSH: Check rate limits (5 req/min)
    TSH->>TSH: Validate tenant_id and plan_id format
    
    TSH->>SS: setup_tenant(request)
    SS->>SS: Validate setup requirements
    SS->>PCM: get_plan_config("plan_pro")
    PCM-->>SS: Plan configuration with resource limits
    
    Note over SS: Plan Pro Configuration:<br/>- max_users: 25<br/>- max_agents: 10<br/>- max_storage_gb: 100<br/>- resources: 8 total
    
    SS->>JM: create_setup_job(tenant_id, plan_id)
    JM->>JM: Generate unique job_id
    JM->>DB: Store setup job record
    Note over DB: PK: SETUP_JOB#{job_id}<br/>status: PENDING<br/>tenant_id: tenant-123<br/>plan_id: plan_pro
    
    SS->>RTG: generate_stack_template(tenant_id, plan_config)
    RTG->>RTG: Generate CloudFormation template
    Note over RTG: Template includes:<br/>- 5 DynamoDB tables<br/>- 4 S3 buckets<br/>- 4 IAM roles<br/>- 15 CloudWatch alarms
    
    RTG->>S3: Upload template to S3
    S3-->>RTG: Template URL
    RTG-->>SS: CloudFormation template ready
    
    SS->>CFS: create_stack(stack_name, template_url, parameters)
    Note over SS,CFS: stack_name: "tenant-tenant-123-dev"<br/>parameters: {TenantId, PlanId, Stage}
    
    CFS->>CF: CreateStack API call
    CF-->>CFS: Stack creation initiated
    CFS-->>SS: Stack creation started
    
    SS->>JM: update_job_progress(job_id, 10, IN_PROGRESS)
    JM->>DB: Update job status and progress
    JM->>CW: Send setup metrics
    
    SS-->>TSH: Setup job created successfully
    TSH-->>API: 201 Created
    Note over TSH,API: {job_id, status: "IN_PROGRESS", progress: 10%, estimated_time: "5-10 minutes"}
    API-->>OS: Setup initiated successfully
    
    Note over CF: CloudFormation creates resources asynchronously
    
    rect rgb(245, 255, 245)
        Note over CF: Resource Creation Phase
        
        loop Monitor stack progress every 30 seconds
            SS->>CFS: get_stack_status(stack_name)
            CFS->>CF: DescribeStacks API call
            CF-->>CFS: Stack status and events
            
            alt Stack events available
                CFS->>CFS: Parse stack events
                CFS->>CFS: Calculate progress based on resources
                Note over CFS: Progress calculation:<br/>- Total resources: 8<br/>- Completed: 3<br/>- Progress: 37.5%
            end
            
            CFS-->>SS: Stack progress update
            SS->>JM: update_job_progress(job_id, progress, IN_PROGRESS)
            JM->>DB: Update progress percentage
            JM->>CW: Send progress metrics
        end
        
        Note over CF: All resources created successfully
        CF->>CF: Stack status: CREATE_COMPLETE
    end
    
    rect rgb(255, 255, 245)
        Note over SS: Completion Phase
        SS->>CFS: get_stack_status(stack_name)
        CFS-->>SS: Stack status: CREATE_COMPLETE
        
        SS->>CFS: get_stack_resources(stack_name)
        CFS->>CF: DescribeStackResources API call
        CF-->>CFS: List of created resources
        Note over CFS: Created resources:<br/>- tenant-123-data-dev (DynamoDB)<br/>- tenant-123-files-dev (S3)<br/>- tenant-123-execution-role (IAM)<br/>- ... (5 more resources)
        
        CFS-->>SS: Stack resources list
        SS->>JM: mark_job_completed(job_id, resources)
        JM->>DB: Update job status to COMPLETED
        JM->>DB: Store stack outputs and resource list
        JM->>CW: Send completion metrics
        
        SS->>SS: Send completion notification to Orchestrator
        Note over SS: Tenant setup completed successfully<br/>All 8 resources created<br/>Duration: 7 minutes
    end
    
    rect rgb(255, 245, 245)
        Note over SS: Error handling scenario
        alt Stack creation fails
            CF->>CF: Stack status: CREATE_FAILED
            SS->>CFS: get_stack_status(stack_name)
            CFS-->>SS: Stack status: CREATE_FAILED
            
            SS->>JM: mark_job_failed(job_id, error_message)
            JM->>DB: Update job status to FAILED
            JM->>CW: Send failure metrics
            
            SS->>CFS: delete_stack(stack_name)
            Note over SS: Cleanup failed resources automatically
            CFS->>CF: DeleteStack API call
        end
    end
    
    Note over OS,CW: Tenant setup process completed