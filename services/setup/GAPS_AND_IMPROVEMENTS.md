# ⚙️ Setup Service - Gaps and Improvements

## 📊 **Current Status: 8.4/10 - MUY BUENO**

### **Completitud:** 90% funcional, gaps identificados

---

## 🎯 **Gaps Identificados**

### **1. CRITICAL GAPS (10%)**

#### **1.1 Real AWS CloudFormation Integration**
**Priority:** High  
**Effort:** 4-5 days  
**Impact:** Core functionality

**Current State:**
- CloudFormation operations are simulated
- Missing real AWS resource provisioning
- Limited error handling for AWS failures

**Required Implementation:**

```python
# src/services/real_cloudformation_service.py
"""Real CloudFormation service implementation."""

import boto3
import json
import time
from typing import Dict, Any, List, Optional
from shared.logger import lambda_logger
from shared.exceptions import SetupException, ResourceNotFoundException

class RealCloudFormationService:
    """Real CloudFormation service for tenant resource provisioning."""
    
    def __init__(self):
        self.cf_client = boto3.client('cloudformation')
        self.s3_client = boto3.client('s3')
        self.template_bucket = os.environ.get('TEMPLATE_BUCKET')
    
    async def provision_tenant_resources(
        self,
        tenant_id: str,
        plan_config: Dict[str, Any],
        region: str = 'us-east-1'
    ) -> Dict[str, Any]:
        """Provision real AWS resources for tenant."""
        try:
            stack_name = f"tenant-{tenant_id}-resources"
            
            # Generate CloudFormation template
            template = await self._generate_tenant_template(tenant_id, plan_config)
            
            # Upload template to S3
            template_url = await self._upload_template(stack_name, template)
            
            # Create CloudFormation stack
            stack_id = await self._create_stack(
                stack_name, 
                template_url, 
                tenant_id, 
                plan_config
            )
            
            # Wait for stack creation
            stack_status = await self._wait_for_stack_completion(stack_id)
            
            if stack_status != 'CREATE_COMPLETE':
                raise SetupException(f"Stack creation failed with status: {stack_status}")
            
            # Get stack outputs
            outputs = await self._get_stack_outputs(stack_id)
            
            lambda_logger.info(f"Successfully provisioned resources for tenant {tenant_id}")
            
            return {
                'stack_id': stack_id,
                'stack_name': stack_name,
                'status': 'PROVISIONED',
                'resources': outputs,
                'region': region
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to provision tenant resources: {str(e)}")
            raise SetupException(f"Resource provisioning failed: {str(e)}")
    
    async def _generate_tenant_template(
        self, 
        tenant_id: str, 
        plan_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate CloudFormation template for tenant."""
        template = {
            "AWSTemplateFormatVersion": "2010-09-09",
            "Description": f"Resources for tenant {tenant_id}",
            "Parameters": {
                "TenantId": {
                    "Type": "String",
                    "Default": tenant_id
                }
            },
            "Resources": {},
            "Outputs": {}
        }
        
        # Add S3 bucket for tenant data
        template["Resources"]["TenantDataBucket"] = {
            "Type": "AWS::S3::Bucket",
            "Properties": {
                "BucketName": f"tenant-{tenant_id}-data",
                "VersioningConfiguration": {
                    "Status": "Enabled"
                },
                "BucketEncryption": {
                    "ServerSideEncryptionConfiguration": [{
                        "ServerSideEncryptionByDefault": {
                            "SSEAlgorithm": "AES256"
                        }
                    }]
                }
            }
        }
        
        # Add Lambda functions based on plan
        if plan_config.get('includes_agents', False):
            template["Resources"]["AgentFunction"] = {
                "Type": "AWS::Lambda::Function",
                "Properties": {
                    "FunctionName": f"tenant-{tenant_id}-agent",
                    "Runtime": "python3.11",
                    "Handler": "index.handler",
                    "Code": {
                        "ZipFile": "# Agent function code placeholder"
                    },
                    "Environment": {
                        "Variables": {
                            "TENANT_ID": tenant_id,
                            "PLAN_TYPE": plan_config.get('plan_type', 'BASIC')
                        }
                    },
                    "MemorySize": plan_config.get('lambda_memory', 256),
                    "Timeout": plan_config.get('lambda_timeout', 30)
                }
            }
        
        # Add API Gateway if needed
        if plan_config.get('includes_api', False):
            template["Resources"]["TenantAPI"] = {
                "Type": "AWS::ApiGateway::RestApi",
                "Properties": {
                    "Name": f"tenant-{tenant_id}-api",
                    "Description": f"API for tenant {tenant_id}"
                }
            }
        
        # Add outputs
        template["Outputs"]["TenantDataBucket"] = {
            "Description": "S3 bucket for tenant data",
            "Value": {"Ref": "TenantDataBucket"}
        }
        
        if plan_config.get('includes_agents', False):
            template["Outputs"]["AgentFunctionArn"] = {
                "Description": "Agent Lambda function ARN",
                "Value": {"Fn::GetAtt": ["AgentFunction", "Arn"]}
            }
        
        return template
    
    async def _upload_template(self, stack_name: str, template: Dict[str, Any]) -> str:
        """Upload CloudFormation template to S3."""
        try:
            template_key = f"templates/{stack_name}.json"
            
            self.s3_client.put_object(
                Bucket=self.template_bucket,
                Key=template_key,
                Body=json.dumps(template, indent=2),
                ContentType='application/json'
            )
            
            template_url = f"https://{self.template_bucket}.s3.amazonaws.com/{template_key}"
            return template_url
            
        except Exception as e:
            lambda_logger.error(f"Failed to upload template: {str(e)}")
            raise
    
    async def _create_stack(
        self,
        stack_name: str,
        template_url: str,
        tenant_id: str,
        plan_config: Dict[str, Any]
    ) -> str:
        """Create CloudFormation stack."""
        try:
            response = self.cf_client.create_stack(
                StackName=stack_name,
                TemplateURL=template_url,
                Parameters=[
                    {
                        'ParameterKey': 'TenantId',
                        'ParameterValue': tenant_id
                    }
                ],
                Tags=[
                    {
                        'Key': 'TenantId',
                        'Value': tenant_id
                    },
                    {
                        'Key': 'PlanType',
                        'Value': plan_config.get('plan_type', 'BASIC')
                    },
                    {
                        'Key': 'Environment',
                        'Value': os.environ.get('STAGE', 'dev')
                    }
                ],
                Capabilities=['CAPABILITY_IAM'],
                OnFailure='ROLLBACK'
            )
            
            return response['StackId']
            
        except Exception as e:
            lambda_logger.error(f"Failed to create stack: {str(e)}")
            raise
    
    async def _wait_for_stack_completion(
        self, 
        stack_id: str, 
        timeout: int = 1800
    ) -> str:
        """Wait for CloudFormation stack to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.cf_client.describe_stacks(StackName=stack_id)
                stack = response['Stacks'][0]
                status = stack['StackStatus']
                
                if status in ['CREATE_COMPLETE', 'UPDATE_COMPLETE']:
                    return status
                elif status in ['CREATE_FAILED', 'ROLLBACK_COMPLETE', 'DELETE_COMPLETE']:
                    return status
                
                # Wait before checking again
                await asyncio.sleep(30)
                
            except Exception as e:
                lambda_logger.error(f"Error checking stack status: {str(e)}")
                await asyncio.sleep(30)
        
        raise SetupException(f"Stack creation timeout after {timeout} seconds")
    
    async def _get_stack_outputs(self, stack_id: str) -> Dict[str, Any]:
        """Get CloudFormation stack outputs."""
        try:
            response = self.cf_client.describe_stacks(StackName=stack_id)
            stack = response['Stacks'][0]
            
            outputs = {}
            for output in stack.get('Outputs', []):
                outputs[output['OutputKey']] = output['OutputValue']
            
            return outputs
            
        except Exception as e:
            lambda_logger.error(f"Failed to get stack outputs: {str(e)}")
            return {}
    
    async def teardown_tenant_resources(self, tenant_id: str) -> Dict[str, Any]:
        """Teardown tenant resources."""
        try:
            stack_name = f"tenant-{tenant_id}-resources"
            
            # Delete CloudFormation stack
            self.cf_client.delete_stack(StackName=stack_name)
            
            # Wait for deletion
            status = await self._wait_for_stack_completion(stack_name)
            
            lambda_logger.info(f"Successfully tore down resources for tenant {tenant_id}")
            
            return {
                'stack_name': stack_name,
                'status': 'DELETED',
                'tenant_id': tenant_id
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to teardown tenant resources: {str(e)}")
            raise SetupException(f"Resource teardown failed: {str(e)}")
```

#### **1.2 Missing Advanced Setup Endpoints**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Feature enhancement

**Current State:**
- Basic setup functionality exists
- Missing resource monitoring
- Missing setup templates management

**Required Endpoints:**

```python
# src/handlers/resource_monitoring.py
"""Resource monitoring handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@setup_resilience("resource_monitoring")
@measure_performance("setup_resource_monitoring")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Monitor tenant resources.
    
    GET /setup/resources/status
    GET /setup/resources/usage
    GET /setup/resources/health
    """
    pass

# src/handlers/setup_templates.py
"""Setup templates management handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@setup_resilience("setup_templates")
@measure_performance("setup_templates")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage setup templates.
    
    GET /setup/templates
    POST /setup/templates
    PUT /setup/templates/{template_id}
    DELETE /setup/templates/{template_id}
    """
    pass

# src/handlers/resource_scaling.py
"""Resource scaling handler."""

@require_auth
@rate_limit(requests_per_minute=20)
@setup_resilience("resource_scaling")
@measure_performance("setup_resource_scaling")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Scale tenant resources.
    
    POST /setup/resources/scale
    GET /setup/resources/scaling-history
    """
    pass
```
