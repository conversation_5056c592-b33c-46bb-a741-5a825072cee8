#!/usr/bin/env python3
# services/payment/src/handlers/get_subscription.py
# Get subscription handler with unified shared layer integration

"""
Get subscription handler.
Handles retrieving current subscription for tenant.
"""

from typing import Any, Dict

# Unified shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import ResourceNotFoundException
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient
from shared.models import UserRole

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService


@require_auth
@rate_limit(requests_per_minute=100)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get current subscription for the tenant.

    GET /payment/subscriptions/{id}
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/payment/subscriptions/{id}'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    # Get subscription ID from path parameters
    subscription_id = event.get('pathParameters', {}).get('id')
    if not subscription_id:
        return APIResponse.validation_error(
            message="Subscription ID is required",
            validation_errors=[{"field": "id", "message": "Subscription ID is required in path"}]
        )

    try:
        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Get subscription details
        subscription_result = subscription_service_instance.get_subscription(subscription_id)

        # Verify subscription belongs to tenant
        if subscription_result.get('tenant_id') != auth_context.tenant_id:
            lambda_logger.warning("Unauthorized subscription access attempt", extra={
                'tenant_id': auth_context.tenant_id,
                'user_id': auth_context.user_id,
                'subscription_id': subscription_id,
                'subscription_tenant_id': subscription_result.get('tenant_id')
            })
            return APIResponse.not_found(
                message="Subscription not found",
                error_code="SUBSCRIPTION_NOT_FOUND"
            )

        # Prepare response data
        response_data = {
            'subscription': subscription_result.get('subscription', {}),
            'plan': subscription_result.get('plan', {}),
            'billing_details': subscription_result.get('billing_details', {}),
            'usage_stats': subscription_result.get('usage_stats', {})
        }

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/subscriptions/{id}",
            method="GET",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/payment/subscriptions/{id}',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Subscription retrieved successfully"
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Subscription not found", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'SUBSCRIPTION_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in get subscription", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while retrieving subscription"
        )
