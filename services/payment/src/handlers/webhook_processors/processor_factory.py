#!/usr/bin/env python3
# services/payment/src/handlers/webhook_processors/processor_factory.py
# Factory for webhook processors

"""
Factory for creating webhook processors based on event type.
Provides centralized management of webhook event processing.
"""

from typing import Dict, Any, Optional
from shared.logger import lambda_logger
from .base_processor import BaseWebhookProcessor
from .payment_failed import PaymentFailedProcessor
from .payment_succeeded import PaymentSucceededProcessor
from .subscription_updated import SubscriptionUpdatedProcessor
from .subscription_deleted import SubscriptionDeletedProcessor


class WebhookProcessorFactory:
    """Factory for creating webhook processors."""
    
    # Mapping of event types to processor classes
    PROCESSOR_MAP = {
        'invoice.payment_failed': PaymentFailedProcessor,
        'invoice.payment_succeeded': PaymentSucceededProcessor,
        'customer.subscription.updated': SubscriptionUpdatedProcessor,
        'customer.subscription.deleted': SubscriptionDeletedProcessor,
    }
    
    # Supported event types
    SUPPORTED_EVENTS = set(PROCESSOR_MAP.keys())
    
    @classmethod
    def create_processor(cls, event_type: str) -> Optional[BaseWebhookProcessor]:
        """
        Create a processor for the given event type.
        
        Args:
            event_type: Stripe event type
            
        Returns:
            Processor instance or None if not supported
        """
        try:
            processor_class = cls.PROCESSOR_MAP.get(event_type)
            
            if processor_class:
                lambda_logger.info("Creating webhook processor", extra={
                    'event_type': event_type,
                    'processor_class': processor_class.__name__
                })
                return processor_class()
            else:
                lambda_logger.warning("No processor found for event type", extra={
                    'event_type': event_type,
                    'supported_events': list(cls.SUPPORTED_EVENTS)
                })
                return None
                
        except Exception as e:
            lambda_logger.error("Failed to create webhook processor", extra={
                'event_type': event_type,
                'error': str(e)
            })
            return None
    
    @classmethod
    def is_supported_event(cls, event_type: str) -> bool:
        """
        Check if the event type is supported.
        
        Args:
            event_type: Stripe event type
            
        Returns:
            True if supported, False otherwise
        """
        return event_type in cls.SUPPORTED_EVENTS
    
    @classmethod
    def get_supported_events(cls) -> list:
        """
        Get list of supported event types.
        
        Returns:
            List of supported event types
        """
        return list(cls.SUPPORTED_EVENTS)
    
    @classmethod
    async def process_webhook_event(cls, event_type: str, event_data: Dict[str, Any],
                                   event_id: str, client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a webhook event using the appropriate processor.
        
        Args:
            event_type: Stripe event type
            event_data: Event data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address
            
        Returns:
            Processing result
        """
        try:
            # Check if event is supported
            if not cls.is_supported_event(event_type):
                lambda_logger.warning("Unsupported webhook event type", extra={
                    'event_type': event_type,
                    'event_id': event_id,
                    'supported_events': list(cls.SUPPORTED_EVENTS)
                })
                
                return {
                    'success': False,
                    'event_type': event_type,
                    'event_id': event_id,
                    'error': 'Unsupported event type',
                    'supported_events': list(cls.SUPPORTED_EVENTS)
                }
            
            # Create processor
            processor = cls.create_processor(event_type)
            if not processor:
                return {
                    'success': False,
                    'event_type': event_type,
                    'event_id': event_id,
                    'error': 'Failed to create processor'
                }
            
            # Process the event
            lambda_logger.info("Processing webhook event", extra={
                'event_type': event_type,
                'event_id': event_id,
                'processor': processor.__class__.__name__
            })
            
            result = await processor.process(event_data, event_id, client_ip)
            
            lambda_logger.info("Webhook event processed successfully", extra={
                'event_type': event_type,
                'event_id': event_id,
                'processor': processor.__class__.__name__,
                'actions_taken': result.get('actions_taken', [])
            })
            
            return result
            
        except Exception as e:
            lambda_logger.error("Failed to process webhook event", extra={
                'event_type': event_type,
                'event_id': event_id,
                'error': str(e),
                'error_type': type(e).__name__
            })
            
            return {
                'success': False,
                'event_type': event_type,
                'event_id': event_id,
                'error': str(e),
                'error_type': type(e).__name__
            }


# Convenience function for direct usage
async def process_stripe_webhook(event_type: str, event_data: Dict[str, Any],
                                event_id: str, client_ip: Optional[str] = None) -> Dict[str, Any]:
    """
    Convenience function to process a Stripe webhook event.
    
    Args:
        event_type: Stripe event type
        event_data: Event data from Stripe
        event_id: Unique event ID
        client_ip: Client IP address
        
    Returns:
        Processing result
    """
    return await WebhookProcessorFactory.process_webhook_event(
        event_type, event_data, event_id, client_ip
    )
