#!/usr/bin/env python3
# services/payment/src/handlers/webhook_processors/subscription_deleted.py
# Subscription deleted webhook processor

"""
Subscription deleted webhook processor for Stripe events.
Handles customer.subscription.deleted events with proper tenant management.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.logger import lambda_logger
from .base_processor import BaseWebhookProcessor


class SubscriptionDeletedProcessor(BaseWebhookProcessor):
    """Processor for customer.subscription.deleted events."""
    
    async def process(self, event_data: Dict[str, Any], event_id: str,
                     client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Process subscription deleted event.
        
        Actions taken:
        1. Update subscription status to CANCELED
        2. Suspend tenant services
        3. Send cancellation confirmation
        4. Schedule data retention/cleanup
        5. Cancel any pending actions
        6. Log event for audit
        
        Args:
            event_data: Subscription data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address
            
        Returns:
            Dict containing processing result
        """
        try:
            subscription_id = event_data.get('id')
            customer_id = event_data.get('customer')
            canceled_at = event_data.get('canceled_at')
            current_period_end = event_data.get('current_period_end')
            cancellation_reason = event_data.get('cancellation_details', {}).get('reason')
            
            lambda_logger.info("Processing subscription deleted event", extra={
                'event_id': event_id,
                'subscription_id': subscription_id,
                'canceled_at': canceled_at,
                'cancellation_reason': cancellation_reason
            })
            
            # Extract tenant ID from metadata
            tenant_id = self.extract_tenant_id(event_data)
            if not tenant_id:
                lambda_logger.warning("No tenant ID found in subscription deleted event", extra={
                    'event_id': event_id,
                    'subscription_id': subscription_id
                })
                tenant_id = 'unknown'
            
            actions_taken = []
            
            # Get subscription info before deletion
            subscription_info = self.get_subscription_from_db(subscription_id)
            
            # 1. Update subscription status to CANCELED
            self.update_subscription_status(
                subscription_id,
                'CANCELED',
                {
                    'canceled_at': datetime.fromtimestamp(canceled_at).isoformat() if canceled_at else datetime.utcnow().isoformat(),
                    'cancellation_reason': cancellation_reason,
                    'final_period_end': datetime.fromtimestamp(current_period_end).isoformat() if current_period_end else None,
                    'updated_at': datetime.utcnow().isoformat()
                }
            )
            actions_taken.append('subscription_marked_canceled')
            
            # 2. Suspend tenant services
            suspension_result = self._suspend_tenant_services(tenant_id, subscription_id)
            if suspension_result:
                actions_taken.append('tenant_services_suspended')
            
            # 3. Send cancellation confirmation
            self._send_cancellation_confirmation(
                tenant_id,
                {
                    'subscription_id': subscription_id,
                    'canceled_at': datetime.fromtimestamp(canceled_at).isoformat() if canceled_at else datetime.utcnow().isoformat(),
                    'final_period_end': datetime.fromtimestamp(current_period_end).isoformat() if current_period_end else None,
                    'cancellation_reason': cancellation_reason,
                    'plan_id': subscription_info.get('plan_id') if subscription_info else None
                }
            )
            actions_taken.append('cancellation_confirmation_sent')
            
            # 4. Schedule data retention/cleanup
            self._schedule_data_retention(tenant_id, subscription_id)
            actions_taken.append('data_retention_scheduled')
            
            # 5. Cancel any pending actions for this tenant
            self._cancel_pending_actions(tenant_id)
            actions_taken.append('pending_actions_cancelled')
            
            # 6. Handle grace period if applicable
            grace_period_end = self._calculate_grace_period_end(current_period_end)
            if grace_period_end:
                self._schedule_final_suspension(tenant_id, grace_period_end)
                actions_taken.append('grace_period_scheduled')
            
            # 7. Log event for audit
            self.log_webhook_event(
                'customer.subscription.deleted',
                event_id,
                tenant_id,
                {
                    'subscription_id': subscription_id,
                    'canceled_at': canceled_at,
                    'cancellation_reason': cancellation_reason,
                    'current_period_end': current_period_end,
                    'grace_period_end': grace_period_end.isoformat() if grace_period_end else None,
                    'actions_taken': actions_taken
                }
            )
            
            lambda_logger.info("Subscription deleted event processed successfully", extra={
                'event_id': event_id,
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'actions_taken': actions_taken
            })
            
            return {
                'success': True,
                'event_type': 'customer.subscription.deleted',
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'canceled_at': datetime.fromtimestamp(canceled_at).isoformat() if canceled_at else datetime.utcnow().isoformat(),
                'cancellation_reason': cancellation_reason,
                'actions_taken': actions_taken,
                'grace_period_end': grace_period_end.isoformat() if grace_period_end else None,
                'next_steps': self._get_next_steps_for_tenant(tenant_id)
            }
            
        except Exception as e:
            lambda_logger.error("Failed to process subscription deleted event", extra={
                'event_id': event_id,
                'error': str(e),
                'subscription_id': event_data.get('id')
            })
            raise
    
    def _suspend_tenant_services(self, tenant_id: str, subscription_id: str) -> bool:
        """Suspend tenant services after subscription cancellation."""
        try:
            # Get tenant info
            tenant_info = self.get_tenant_info(tenant_id)
            if not tenant_info:
                lambda_logger.warning("Cannot suspend services - tenant not found", extra={
                    'tenant_id': tenant_id
                })
                return False
            
            # Update tenant status to suspended
            # In a real implementation, this would:
            # - Disable API access for non-admin users
            # - Restrict access to billing and account management only
            # - Stop all background jobs and processing
            # - Disable agent services
            
            lambda_logger.info("Tenant services suspended", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'previous_status': tenant_info.get('status')
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to suspend tenant services", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'error': str(e)
            })
            return False
    
    def _send_cancellation_confirmation(self, tenant_id: str, data: Dict[str, Any]) -> None:
        """Send cancellation confirmation email."""
        try:
            tenant_info = self.get_tenant_info(tenant_id)
            if not tenant_info:
                lambda_logger.warning("Cannot send confirmation - tenant not found", extra={
                    'tenant_id': tenant_id
                })
                return
            
            email_data = {
                'tenant_id': tenant_id,
                'company_name': tenant_info.get('company_name', 'Your Company'),
                'email': tenant_info.get('email'),
                'subscription_id': data['subscription_id'],
                'canceled_at': data['canceled_at'],
                'final_period_end': data['final_period_end'],
                'cancellation_reason': data['cancellation_reason'],
                'plan_id': data['plan_id']
            }
            
            self.send_notification_email(
                tenant_id,
                'subscription_canceled',
                email_data
            )
            
        except Exception as e:
            lambda_logger.error("Failed to send cancellation confirmation", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
    
    def _schedule_data_retention(self, tenant_id: str, subscription_id: str) -> None:
        """Schedule data retention and cleanup processes."""
        try:
            # Schedule data export (30 days)
            self.schedule_tenant_action(
                tenant_id,
                'export_tenant_data',
                delay_days=30
            )
            
            # Schedule data deletion (90 days)
            self.schedule_tenant_action(
                tenant_id,
                'delete_tenant_data',
                delay_days=90
            )
            
            lambda_logger.info("Data retention scheduled", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'export_scheduled': '30 days',
                'deletion_scheduled': '90 days'
            })
            
        except Exception as e:
            lambda_logger.error("Failed to schedule data retention", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'error': str(e)
            })
    
    def _cancel_pending_actions(self, tenant_id: str) -> None:
        """Cancel any pending actions for the tenant."""
        try:
            # Cancel pending payment reminders
            # Cancel pending suspension actions
            # Cancel pending notifications
            
            lambda_logger.info("Pending actions cancelled", extra={
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cancel pending actions", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
    
    def _calculate_grace_period_end(self, current_period_end: Optional[int]) -> Optional[datetime]:
        """Calculate grace period end date."""
        try:
            if not current_period_end:
                return None
            
            # Grace period: 7 days after the current period end
            from datetime import timedelta
            period_end = datetime.fromtimestamp(current_period_end)
            grace_period_end = period_end + timedelta(days=7)
            
            return grace_period_end
            
        except Exception as e:
            lambda_logger.error("Failed to calculate grace period", extra={
                'current_period_end': current_period_end,
                'error': str(e)
            })
            return None
    
    def _schedule_final_suspension(self, tenant_id: str, grace_period_end: datetime) -> None:
        """Schedule final suspension after grace period."""
        try:
            # Calculate delay in days
            delay_days = (grace_period_end - datetime.utcnow()).days
            
            if delay_days > 0:
                self.schedule_tenant_action(
                    tenant_id,
                    'final_suspension',
                    delay_days=delay_days
                )
                
                lambda_logger.info("Final suspension scheduled", extra={
                    'tenant_id': tenant_id,
                    'grace_period_end': grace_period_end.isoformat(),
                    'delay_days': delay_days
                })
            
        except Exception as e:
            lambda_logger.error("Failed to schedule final suspension", extra={
                'tenant_id': tenant_id,
                'grace_period_end': grace_period_end.isoformat() if grace_period_end else None,
                'error': str(e)
            })
    
    def _get_next_steps_for_tenant(self, tenant_id: str) -> List[str]:
        """Get next steps for the tenant after cancellation."""
        return [
            "Your subscription has been canceled",
            "You have 7 days of grace period with limited access",
            "Your data will be available for export for 30 days",
            "Contact support if you want to reactivate your subscription",
            "All data will be permanently deleted after 90 days"
        ]
