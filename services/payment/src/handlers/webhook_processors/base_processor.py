#!/usr/bin/env python3
# services/payment/src/handlers/webhook_processors/base_processor.py
# Base webhook processor for Stripe events

"""
Base webhook processor for Stripe events.
Provides common functionality for all webhook event processors.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

from shared.logger import lambda_logger, audit_log
from shared.exceptions import PaymentException, ValidationException, ExternalServiceException
from shared.database import db_client


class BaseWebhookProcessor(ABC):
    """Base class for Stripe webhook event processors."""
    
    def __init__(self):
        """Initialize base processor."""
        self.db_client = db_client
    
    @abstractmethod
    async def process(self, event_data: Dict[str, Any], event_id: str, 
                     client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Process the webhook event.
        
        Args:
            event_data: Event data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address
            
        Returns:
            Dict containing processing result
        """
        pass
    
    def extract_tenant_id(self, event_data: Dict[str, Any]) -> Optional[str]:
        """Extract tenant ID from Stripe object metadata."""
        metadata = event_data.get('metadata', {})
        return metadata.get('tenant_id')
    
    def extract_subscription_id(self, event_data: Dict[str, Any]) -> Optional[str]:
        """Extract subscription ID from event data."""
        # For subscription events
        if 'id' in event_data and event_data.get('object') == 'subscription':
            return event_data['id']
        
        # For invoice events
        if 'subscription' in event_data:
            return event_data['subscription']
        
        return None
    
    def extract_customer_id(self, event_data: Dict[str, Any]) -> Optional[str]:
        """Extract customer ID from event data."""
        return event_data.get('customer')
    
    def log_webhook_event(self, event_type: str, event_id: str, tenant_id: Optional[str],
                         details: Dict[str, Any]) -> None:
        """Log webhook event for audit trail."""
        try:
            # Store webhook event in database for audit
            webhook_log = {
                'event_id': event_id,
                'event_type': event_type,
                'tenant_id': tenant_id,
                'processed_at': datetime.utcnow().isoformat(),
                'details': details,
                'status': 'processed'
            }
            
            # In a real implementation, this would save to a webhook_events table
            lambda_logger.info("Webhook event logged", extra=webhook_log)
            
            # Audit log
            audit_log("stripe_webhook_processed", {
                'event_id': event_id,
                'event_type': event_type,
                'tenant_id': tenant_id,
                'details': details
            })
            
        except Exception as e:
            lambda_logger.error("Failed to log webhook event", extra={
                'event_id': event_id,
                'event_type': event_type,
                'error': str(e)
            })
    
    def get_subscription_from_db(self, subscription_id: str) -> Optional[Dict[str, Any]]:
        """Get subscription details from database."""
        try:
            # In a real implementation, this would query the subscriptions table
            # For now, return mock data
            return {
                'subscription_id': subscription_id,
                'tenant_id': 'tenant_123',
                'status': 'active',
                'plan_id': 'plan_pro',
                'current_period_start': datetime.utcnow().isoformat(),
                'current_period_end': datetime.utcnow().isoformat()
            }
        except Exception as e:
            lambda_logger.error("Failed to get subscription from database", extra={
                'subscription_id': subscription_id,
                'error': str(e)
            })
            return None
    
    def update_subscription_status(self, subscription_id: str, status: str, 
                                  details: Optional[Dict[str, Any]] = None) -> None:
        """Update subscription status in database."""
        try:
            update_data = {
                'status': status,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            if details:
                update_data.update(details)
            
            # In a real implementation, this would update the subscriptions table
            lambda_logger.info("Subscription status updated", extra={
                'subscription_id': subscription_id,
                'status': status,
                'details': details
            })
            
        except Exception as e:
            lambda_logger.error("Failed to update subscription status", extra={
                'subscription_id': subscription_id,
                'status': status,
                'error': str(e)
            })
            raise PaymentException(f"Failed to update subscription status: {str(e)}")
    
    def send_notification_email(self, tenant_id: str, email_type: str, 
                               data: Dict[str, Any]) -> None:
        """Send notification email to tenant."""
        try:
            # In a real implementation, this would integrate with SES or email service
            lambda_logger.info("Notification email sent", extra={
                'tenant_id': tenant_id,
                'email_type': email_type,
                'data': data
            })
            
        except Exception as e:
            lambda_logger.warning("Failed to send notification email", extra={
                'tenant_id': tenant_id,
                'email_type': email_type,
                'error': str(e)
            })
    
    def schedule_tenant_action(self, tenant_id: str, action: str, 
                              delay_days: int = 0) -> None:
        """Schedule an action for a tenant (e.g., suspension, reminder)."""
        try:
            scheduled_time = datetime.utcnow()
            if delay_days > 0:
                from datetime import timedelta
                scheduled_time += timedelta(days=delay_days)
            
            # In a real implementation, this would create a scheduled job
            lambda_logger.info("Tenant action scheduled", extra={
                'tenant_id': tenant_id,
                'action': action,
                'scheduled_time': scheduled_time.isoformat(),
                'delay_days': delay_days
            })
            
        except Exception as e:
            lambda_logger.error("Failed to schedule tenant action", extra={
                'tenant_id': tenant_id,
                'action': action,
                'error': str(e)
            })
    
    def get_tenant_info(self, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get tenant information from database."""
        try:
            # In a real implementation, this would query the tenants table
            return {
                'tenant_id': tenant_id,
                'company_name': 'Test Company',
                'email': '<EMAIL>',
                'status': 'active'
            }
        except Exception as e:
            lambda_logger.error("Failed to get tenant info", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    def increment_failed_attempts(self, subscription_id: str) -> int:
        """Increment failed payment attempts counter."""
        try:
            # In a real implementation, this would update the database
            # For now, return a mock value
            failed_attempts = 1
            
            lambda_logger.info("Failed attempts incremented", extra={
                'subscription_id': subscription_id,
                'failed_attempts': failed_attempts
            })
            
            return failed_attempts
            
        except Exception as e:
            lambda_logger.error("Failed to increment failed attempts", extra={
                'subscription_id': subscription_id,
                'error': str(e)
            })
            return 0
    
    def reset_failed_attempts(self, subscription_id: str) -> None:
        """Reset failed payment attempts counter."""
        try:
            # In a real implementation, this would update the database
            lambda_logger.info("Failed attempts reset", extra={
                'subscription_id': subscription_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to reset failed attempts", extra={
                'subscription_id': subscription_id,
                'error': str(e)
            })
