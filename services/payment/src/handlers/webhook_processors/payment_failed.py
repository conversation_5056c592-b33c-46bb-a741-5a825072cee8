#!/usr/bin/env python3
# services/payment/src/handlers/webhook_processors/payment_failed.py
# Payment failed webhook processor

"""
Payment failed webhook processor for Stripe events.
Handles invoice.payment_failed events with proper tenant management.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.logger import lambda_logger
from .base_processor import BaseWebhookProcessor


class PaymentFailedProcessor(BaseWebhookProcessor):
    """Processor for invoice.payment_failed events."""
    
    async def process(self, event_data: Dict[str, Any], event_id: str,
                     client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Process payment failed event.
        
        Actions taken:
        1. Update subscription status to PAST_DUE
        2. Increment failed attempts counter
        3. Send payment failed notification
        4. Schedule suspension if max attempts reached
        5. Log event for audit
        
        Args:
            event_data: Invoice data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address
            
        Returns:
            Dict containing processing result
        """
        try:
            invoice_id = event_data.get('id')
            subscription_id = event_data.get('subscription')
            customer_id = event_data.get('customer')
            amount_due = event_data.get('amount_due', 0)
            currency = event_data.get('currency', 'usd')
            attempt_count = event_data.get('attempt_count', 1)
            next_payment_attempt = event_data.get('next_payment_attempt')
            
            lambda_logger.info("Processing payment failed event", extra={
                'event_id': event_id,
                'invoice_id': invoice_id,
                'subscription_id': subscription_id,
                'amount_due': amount_due,
                'attempt_count': attempt_count
            })
            
            # Extract tenant ID from metadata
            tenant_id = self.extract_tenant_id(event_data)
            if not tenant_id:
                lambda_logger.warning("No tenant ID found in payment failed event", extra={
                    'event_id': event_id,
                    'invoice_id': invoice_id
                })
                tenant_id = 'unknown'
            
            actions_taken = []
            
            # 1. Update subscription status to PAST_DUE
            if subscription_id:
                self.update_subscription_status(
                    subscription_id, 
                    'PAST_DUE',
                    {
                        'last_payment_failure': datetime.utcnow().isoformat(),
                        'failed_invoice_id': invoice_id,
                        'amount_due': amount_due,
                        'currency': currency
                    }
                )
                actions_taken.append('subscription_marked_past_due')
            
            # 2. Increment failed attempts counter
            failed_attempts = self.increment_failed_attempts(subscription_id)
            actions_taken.append(f'failed_attempts_incremented_to_{failed_attempts}')
            
            # 3. Send payment failed notification
            self._send_payment_failed_notification(
                tenant_id, 
                {
                    'invoice_id': invoice_id,
                    'amount': amount_due / 100,  # Convert from cents
                    'currency': currency.upper(),
                    'attempt_count': attempt_count,
                    'failed_attempts': failed_attempts,
                    'next_payment_attempt': next_payment_attempt
                }
            )
            actions_taken.append('payment_failed_notification_sent')
            
            # 4. Schedule suspension if max attempts reached
            max_attempts = 3  # Configurable
            if failed_attempts >= max_attempts:
                self.schedule_tenant_action(
                    tenant_id, 
                    'suspend_for_non_payment',
                    delay_days=1  # Grace period
                )
                actions_taken.append('suspension_scheduled')
                
                # Send final warning
                self._send_final_warning_notification(tenant_id, {
                    'failed_attempts': failed_attempts,
                    'suspension_date': 'in 1 day'
                })
                actions_taken.append('final_warning_sent')
            else:
                # Schedule retry reminder
                self.schedule_tenant_action(
                    tenant_id,
                    'send_payment_reminder',
                    delay_days=1
                )
                actions_taken.append('payment_reminder_scheduled')
            
            # 5. Log event for audit
            self.log_webhook_event(
                'invoice.payment_failed',
                event_id,
                tenant_id,
                {
                    'invoice_id': invoice_id,
                    'subscription_id': subscription_id,
                    'amount_due': amount_due,
                    'currency': currency,
                    'attempt_count': attempt_count,
                    'failed_attempts': failed_attempts,
                    'actions_taken': actions_taken
                }
            )
            
            lambda_logger.info("Payment failed event processed successfully", extra={
                'event_id': event_id,
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'failed_attempts': failed_attempts,
                'actions_taken': actions_taken
            })
            
            return {
                'success': True,
                'event_type': 'invoice.payment_failed',
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'invoice_id': invoice_id,
                'failed_attempts': failed_attempts,
                'actions_taken': actions_taken,
                'next_steps': self._get_next_steps(failed_attempts, max_attempts)
            }
            
        except Exception as e:
            lambda_logger.error("Failed to process payment failed event", extra={
                'event_id': event_id,
                'error': str(e),
                'invoice_id': event_data.get('id'),
                'subscription_id': event_data.get('subscription')
            })
            raise
    
    def _send_payment_failed_notification(self, tenant_id: str, data: Dict[str, Any]) -> None:
        """Send payment failed notification email."""
        try:
            tenant_info = self.get_tenant_info(tenant_id)
            if not tenant_info:
                lambda_logger.warning("Cannot send notification - tenant not found", extra={
                    'tenant_id': tenant_id
                })
                return
            
            email_data = {
                'tenant_id': tenant_id,
                'company_name': tenant_info.get('company_name', 'Your Company'),
                'email': tenant_info.get('email'),
                'amount': data['amount'],
                'currency': data['currency'],
                'attempt_count': data['attempt_count'],
                'failed_attempts': data['failed_attempts'],
                'invoice_id': data['invoice_id']
            }
            
            self.send_notification_email(
                tenant_id,
                'payment_failed',
                email_data
            )
            
        except Exception as e:
            lambda_logger.error("Failed to send payment failed notification", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
    
    def _send_final_warning_notification(self, tenant_id: str, data: Dict[str, Any]) -> None:
        """Send final warning before suspension."""
        try:
            tenant_info = self.get_tenant_info(tenant_id)
            if not tenant_info:
                return
            
            email_data = {
                'tenant_id': tenant_id,
                'company_name': tenant_info.get('company_name', 'Your Company'),
                'email': tenant_info.get('email'),
                'failed_attempts': data['failed_attempts'],
                'suspension_date': data['suspension_date']
            }
            
            self.send_notification_email(
                tenant_id,
                'final_payment_warning',
                email_data
            )
            
        except Exception as e:
            lambda_logger.error("Failed to send final warning notification", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
    
    def _get_next_steps(self, failed_attempts: int, max_attempts: int) -> List[str]:
        """Get next steps based on failed attempts."""
        if failed_attempts >= max_attempts:
            return [
                "Account will be suspended in 1 day",
                "Update payment method immediately",
                "Contact support if you need assistance"
            ]
        else:
            remaining_attempts = max_attempts - failed_attempts
            return [
                f"Payment will be retried automatically",
                f"{remaining_attempts} attempts remaining before suspension",
                "Update payment method to avoid service interruption",
                "Check your email for payment instructions"
            ]
