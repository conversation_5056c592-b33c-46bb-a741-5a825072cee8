# 🔗 Stripe Webhook Processors

## 📋 **Overview**

This module contains specialized processors for handling critical Stripe webhook events. Each processor implements specific business logic for different event types, ensuring proper tenant management and service continuity.

## 🏗️ **Architecture**

### **Base Processor**
- `BaseWebhookProcessor`: Abstract base class with common functionality
- Provides database operations, email notifications, and audit logging
- Ensures consistent error handling and logging across all processors

### **Event Processors**
- `PaymentFailedProcessor`: Handles `invoice.payment_failed` events
- `PaymentSucceededProcessor`: Handles `invoice.payment_succeeded` events  
- `SubscriptionUpdatedProcessor`: Handles `customer.subscription.updated` events
- `SubscriptionDeletedProcessor`: Handles `customer.subscription.deleted` events

### **Factory Pattern**
- `WebhookProcessorFactory`: Creates appropriate processors based on event type
- Centralizes event routing and processor management
- Provides fallback to legacy processing for unsupported events

## 🔧 **Supported Events**

### **invoice.payment_failed**
**Actions Taken:**
- Update subscription status to `PAST_DUE`
- Increment failed attempts counter
- Send payment failed notification email
- Schedule suspension if max attempts reached (3)
- Schedule payment reminders

**Business Logic:**
- Grace period: 3 failed attempts before suspension
- Suspension delay: 1 day after final failure
- Automatic retry scheduling

### **invoice.payment_succeeded**
**Actions Taken:**
- Update subscription status to `ACTIVE`
- Reset failed attempts counter
- Reactivate tenant if suspended
- Send payment confirmation email
- Update billing period information
- Cancel pending suspension actions

**Business Logic:**
- Immediate service reactivation
- Billing period updates
- Suspension cancellation

### **customer.subscription.updated**
**Actions Taken:**
- Update subscription details in database
- Handle status changes (active, past_due, canceled, etc.)
- Process plan changes and update tenant limits
- Send notifications for significant changes
- Handle cancellation scheduling

**Business Logic:**
- Status transition handling
- Plan upgrade/downgrade processing
- Cancellation scheduling management

### **customer.subscription.deleted**
**Actions Taken:**
- Update subscription status to `CANCELED`
- Suspend tenant services
- Send cancellation confirmation
- Schedule data retention/cleanup
- Cancel pending actions
- Implement grace period (7 days)

**Business Logic:**
- Immediate service suspension
- Data retention: 30 days for export, 90 days for deletion
- Grace period for reactivation

## 📊 **Data Flow**

```mermaid
sequenceDiagram
    participant S as Stripe
    participant W as Webhook Handler
    participant F as Processor Factory
    participant P as Event Processor
    participant D as Database
    participant E as Email Service
    participant T as Tenant Service
    
    S->>W: Webhook Event
    W->>W: Verify Signature
    W->>F: Route Event
    F->>P: Create Processor
    P->>D: Update Records
    P->>E: Send Notifications
    P->>T: Update Tenant Status
    P->>W: Return Result
    W->>S: Acknowledge
```

## 🔒 **Security**

### **Signature Verification**
- Uses Stripe's official signature verification
- Validates webhook authenticity
- Prevents replay attacks

### **Data Protection**
- Tenant ID extraction from metadata
- Secure database operations
- Audit logging for all actions

### **Error Handling**
- Graceful degradation for failures
- Comprehensive error logging
- Retry mechanisms for critical operations

## 📝 **Configuration**

### **Environment Variables**
```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Database
DYNAMODB_TABLE=agent-scl-main-dev

# Email
EMAIL_FROM=<EMAIL>
```

### **Webhook Endpoint**
```
POST /payment/webhooks/stripe
Content-Type: application/json
Stripe-Signature: t=1234567890,v1=...
```

## 🧪 **Testing**

### **Unit Tests**
```bash
# Test individual processors
pytest tests/unit/webhook_processors/

# Test specific processor
pytest tests/unit/webhook_processors/test_payment_failed.py
```

### **Integration Tests**
```bash
# Test webhook flow end-to-end
pytest tests/integration/test_webhook_flow.py

# Test with Stripe CLI
stripe listen --forward-to localhost:3000/payment/webhooks/stripe
stripe trigger invoice.payment_failed
```

### **Mock Events**
```python
# Example test event
payment_failed_event = {
    "id": "evt_test_webhook",
    "type": "invoice.payment_failed",
    "data": {
        "object": {
            "id": "in_test_invoice",
            "subscription": "sub_test_subscription",
            "amount_due": 2999,
            "currency": "usd",
            "attempt_count": 1,
            "metadata": {
                "tenant_id": "tenant_123"
            }
        }
    }
}
```

## 📊 **Monitoring**

### **CloudWatch Metrics**
- `WebhookEventsProcessed` - Total events processed
- `WebhookProcessingDuration` - Processing time per event
- `WebhookErrors` - Failed webhook processing
- `TenantSuspensions` - Number of tenant suspensions
- `PaymentFailures` - Payment failure events

### **Alarms**
- High webhook error rate (>5% in 5 minutes)
- Long processing times (>30 seconds)
- Failed tenant notifications
- Database operation failures

## 🔄 **Business Rules**

### **Payment Failures**
- **Attempt 1**: Send payment failed notification
- **Attempt 2**: Send payment failed notification + reminder
- **Attempt 3**: Send final warning + schedule suspension
- **After 3 failures**: Suspend tenant services (1 day grace period)

### **Subscription Cancellation**
- **Immediate**: Suspend services, send confirmation
- **Grace Period**: 7 days limited access
- **Data Retention**: 30 days for export, 90 days total
- **Final Cleanup**: Permanent data deletion

### **Status Transitions**
- `active` → `past_due`: Send notification, maintain access
- `past_due` → `unpaid`: Restrict access, send warning
- `unpaid` → `active`: Restore full access, send confirmation
- `active` → `canceled`: Immediate suspension, start retention

## 🚀 **Deployment**

### **Webhook Configuration**
1. Configure webhook endpoint in Stripe Dashboard
2. Set webhook secret in environment variables
3. Enable required event types:
   - `invoice.payment_failed`
   - `invoice.payment_succeeded`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

### **Monitoring Setup**
1. Configure CloudWatch alarms
2. Set up notification channels
3. Create dashboard for webhook metrics
4. Enable audit logging

## 🐛 **Troubleshooting**

### **Common Issues**

**Webhook Signature Verification Fails**
- Check webhook secret configuration
- Verify endpoint URL in Stripe
- Check for proxy/load balancer modifications

**Events Not Processing**
- Verify event type is supported
- Check processor factory configuration
- Review error logs for exceptions

**Database Updates Failing**
- Check DynamoDB permissions
- Verify table configuration
- Review connection settings

### **Debug Commands**
```bash
# Check webhook logs
aws logs filter-log-events \
  --log-group-name /aws/lambda/payment-dev-stripeWebhook \
  --start-time 1692000000000

# Test webhook locally
curl -X POST http://localhost:3000/payment/webhooks/stripe \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: t=1234567890,v1=test" \
  -d @test_webhook_event.json
```

## 📈 **Future Enhancements**

- [ ] Support for additional Stripe events
- [ ] Advanced retry mechanisms with exponential backoff
- [ ] Webhook event replay functionality
- [ ] Real-time webhook processing dashboard
- [ ] Custom business rules engine
- [ ] Integration with external notification services
