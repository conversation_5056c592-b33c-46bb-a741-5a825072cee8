#!/usr/bin/env python3
# services/payment/src/handlers/webhook_processors/subscription_updated.py
# Subscription updated webhook processor

"""
Subscription updated webhook processor for Stripe events.
Handles customer.subscription.updated events with proper tenant management.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.logger import lambda_logger
from .base_processor import BaseWebhookProcessor


class SubscriptionUpdatedProcessor(BaseWebhookProcessor):
    """Processor for customer.subscription.updated events."""
    
    async def process(self, event_data: Dict[str, Any], event_id: str,
                     client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Process subscription updated event.
        
        Actions taken:
        1. Update subscription details in database
        2. Handle status changes (active, past_due, canceled, etc.)
        3. Update plan changes
        4. Send notifications for significant changes
        5. Log event for audit
        
        Args:
            event_data: Subscription data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address
            
        Returns:
            Dict containing processing result
        """
        try:
            subscription_id = event_data.get('id')
            customer_id = event_data.get('customer')
            status = event_data.get('status')
            current_period_start = event_data.get('current_period_start')
            current_period_end = event_data.get('current_period_end')
            cancel_at_period_end = event_data.get('cancel_at_period_end', False)
            canceled_at = event_data.get('canceled_at')
            trial_start = event_data.get('trial_start')
            trial_end = event_data.get('trial_end')
            
            # Get plan information
            items = event_data.get('items', {}).get('data', [])
            plan_info = items[0] if items else {}
            plan_id = plan_info.get('price', {}).get('id') if plan_info else None
            quantity = plan_info.get('quantity', 1) if plan_info else 1
            
            lambda_logger.info("Processing subscription updated event", extra={
                'event_id': event_id,
                'subscription_id': subscription_id,
                'status': status,
                'plan_id': plan_id,
                'cancel_at_period_end': cancel_at_period_end
            })
            
            # Extract tenant ID from metadata
            tenant_id = self.extract_tenant_id(event_data)
            if not tenant_id:
                lambda_logger.warning("No tenant ID found in subscription updated event", extra={
                    'event_id': event_id,
                    'subscription_id': subscription_id
                })
                tenant_id = 'unknown'
            
            actions_taken = []
            
            # Get previous subscription state for comparison
            previous_subscription = self.get_subscription_from_db(subscription_id)
            previous_status = previous_subscription.get('status') if previous_subscription else None
            previous_plan_id = previous_subscription.get('plan_id') if previous_subscription else None
            
            # 1. Update subscription details in database
            subscription_updates = {
                'status': status,
                'plan_id': plan_id,
                'quantity': quantity,
                'current_period_start': datetime.fromtimestamp(current_period_start).isoformat() if current_period_start else None,
                'current_period_end': datetime.fromtimestamp(current_period_end).isoformat() if current_period_end else None,
                'cancel_at_period_end': cancel_at_period_end,
                'canceled_at': datetime.fromtimestamp(canceled_at).isoformat() if canceled_at else None,
                'trial_start': datetime.fromtimestamp(trial_start).isoformat() if trial_start else None,
                'trial_end': datetime.fromtimestamp(trial_end).isoformat() if trial_end else None,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            self.update_subscription_status(subscription_id, status, subscription_updates)
            actions_taken.append('subscription_details_updated')
            
            # 2. Handle status changes
            if previous_status and previous_status != status:
                status_change_actions = self._handle_status_change(
                    tenant_id, subscription_id, previous_status, status
                )
                actions_taken.extend(status_change_actions)
            
            # 3. Handle plan changes
            if previous_plan_id and previous_plan_id != plan_id:
                plan_change_actions = self._handle_plan_change(
                    tenant_id, subscription_id, previous_plan_id, plan_id
                )
                actions_taken.extend(plan_change_actions)
            
            # 4. Handle cancellation scheduling
            if cancel_at_period_end and not previous_subscription.get('cancel_at_period_end', False):
                cancellation_actions = self._handle_cancellation_scheduled(
                    tenant_id, subscription_id, current_period_end
                )
                actions_taken.extend(cancellation_actions)
            
            # 5. Log event for audit
            self.log_webhook_event(
                'customer.subscription.updated',
                event_id,
                tenant_id,
                {
                    'subscription_id': subscription_id,
                    'status': status,
                    'previous_status': previous_status,
                    'plan_id': plan_id,
                    'previous_plan_id': previous_plan_id,
                    'cancel_at_period_end': cancel_at_period_end,
                    'actions_taken': actions_taken
                }
            )
            
            lambda_logger.info("Subscription updated event processed successfully", extra={
                'event_id': event_id,
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'status': status,
                'actions_taken': actions_taken
            })
            
            return {
                'success': True,
                'event_type': 'customer.subscription.updated',
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'status': status,
                'plan_id': plan_id,
                'actions_taken': actions_taken,
                'changes_detected': {
                    'status_changed': previous_status != status if previous_status else False,
                    'plan_changed': previous_plan_id != plan_id if previous_plan_id else False,
                    'cancellation_scheduled': cancel_at_period_end
                }
            }
            
        except Exception as e:
            lambda_logger.error("Failed to process subscription updated event", extra={
                'event_id': event_id,
                'error': str(e),
                'subscription_id': event_data.get('id')
            })
            raise
    
    def _handle_status_change(self, tenant_id: str, subscription_id: str,
                             previous_status: str, new_status: str) -> List[str]:
        """Handle subscription status changes."""
        actions = []
        
        try:
            lambda_logger.info("Handling subscription status change", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'previous_status': previous_status,
                'new_status': new_status
            })
            
            # Handle specific status transitions
            if new_status == 'active' and previous_status in ['past_due', 'unpaid']:
                # Subscription reactivated
                self._reactivate_tenant_services(tenant_id)
                self._send_reactivation_notification(tenant_id)
                actions.extend(['tenant_services_reactivated', 'reactivation_notification_sent'])
                
            elif new_status == 'past_due' and previous_status == 'active':
                # Subscription past due
                self._send_past_due_notification(tenant_id)
                actions.append('past_due_notification_sent')
                
            elif new_status == 'unpaid' and previous_status in ['active', 'past_due']:
                # Subscription unpaid - more serious
                self._restrict_tenant_access(tenant_id)
                self._send_unpaid_notification(tenant_id)
                actions.extend(['tenant_access_restricted', 'unpaid_notification_sent'])
                
            elif new_status == 'canceled':
                # Subscription canceled
                self._handle_subscription_cancellation(tenant_id, subscription_id)
                actions.append('subscription_cancellation_handled')
            
            return actions
            
        except Exception as e:
            lambda_logger.error("Failed to handle status change", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'error': str(e)
            })
            return actions
    
    def _handle_plan_change(self, tenant_id: str, subscription_id: str,
                           previous_plan_id: str, new_plan_id: str) -> List[str]:
        """Handle subscription plan changes."""
        actions = []
        
        try:
            lambda_logger.info("Handling subscription plan change", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'previous_plan_id': previous_plan_id,
                'new_plan_id': new_plan_id
            })
            
            # Update tenant limits based on new plan
            self._update_tenant_limits(tenant_id, new_plan_id)
            actions.append('tenant_limits_updated')
            
            # Send plan change notification
            self._send_plan_change_notification(tenant_id, previous_plan_id, new_plan_id)
            actions.append('plan_change_notification_sent')
            
            return actions
            
        except Exception as e:
            lambda_logger.error("Failed to handle plan change", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'error': str(e)
            })
            return actions
    
    def _handle_cancellation_scheduled(self, tenant_id: str, subscription_id: str,
                                     cancellation_date: int) -> List[str]:
        """Handle subscription cancellation scheduling."""
        actions = []
        
        try:
            cancellation_datetime = datetime.fromtimestamp(cancellation_date)
            
            lambda_logger.info("Handling subscription cancellation scheduling", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'cancellation_date': cancellation_datetime.isoformat()
            })
            
            # Schedule cancellation notification
            self.schedule_tenant_action(
                tenant_id,
                'send_cancellation_reminder',
                delay_days=7  # Remind 7 days before cancellation
            )
            actions.append('cancellation_reminder_scheduled')
            
            # Send immediate notification
            self._send_cancellation_scheduled_notification(tenant_id, cancellation_datetime)
            actions.append('cancellation_scheduled_notification_sent')
            
            return actions
            
        except Exception as e:
            lambda_logger.error("Failed to handle cancellation scheduling", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'error': str(e)
            })
            return actions
    
    def _reactivate_tenant_services(self, tenant_id: str) -> None:
        """Reactivate tenant services after subscription reactivation."""
        # Implementation would restore full access
        lambda_logger.info("Tenant services reactivated", extra={'tenant_id': tenant_id})
    
    def _restrict_tenant_access(self, tenant_id: str) -> None:
        """Restrict tenant access for unpaid subscription."""
        # Implementation would limit access to billing-only
        lambda_logger.info("Tenant access restricted", extra={'tenant_id': tenant_id})
    
    def _update_tenant_limits(self, tenant_id: str, plan_id: str) -> None:
        """Update tenant limits based on new plan."""
        # Implementation would update usage limits
        lambda_logger.info("Tenant limits updated", extra={'tenant_id': tenant_id, 'plan_id': plan_id})
    
    def _handle_subscription_cancellation(self, tenant_id: str, subscription_id: str) -> None:
        """Handle subscription cancellation."""
        # Implementation would handle final cancellation
        lambda_logger.info("Subscription cancellation handled", extra={
            'tenant_id': tenant_id,
            'subscription_id': subscription_id
        })
    
    # Notification methods
    def _send_reactivation_notification(self, tenant_id: str) -> None:
        """Send reactivation notification."""
        self.send_notification_email(tenant_id, 'subscription_reactivated', {})
    
    def _send_past_due_notification(self, tenant_id: str) -> None:
        """Send past due notification."""
        self.send_notification_email(tenant_id, 'subscription_past_due', {})
    
    def _send_unpaid_notification(self, tenant_id: str) -> None:
        """Send unpaid notification."""
        self.send_notification_email(tenant_id, 'subscription_unpaid', {})
    
    def _send_plan_change_notification(self, tenant_id: str, previous_plan_id: str, new_plan_id: str) -> None:
        """Send plan change notification."""
        self.send_notification_email(tenant_id, 'plan_changed', {
            'previous_plan_id': previous_plan_id,
            'new_plan_id': new_plan_id
        })
    
    def _send_cancellation_scheduled_notification(self, tenant_id: str, cancellation_date: datetime) -> None:
        """Send cancellation scheduled notification."""
        self.send_notification_email(tenant_id, 'cancellation_scheduled', {
            'cancellation_date': cancellation_date.isoformat()
        })
