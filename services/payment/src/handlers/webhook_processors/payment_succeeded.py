#!/usr/bin/env python3
# services/payment/src/handlers/webhook_processors/payment_succeeded.py
# Payment succeeded webhook processor

"""
Payment succeeded webhook processor for Stripe events.
Handles invoice.payment_succeeded events with proper tenant management.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.logger import lambda_logger
from .base_processor import BaseWebhookProcessor


class PaymentSucceededProcessor(BaseWebhookProcessor):
    """Processor for invoice.payment_succeeded events."""
    
    async def process(self, event_data: Dict[str, Any], event_id: str,
                     client_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Process payment succeeded event.
        
        Actions taken:
        1. Update subscription status to ACTIVE
        2. Reset failed attempts counter
        3. Reactivate tenant if suspended
        4. Send payment confirmation
        5. Update billing period
        6. Log event for audit
        
        Args:
            event_data: Invoice data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address
            
        Returns:
            Dict containing processing result
        """
        try:
            invoice_id = event_data.get('id')
            subscription_id = event_data.get('subscription')
            customer_id = event_data.get('customer')
            amount_paid = event_data.get('amount_paid', 0)
            currency = event_data.get('currency', 'usd')
            period_start = event_data.get('period_start')
            period_end = event_data.get('period_end')
            hosted_invoice_url = event_data.get('hosted_invoice_url')
            invoice_pdf = event_data.get('invoice_pdf')
            
            lambda_logger.info("Processing payment succeeded event", extra={
                'event_id': event_id,
                'invoice_id': invoice_id,
                'subscription_id': subscription_id,
                'amount_paid': amount_paid,
                'currency': currency
            })
            
            # Extract tenant ID from metadata
            tenant_id = self.extract_tenant_id(event_data)
            if not tenant_id:
                lambda_logger.warning("No tenant ID found in payment succeeded event", extra={
                    'event_id': event_id,
                    'invoice_id': invoice_id
                })
                tenant_id = 'unknown'
            
            actions_taken = []
            
            # 1. Update subscription status to ACTIVE
            if subscription_id:
                self.update_subscription_status(
                    subscription_id,
                    'ACTIVE',
                    {
                        'last_payment_success': datetime.utcnow().isoformat(),
                        'last_invoice_id': invoice_id,
                        'amount_paid': amount_paid,
                        'currency': currency,
                        'current_period_start': datetime.fromtimestamp(period_start).isoformat() if period_start else None,
                        'current_period_end': datetime.fromtimestamp(period_end).isoformat() if period_end else None
                    }
                )
                actions_taken.append('subscription_activated')
            
            # 2. Reset failed attempts counter
            self.reset_failed_attempts(subscription_id)
            actions_taken.append('failed_attempts_reset')
            
            # 3. Reactivate tenant if suspended
            tenant_reactivated = self._reactivate_tenant_if_suspended(tenant_id)
            if tenant_reactivated:
                actions_taken.append('tenant_reactivated')
            
            # 4. Send payment confirmation
            self._send_payment_confirmation(
                tenant_id,
                {
                    'invoice_id': invoice_id,
                    'amount': amount_paid / 100,  # Convert from cents
                    'currency': currency.upper(),
                    'period_start': datetime.fromtimestamp(period_start).isoformat() if period_start else None,
                    'period_end': datetime.fromtimestamp(period_end).isoformat() if period_end else None,
                    'invoice_url': hosted_invoice_url,
                    'invoice_pdf': invoice_pdf
                }
            )
            actions_taken.append('payment_confirmation_sent')
            
            # 5. Update billing period and next billing date
            if period_end:
                self._update_billing_period(subscription_id, period_start, period_end)
                actions_taken.append('billing_period_updated')
            
            # 6. Cancel any pending suspension actions
            self._cancel_pending_suspensions(tenant_id)
            actions_taken.append('pending_suspensions_cancelled')
            
            # 7. Log event for audit
            self.log_webhook_event(
                'invoice.payment_succeeded',
                event_id,
                tenant_id,
                {
                    'invoice_id': invoice_id,
                    'subscription_id': subscription_id,
                    'amount_paid': amount_paid,
                    'currency': currency,
                    'period_start': period_start,
                    'period_end': period_end,
                    'actions_taken': actions_taken
                }
            )
            
            lambda_logger.info("Payment succeeded event processed successfully", extra={
                'event_id': event_id,
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'amount_paid': amount_paid,
                'actions_taken': actions_taken
            })
            
            return {
                'success': True,
                'event_type': 'invoice.payment_succeeded',
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'invoice_id': invoice_id,
                'amount_paid': amount_paid / 100,
                'currency': currency.upper(),
                'actions_taken': actions_taken,
                'next_billing_date': datetime.fromtimestamp(period_end).isoformat() if period_end else None
            }
            
        except Exception as e:
            lambda_logger.error("Failed to process payment succeeded event", extra={
                'event_id': event_id,
                'error': str(e),
                'invoice_id': event_data.get('id'),
                'subscription_id': event_data.get('subscription')
            })
            raise
    
    def _reactivate_tenant_if_suspended(self, tenant_id: str) -> bool:
        """Reactivate tenant if it was suspended for non-payment."""
        try:
            tenant_info = self.get_tenant_info(tenant_id)
            if not tenant_info:
                return False
            
            current_status = tenant_info.get('status', 'active')
            
            if current_status in ['suspended', 'past_due']:
                # In a real implementation, this would update the tenant status
                lambda_logger.info("Tenant reactivated after successful payment", extra={
                    'tenant_id': tenant_id,
                    'previous_status': current_status
                })
                return True
            
            return False
            
        except Exception as e:
            lambda_logger.error("Failed to reactivate tenant", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def _send_payment_confirmation(self, tenant_id: str, data: Dict[str, Any]) -> None:
        """Send payment confirmation email."""
        try:
            tenant_info = self.get_tenant_info(tenant_id)
            if not tenant_info:
                lambda_logger.warning("Cannot send confirmation - tenant not found", extra={
                    'tenant_id': tenant_id
                })
                return
            
            email_data = {
                'tenant_id': tenant_id,
                'company_name': tenant_info.get('company_name', 'Your Company'),
                'email': tenant_info.get('email'),
                'amount': data['amount'],
                'currency': data['currency'],
                'invoice_id': data['invoice_id'],
                'period_start': data['period_start'],
                'period_end': data['period_end'],
                'invoice_url': data.get('invoice_url'),
                'invoice_pdf': data.get('invoice_pdf')
            }
            
            self.send_notification_email(
                tenant_id,
                'payment_confirmation',
                email_data
            )
            
        except Exception as e:
            lambda_logger.error("Failed to send payment confirmation", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
    
    def _update_billing_period(self, subscription_id: str, period_start: int, period_end: int) -> None:
        """Update billing period information."""
        try:
            billing_data = {
                'current_period_start': datetime.fromtimestamp(period_start).isoformat(),
                'current_period_end': datetime.fromtimestamp(period_end).isoformat(),
                'next_billing_date': datetime.fromtimestamp(period_end).isoformat(),
                'billing_updated_at': datetime.utcnow().isoformat()
            }
            
            # In a real implementation, this would update the subscription record
            lambda_logger.info("Billing period updated", extra={
                'subscription_id': subscription_id,
                'billing_data': billing_data
            })
            
        except Exception as e:
            lambda_logger.error("Failed to update billing period", extra={
                'subscription_id': subscription_id,
                'error': str(e)
            })
    
    def _cancel_pending_suspensions(self, tenant_id: str) -> None:
        """Cancel any pending suspension actions for the tenant."""
        try:
            # In a real implementation, this would cancel scheduled jobs
            lambda_logger.info("Pending suspensions cancelled", extra={
                'tenant_id': tenant_id
            })
            
        except Exception as e:
            lambda_logger.error("Failed to cancel pending suspensions", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
