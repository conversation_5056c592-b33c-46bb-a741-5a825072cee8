#!/usr/bin/env python3
# services/payment/src/handlers/list_plans.py
# List plans handler with unified shared layer integration

"""
List plans handler.
Handles retrieving available subscription plans with filtering and pagination.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..config.rate_limits import get_endpoint_rate_limit
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_list_plans_request


@require_auth
@rate_limit(requests_per_minute=get_endpoint_rate_limit("list_plans"))
@payment_resilience("list_plans")
@measure_performance("payment_list_plans")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    List available subscription plans.

    GET /payment/plans?billing_interval=MONTHLY&active_only=true&limit=10&offset=0
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/payment/plans'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate query parameters
        query_params = event.get('queryStringParameters') or {}
        validated_params = validate_list_plans_request(query_params)

        billing_interval = validated_params.get('billing_interval')
        active_only = validated_params.get('active_only', True)
        limit = validated_params.get('limit', 10)
        offset = validated_params.get('offset', 0)

        lambda_logger.info("Listing available plans", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'billing_interval': billing_interval,
            'active_only': active_only,
            'limit': limit,
            'offset': offset
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Get available plans with filters
        plans_result = subscription_service_instance.list_available_plans(
            billing_interval=billing_interval,
            active_only=active_only,
            limit=limit,
            offset=offset,
            tenant_id=auth_context.tenant_id
        )

        # Prepare response data
        response_data = {
            'plans': plans_result.get('plans', []),
            'pagination': {
                'total': plans_result.get('total', 0),
                'limit': limit,
                'offset': offset,
                'has_more': plans_result.get('has_more', False)
            },
            'filters': {
                'billing_interval': billing_interval,
                'active_only': active_only
            },
            'metadata': plans_result.get('metadata', {})
        }

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/plans",
            method="GET",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/payment/plans',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message=f"Retrieved {len(response_data['plans'])} available plans"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in list plans", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in list plans", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in list plans", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in list plans", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while retrieving plans"
        )


@require_auth
@rate_limit(requests_per_minute=get_endpoint_rate_limit("get_plan"))
@payment_resilience("get_plan")
@measure_performance("payment_get_plan")
def get_plan_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get specific plan details.

    GET /payment/plans/{plan_id}
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/payment/plans/{plan_id}'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Extract plan_id from path parameters
        path_parameters = event.get('pathParameters') or {}
        plan_id = path_parameters.get('plan_id')

        if not plan_id:
            lambda_logger.warning("Missing plan_id in path parameters")
            return APIResponse.error(
                message="Plan ID is required",
                status_code=400
            )

        lambda_logger.info(f"Getting plan details for plan_id: {plan_id}")

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Get plan details
        plan = subscription_service_instance.get_plan_details(plan_id)

        if not plan:
            lambda_logger.warning(f"Plan not found: {plan_id}")
            return APIResponse.error(
                message="Plan not found",
                status_code=404
            )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            f'/payment/plans/{plan_id}',
            200,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.success(
            data={'plan': plan},
            message="Plan details retrieved successfully"
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            f'/payment/plans/{plan_id if "plan_id" in locals() else "unknown"}',
            404,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )

        lambda_logger.warning("Plan not found", extra={
            'plan_id': plan_id if 'plan_id' in locals() else None,
            'error': str(e),
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'PLAN_NOT_FOUND')
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            f'/payment/plans/{plan_id}',
            e.status_code,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            f'/payment/plans/{plan_id}',
            500,
            duration_ms,
            request_id=request_id,
            error=str(e)
        )

        lambda_logger.error("Unexpected error during plan retrieval", extra={
            'error': str(e),
            'plan_id': plan_id,
            'request_id': request_id
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while retrieving plan details"
        )
