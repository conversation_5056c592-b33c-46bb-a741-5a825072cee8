#!/usr/bin/env python3
# services/payment/src/handlers/create_subscription.py
# Create subscription handler with unified shared layer integration

"""
Create subscription handler.
Handles subscription creation for tenants with proper validation and error handling.
"""

import json
from typing import Any, Dict

# Unified shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response
from shared.validators import UnifiedRequestValidator
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient
from shared.models import UserRole, BillingInterval

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.unified_payment_validators import validate_create_subscription_request


@require_auth
@rate_limit(requests_per_minute=30)
@payment_resilience("create_subscription")
@measure_performance("payment_create_subscription")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Create a subscription for the tenant.

    POST /payment/subscriptions
    {
        "plan_id": "plan-123",
        "billing_interval": "MONTHLY",
        "payment_method_id": "pm_123",
        "coupon_code": "SAVE20"
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/subscriptions'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_create_subscription_request(body_data)

        plan_id = validated_data['plan_id']
        billing_interval_str = validated_data['billing_interval']
        payment_method_id = validated_data.get('payment_method_id')
        coupon_code = validated_data.get('coupon_code')

        # Convert billing interval string to enum
        try:
            billing_interval = BillingInterval(billing_interval_str)
        except ValueError:
            raise ValidationException(
                f"Invalid billing interval: {billing_interval_str}",
                validation_errors=[{"field": "billing_interval", "message": f"Invalid value: {billing_interval_str}"}]
            )

        lambda_logger.info("Creating subscription", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'plan_id': plan_id,
            'billing_interval': billing_interval.value,
            'has_payment_method': bool(payment_method_id),
            'has_coupon': bool(coupon_code)
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Create subscription (synchronous operation)
        subscription_result = subscription_service_instance.create_subscription(
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            plan_id=plan_id,
            billing_interval=billing_interval,
            payment_method_id=payment_method_id,
            coupon_code=coupon_code
        )

        # Prepare response data
        response_data = {
            'subscription': subscription_result.get('subscription', {}),
            'plan': subscription_result.get('plan', {}),
            'next_steps': []
        }

        # Add Stripe client secret if present (for payment confirmation)
        if subscription_result.get('stripe_client_secret'):
            response_data['stripe_client_secret'] = subscription_result['stripe_client_secret']
            response_data['next_steps'].append('Complete payment setup using Stripe client secret')
        else:
            response_data['next_steps'].append('Subscription is active and ready to use')

        # Log audit event for subscription creation
        audit_log(
            lambda_logger,
            "subscription_created",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'plan_id': plan_id,
                'billing_interval': billing_interval.value,
                'subscription_id': subscription_result.get('subscription', {}).get('subscription_id')
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/subscriptions",
            method="POST",
            status_code=201
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/subscriptions',
            201,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.created(
            data=response_data,
            message="Subscription created successfully"
        )
        
    except ValidationException as e:
        lambda_logger.warning("Validation error in create subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )
        
    except PaymentException as e:
        lambda_logger.error("Payment error in create subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'plan_id': plan_id if 'plan_id' in locals() else None
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )
        
    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in create subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'plan_id': plan_id if 'plan_id' in locals() else None
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in create subscription", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while creating subscription"
        )
