#!/usr/bin/env python3
# services/payment/src/handlers/cancel_subscription.py
# Cancel subscription handler with unified shared layer integration

"""
Cancel subscription handler.
Handles subscription cancellation for tenants with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Unified shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import ValidationException, ResourceNotFoundException
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response
from shared.validators import UnifiedRequestValidator
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient
from shared.models import UserRole

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.unified_payment_validators import UnifiedPaymentValidator


@require_auth
@rate_limit(requests_per_minute=10)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Cancel subscription for the tenant.

    POST /payment/subscriptions/{id}/cancel
    {
        "reason": "No longer needed",
        "at_period_end": true,
        "immediate": false
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/subscriptions/{id}/cancel'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    # Get subscription ID from path parameters
    subscription_id = event.get('pathParameters', {}).get('id')
    if not subscription_id:
        return APIResponse.validation_error(
            message="Subscription ID is required",
            validation_errors=[{"field": "id", "message": "Subscription ID is required in path"}]
        )

    try:
        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_cancel_subscription_request(body_data)

        reason = validated_data.get('reason')
        at_period_end = validated_data.get('at_period_end', True)
        immediate = validated_data.get('immediate', False)

        lambda_logger.info("Cancelling subscription", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id,
            'reason': reason,
            'at_period_end': at_period_end,
            'immediate': immediate
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Cancel subscription
        cancellation_result = subscription_service_instance.cancel_subscription(
            subscription_id=subscription_id,
            tenant_id=auth_context.tenant_id,
            reason=reason,
            at_period_end=at_period_end,
            immediate=immediate
        )

        # Prepare response data
        response_data = {
            'subscription': cancellation_result.get('subscription', {}),
            'cancellation_details': {
                'cancelled_at_period_end': at_period_end,
                'immediate': immediate,
                'reason': reason,
                'effective_date': cancellation_result.get('effective_date'),
                'refund_amount': cancellation_result.get('refund_amount', 0)
            },
            'next_steps': []
        }

        if at_period_end:
            response_data['next_steps'].append('Subscription will remain active until the end of current billing period')
            response_data['next_steps'].append('You can reactivate before the period ends')
        else:
            response_data['next_steps'].append('Subscription has been cancelled immediately')
            if cancellation_result.get('refund_amount', 0) > 0:
                response_data['next_steps'].append('Prorated refund will be processed within 5-7 business days')

        # Log audit event for subscription cancellation
        audit_log(
            lambda_logger,
            "subscription_cancelled",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'subscription_id': subscription_id,
                'reason': reason,
                'at_period_end': at_period_end,
                'immediate': immediate,
                'effective_date': cancellation_result.get('effective_date')
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/subscriptions/{id}/cancel",
            method="POST",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/subscriptions/{id}/cancel',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Subscription cancelled successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in cancel subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Subscription not found for cancellation", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'SUBSCRIPTION_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in cancel subscription", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while cancelling subscription"
        )
