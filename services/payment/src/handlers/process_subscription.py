#!/usr/bin/env python3
# services/payment/src/handlers/process_subscription.py
# Process subscription handler with unified shared layer integration

"""
Process subscription handler for registration flow.
Handles payment processing during tenant registration with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Unified shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response
from shared.validators import UnifiedRequestValidator, UnifiedUserValidator
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.unified_payment_validators import validate_process_subscription_request


@require_auth
@rate_limit(requests_per_minute=20)
@payment_resilience("process_subscription")
@measure_performance("payment_process_subscription")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process subscription for tenant registration.

    POST /payment/process-subscription
    {
        "tenant_id": "tenant-123",
        "plan_id": "pro",
        "payment_method": {
            "type": "card",
            "card_number": "****************",
            "exp_month": 12,
            "exp_year": 2025,
            "cvc": "123"
        },
        "billing_interval": "monthly",
        "trial_days": 14,
        "coupon_code": "WELCOME20"
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/process-subscription'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body using unified validator
        body_data = UnifiedRequestValidator.parse_request_body(event)

        # Validate subscription data using unified patterns
        required_fields = ['tenant_id', 'plan_id', 'payment_method']
        for field in required_fields:
            if not body_data.get(field):
                raise ValidationException(f"{field.replace('_', ' ').title()} is required")

        # Extract and validate data
        tenant_id = body_data['tenant_id']
        plan_id = body_data['plan_id']
        payment_method = body_data['payment_method']
        billing_interval = body_data.get('billing_interval', 'monthly')
        trial_days = body_data.get('trial_days', 0)
        coupon_code = body_data.get('coupon_code')

        # Validate tenant exists using service communication
        db_client = DynamoDBClient()
        service_comm = ServiceCommunicationManager(db_client)

        # Validate complete subscription data using unified validator
        validated_data = validate_process_subscription_request(body_data)

        # Verify tenant exists and user has access
        tenant_response = service_comm.get_tenant_info(tenant_id)
        if not tenant_response.success:
            raise ResourceNotFoundException(f"Tenant {tenant_id} not found or inaccessible")

        lambda_logger.info("Processing subscription for registration", extra={
            'tenant_id': tenant_id,
            'user_id': auth_context.user_id,
            'plan_id': plan_id,
            'billing_interval': billing_interval,
            'trial_days': trial_days,
            'has_coupon': bool(coupon_code),
            'auth_tenant_id': auth_context.tenant_id
        })

        # Verify tenant ownership (user can only process subscription for their own tenant)
        if tenant_id != auth_context.tenant_id:
            lambda_logger.warning("Tenant ID mismatch in subscription processing", extra={
                'requested_tenant_id': tenant_id,
                'auth_tenant_id': auth_context.tenant_id,
                'user_id': auth_context.user_id
            })
            return APIResponse.error(
                message="You can only process subscriptions for your own tenant",
                status_code=403,
                error_code="TENANT_MISMATCH"
            )

        # Check permissions (only MASTER can process subscriptions)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for subscription processing", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Only tenant masters can process subscriptions",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Process subscription
        processing_result = subscription_service_instance.process_subscription(
            tenant_id=tenant_id,
            plan_id=plan_id,
            payment_method=payment_method,
            billing_interval=billing_interval,
            trial_days=trial_days,
            coupon_code=coupon_code,
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'subscription': processing_result.get('subscription', {}),
            'payment_result': processing_result.get('payment_result', {}),
            'plan_details': processing_result.get('plan_details', {}),
            'billing_info': processing_result.get('billing_info', {}),
            'trial_info': processing_result.get('trial_info', {}),
            'coupon_applied': processing_result.get('coupon_applied', {}),
            'next_steps': processing_result.get('next_steps', [])
        }

        # Log audit event for subscription processing
        audit_log(
            lambda_logger,
            "subscription_processed",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'subscription_id': processing_result.get('subscription', {}).get('subscription_id'),
                'plan_id': plan_id,
                'billing_interval': billing_interval,
                'trial_days': trial_days,
                'coupon_applied': bool(coupon_code),
                'payment_status': processing_result.get('payment_result', {}).get('status')
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/process-subscription",
            method="POST",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/process-subscription',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Subscription processed successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in process subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'plan_id': plan_id if 'plan_id' in locals() else None,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in process subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'plan_id': plan_id if 'plan_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in process subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'plan_id': plan_id if 'plan_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in process subscription", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'plan_id': plan_id if 'plan_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while processing subscription"
        )
