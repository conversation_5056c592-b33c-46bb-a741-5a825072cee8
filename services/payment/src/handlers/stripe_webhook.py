#!/usr/bin/env python3
# services/payment/src/handlers/stripe_webhook.py
# Stripe webhook handler with unified shared layer integration

"""
Stripe webhook handler.
Handles Stripe webhook events for subscription management with proper validation and audit logging.
"""

import json
import hmac
import hashlib
from typing import Any, Dict

# Unified shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import payment_resilience, with_retry
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response
from shared.secrets import get_integration_credentials
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient

# Service imports
from ..config.dependencies import container
from ..validators.unified_payment_validators import UnifiedPaymentValidator
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_stripe_webhook_request


# Note: Webhooks don't use @require_auth - they use Stripe signature verification
@payment_resilience("stripe_webhook")
@measure_performance("payment_stripe_webhook")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle Stripe webhook events.

    POST /payment/webhooks/stripe

    Supported events:
    - customer.subscription.created
    - customer.subscription.updated
    - customer.subscription.deleted
    - invoice.payment_succeeded
    - invoice.payment_failed
    - customer.subscription.trial_will_end
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Log API request (no tenant/user for webhooks)
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/webhooks/stripe'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Get webhook payload and signature
        payload = event.get('body', '')
        headers = event.get('headers', {})
        signature = headers.get('stripe-signature') or headers.get('Stripe-Signature', '')

        if not payload or not signature:
            audit_log(
                lambda_logger,
                "stripe_webhook_missing_data",
                tenant_id=None,
                user_id=None,
                details={
                    'ip_address': client_ip,
                    'request_id': request_id,
                    'has_payload': bool(payload),
                    'has_signature': bool(signature)
                }
            )
            return APIResponse.validation_error(
                message="Missing webhook payload or signature",
                validation_errors=[
                    {"field": "payload", "message": "Webhook payload is required"},
                    {"field": "stripe-signature", "message": "Stripe signature is required"}
                ]
            )
        
        # Get webhook secret from environment/secrets
        try:
            stripe_credentials = get_integration_credentials('stripe')
            webhook_secret = stripe_credentials.get('webhook_secret')
        except Exception as e:
            lambda_logger.error("Failed to get Stripe credentials", extra={'error': str(e)})
            webhook_secret = None

        if not webhook_secret:
            lambda_logger.error("Stripe webhook secret not configured")
            return APIResponse.error(
                message="Webhook configuration error",
                status_code=500,
                error_code="WEBHOOK_CONFIG_ERROR"
            )

        # Verify webhook signature
        try:
            webhook_event = _verify_stripe_signature(payload, signature, webhook_secret)
        except ValidationException as e:
            audit_log(
                lambda_logger,
                "stripe_webhook_invalid_signature",
                tenant_id=None,
                user_id=None,
                details={
                    'ip_address': client_ip,
                    'request_id': request_id,
                    'error': str(e)
                }
            )
            return APIResponse.error(
                message="Invalid webhook signature",
                status_code=401,
                error_code="INVALID_SIGNATURE"
            )

        # Parse webhook event
        event_type = webhook_event.get('type')
        event_data = webhook_event.get('data', {}).get('object', {})
        event_id = webhook_event.get('id')

        lambda_logger.info("Processing Stripe webhook", extra={
            'event_type': event_type,
            'event_id': event_id,
            'object_id': event_data.get('id'),
            'client_ip': client_ip
        })

        # Process webhook event using new processors
        from .webhook_processors.processor_factory import WebhookProcessorFactory

        # Check if event is supported by new processors
        if WebhookProcessorFactory.is_supported_event(event_type):
            # Use new webhook processors
            import asyncio
            result = asyncio.run(WebhookProcessorFactory.process_webhook_event(
                event_type=event_type,
                event_data=event_data,
                event_id=event_id,
                client_ip=client_ip
            ))
        else:
            # Fallback to old subscription service for unsupported events
            subscription_service_instance = container.resolve(ISubscriptionService)
            result = subscription_service_instance.process_stripe_webhook(
                event_type=event_type,
                event_data=event_data,
                event_id=event_id,
                client_ip=client_ip
            )

        # Prepare response data
        response_data = {
            'event_id': event_id,
            'event_type': event_type,
            'processed': True,
            'result': result.get('result', {}),
            'actions_taken': result.get('actions_taken', []),
            'tenant_id': result.get('tenant_id'),
            'subscription_id': result.get('subscription_id')
        }

        # Log audit event for webhook processing
        audit_log(
            lambda_logger,
            "stripe_webhook_processed",
            tenant_id=result.get('tenant_id'),
            user_id=None,
            details={
                'event_id': event_id,
                'event_type': event_type,
                'actions_taken': result.get('actions_taken', []),
                'client_ip': client_ip
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=result.get('tenant_id'),
            endpoint="/payment/webhooks/stripe",
            method="POST",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/webhooks/stripe',
            200,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message=f"Webhook event {event_type} processed successfully"
        )
        
    except ValidationException as e:
        lambda_logger.warning("Validation error in stripe webhook", extra={
            'error': str(e),
            'client_ip': client_ip,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in stripe webhook", extra={
            'error': str(e),
            'client_ip': client_ip,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in stripe webhook", extra={
            'error': str(e),
            'client_ip': client_ip,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in stripe webhook", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'client_ip': client_ip,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while processing webhook"
        )



def _verify_stripe_signature(payload: str, signature: str, webhook_secret: str) -> Dict[str, Any]:
    """
    Verify Stripe webhook signature using Stripe's official verification.

    Args:
        payload: Raw webhook payload
        signature: Stripe signature header
        webhook_secret: Webhook secret for verification

    Returns:
        Parsed webhook event

    Raises:
        ValidationException: If signature is invalid
    """
    try:
        import stripe

        # Use Stripe's official signature verification
        try:
            webhook_event = stripe.Webhook.construct_event(
                payload, signature, webhook_secret
            )

            lambda_logger.info("Stripe signature verified successfully", extra={
                'event_id': webhook_event.get('id'),
                'event_type': webhook_event.get('type')
            })

            return webhook_event

        except stripe.error.SignatureVerificationError as e:
            lambda_logger.error("Stripe signature verification failed", extra={
                'error': str(e)
            })
            raise ValidationException(f"Invalid webhook signature: {str(e)}")

        except ValueError as e:
            lambda_logger.error("Invalid webhook payload", extra={
                'error': str(e)
            })
            raise ValidationException(f"Invalid webhook payload: {str(e)}")

    except ImportError:
        # Fallback to mock implementation if Stripe library not available
        lambda_logger.warning("Stripe library not available, using mock verification")

        # Basic signature format validation
        if not signature.startswith('t='):
            raise ValidationException("Invalid signature format")

        # Parse the payload as JSON
        try:
            webhook_event = json.loads(payload)
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON payload")

        # Validate required fields
        required_fields = ['id', 'type', 'data']
        for field in required_fields:
            if field not in webhook_event:
                raise ValidationException(f"Missing required field: {field}")

        lambda_logger.info("Mock: Stripe signature verified successfully", extra={
            'event_id': webhook_event.get('id'),
            'event_type': webhook_event.get('type')
        })

        return webhook_event

    except Exception as e:
        lambda_logger.error(f"Stripe signature verification failed: {str(e)}")
        raise ValidationException(f"Invalid webhook signature: {str(e)}")




