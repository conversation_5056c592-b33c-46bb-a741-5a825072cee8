#!/usr/bin/env python3
# services/payment/src/handlers/download_invoice.py
# Download invoice handler with unified shared layer integration

"""
Download invoice handler.
Handles downloading invoice PDFs with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_download_invoice_request


@require_auth
@rate_limit(requests_per_minute=30)
@payment_resilience("download_invoice")
@measure_performance("payment_download_invoice")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Download invoice PDF.

    GET /payment/invoices/{id}/download?format=pdf&include_attachments=true
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Extract invoice_id from path parameters
    path_params = event.get('pathParameters') or {}
    invoice_id = path_params.get('id')

    if not invoice_id:
        return APIResponse.validation_error(
            message="Invoice ID is required",
            validation_errors=[{"field": "id", "message": "Invoice ID is required in path"}]
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', f'/payment/invoices/{invoice_id}/download'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Parse and validate query parameters
        query_params = event.get('queryStringParameters') or {}
        validated_params = validate_download_invoice_request(query_params)

        format_type = validated_params.get('format', 'pdf')
        include_attachments = validated_params.get('include_attachments', False)

        # Check permissions (MASTER and ADMIN can download invoices)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning("Insufficient permissions for invoice download", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id,
                'invoice_id': invoice_id
            })
            return APIResponse.error(
                message="Insufficient permissions to download invoices",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        lambda_logger.info("Downloading invoice", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'invoice_id': invoice_id,
            'format': format_type,
            'include_attachments': include_attachments
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Get invoice download URL
        download_result = subscription_service_instance.get_invoice_download_url(
            tenant_id=auth_context.tenant_id,
            invoice_id=invoice_id,
            format_type=format_type,
            include_attachments=include_attachments
        )

        # Prepare response data
        response_data = {
            'invoice_id': invoice_id,
            'download_url': download_result.get('download_url'),
            'expires_at': download_result.get('expires_at'),
            'file_size': download_result.get('file_size'),
            'content_type': download_result.get('content_type', 'application/pdf'),
            'format': format_type,
            'include_attachments': include_attachments,
            'invoice_details': download_result.get('invoice_details', {})
        }

        # Log audit event for invoice download
        audit_log(
            lambda_logger,
            "invoice_downloaded",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'invoice_id': invoice_id,
                'format': format_type,
                'include_attachments': include_attachments
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/invoices/{id}/download",
            method="GET",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/payment/invoices/{id}/download',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Invoice download URL generated successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in download invoice", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'invoice_id': invoice_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in download invoice", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'invoice_id': invoice_id,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in download invoice", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'invoice_id': invoice_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in download invoice", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'invoice_id': invoice_id if 'invoice_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while generating invoice download URL"
        )
