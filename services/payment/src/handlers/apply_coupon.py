#!/usr/bin/env python3
# services/payment/src/handlers/apply_coupon.py
# Apply coupon handler with unified shared layer integration

"""
Apply coupon handler.
Handles applying discount coupons to subscriptions and invoices with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_apply_coupon_request


@require_auth
@rate_limit(requests_per_minute=30)
@payment_resilience("apply_coupon")
@measure_performance("payment_apply_coupon")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Apply discount coupon to subscription.

    POST /payment/coupons/apply
    {
        "coupon_code": "SAVE20",
        "subscription_id": "sub_123456789",
        "apply_to": "subscription",
        "validate_only": false
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/coupons/apply'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_apply_coupon_request(body_data)

        coupon_code = validated_data['coupon_code'].upper().strip()
        subscription_id = validated_data.get('subscription_id')
        apply_to = validated_data.get('apply_to', 'subscription')
        validate_only = validated_data.get('validate_only', False)

        lambda_logger.info("Processing coupon application", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'coupon_code': coupon_code,
            'subscription_id': subscription_id,
            'apply_to': apply_to,
            'validate_only': validate_only
        })

        # Check permissions (only MASTER and ADMIN can apply coupons)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning("Insufficient permissions for coupon application", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Only tenant masters and admins can apply coupons",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Apply coupon
        coupon_result = subscription_service_instance.apply_coupon(
            tenant_id=auth_context.tenant_id,
            coupon_code=coupon_code,
            subscription_id=subscription_id,
            apply_to=apply_to,
            validate_only=validate_only,
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'coupon_applied': not validate_only,
            'validation_only': validate_only,
            'coupon_details': coupon_result.get('coupon_details', {}),
            'discount_details': coupon_result.get('discount_details', {}),
            'subscription_info': coupon_result.get('subscription_info', {}),
            'eligibility': coupon_result.get('eligibility', {}),
            'application_result': coupon_result.get('application_result', {}),
            'next_billing_amount': coupon_result.get('next_billing_amount'),
            'savings_summary': coupon_result.get('savings_summary', {})
        }

        # Log audit event for coupon application
        audit_log(
            lambda_logger,
            "coupon_applied" if not validate_only else "coupon_validated",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'coupon_code': coupon_code,
                'subscription_id': subscription_id,
                'apply_to': apply_to,
                'validate_only': validate_only,
                'discount_amount': coupon_result.get('discount_details', {}).get('discount_amount', 0),
                'eligible': coupon_result.get('eligibility', {}).get('eligible', False)
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/coupons/apply",
            method="POST",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/coupons/apply',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        success_message = (
            f"Coupon '{coupon_code}' validated successfully" if validate_only
            else f"Coupon '{coupon_code}' applied successfully"
        )

        return APIResponse.success(
            data=response_data,
            message=success_message
        )
        
    except ValidationException as e:
        lambda_logger.warning("Validation error in apply coupon", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'coupon_code': coupon_code if 'coupon_code' in locals() else None,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in apply coupon", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'coupon_code': coupon_code if 'coupon_code' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in apply coupon", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'coupon_code': coupon_code if 'coupon_code' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in apply coupon", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'coupon_code': coupon_code if 'coupon_code' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while applying coupon"
        )
