#!/usr/bin/env python3
# services/payment/src/handlers/validate_funds.py
# Validate funds availability handler

"""
Validate funds availability handler.
Verifies if customer has sufficient funds or valid payment method for upcoming charges.
"""

import json
from typing import Any, Dict, Optional

# Unified shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.exceptions import ValidationException, PlatformException, PaymentException
from shared.auth import extract_user_context
from shared.validators import UnifiedRequestValidator
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient

# Service imports
from ..services.subscription_service import ISubscriptionService
from ..services.payment_validation_service import PaymentValidationService
from ..config.dependencies import container


@rate_limit(requests_per_minute=30)
@payment_resilience("validate_funds")
@measure_performance("payment_validate_funds")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Validate funds availability for customer.
    
    GET /payment/validate-funds?amount=100&currency=usd&subscription_id=sub_123
    
    Query Parameters:
    - amount (optional): Amount to validate in cents
    - currency (optional): Currency code (default: usd)
    - subscription_id (optional): Subscription to validate for next payment
    - check_type (optional): Type of validation (upcoming_payment, manual_charge, upgrade)
    
    Args:
        event: API Gateway event
        context: Lambda context
        
    Returns:
        API response with funds validation result
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    # Get request context
    request_context = get_request_context(event)
    request_id = request_context['request_id']
    
    try:
        # Log API request
        log_api_request(event, "validate_funds")
        
        # Extract user context from JWT
        user_context = extract_user_context(event)
        tenant_id = user_context['tenant_id']
        user_id = user_context['user_id']
        
        # Extract query parameters
        query_params = extract_query_parameters(event)
        amount = query_params.get('amount')
        currency = query_params.get('currency', 'usd')
        subscription_id = query_params.get('subscription_id')
        check_type = query_params.get('check_type', 'manual_charge')
        
        lambda_logger.info("Validating funds", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'user_id': user_id,
            'amount': amount,
            'currency': currency,
            'subscription_id': subscription_id,
            'check_type': check_type
        })
        
        # Validate input parameters
        if amount is not None:
            try:
                amount = int(amount)
                if amount <= 0:
                    raise ValidationException("Amount must be positive")
            except ValueError:
                raise ValidationException("Invalid amount format")
        
        if currency not in ['usd', 'eur', 'gbp', 'cad', 'aud']:
            raise ValidationException("Unsupported currency")
        
        if check_type not in ['upcoming_payment', 'manual_charge', 'upgrade', 'renewal']:
            raise ValidationException("Invalid check_type")
        
        # Get services
        subscription_service = container.get(ISubscriptionService)
        validation_service = PaymentValidationService()
        
        # Perform validation based on check type
        validation_result = None
        
        if check_type == 'upcoming_payment' and subscription_id:
            # Validate upcoming subscription payment
            validation_result = await validation_service.validate_upcoming_payment(
                tenant_id, subscription_id
            )
            
        elif check_type == 'manual_charge' and amount:
            # Validate manual charge amount
            validation_result = await validation_service.validate_manual_charge(
                tenant_id, amount, currency
            )
            
        elif check_type == 'upgrade' and subscription_id and amount:
            # Validate subscription upgrade
            validation_result = await validation_service.validate_subscription_upgrade(
                tenant_id, subscription_id, amount, currency
            )
            
        elif check_type == 'renewal' and subscription_id:
            # Validate subscription renewal
            validation_result = await validation_service.validate_subscription_renewal(
                tenant_id, subscription_id
            )
            
        else:
            # General payment method validation
            validation_result = await validation_service.validate_payment_methods(
                tenant_id
            )
        
        # Prepare response
        response_data = {
            'validation': {
                'valid': validation_result['valid'],
                'reason': validation_result.get('reason'),
                'details': validation_result.get('details', {}),
                'checked_at': validation_result['checked_at']
            },
            'payment_methods': {
                'available': validation_result.get('payment_methods_available', 0),
                'default_valid': validation_result.get('default_payment_method_valid', False),
                'requires_action': validation_result.get('requires_action', False)
            },
            'recommendations': validation_result.get('recommendations', [])
        }
        
        # Add amount-specific information if provided
        if amount:
            response_data['amount_info'] = {
                'amount': amount,
                'currency': currency,
                'formatted_amount': f"{amount / 100:.2f} {currency.upper()}"
            }
        
        # Add subscription-specific information if provided
        if subscription_id:
            subscription_info = validation_result.get('subscription_info', {})
            response_data['subscription_info'] = {
                'subscription_id': subscription_id,
                'status': subscription_info.get('status'),
                'next_payment_date': subscription_info.get('next_payment_date'),
                'next_payment_amount': subscription_info.get('next_payment_amount')
            }
        
        result = APIResponse.success(
            data=response_data,
            message="Funds validation completed successfully"
        )
        
        # Log API response
        log_api_response(event, result, "validate_funds")
        
        return result
        
    except ValidationException as e:
        lambda_logger.warning("Validation failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error(message=str(e), status_code=422)
        
    except PaymentException as e:
        lambda_logger.error("Payment validation failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        return APIResponse.error(message=str(e), status_code=400)
        
    except Exception as e:
        lambda_logger.error("Funds validation failed", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        return APIResponse.error(
            message="Failed to validate funds",
            status_code=500
        )


def validate_funds_request(query_params: Dict[str, Any]) -> None:
    """
    Validate funds validation request parameters.
    
    Args:
        query_params: Query parameters from request
        
    Raises:
        ValidationException: If validation fails
    """
    check_type = query_params.get('check_type', 'manual_charge')
    amount = query_params.get('amount')
    subscription_id = query_params.get('subscription_id')
    
    # Validate required parameters based on check type
    if check_type in ['manual_charge', 'upgrade'] and not amount:
        raise ValidationException("Amount is required for manual charge and upgrade validations")
    
    if check_type in ['upcoming_payment', 'upgrade', 'renewal'] and not subscription_id:
        raise ValidationException("Subscription ID is required for subscription-related validations")
    
    # Validate amount format if provided
    if amount is not None:
        try:
            amount_int = int(amount)
            if amount_int <= 0:
                raise ValidationException("Amount must be positive")
        except ValueError:
            raise ValidationException("Amount must be a valid integer in cents")
