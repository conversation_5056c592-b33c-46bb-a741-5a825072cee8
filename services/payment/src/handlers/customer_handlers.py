#!/usr/bin/env python3
# services/payment/src/handlers/customer_handlers.py
# Customer handlers with unified shared layer integration

"""
Customer management handlers.
Provides REST endpoints for customer operations with proper validation and audit logging.
"""

import json
from typing import Dict, Any

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import (
    validate_create_customer_request,
    validate_update_customer_request
)


@require_auth
@rate_limit(requests_per_minute=60)
@payment_resilience("get_customer")
@measure_performance("payment_get_customer")
def get_customer_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get current tenant's customer information.

    GET /payment/customer
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/payment/customer'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        lambda_logger.info("Getting customer information", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Get customer information
        customer_result = subscription_service_instance.get_customer_info(
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'customer': customer_result.get('customer', {}),
            'billing_info': customer_result.get('billing_info', {}),
            'payment_methods': customer_result.get('payment_methods', []),
            'subscription_info': customer_result.get('subscription_info', {}),
            'usage_stats': customer_result.get('usage_stats', {})
        }

        # Log audit event
        audit_log(
            lambda_logger,
            "customer_info_retrieved",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'has_customer': bool(customer_result.get('customer')),
                'payment_methods_count': len(customer_result.get('payment_methods', []))
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/customer",
            method="GET",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/payment/customer',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Customer information retrieved successfully"
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Customer not found", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'CUSTOMER_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error getting customer", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while retrieving customer information"
        )


@require_auth
@rate_limit(requests_per_minute=10)
@payment_resilience("create_customer")
@measure_performance("payment_create_customer")
def create_customer_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Create a customer for the current tenant.

    POST /payment/customer
    {
        "billing_email": "<EMAIL>",
        "company_name": "Company Inc",
        "phone": "+*********0",
        "address": {
            "line1": "123 Main St",
            "city": "New York",
            "state": "NY",
            "postal_code": "10001",
            "country": "US"
        },
        "tax_id": "*********"
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/customer'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Check permissions (only MASTER can create customers)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for customer creation", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Only tenant masters can create customers",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_create_customer_request(body_data)

        lambda_logger.info("Creating customer", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'billing_email': validated_data.get('billing_email'),
            'company_name': validated_data.get('company_name')
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Create customer
        creation_result = subscription_service_instance.create_customer(
            tenant_id=auth_context.tenant_id,
            billing_email=validated_data.get('billing_email'),
            company_name=validated_data.get('company_name'),
            phone=validated_data.get('phone'),
            address=validated_data.get('address'),
            tax_id=validated_data.get('tax_id'),
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'customer': creation_result.get('customer', {}),
            'stripe_customer_id': creation_result.get('stripe_customer_id'),
            'billing_info': creation_result.get('billing_info', {}),
            'setup_required': creation_result.get('setup_required', []),
            'next_steps': creation_result.get('next_steps', [])
        }

        # Log audit event
        audit_log(
            lambda_logger,
            "customer_created",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'customer_id': creation_result.get('customer', {}).get('customer_id'),
                'billing_email': validated_data.get('billing_email'),
                'company_name': validated_data.get('company_name')
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/customer",
            method="POST",
            status_code=201
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/customer',
            201,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Customer created successfully",
            status_code=201
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in create customer", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in create customer", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in create customer", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while creating customer"
        )


@require_auth
@rate_limit(requests_per_minute=20)
@payment_resilience("update_customer")
@measure_performance("payment_update_customer")
def update_customer_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update current tenant's customer information.

    PUT /payment/customer
    {
        "billing_email": "<EMAIL>",
        "company_name": "New Company Name",
        "phone": "+*********0",
        "address": {
            "line1": "456 New St",
            "city": "Boston",
            "state": "MA",
            "postal_code": "02101",
            "country": "US"
        },
        "tax_id": "*********"
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', '/payment/customer'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Check permissions (only MASTER can update customers)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for customer update", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Only tenant masters can update customers",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_update_customer_request(body_data)

        lambda_logger.info("Updating customer", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'fields_to_update': list(validated_data.keys())
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Update customer
        update_result = subscription_service_instance.update_customer(
            tenant_id=auth_context.tenant_id,
            billing_email=validated_data.get('billing_email'),
            company_name=validated_data.get('company_name'),
            phone=validated_data.get('phone'),
            address=validated_data.get('address'),
            tax_id=validated_data.get('tax_id'),
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'customer': update_result.get('customer', {}),
            'changes_applied': update_result.get('changes_applied', []),
            'billing_info': update_result.get('billing_info', {}),
            'sync_status': update_result.get('sync_status', {})
        }

        # Log audit event
        audit_log(
            lambda_logger,
            "customer_updated",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'customer_id': update_result.get('customer', {}).get('customer_id'),
                'changes_applied': update_result.get('changes_applied', [])
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/customer",
            method="PUT",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/payment/customer',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Customer updated successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in update customer", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in update customer", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Customer not found in update", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'CUSTOMER_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in update customer", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while updating customer"
        )


@require_auth
@rate_limit(requests_per_minute=5)
@payment_resilience("sync_customer")
@measure_performance("payment_sync_customer")
def sync_customer_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Sync customer data with Stripe.

    POST /payment/customer/sync
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/customer/sync'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Check permissions (only MASTER can sync customers)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for customer sync", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Only tenant masters can sync customers",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        lambda_logger.info("Syncing customer with Stripe", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Sync customer with Stripe
        sync_result = subscription_service_instance.sync_customer_with_stripe(
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'customer': sync_result.get('customer', {}),
            'sync_status': sync_result.get('sync_status', {}),
            'changes_detected': sync_result.get('changes_detected', []),
            'stripe_data': sync_result.get('stripe_data', {}),
            'last_sync': sync_result.get('last_sync')
        }

        # Log audit event
        audit_log(
            lambda_logger,
            "customer_synced_with_stripe",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'customer_id': sync_result.get('customer', {}).get('customer_id'),
                'changes_detected': sync_result.get('changes_detected', []),
                'sync_successful': sync_result.get('sync_status', {}).get('successful', False)
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/customer/sync",
            method="POST",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/customer/sync',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Customer synced with Stripe successfully"
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in sync customer", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Customer not found in sync", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'CUSTOMER_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in sync customer", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while syncing customer"
        )
