#!/usr/bin/env python3
# services/payment/src/handlers/update_payment_method.py
# Update payment method handler with unified shared layer integration

"""
Update payment method handler.
Handles updating customer payment methods with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_update_payment_method_request


@require_auth
@rate_limit(requests_per_minute=30)
@payment_resilience("update_payment_method")
@measure_performance("payment_update_payment_method")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update customer payment method.

    PUT /payment/payment-method
    {
        "payment_method_id": "pm_1234567890",
        "set_as_default": true,
        "billing_address": {
            "line1": "123 Main St",
            "city": "New York",
            "state": "NY",
            "postal_code": "10001",
            "country": "US"
        },
        "metadata": {
            "nickname": "Primary Card"
        }
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', '/payment/payment-method'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_update_payment_method_request(body_data)

        payment_method_id = validated_data['payment_method_id']
        set_as_default = validated_data.get('set_as_default', False)
        billing_address = validated_data.get('billing_address')
        metadata = validated_data.get('metadata', {})

        lambda_logger.info("Updating payment method", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'payment_method_id': payment_method_id,
            'set_as_default': set_as_default,
            'has_billing_address': bool(billing_address),
            'has_metadata': bool(metadata)
        })

        # Check permissions (only MASTER can update payment methods)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for payment method update", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Only tenant masters can update payment methods",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Update payment method
        update_result = subscription_service_instance.update_payment_method(
            tenant_id=auth_context.tenant_id,
            payment_method_id=payment_method_id,
            set_as_default=set_as_default,
            billing_address=billing_address,
            metadata=metadata,
            user_id=auth_context.user_id
        )

        # Prepare response data
        response_data = {
            'payment_method': update_result.get('payment_method', {}),
            'customer_info': update_result.get('customer_info', {}),
            'changes_applied': update_result.get('changes_applied', []),
            'is_default': update_result.get('is_default', False),
            'billing_address_updated': bool(billing_address),
            'metadata_updated': bool(metadata),
            'all_payment_methods': update_result.get('all_payment_methods', [])
        }

        # Log audit event for payment method update
        audit_log(
            lambda_logger,
            "payment_method_updated",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'payment_method_id': payment_method_id,
                'set_as_default': set_as_default,
                'billing_address_updated': bool(billing_address),
                'metadata_updated': bool(metadata)
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/payment-method",
            method="PUT",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/payment/payment-method',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Payment method updated successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in update payment method", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'payment_method_id': payment_method_id if 'payment_method_id' in locals() else None,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in update payment method", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'payment_method_id': payment_method_id if 'payment_method_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in update payment method", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'payment_method_id': payment_method_id if 'payment_method_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in update payment method", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'payment_method_id': payment_method_id if 'payment_method_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while updating payment method"
        )
        
    except PaymentException as e:
        lambda_logger.error("Payment error during payment method update", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=402,
            error_code="PAYMENT_ERROR"
        )
        
    except Exception as e:
        lambda_logger.error("Payment method update error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error during payment method update",
            status_code=500,
            error_code="PAYMENT_METHOD_UPDATE_ERROR"
        )
