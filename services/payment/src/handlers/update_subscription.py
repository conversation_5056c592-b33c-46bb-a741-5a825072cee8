#!/usr/bin/env python3
# services/payment/src/handlers/update_subscription.py
# Update subscription handler with unified shared layer integration

"""
Update subscription handler.
Handles subscription plan changes and modifications with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import metrics_manager
from shared.exceptions import ValidationException, PaymentException, ResourceNotFoundException
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..models.subscription import BillingInterval
from ..validators.subscription_validators import validate_update_subscription_request


@require_auth
@rate_limit(requests_per_minute=20)
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Update subscription plan or billing interval.

    PUT /payment/subscriptions/{id}
    {
        "plan_id": "plan_professional",
        "billing_interval": "YEARLY",
        "prorate": true,
        "effective_date": "immediate"
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', '/payment/subscriptions/{id}'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    # Get subscription ID from path parameters
    subscription_id = event.get('pathParameters', {}).get('id')
    if not subscription_id:
        return APIResponse.validation_error(
            message="Subscription ID is required",
            validation_errors=[{"field": "id", "message": "Subscription ID is required in path"}]
        )

    try:
        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_update_subscription_request(body_data)

        plan_id = validated_data.get('plan_id')
        billing_interval_str = validated_data.get('billing_interval')
        prorate = validated_data.get('prorate', True)
        effective_date = validated_data.get('effective_date', 'immediate')

        # Convert billing interval string to enum if provided
        billing_interval = None
        if billing_interval_str:
            try:
                billing_interval = BillingInterval(billing_interval_str)
            except ValueError:
                raise ValidationException(
                    f"Invalid billing interval: {billing_interval_str}",
                    validation_errors=[{"field": "billing_interval", "message": f"Invalid value: {billing_interval_str}"}]
                )

        lambda_logger.info("Updating subscription", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id,
            'new_plan_id': plan_id,
            'billing_interval': billing_interval.value if billing_interval else None,
            'prorate': prorate,
            'effective_date': effective_date
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Update subscription (synchronous operation)
        subscription_result = subscription_service_instance.update_subscription(
            subscription_id=subscription_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            plan_id=plan_id,
            billing_interval=billing_interval,
            prorate=prorate,
            effective_date=effective_date
        )

        # Prepare response data
        response_data = {
            'subscription': subscription_result.get('subscription', {}),
            'changes': subscription_result.get('changes', {}),
            'billing_info': subscription_result.get('billing_info', {}),
            'next_steps': []
        }

        # Add next steps based on update type
        if subscription_result.get('requires_payment_confirmation'):
            response_data['stripe_client_secret'] = subscription_result.get('stripe_client_secret')
            response_data['next_steps'].append('Complete payment confirmation using Stripe client secret')
        else:
            response_data['next_steps'].append('Subscription update is complete and active')

        # Log audit event for subscription update
        audit_log(
            lambda_logger,
            "subscription_updated",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'subscription_id': subscription_id,
                'plan_id': plan_id,
                'billing_interval': billing_interval.value if billing_interval else None,
                'prorate': prorate,
                'effective_date': effective_date
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/subscriptions/{id}",
            method="PUT",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/payment/subscriptions/{id}',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Subscription updated successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in update subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in update subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in update subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in update subscription", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while updating subscription"
        )
