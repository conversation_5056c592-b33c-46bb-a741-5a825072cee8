# services/payment/src/handlers/payu_webhook.py
# PayU webhook handler for payment notifications

"""
PayU Webhook Handler

This handler processes webhook notifications from PayU payment gateway.
It handles:
1. Payment confirmations
2. Payment failures
3. Subscription events
4. Refund notifications
"""

import json
from typing import Dict, Any, Optional
from datetime import datetime

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.exceptions import ValidationException, PaymentException
from shared.request_utils import parse_request_body, get_request_context
from shared.dependency_injection import container

from ..services.payu_client import payu_client
from ..services.payment_service import IPaymentService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


@rate_limit(requests_per_minute=1000)  # High limit for webhook notifications
@measure_performance("payment_payu_webhook")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Handle PayU webhook notifications.
    
    POST /payment/payu/webhook
    
    PayU sends notifications for:
    - Payment confirmations
    - Payment failures
    - Subscription events
    - Refund notifications
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    # Get request context
    request_context = get_request_context(event)
    request_id = request_context['request_id']
    client_ip = request_context['client_ip']
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        '/payment/payu/webhook',
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Parse webhook payload
        try:
            body = parse_request_body(event)
        except Exception as e:
            raise ValidationException(f"Invalid webhook payload: {str(e)}")
        
        # Get PayU signature from headers
        headers = event.get('headers', {})
        payu_signature = headers.get('X-PayU-Signature') or headers.get('x-payu-signature')
        
        if not payu_signature:
            lambda_logger.warning("PayU webhook received without signature", extra={
                'request_id': request_id,
                'client_ip': client_ip
            })
            # For development, we might allow unsigned webhooks
            # In production, this should be rejected
        
        # Verify webhook signature if present
        raw_payload = event.get('body', '')
        if payu_signature and not payu_client.verify_webhook_signature(raw_payload, payu_signature):
            lambda_logger.error("PayU webhook signature verification failed", extra={
                'request_id': request_id,
                'client_ip': client_ip,
                'signature': payu_signature
            })
            return APIResponse.error("Invalid webhook signature", 401)
        
        # Extract webhook data
        state_pol = body.get('state_pol')  # PayU transaction state
        response_code_pol = body.get('response_code_pol')  # PayU response code
        reference_sale = body.get('reference_sale')  # Our reference code
        reference_pol = body.get('reference_pol')  # PayU reference
        transaction_id = body.get('transaction_id')  # PayU transaction ID
        value = body.get('value')  # Transaction amount
        currency = body.get('currency')  # Currency code
        payment_method_type = body.get('payment_method_type')  # Payment method
        
        lambda_logger.info("Processing PayU webhook", extra={
            'request_id': request_id,
            'state_pol': state_pol,
            'response_code_pol': response_code_pol,
            'reference_sale': reference_sale,
            'reference_pol': reference_pol,
            'transaction_id': transaction_id,
            'value': value,
            'currency': currency,
            'payment_method_type': payment_method_type
        })
        
        # Validate required fields
        if not all([state_pol, reference_sale, transaction_id]):
            raise ValidationException("Missing required webhook fields")
        
        # Get payment service
        payment_service = container.resolve(IPaymentService)
        
        # Process webhook based on transaction state
        if state_pol == '4':  # Approved
            success, error_msg = payment_service.handle_payment_approved(
                reference_code=reference_sale,
                transaction_id=transaction_id,
                payu_reference=reference_pol,
                amount=value,
                currency=currency,
                payment_method=payment_method_type,
                webhook_data=body
            )
            
            if success:
                lambda_logger.info("Payment approved successfully", extra={
                    'reference_sale': reference_sale,
                    'transaction_id': transaction_id,
                    'amount': value,
                    'currency': currency
                })
            else:
                lambda_logger.error(f"Failed to process approved payment: {error_msg}")
                
        elif state_pol == '6':  # Declined
            success, error_msg = payment_service.handle_payment_declined(
                reference_code=reference_sale,
                transaction_id=transaction_id,
                payu_reference=reference_pol,
                response_code=response_code_pol,
                webhook_data=body
            )
            
            if success:
                lambda_logger.info("Payment declined processed", extra={
                    'reference_sale': reference_sale,
                    'transaction_id': transaction_id,
                    'response_code': response_code_pol
                })
            else:
                lambda_logger.error(f"Failed to process declined payment: {error_msg}")
                
        elif state_pol == '104':  # Error
            success, error_msg = payment_service.handle_payment_error(
                reference_code=reference_sale,
                transaction_id=transaction_id,
                payu_reference=reference_pol,
                response_code=response_code_pol,
                webhook_data=body
            )
            
            if success:
                lambda_logger.info("Payment error processed", extra={
                    'reference_sale': reference_sale,
                    'transaction_id': transaction_id,
                    'response_code': response_code_pol
                })
            else:
                lambda_logger.error(f"Failed to process payment error: {error_msg}")
                
        elif state_pol == '7':  # Pending
            success, error_msg = payment_service.handle_payment_pending(
                reference_code=reference_sale,
                transaction_id=transaction_id,
                payu_reference=reference_pol,
                webhook_data=body
            )
            
            if success:
                lambda_logger.info("Payment pending processed", extra={
                    'reference_sale': reference_sale,
                    'transaction_id': transaction_id
                })
            else:
                lambda_logger.error(f"Failed to process pending payment: {error_msg}")
                
        else:
            lambda_logger.warning(f"Unknown PayU state received: {state_pol}", extra={
                'reference_sale': reference_sale,
                'transaction_id': transaction_id,
                'state_pol': state_pol
            })
            success = True  # Don't fail for unknown states
        
        # Audit log for webhook processing
        audit_log(
            action='payu_webhook_processed',
            resource_type='payment',
            resource_id=reference_sale,
            client_ip=client_ip,
            request_id=request_id,
            webhook_data={
                'state_pol': state_pol,
                'response_code_pol': response_code_pol,
                'transaction_id': transaction_id,
                'value': value,
                'currency': currency,
                'success': success
            }
        )
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/payu/webhook',
            200,
            duration_ms,
            request_id=request_id
        )
        
        # PayU expects a simple "OK" response
        return APIResponse.success(
            data={'status': 'processed'},
            message="Webhook processed successfully"
        )
        
    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/payu/webhook',
            400,
            duration_ms,
            request_id=request_id
        )
        
        lambda_logger.error(f"PayU webhook validation error: {str(e)}", extra={
            'request_id': request_id,
            'client_ip': client_ip
        })
        
        return APIResponse.validation_error(str(e))
        
    except PaymentException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/payu/webhook',
            422,
            duration_ms,
            request_id=request_id
        )
        
        lambda_logger.error(f"PayU webhook payment error: {str(e)}", extra={
            'request_id': request_id,
            'client_ip': client_ip
        })
        
        return APIResponse.error(str(e), 422)
        
    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/payu/webhook',
            500,
            duration_ms,
            request_id=request_id
        )
        
        lambda_logger.error(f"Unexpected error in PayU webhook: {str(e)}", extra={
            'request_id': request_id,
            'client_ip': client_ip,
            'error_type': type(e).__name__
        })
        
        return APIResponse.internal_server_error(
            "An unexpected error occurred while processing webhook"
        )
