#!/usr/bin/env python3
# services/payment/src/handlers/get_billing_history.py
# Get billing history handler with unified shared layer integration

"""
Get billing history handler.
Handles retrieval of billing history and invoices with filtering and pagination.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_billing_history_request


@require_auth
@rate_limit(requests_per_minute=60)
@payment_resilience("get_billing_history")
@measure_performance("payment_get_billing_history")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get billing history for tenant.

    GET /payment/billing/history?limit=10&starting_after=inv_123&status=paid&date_from=2025-01-01&date_to=2025-12-31
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/payment/billing/history'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate query parameters
        query_params = event.get('queryStringParameters') or {}
        validated_params = validate_billing_history_request(query_params)

        limit = validated_params.get('limit', 10)
        starting_after = validated_params.get('starting_after')
        status = validated_params.get('status')
        date_from = validated_params.get('date_from')
        date_to = validated_params.get('date_to')
        invoice_type = validated_params.get('invoice_type')

        lambda_logger.info("Getting billing history", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'limit': limit,
            'starting_after': starting_after,
            'status': status,
            'date_from': date_from,
            'date_to': date_to,
            'invoice_type': invoice_type
        })

        # Check permissions (MASTER and ADMIN can view billing)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning("Insufficient permissions for billing history", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to view billing history",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Get billing history with filters
        billing_result = subscription_service_instance.get_billing_history(
            tenant_id=auth_context.tenant_id,
            limit=limit,
            starting_after=starting_after,
            status=status,
            date_from=date_from,
            date_to=date_to,
            invoice_type=invoice_type
        )

        # Prepare response data
        response_data = {
            'billing_history': billing_result.get('invoices', []),
            'summary': billing_result.get('summary', {}),
            'pagination': {
                'limit': limit,
                'starting_after': starting_after,
                'has_more': billing_result.get('has_more', False),
                'total_count': billing_result.get('total_count', 0)
            },
            'filters': {
                'status': status,
                'date_from': date_from,
                'date_to': date_to,
                'invoice_type': invoice_type
            },
            'metadata': billing_result.get('metadata', {})
        }

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/billing/history",
            method="GET",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/payment/billing/history',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message=f"Retrieved {len(response_data['billing_history'])} billing records"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in get billing history", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in get billing history", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in get billing history", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in get billing history", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while retrieving billing history"
        )
