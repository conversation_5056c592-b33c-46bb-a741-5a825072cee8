#!/usr/bin/env python3
# services/payment/src/handlers/resume_subscription.py
# Resume subscription handler with unified shared layer integration

"""
Resume subscription handler.
Handles resuming paused subscriptions with proper validation and audit logging.
"""

import json
from typing import Any, Dict

# Shared layer imports
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Service imports
from ..config.dependencies import container
from ..config.rate_limits import get_endpoint_rate_limit
from ..services.subscription_service import ISubscriptionService
from ..validators.subscription_validators import validate_resume_subscription_request


@require_auth
@rate_limit(requests_per_minute=get_endpoint_rate_limit("update_subscription"))
@payment_resilience("resume_subscription")
@measure_performance("payment_resume_subscription")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Resume paused subscription.

    POST /payment/subscriptions/{id}/resume
    {
        "immediate": true,
        "reason": "financial_situation_improved"
    }
    """

    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = get_auth_context(event)

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/payment/subscriptions/{id}/resume'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )

    start_time = lambda_logger.get_current_timestamp()

    # Get subscription ID from path parameters
    subscription_id = event.get('pathParameters', {}).get('id')
    if not subscription_id:
        return APIResponse.validation_error(
            message="Subscription ID is required",
            validation_errors=[{"field": "id", "message": "Subscription ID is required in path"}]
        )

    try:
        # Parse and validate request body
        try:
            body_data = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate request data
        validated_data = validate_resume_subscription_request(body_data)

        immediate = validated_data.get('immediate', True)
        reason = validated_data.get('reason', 'user_requested')

        lambda_logger.info("Resuming subscription", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id,
            'immediate': immediate,
            'reason': reason
        })

        # Get subscription service from DI container
        subscription_service_instance = container.resolve(ISubscriptionService)

        # Resume subscription (synchronous operation)
        subscription_result = subscription_service_instance.resume_subscription(
            subscription_id=subscription_id,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            immediate=immediate,
            reason=reason
        )

        # Prepare response data
        response_data = {
            'subscription': subscription_result.get('subscription', {}),
            'resume_details': subscription_result.get('resume_details', {}),
            'billing_info': subscription_result.get('billing_info', {}),
            'next_steps': []
        }

        # Add next steps
        if subscription_result.get('immediate'):
            response_data['next_steps'].append('Subscription is now active and billing will resume immediately')
        else:
            response_data['next_steps'].append(f"Subscription will resume on {subscription_result.get('resume_date', 'next billing cycle')}")

        # Log audit event for subscription resume
        audit_log(
            lambda_logger,
            "subscription_resumed",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            details={
                'subscription_id': subscription_id,
                'immediate': immediate,
                'reason': reason
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=auth_context.tenant_id,
            endpoint="/payment/subscriptions/{id}/resume",
            method="POST",
            status_code=200
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/payment/subscriptions/{id}/resume',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Subscription resumed successfully"
        )

    except ValidationException as e:
        lambda_logger.warning("Validation error in resume subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id,
            'validation_errors': getattr(e, 'validation_errors', [])
        })

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', [])
        )

    except PaymentException as e:
        lambda_logger.error("Payment error in resume subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.error(
            message=e.message,
            status_code=402,
            error_code=getattr(e, 'error_code', 'PAYMENT_ERROR')
        )

    except ResourceNotFoundException as e:
        lambda_logger.warning("Resource not found in resume subscription", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.not_found(
            message=e.message,
            error_code=getattr(e, 'error_code', 'RESOURCE_NOT_FOUND')
        )

    except Exception as e:
        lambda_logger.error("Unexpected error in resume subscription", extra={
            'error': str(e),
            'error_type': type(e).__name__,
            'tenant_id': auth_context.tenant_id if auth_context else None,
            'user_id': auth_context.user_id if auth_context else None,
            'subscription_id': subscription_id if 'subscription_id' in locals() else None,
            'request_id': request_id
        })

        return APIResponse.internal_error(
            message="An unexpected error occurred while resuming subscription"
        )
