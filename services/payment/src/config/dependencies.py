#!/usr/bin/env python3
# services/payment/src/config/dependencies.py
# Dependency injection configuration for payment service

"""
Dependency injection configuration for payment service.
Configures service dependencies and interfaces.
"""

from shared.dependency_injection import container
from shared.logger import lambda_logger

# Import service interfaces and implementations
from ..services.subscription_service import ISubscriptionService, SubscriptionService
from ..services.payment_service import IPaymentService, PaymentService


def configure_dependencies():
    """
    Configure dependency injection for payment service.

    This function registers the subscription service implementation with its interface
    in the dependency injection container.
    """
    try:
        # Register subscription service as singleton for better performance
        container.register_singleton(ISubscriptionService, SubscriptionService)

        # Register payment service as singleton
        container.register_singleton(IPaymentService, PaymentService)

        lambda_logger.info("Payment service dependencies configured successfully")

    except Exception as e:
        lambda_logger.error(f"Failed to configure payment service dependencies: {str(e)}")
        raise


# Auto-configure dependencies when module is imported
configure_dependencies()
