# services/payment/src/config/rate_limits.py
# Standardized rate limiting configuration for payment service

"""
Standardized rate limiting configuration for payment service.
Defines consistent rate limits across all payment endpoints.
"""

from typing import Dict, Any
from enum import Enum


class PaymentEndpointType(Enum):
    """Payment endpoint types for rate limiting."""
    # Read operations (higher limits)
    READ_SUBSCRIPTION = "read_subscription"
    READ_PLANS = "read_plans"
    READ_BILLING = "read_billing"
    READ_INVOICES = "read_invoices"
    
    # Write operations (moderate limits)
    CREATE_SUBSCRIPTION = "create_subscription"
    UPDATE_SUBSCRIPTION = "update_subscription"
    CHANGE_PLAN = "change_plan"
    UPDATE_PAYMENT_METHOD = "update_payment_method"
    
    # Critical operations (lower limits)
    CANCEL_SUBSCRIPTION = "cancel_subscription"
    PROCESS_PAYMENT = "process_payment"
    APPLY_COUPON = "apply_coupon"
    DOWNLOAD_INVOICE = "download_invoice"
    
    # Webhook operations (special limits)
    WEBHOOK = "webhook"


# Standardized rate limits per endpoint type
PAYMENT_RATE_LIMITS: Dict[PaymentEndpointType, Dict[str, int]] = {
    # Read operations - High frequency allowed
    PaymentEndpointType.READ_SUBSCRIPTION: {
        "requests_per_minute": 120,
        "burst_limit": 20,
        "daily_limit": 10000
    },
    PaymentEndpointType.READ_PLANS: {
        "requests_per_minute": 120,
        "burst_limit": 20,
        "daily_limit": 10000
    },
    PaymentEndpointType.READ_BILLING: {
        "requests_per_minute": 60,
        "burst_limit": 15,
        "daily_limit": 5000
    },
    PaymentEndpointType.READ_INVOICES: {
        "requests_per_minute": 60,
        "burst_limit": 15,
        "daily_limit": 5000
    },
    
    # Write operations - Moderate frequency
    PaymentEndpointType.CREATE_SUBSCRIPTION: {
        "requests_per_minute": 20,
        "burst_limit": 5,
        "daily_limit": 1000
    },
    PaymentEndpointType.UPDATE_SUBSCRIPTION: {
        "requests_per_minute": 30,
        "burst_limit": 8,
        "daily_limit": 2000
    },
    PaymentEndpointType.CHANGE_PLAN: {
        "requests_per_minute": 15,
        "burst_limit": 3,
        "daily_limit": 500
    },
    PaymentEndpointType.UPDATE_PAYMENT_METHOD: {
        "requests_per_minute": 20,
        "burst_limit": 5,
        "daily_limit": 1000
    },
    
    # Critical operations - Low frequency
    PaymentEndpointType.CANCEL_SUBSCRIPTION: {
        "requests_per_minute": 10,
        "burst_limit": 2,
        "daily_limit": 100
    },
    PaymentEndpointType.PROCESS_PAYMENT: {
        "requests_per_minute": 30,
        "burst_limit": 5,
        "daily_limit": 2000
    },
    PaymentEndpointType.APPLY_COUPON: {
        "requests_per_minute": 30,
        "burst_limit": 5,
        "daily_limit": 1000
    },
    PaymentEndpointType.DOWNLOAD_INVOICE: {
        "requests_per_minute": 30,
        "burst_limit": 5,
        "daily_limit": 1000
    },
    
    # Webhook operations - Special handling
    PaymentEndpointType.WEBHOOK: {
        "requests_per_minute": 300,  # High for webhook bursts
        "burst_limit": 50,
        "daily_limit": 50000
    }
}


def get_rate_limit_for_endpoint(endpoint_type: PaymentEndpointType) -> int:
    """
    Get standardized rate limit for payment endpoint.
    
    Args:
        endpoint_type: Type of payment endpoint
        
    Returns:
        Rate limit in requests per minute
    """
    return PAYMENT_RATE_LIMITS.get(endpoint_type, {}).get("requests_per_minute", 60)


def get_rate_limit_config(endpoint_type: PaymentEndpointType) -> Dict[str, int]:
    """
    Get complete rate limit configuration for payment endpoint.
    
    Args:
        endpoint_type: Type of payment endpoint
        
    Returns:
        Complete rate limit configuration
    """
    return PAYMENT_RATE_LIMITS.get(endpoint_type, {
        "requests_per_minute": 60,
        "burst_limit": 10,
        "daily_limit": 5000
    })


# Endpoint mapping for easy lookup
ENDPOINT_TYPE_MAPPING: Dict[str, PaymentEndpointType] = {
    # Subscription endpoints
    "get_subscription": PaymentEndpointType.READ_SUBSCRIPTION,
    "list_subscriptions": PaymentEndpointType.READ_SUBSCRIPTION,
    "create_subscription": PaymentEndpointType.CREATE_SUBSCRIPTION,
    "update_subscription": PaymentEndpointType.UPDATE_SUBSCRIPTION,
    "cancel_subscription": PaymentEndpointType.CANCEL_SUBSCRIPTION,
    "resume_subscription": PaymentEndpointType.UPDATE_SUBSCRIPTION,
    "pause_subscription": PaymentEndpointType.UPDATE_SUBSCRIPTION,
    "change_plan": PaymentEndpointType.CHANGE_PLAN,
    
    # Plan endpoints
    "list_plans": PaymentEndpointType.READ_PLANS,
    "get_plan": PaymentEndpointType.READ_PLANS,
    
    # Billing endpoints
    "get_billing_history": PaymentEndpointType.READ_BILLING,
    "get_usage": PaymentEndpointType.READ_BILLING,
    "process_payment": PaymentEndpointType.PROCESS_PAYMENT,
    
    # Invoice endpoints
    "list_invoices": PaymentEndpointType.READ_INVOICES,
    "get_invoice": PaymentEndpointType.READ_INVOICES,
    "download_invoice": PaymentEndpointType.DOWNLOAD_INVOICE,
    
    # Payment method endpoints
    "update_payment_method": PaymentEndpointType.UPDATE_PAYMENT_METHOD,
    "add_payment_method": PaymentEndpointType.UPDATE_PAYMENT_METHOD,
    "remove_payment_method": PaymentEndpointType.UPDATE_PAYMENT_METHOD,
    
    # Coupon endpoints
    "apply_coupon": PaymentEndpointType.APPLY_COUPON,
    "remove_coupon": PaymentEndpointType.APPLY_COUPON,
    
    # Webhook endpoints
    "stripe_webhook": PaymentEndpointType.WEBHOOK,
    "payu_webhook": PaymentEndpointType.WEBHOOK
}


def get_endpoint_rate_limit(endpoint_name: str) -> int:
    """
    Get rate limit for specific endpoint by name.
    
    Args:
        endpoint_name: Name of the endpoint
        
    Returns:
        Rate limit in requests per minute
    """
    endpoint_type = ENDPOINT_TYPE_MAPPING.get(endpoint_name)
    if endpoint_type:
        return get_rate_limit_for_endpoint(endpoint_type)
    
    # Default rate limit for unknown endpoints
    return 60
