# services/payment/src/config/settings.py
# Centralized configuration for payment service

"""
Centralized configuration management for payment service.
Consolidates all configuration settings in one place for better maintainability.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class PaymentServiceConfig:
    """Payment service configuration."""
    
    # Service identification
    service_name: str = "payment"
    service_version: str = "1.0.0"
    
    # Environment settings
    environment: str = os.getenv("ENVIRONMENT", "dev")
    region: str = os.getenv("AWS_REGION", "us-east-1")
    
    # Database settings
    dynamodb_table: str = os.getenv("DYNAMODB_TABLE", "agent-scl-main-table")
    
    # Stripe configuration (will be loaded from AWS Secrets Manager)
    stripe_publishable_key: Optional[str] = None
    stripe_test_secret_key: Optional[str] = None
    stripe_live_secret_key: Optional[str] = None
    stripe_webhook_secret: Optional[str] = None
    
    # PayU configuration (for LATAM)
    payu_api_key: Optional[str] = os.getenv("PAYU_API_KEY")
    payu_merchant_id: Optional[str] = os.getenv("PAYU_MERCHANT_ID")
    payu_account_id: Optional[str] = os.getenv("PAYU_ACCOUNT_ID")
    
    # Rate limiting settings
    default_rate_limit: int = 60
    burst_rate_limit: int = 10
    daily_rate_limit: int = 5000
    
    # Subscription settings
    default_trial_days: int = 14
    max_trial_days: int = 30
    grace_period_days: int = 3
    
    # Invoice settings
    invoice_due_days: int = 30
    late_fee_percentage: float = 0.05
    max_retry_attempts: int = 3
    
    # Currency settings
    default_currency: str = "USD"
    supported_currencies: list = None
    
    # Feature flags
    enable_proration: bool = True
    enable_coupons: bool = True
    enable_tax_calculation: bool = False
    enable_dunning_management: bool = True
    
    # Logging settings
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    enable_audit_logging: bool = True
    enable_performance_logging: bool = True
    
    # Cache settings
    cache_ttl_seconds: int = 300  # 5 minutes
    cache_max_size: int = 1000
    
    # Webhook settings
    webhook_timeout_seconds: int = 30
    webhook_retry_attempts: int = 3
    webhook_retry_delay_seconds: int = 5
    
    def __post_init__(self):
        """Post-initialization setup."""
        if self.supported_currencies is None:
            self.supported_currencies = ["USD", "EUR", "GBP", "CAD", "AUD", "COP", "MXN", "BRL"]

        # Load Stripe credentials from AWS Secrets Manager
        self._load_stripe_credentials()

    def _load_stripe_credentials(self):
        """Load Stripe credentials from AWS Secrets Manager."""
        try:
            from shared.secrets import get_integration_credentials

            stripe_credentials = get_integration_credentials('stripe')
            if stripe_credentials:
                self.stripe_publishable_key = stripe_credentials.get('publishable_key')
                self.stripe_test_secret_key = stripe_credentials.get('test_secret_key')
                self.stripe_live_secret_key = stripe_credentials.get('live_secret_key')
                self.stripe_webhook_secret = stripe_credentials.get('webhook_secret')

                # Use test key for dev environment, live key for production
                if self.is_development:
                    self.stripe_secret_key = self.stripe_test_secret_key
                else:
                    self.stripe_secret_key = self.stripe_live_secret_key

        except Exception as e:
            # In development, log warning instead of failing
            if self.is_development:
                print(f"⚠️ Warning: Could not load Stripe credentials: {e}")
            else:
                raise ValueError(f"Failed to load Stripe credentials: {e}")

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() in ["prod", "production"]
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() in ["dev", "development", "local"]
    
    def get_stripe_config(self) -> Dict[str, str]:
        """Get Stripe configuration."""
        return {
            "publishable_key": self.stripe_publishable_key,
            "secret_key": self.stripe_secret_key,
            "webhook_secret": self.stripe_webhook_secret
        }
    
    def get_payu_config(self) -> Dict[str, str]:
        """Get PayU configuration."""
        return {
            "api_key": self.payu_api_key,
            "merchant_id": self.payu_merchant_id,
            "account_id": self.payu_account_id
        }
    
    def get_rate_limit_config(self) -> Dict[str, int]:
        """Get rate limiting configuration."""
        return {
            "default_rate_limit": self.default_rate_limit,
            "burst_rate_limit": self.burst_rate_limit,
            "daily_rate_limit": self.daily_rate_limit
        }
    
    def get_subscription_config(self) -> Dict[str, Any]:
        """Get subscription configuration."""
        return {
            "default_trial_days": self.default_trial_days,
            "max_trial_days": self.max_trial_days,
            "grace_period_days": self.grace_period_days,
            "enable_proration": self.enable_proration
        }
    
    def get_invoice_config(self) -> Dict[str, Any]:
        """Get invoice configuration."""
        return {
            "due_days": self.invoice_due_days,
            "late_fee_percentage": self.late_fee_percentage,
            "max_retry_attempts": self.max_retry_attempts
        }
    
    def validate_config(self) -> bool:
        """Validate configuration settings."""
        errors = []
        
        # Validate required Stripe settings for production
        if self.is_production:
            if not self.stripe_secret_key:
                errors.append("STRIPE_SECRET_KEY is required in production")
            if not self.stripe_webhook_secret:
                errors.append("STRIPE_WEBHOOK_SECRET is required in production")
        
        # Validate numeric settings
        if self.default_trial_days < 0 or self.default_trial_days > self.max_trial_days:
            errors.append("Invalid trial days configuration")
        
        if self.invoice_due_days <= 0:
            errors.append("Invoice due days must be positive")
        
        if self.late_fee_percentage < 0 or self.late_fee_percentage > 1:
            errors.append("Late fee percentage must be between 0 and 1")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {', '.join(errors)}")
        
        return True


# Global configuration instance
config = PaymentServiceConfig()

# Validate configuration on import
try:
    config.validate_config()
except ValueError as e:
    # In development, log warning instead of failing
    if config.is_development:
        print(f"⚠️ Configuration warning: {e}")
    else:
        raise


def get_config() -> PaymentServiceConfig:
    """Get payment service configuration."""
    return config


def update_config(**kwargs) -> None:
    """Update configuration settings."""
    global config
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            raise ValueError(f"Unknown configuration key: {key}")


# Export commonly used settings
ENVIRONMENT = config.environment
IS_PRODUCTION = config.is_production
IS_DEVELOPMENT = config.is_development
DEFAULT_CURRENCY = config.default_currency
SUPPORTED_CURRENCIES = config.supported_currencies
