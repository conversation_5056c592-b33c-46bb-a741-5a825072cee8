# services/payment/src/services/payu_client.py
# PayU payment gateway integration client

"""
PayU Payment Gateway Client

This service handles integration with PayU payment gateway for processing
payments in Latin American markets. It provides:
1. Payment creation and processing
2. Webhook handling for payment notifications
3. Subscription management
4. Multi-currency support
"""

import json
import hashlib
import hmac
import requests
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from decimal import Decimal

from shared.logger import lambda_logger
from shared.config import get_settings
from shared.exceptions import PaymentException, ValidationException

from ..config.payment_config import get_payment_config


class PayUClient:
    """
    PayU payment gateway client for processing payments.
    
    Supports:
    - Credit/debit card payments
    - Bank transfers
    - Cash payments (Efecty, Baloto, etc.)
    - Recurring payments/subscriptions
    """
    
    def __init__(self):
        self.config = get_payment_config()
        self.settings = get_settings()
        
        # PayU configuration
        self.api_key = self.settings.get('PAYU_API_KEY')
        self.merchant_id = self.settings.get('PAYU_MERCHANT_ID')
        self.account_id = self.settings.get('PAYU_ACCOUNT_ID')
        self.api_login = self.settings.get('PAYU_API_LOGIN')
        
        # Environment URLs
        self.is_sandbox = self.settings.get('PAYU_SANDBOX', 'true').lower() == 'true'
        if self.is_sandbox:
            self.payments_url = "https://sandbox.api.payulatam.com/payments-api/4.0/service.cgi"
            self.reports_url = "https://sandbox.api.payulatam.com/reports-api/4.0/service.cgi"
        else:
            self.payments_url = "https://api.payulatam.com/payments-api/4.0/service.cgi"
            self.reports_url = "https://api.payulatam.com/reports-api/4.0/service.cgi"
    
    def create_payment(
        self,
        amount: Decimal,
        currency: str,
        description: str,
        reference_code: str,
        buyer_info: Dict[str, Any],
        payment_method: str = "CARD",
        card_info: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Create a payment with PayU.
        
        Args:
            amount: Payment amount
            currency: Currency code (COP, USD, etc.)
            description: Payment description
            reference_code: Unique reference for the payment
            buyer_info: Buyer information (name, email, etc.)
            payment_method: Payment method (CARD, PSE, EFECTY, etc.)
            card_info: Card information if payment_method is CARD
        
        Returns:
            Tuple of (success, response_data, error_message)
        """
        try:
            # Generate signature
            signature = self._generate_signature(reference_code, amount, currency)
            
            # Build payment request
            payment_request = {
                "language": "es",
                "command": "SUBMIT_TRANSACTION",
                "merchant": {
                    "apiKey": self.api_key,
                    "apiLogin": self.api_login
                },
                "transaction": {
                    "order": {
                        "accountId": self.account_id,
                        "referenceCode": reference_code,
                        "description": description,
                        "language": "es",
                        "signature": signature,
                        "notifyUrl": f"{self.config.get('webhook_base_url')}/payment/payu/webhook",
                        "additionalValues": {
                            "TX_VALUE": {
                                "value": float(amount),
                                "currency": currency
                            }
                        },
                        "buyer": {
                            "merchantBuyerId": buyer_info.get("id"),
                            "fullName": buyer_info.get("name"),
                            "emailAddress": buyer_info.get("email"),
                            "contactPhone": buyer_info.get("phone", ""),
                            "dniNumber": buyer_info.get("document_number", ""),
                            "shippingAddress": {
                                "street1": buyer_info.get("address", ""),
                                "city": buyer_info.get("city", ""),
                                "state": buyer_info.get("state", ""),
                                "country": buyer_info.get("country", "CO"),
                                "postalCode": buyer_info.get("postal_code", "")
                            }
                        }
                    },
                    "type": "AUTHORIZATION_AND_CAPTURE",
                    "paymentMethod": payment_method,
                    "paymentCountry": buyer_info.get("country", "CO"),
                    "deviceSessionId": buyer_info.get("device_session_id"),
                    "ipAddress": buyer_info.get("ip_address"),
                    "cookie": buyer_info.get("cookie"),
                    "userAgent": buyer_info.get("user_agent")
                }
            }
            
            # Add credit card information if provided
            if payment_method == "CARD" and card_info:
                payment_request["transaction"]["creditCard"] = {
                    "number": card_info.get("number"),
                    "securityCode": card_info.get("cvv"),
                    "expirationDate": card_info.get("expiry_date"),
                    "name": card_info.get("holder_name")
                }
            
            # Make API request
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            response = requests.post(
                self.payments_url,
                json=payment_request,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get("code") == "SUCCESS":
                    transaction = response_data.get("transactionResponse", {})
                    
                    return True, {
                        "transaction_id": transaction.get("transactionId"),
                        "order_id": transaction.get("orderId"),
                        "state": transaction.get("state"),
                        "response_code": transaction.get("responseCode"),
                        "response_message": transaction.get("responseMessage"),
                        "authorization_code": transaction.get("authorizationCode"),
                        "payment_network_response_code": transaction.get("paymentNetworkResponseCode"),
                        "payment_network_response_error_message": transaction.get("paymentNetworkResponseErrorMessage"),
                        "trazability_code": transaction.get("trazabilityCode"),
                        "reference_code": reference_code,
                        "amount": float(amount),
                        "currency": currency
                    }, None
                else:
                    error_msg = response_data.get("error", "Unknown PayU error")
                    return False, {}, f"PayU API error: {error_msg}"
            else:
                return False, {}, f"PayU API request failed: {response.status_code}"
                
        except Exception as e:
            lambda_logger.error(f"PayU payment creation failed: {str(e)}")
            return False, {}, str(e)
    
    def create_subscription(
        self,
        plan_code: str,
        customer_info: Dict[str, Any],
        credit_card_info: Dict[str, Any],
        trial_days: Optional[int] = None
    ) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        Create a recurring payment subscription with PayU.
        
        Args:
            plan_code: PayU plan code for the subscription
            customer_info: Customer information
            credit_card_info: Credit card information
            trial_days: Optional trial period in days
        
        Returns:
            Tuple of (success, response_data, error_message)
        """
        try:
            # Create subscription request
            subscription_request = {
                "language": "es",
                "command": "CREATE_SUBSCRIPTION",
                "merchant": {
                    "apiKey": self.api_key,
                    "apiLogin": self.api_login
                },
                "subscription": {
                    "plan": {
                        "planCode": plan_code
                    },
                    "customer": {
                        "fullName": customer_info.get("name"),
                        "email": customer_info.get("email")
                    },
                    "creditCardToken": credit_card_info.get("token")
                }
            }
            
            if trial_days:
                subscription_request["subscription"]["trialDays"] = trial_days
            
            # Make API request
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            response = requests.post(
                self.payments_url,
                json=subscription_request,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get("code") == "SUCCESS":
                    subscription = response_data.get("subscription", {})
                    
                    return True, {
                        "subscription_id": subscription.get("id"),
                        "plan_code": plan_code,
                        "customer_id": subscription.get("customer", {}).get("id"),
                        "credit_card_token": subscription.get("creditCardToken"),
                        "trial_days": trial_days
                    }, None
                else:
                    error_msg = response_data.get("error", "Unknown PayU subscription error")
                    return False, {}, f"PayU subscription error: {error_msg}"
            else:
                return False, {}, f"PayU subscription request failed: {response.status_code}"
                
        except Exception as e:
            lambda_logger.error(f"PayU subscription creation failed: {str(e)}")
            return False, {}, str(e)
    
    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Verify PayU webhook signature for security.
        
        Args:
            payload: Raw webhook payload
            signature: Signature from PayU headers
        
        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # PayU uses MD5 hash for webhook signatures
            expected_signature = hashlib.md5(
                f"{self.api_key}~{self.merchant_id}~{payload}".encode('utf-8')
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
            
        except Exception as e:
            lambda_logger.error(f"PayU signature verification failed: {str(e)}")
            return False
    
    def _generate_signature(self, reference_code: str, amount: Decimal, currency: str) -> str:
        """
        Generate PayU signature for payment requests.
        
        Args:
            reference_code: Payment reference code
            amount: Payment amount
            currency: Currency code
        
        Returns:
            Generated signature string
        """
        # PayU signature format: ApiKey~merchantId~referenceCode~amount~currency
        signature_string = f"{self.api_key}~{self.merchant_id}~{reference_code}~{amount}~{currency}"
        
        return hashlib.md5(signature_string.encode('utf-8')).hexdigest()


# Create singleton instance
payu_client = PayUClient()
