# services/payment/src/services/payment_service.py
# Payment service for handling payment operations

"""
Payment Service

This service handles payment operations including:
1. Payment processing
2. Webhook handling
3. Payment status updates
4. Integration with multiple payment gateways
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, Optional
from datetime import datetime

from shared.logger import lambda_logger
from shared.database import DynamoDBClient
from shared.config import get_settings
from shared.exceptions import PaymentException, DatabaseException

from ..config.payment_config import get_payment_config


class IPaymentService(ABC):
    """Interface for payment service operations."""
    
    @abstractmethod
    def handle_payment_approved(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        amount: float,
        currency: str,
        payment_method: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle approved payment notification."""
        pass
    
    @abstractmethod
    def handle_payment_declined(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        response_code: str,
        webhook_data: Dict[str, Any]
    ) -> <PERSON><PERSON>[bool, Optional[str]]:
        """Handle declined payment notification."""
        pass
    
    @abstractmethod
    def handle_payment_error(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        response_code: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle payment error notification."""
        pass
    
    @abstractmethod
    def handle_payment_pending(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle pending payment notification."""
        pass


class PaymentService(IPaymentService):
    """
    Payment service implementation.
    
    Handles payment operations and webhook processing.
    """
    
    def __init__(self):
        self.config = get_payment_config()
        self.settings = get_settings()
        self.db_client = DynamoDBClient(self.config.get('dynamodb_table_name'))
    
    def handle_payment_approved(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        amount: float,
        currency: str,
        payment_method: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle approved payment notification."""
        try:
            # Update payment record in database
            payment_update = {
                'status': 'approved',
                'transaction_id': transaction_id,
                'payu_reference': payu_reference,
                'amount': amount,
                'currency': currency,
                'payment_method': payment_method,
                'approved_at': datetime.utcnow().isoformat(),
                'webhook_data': webhook_data
            }
            
            # Find payment by reference code
            payment_record = self._get_payment_by_reference(reference_code)
            if not payment_record:
                lambda_logger.error(f"Payment not found for reference: {reference_code}")
                return False, f"Payment not found for reference: {reference_code}"
            
            # Update payment status
            success = self._update_payment_status(
                payment_record['tenant_id'],
                payment_record['payment_id'],
                payment_update
            )
            
            if success:
                # Activate subscription if this is a subscription payment
                if payment_record.get('subscription_id'):
                    self._activate_subscription(
                        payment_record['tenant_id'],
                        payment_record['subscription_id']
                    )
                
                lambda_logger.info(f"Payment approved successfully: {reference_code}")
                return True, None
            else:
                return False, "Failed to update payment status"
                
        except Exception as e:
            lambda_logger.error(f"Error handling approved payment: {str(e)}")
            return False, str(e)
    
    def handle_payment_declined(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        response_code: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle declined payment notification."""
        try:
            # Update payment record in database
            payment_update = {
                'status': 'declined',
                'transaction_id': transaction_id,
                'payu_reference': payu_reference,
                'response_code': response_code,
                'declined_at': datetime.utcnow().isoformat(),
                'webhook_data': webhook_data
            }
            
            # Find payment by reference code
            payment_record = self._get_payment_by_reference(reference_code)
            if not payment_record:
                lambda_logger.error(f"Payment not found for reference: {reference_code}")
                return False, f"Payment not found for reference: {reference_code}"
            
            # Update payment status
            success = self._update_payment_status(
                payment_record['tenant_id'],
                payment_record['payment_id'],
                payment_update
            )
            
            if success:
                lambda_logger.info(f"Payment declined processed: {reference_code}")
                return True, None
            else:
                return False, "Failed to update payment status"
                
        except Exception as e:
            lambda_logger.error(f"Error handling declined payment: {str(e)}")
            return False, str(e)
    
    def handle_payment_error(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        response_code: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle payment error notification."""
        try:
            # Update payment record in database
            payment_update = {
                'status': 'error',
                'transaction_id': transaction_id,
                'payu_reference': payu_reference,
                'response_code': response_code,
                'error_at': datetime.utcnow().isoformat(),
                'webhook_data': webhook_data
            }
            
            # Find payment by reference code
            payment_record = self._get_payment_by_reference(reference_code)
            if not payment_record:
                lambda_logger.error(f"Payment not found for reference: {reference_code}")
                return False, f"Payment not found for reference: {reference_code}"
            
            # Update payment status
            success = self._update_payment_status(
                payment_record['tenant_id'],
                payment_record['payment_id'],
                payment_update
            )
            
            if success:
                lambda_logger.info(f"Payment error processed: {reference_code}")
                return True, None
            else:
                return False, "Failed to update payment status"
                
        except Exception as e:
            lambda_logger.error(f"Error handling payment error: {str(e)}")
            return False, str(e)
    
    def handle_payment_pending(
        self,
        reference_code: str,
        transaction_id: str,
        payu_reference: str,
        webhook_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """Handle pending payment notification."""
        try:
            # Update payment record in database
            payment_update = {
                'status': 'pending',
                'transaction_id': transaction_id,
                'payu_reference': payu_reference,
                'pending_at': datetime.utcnow().isoformat(),
                'webhook_data': webhook_data
            }
            
            # Find payment by reference code
            payment_record = self._get_payment_by_reference(reference_code)
            if not payment_record:
                lambda_logger.error(f"Payment not found for reference: {reference_code}")
                return False, f"Payment not found for reference: {reference_code}"
            
            # Update payment status
            success = self._update_payment_status(
                payment_record['tenant_id'],
                payment_record['payment_id'],
                payment_update
            )
            
            if success:
                lambda_logger.info(f"Payment pending processed: {reference_code}")
                return True, None
            else:
                return False, "Failed to update payment status"
                
        except Exception as e:
            lambda_logger.error(f"Error handling pending payment: {str(e)}")
            return False, str(e)
    
    def _get_payment_by_reference(self, reference_code: str) -> Optional[Dict[str, Any]]:
        """Get payment record by reference code."""
        try:
            # Query using GSI on reference_code
            # Note: This assumes a GSI exists on reference_code field
            response = self.db_client.query_gsi(
                gsi_name='ReferenceCodeIndex',
                pk_name='reference_code',
                pk_value=reference_code
            )

            if response and len(response) > 0:
                payment_record = response[0]
                return {
                    'tenant_id': payment_record.get('tenant_id'),
                    'payment_id': payment_record.get('payment_id'),
                    'subscription_id': payment_record.get('subscription_id')
                }

            # Fallback: If GSI doesn't exist or no record found, return None
            lambda_logger.warning(f"Payment not found for reference: {reference_code}")
            return None

        except Exception as e:
            lambda_logger.error(f"Error getting payment by reference: {str(e)}")
            return None
    
    def _update_payment_status(
        self,
        tenant_id: str,
        payment_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update payment status in database."""
        try:
            return self.db_client.update_item(
                pk=f"PAYMENT#{payment_id}",
                sk="DETAILS",
                updates=updates,
                tenant_id=tenant_id
            )
        except Exception as e:
            lambda_logger.error(f"Error updating payment status: {str(e)}")
            return False
    
    def _activate_subscription(self, tenant_id: str, subscription_id: str) -> bool:
        """Activate subscription after successful payment."""
        try:
            updates = {
                'status': 'active',
                'activated_at': datetime.utcnow().isoformat()
            }
            
            return self.db_client.update_item(
                pk=f"SUBSCRIPTION#{subscription_id}",
                sk="DETAILS",
                updates=updates,
                tenant_id=tenant_id
            )
        except Exception as e:
            lambda_logger.error(f"Error activating subscription: {str(e)}")
            return False
