#!/usr/bin/env python3
# services/payment/src/services/payment_validation_service.py
# Payment validation service

"""
Payment validation service for checking funds availability and payment method validity.
"""

import stripe
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from shared.logger import lambda_logger
from shared.exceptions import PaymentException, ValidationException
from shared.secrets import get_integration_credentials
from shared.service_communication import ServiceCommunicationManager
from shared.database import DynamoDBClient
from shared.validators import UnifiedUserValidator

from .subscription_service import ISubscriptionService
from ..config.dependencies import container


class PaymentValidationService:
    """Service for validating payment methods and funds availability."""
    
    def __init__(self):
        """Initialize payment validation service with unified communication."""
        self.stripe_credentials = get_integration_credentials('stripe')
        stripe.api_key = self.stripe_credentials['secret_key']
        self.subscription_service = container.get(ISubscriptionService)
        # Initialize service communication for cross-service validation
        self.db_client = DynamoDBClient()
        self.service_comm = ServiceCommunicationManager(self.db_client)
    
    async def validate_upcoming_payment(self, tenant_id: str, subscription_id: str) -> Dict[str, Any]:
        """
        Validate upcoming subscription payment.
        
        Args:
            tenant_id: Tenant ID
            subscription_id: Subscription ID
            
        Returns:
            Validation result dictionary
        """
        try:
            # Get subscription details
            subscription = await self.subscription_service.get_subscription(subscription_id)
            
            if not subscription or subscription['tenant_id'] != tenant_id:
                raise ValidationException("Subscription not found")
            
            # Get Stripe subscription
            stripe_subscription = stripe.Subscription.retrieve(subscription['stripe_subscription_id'])
            
            # Check subscription status
            if stripe_subscription.status not in ['active', 'trialing']:
                return {
                    'valid': False,
                    'reason': f"Subscription status is {stripe_subscription.status}",
                    'checked_at': datetime.utcnow().isoformat(),
                    'subscription_info': {
                        'status': stripe_subscription.status,
                        'subscription_id': subscription_id
                    }
                }
            
            # Get upcoming invoice
            upcoming_invoice = stripe.Invoice.upcoming(
                subscription=stripe_subscription.id
            )
            
            # Validate payment method
            payment_method_valid = await self._validate_payment_method(
                stripe_subscription.default_payment_method
            )
            
            result = {
                'valid': payment_method_valid['valid'],
                'reason': payment_method_valid.get('reason'),
                'checked_at': datetime.utcnow().isoformat(),
                'subscription_info': {
                    'status': stripe_subscription.status,
                    'subscription_id': subscription_id,
                    'next_payment_date': datetime.fromtimestamp(
                        upcoming_invoice.period_end
                    ).isoformat(),
                    'next_payment_amount': upcoming_invoice.amount_due
                },
                'payment_methods_available': 1 if payment_method_valid['valid'] else 0,
                'default_payment_method_valid': payment_method_valid['valid']
            }
            
            if not payment_method_valid['valid']:
                result['recommendations'] = [
                    "Update your payment method",
                    "Ensure your card has sufficient funds",
                    "Check that your card hasn't expired"
                ]
            
            return result
            
        except stripe.error.StripeError as e:
            lambda_logger.error("Stripe error in upcoming payment validation", extra={
                'error': str(e),
                'subscription_id': subscription_id
            })
            raise PaymentException(f"Payment validation failed: {str(e)}")
    
    async def validate_manual_charge(self, tenant_id: str, amount: int, currency: str) -> Dict[str, Any]:
        """
        Validate manual charge amount.
        
        Args:
            tenant_id: Tenant ID
            amount: Amount in cents
            currency: Currency code
            
        Returns:
            Validation result dictionary
        """
        try:
            # Get customer for tenant
            subscription = await self.subscription_service.get_active_subscription(tenant_id)
            
            if not subscription:
                return {
                    'valid': False,
                    'reason': "No active subscription found",
                    'checked_at': datetime.utcnow().isoformat(),
                    'recommendations': ["Set up a subscription first"]
                }
            
            # Get Stripe customer
            stripe_customer = stripe.Customer.retrieve(subscription['stripe_customer_id'])
            
            # Validate default payment method
            if not stripe_customer.default_source and not stripe_customer.invoice_settings.default_payment_method:
                return {
                    'valid': False,
                    'reason': "No default payment method",
                    'checked_at': datetime.utcnow().isoformat(),
                    'payment_methods_available': 0,
                    'recommendations': ["Add a payment method"]
                }
            
            # Get payment method
            payment_method_id = (
                stripe_customer.invoice_settings.default_payment_method or 
                stripe_customer.default_source
            )
            
            payment_method_valid = await self._validate_payment_method(payment_method_id)
            
            # For manual charges, we can't pre-validate the exact amount
            # but we can check if the payment method is valid
            result = {
                'valid': payment_method_valid['valid'],
                'reason': payment_method_valid.get('reason'),
                'checked_at': datetime.utcnow().isoformat(),
                'payment_methods_available': 1 if payment_method_valid['valid'] else 0,
                'default_payment_method_valid': payment_method_valid['valid'],
                'details': {
                    'amount_requested': amount,
                    'currency': currency,
                    'validation_type': 'manual_charge'
                }
            }
            
            if not payment_method_valid['valid']:
                result['recommendations'] = [
                    "Update your payment method",
                    "Ensure your card is valid and not expired",
                    "Contact your bank if issues persist"
                ]
            
            return result
            
        except stripe.error.StripeError as e:
            lambda_logger.error("Stripe error in manual charge validation", extra={
                'error': str(e),
                'tenant_id': tenant_id,
                'amount': amount
            })
            raise PaymentException(f"Payment validation failed: {str(e)}")
    
    async def validate_subscription_upgrade(self, tenant_id: str, subscription_id: str, 
                                          amount: int, currency: str) -> Dict[str, Any]:
        """
        Validate subscription upgrade.
        
        Args:
            tenant_id: Tenant ID
            subscription_id: Subscription ID
            amount: Upgrade amount in cents
            currency: Currency code
            
        Returns:
            Validation result dictionary
        """
        try:
            # Get subscription details
            subscription = await self.subscription_service.get_subscription(subscription_id)
            
            if not subscription or subscription['tenant_id'] != tenant_id:
                raise ValidationException("Subscription not found")
            
            # Get Stripe subscription
            stripe_subscription = stripe.Subscription.retrieve(subscription['stripe_subscription_id'])
            
            # Check if subscription can be upgraded
            if stripe_subscription.status != 'active':
                return {
                    'valid': False,
                    'reason': f"Cannot upgrade subscription with status {stripe_subscription.status}",
                    'checked_at': datetime.utcnow().isoformat()
                }
            
            # Validate payment method for proration amount
            payment_method_valid = await self._validate_payment_method(
                stripe_subscription.default_payment_method
            )
            
            result = {
                'valid': payment_method_valid['valid'],
                'reason': payment_method_valid.get('reason'),
                'checked_at': datetime.utcnow().isoformat(),
                'subscription_info': {
                    'status': stripe_subscription.status,
                    'subscription_id': subscription_id,
                    'current_plan': subscription.get('plan_id')
                },
                'payment_methods_available': 1 if payment_method_valid['valid'] else 0,
                'default_payment_method_valid': payment_method_valid['valid'],
                'details': {
                    'upgrade_amount': amount,
                    'currency': currency,
                    'validation_type': 'upgrade'
                }
            }
            
            if not payment_method_valid['valid']:
                result['recommendations'] = [
                    "Update your payment method before upgrading",
                    "Ensure sufficient funds for the upgrade charge"
                ]
            
            return result
            
        except stripe.error.StripeError as e:
            lambda_logger.error("Stripe error in upgrade validation", extra={
                'error': str(e),
                'subscription_id': subscription_id
            })
            raise PaymentException(f"Upgrade validation failed: {str(e)}")
    
    async def validate_subscription_renewal(self, tenant_id: str, subscription_id: str) -> Dict[str, Any]:
        """
        Validate subscription renewal.
        
        Args:
            tenant_id: Tenant ID
            subscription_id: Subscription ID
            
        Returns:
            Validation result dictionary
        """
        # Renewal validation is similar to upcoming payment validation
        return await self.validate_upcoming_payment(tenant_id, subscription_id)
    
    async def validate_payment_methods(self, tenant_id: str) -> Dict[str, Any]:
        """
        Validate general payment methods for tenant.
        
        Args:
            tenant_id: Tenant ID
            
        Returns:
            Validation result dictionary
        """
        try:
            # Get active subscription
            subscription = await self.subscription_service.get_active_subscription(tenant_id)
            
            if not subscription:
                return {
                    'valid': False,
                    'reason': "No active subscription found",
                    'checked_at': datetime.utcnow().isoformat(),
                    'payment_methods_available': 0,
                    'recommendations': ["Set up a subscription first"]
                }
            
            # Get Stripe customer
            stripe_customer = stripe.Customer.retrieve(subscription['stripe_customer_id'])
            
            # Get all payment methods
            payment_methods = stripe.PaymentMethod.list(
                customer=stripe_customer.id,
                type='card'
            )
            
            valid_methods = 0
            default_valid = False
            
            for pm in payment_methods.data:
                pm_valid = await self._validate_payment_method(pm.id)
                if pm_valid['valid']:
                    valid_methods += 1
                    if pm.id == stripe_customer.invoice_settings.default_payment_method:
                        default_valid = True
            
            result = {
                'valid': valid_methods > 0,
                'reason': "No valid payment methods" if valid_methods == 0 else None,
                'checked_at': datetime.utcnow().isoformat(),
                'payment_methods_available': valid_methods,
                'default_payment_method_valid': default_valid,
                'details': {
                    'total_payment_methods': len(payment_methods.data),
                    'valid_payment_methods': valid_methods
                }
            }
            
            if valid_methods == 0:
                result['recommendations'] = [
                    "Add a valid payment method",
                    "Ensure your cards haven't expired",
                    "Contact support if you need assistance"
                ]
            elif not default_valid:
                result['recommendations'] = [
                    "Set a valid payment method as default"
                ]
            
            return result
            
        except stripe.error.StripeError as e:
            lambda_logger.error("Stripe error in payment methods validation", extra={
                'error': str(e),
                'tenant_id': tenant_id
            })
            raise PaymentException(f"Payment methods validation failed: {str(e)}")
    
    async def _validate_payment_method(self, payment_method_id: str) -> Dict[str, Any]:
        """
        Validate individual payment method.
        
        Args:
            payment_method_id: Payment method ID
            
        Returns:
            Validation result
        """
        try:
            if not payment_method_id:
                return {
                    'valid': False,
                    'reason': "No payment method provided"
                }
            
            # Retrieve payment method
            payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
            
            # Check if it's a card
            if payment_method.type == 'card':
                card = payment_method.card
                
                # Check expiration
                current_date = datetime.now()
                exp_year = card.exp_year
                exp_month = card.exp_month
                
                if (exp_year < current_date.year or 
                    (exp_year == current_date.year and exp_month < current_date.month)):
                    return {
                        'valid': False,
                        'reason': "Card has expired"
                    }
                
                # Check if card requires action
                if hasattr(card, 'three_d_secure_usage') and card.three_d_secure_usage.supported:
                    return {
                        'valid': True,
                        'requires_action': True,
                        'reason': "Card may require 3D Secure authentication"
                    }
            
            return {
                'valid': True,
                'reason': None
            }
            
        except stripe.error.StripeError as e:
            return {
                'valid': False,
                'reason': f"Payment method validation failed: {str(e)}"
            }
