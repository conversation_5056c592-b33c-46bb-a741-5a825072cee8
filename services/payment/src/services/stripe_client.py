# src/payment/services/stripe_client.py
# Implementado según Stripe SDK Documentation y mejores prácticas

"""
Stripe client service for centralized Stripe API management.
Implements secure configuration, error handling, and best practices.
"""

import stripe
from typing import Optional, Dict, Any, List
from decimal import Decimal
import os
from datetime import datetime

from shared.config import get_settings
from shared.logger import audit_log, lambda_logger
from shared.exceptions import (
    PaymentException,
    ValidationException,
    BusinessLogicException
)
from shared.resilience import resilient


class StripeClientService:
    """Centralized Stripe client service with secure configuration."""
    
    def __init__(self):
        """Initialize Stripe client with secure configuration."""
        self.settings = get_settings()
        self._configure_stripe()
        
    def _configure_stripe(self) -> None:
        """Configure Stripe with API keys and settings."""
        # Validate and set API key based on environment
        if self.settings.environment == "production":
            api_key = self.settings.stripe_live_secret_key
            if not api_key or not api_key.startswith("sk_live_"):
                raise ValidationException(
                    "Invalid or missing Stripe live secret key. Must start with 'sk_live_'"
                )
        else:
            api_key = self.settings.stripe_test_secret_key
            if not api_key or not api_key.startswith("sk_test_"):
                raise ValidationException(
                    "Invalid or missing Stripe test secret key. Must start with 'sk_test_'"
                )

        # Validate key length (Stripe keys are typically 107+ characters)
        if len(api_key) < 50:
            raise ValidationException("Stripe API key appears to be invalid (too short)")

        # Set API key securely
        stripe.api_key = api_key

        # Configure API version for consistency (latest stable version)
        stripe.api_version = "2025-06-30.basil"

        # Configure request options with security best practices
        stripe.max_network_retries = 3
        stripe.default_http_client = stripe.http_client.RequestsClient(
            timeout=30,
            session=None
        )

        # Log configuration without exposing sensitive data
        key_prefix = api_key[:12] + "..." if len(api_key) > 12 else "***"
        lambda_logger.info(f"Stripe configured for {self.settings.environment} environment with key: {key_prefix}")
    
    def _handle_stripe_error(self, error: stripe.error.StripeError) -> None:
        """Handle Stripe errors and convert to application exceptions."""
        error_message = str(error)
        error_code = getattr(error, 'code', 'unknown')
        
        lambda_logger.error(f"Stripe error: {error_code} - {error_message}")
        
        if isinstance(error, stripe.error.CardError):
            # Card was declined
            raise PaymentException(
                message=f"Payment failed: {error.user_message}",
                error_code="CARD_DECLINED"
            )
        elif isinstance(error, stripe.error.RateLimitError):
            # Too many requests
            raise PaymentException(
                message="Too many requests to payment processor",
                error_code="RATE_LIMIT_ERROR"
            )
        elif isinstance(error, stripe.error.InvalidRequestError):
            # Invalid parameters
            raise ValidationException(
                message=f"Invalid payment request: {error_message}"
            )
        elif isinstance(error, stripe.error.AuthenticationError):
            # Authentication failed
            raise PaymentException(
                message="Payment processor authentication failed",
                error_code="AUTHENTICATION_ERROR"
            )
        elif isinstance(error, stripe.error.APIConnectionError):
            # Network communication failed
            raise PaymentException(
                message="Payment processor connection failed",
                error_code="CONNECTION_ERROR"
            )
        elif isinstance(error, stripe.error.StripeError):
            # Generic Stripe error
            raise PaymentException(
                message=f"Payment processor error: {error_message}",
                error_code="STRIPE_ERROR"
            )
        else:
            # Unknown error
            raise PaymentException(
                message="Unknown payment processor error",
                error_code="UNKNOWN_ERROR"
            )
    
    @resilient("payment", timeout_seconds=30.0, max_attempts=2, max_concurrent=5)
    def create_customer(
        self,
        email: str,
        name: str,
        tenant_id: str,
        phone: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> stripe.Customer:
        """Create a new Stripe customer."""
        try:
            customer_metadata = {
                "tenant_id": tenant_id,
                "created_by": "platform_api",
                "created_at": datetime.utcnow().isoformat()
            }
            
            if metadata:
                customer_metadata.update(metadata)
            
            customer_data = {
                "email": email,
                "name": name,
                "metadata": customer_metadata
            }
            
            if phone:
                customer_data["phone"] = phone
            
            customer = stripe.Customer.create(**customer_data)
            
            lambda_logger.info(f"Created Stripe customer: {customer.id} for tenant: {tenant_id}")
            return customer
            
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def get_customer(self, customer_id: str) -> stripe.Customer:
        """Retrieve a Stripe customer by ID."""
        try:
            customer = stripe.Customer.retrieve(customer_id)
            return customer
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def update_customer(
        self,
        customer_id: str,
        **kwargs
    ) -> stripe.Customer:
        """Update a Stripe customer."""
        try:
            customer = stripe.Customer.modify(customer_id, **kwargs)
            lambda_logger.info(f"Updated Stripe customer: {customer_id}")
            return customer
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def delete_customer(self, customer_id: str) -> stripe.Customer:
        """Delete a Stripe customer."""
        try:
            customer = stripe.Customer.delete(customer_id)
            lambda_logger.info(f"Deleted Stripe customer: {customer_id}")
            return customer
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    @resilient("payment", timeout_seconds=60.0, max_attempts=2, max_concurrent=3)
    def create_subscription(
        self,
        customer_id: str,
        price_id: str,
        payment_method_id: Optional[str] = None,
        trial_period_days: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        require_payment_method: bool = True
    ) -> stripe.Subscription:
        """Create a new subscription with proper payment handling."""
        try:
            # Validate payment method requirement
            if require_payment_method and not payment_method_id and not trial_period_days:
                raise ValidationException(
                    "Payment method is required for subscriptions without trial period"
                )

            subscription_data = {
                "customer": customer_id,
                "items": [{"price": price_id}],
                "payment_settings": {
                    "save_default_payment_method": "on_subscription",
                    "payment_method_types": ["card"]  # Restrict to secure payment types
                },
                "expand": ["latest_invoice.payment_intent", "pending_setup_intent"]
            }

            # Configure payment behavior based on payment method availability
            if payment_method_id:
                subscription_data["default_payment_method"] = payment_method_id
                subscription_data["payment_behavior"] = "default_incomplete"
            elif trial_period_days:
                # Allow trial without payment method
                subscription_data["payment_behavior"] = "default_incomplete"
            else:
                # Require immediate payment setup
                subscription_data["payment_behavior"] = "error_if_incomplete"

            if trial_period_days:
                subscription_data["trial_period_days"] = trial_period_days

            if metadata:
                subscription_data["metadata"] = metadata

            subscription = stripe.Subscription.create(**subscription_data)

            lambda_logger.info(f"Created subscription: {subscription.id} for customer: {customer_id}")
            return subscription

        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def get_subscription(self, subscription_id: str) -> stripe.Subscription:
        """Retrieve a subscription by ID."""
        try:
            subscription = stripe.Subscription.retrieve(
                subscription_id,
                expand=["latest_invoice", "customer", "default_payment_method"]
            )
            return subscription
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def update_subscription(
        self,
        subscription_id: str,
        **kwargs
    ) -> stripe.Subscription:
        """Update a subscription."""
        try:
            subscription = stripe.Subscription.modify(subscription_id, **kwargs)
            lambda_logger.info(f"Updated subscription: {subscription_id}")
            return subscription
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    @resilient("payment", timeout_seconds=30.0, max_attempts=2, max_concurrent=5)
    def cancel_subscription(
        self,
        subscription_id: str,
        at_period_end: bool = True
    ) -> stripe.Subscription:
        """Cancel a subscription."""
        try:
            if at_period_end:
                subscription = stripe.Subscription.modify(
                    subscription_id,
                    cancel_at_period_end=True
                )
            else:
                subscription = stripe.Subscription.cancel(subscription_id)
            
            lambda_logger.info(f"Cancelled subscription: {subscription_id}")
            return subscription
            
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def create_price(
        self,
        product_id: str,
        unit_amount: int,
        currency: str = "usd",
        recurring_interval: str = "month",
        metadata: Optional[Dict[str, str]] = None
    ) -> stripe.Price:
        """Create a new price for a product."""
        try:
            price_data = {
                "product": product_id,
                "unit_amount": unit_amount,
                "currency": currency,
                "recurring": {"interval": recurring_interval}
            }
            
            if metadata:
                price_data["metadata"] = metadata
            
            price = stripe.Price.create(**price_data)
            
            lambda_logger.info(f"Created price: {price.id} for product: {product_id}")
            return price
            
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def create_product(
        self,
        name: str,
        description: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> stripe.Product:
        """Create a new product."""
        try:
            product_data = {
                "name": name,
                "type": "service"
            }
            
            if description:
                product_data["description"] = description
            
            if metadata:
                product_data["metadata"] = metadata
            
            product = stripe.Product.create(**product_data)
            
            lambda_logger.info(f"Created product: {product.id}")
            return product
            
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)
    
    def list_prices(
        self,
        product_id: Optional[str] = None,
        active: bool = True,
        limit: int = 100
    ) -> List[stripe.Price]:
        """List prices, optionally filtered by product."""
        try:
            params = {
                "active": active,
                "limit": limit
            }
            
            if product_id:
                params["product"] = product_id
            
            prices = stripe.Price.list(**params)
            return prices.data
            
        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)

    def create_setup_intent(
        self,
        customer_id: str,
        payment_method_types: List[str] = None,
        usage: str = "off_session",
        metadata: Optional[Dict[str, str]] = None
    ) -> stripe.SetupIntent:
        """Create a Setup Intent for future payments."""
        try:
            setup_intent_data = {
                "customer": customer_id,
                "usage": usage,
                "payment_method_types": payment_method_types or ["card"]
            }

            if metadata:
                setup_intent_data["metadata"] = metadata

            setup_intent = stripe.SetupIntent.create(**setup_intent_data)

            lambda_logger.info(f"Created setup intent: {setup_intent.id} for customer: {customer_id}")
            return setup_intent

        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)

    def confirm_setup_intent(
        self,
        setup_intent_id: str,
        payment_method: str
    ) -> stripe.SetupIntent:
        """Confirm a Setup Intent with a payment method."""
        try:
            setup_intent = stripe.SetupIntent.confirm(
                setup_intent_id,
                payment_method=payment_method
            )

            lambda_logger.info(f"Confirmed setup intent: {setup_intent_id}")
            return setup_intent

        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)

    def attach_payment_method(
        self,
        payment_method_id: str,
        customer_id: str
    ) -> stripe.PaymentMethod:
        """Attach a payment method to a customer."""
        try:
            payment_method = stripe.PaymentMethod.attach(
                payment_method_id,
                customer=customer_id
            )

            lambda_logger.info(f"Attached payment method: {payment_method_id} to customer: {customer_id}")
            return payment_method

        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)

    def list_payment_methods(
        self,
        customer_id: str,
        type: str = "card"
    ) -> List[stripe.PaymentMethod]:
        """List payment methods for a customer."""
        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id,
                type=type
            )

            return payment_methods.data

        except stripe.error.StripeError as e:
            self._handle_stripe_error(e)

    def construct_webhook_event(
        self,
        payload: str,
        signature: str,
        endpoint_secret: str,
        tolerance: int = 300
    ) -> stripe.Event:
        """Construct and verify webhook event with timestamp tolerance."""
        try:
            # Validate inputs
            if not payload:
                raise ValidationException("Webhook payload cannot be empty")

            if not signature:
                raise ValidationException("Webhook signature cannot be empty")

            if not endpoint_secret:
                raise ValidationException("Webhook endpoint secret cannot be empty")

            # Construct event with tolerance for timestamp validation
            event = stripe.Webhook.construct_event(
                payload, signature, endpoint_secret, tolerance
            )

            # Additional security validation
            if not event.get('id'):
                raise ValidationException("Webhook event missing required ID")

            if not event.get('type'):
                raise ValidationException("Webhook event missing required type")

            return event

        except ValueError as e:
            # Invalid payload
            lambda_logger.warning(f"Invalid webhook payload: {str(e)}")
            raise ValidationException(f"Invalid webhook payload: {str(e)}")
        except stripe.error.SignatureVerificationError as e:
            # Invalid signature - potential security issue
            lambda_logger.error(f"Webhook signature verification failed: {str(e)}")
            raise ValidationException(f"Invalid webhook signature: {str(e)}")


# Global instance - initialized lazily
_stripe_client_instance = None

def get_stripe_client() -> StripeClientService:
    """Get or create the global Stripe client instance."""
    global _stripe_client_instance
    if _stripe_client_instance is None:
        _stripe_client_instance = StripeClientService()
    return _stripe_client_instance

# For backward compatibility
stripe_client = get_stripe_client()
