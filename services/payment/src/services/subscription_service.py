# src/payment/services/subscription_service.py
# Implementado según Stripe Subscription Management Best Practices

"""
Subscription service for managing Stripe subscriptions.
Handles subscription lifecycle: create, update, cancel, change plans.
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
import time
import stripe

from .stripe_client import get_stripe_client
from ..models.subscription import Subscription
from ..models.plan import Plan
from ..models import SubscriptionStatus, BillingInterval

# Unified shared layer imports
from shared.logger import audit_log, lambda_logger
from shared.models import TenantInfo, create_tenant_info, UserRole, UserStatus
from shared.database import db_client, DynamoDBClient
from shared.service_communication import ServiceCommunicationManager
from shared.validators import UnifiedUserValidator
from shared.exceptions import (
    PaymentException,
    ValidationException,
    ResourceNotFoundException,
    BusinessLogicException
)


class ISubscriptionService(ABC):
    """Interface for subscription service operations."""

    @abstractmethod
    def list_available_plans(self, **kwargs) -> Dict[str, Any]:
        """List all available subscription plans with filtering and pagination."""
        pass

    @abstractmethod
    def get_plan_details(self, plan_id: str, **kwargs) -> Dict[str, Any]:
        """Get details of a specific plan by ID."""
        pass

    @abstractmethod
    def create_subscription(self, tenant_id: str, plan_id: str, **kwargs) -> Dict[str, Any]:
        """Create a new subscription."""
        pass

    @abstractmethod
    def get_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """Get subscription details."""
        pass

    @abstractmethod
    def cancel_subscription(self, subscription_id: str, **kwargs) -> Dict[str, Any]:
        """Cancel a subscription."""
        pass

    @abstractmethod
    def update_subscription(self, subscription_id: str, tenant_id: str, user_id: str, **kwargs) -> Dict[str, Any]:
        """Update a subscription."""
        pass

    @abstractmethod
    def pause_subscription(self, subscription_id: str, tenant_id: str, user_id: str, **kwargs) -> Dict[str, Any]:
        """Pause a subscription."""
        pass

    @abstractmethod
    def resume_subscription(self, subscription_id: str, tenant_id: str, user_id: str, **kwargs) -> Dict[str, Any]:
        """Resume a subscription."""
        pass

    @abstractmethod
    def get_billing_history(self, tenant_id: str, **kwargs) -> Dict[str, Any]:
        """Get billing history for a tenant."""
        pass

    @abstractmethod
    def get_invoice_download_url(self, tenant_id: str, invoice_id: str, **kwargs) -> Dict[str, Any]:
        """Get download URL for an invoice."""
        pass

    @abstractmethod
    def update_payment_method(self, tenant_id: str, payment_method_id: str, **kwargs) -> Dict[str, Any]:
        """Update a payment method."""
        pass

    @abstractmethod
    def process_stripe_webhook(self, event_type: str, event_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Process a Stripe webhook event."""
        pass

    @abstractmethod
    def apply_coupon(self, tenant_id: str, coupon_code: str, **kwargs) -> Dict[str, Any]:
        """Apply a coupon to a subscription."""
        pass

    @abstractmethod
    def process_subscription(self, tenant_id: str, plan_id: str, **kwargs) -> Dict[str, Any]:
        """Process a subscription for tenant registration."""
        pass

    @abstractmethod
    def get_customer_info(self, tenant_id: str, **kwargs) -> Dict[str, Any]:
        """Get customer information."""
        pass

    @abstractmethod
    def create_customer(self, tenant_id: str, **kwargs) -> Dict[str, Any]:
        """Create a customer."""
        pass

    @abstractmethod
    def update_customer(self, tenant_id: str, **kwargs) -> Dict[str, Any]:
        """Update a customer."""
        pass

    @abstractmethod
    def sync_customer_with_stripe(self, tenant_id: str, **kwargs) -> Dict[str, Any]:
        """Sync customer with Stripe."""
        pass


class SubscriptionService(ISubscriptionService):
    """Service for managing Stripe subscriptions."""
    
    def __init__(self):
        """Initialize subscription service with unified communication."""
        self.stripe_client = get_stripe_client()
        # Initialize service communication for cross-service operations
        self.db_client = DynamoDBClient()
        self.service_comm = ServiceCommunicationManager(self.db_client)

    def list_available_plans(
        self,
        billing_interval: str = None,
        active_only: bool = True,
        limit: int = 10,
        offset: int = 0,
        tenant_id: str = None,
        category: str = None
    ) -> Dict[str, Any]:
        """
        List all available subscription plans with filtering and pagination.
        Real implementation using database.

        Args:
            billing_interval: Filter by billing interval (MONTHLY/YEARLY)
            active_only: Only return active plans
            limit: Maximum number of plans to return
            offset: Number of plans to skip
            tenant_id: Tenant ID for context
            category: Filter by plan category

        Returns:
            Dict containing plans list and pagination info
        """
        try:
            lambda_logger.info("Listing available plans", extra={
                'billing_interval': billing_interval,
                'active_only': active_only,
                'limit': limit,
                'offset': offset,
                'tenant_id': tenant_id,
                'category': category
            })

            # Get all available plans from database
            all_plans = []
            try:
                # Query active plans from database
                if active_only:
                    items = db_client.query(
                        'PLAN_STATUS#ACTIVE',
                        sk_prefix='PLAN#',
                        tenant_id='global',
                        index_name='GSI2'
                    )
                else:
                    # Get all plans regardless of status
                    items = db_client.query(
                        'PLANS',
                        sk_prefix='PLAN#',
                        tenant_id='global',
                        index_name='GSI1'
                    )

                # Convert items to Plan objects
                for item in items:
                    try:
                        plan = Plan.from_dict(item)
                        all_plans.append(plan)
                    except Exception as e:
                        lambda_logger.warning(f"Failed to parse plan: {e}")
                        continue

            except Exception as e:
                lambda_logger.error(f"Failed to query plans from database: {e}")
                # Return empty result if database query fails
                return {
                    'plans': [],
                    'total_count': 0,
                    'has_more': False,
                    'pagination': {
                        'limit': limit,
                        'offset': offset,
                        'current_page': (offset // limit) + 1,
                        'total_pages': 0
                    }
                }

            # Apply filters
            filtered_plans = []
            for plan in all_plans:
                # Filter by active status (if not already filtered by query)
                if active_only and not plan.is_active():
                    continue

                # Filter by billing interval
                if billing_interval:
                    if billing_interval.upper() == 'MONTHLY' and plan.monthly_price <= 0:
                        continue
                    elif billing_interval.upper() == 'YEARLY' and (not plan.yearly_price or plan.yearly_price <= 0):
                        continue

                filtered_plans.append(plan)

            # Apply pagination
            total_plans = len(filtered_plans)
            paginated_plans = filtered_plans[offset:offset + limit]
            has_more = (offset + limit) < total_plans

            # Convert plans to dict format for response
            plan_dicts = []
            for plan in paginated_plans:
                plan_dict = plan.to_dict()

                # Add display pricing based on billing interval
                if billing_interval:
                    if billing_interval.upper() == 'MONTHLY':
                        plan_dict['display_price'] = float(plan.monthly_price)
                        plan_dict['display_interval'] = 'month'
                    elif billing_interval.upper() == 'YEARLY':
                        plan_dict['display_price'] = float(plan.yearly_price) if plan.yearly_price else 0
                        plan_dict['display_interval'] = 'year'
                else:
                    plan_dict['display_price'] = float(plan.monthly_price)
                    plan_dict['display_interval'] = 'month'

                # Add computed fields
                plan_dict['is_popular'] = plan.plan_type.value == 'PRO'  # Mark PRO as popular
                plan_dict['savings_percentage'] = self._calculate_yearly_savings(plan) if plan.yearly_price else 0

                plan_dicts.append(plan_dict)

            lambda_logger.info("Plans listed successfully", extra={
                'total_plans': total_plans,
                'returned_plans': len(plan_dicts),
                'has_more': has_more,
                'billing_interval': billing_interval
            })

            return {
                'plans': plan_dicts,
                'total_count': total_plans,
                'has_more': has_more,
                'pagination': {
                    'limit': limit,
                    'offset': offset,
                    'current_page': (offset // limit) + 1,
                    'total_pages': (total_plans + limit - 1) // limit
                },
                'filters_applied': {
                    'billing_interval': billing_interval,
                    'active_only': active_only,
                    'category': category
                }
            }

        except Exception as e:
            lambda_logger.error(f"Failed to list available plans: {str(e)}")
            raise PaymentException(f"Failed to retrieve plans: {str(e)}")

    def get_plan_details(self, plan_id: str) -> Dict[str, Any]:
        """
        Get details of a specific plan by ID.
        Real implementation using database.

        Args:
            plan_id: The ID of the plan to retrieve

        Returns:
            Dict containing plan details

        Raises:
            ResourceNotFoundException: If plan is not found
            PaymentException: If there's an error retrieving the plan
        """
        try:
            lambda_logger.info("Getting plan details", extra={
                'plan_id': plan_id
            })

            # Get plan from database
            plan = Plan.get_by_id(plan_id)
            if not plan:
                raise ResourceNotFoundException(f"Plan not found: {plan_id}")

            # Convert to dict format for response
            plan_dict = plan.to_dict()

            # Add computed fields
            plan_dict['is_popular'] = plan.plan_type.value == 'PRO'  # Mark PRO as popular
            plan_dict['savings_percentage'] = self._calculate_yearly_savings(plan) if plan.yearly_price else 0

            # Add display pricing for both intervals
            plan_dict['monthly_display'] = {
                'price': float(plan.monthly_price),
                'interval': 'month',
                'currency': plan.currency
            }

            if plan.yearly_price and plan.yearly_price > 0:
                plan_dict['yearly_display'] = {
                    'price': float(plan.yearly_price),
                    'interval': 'year',
                    'currency': plan.currency,
                    'savings_percentage': plan_dict['savings_percentage']
                }

            # Add feature details
            plan_dict['feature_details'] = {
                'included_features': plan.features,
                'limits': plan.limits,
                'trial_period_days': plan.trial_days
            }

            lambda_logger.info("Plan details retrieved successfully", extra={
                'plan_id': plan_id,
                'plan_name': plan.name,
                'plan_type': plan.plan_type.value
            })

            return plan_dict

        except ResourceNotFoundException:
            # Re-raise ResourceNotFoundException as-is
            raise
        except Exception as e:
            lambda_logger.error(f"Failed to get plan details: {str(e)}", extra={
                'plan_id': plan_id
            })
            raise PaymentException(f"Failed to retrieve plan details: {str(e)}")

    def _calculate_yearly_savings(self, plan: Plan) -> float:
        """Calculate yearly savings percentage."""
        if not plan.yearly_price or plan.monthly_price <= 0:
            return 0.0

        yearly_equivalent = plan.monthly_price * 12
        if yearly_equivalent <= plan.yearly_price:
            return 0.0

        savings = (yearly_equivalent - plan.yearly_price) / yearly_equivalent * 100
        return round(savings, 1)

    def _get_mock_plans(self) -> List[Dict[str, Any]]:
        """Get mock plans for testing."""
        return [
            {
                'plan_id': 'plan_free',
                'name': 'Free Plan',
                'plan_type': 'FREE',
                'category': 'basic',
                'monthly_price': 0.0,
                'yearly_price': 0.0,
                'currency': 'USD',
                'features': [
                    'Basic logistics tracking',
                    'Up to 1 user',
                    '1GB storage',
                    'Community support'
                ],
                'max_users': 1,
                'max_storage_gb': 1,
                'description': 'Perfect for getting started with basic logistics management',
                'is_popular': False,
                'is_active': True,
                'trial_days': 0
            },
            {
                'plan_id': 'plan_basic',
                'name': 'Basic Plan',
                'plan_type': 'BASIC',
                'category': 'basic',
                'monthly_price': 29.99,
                'yearly_price': 299.99,
                'currency': 'USD',
                'features': [
                    'Advanced logistics tracking',
                    'Up to 5 users',
                    '10GB storage',
                    'Email support',
                    'Basic analytics'
                ],
                'max_users': 5,
                'max_storage_gb': 10,
                'description': 'Great for small teams starting with logistics management',
                'is_popular': True,
                'is_active': True,
                'trial_days': 14
            },
            {
                'plan_id': 'plan_professional',
                'name': 'Professional Plan',
                'plan_type': 'PROFESSIONAL',
                'category': 'professional',
                'monthly_price': 99.99,
                'yearly_price': 999.99,
                'currency': 'USD',
                'features': [
                    'All basic features',
                    'Up to 25 users',
                    '100GB storage',
                    'Priority support',
                    'Advanced analytics',
                    'API access',
                    'Custom integrations'
                ],
                'max_users': 25,
                'max_storage_gb': 100,
                'description': 'Perfect for growing businesses with complex logistics needs',
                'is_popular': False,
                'is_active': True,
                'trial_days': 30
            },
            {
                'plan_id': 'plan_enterprise',
                'name': 'Enterprise Plan',
                'plan_type': 'ENTERPRISE',
                'category': 'enterprise',
                'monthly_price': 299.99,
                'yearly_price': 2999.99,
                'currency': 'USD',
                'features': [
                    'All professional features',
                    'Unlimited users',
                    'Unlimited storage',
                    '24/7 dedicated support',
                    'Advanced AI analytics',
                    'Custom development',
                    'SLA guarantees',
                    'White-label options'
                ],
                'max_users': -1,  # Unlimited
                'max_storage_gb': -1,  # Unlimited
                'description': 'Complete solution for large enterprises with complex logistics operations',
                'is_popular': False,
                'is_active': True,
                'trial_days': 30
            }
        ]
    
    def create_subscription(
        self,
        tenant_id: str,
        plan_id: str,
        billing_interval: BillingInterval,
        payment_method_id: Optional[str] = None,
        trial_days: Optional[int] = None,
        coupon_id: Optional[str] = None
    ) -> Subscription:
        """
        Create a new subscription for a tenant.
        
        Args:
            tenant_id: Tenant ID
            plan_id: Plan ID
            billing_interval: Billing interval (monthly/yearly)
            payment_method_id: Payment method ID
            trial_days: Trial period in days
            coupon_id: Coupon ID for discounts
            
        Returns:
            Subscription instance
            
        Raises:
            ValidationException: If input data is invalid
            PaymentException: If subscription creation fails
            ResourceNotFoundException: If tenant/plan not found
        """
        try:
            # Validate tenant exists (simplified check)
            if not tenant_id:
                raise ValidationException("Tenant ID is required")

            # Check for existing active subscription
            existing_subscription = self._get_active_subscription_by_tenant(tenant_id)
            if existing_subscription:
                raise BusinessLogicException(
                    f"Tenant already has an active subscription: {existing_subscription.subscription_id}"
                )
            
            # Validate plan
            plan = Plan.get_by_id(plan_id)
            if not plan:
                raise ResourceNotFoundException(f"Plan not found: {plan_id}")
            
            if not plan.is_active():
                raise ValidationException("Cannot subscribe to inactive plan")
            
            # Create subscription in database
            subscription = Subscription(
                tenant_id=tenant_id,
                plan_id=plan_id,
                status=SubscriptionStatus.TRIAL if trial_days > 0 else SubscriptionStatus.ACTIVE,
                billing_interval=billing_interval,
                amount=plan.get_price_for_interval(billing_interval),
                currency=plan.currency,
                trial_ends_at=int(time.time()) + (trial_days * 24 * 60 * 60) if trial_days > 0 else None,
                current_period_start=int(time.time()),
                current_period_end=int(time.time()) + (30 * 24 * 60 * 60),  # 30 days
                next_billing_date=int(time.time()) + (trial_days * 24 * 60 * 60) if trial_days > 0 else int(time.time()) + (30 * 24 * 60 * 60)
            )

            # Save subscription to database
            subscription.save()

            # Log subscription creation
            audit_log(
                event_type="subscription_created",
                tenant_id=tenant_id,
                details={
                    "subscription_id": subscription.subscription_id,
                    "plan_id": plan_id,
                    "billing_interval": billing_interval.value,
                    "trial_days": trial_days,
                    "amount": float(subscription.amount),
                    "currency": subscription.currency
                }
            )
            
            lambda_logger.info(f"Created subscription {subscription.subscription_id} for tenant {tenant_id}")
            return subscription
            
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except stripe.error.StripeError as e:
            lambda_logger.error(f"Stripe error creating subscription: {str(e)}")
            raise PaymentException(f"Failed to create subscription: {str(e)}")
        except Exception as e:
            lambda_logger.error(f"Unexpected error creating subscription: {str(e)}")
            raise PaymentException(f"Failed to create subscription: {str(e)}")

    def _get_active_subscription_by_tenant(self, tenant_id: str) -> Optional[Subscription]:
        """Get active subscription for tenant."""
        try:
            items = db_client.query(
                f'TENANT#{tenant_id}',
                sk_prefix='SUBSCRIPTION#',
                tenant_id=tenant_id
            )

            # Filter for active subscriptions
            for item in items:
                if item.get('status') in [SubscriptionStatus.ACTIVE.value, SubscriptionStatus.TRIAL.value]:
                    return Subscription.from_dict(item)

            return None

        except Exception as e:
            lambda_logger.error(f"Failed to get active subscription for tenant {tenant_id}: {e}")
            return None

    def get_subscription_by_id(self, subscription_id: str) -> Optional[Subscription]:
        """Get subscription by ID."""
        try:
            return Subscription.get_by_id(subscription_id)
        except Exception as e:
            lambda_logger.error(f"Error retrieving subscription {subscription_id}: {str(e)}")
            return None
    
    def get_active_subscription_by_tenant(self, tenant_id: str) -> Optional[Subscription]:
        """Get active subscription for a tenant."""
        try:
            return Subscription.get_active_by_tenant_id(tenant_id)
        except Exception as e:
            lambda_logger.error(f"Error retrieving active subscription for tenant {tenant_id}: {str(e)}")
            return None
    
    def cancel_subscription(
        self,
        subscription_id: str,
        at_period_end: bool = True,
        cancellation_reason: Optional[str] = None
    ) -> Subscription:
        """
        Cancel a subscription.
        
        Args:
            subscription_id: Subscription ID
            at_period_end: Whether to cancel at period end
            cancellation_reason: Reason for cancellation
            
        Returns:
            Cancelled subscription instance
            
        Raises:
            ResourceNotFoundException: If subscription not found
            PaymentException: If cancellation fails
        """
        try:
            # Get existing subscription
            subscription = self.get_subscription_by_id(subscription_id)
            if not subscription:
                raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")
            
            # Cancel in Stripe
            stripe_subscription = self.stripe_client.cancel_subscription(
                subscription.stripe_subscription_id,
                at_period_end=at_period_end
            )
            
            # Update local record
            subscription.status = self._map_stripe_status(stripe_subscription.status)
            subscription.cancel_at_period_end = stripe_subscription.cancel_at_period_end
            subscription.canceled_at = datetime.fromtimestamp(stripe_subscription.canceled_at) if stripe_subscription.canceled_at else None
            subscription.stripe_data = self._serialize_stripe_subscription(stripe_subscription)
            subscription.updated_at = datetime.utcnow()
            
            if cancellation_reason:
                subscription.stripe_data["cancellation_reason"] = cancellation_reason
            
            subscription.save()
            
            lambda_logger.info(f"Cancelled subscription {subscription_id}")
            return subscription
            
        except ResourceNotFoundException:
            raise
        except stripe.error.StripeError as e:
            lambda_logger.error(f"Stripe error cancelling subscription: {str(e)}")
            raise PaymentException(f"Failed to cancel subscription: {str(e)}")
        except Exception as e:
            lambda_logger.error(f"Unexpected error cancelling subscription: {str(e)}")
            raise PaymentException(f"Failed to cancel subscription: {str(e)}")
    
    def update_subscription(
        self,
        subscription_id: str,
        tenant_id: str,
        user_id: str,
        plan_id: Optional[str] = None,
        billing_interval: Optional[BillingInterval] = None,
        prorate: bool = True,
        effective_date: str = 'immediate'
    ) -> Dict[str, Any]:
        """
        Update subscription plan and/or billing interval.
        Mock implementation for testing.

        Args:
            subscription_id: Subscription ID to update
            tenant_id: Tenant ID for ownership verification
            user_id: User ID performing the update
            plan_id: New plan ID (optional)
            billing_interval: New billing interval (optional)
            prorate: Whether to prorate the change
            effective_date: When to apply changes ('immediate' or 'next_billing_cycle')

        Returns:
            Dict containing updated subscription info and changes
        """
        try:
            lambda_logger.info("Updating subscription", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'plan_id': plan_id,
                'billing_interval': billing_interval.value if billing_interval else None,
                'prorate': prorate,
                'effective_date': effective_date
            })

            # Get current subscription
            subscription = Subscription.get_by_id(subscription_id, tenant_id)
            if not subscription:
                raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")

            # Verify subscription belongs to tenant
            if subscription.tenant_id != tenant_id:
                raise ValidationException("Subscription does not belong to this tenant")

            # Track changes
            changes = {
                'previous_plan_id': subscription.plan_id,
                'previous_billing_interval': subscription.billing_interval.value,
                'proration_applied': prorate,
                'effective_date': effective_date,
                'changes_made': []
            }

            # Apply updates
            update_needed = False

            # Update plan if provided
            if plan_id and plan_id != subscription.plan_id:
                # Validate new plan
                new_plan = Plan.get_by_id(plan_id)
                if not new_plan:
                    raise ResourceNotFoundException(f"Plan not found: {plan_id}")

                if not new_plan.is_active():
                    raise ValidationException("Cannot update to inactive plan")

                # Update subscription plan
                subscription.plan_id = plan_id
                subscription.amount = new_plan.get_price_for_interval(subscription.billing_interval)
                changes['new_plan_id'] = plan_id
                changes['changes_made'].append('plan_id')
                update_needed = True

            # Update billing interval if provided
            if billing_interval and billing_interval != subscription.billing_interval:
                # Get plan to calculate new amount
                plan = Plan.get_by_id(subscription.plan_id)
                if plan:
                    new_amount = plan.get_price_for_interval(billing_interval)
                    subscription.billing_interval = billing_interval
                    subscription.amount = new_amount
                    changes['new_billing_interval'] = billing_interval.value
                    changes['changes_made'].append('billing_interval')
                    update_needed = True

            # Save changes if any were made
            if update_needed:
                subscription.save()

                # Log the update
                audit_log(
                    event_type="subscription_updated",
                    tenant_id=tenant_id,
                    details={
                        "subscription_id": subscription_id,
                        "changes": changes['changes_made'],
                        "updated_by": user_id
                    }
                )

            # Prepare billing information
            billing_info = {
                'next_billing_date': subscription.next_billing_date,
                'amount_due': float(subscription.amount),
                'currency': subscription.currency,
                'billing_interval': subscription.billing_interval.value
            }

            lambda_logger.info("Subscription updated successfully", extra={
                'subscription_id': subscription_id,
                'changes_made': changes['changes_made']
            })

            return {
                'subscription': subscription.to_dict(),
                'changes': changes,
                'billing_info': billing_info,
                'requires_payment_confirmation': False
            }

        except Exception as e:
            lambda_logger.error(f"Error updating subscription {subscription_id}: {str(e)}")
            raise PaymentException(f"Failed to update subscription: {str(e)}")

    def pause_subscription(
        self,
        subscription_id: str,
        tenant_id: str,
        user_id: str,
        pause_duration_days: int = 30,
        reason: str = 'user_requested',
        resume_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Pause a subscription.
        Mock implementation for testing.

        Args:
            subscription_id: Subscription ID to pause
            tenant_id: Tenant ID for ownership verification
            user_id: User ID performing the pause
            pause_duration_days: Number of days to pause (default: 30)
            reason: Reason for pausing
            resume_date: Specific resume date (optional)

        Returns:
            Dict containing paused subscription info and pause details
        """
        try:
            lambda_logger.info("Pausing subscription", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'pause_duration_days': pause_duration_days,
                'reason': reason,
                'resume_date': resume_date
            })

            # Get subscription from database
            subscription = Subscription.get_by_id(subscription_id, tenant_id)
            if not subscription:
                raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")

            # Verify subscription belongs to tenant
            if subscription.tenant_id != tenant_id:
                raise ValidationException("Subscription does not belong to this tenant")

            # Check if subscription can be paused
            if subscription.status == SubscriptionStatus.CANCELLED:
                raise BusinessLogicException("Cannot pause a cancelled subscription")

            if subscription.status == SubscriptionStatus.PAUSED:
                raise BusinessLogicException("Subscription is already paused")

            # Calculate resume date
            current_time = int(time.time())
            if resume_date:
                # Parse resume_date string to timestamp
                from datetime import datetime
                try:
                    resume_datetime = datetime.fromisoformat(resume_date.replace('Z', '+00:00'))
                    calculated_resume_timestamp = int(resume_datetime.timestamp())
                except ValueError:
                    raise ValidationException("Invalid resume_date format. Use ISO format.")
            else:
                calculated_resume_timestamp = current_time + (pause_duration_days * 24 * 60 * 60)

            # Update subscription status
            subscription.status = SubscriptionStatus.PAUSED
            subscription.paused_at = current_time

            # Store pause details in metadata
            pause_data = {
                'reason': reason,
                'paused_at': current_time,
                'resume_date': calculated_resume_timestamp,
                'pause_duration_days': pause_duration_days,
                'paused_by': user_id
            }

            # Update next billing date to resume date
            subscription.next_billing_date = calculated_resume_timestamp

            # Save subscription
            subscription.save()

            # Log pause action
            audit_log(
                event_type="subscription_paused",
                tenant_id=tenant_id,
                details={
                    "subscription_id": subscription_id,
                    "pause_reason": reason,
                    "pause_duration_days": pause_duration_days,
                    "resume_timestamp": calculated_resume_timestamp,
                    "paused_by": user_id
                }
            )

            # Prepare response data
            pause_details = {
                'paused_at': current_time,
                'resume_date': calculated_resume_timestamp,
                'pause_duration_days': pause_duration_days,
                'reason': reason,
                'automatic_resume': True
            }

            billing_impact = {
                'billing_paused': True,
                'next_billing_date': calculated_resume_timestamp,
                'prorated_amount': 0.00,  # No proration for pause
                'currency': subscription.currency
            }

            lambda_logger.info("Subscription paused successfully", extra={
                'subscription_id': subscription_id,
                'resume_timestamp': calculated_resume_timestamp
            })

            return {
                'subscription': subscription.to_dict(),
                'pause_details': pause_details,
                'billing_impact': billing_impact,
                'resume_date': calculated_resume_timestamp
            }

        except Exception as e:
            lambda_logger.error(f"Error pausing subscription {subscription_id}: {str(e)}")
            raise PaymentException(f"Failed to pause subscription: {str(e)}")

    def resume_subscription(
        self,
        subscription_id: str,
        tenant_id: str,
        user_id: str,
        immediate: bool = True,
        reason: str = 'user_requested'
    ) -> Dict[str, Any]:
        """
        Resume a paused subscription.
        Mock implementation for testing.

        Args:
            subscription_id: Subscription ID to resume
            tenant_id: Tenant ID for ownership verification
            user_id: User ID performing the resume
            immediate: Whether to resume immediately (default: True)
            reason: Reason for resuming

        Returns:
            Dict containing resumed subscription info and resume details
        """
        try:
            lambda_logger.info("Resuming subscription", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'user_id': user_id,
                'immediate': immediate,
                'reason': reason
            })

            # Get subscription from database
            subscription = Subscription.get_by_id(subscription_id, tenant_id)
            if not subscription:
                raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")

            # Verify subscription belongs to tenant
            if subscription.tenant_id != tenant_id:
                raise ValidationException("Subscription does not belong to this tenant")

            # Check if subscription can be resumed
            if subscription.status != SubscriptionStatus.PAUSED:
                raise BusinessLogicException("Can only resume paused subscriptions")

            # Calculate resume date and next billing
            current_time = int(time.time())

            if immediate:
                resume_timestamp = current_time
                # Calculate next billing based on billing interval
                if subscription.billing_interval == BillingInterval.MONTHLY:
                    next_billing_timestamp = current_time + (30 * 24 * 60 * 60)  # 30 days
                else:  # YEARLY
                    next_billing_timestamp = current_time + (365 * 24 * 60 * 60)  # 365 days
            else:
                # Resume at originally scheduled time (if available) or next billing cycle
                resume_timestamp = subscription.next_billing_date or (current_time + (30 * 24 * 60 * 60))
                next_billing_timestamp = resume_timestamp

            # Update subscription status
            subscription.status = SubscriptionStatus.ACTIVE
            subscription.paused_at = None
            subscription.next_billing_date = next_billing_timestamp

            # Store resume details in metadata
            resume_data = {
                'reason': reason,
                'resumed_at': resume_timestamp,
                'immediate': immediate,
                'resumed_by': user_id,
                'previous_status': 'PAUSED'
            }

            # Save subscription
            subscription.save()

            # Log resume action
            audit_log(
                event_type="subscription_resumed",
                tenant_id=tenant_id,
                details={
                    "subscription_id": subscription_id,
                    "resume_reason": reason,
                    "immediate": immediate,
                    "resume_timestamp": resume_timestamp,
                    "next_billing_timestamp": next_billing_timestamp,
                    "resumed_by": user_id
                }
            )

            # Prepare response data
            resume_details = {
                'resumed_at': resume_timestamp,
                'immediate': immediate,
                'reason': reason,
                'previous_status': 'PAUSED'
            }

            billing_info = {
                'billing_resumed': True,
                'next_billing_date': next_billing_timestamp,
                'amount_due': float(subscription.amount),
                'currency': subscription.currency,
                'prorated_amount': 0.00  # No proration for resume
            }

            lambda_logger.info("Subscription resumed successfully", extra={
                'subscription_id': subscription_id,
                'immediate': immediate,
                'next_billing_timestamp': next_billing_timestamp
            })

            return {
                'subscription': subscription.to_dict(),
                'resume_details': resume_details,
                'billing_info': billing_info,
                'immediate': immediate,
                'resume_date': resume_timestamp
            }

        except Exception as e:
            lambda_logger.error(f"Error resuming subscription {subscription_id}: {str(e)}")
            raise PaymentException(f"Failed to resume subscription: {str(e)}")

    def _get_price_id_for_plan(self, plan: Plan, billing_interval: BillingInterval) -> Optional[str]:
        """Get Stripe price ID for plan and billing interval."""
        if billing_interval == BillingInterval.MONTHLY:
            return plan.stripe_monthly_price_id
        elif billing_interval == BillingInterval.YEARLY:
            return plan.stripe_yearly_price_id
        return None
    
    def _map_stripe_status(self, stripe_status: str) -> SubscriptionStatus:
        """Map Stripe subscription status to local status."""
        status_mapping = {
            "active": SubscriptionStatus.ACTIVE,
            "trialing": SubscriptionStatus.TRIALING,
            "past_due": SubscriptionStatus.PAST_DUE,
            "canceled": SubscriptionStatus.CANCELLED,
            "unpaid": SubscriptionStatus.UNPAID,
            "incomplete": SubscriptionStatus.INCOMPLETE,
            "incomplete_expired": SubscriptionStatus.INCOMPLETE_EXPIRED
        }
        return status_mapping.get(stripe_status, SubscriptionStatus.INCOMPLETE)
    
    def _serialize_stripe_subscription(self, stripe_subscription: stripe.Subscription) -> Dict[str, Any]:
        """Serialize Stripe subscription object for storage."""
        return {
            "id": stripe_subscription.id,
            "status": stripe_subscription.status,
            "customer": stripe_subscription.customer,
            "current_period_start": stripe_subscription.current_period_start,
            "current_period_end": stripe_subscription.current_period_end,
            "trial_start": stripe_subscription.trial_start,
            "trial_end": stripe_subscription.trial_end,
            "cancel_at_period_end": stripe_subscription.cancel_at_period_end,
            "canceled_at": stripe_subscription.canceled_at,
            "created": stripe_subscription.created,
            "metadata": stripe_subscription.metadata
        }

    def create_subscription(
        self,
        tenant_id: str,
        user_id: str,
        plan_id: str,
        billing_interval: BillingInterval,
        payment_method_id: Optional[str] = None,
        coupon_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new subscription for a tenant (simplified version).

        Args:
            tenant_id: Tenant ID
            user_id: User ID creating the subscription
            plan_id: Plan ID
            billing_interval: Billing interval
            payment_method_id: Payment method ID (optional)
            coupon_code: Coupon code (optional)

        Returns:
            Dict containing subscription details
        """
        try:
            lambda_logger.info("Creating subscription", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'plan_id': plan_id,
                'billing_interval': billing_interval.value
            })

            # Get plan details from database
            plan = Plan.get_by_id(plan_id)
            if not plan:
                raise ResourceNotFoundException(f"Plan not found: {plan_id}")

            if not plan.is_active():
                raise BusinessLogicException(f"Plan {plan_id} is not active")

            # Determine trial period
            trial_days = plan.trial_days if plan.trial_days > 0 else 0

            # Create subscription instance
            subscription = Subscription(
                tenant_id=tenant_id,
                plan_id=plan_id,
                status=SubscriptionStatus.TRIAL if trial_days > 0 else SubscriptionStatus.ACTIVE,
                billing_interval=billing_interval,
                amount=plan.get_price_for_interval(billing_interval),
                currency=plan.currency,
                trial_ends_at=int(time.time()) + (trial_days * 24 * 60 * 60) if trial_days > 0 else None,
                current_period_start=int(time.time()),
                current_period_end=int(time.time()) + (30 * 24 * 60 * 60),  # 30 days
                next_billing_date=int(time.time()) + (trial_days * 24 * 60 * 60) if trial_days > 0 else int(time.time()) + (30 * 24 * 60 * 60)
            )

            # Save subscription to database
            subscription.save()

            # Log successful creation
            audit_log(
                event_type="subscription_created",
                tenant_id=tenant_id,
                details={
                    "subscription_id": subscription.subscription_id,
                    "plan_id": plan_id,
                    "billing_interval": billing_interval.value,
                    "trial_days": trial_days,
                    "created_by": user_id
                }
            )

            return {
                'subscription': subscription.to_dict(),
                'plan': plan.to_dict(),
                'stripe_client_secret': None  # Will be set when payment method is added
            }

        except Exception as e:
            lambda_logger.error("Error creating subscription", extra={
                'error': str(e),
                'tenant_id': tenant_id,
                'plan_id': plan_id
            })
            raise PaymentException(f"Failed to create subscription: {str(e)}")

    def get_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Get subscription details by ID.

        Args:
            subscription_id: Subscription ID

        Returns:
            Dict containing subscription details
        """
        try:
            lambda_logger.info("Getting subscription", extra={
                'subscription_id': subscription_id
            })

            # Get subscription from database
            # Extract tenant_id from subscription_id if needed
            tenant_id = None
            if "_" in subscription_id:
                parts = subscription_id.split("_")
                if len(parts) >= 2:
                    tenant_id = parts[1]

            # Try to find subscription by ID
            subscription = None
            if tenant_id:
                subscription = Subscription.get_by_id(subscription_id, tenant_id)

            if not subscription:
                # Try to find by subscription_id across all tenants (less efficient but works)
                raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")

            # Get plan details
            plan = Plan.get_by_id(subscription.plan_id)
            if not plan:
                lambda_logger.warning(f"Plan not found for subscription: {subscription.plan_id}")
                plan_data = {
                    'plan_id': subscription.plan_id,
                    'name': 'Unknown Plan',
                    'status': 'UNKNOWN'
                }
            else:
                plan_data = plan.to_dict()

            # Calculate billing details
            billing_details = {
                'next_billing_date': subscription.next_billing_date,
                'amount_due': float(subscription.amount),
                'currency': subscription.currency,
                'billing_interval': subscription.billing_interval.value
            }

            # Get usage stats (placeholder - would come from usage service)
            usage_stats = {
                'api_calls_used': 0,  # Would come from usage tracking
                'api_calls_limit': plan.get_limit('api_calls_per_month') if plan else 0,
                'storage_used_gb': 0,  # Would come from storage tracking
                'storage_limit_gb': plan.get_limit('storage_gb') if plan else 0
            }

            return {
                'subscription': subscription.to_dict(),
                'plan': plan_data,
                'billing_details': billing_details,
                'usage_stats': usage_stats,
                'tenant_id': subscription.tenant_id
            }

        except Exception as e:
            lambda_logger.error("Error getting subscription", extra={
                'error': str(e),
                'subscription_id': subscription_id
            })
            raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")

    def cancel_subscription(
        self,
        subscription_id: str,
        tenant_id: str,
        reason: str = None,
        at_period_end: bool = True,
        immediate: bool = False
    ) -> Dict[str, Any]:
        """
        Cancel a subscription.

        Args:
            subscription_id: Subscription ID
            tenant_id: Tenant ID (for verification)
            reason: Cancellation reason
            at_period_end: Whether to cancel at period end
            immediate: Whether to cancel immediately

        Returns:
            Dict containing cancellation details
        """
        try:
            lambda_logger.info("Cancelling subscription", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'at_period_end': at_period_end,
                'immediate': immediate,
                'reason': reason
            })

            # Get subscription from database
            subscription = Subscription.get_by_id(subscription_id, tenant_id)
            if not subscription:
                raise ResourceNotFoundException(f"Subscription not found: {subscription_id}")

            # Verify subscription belongs to tenant
            if subscription.tenant_id != tenant_id:
                raise ValidationException("Subscription does not belong to this tenant")

            # Check if subscription is already cancelled
            if subscription.status == SubscriptionStatus.CANCELLED:
                raise BusinessLogicException("Subscription is already cancelled")

            # Calculate effective cancellation date
            current_time = int(time.time())
            if immediate:
                effective_date = current_time
                new_status = SubscriptionStatus.CANCELLED
            elif at_period_end:
                effective_date = subscription.current_period_end or (current_time + (30 * 24 * 60 * 60))
                new_status = SubscriptionStatus.ACTIVE  # Remains active until period end
            else:
                effective_date = current_time
                new_status = SubscriptionStatus.CANCELLED

            # Calculate refund amount for immediate cancellation
            refund_amount = 0.0
            if immediate and subscription.current_period_start and subscription.current_period_end:
                # Calculate prorated refund based on remaining time
                total_period = subscription.current_period_end - subscription.current_period_start
                remaining_time = subscription.current_period_end - current_time

                if remaining_time > 0 and total_period > 0:
                    refund_percentage = remaining_time / total_period
                    refund_amount = float(subscription.amount) * refund_percentage

            # Update subscription
            subscription.status = new_status
            if immediate:
                subscription.cancelled_at = current_time

            # Store cancellation details in metadata
            cancellation_data = {
                'reason': reason,
                'cancelled_at': current_time if immediate else None,
                'cancel_at_period_end': at_period_end,
                'effective_cancellation_date': effective_date,
                'refund_amount': refund_amount
            }

            # Save subscription
            subscription.save()

            # Log cancellation
            audit_log(
                event_type="subscription_cancelled",
                tenant_id=tenant_id,
                details={
                    "subscription_id": subscription_id,
                    "cancellation_reason": reason,
                    "immediate": immediate,
                    "at_period_end": at_period_end,
                    "refund_amount": refund_amount,
                    "effective_date": effective_date
                }
            )

            lambda_logger.info("Subscription cancelled successfully", extra={
                'subscription_id': subscription_id,
                'immediate': immediate,
                'refund_amount': refund_amount
            })

            return {
                'subscription': subscription.to_dict(),
                'effective_date': effective_date,
                'refund_amount': refund_amount,
                'cancelled_immediately': immediate,
                'cancelled_at_period_end': at_period_end,
                'cancellation_details': cancellation_data
            }

        except Exception as e:
            lambda_logger.error("Error cancelling subscription", extra={
                'error': str(e),
                'subscription_id': subscription_id,
                'tenant_id': tenant_id
            })
            raise ResourceNotFoundException(f"Failed to cancel subscription: {subscription_id}")

    def get_billing_history(
        self,
        tenant_id: str,
        limit: int = 10,
        starting_after: str = None,
        status: str = None,
        date_from: str = None,
        date_to: str = None,
        invoice_type: str = None
    ) -> Dict[str, Any]:
        """
        Get billing history for a tenant.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            limit: Maximum number of invoices to return
            starting_after: Invoice ID to start after for pagination
            status: Filter by invoice status
            date_from: Filter invoices from this date
            date_to: Filter invoices to this date
            invoice_type: Filter by invoice type

        Returns:
            Dict containing invoices list and summary
        """
        try:
            lambda_logger.info("Getting billing history", extra={
                'tenant_id': tenant_id,
                'limit': limit,
                'starting_after': starting_after,
                'status': status,
                'date_from': date_from,
                'date_to': date_to,
                'invoice_type': invoice_type
            })

            # Import Invoice model
            from models import Invoice, InvoiceStatus, InvoiceType

            # Get invoices from database
            invoice_status = None
            if status:
                try:
                    invoice_status = InvoiceStatus(status.lower())
                except ValueError:
                    raise ValidationException(f"Invalid invoice status: {status}")

            # Query invoices
            invoices = Invoice.get_by_tenant(
                tenant_id=tenant_id,
                limit=limit * 2,  # Get more to allow for filtering
                status=invoice_status
            )

            # Apply additional filters
            filtered_invoices = []
            for invoice in invoices:
                # Filter by invoice type
                if invoice_type:
                    try:
                        filter_type = InvoiceType(invoice_type.lower())
                        if invoice.invoice_type != filter_type:
                            continue
                    except ValueError:
                        continue

                # Filter by date range
                if date_from:
                    try:
                        from datetime import datetime
                        from_timestamp = int(datetime.fromisoformat(date_from.replace('Z', '+00:00')).timestamp())
                        if invoice.created_at < from_timestamp:
                            continue
                    except ValueError:
                        pass  # Skip invalid date format

                if date_to:
                    try:
                        from datetime import datetime
                        to_timestamp = int(datetime.fromisoformat(date_to.replace('Z', '+00:00')).timestamp())
                        if invoice.created_at > to_timestamp:
                            continue
                    except ValueError:
                        pass  # Skip invalid date format

                filtered_invoices.append(invoice)

            # Apply pagination
            total_invoices = len(filtered_invoices)

            # Find starting position if starting_after is provided
            start_index = 0
            if starting_after:
                for i, invoice in enumerate(filtered_invoices):
                    if invoice.invoice_id == starting_after:
                        start_index = i + 1
                        break

            # Apply pagination
            paginated_invoices = filtered_invoices[start_index:start_index + limit]
            has_more = (start_index + limit) < total_invoices

            # Convert invoices to dict format for response
            invoice_dicts = [invoice.to_dict() for invoice in paginated_invoices]

            # Calculate summary
            summary = self._calculate_billing_summary(filtered_invoices)

            lambda_logger.info("Billing history retrieved successfully", extra={
                'tenant_id': tenant_id,
                'total_invoices': total_invoices,
                'returned_invoices': len(invoice_dicts),
                'has_more': has_more
            })

            return {
                'invoices': invoice_dicts,
                'total_count': total_invoices,
                'has_more': has_more,
                'summary': summary,
                'pagination': {
                    'limit': limit,
                    'starting_after': starting_after,
                    'has_more': has_more
                },
                'filters_applied': {
                    'status': status,
                    'date_from': date_from,
                    'date_to': date_to,
                    'invoice_type': invoice_type
                }
            }

        except Exception as e:
            lambda_logger.error(f"Failed to get billing history: {str(e)}")
            raise PaymentException(f"Failed to retrieve billing history: {str(e)}")

    def _calculate_billing_summary(self, invoices: List) -> Dict[str, Any]:
        """Calculate billing summary from invoices."""
        from decimal import Decimal

        total_amount = Decimal('0.00')
        total_paid = Decimal('0.00')
        total_outstanding = Decimal('0.00')

        status_counts = {
            'paid': 0,
            'open': 0,
            'draft': 0,
            'void': 0,
            'uncollectible': 0
        }

        for invoice in invoices:
            if hasattr(invoice, 'amount_due'):
                # Invoice object
                total_amount += invoice.amount_due
                total_paid += invoice.amount_paid
                total_outstanding += invoice.amount_remaining
                status_counts[invoice.status.value] = status_counts.get(invoice.status.value, 0) + 1
            else:
                # Dict format (fallback)
                total_amount += Decimal(str(invoice.get('amount_due', 0)))
                total_paid += Decimal(str(invoice.get('amount_paid', 0)))
                total_outstanding += Decimal(str(invoice.get('amount_remaining', 0)))
                status = invoice.get('status', 'unknown').lower()
                status_counts[status] = status_counts.get(status, 0) + 1

        return {
            'total_invoices': len(invoices),
            'total_amount': float(total_amount),
            'total_paid': float(total_paid),
            'total_outstanding': float(total_outstanding),
            'status_breakdown': status_counts,
            'currency': 'USD'  # Default currency
        }

    def _get_mock_invoices(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get mock invoices for testing."""
        from datetime import datetime, timedelta

        base_date = datetime.now()

        return [
            {
                'id': 'inv_001',
                'tenant_id': tenant_id,
                'number': 'INV-2025-001',
                'status': 'paid',
                'type': 'subscription',
                'amount_paid': 29.99,
                'amount_due': 0.00,
                'currency': 'USD',
                'created_at': (base_date - timedelta(days=30)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'due_date': (base_date - timedelta(days=25)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'paid_at': (base_date - timedelta(days=25)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'description': 'Professional Plan - Monthly',
                'subscription_id': 'sub_mock_123',
                'period_start': (base_date - timedelta(days=60)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'period_end': (base_date - timedelta(days=30)).strftime('%Y-%m-%dT%H:%M:%SZ')
            },
            {
                'id': 'inv_002',
                'tenant_id': tenant_id,
                'number': 'INV-2025-002',
                'status': 'paid',
                'type': 'subscription',
                'amount_paid': 29.99,
                'amount_due': 0.00,
                'currency': 'USD',
                'created_at': base_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'due_date': (base_date + timedelta(days=5)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'paid_at': base_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'description': 'Professional Plan - Monthly',
                'subscription_id': 'sub_mock_123',
                'period_start': (base_date - timedelta(days=30)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'period_end': base_date.strftime('%Y-%m-%dT%H:%M:%SZ')
            },
            {
                'id': 'inv_003',
                'tenant_id': tenant_id,
                'number': 'INV-2025-003',
                'status': 'open',
                'type': 'subscription',
                'amount_paid': 0.00,
                'amount_due': 29.99,
                'currency': 'USD',
                'created_at': (base_date + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'due_date': (base_date + timedelta(days=35)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                'paid_at': None,
                'description': 'Professional Plan - Monthly',
                'subscription_id': 'sub_mock_123',
                'period_start': base_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'period_end': (base_date + timedelta(days=30)).strftime('%Y-%m-%dT%H:%M:%SZ')
            }
        ]

    def _calculate_billing_summary(self, invoices: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate billing summary from invoices."""
        total_paid = sum(inv.get('amount_paid', 0) for inv in invoices)
        total_due = sum(inv.get('amount_due', 0) for inv in invoices)

        status_counts = {}
        for invoice in invoices:
            status = invoice.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1

        return {
            'total_invoices': len(invoices),
            'total_paid': total_paid,
            'total_due': total_due,
            'currency': 'USD',
            'status_breakdown': status_counts,
            'last_payment_date': max(
                (inv.get('paid_at') for inv in invoices if inv.get('paid_at')),
                default=None
            )
        }

    def get_invoice_download_url(
        self,
        tenant_id: str,
        invoice_id: str,
        format_type: str = 'pdf',
        include_attachments: bool = False
    ) -> Dict[str, Any]:
        """
        Get download URL for an invoice.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            invoice_id: Invoice ID
            format_type: Format type (pdf, html, json)
            include_attachments: Whether to include attachments

        Returns:
            Dict containing download URL and invoice details
        """
        try:
            lambda_logger.info("Mock: Getting invoice download URL", extra={
                'tenant_id': tenant_id,
                'invoice_id': invoice_id,
                'format_type': format_type,
                'include_attachments': include_attachments
            })

            # Mock: Verify invoice exists and belongs to tenant
            mock_invoices = self._get_mock_invoices(tenant_id)
            invoice = next((inv for inv in mock_invoices if inv['id'] == invoice_id), None)

            if not invoice:
                raise ResourceNotFoundException(f"Invoice not found: {invoice_id}")

            # Mock: Generate download URL (would be S3 presigned URL in real implementation)
            from datetime import datetime, timedelta
            expires_at = (datetime.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

            download_url = f"https://mock-storage.amazonaws.com/invoices/{tenant_id}/{invoice_id}.{format_type}?expires={expires_at}"

            # Mock: File size based on format
            file_sizes = {
                'pdf': 245760,  # ~240KB
                'html': 51200,  # ~50KB
                'json': 8192    # ~8KB
            }

            content_types = {
                'pdf': 'application/pdf',
                'html': 'text/html',
                'json': 'application/json'
            }

            # Mock: Invoice details
            invoice_details = {
                'invoice_number': invoice.get('number'),
                'amount': invoice.get('amount_paid', 0) + invoice.get('amount_due', 0),
                'currency': invoice.get('currency', 'USD'),
                'status': invoice.get('status'),
                'created_at': invoice.get('created_at'),
                'due_date': invoice.get('due_date'),
                'description': invoice.get('description')
            }

            lambda_logger.info("Mock: Invoice download URL generated successfully", extra={
                'tenant_id': tenant_id,
                'invoice_id': invoice_id,
                'format_type': format_type,
                'expires_at': expires_at
            })

            return {
                'download_url': download_url,
                'expires_at': expires_at,
                'file_size': file_sizes.get(format_type, 245760),
                'content_type': content_types.get(format_type, 'application/pdf'),
                'invoice_details': invoice_details,
                'format': format_type,
                'include_attachments': include_attachments,
                'attachments_count': 2 if include_attachments else 0
            }

        except Exception as e:
            lambda_logger.error(f"Failed to get invoice download URL: {str(e)}")
            raise PaymentException(f"Failed to generate download URL: {str(e)}")

    def update_payment_method(
        self,
        tenant_id: str,
        payment_method_id: str,
        set_as_default: bool = False,
        billing_address: Dict[str, Any] = None,
        metadata: Dict[str, Any] = None,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Update a payment method.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            payment_method_id: Payment method ID to update
            set_as_default: Whether to set as default payment method
            billing_address: New billing address (optional)
            metadata: Additional metadata (optional)
            user_id: User ID performing the update

        Returns:
            Dict containing updated payment method info
        """
        try:
            lambda_logger.info("Updating payment method", extra={
                'tenant_id': tenant_id,
                'payment_method_id': payment_method_id,
                'set_as_default': set_as_default,
                'has_billing_address': bool(billing_address),
                'has_metadata': bool(metadata),
                'user_id': user_id
            })

            # Get payment method from database
            from models import PaymentMethod

            payment_method = PaymentMethod.get_by_id(payment_method_id, tenant_id)
            if not payment_method:
                raise ResourceNotFoundException(f"Payment method not found: {payment_method_id}")

            # Verify payment method belongs to tenant
            if payment_method.tenant_id != tenant_id:
                raise ValidationException("Payment method does not belong to this tenant")

            # Track changes
            changes_made = []

            # Update billing address if provided
            if billing_address:
                payment_method.billing_address = billing_address
                changes_made.append('billing_address')

            # Update metadata if provided
            if metadata:
                if not payment_method.metadata:
                    payment_method.metadata = {}
                payment_method.metadata.update(metadata)
                changes_made.append('metadata')

            # Set as default if requested
            if set_as_default:
                # First, unset any existing default payment methods for this tenant
                existing_methods = PaymentMethod.get_by_tenant(tenant_id)
                for method in existing_methods:
                    if method.is_default and method.payment_method_id != payment_method_id:
                        method.is_default = False
                        method.save()

                payment_method.is_default = True
                changes_made.append('set_as_default')

            # Save payment method if changes were made
            if changes_made:
                payment_method.save()

                # Log the update
                audit_log(
                    event_type="payment_method_updated",
                    tenant_id=tenant_id,
                    details={
                        "payment_method_id": payment_method_id,
                        "changes": changes_made,
                        "updated_by": user_id
                    }
                )

            lambda_logger.info("Payment method updated successfully", extra={
                'payment_method_id': payment_method_id,
                'changes_made': changes_made
            })

            return {
                'payment_method': payment_method.to_dict(),
                'changes_applied': changes_made,
                'is_default': payment_method.is_default,
                'updated_at': payment_method.updated_at
            }

        except Exception as e:
            lambda_logger.error(f"Failed to update payment method: {str(e)}")
            raise PaymentException(f"Failed to update payment method: {str(e)}")

    def process_stripe_webhook(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        event_id: str,
        client_ip: str = None
    ) -> Dict[str, Any]:
        """
        Process a Stripe webhook event.
        Mock implementation for testing.

        Args:
            event_type: Type of Stripe event
            event_data: Event data from Stripe
            event_id: Unique event ID
            client_ip: Client IP address

        Returns:
            Dict containing processing result
        """
        try:
            lambda_logger.info("Mock: Processing Stripe webhook", extra={
                'event_type': event_type,
                'event_id': event_id,
                'object_id': event_data.get('id'),
                'client_ip': client_ip
            })

            actions_taken = []
            tenant_id = None
            subscription_id = None

            # Mock: Process different event types
            if event_type == 'customer.subscription.created':
                subscription_id = event_data.get('id', 'sub_mock_webhook')
                tenant_id = self._extract_tenant_from_metadata(event_data)
                actions_taken.append('subscription_created_recorded')

            elif event_type == 'customer.subscription.updated':
                subscription_id = event_data.get('id', 'sub_mock_webhook')
                tenant_id = self._extract_tenant_from_metadata(event_data)
                status = event_data.get('status')
                actions_taken.append(f'subscription_status_updated_to_{status}')

            elif event_type == 'customer.subscription.deleted':
                subscription_id = event_data.get('id', 'sub_mock_webhook')
                tenant_id = self._extract_tenant_from_metadata(event_data)
                actions_taken.append('subscription_deleted_recorded')

            elif event_type == 'invoice.payment_succeeded':
                invoice_id = event_data.get('id', 'in_mock_webhook')
                subscription_id = event_data.get('subscription')
                tenant_id = self._extract_tenant_from_metadata(event_data)
                actions_taken.extend(['payment_recorded', 'subscription_renewed'])

            elif event_type == 'invoice.payment_failed':
                invoice_id = event_data.get('id', 'in_mock_webhook')
                subscription_id = event_data.get('subscription')
                tenant_id = self._extract_tenant_from_metadata(event_data)
                actions_taken.extend(['payment_failure_recorded', 'retry_scheduled'])

            elif event_type == 'customer.subscription.trial_will_end':
                subscription_id = event_data.get('id', 'sub_mock_webhook')
                tenant_id = self._extract_tenant_from_metadata(event_data)
                actions_taken.append('trial_end_notification_sent')

            else:
                actions_taken.append('event_logged_only')

            # Mock: Simulate processing result
            result = {
                'event_processed': True,
                'event_type': event_type,
                'object_id': event_data.get('id'),
                'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

            lambda_logger.info("Mock: Stripe webhook processed successfully", extra={
                'event_type': event_type,
                'event_id': event_id,
                'actions_taken': actions_taken,
                'tenant_id': tenant_id
            })

            return {
                'result': result,
                'actions_taken': actions_taken,
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'event_id': event_id
            }

        except Exception as e:
            lambda_logger.error(f"Failed to process Stripe webhook: {str(e)}")
            raise PaymentException(f"Failed to process webhook: {str(e)}")

    def _extract_tenant_from_metadata(self, event_data: Dict[str, Any]) -> str:
        """Extract tenant ID from Stripe object metadata."""
        metadata = event_data.get('metadata', {})
        return metadata.get('tenant_id', 'tenant_mock_123')

    def apply_coupon(
        self,
        tenant_id: str,
        coupon_code: str,
        subscription_id: str = None,
        apply_to: str = 'subscription',
        validate_only: bool = False,
        user_id: str = None,
        customer_email: str = None,
        promo_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Apply a coupon to a subscription.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            coupon_code: Coupon code to apply
            subscription_id: Subscription ID (optional)
            apply_to: Where to apply the coupon
            validate_only: Only validate, don't apply
            user_id: User ID applying the coupon
            customer_email: Customer email (for guest coupons)
            promo_context: Additional promo context

        Returns:
            Dict containing coupon application result
        """
        try:
            lambda_logger.info("Mock: Applying coupon", extra={
                'tenant_id': tenant_id,
                'coupon_code': coupon_code,
                'subscription_id': subscription_id,
                'apply_to': apply_to,
                'validate_only': validate_only,
                'user_id': user_id
            })

            # Mock: Validate coupon exists and is active
            mock_coupons = {
                'SAVE20': {
                    'code': 'SAVE20',
                    'name': '20% Off Discount',
                    'description': 'Save 20% on your subscription',
                    'discount_type': 'percentage',
                    'discount_value': 20,
                    'currency': 'USD',
                    'valid': True,
                    'max_uses': 1000,
                    'used_count': 45,
                    'expires_at': '2025-12-31T23:59:59Z'
                },
                'WELCOME50': {
                    'code': 'WELCOME50',
                    'name': '$50 Welcome Credit',
                    'description': 'Welcome bonus for new customers',
                    'discount_type': 'fixed_amount',
                    'discount_value': 5000,  # in cents
                    'currency': 'USD',
                    'valid': True,
                    'max_uses': 500,
                    'used_count': 123,
                    'expires_at': '2025-06-30T23:59:59Z'
                },
                'EXPIRED10': {
                    'code': 'EXPIRED10',
                    'name': 'Expired 10% Off',
                    'description': 'This coupon has expired',
                    'discount_type': 'percentage',
                    'discount_value': 10,
                    'currency': 'USD',
                    'valid': False,
                    'max_uses': 100,
                    'used_count': 100,
                    'expires_at': '2024-12-31T23:59:59Z'
                }
            }

            coupon_details = mock_coupons.get(coupon_code)
            if not coupon_details:
                raise ResourceNotFoundException(f"Coupon '{coupon_code}' not found")

            # Mock: Check coupon eligibility
            eligibility = {
                'eligible': True,
                'reasons': []
            }

            if not coupon_details['valid']:
                eligibility['eligible'] = False
                eligibility['reasons'].append('Coupon has expired or is no longer valid')

            if coupon_details['used_count'] >= coupon_details['max_uses']:
                eligibility['eligible'] = False
                eligibility['reasons'].append('Coupon usage limit exceeded')

            # Mock: Get subscription info
            subscription_info = {
                'subscription_id': subscription_id or f'sub_{tenant_id}',
                'current_plan': 'pro_monthly',
                'current_amount': 2999,  # $29.99 in cents
                'currency': 'USD',
                'billing_interval': 'month'
            }

            # Mock: Calculate discount
            original_amount = subscription_info['current_amount']
            discount_amount = 0

            if eligibility['eligible']:
                if coupon_details['discount_type'] == 'percentage':
                    discount_amount = int(original_amount * (coupon_details['discount_value'] / 100))
                elif coupon_details['discount_type'] == 'fixed_amount':
                    discount_amount = min(coupon_details['discount_value'], original_amount)

            final_amount = max(0, original_amount - discount_amount)
            savings_percentage = (discount_amount / original_amount * 100) if original_amount > 0 else 0

            discount_details = {
                'original_amount': original_amount,
                'discount_amount': discount_amount,
                'final_amount': final_amount,
                'savings_percentage': round(savings_percentage, 2),
                'currency': subscription_info['currency']
            }

            # Mock: Application result
            application_result = {}
            if not validate_only and eligibility['eligible']:
                application_result = {
                    'applied': True,
                    'applied_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                    'applied_to': apply_to,
                    'valid_until': '2025-12-31T23:59:59Z',
                    'application_id': f'app_{coupon_code}_{tenant_id}'
                }

            # Mock: Savings summary
            savings_summary = {
                'immediate_savings': discount_amount if apply_to == 'current_invoice' else 0,
                'next_billing_savings': discount_amount if apply_to in ['subscription', 'next_invoice'] else 0,
                'total_potential_savings': discount_amount * 12 if apply_to == 'subscription' else discount_amount
            }

            lambda_logger.info("Mock: Coupon processing completed", extra={
                'tenant_id': tenant_id,
                'coupon_code': coupon_code,
                'eligible': eligibility['eligible'],
                'discount_amount': discount_amount,
                'validate_only': validate_only
            })

            return {
                'coupon_details': coupon_details,
                'eligibility': eligibility,
                'discount_details': discount_details,
                'subscription_info': subscription_info,
                'application_result': application_result,
                'next_billing_amount': final_amount,
                'savings_summary': savings_summary,
                'applied_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

        except Exception as e:
            lambda_logger.error(f"Failed to apply coupon: {str(e)}")
            raise PaymentException(f"Failed to apply coupon: {str(e)}")

    def process_subscription(
        self,
        tenant_id: str,
        plan_id: str,
        payment_method: Dict[str, Any],
        billing_interval: str = 'monthly',
        trial_days: int = 0,
        coupon_code: str = None,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Process a subscription for tenant registration.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            plan_id: Plan ID to subscribe to
            payment_method: Payment method details
            billing_interval: Billing interval
            trial_days: Trial period in days
            coupon_code: Optional coupon code
            user_id: User ID processing the subscription

        Returns:
            Dict containing subscription processing result
        """
        try:
            lambda_logger.info("Mock: Processing subscription", extra={
                'tenant_id': tenant_id,
                'plan_id': plan_id,
                'billing_interval': billing_interval,
                'trial_days': trial_days,
                'has_coupon': bool(coupon_code),
                'user_id': user_id
            })

            # Mock: Plan details
            plan_details = {
                'free': {
                    'name': 'Free Plan',
                    'price': 0,
                    'currency': 'USD',
                    'features': ['Basic agents', '1 user', 'Limited data'],
                    'billing_interval': billing_interval
                },
                'pro': {
                    'name': 'Pro Plan',
                    'price': 2999,  # $29.99 in cents
                    'currency': 'USD',
                    'features': ['All agents', '5 users', 'Advanced analytics'],
                    'billing_interval': billing_interval
                },
                'enterprise': {
                    'name': 'Enterprise Plan',
                    'price': 29900,  # $299.00 in cents
                    'currency': 'USD',
                    'features': ['All agents', 'Unlimited users', 'Custom integrations'],
                    'billing_interval': billing_interval
                }
            }

            current_plan = plan_details.get(plan_id, plan_details['free'])

            # Mock: Generate subscription ID
            subscription_id = f'sub_{tenant_id}_{plan_id}_{int(datetime.now().timestamp())}'

            # Mock: Process payment (only for paid plans)
            payment_result = {
                'status': 'succeeded',
                'payment_method_id': f'pm_{tenant_id}_mock',
                'amount_paid': current_plan['price'],
                'currency': current_plan['currency'],
                'transaction_id': f'txn_{subscription_id}',
                'processed_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

            if current_plan['price'] > 0:
                # Mock: Validate payment method
                if payment_method.get('type') == 'card':
                    card_number = payment_method.get('card_number', '').replace(' ', '').replace('-', '')
                    if card_number.startswith('****************'):
                        # Mock: Declined card
                        payment_result['status'] = 'failed'
                        payment_result['failure_reason'] = 'card_declined'
                        raise PaymentException("Payment failed: Card was declined")

            # Mock: Apply coupon if provided
            coupon_applied = {}
            final_amount = current_plan['price']

            if coupon_code and current_plan['price'] > 0:
                mock_coupons = {
                    'SAVE20': {'discount_type': 'percentage', 'discount_value': 20},
                    'WELCOME50': {'discount_type': 'fixed_amount', 'discount_value': 5000}
                }

                coupon = mock_coupons.get(coupon_code)
                if coupon:
                    if coupon['discount_type'] == 'percentage':
                        discount_amount = int(current_plan['price'] * (coupon['discount_value'] / 100))
                    else:
                        discount_amount = min(coupon['discount_value'], current_plan['price'])

                    final_amount = max(0, current_plan['price'] - discount_amount)
                    coupon_applied = {
                        'code': coupon_code,
                        'discount_amount': discount_amount,
                        'final_amount': final_amount
                    }

            # Mock: Calculate trial and billing dates
            from datetime import timedelta
            now = datetime.now()
            trial_end = now + timedelta(days=trial_days) if trial_days > 0 else now

            if billing_interval == 'yearly':
                next_billing = trial_end + timedelta(days=365)
            elif billing_interval == 'quarterly':
                next_billing = trial_end + timedelta(days=90)
            else:  # monthly
                next_billing = trial_end + timedelta(days=30)

            # Mock: Subscription details
            subscription = {
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'plan_id': plan_id,
                'plan_name': current_plan['name'],
                'status': 'trialing' if trial_days > 0 else 'active',
                'amount': final_amount,
                'currency': current_plan['currency'],
                'billing_interval': billing_interval,
                'trial_end': trial_end.strftime('%Y-%m-%dT%H:%M:%SZ') if trial_days > 0 else None,
                'current_period_start': now.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'current_period_end': next_billing.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'next_billing_date': next_billing.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'created_at': now.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'created_by': user_id
            }

            # Mock: Billing info
            billing_info = {
                'billing_interval': billing_interval,
                'amount': final_amount,
                'currency': current_plan['currency'],
                'next_billing_date': next_billing.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'payment_method_id': payment_result.get('payment_method_id')
            }

            # Mock: Trial info
            trial_info = {}
            if trial_days > 0:
                trial_info = {
                    'trial_days': trial_days,
                    'trial_start': now.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    'trial_end': trial_end.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    'days_remaining': trial_days
                }

            # Mock: Next steps
            next_steps = [
                'Subscription activated successfully',
                'Access to all plan features enabled'
            ]

            if trial_days > 0:
                next_steps.append(f'Trial period of {trial_days} days started')

            if coupon_applied:
                next_steps.append(f'Coupon {coupon_code} applied successfully')

            lambda_logger.info("Mock: Subscription processed successfully", extra={
                'tenant_id': tenant_id,
                'subscription_id': subscription_id,
                'plan_id': plan_id,
                'final_amount': final_amount,
                'trial_days': trial_days
            })

            return {
                'subscription': subscription,
                'payment_result': payment_result,
                'plan_details': current_plan,
                'billing_info': billing_info,
                'trial_info': trial_info,
                'coupon_applied': coupon_applied,
                'next_steps': next_steps
            }

        except Exception as e:
            lambda_logger.error(f"Failed to process subscription: {str(e)}")
            raise PaymentException(f"Failed to process subscription: {str(e)}")

    def get_customer_info(
        self,
        tenant_id: str,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Get customer information.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            user_id: User ID requesting the info

        Returns:
            Dict containing customer information
        """
        try:
            lambda_logger.info("Mock: Getting customer info", extra={
                'tenant_id': tenant_id,
                'user_id': user_id
            })

            # Mock: Customer data
            customer = {
                'customer_id': f'cus_{tenant_id}',
                'tenant_id': tenant_id,
                'stripe_customer_id': f'cus_stripe_{tenant_id}',
                'billing_email': f'billing@{tenant_id}.com',
                'company_name': f'{tenant_id.title()} Company',
                'phone': '+*********0',
                'address': {
                    'line1': '123 Business St',
                    'city': 'New York',
                    'state': 'NY',
                    'postal_code': '10001',
                    'country': 'US'
                },
                'tax_id': '*********',
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

            # Mock: Billing info
            billing_info = {
                'default_payment_method': 'pm_default_123',
                'billing_cycle': 'monthly',
                'next_billing_date': '2025-02-01T00:00:00Z',
                'currency': 'USD',
                'tax_rate': 0.08
            }

            # Mock: Payment methods
            payment_methods = [
                {
                    'id': 'pm_default_123',
                    'type': 'card',
                    'card': {
                        'brand': 'visa',
                        'last4': '4242',
                        'exp_month': 12,
                        'exp_year': 2025
                    },
                    'is_default': True
                },
                {
                    'id': 'pm_backup_456',
                    'type': 'card',
                    'card': {
                        'brand': 'mastercard',
                        'last4': '5555',
                        'exp_month': 6,
                        'exp_year': 2026
                    },
                    'is_default': False
                }
            ]

            # Mock: Subscription info
            subscription_info = {
                'subscription_id': f'sub_{tenant_id}',
                'plan_id': 'pro',
                'status': 'active',
                'current_period_start': '2025-01-01T00:00:00Z',
                'current_period_end': '2025-02-01T00:00:00Z'
            }

            # Mock: Usage stats
            usage_stats = {
                'api_calls_this_month': 15420,
                'storage_used_gb': 2.5,
                'users_active': 3,
                'last_activity': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

            lambda_logger.info("Mock: Customer info retrieved successfully", extra={
                'tenant_id': tenant_id,
                'customer_id': customer['customer_id']
            })

            return {
                'customer': customer,
                'billing_info': billing_info,
                'payment_methods': payment_methods,
                'subscription_info': subscription_info,
                'usage_stats': usage_stats
            }

        except Exception as e:
            lambda_logger.error(f"Failed to get customer info: {str(e)}")
            raise PaymentException(f"Failed to get customer info: {str(e)}")

    def create_customer(
        self,
        tenant_id: str,
        billing_email: str = None,
        company_name: str = None,
        phone: str = None,
        address: Dict[str, Any] = None,
        tax_id: str = None,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Create a customer.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            billing_email: Billing email
            company_name: Company name
            phone: Phone number
            address: Address information
            tax_id: Tax ID
            user_id: User ID creating the customer

        Returns:
            Dict containing customer creation result
        """
        try:
            lambda_logger.info("Mock: Creating customer", extra={
                'tenant_id': tenant_id,
                'billing_email': billing_email,
                'company_name': company_name,
                'user_id': user_id
            })

            # Mock: Generate customer ID
            customer_id = f'cus_{tenant_id}_{int(datetime.now().timestamp())}'
            stripe_customer_id = f'cus_stripe_{customer_id}'

            # Mock: Customer data
            customer = {
                'customer_id': customer_id,
                'tenant_id': tenant_id,
                'stripe_customer_id': stripe_customer_id,
                'billing_email': billing_email or f'billing@{tenant_id}.com',
                'company_name': company_name or f'{tenant_id.title()} Company',
                'phone': phone,
                'address': address or {
                    'line1': '123 Business St',
                    'city': 'New York',
                    'state': 'NY',
                    'postal_code': '10001',
                    'country': 'US'
                },
                'tax_id': tax_id,
                'created_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                'created_by': user_id
            }

            # Mock: Billing info
            billing_info = {
                'currency': 'USD',
                'tax_rate': 0.08,
                'billing_cycle': 'monthly',
                'payment_terms': 'net_30'
            }

            # Mock: Setup requirements
            setup_required = [
                'Add payment method',
                'Verify billing email',
                'Complete tax information'
            ]

            # Mock: Next steps
            next_steps = [
                'Customer created successfully in Stripe',
                'Add a payment method to enable billing',
                'Subscribe to a plan to activate services'
            ]

            lambda_logger.info("Mock: Customer created successfully", extra={
                'tenant_id': tenant_id,
                'customer_id': customer_id,
                'stripe_customer_id': stripe_customer_id
            })

            return {
                'customer': customer,
                'stripe_customer_id': stripe_customer_id,
                'billing_info': billing_info,
                'setup_required': setup_required,
                'next_steps': next_steps
            }

        except Exception as e:
            lambda_logger.error(f"Failed to create customer: {str(e)}")
            raise PaymentException(f"Failed to create customer: {str(e)}")

    def update_customer(
        self,
        tenant_id: str,
        billing_email: str = None,
        company_name: str = None,
        phone: str = None,
        address: Dict[str, Any] = None,
        tax_id: str = None,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Update a customer.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            billing_email: New billing email
            company_name: New company name
            phone: New phone number
            address: New address information
            tax_id: New tax ID
            user_id: User ID updating the customer

        Returns:
            Dict containing customer update result
        """
        try:
            lambda_logger.info("Mock: Updating customer", extra={
                'tenant_id': tenant_id,
                'billing_email': billing_email,
                'company_name': company_name,
                'user_id': user_id
            })

            # Mock: Track changes
            changes_applied = []

            # Mock: Current customer data
            customer = {
                'customer_id': f'cus_{tenant_id}',
                'tenant_id': tenant_id,
                'stripe_customer_id': f'cus_stripe_{tenant_id}',
                'billing_email': f'billing@{tenant_id}.com',
                'company_name': f'{tenant_id.title()} Company',
                'phone': '+*********0',
                'address': {
                    'line1': '123 Business St',
                    'city': 'New York',
                    'state': 'NY',
                    'postal_code': '10001',
                    'country': 'US'
                },
                'tax_id': '*********',
                'updated_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                'updated_by': user_id
            }

            # Mock: Apply updates
            if billing_email and billing_email != customer['billing_email']:
                customer['billing_email'] = billing_email
                changes_applied.append('billing_email')

            if company_name and company_name != customer['company_name']:
                customer['company_name'] = company_name
                changes_applied.append('company_name')

            if phone and phone != customer['phone']:
                customer['phone'] = phone
                changes_applied.append('phone')

            if address:
                customer['address'].update(address)
                changes_applied.append('address')

            if tax_id and tax_id != customer['tax_id']:
                customer['tax_id'] = tax_id
                changes_applied.append('tax_id')

            # Mock: Billing info
            billing_info = {
                'currency': 'USD',
                'tax_rate': 0.08,
                'billing_cycle': 'monthly',
                'last_updated': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

            # Mock: Sync status
            sync_status = {
                'stripe_synced': True,
                'last_sync': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                'sync_successful': True
            }

            lambda_logger.info("Mock: Customer updated successfully", extra={
                'tenant_id': tenant_id,
                'customer_id': customer['customer_id'],
                'changes_applied': changes_applied
            })

            return {
                'customer': customer,
                'changes_applied': changes_applied,
                'billing_info': billing_info,
                'sync_status': sync_status
            }

        except Exception as e:
            lambda_logger.error(f"Failed to update customer: {str(e)}")
            raise PaymentException(f"Failed to update customer: {str(e)}")

    def sync_customer_with_stripe(
        self,
        tenant_id: str,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Sync customer with Stripe.
        Mock implementation for testing.

        Args:
            tenant_id: Tenant ID
            user_id: User ID performing the sync

        Returns:
            Dict containing sync result
        """
        try:
            lambda_logger.info("Mock: Syncing customer with Stripe", extra={
                'tenant_id': tenant_id,
                'user_id': user_id
            })

            # Mock: Customer data after sync
            customer = {
                'customer_id': f'cus_{tenant_id}',
                'tenant_id': tenant_id,
                'stripe_customer_id': f'cus_stripe_{tenant_id}',
                'billing_email': f'billing@{tenant_id}.com',
                'company_name': f'{tenant_id.title()} Company',
                'phone': '+*********0',
                'address': {
                    'line1': '123 Business St',
                    'city': 'New York',
                    'state': 'NY',
                    'postal_code': '10001',
                    'country': 'US'
                },
                'tax_id': '*********',
                'last_synced': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }

            # Mock: Sync status
            sync_status = {
                'successful': True,
                'last_sync': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                'stripe_version': '2023-10-16',
                'sync_duration_ms': 245
            }

            # Mock: Changes detected during sync
            changes_detected = [
                'Updated billing email in Stripe',
                'Synchronized payment methods',
                'Updated tax information'
            ]

            # Mock: Stripe data
            stripe_data = {
                'id': f'cus_stripe_{tenant_id}',
                'object': 'customer',
                'balance': 0,
                'created': 1704067200,  # 2024-01-01
                'currency': 'usd',
                'default_source': None,
                'delinquent': False,
                'description': f'Customer for {tenant_id}',
                'discount': None,
                'email': f'billing@{tenant_id}.com',
                'invoice_prefix': tenant_id.upper()[:3],
                'livemode': False,
                'metadata': {
                    'tenant_id': tenant_id
                },
                'name': f'{tenant_id.title()} Company',
                'next_invoice_sequence': 1,
                'phone': '+*********0',
                'preferred_locales': ['en'],
                'shipping': None,
                'tax_exempt': 'none'
            }

            lambda_logger.info("Mock: Customer synced with Stripe successfully", extra={
                'tenant_id': tenant_id,
                'customer_id': customer['customer_id'],
                'changes_detected_count': len(changes_detected)
            })

            return {
                'customer': customer,
                'sync_status': sync_status,
                'changes_detected': changes_detected,
                'stripe_data': stripe_data,
                'last_sync': sync_status['last_sync']
            }

        except Exception as e:
            lambda_logger.error(f"Failed to sync customer with Stripe: {str(e)}")
            raise PaymentException(f"Failed to sync customer with Stripe: {str(e)}")


# Global instance
subscription_service = SubscriptionService()
