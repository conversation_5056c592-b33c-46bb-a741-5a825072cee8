# services/payment/src/common/imports.py
# Centralized imports for payment service

"""
Centralized imports for payment service.
Provides commonly used imports to reduce repetition and improve consistency.
"""

# Standard library imports
import json
import time
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union
from enum import Enum

# Shared layer imports - Core functionality
from shared.responses import APIResponse, handle_cors_preflight
from shared.auth import require_auth, get_auth_context
from shared.middleware.resilience_middleware import rate_limit, payment_resilience
from shared.metrics import measure_performance
from shared.logger import lambda_logger, audit_log, log_api_request, log_api_response

# Shared layer imports - Exceptions
from shared.exceptions import (
    PlatformException,
    ValidationException,
    PaymentException,
    ResourceNotFoundException,
    BusinessLogicException,
    ExternalServiceException
)

# Shared layer imports - Database and models
from shared.database import db_client
from shared.models import (
    SubscriptionInfo,
    SubscriptionStatus,
    BillingInterval,
    PlanInfo,
    PlanStatus,
    PlanType,
    CustomerInfo,
    CustomerStatus
)

# Payment service imports - Configuration
from ..config.dependencies import container
from ..config.rate_limits import get_endpoint_rate_limit, get_rate_limit_config
from ..config.settings import get_config, ENVIRONMENT, IS_PRODUCTION

# Payment service imports - Models
from ..models import (
    Subscription,
    Plan,
    Customer,
    PaymentMethod
)

# Payment service imports - Services
from ..services.subscription_service import ISubscriptionService

# Payment service imports - Validators
from ..validators.subscription_validators import (
    validate_create_subscription_request,
    validate_update_subscription_request,
    validate_cancel_subscription_request,
    validate_change_plan_request,
    validate_list_plans_request,
    validate_get_subscription_request,
    validate_pause_subscription_request,
    validate_resume_subscription_request,
    validate_update_payment_method_request,
    validate_download_invoice_request,
    validate_stripe_webhook_request
)


# Common decorators for handlers
def payment_handler(endpoint_name: str, rate_limit_override: Optional[int] = None):
    """
    Common decorator for payment handlers.
    
    Args:
        endpoint_name: Name of the endpoint for rate limiting
        rate_limit_override: Override default rate limit
    """
    def decorator(func):
        # Apply decorators in reverse order (bottom to top execution)
        func = measure_performance(f"payment_{endpoint_name}")(func)
        func = payment_resilience(endpoint_name)(func)
        
        # Apply rate limiting
        if rate_limit_override:
            func = rate_limit(requests_per_minute=rate_limit_override)(func)
        else:
            func = rate_limit(requests_per_minute=get_endpoint_rate_limit(endpoint_name))(func)
        
        func = require_auth(func)
        return func
    return decorator


# Common response helpers
def success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response."""
    return APIResponse.success(data=data, message=message)


def error_response(error: Exception, status_code: int = 400) -> Dict[str, Any]:
    """Create standardized error response."""
    if isinstance(error, ValidationException):
        return APIResponse.error(message=str(error), status_code=400)
    elif isinstance(error, ResourceNotFoundException):
        return APIResponse.error(message=str(error), status_code=404)
    elif isinstance(error, PaymentException):
        return APIResponse.error(message=str(error), status_code=402)
    elif isinstance(error, BusinessLogicException):
        return APIResponse.error(message=str(error), status_code=422)
    else:
        return APIResponse.error(message="Internal server error", status_code=500)


# Common validation helpers
def validate_tenant_access(auth_context: Dict[str, Any], tenant_id: str) -> bool:
    """Validate that user has access to tenant."""
    user_tenant_id = auth_context.get('tenant_id')
    if user_tenant_id != tenant_id:
        raise ValidationException(f"Access denied to tenant {tenant_id}")
    return True


def extract_path_parameter(event: Dict[str, Any], param_name: str) -> str:
    """Extract path parameter from event."""
    path_params = event.get('pathParameters') or {}
    param_value = path_params.get(param_name)
    if not param_value:
        raise ValidationException(f"Missing required path parameter: {param_name}")
    return param_value


def extract_query_parameter(event: Dict[str, Any], param_name: str, default: Any = None) -> Any:
    """Extract query parameter from event."""
    query_params = event.get('queryStringParameters') or {}
    return query_params.get(param_name, default)


def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
    """Parse and validate request body."""
    body = event.get('body')
    if not body:
        return {}
    
    try:
        if isinstance(body, str):
            return json.loads(body)
        return body
    except json.JSONDecodeError as e:
        raise ValidationException(f"Invalid JSON in request body: {str(e)}")


# Common logging helpers
def log_payment_operation(operation: str, tenant_id: str, **kwargs):
    """Log payment operation for audit trail."""
    audit_log(
        event_type="payment_operation",
        tenant_id=tenant_id,
        details={
            "operation": operation,
            **kwargs
        }
    )


def log_subscription_change(subscription_id: str, tenant_id: str, change_type: str, **kwargs):
    """Log subscription change for audit trail."""
    audit_log(
        event_type="subscription_change",
        tenant_id=tenant_id,
        details={
            "subscription_id": subscription_id,
            "change_type": change_type,
            **kwargs
        }
    )


# Common error handling
def handle_payment_error(error: Exception, operation: str, tenant_id: str = None) -> Dict[str, Any]:
    """Handle payment errors with proper logging and response."""
    lambda_logger.error(f"Payment operation failed: {operation}", extra={
        "error": str(error),
        "error_type": type(error).__name__,
        "tenant_id": tenant_id,
        "operation": operation
    })
    
    if tenant_id:
        log_payment_operation(
            operation=f"{operation}_failed",
            tenant_id=tenant_id,
            error=str(error),
            error_type=type(error).__name__
        )
    
    return error_response(error)


# Export all commonly used items
__all__ = [
    # Standard library
    'json', 'time', 'uuid', 'datetime', 'timedelta', 'Decimal', 'Enum',
    'Any', 'Dict', 'List', 'Optional', 'Union',
    
    # Shared layer
    'APIResponse', 'handle_cors_preflight', 'require_auth', 'get_auth_context',
    'rate_limit', 'payment_resilience', 'measure_performance',
    'lambda_logger', 'audit_log', 'log_api_request', 'log_api_response',
    'ValidationException', 'PaymentException', 'ResourceNotFoundException',
    'BusinessLogicException', 'ExternalServiceException',
    'db_client', 'SubscriptionInfo', 'SubscriptionStatus', 'BillingInterval',
    'PlanInfo', 'PlanStatus', 'PlanType', 'CustomerInfo', 'CustomerStatus',
    
    # Payment service
    'container', 'get_endpoint_rate_limit', 'get_rate_limit_config',
    'get_config', 'ENVIRONMENT', 'IS_PRODUCTION',
    'Subscription', 'Plan', 'Customer', 'PaymentMethod',
    'ISubscriptionService',
    
    # Helpers
    'payment_handler', 'success_response', 'error_response',
    'validate_tenant_access', 'extract_path_parameter', 'extract_query_parameter',
    'parse_request_body', 'log_payment_operation', 'log_subscription_change',
    'handle_payment_error'
]
