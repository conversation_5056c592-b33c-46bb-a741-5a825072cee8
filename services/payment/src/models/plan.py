# services/payment/src/models/plan.py
# Unified Plan model using shared layer models

"""
Plan model that extends shared layer models for payment service operations.
Uses shared.models as the source of truth for consistency across services.
"""

import time
import uuid
from typing import Any, Dict, List, Optional
from decimal import Decimal

# Import shared layer models (source of truth)
from shared.models import PlanInfo, PlanStatus, PlanType, BillingInterval
from shared.database import db_client
from shared.exceptions import (
    ResourceNotFoundException,
    ValidationException
)
from shared.logger import lambda_logger


class Plan:
    """
    Extended Plan model for payment service operations.
    Uses shared.models.PlanInfo as the core data structure.
    """

    def __init__(
        self,
        plan_id: str = None,
        name: str = None,
        plan_type: PlanType = None,
        status: PlanStatus = PlanStatus.ACTIVE,
        monthly_price: Decimal = None,
        yearly_price: Decimal = None,
        currency: str = "USD",
        features: List[str] = None,
        limits: Dict[str, Any] = None,
        trial_days: int = 0,
        description: str = None,
        # Extended fields specific to payment service
        stripe_monthly_price_id: str = None,
        stripe_yearly_price_id: str = None,
        payu_plan_id: str = None,
        created_at: int = None,
        updated_at: int = None,
        **kwargs
    ):
        """Initialize Plan instance using shared models as base."""
        # Core plan info using shared model
        self._plan_info = PlanInfo(
            plan_id=plan_id or str(uuid.uuid4()),
            name=name,
            plan_type=plan_type if isinstance(plan_type, PlanType) else PlanType(plan_type) if plan_type else None,
            status=status if isinstance(status, PlanStatus) else PlanStatus(status),
            monthly_price=Decimal(str(monthly_price)) if monthly_price is not None else Decimal('0.00'),
            yearly_price=Decimal(str(yearly_price)) if yearly_price is not None else None,
            currency=currency,
            features=features or [],
            limits=limits or {},
            trial_days=trial_days,
            description=description
        )

        # Extended fields specific to payment service
        self.stripe_monthly_price_id = stripe_monthly_price_id
        self.stripe_yearly_price_id = stripe_yearly_price_id
        self.payu_plan_id = payu_plan_id
        self.created_at = created_at or int(time.time())
        self.updated_at = updated_at or int(time.time())

        # Additional fields from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        # Validate plan data
        self._validate()

    # Delegate core properties to shared model
    @property
    def plan_id(self) -> str:
        return self._plan_info.plan_id

    @property
    def name(self) -> str:
        return self._plan_info.name

    @property
    def plan_type(self) -> PlanType:
        return self._plan_info.plan_type

    @property
    def status(self) -> PlanStatus:
        return self._plan_info.status

    @status.setter
    def status(self, value: PlanStatus):
        self._plan_info.status = value

    @property
    def monthly_price(self) -> Decimal:
        return self._plan_info.monthly_price

    @property
    def yearly_price(self) -> Optional[Decimal]:
        return self._plan_info.yearly_price

    @property
    def currency(self) -> str:
        return self._plan_info.currency

    @property
    def features(self) -> List[str]:
        return self._plan_info.features

    @property
    def limits(self) -> Dict[str, Any]:
        return self._plan_info.limits

    @property
    def trial_days(self) -> int:
        return self._plan_info.trial_days

    @property
    def description(self) -> Optional[str]:
        return self._plan_info.description

    def get_shared_info(self) -> PlanInfo:
        """Get the shared plan info for cross-service communication."""
        return self._plan_info

    def _validate(self) -> None:
        """Validate plan data."""
        if not self.name:
            raise ValidationException("Plan name is required")

        if not self.plan_type:
            raise ValidationException("Plan type is required")

        # Basic price validation
        if self.monthly_price < 0:
            raise ValidationException("Monthly price cannot be negative")

        if self.yearly_price and self.yearly_price < 0:
            raise ValidationException("Yearly price cannot be negative")

        if self.trial_days < 0:
            raise ValidationException("Trial days cannot be negative")

    def save(self) -> None:
        """Save plan to database using shared layer."""
        try:
            self.updated_at = int(time.time())

            # Prepare item for DynamoDB using shared layer pattern
            item = {
                'PK': f'PLAN#{self.plan_id}',
                'SK': 'METADATA',
                'entity_type': 'PLAN',
                'GSI1PK': f'PLAN_TYPE#{self.plan_type.value}',
                'GSI1SK': f'PLAN#{self.plan_id}',
                'GSI2PK': f'PLAN_STATUS#{self.status.value}',
                'GSI2SK': f'PLAN#{self.plan_id}',
                # Core plan data
                'plan_id': self.plan_id,
                'name': self.name,
                'plan_type': self.plan_type.value,
                'status': self.status.value,
                'monthly_price': str(self.monthly_price),
                'yearly_price': str(self.yearly_price) if self.yearly_price else None,
                'currency': self.currency,
                'features': self.features,
                'limits': self.limits,
                'trial_days': self.trial_days,
                'description': self.description,
                'created_at': self.created_at,
                'updated_at': self.updated_at,
                # Extended fields
                'stripe_monthly_price_id': self.stripe_monthly_price_id,
                'stripe_yearly_price_id': self.stripe_yearly_price_id,
                'payu_plan_id': self.payu_plan_id
            }

            # Use shared layer database client
            db_client.put_item(item, 'global')

            lambda_logger.info("Plan saved", extra={
                'plan_id': self.plan_id,
                'plan_name': self.name,
                'plan_type': self.plan_type.value
            })

        except Exception as e:
            lambda_logger.error("Failed to save plan", extra={
                'plan_id': self.plan_id,
                'plan_name': self.name,
                'error': str(e)
            })
            raise

    def update(self, **kwargs) -> None:
        """Update plan fields."""
        for key, value in kwargs.items():
            # Update core plan info fields
            if hasattr(self._plan_info, key):
                if key == 'status' and isinstance(value, str):
                    value = PlanStatus(value)
                elif key == 'plan_type' and isinstance(value, str):
                    value = PlanType(value)
                elif key in ['monthly_price', 'yearly_price']:
                    value = Decimal(str(value))
                setattr(self._plan_info, key, value)
            # Update extended fields
            elif hasattr(self, key):
                setattr(self, key, value)

        self.save()

    def get_price_for_interval(self, interval: BillingInterval) -> Decimal:
        """Get price for billing interval."""
        return self._plan_info.get_price_for_interval(interval)

    def is_active(self) -> bool:
        """Check if plan is active."""
        return self.status == PlanStatus.ACTIVE

    def is_free(self) -> bool:
        """Check if plan is free."""
        return self.plan_type == PlanType.FREE

    def has_feature(self, feature: str) -> bool:
        """Check if plan has a specific feature."""
        return feature in self.features

    def get_limit(self, limit_name: str) -> Any:
        """Get a specific limit value."""
        return self.limits.get(limit_name)

    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert plan to dictionary."""
        data = {
            'plan_id': self.plan_id,
            'name': self.name,
            'plan_type': self.plan_type.value,
            'status': self.status.value,
            'monthly_price': str(self.monthly_price),
            'yearly_price': str(self.yearly_price) if self.yearly_price else None,
            'currency': self.currency,
            'features': self.features,
            'limits': self.limits,
            'trial_days': self.trial_days,
            'description': self.description,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_active': self.is_active(),
            'is_free': self.is_free()
        }

        if include_sensitive:
            data.update({
                'stripe_monthly_price_id': self.stripe_monthly_price_id,
                'stripe_yearly_price_id': self.stripe_yearly_price_id,
                'payu_plan_id': self.payu_plan_id
            })

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Plan':
        """Create plan from dictionary."""
        return cls(**data)

    @classmethod
    def get_by_id(cls, plan_id: str) -> Optional['Plan']:
        """Get plan by ID using shared layer."""
        try:
            item = db_client.get_item(
                f'PLAN#{plan_id}',
                'METADATA',
                'global'
            )

            if item:
                return cls.from_dict(item)
            return None

        except Exception as e:
            lambda_logger.error("Failed to get plan", extra={
                'plan_id': plan_id,
                'error': str(e)
            })
            raise

