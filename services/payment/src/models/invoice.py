"""
Invoice model for payment service.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from decimal import Decimal
from enum import Enum
import uuid
import time

from shared.database import db_client
from shared.logger import lambda_logger


class InvoiceStatus(Enum):
    """Invoice status enumeration."""
    DRAFT = "draft"
    OPEN = "open"
    PAID = "paid"
    VOID = "void"
    UNCOLLECTIBLE = "uncollectible"


class InvoiceType(Enum):
    """Invoice type enumeration."""
    SUBSCRIPTION = "subscription"
    ONE_TIME = "one_time"
    USAGE = "usage"
    CREDIT = "credit"


@dataclass
class Invoice:
    """Invoice model."""
    
    # Required fields
    tenant_id: str
    subscription_id: str
    amount_due: Decimal
    currency: str = "USD"
    
    # Auto-generated fields
    invoice_id: str = field(default_factory=lambda: f"inv_{uuid.uuid4().hex[:12]}")
    invoice_number: str = field(default="")
    
    # Status and type
    status: InvoiceStatus = InvoiceStatus.OPEN
    invoice_type: InvoiceType = InvoiceType.SUBSCRIPTION
    
    # Amounts
    amount_paid: Decimal = field(default=Decimal('0.00'))
    amount_remaining: Decimal = field(default=Decimal('0.00'))
    subtotal: Decimal = field(default=Decimal('0.00'))
    tax_amount: Decimal = field(default=Decimal('0.00'))
    
    # Dates (timestamps)
    created_at: int = field(default_factory=lambda: int(time.time()))
    due_date: Optional[int] = None
    paid_at: Optional[int] = None
    voided_at: Optional[int] = None
    
    # Period information
    period_start: Optional[int] = None
    period_end: Optional[int] = None
    
    # Description and metadata
    description: str = ""
    line_items: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Stripe integration
    stripe_invoice_id: Optional[str] = None
    stripe_data: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization setup."""
        if not self.invoice_number:
            # Generate invoice number: INV-YYYY-NNNN
            from datetime import datetime
            year = datetime.fromtimestamp(self.created_at).year
            # Simple sequential number (in production, use a proper sequence)
            seq_num = str(self.created_at)[-4:]
            self.invoice_number = f"INV-{year}-{seq_num}"
        
        if self.amount_remaining == Decimal('0.00'):
            self.amount_remaining = self.amount_due - self.amount_paid
        
        if self.subtotal == Decimal('0.00'):
            self.subtotal = self.amount_due - self.tax_amount
    
    def save(self) -> None:
        """Save invoice to database."""
        try:
            # Prepare data for storage
            invoice_data = self.to_dict()
            
            # Save to database
            db_client.put_item(
                pk=f'TENANT#{self.tenant_id}',
                sk=f'INVOICE#{self.invoice_id}',
                tenant_id=self.tenant_id,
                data=invoice_data
            )
            
            # Create GSI entries for querying
            # GSI1: By status
            db_client.put_item(
                pk=f'INVOICE_STATUS#{self.status.value}',
                sk=f'INVOICE#{self.invoice_id}',
                tenant_id=self.tenant_id,
                data=invoice_data,
                gsi1_pk=f'INVOICE_STATUS#{self.status.value}',
                gsi1_sk=f'TENANT#{self.tenant_id}#{self.created_at}'
            )
            
            # GSI2: By subscription
            if self.subscription_id:
                db_client.put_item(
                    pk=f'SUBSCRIPTION#{self.subscription_id}',
                    sk=f'INVOICE#{self.invoice_id}',
                    tenant_id=self.tenant_id,
                    data=invoice_data,
                    gsi2_pk=f'SUBSCRIPTION#{self.subscription_id}',
                    gsi2_sk=f'INVOICE#{self.created_at}'
                )
            
            lambda_logger.info(f"Invoice saved: {self.invoice_id}")
            
        except Exception as e:
            lambda_logger.error(f"Failed to save invoice {self.invoice_id}: {str(e)}")
            raise
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert invoice to dictionary."""
        return {
            'invoice_id': self.invoice_id,
            'invoice_number': self.invoice_number,
            'tenant_id': self.tenant_id,
            'subscription_id': self.subscription_id,
            'status': self.status.value,
            'invoice_type': self.invoice_type.value,
            'amount_due': float(self.amount_due),
            'amount_paid': float(self.amount_paid),
            'amount_remaining': float(self.amount_remaining),
            'subtotal': float(self.subtotal),
            'tax_amount': float(self.tax_amount),
            'currency': self.currency,
            'created_at': self.created_at,
            'due_date': self.due_date,
            'paid_at': self.paid_at,
            'voided_at': self.voided_at,
            'period_start': self.period_start,
            'period_end': self.period_end,
            'description': self.description,
            'line_items': self.line_items,
            'metadata': self.metadata,
            'stripe_invoice_id': self.stripe_invoice_id,
            'stripe_data': self.stripe_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Invoice':
        """Create invoice from dictionary."""
        # Convert string amounts back to Decimal
        decimal_fields = ['amount_due', 'amount_paid', 'amount_remaining', 'subtotal', 'tax_amount']
        for field in decimal_fields:
            if field in data and data[field] is not None:
                data[field] = Decimal(str(data[field]))
        
        # Convert enum fields
        if 'status' in data:
            data['status'] = InvoiceStatus(data['status'])
        if 'invoice_type' in data:
            data['invoice_type'] = InvoiceType(data['invoice_type'])
        
        return cls(**data)
    
    @classmethod
    def get_by_id(cls, invoice_id: str, tenant_id: str) -> Optional['Invoice']:
        """Get invoice by ID."""
        try:
            item = db_client.get_item(
                f'TENANT#{tenant_id}',
                f'INVOICE#{invoice_id}',
                tenant_id
            )
            
            if item:
                return cls.from_dict(item)
            
            return None
            
        except Exception as e:
            lambda_logger.error(f"Failed to get invoice {invoice_id}: {str(e)}")
            raise
    
    @classmethod
    def get_by_tenant(
        cls, 
        tenant_id: str, 
        limit: int = 10, 
        status: Optional[InvoiceStatus] = None
    ) -> List['Invoice']:
        """Get invoices for a tenant."""
        try:
            if status:
                # Query by status using GSI1
                items = db_client.query(
                    f'INVOICE_STATUS#{status.value}',
                    sk_prefix=f'TENANT#{tenant_id}',
                    tenant_id=tenant_id,
                    index_name='GSI1',
                    limit=limit
                )
            else:
                # Query all invoices for tenant
                items = db_client.query(
                    f'TENANT#{tenant_id}',
                    sk_prefix='INVOICE#',
                    tenant_id=tenant_id,
                    limit=limit
                )
            
            invoices = []
            for item in items:
                try:
                    invoice = cls.from_dict(item)
                    invoices.append(invoice)
                except Exception as e:
                    lambda_logger.warning(f"Failed to parse invoice: {e}")
                    continue
            
            return invoices
            
        except Exception as e:
            lambda_logger.error(f"Failed to get invoices for tenant {tenant_id}: {str(e)}")
            raise
    
    def mark_as_paid(self, paid_amount: Decimal, paid_at: Optional[int] = None) -> None:
        """Mark invoice as paid."""
        self.amount_paid = paid_amount
        self.amount_remaining = self.amount_due - paid_amount
        self.paid_at = paid_at or int(time.time())
        
        if self.amount_remaining <= Decimal('0.00'):
            self.status = InvoiceStatus.PAID
        
        self.save()
    
    def void(self, voided_at: Optional[int] = None) -> None:
        """Void the invoice."""
        self.status = InvoiceStatus.VOID
        self.voided_at = voided_at or int(time.time())
        self.save()
