# services/payment/src/models/customer.py
# Unified Customer model using shared layer models

"""
Customer model that extends shared layer models for payment service operations.
Uses shared.models as the source of truth for consistency across services.
"""

import time
import uuid
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field

# Import shared layer models (source of truth)
from shared.models import CustomerInfo, CustomerStatus
from shared.database import db_client
from shared.logger import lambda_logger
from shared.exceptions import ValidationException


class Customer:
    """
    Extended Customer model for payment service operations.
    Uses shared.models.CustomerInfo as the core data structure.
    """

    def __init__(
        self,
        customer_id: str = None,
        tenant_id: str = None,
        stripe_customer_id: str = None,
        billing_email: str = None,
        company_name: str = None,
        phone: str = None,
        address: Dict[str, str] = None,
        tax_id: str = None,
        status: CustomerStatus = CustomerStatus.ACTIVE,
        # Extended fields specific to payment service
        stripe_data: Dict[str, Any] = None,
        payment_methods: List[Dict[str, Any]] = None,
        created_at: int = None,
        updated_at: int = None,
        **kwargs
    ):
        """Initialize Customer instance using shared models as base."""
        # Core customer info using shared model
        self._customer_info = CustomerInfo(
            customer_id=customer_id or str(uuid.uuid4()),
            tenant_id=tenant_id,
            stripe_customer_id=stripe_customer_id,
            billing_email=billing_email,
            company_name=company_name,
            phone=phone,
            address=address,
            tax_id=tax_id,
            status=status if isinstance(status, CustomerStatus) else CustomerStatus(status) if status else CustomerStatus.ACTIVE,
            created_at=created_at or int(time.time()),
            updated_at=updated_at or int(time.time())
        )

        # Extended fields specific to payment service
        self.stripe_data = stripe_data or {}
        self.payment_methods = payment_methods or []

        # Additional fields from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

    # Delegate core properties to shared model
    @property
    def customer_id(self) -> str:
        return self._customer_info.customer_id

    @property
    def tenant_id(self) -> str:
        return self._customer_info.tenant_id

    @property
    def stripe_customer_id(self) -> Optional[str]:
        return self._customer_info.stripe_customer_id

    @stripe_customer_id.setter
    def stripe_customer_id(self, value: str):
        self._customer_info.stripe_customer_id = value

    @property
    def billing_email(self) -> Optional[str]:
        return self._customer_info.billing_email

    @billing_email.setter
    def billing_email(self, value: str):
        self._customer_info.billing_email = value

    @property
    def company_name(self) -> Optional[str]:
        return self._customer_info.company_name

    @company_name.setter
    def company_name(self, value: str):
        self._customer_info.company_name = value

    @property
    def phone(self) -> Optional[str]:
        return self._customer_info.phone

    @phone.setter
    def phone(self, value: str):
        self._customer_info.phone = value

    @property
    def address(self) -> Optional[Dict[str, str]]:
        return self._customer_info.address

    @address.setter
    def address(self, value: Dict[str, str]):
        self._customer_info.address = value

    @property
    def tax_id(self) -> Optional[str]:
        return self._customer_info.tax_id

    @tax_id.setter
    def tax_id(self, value: str):
        self._customer_info.tax_id = value

    @property
    def status(self) -> CustomerStatus:
        return self._customer_info.status

    @status.setter
    def status(self, value: CustomerStatus):
        self._customer_info.status = value

    @property
    def created_at(self) -> Optional[int]:
        return self._customer_info.created_at

    @property
    def updated_at(self) -> Optional[int]:
        return self._customer_info.updated_at

    def get_shared_info(self) -> CustomerInfo:
        """Get the shared customer info for cross-service communication."""
        return self._customer_info

    def _validate(self) -> None:
        """Validate customer data."""
        if not self.tenant_id:
            raise ValidationException("Tenant ID is required")

        if self.billing_email:
            from shared.validators import validate_email_address
            validate_email_address(self.billing_email)

    def is_active(self) -> bool:
        """Check if customer is active."""
        return self._customer_info.is_active()

    def save(self) -> None:
        """Save customer to database using shared layer."""
        try:
            # Update timestamp
            self._customer_info.updated_at = int(time.time())

            # Prepare item for DynamoDB using shared layer pattern
            item = {
                'PK': f'TENANT#{self.tenant_id}',
                'SK': f'CUSTOMER#{self.customer_id}',
                'entity_type': 'CUSTOMER',
                'GSI1PK': 'CUSTOMERS',
                'GSI1SK': f'TENANT#{self.tenant_id}#EMAIL#{self.billing_email}',
                'GSI2PK': f'STRIPE_CUSTOMER#{self.stripe_customer_id}' if self.stripe_customer_id else None,
                'GSI2SK': f'CUSTOMER#{self.customer_id}',
                # Core customer data
                'customer_id': self.customer_id,
                'tenant_id': self.tenant_id,
                'stripe_customer_id': self.stripe_customer_id,
                'billing_email': self.billing_email,
                'company_name': self.company_name,
                'phone': self.phone,
                'address': self.address,
                'tax_id': self.tax_id,
                'status': self.status.value,
                'created_at': self.created_at,
                'updated_at': self.updated_at,
                # Extended fields
                'stripe_data': self.stripe_data,
                'payment_methods': self.payment_methods
            }

            # Use shared layer database client
            db_client.put_item(item, self.tenant_id)

            lambda_logger.info("Customer saved", extra={
                'customer_id': self.customer_id,
                'tenant_id': self.tenant_id
            })

        except Exception as e:
            lambda_logger.error("Failed to save customer", extra={
                'customer_id': self.customer_id,
                'tenant_id': self.tenant_id,
                'error': str(e)
            })
            raise

    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert customer to dictionary."""
        data = {
            'customer_id': self.customer_id,
            'tenant_id': self.tenant_id,
            'billing_email': self.billing_email,
            'company_name': self.company_name,
            'phone': self.phone,
            'address': self.address,
            'tax_id': self.tax_id,
            'status': self.status.value,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_active': self.is_active()
        }

        if include_sensitive:
            data.update({
                'stripe_customer_id': self.stripe_customer_id,
                'stripe_data': self.stripe_data,
                'payment_methods': self.payment_methods
            })

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Customer':
        """Create customer from dictionary."""
        return cls(**data)

