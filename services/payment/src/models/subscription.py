# services/payment/src/models/subscription.py
# Unified Subscription model using shared layer models

"""
Subscription model that extends shared layer models for payment service operations.
Uses shared.models as the source of truth for consistency across services.
"""

import time
import uuid
from typing import Any, Dict, List, Optional
from decimal import Decimal
from enum import Enum

# Import shared layer models (source of truth)
from shared.models import SubscriptionInfo, SubscriptionStatus, BillingInterval
from shared.database import db_client
from shared.exceptions import (
    ResourceNotFoundException,
    ValidationException,
    PaymentException
)
from shared.logger import lambda_logger


class Subscription:
    """
    Extended Subscription model for payment service operations.
    Uses shared.models.SubscriptionInfo as the core data structure.
    """

    def __init__(
        self,
        subscription_id: str = None,
        tenant_id: str = None,
        plan_id: str = None,
        status: SubscriptionStatus = SubscriptionStatus.TRIAL,
        billing_interval: BillingInterval = BillingInterval.MONTHLY,
        amount: Decimal = None,
        currency: str = "USD",
        trial_ends_at: int = None,
        current_period_start: int = None,
        current_period_end: int = None,
        next_billing_date: int = None,
        stripe_subscription_id: str = None,
        # Extended fields specific to payment service
        payment_method_id: str = None,
        payu_subscription_id: str = None,
        created_at: int = None,
        updated_at: int = None,
        **kwargs
    ):
        """Initialize Subscription instance using shared models as base."""
        # Core subscription info using shared model
        self._subscription_info = SubscriptionInfo(
            subscription_id=subscription_id or str(uuid.uuid4()),
            tenant_id=tenant_id,
            plan_id=plan_id,
            status=status if isinstance(status, SubscriptionStatus) else SubscriptionStatus(status),
            billing_interval=billing_interval if isinstance(billing_interval, BillingInterval) else BillingInterval(billing_interval),
            amount=Decimal(str(amount)) if amount else Decimal('0.00'),
            currency=currency,
            trial_ends_at=trial_ends_at,
            current_period_start=current_period_start or int(time.time()),
            current_period_end=current_period_end,
            next_billing_date=next_billing_date,
            stripe_subscription_id=stripe_subscription_id,
            created_at=created_at or int(time.time()),
            updated_at=updated_at or int(time.time())
        )

        # Extended fields specific to payment service
        self.payment_method_id = payment_method_id
        self.payu_subscription_id = payu_subscription_id

        # Additional fields from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

    # Delegate core properties to shared model
    @property
    def subscription_id(self) -> str:
        return self._subscription_info.subscription_id

    @property
    def tenant_id(self) -> str:
        return self._subscription_info.tenant_id

    @property
    def plan_id(self) -> str:
        return self._subscription_info.plan_id

    @property
    def status(self) -> SubscriptionStatus:
        return self._subscription_info.status

    @status.setter
    def status(self, value: SubscriptionStatus):
        self._subscription_info.status = value

    @property
    def billing_interval(self) -> BillingInterval:
        return self._subscription_info.billing_interval

    @property
    def amount(self) -> Decimal:
        return self._subscription_info.amount

    @property
    def currency(self) -> str:
        return self._subscription_info.currency

    @property
    def trial_ends_at(self) -> Optional[int]:
        return self._subscription_info.trial_ends_at

    @property
    def current_period_start(self) -> Optional[int]:
        return self._subscription_info.current_period_start

    @property
    def current_period_end(self) -> Optional[int]:
        return self._subscription_info.current_period_end

    @property
    def next_billing_date(self) -> Optional[int]:
        return self._subscription_info.next_billing_date

    @property
    def stripe_subscription_id(self) -> Optional[str]:
        return self._subscription_info.stripe_subscription_id

    @property
    def created_at(self) -> Optional[int]:
        return self._subscription_info.created_at

    @property
    def updated_at(self) -> Optional[int]:
        return self._subscription_info.updated_at

    def get_shared_info(self) -> SubscriptionInfo:
        """Get the shared subscription info for cross-service communication."""
        return self._subscription_info
    
    def save(self) -> None:
        """Save subscription to database using shared layer."""
        try:
            # Update timestamp
            self._subscription_info.updated_at = int(time.time())

            # Prepare item for DynamoDB using shared layer pattern
            item = {
                'PK': f'TENANT#{self.tenant_id}',
                'SK': f'SUBSCRIPTION#{self.subscription_id}',
                'entity_type': 'SUBSCRIPTION',
                'GSI1PK': f'SUBSCRIPTION#{self.subscription_id}',
                'GSI1SK': f'TENANT#{self.tenant_id}',
                'GSI2PK': f'PLAN#{self.plan_id}',
                'GSI2SK': f'SUBSCRIPTION#{self.subscription_id}',
                # Core subscription data
                'subscription_id': self.subscription_id,
                'tenant_id': self.tenant_id,
                'plan_id': self.plan_id,
                'status': self.status.value,
                'billing_interval': self.billing_interval.value,
                'amount': str(self.amount),
                'currency': self.currency,
                'trial_ends_at': self.trial_ends_at,
                'current_period_start': self.current_period_start,
                'current_period_end': self.current_period_end,
                'next_billing_date': self.next_billing_date,
                'stripe_subscription_id': self.stripe_subscription_id,
                'created_at': self.created_at,
                'updated_at': self.updated_at,
                # Extended fields
                'payment_method_id': self.payment_method_id,
                'payu_subscription_id': self.payu_subscription_id
            }

            # Use shared layer database client
            db_client.put_item(item, self.tenant_id)

            lambda_logger.info("Subscription saved", extra={
                'subscription_id': self.subscription_id,
                'tenant_id': self.tenant_id,
                'status': self.status.value
            })

        except Exception as e:
            lambda_logger.error("Failed to save subscription", extra={
                'subscription_id': self.subscription_id,
                'tenant_id': self.tenant_id,
                'error': str(e)
            })
            raise
    
    def update(self, **kwargs) -> None:
        """Update subscription fields."""
        for key, value in kwargs.items():
            # Update core subscription info fields
            if hasattr(self._subscription_info, key):
                if key == 'status' and isinstance(value, str):
                    value = SubscriptionStatus(value)
                elif key == 'billing_interval' and isinstance(value, str):
                    value = BillingInterval(value)
                elif key == 'amount':
                    value = Decimal(str(value))
                setattr(self._subscription_info, key, value)
            # Update extended fields
            elif hasattr(self, key):
                setattr(self, key, value)

        self.save()

    def is_active(self) -> bool:
        """Check if subscription is active."""
        return self._subscription_info.is_active()

    def is_trial(self) -> bool:
        """Check if subscription is in trial."""
        return self._subscription_info.is_trial()

    def is_expired(self) -> bool:
        """Check if subscription is expired."""
        return self._subscription_info.is_expired()

    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert subscription to dictionary."""
        data = {
            'subscription_id': self.subscription_id,
            'tenant_id': self.tenant_id,
            'plan_id': self.plan_id,
            'status': self.status.value,
            'billing_interval': self.billing_interval.value,
            'amount': str(self.amount),
            'currency': self.currency,
            'trial_ends_at': self.trial_ends_at,
            'current_period_start': self.current_period_start,
            'current_period_end': self.current_period_end,
            'next_billing_date': self.next_billing_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_active': self.is_active(),
            'is_trial': self.is_trial(),
            'is_expired': self.is_expired()
        }

        if include_sensitive:
            data.update({
                'payment_method_id': self.payment_method_id,
                'stripe_subscription_id': self.stripe_subscription_id,
                'payu_subscription_id': self.payu_subscription_id
            })

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Subscription':
        """Create subscription from dictionary."""
        return cls(**data)

    @classmethod
    def get_by_id(cls, subscription_id: str, tenant_id: str) -> Optional['Subscription']:
        """Get subscription by ID using shared layer."""
        try:
            item = db_client.get_item(
                f'TENANT#{tenant_id}',
                f'SUBSCRIPTION#{subscription_id}',
                tenant_id
            )

            if item:
                return cls.from_dict(item)

            return None

        except Exception as e:
            lambda_logger.error("Failed to get subscription by ID", extra={
                'subscription_id': subscription_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise

