"""
Payment Method model for payment service.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum
import uuid
import time

from shared.database import db_client
from shared.logger import lambda_logger


class PaymentMethodType(Enum):
    """Payment method type enumeration."""
    CARD = "card"
    BANK_ACCOUNT = "bank_account"
    PAYPAL = "paypal"
    APPLE_PAY = "apple_pay"
    GOOGLE_PAY = "google_pay"


class PaymentMethodStatus(Enum):
    """Payment method status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    REQUIRES_ACTION = "requires_action"


@dataclass
class PaymentMethod:
    """Payment method model."""
    
    # Required fields
    tenant_id: str
    payment_method_type: PaymentMethodType
    
    # Auto-generated fields
    payment_method_id: str = field(default_factory=lambda: f"pm_{uuid.uuid4().hex[:12]}")
    
    # Status and configuration
    status: PaymentMethodStatus = PaymentMethodStatus.ACTIVE
    is_default: bool = False
    
    # Card details (for card type)
    card_brand: Optional[str] = None
    card_last4: Optional[str] = None
    card_exp_month: Optional[int] = None
    card_exp_year: Optional[int] = None
    card_country: Optional[str] = None
    
    # Billing details
    billing_address: Dict[str, Any] = field(default_factory=dict)
    billing_email: Optional[str] = None
    billing_name: Optional[str] = None
    
    # Timestamps
    created_at: int = field(default_factory=lambda: int(time.time()))
    updated_at: int = field(default_factory=lambda: int(time.time()))
    
    # Metadata and external references
    metadata: Dict[str, Any] = field(default_factory=dict)
    stripe_payment_method_id: Optional[str] = None
    stripe_data: Dict[str, Any] = field(default_factory=dict)
    
    def save(self) -> None:
        """Save payment method to database."""
        try:
            # Update timestamp
            self.updated_at = int(time.time())
            
            # Prepare data for storage
            payment_method_data = self.to_dict()
            
            # Save to database
            db_client.put_item(
                pk=f'TENANT#{self.tenant_id}',
                sk=f'PAYMENT_METHOD#{self.payment_method_id}',
                tenant_id=self.tenant_id,
                data=payment_method_data
            )
            
            # Create GSI entries for querying
            # GSI1: By status
            db_client.put_item(
                pk=f'PM_STATUS#{self.status.value}',
                sk=f'PM#{self.payment_method_id}',
                tenant_id=self.tenant_id,
                data=payment_method_data,
                gsi1_pk=f'PM_STATUS#{self.status.value}',
                gsi1_sk=f'TENANT#{self.tenant_id}#{self.created_at}'
            )
            
            # GSI2: By default status
            if self.is_default:
                db_client.put_item(
                    pk=f'PM_DEFAULT#{self.tenant_id}',
                    sk=f'PM#{self.payment_method_id}',
                    tenant_id=self.tenant_id,
                    data=payment_method_data,
                    gsi2_pk=f'PM_DEFAULT#{self.tenant_id}',
                    gsi2_sk=f'PM#{self.created_at}'
                )
            
            lambda_logger.info(f"Payment method saved: {self.payment_method_id}")
            
        except Exception as e:
            lambda_logger.error(f"Failed to save payment method {self.payment_method_id}: {str(e)}")
            raise
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert payment method to dictionary."""
        return {
            'payment_method_id': self.payment_method_id,
            'tenant_id': self.tenant_id,
            'payment_method_type': self.payment_method_type.value,
            'status': self.status.value,
            'is_default': self.is_default,
            'card_brand': self.card_brand,
            'card_last4': self.card_last4,
            'card_exp_month': self.card_exp_month,
            'card_exp_year': self.card_exp_year,
            'card_country': self.card_country,
            'billing_address': self.billing_address,
            'billing_email': self.billing_email,
            'billing_name': self.billing_name,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'metadata': self.metadata,
            'stripe_payment_method_id': self.stripe_payment_method_id,
            'stripe_data': self.stripe_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PaymentMethod':
        """Create payment method from dictionary."""
        # Convert enum fields
        if 'payment_method_type' in data:
            data['payment_method_type'] = PaymentMethodType(data['payment_method_type'])
        if 'status' in data:
            data['status'] = PaymentMethodStatus(data['status'])
        
        return cls(**data)
    
    @classmethod
    def get_by_id(cls, payment_method_id: str, tenant_id: str) -> Optional['PaymentMethod']:
        """Get payment method by ID."""
        try:
            item = db_client.get_item(
                f'TENANT#{tenant_id}',
                f'PAYMENT_METHOD#{payment_method_id}',
                tenant_id
            )
            
            if item:
                return cls.from_dict(item)
            
            return None
            
        except Exception as e:
            lambda_logger.error(f"Failed to get payment method {payment_method_id}: {str(e)}")
            raise
    
    @classmethod
    def get_by_tenant(
        cls, 
        tenant_id: str, 
        limit: int = 10, 
        status: Optional[PaymentMethodStatus] = None
    ) -> List['PaymentMethod']:
        """Get payment methods for a tenant."""
        try:
            if status:
                # Query by status using GSI1
                items = db_client.query(
                    f'PM_STATUS#{status.value}',
                    sk_prefix=f'TENANT#{tenant_id}',
                    tenant_id=tenant_id,
                    index_name='GSI1',
                    limit=limit
                )
            else:
                # Query all payment methods for tenant
                items = db_client.query(
                    f'TENANT#{tenant_id}',
                    sk_prefix='PAYMENT_METHOD#',
                    tenant_id=tenant_id,
                    limit=limit
                )
            
            payment_methods = []
            for item in items:
                try:
                    payment_method = cls.from_dict(item)
                    payment_methods.append(payment_method)
                except Exception as e:
                    lambda_logger.warning(f"Failed to parse payment method: {e}")
                    continue
            
            return payment_methods
            
        except Exception as e:
            lambda_logger.error(f"Failed to get payment methods for tenant {tenant_id}: {str(e)}")
            raise
    
    @classmethod
    def get_default_for_tenant(cls, tenant_id: str) -> Optional['PaymentMethod']:
        """Get default payment method for a tenant."""
        try:
            items = db_client.query(
                f'PM_DEFAULT#{tenant_id}',
                sk_prefix='PM#',
                tenant_id=tenant_id,
                index_name='GSI2',
                limit=1
            )
            
            if items:
                return cls.from_dict(items[0])
            
            return None
            
        except Exception as e:
            lambda_logger.error(f"Failed to get default payment method for tenant {tenant_id}: {str(e)}")
            raise
    
    def set_as_default(self) -> None:
        """Set this payment method as default for the tenant."""
        # First, unset any existing default
        existing_methods = self.get_by_tenant(self.tenant_id)
        for method in existing_methods:
            if method.is_default and method.payment_method_id != self.payment_method_id:
                method.is_default = False
                method.save()
        
        # Set this as default
        self.is_default = True
        self.save()
    
    def deactivate(self) -> None:
        """Deactivate the payment method."""
        self.status = PaymentMethodStatus.INACTIVE
        self.is_default = False  # Can't be default if inactive
        self.save()
    
    def is_card(self) -> bool:
        """Check if this is a card payment method."""
        return self.payment_method_type == PaymentMethodType.CARD
    
    def get_display_name(self) -> str:
        """Get a display-friendly name for the payment method."""
        if self.is_card() and self.card_brand and self.card_last4:
            return f"{self.card_brand.title()} •••• {self.card_last4}"
        elif self.metadata.get('nickname'):
            return self.metadata['nickname']
        else:
            return f"{self.payment_method_type.value.title()} Payment Method"
