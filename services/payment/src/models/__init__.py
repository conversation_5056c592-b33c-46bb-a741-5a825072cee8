# services/payment/src/models/__init__.py
# Models package for payment service

"""
Models package for payment service.
Exports unified models that use shared layer as source of truth.
"""

from .subscription import Subscription
from .plan import Plan
from .customer import Customer
from .invoice import Invoice, InvoiceStatus, InvoiceType
from .payment_method import PaymentMethod, PaymentMethodType, PaymentMethodStatus

# Re-export unified shared models for consistency
from shared.models import (
    SubscriptionInfo, SubscriptionStatus, BillingInterval,
    PlanInfo, PlanStatus, PlanType,
    CustomerInfo, CustomerStatus,
    UserRole, UserStatus, UserInfo  # Added unified user models
)

__all__ = [
    # Extended models (payment service specific)
    'Subscription',
    'Plan',
    'Customer',
    'PaymentMethod',
    'PaymentMethodType',
    'PaymentMethodStatus',
    'Invoice',
    'InvoiceStatus',
    'InvoiceType',

    # Unified shared models (cross-service)
    'SubscriptionInfo',
    'SubscriptionStatus',
    'BillingInterval',
    'PlanInfo',
    'PlanStatus',
    'PlanType',
    'CustomerInfo',
    'CustomerStatus',
    'UserRole',
    'UserStatus',
    'UserInfo'
]
