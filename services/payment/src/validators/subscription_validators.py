#!/usr/bin/env python3
# services/payment/src/validators/subscription_validators.py
# Subscription-specific validators

"""
Validators for subscription-related operations.
"""

from typing import Dict, Any, List
from shared.exceptions import ValidationException


def validate_create_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate create subscription request data.
    
    Args:
        data: Request data to validate
        
    Returns:
        Validated and cleaned data
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    # Required fields
    required_fields = ['plan_id']
    for field in required_fields:
        if not data.get(field):
            errors.append({
                "field": field,
                "message": f"{field.replace('_', ' ').title()} is required"
            })
    
    # Plan ID validation
    plan_id = data.get('plan_id', '').strip()
    if plan_id:
        if len(plan_id) < 3 or len(plan_id) > 50:
            errors.append({
                "field": "plan_id",
                "message": "Plan ID must be between 3 and 50 characters"
            })
    
    # Billing interval validation
    billing_interval = data.get('billing_interval', 'MONTHLY').upper()
    valid_intervals = ['MONTHLY', 'YEARLY']
    if billing_interval not in valid_intervals:
        errors.append({
            "field": "billing_interval",
            "message": f"Billing interval must be one of: {', '.join(valid_intervals)}"
        })
    
    # Payment method ID validation (optional)
    payment_method_id = data.get('payment_method_id')
    if payment_method_id:
        payment_method_id = payment_method_id.strip()
        if len(payment_method_id) < 3:
            errors.append({
                "field": "payment_method_id",
                "message": "Payment method ID must be at least 3 characters"
            })
    
    # Coupon code validation (optional)
    coupon_code = data.get('coupon_code')
    if coupon_code:
        coupon_code = coupon_code.strip()
        if len(coupon_code) < 2 or len(coupon_code) > 50:
            errors.append({
                "field": "coupon_code",
                "message": "Coupon code must be between 2 and 50 characters"
            })
    
    if errors:
        raise ValidationException(
            "Validation failed for create subscription request",
            validation_errors=errors
        )
    
    return {
        'plan_id': plan_id,
        'billing_interval': billing_interval,
        'payment_method_id': payment_method_id,
        'coupon_code': coupon_code
    }


def validate_update_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate update subscription request data.
    
    Args:
        data: Request data to validate
        
    Returns:
        Validated and cleaned data
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    # At least one field must be provided
    updatable_fields = ['plan_id', 'billing_interval', 'payment_method_id']
    if not any(data.get(field) for field in updatable_fields):
        errors.append({
            "field": "general",
            "message": "At least one field must be provided for update"
        })
    
    # Plan ID validation (optional)
    plan_id = data.get('plan_id')
    if plan_id:
        plan_id = plan_id.strip()
        if len(plan_id) < 3 or len(plan_id) > 50:
            errors.append({
                "field": "plan_id",
                "message": "Plan ID must be between 3 and 50 characters"
            })
    
    # Billing interval validation (optional)
    billing_interval = data.get('billing_interval')
    if billing_interval:
        billing_interval = billing_interval.upper()
        valid_intervals = ['MONTHLY', 'YEARLY']
        if billing_interval not in valid_intervals:
            errors.append({
                "field": "billing_interval",
                "message": f"Billing interval must be one of: {', '.join(valid_intervals)}"
            })
    
    # Payment method ID validation (optional)
    payment_method_id = data.get('payment_method_id')
    if payment_method_id:
        payment_method_id = payment_method_id.strip()
        if len(payment_method_id) < 3:
            errors.append({
                "field": "payment_method_id",
                "message": "Payment method ID must be at least 3 characters"
            })

    # Prorate validation (optional, defaults to True)
    prorate = data.get('prorate', True)
    if not isinstance(prorate, bool):
        errors.append({
            "field": "prorate",
            "message": "Prorate must be a boolean value"
        })

    # Effective date validation (optional)
    effective_date = data.get('effective_date', 'immediate')
    if effective_date:
        effective_date = effective_date.strip().lower()
        valid_dates = ['immediate', 'next_billing_cycle']
        if effective_date not in valid_dates:
            errors.append({
                "field": "effective_date",
                "message": f"Effective date must be one of: {', '.join(valid_dates)}"
            })

    if errors:
        raise ValidationException(
            "Validation failed for update subscription request",
            validation_errors=errors
        )

    return {
        'plan_id': plan_id,
        'billing_interval': billing_interval,
        'payment_method_id': payment_method_id,
        'prorate': prorate,
        'effective_date': effective_date
    }


def validate_pause_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate pause subscription request data.
    
    Args:
        data: Request data to validate
        
    Returns:
        Validated and cleaned data
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    # Pause reason validation (optional)
    pause_reason = data.get('pause_reason', '').strip()
    if pause_reason and len(pause_reason) > 500:
        errors.append({
            "field": "pause_reason",
            "message": "Pause reason must not exceed 500 characters"
        })
    
    # Resume date validation (optional)
    resume_date = data.get('resume_date')
    if resume_date:
        # Basic date format validation - will be handled by datetime parsing
        if not isinstance(resume_date, str) or len(resume_date) < 10:
            errors.append({
                "field": "resume_date",
                "message": "Resume date must be in valid ISO format (YYYY-MM-DD)"
            })
    
    if errors:
        raise ValidationException(
            "Validation failed for pause subscription request",
            validation_errors=errors
        )
    
    return {
        'pause_reason': pause_reason,
        'resume_date': resume_date
    }


def validate_cancel_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate cancel subscription request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Cancellation reason validation (optional)
    reason = data.get('reason', '').strip()
    if reason and len(reason) > 500:
        errors.append({
            "field": "reason",
            "message": "Cancellation reason must not exceed 500 characters"
        })

    # At period end validation (optional, defaults to True)
    at_period_end = data.get('at_period_end', True)
    if not isinstance(at_period_end, bool):
        errors.append({
            "field": "at_period_end",
            "message": "at_period_end must be a boolean value"
        })

    # Immediate cancellation validation
    immediate = data.get('immediate', False)
    if not isinstance(immediate, bool):
        errors.append({
            "field": "immediate",
            "message": "immediate must be a boolean value"
        })

    # Cannot have both at_period_end and immediate as True
    if at_period_end and immediate:
        errors.append({
            "field": "general",
            "message": "Cannot set both at_period_end and immediate to true"
        })

    if errors:
        raise ValidationException(
            "Validation failed for cancel subscription request",
            validation_errors=errors
        )

    return {
        'reason': reason,
        'at_period_end': at_period_end,
        'immediate': immediate
    }


def validate_resume_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate resume subscription request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Immediate resumption validation (optional, defaults to True)
    immediate = data.get('immediate', True)
    if not isinstance(immediate, bool):
        errors.append({
            "field": "immediate",
            "message": "immediate must be a boolean value"
        })

    # Resume reason validation (optional)
    reason = data.get('reason', '').strip()
    if reason and len(reason) > 500:
        errors.append({
            "field": "reason",
            "message": "Resume reason must not exceed 500 characters"
        })

    # Resume date validation (optional, only if not immediate)
    resume_date = data.get('resume_date')
    if resume_date and not immediate:
        # Basic date format validation - will be handled by datetime parsing
        if not isinstance(resume_date, str) or len(resume_date) < 10:
            errors.append({
                "field": "resume_date",
                "message": "Resume date must be in valid ISO format (YYYY-MM-DD)"
            })

    # Cannot specify resume_date if immediate is True
    if immediate and resume_date:
        errors.append({
            "field": "general",
            "message": "Cannot specify resume_date when immediate is true"
        })

    if errors:
        raise ValidationException(
            "Validation failed for resume subscription request",
            validation_errors=errors
        )

    return {
        'immediate': immediate,
        'reason': reason,
        'resume_date': resume_date if not immediate else None
    }


def validate_list_plans_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate list plans request query parameters.

    Args:
        data: Query parameters to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Billing interval validation (optional)
    billing_interval = data.get('billing_interval')
    if billing_interval:
        billing_interval = billing_interval.upper()
        valid_intervals = ['MONTHLY', 'YEARLY']
        if billing_interval not in valid_intervals:
            errors.append({
                "field": "billing_interval",
                "message": f"Billing interval must be one of: {', '.join(valid_intervals)}"
            })

    # Active only validation (optional, defaults to True)
    active_only = data.get('active_only', 'true')
    if isinstance(active_only, str):
        active_only = active_only.lower() in ['true', '1', 'yes']
    elif not isinstance(active_only, bool):
        errors.append({
            "field": "active_only",
            "message": "active_only must be a boolean value"
        })

    # Limit validation (optional, defaults to 10)
    limit = data.get('limit', '10')
    try:
        limit = int(limit)
        if limit < 1 or limit > 100:
            errors.append({
                "field": "limit",
                "message": "Limit must be between 1 and 100"
            })
    except (ValueError, TypeError):
        errors.append({
            "field": "limit",
            "message": "Limit must be a valid integer"
        })

    # Offset validation (optional, defaults to 0)
    offset = data.get('offset', '0')
    try:
        offset = int(offset)
        if offset < 0:
            errors.append({
                "field": "offset",
                "message": "Offset must be a non-negative integer"
            })
    except (ValueError, TypeError):
        errors.append({
            "field": "offset",
            "message": "Offset must be a valid integer"
        })

    # Category validation (optional)
    category = data.get('category')
    if category:
        category = category.strip().lower()
        valid_categories = ['basic', 'professional', 'enterprise', 'custom']
        if category not in valid_categories:
            errors.append({
                "field": "category",
                "message": f"Category must be one of: {', '.join(valid_categories)}"
            })

    if errors:
        raise ValidationException(
            "Validation failed for list plans request",
            validation_errors=errors
        )

    return {
        'billing_interval': billing_interval,
        'active_only': active_only,
        'limit': limit,
        'offset': offset,
        'category': category
    }


def validate_billing_history_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate billing history request query parameters.

    Args:
        data: Query parameters to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Limit validation (optional, defaults to 10)
    limit = data.get('limit', '10')
    try:
        limit = int(limit)
        if limit < 1 or limit > 100:
            errors.append({
                "field": "limit",
                "message": "Limit must be between 1 and 100"
            })
    except (ValueError, TypeError):
        errors.append({
            "field": "limit",
            "message": "Limit must be a valid integer"
        })

    # Starting after validation (optional)
    starting_after = data.get('starting_after')
    if starting_after:
        starting_after = starting_after.strip()
        if len(starting_after) < 3:
            errors.append({
                "field": "starting_after",
                "message": "starting_after must be at least 3 characters"
            })

    # Status validation (optional)
    status = data.get('status')
    if status:
        status = status.lower()
        valid_statuses = ['paid', 'open', 'draft', 'void', 'uncollectible']
        if status not in valid_statuses:
            errors.append({
                "field": "status",
                "message": f"Status must be one of: {', '.join(valid_statuses)}"
            })

    # Date from validation (optional)
    date_from = data.get('date_from')
    if date_from:
        # Basic date format validation - will be handled by datetime parsing
        if not isinstance(date_from, str) or len(date_from) < 10:
            errors.append({
                "field": "date_from",
                "message": "date_from must be in valid ISO format (YYYY-MM-DD)"
            })

    # Date to validation (optional)
    date_to = data.get('date_to')
    if date_to:
        # Basic date format validation - will be handled by datetime parsing
        if not isinstance(date_to, str) or len(date_to) < 10:
            errors.append({
                "field": "date_to",
                "message": "date_to must be in valid ISO format (YYYY-MM-DD)"
            })

    # Invoice type validation (optional)
    invoice_type = data.get('invoice_type')
    if invoice_type:
        invoice_type = invoice_type.lower()
        valid_types = ['subscription', 'one_time', 'credit_note', 'debit_note']
        if invoice_type not in valid_types:
            errors.append({
                "field": "invoice_type",
                "message": f"Invoice type must be one of: {', '.join(valid_types)}"
            })

    if errors:
        raise ValidationException(
            "Validation failed for billing history request",
            validation_errors=errors
        )

    return {
        'limit': limit,
        'starting_after': starting_after,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'invoice_type': invoice_type
    }


def validate_download_invoice_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate download invoice request query parameters.

    Args:
        data: Query parameters to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Format validation (optional, defaults to 'pdf')
    format_type = data.get('format', 'pdf')
    if format_type:
        format_type = format_type.lower()
        valid_formats = ['pdf', 'html', 'json']
        if format_type not in valid_formats:
            errors.append({
                "field": "format",
                "message": f"Format must be one of: {', '.join(valid_formats)}"
            })

    # Include attachments validation (optional, defaults to False)
    include_attachments = data.get('include_attachments', 'false')
    if isinstance(include_attachments, str):
        include_attachments = include_attachments.lower() in ['true', '1', 'yes']
    elif not isinstance(include_attachments, bool):
        errors.append({
            "field": "include_attachments",
            "message": "include_attachments must be a boolean value"
        })

    # Language validation (optional, defaults to 'en')
    language = data.get('language', 'en')
    if language:
        language = language.lower()
        valid_languages = ['en', 'es', 'fr', 'de', 'pt']
        if language not in valid_languages:
            errors.append({
                "field": "language",
                "message": f"Language must be one of: {', '.join(valid_languages)}"
            })

    if errors:
        raise ValidationException(
            "Validation failed for download invoice request",
            validation_errors=errors
        )

    return {
        'format': format_type,
        'include_attachments': include_attachments,
        'language': language
    }


def validate_update_payment_method_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate update payment method request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Payment method ID validation (required)
    payment_method_id = data.get('payment_method_id', '').strip()
    if not payment_method_id:
        errors.append({
            "field": "payment_method_id",
            "message": "Payment method ID is required"
        })
    elif not payment_method_id.startswith('pm_'):
        errors.append({
            "field": "payment_method_id",
            "message": "Payment method ID must start with 'pm_'"
        })
    elif len(payment_method_id) < 10:
        errors.append({
            "field": "payment_method_id",
            "message": "Payment method ID must be at least 10 characters"
        })

    # Set as default validation (optional, defaults to False)
    set_as_default = data.get('set_as_default', False)
    if not isinstance(set_as_default, bool):
        errors.append({
            "field": "set_as_default",
            "message": "set_as_default must be a boolean value"
        })

    # Billing address validation (optional)
    billing_address = data.get('billing_address')
    if billing_address:
        if not isinstance(billing_address, dict):
            errors.append({
                "field": "billing_address",
                "message": "billing_address must be an object"
            })
        else:
            # Validate billing address fields
            required_fields = ['line1', 'city', 'country']
            for field in required_fields:
                if not billing_address.get(field, '').strip():
                    errors.append({
                        "field": f"billing_address.{field}",
                        "message": f"{field} is required in billing address"
                    })

            # Validate country code
            country = billing_address.get('country', '').strip().upper()
            if country and len(country) != 2:
                errors.append({
                    "field": "billing_address.country",
                    "message": "Country must be a 2-letter ISO code"
                })

            # Validate postal code for specific countries
            postal_code = billing_address.get('postal_code', '').strip()
            if country == 'US' and postal_code:
                if not postal_code.replace('-', '').isdigit() or len(postal_code.replace('-', '')) not in [5, 9]:
                    errors.append({
                        "field": "billing_address.postal_code",
                        "message": "US postal code must be 5 or 9 digits"
                    })

    # Metadata validation (optional)
    metadata = data.get('metadata', {})
    if metadata:
        if not isinstance(metadata, dict):
            errors.append({
                "field": "metadata",
                "message": "metadata must be an object"
            })
        else:
            # Validate metadata size
            if len(str(metadata)) > 2000:
                errors.append({
                    "field": "metadata",
                    "message": "metadata must not exceed 2000 characters"
                })

            # Validate metadata keys
            for key in metadata.keys():
                if not isinstance(key, str) or len(key) > 50:
                    errors.append({
                        "field": f"metadata.{key}",
                        "message": "metadata keys must be strings with max 50 characters"
                    })

    if errors:
        raise ValidationException(
            "Validation failed for update payment method request",
            validation_errors=errors
        )

    return {
        'payment_method_id': payment_method_id,
        'set_as_default': set_as_default,
        'billing_address': billing_address,
        'metadata': metadata
    }


def validate_stripe_webhook_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate Stripe webhook request data.

    Args:
        data: Webhook event data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Event ID validation (required)
    event_id = data.get('id', '').strip()
    if not event_id:
        errors.append({
            "field": "id",
            "message": "Event ID is required"
        })
    elif not event_id.startswith('evt_'):
        errors.append({
            "field": "id",
            "message": "Event ID must start with 'evt_'"
        })

    # Event type validation (required)
    event_type = data.get('type', '').strip()
    if not event_type:
        errors.append({
            "field": "type",
            "message": "Event type is required"
        })
    else:
        # Validate supported event types
        supported_events = [
            'customer.subscription.created',
            'customer.subscription.updated',
            'customer.subscription.deleted',
            'invoice.payment_succeeded',
            'invoice.payment_failed',
            'customer.subscription.trial_will_end',
            'payment_method.attached',
            'payment_method.detached'
        ]
        if event_type not in supported_events:
            errors.append({
                "field": "type",
                "message": f"Unsupported event type. Supported: {', '.join(supported_events)}"
            })

    # Data validation (required)
    event_data = data.get('data')
    if not event_data:
        errors.append({
            "field": "data",
            "message": "Event data is required"
        })
    elif not isinstance(event_data, dict):
        errors.append({
            "field": "data",
            "message": "Event data must be an object"
        })
    else:
        # Validate data.object exists
        if 'object' not in event_data:
            errors.append({
                "field": "data.object",
                "message": "Event data object is required"
            })

    # API version validation (optional)
    api_version = data.get('api_version')
    if api_version and not isinstance(api_version, str):
        errors.append({
            "field": "api_version",
            "message": "API version must be a string"
        })

    # Created timestamp validation (optional)
    created = data.get('created')
    if created and not isinstance(created, int):
        errors.append({
            "field": "created",
            "message": "Created timestamp must be an integer"
        })

    if errors:
        raise ValidationException(
            "Validation failed for Stripe webhook request",
            validation_errors=errors
        )

    return {
        'id': event_id,
        'type': event_type,
        'data': event_data,
        'api_version': api_version,
        'created': created
    }


def validate_apply_coupon_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate apply coupon request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Coupon code validation (required)
    coupon_code = data.get('coupon_code', '').strip().upper()
    if not coupon_code:
        errors.append({
            "field": "coupon_code",
            "message": "Coupon code is required"
        })
    elif len(coupon_code) < 3:
        errors.append({
            "field": "coupon_code",
            "message": "Coupon code must be at least 3 characters"
        })
    elif len(coupon_code) > 50:
        errors.append({
            "field": "coupon_code",
            "message": "Coupon code must not exceed 50 characters"
        })
    elif not coupon_code.replace('-', '').replace('_', '').isalnum():
        errors.append({
            "field": "coupon_code",
            "message": "Coupon code can only contain letters, numbers, hyphens, and underscores"
        })

    # Subscription ID validation (optional)
    subscription_id = data.get('subscription_id', '').strip()
    if subscription_id:
        if not subscription_id.startswith('sub_'):
            errors.append({
                "field": "subscription_id",
                "message": "Subscription ID must start with 'sub_'"
            })
        elif len(subscription_id) < 10:
            errors.append({
                "field": "subscription_id",
                "message": "Subscription ID must be at least 10 characters"
            })

    # Apply to validation (optional, defaults to 'subscription')
    apply_to = data.get('apply_to', 'subscription').strip().lower()
    valid_apply_to_values = ['subscription', 'next_invoice', 'current_invoice']
    if apply_to not in valid_apply_to_values:
        errors.append({
            "field": "apply_to",
            "message": f"apply_to must be one of: {', '.join(valid_apply_to_values)}"
        })

    # Validate only flag (optional, defaults to False)
    validate_only = data.get('validate_only', False)
    if not isinstance(validate_only, bool):
        errors.append({
            "field": "validate_only",
            "message": "validate_only must be a boolean value"
        })

    # Customer email validation (optional, for guest coupons)
    customer_email = data.get('customer_email', '').strip()
    if customer_email:
        try:
            from shared.validators import validate_email_address
            validate_email_address(customer_email)
        except Exception:
            errors.append({
                "field": "customer_email",
                "message": "Invalid email format"
            })

    # Promo context validation (optional)
    promo_context = data.get('promo_context', {})
    if promo_context and not isinstance(promo_context, dict):
        errors.append({
            "field": "promo_context",
            "message": "promo_context must be an object"
        })

    if errors:
        raise ValidationException(
            "Validation failed for apply coupon request",
            validation_errors=errors
        )

    return {
        'coupon_code': coupon_code,
        'subscription_id': subscription_id if subscription_id else None,
        'apply_to': apply_to,
        'validate_only': validate_only,
        'customer_email': customer_email if customer_email else None,
        'promo_context': promo_context
    }


def validate_process_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate process subscription request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Tenant ID validation (required)
    tenant_id = data.get('tenant_id', '').strip()
    if not tenant_id:
        errors.append({
            "field": "tenant_id",
            "message": "Tenant ID is required"
        })
    elif len(tenant_id) < 3:
        errors.append({
            "field": "tenant_id",
            "message": "Tenant ID must be at least 3 characters"
        })

    # Plan ID validation (required)
    plan_id = data.get('plan_id', '').strip().lower()
    if not plan_id:
        errors.append({
            "field": "plan_id",
            "message": "Plan ID is required"
        })
    else:
        valid_plans = ['free', 'pro', 'enterprise']
        if plan_id not in valid_plans:
            errors.append({
                "field": "plan_id",
                "message": f"Plan ID must be one of: {', '.join(valid_plans)}"
            })

    # Payment method validation (required for paid plans)
    payment_method = data.get('payment_method')
    if not payment_method:
        if plan_id != 'free':
            errors.append({
                "field": "payment_method",
                "message": "Payment method is required for paid plans"
            })
    else:
        if not isinstance(payment_method, dict):
            errors.append({
                "field": "payment_method",
                "message": "Payment method must be an object"
            })
        else:
            # Validate payment method type
            payment_type = payment_method.get('type', '').strip().lower()
            if not payment_type:
                errors.append({
                    "field": "payment_method.type",
                    "message": "Payment method type is required"
                })
            elif payment_type not in ['card', 'bank_account', 'paypal']:
                errors.append({
                    "field": "payment_method.type",
                    "message": "Payment method type must be 'card', 'bank_account', or 'paypal'"
                })

            # Validate card details if type is card
            if payment_type == 'card':
                card_number = payment_method.get('card_number', '').replace(' ', '').replace('-', '')
                if not card_number:
                    errors.append({
                        "field": "payment_method.card_number",
                        "message": "Card number is required"
                    })
                elif not card_number.isdigit() or len(card_number) < 13 or len(card_number) > 19:
                    errors.append({
                        "field": "payment_method.card_number",
                        "message": "Card number must be 13-19 digits"
                    })

                exp_month = payment_method.get('exp_month')
                if not exp_month:
                    errors.append({
                        "field": "payment_method.exp_month",
                        "message": "Expiration month is required"
                    })
                elif not isinstance(exp_month, int) or exp_month < 1 or exp_month > 12:
                    errors.append({
                        "field": "payment_method.exp_month",
                        "message": "Expiration month must be between 1 and 12"
                    })

                exp_year = payment_method.get('exp_year')
                if not exp_year:
                    errors.append({
                        "field": "payment_method.exp_year",
                        "message": "Expiration year is required"
                    })
                elif not isinstance(exp_year, int) or exp_year < 2024:
                    errors.append({
                        "field": "payment_method.exp_year",
                        "message": "Expiration year must be 2024 or later"
                    })

                cvc = payment_method.get('cvc', '').strip()
                if not cvc:
                    errors.append({
                        "field": "payment_method.cvc",
                        "message": "CVC is required"
                    })
                elif not cvc.isdigit() or len(cvc) < 3 or len(cvc) > 4:
                    errors.append({
                        "field": "payment_method.cvc",
                        "message": "CVC must be 3-4 digits"
                    })

    # Billing interval validation (optional, defaults to 'monthly')
    billing_interval = data.get('billing_interval', 'monthly').strip().lower()
    valid_intervals = ['monthly', 'yearly', 'quarterly']
    if billing_interval not in valid_intervals:
        errors.append({
            "field": "billing_interval",
            "message": f"Billing interval must be one of: {', '.join(valid_intervals)}"
        })

    # Trial days validation (optional, defaults to 0)
    trial_days = data.get('trial_days', 0)
    if not isinstance(trial_days, int) or trial_days < 0 or trial_days > 365:
        errors.append({
            "field": "trial_days",
            "message": "Trial days must be an integer between 0 and 365"
        })

    # Coupon code validation (optional)
    coupon_code = data.get('coupon_code', '').strip().upper()
    if coupon_code:
        if len(coupon_code) < 3 or len(coupon_code) > 50:
            errors.append({
                "field": "coupon_code",
                "message": "Coupon code must be between 3 and 50 characters"
            })
        elif not coupon_code.replace('-', '').replace('_', '').isalnum():
            errors.append({
                "field": "coupon_code",
                "message": "Coupon code can only contain letters, numbers, hyphens, and underscores"
            })

    if errors:
        raise ValidationException(
            "Validation failed for process subscription request",
            validation_errors=errors
        )

    return {
        'tenant_id': tenant_id,
        'plan_id': plan_id,
        'payment_method': payment_method,
        'billing_interval': billing_interval,
        'trial_days': trial_days,
        'coupon_code': coupon_code if coupon_code else None
    }


def validate_create_customer_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate create customer request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # Billing email validation (optional but recommended)
    billing_email = data.get('billing_email', '').strip()
    if billing_email:
        try:
            from shared.validators import validate_email_address
            validate_email_address(billing_email)
        except Exception:
            errors.append({
                "field": "billing_email",
                "message": "Invalid email format"
            })

    # Company name validation (optional)
    company_name = data.get('company_name', '').strip()
    if company_name and len(company_name) < 2:
        errors.append({
            "field": "company_name",
            "message": "Company name must be at least 2 characters"
        })

    # Phone validation (optional)
    phone = data.get('phone', '').strip()
    if phone:
        # Basic phone validation - remove spaces, dashes, parentheses
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        if not clean_phone.startswith('+'):
            clean_phone = '+' + clean_phone
        if len(clean_phone) < 10 or len(clean_phone) > 20:
            errors.append({
                "field": "phone",
                "message": "Phone number must be between 10 and 20 characters"
            })

    # Address validation (optional)
    address = data.get('address')
    if address:
        if not isinstance(address, dict):
            errors.append({
                "field": "address",
                "message": "Address must be an object"
            })
        else:
            # Validate required address fields
            required_fields = ['line1', 'city', 'country']
            for field in required_fields:
                if not address.get(field, '').strip():
                    errors.append({
                        "field": f"address.{field}",
                        "message": f"{field} is required in address"
                    })

            # Validate country code
            country = address.get('country', '').strip().upper()
            if country and len(country) != 2:
                errors.append({
                    "field": "address.country",
                    "message": "Country must be a 2-letter ISO code"
                })

    # Tax ID validation (optional)
    tax_id = data.get('tax_id', '').strip()
    if tax_id and len(tax_id) < 5:
        errors.append({
            "field": "tax_id",
            "message": "Tax ID must be at least 5 characters"
        })

    if errors:
        raise ValidationException(
            "Validation failed for create customer request",
            validation_errors=errors
        )

    return {
        'billing_email': billing_email if billing_email else None,
        'company_name': company_name if company_name else None,
        'phone': phone if phone else None,
        'address': address,
        'tax_id': tax_id if tax_id else None
    }


def validate_update_customer_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate update customer request data.

    Args:
        data: Request data to validate

    Returns:
        Validated and cleaned data

    Raises:
        ValidationException: If validation fails
    """
    errors = []

    # At least one field must be provided for update
    updatable_fields = ['billing_email', 'company_name', 'phone', 'address', 'tax_id']
    if not any(data.get(field) for field in updatable_fields):
        errors.append({
            "field": "general",
            "message": "At least one field must be provided for update"
        })

    # Billing email validation (optional)
    billing_email = data.get('billing_email', '').strip().lower()
    if billing_email:
        try:
            from shared.validators import validate_email_address
            validate_email_address(billing_email)
        except Exception:
            errors.append({
                "field": "billing_email",
                "message": "Invalid email format"
            })

    # Company name validation (optional)
    company_name = data.get('company_name', '').strip()
    if company_name and len(company_name) < 2:
        errors.append({
            "field": "company_name",
            "message": "Company name must be at least 2 characters"
        })

    # Phone validation (optional)
    phone = data.get('phone', '').strip()
    if phone:
        # Basic phone validation
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        if not clean_phone.startswith('+'):
            clean_phone = '+' + clean_phone
        if len(clean_phone) < 10 or len(clean_phone) > 20:
            errors.append({
                "field": "phone",
                "message": "Phone number must be between 10 and 20 characters"
            })

    # Address validation (optional)
    address = data.get('address')
    if address:
        if not isinstance(address, dict):
            errors.append({
                "field": "address",
                "message": "Address must be an object"
            })
        else:
            # Validate required address fields if provided
            if address.get('line1') and not address.get('line1').strip():
                errors.append({
                    "field": "address.line1",
                    "message": "Address line1 cannot be empty"
                })

            if address.get('city') and not address.get('city').strip():
                errors.append({
                    "field": "address.city",
                    "message": "City cannot be empty"
                })

            # Validate country code if provided
            country = address.get('country', '').strip().upper()
            if country and len(country) != 2:
                errors.append({
                    "field": "address.country",
                    "message": "Country must be a 2-letter ISO code"
                })

    # Tax ID validation (optional)
    tax_id = data.get('tax_id', '').strip()
    if tax_id and len(tax_id) < 5:
        errors.append({
            "field": "tax_id",
            "message": "Tax ID must be at least 5 characters"
        })

    if errors:
        raise ValidationException(
            "Validation failed for update customer request",
            validation_errors=errors
        )

    return {
        'billing_email': billing_email if billing_email else None,
        'company_name': company_name if company_name else None,
        'phone': phone if phone else None,
        'address': address,
        'tax_id': tax_id if tax_id else None
    }
