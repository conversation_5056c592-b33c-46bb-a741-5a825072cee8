#!/usr/bin/env python3
# services/payment/src/validators/unified_payment_validators.py
# Unified payment validators using shared layer patterns

"""
Unified payment validators that use shared layer validation patterns.
Replaces duplicated validation logic with centralized, consistent validators.
"""

import re
from typing import Dict, Any, <PERSON>, Tuple
from decimal import Decimal

from shared.validators import UnifiedUserValidator, UnifiedRequestValidator
from shared.models import BillingInterval, UserRole
from shared.exceptions import ValidationException


class UnifiedPaymentValidator:
    """
    Unified payment validation using shared layer patterns.
    Centralizes all payment-related validation logic.
    """

    @staticmethod
    def validate_subscription_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate subscription creation/update data.
        Uses unified patterns from shared layer.
        """
        errors = []

        # Validate required fields
        required_fields = ['tenant_id', 'plan_id']
        for field in required_fields:
            if not data.get(field):
                errors.append(f"{field.replace('_', ' ').title()} is required")

        # Validate tenant_id format
        if data.get('tenant_id'):
            if not re.match(r'^[a-zA-Z0-9\-_]+$', data['tenant_id']):
                errors.append("Invalid tenant ID format")

        # Validate plan_id format
        if data.get('plan_id'):
            if not re.match(r'^[a-zA-Z0-9\-_]+$', data['plan_id']):
                errors.append("Invalid plan ID format")

        # Validate billing interval
        if data.get('billing_interval'):
            try:
                BillingInterval(data['billing_interval'])
            except ValueError:
                valid_intervals = [interval.value for interval in BillingInterval]
                errors.append(f"Invalid billing interval. Must be one of: {', '.join(valid_intervals)}")

        # Validate trial days
        if 'trial_days' in data:
            try:
                trial_days = int(data['trial_days'])
                if trial_days < 0 or trial_days > 365:
                    errors.append("Trial days must be between 0 and 365")
            except (ValueError, TypeError):
                errors.append("Trial days must be a valid number")

        # Validate coupon code format
        if data.get('coupon_code'):
            if not re.match(r'^[A-Z0-9]{3,20}$', data['coupon_code']):
                errors.append("Invalid coupon code format")

        return len(errors) == 0, errors

    @staticmethod
    def validate_payment_method_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate payment method data.
        """
        errors = []

        # Validate payment method type
        if not data.get('type'):
            errors.append("Payment method type is required")
        elif data['type'] not in ['card', 'bank_account', 'sepa_debit']:
            errors.append("Invalid payment method type")

        # Validate card data if type is card
        if data.get('type') == 'card':
            card_data = data.get('card', {})
            
            # Validate card number (basic format check)
            if not card_data.get('number'):
                errors.append("Card number is required")
            elif not re.match(r'^\d{13,19}$', card_data['number'].replace(' ', '')):
                errors.append("Invalid card number format")

            # Validate expiry month
            if not card_data.get('exp_month'):
                errors.append("Expiry month is required")
            else:
                try:
                    month = int(card_data['exp_month'])
                    if month < 1 or month > 12:
                        errors.append("Invalid expiry month")
                except (ValueError, TypeError):
                    errors.append("Expiry month must be a number")

            # Validate expiry year
            if not card_data.get('exp_year'):
                errors.append("Expiry year is required")
            else:
                try:
                    year = int(card_data['exp_year'])
                    current_year = 2025  # Could be dynamic
                    if year < current_year or year > current_year + 20:
                        errors.append("Invalid expiry year")
                except (ValueError, TypeError):
                    errors.append("Expiry year must be a number")

            # Validate CVC
            if not card_data.get('cvc'):
                errors.append("CVC is required")
            elif not re.match(r'^\d{3,4}$', card_data['cvc']):
                errors.append("Invalid CVC format")

        return len(errors) == 0, errors

    @staticmethod
    def validate_amount_and_currency(amount: Any, currency: str) -> Tuple[bool, List[str]]:
        """
        Validate payment amount and currency.
        """
        errors = []

        # Validate amount
        if amount is None:
            errors.append("Amount is required")
        else:
            try:
                amount_decimal = Decimal(str(amount))
                if amount_decimal <= 0:
                    errors.append("Amount must be greater than zero")
                if amount_decimal > Decimal('999999.99'):
                    errors.append("Amount exceeds maximum limit")
            except (ValueError, TypeError):
                errors.append("Invalid amount format")

        # Validate currency
        if not currency:
            errors.append("Currency is required")
        elif not re.match(r'^[A-Z]{3}$', currency.upper()):
            errors.append("Invalid currency format (must be 3-letter ISO code)")
        elif currency.upper() not in ['USD', 'EUR', 'GBP', 'CAD', 'AUD']:
            errors.append("Unsupported currency")

        return len(errors) == 0, errors

    @staticmethod
    def validate_subscription_permissions(user_role: str, action: str) -> bool:
        """
        Validate subscription permissions using unified role hierarchy.
        """
        try:
            role = UserRole(user_role)
            
            # Define permission requirements
            permission_map = {
                'create_subscription': UserRole.ADMIN,
                'update_subscription': UserRole.ADMIN,
                'cancel_subscription': UserRole.MASTER,
                'view_subscription': UserRole.MEMBER,
                'manage_billing': UserRole.ADMIN,
                'view_invoices': UserRole.MEMBER
            }
            
            required_role = permission_map.get(action, UserRole.MASTER)
            return role.can_access(required_role)
            
        except ValueError:
            return False

    @staticmethod
    def validate_webhook_signature(payload: str, signature: str, secret: str) -> bool:
        """
        Validate webhook signature for security.
        """
        import hmac
        import hashlib
        
        try:
            # Extract timestamp and signature from header
            elements = signature.split(',')
            timestamp = None
            signature_hash = None
            
            for element in elements:
                if element.startswith('t='):
                    timestamp = element[2:]
                elif element.startswith('v1='):
                    signature_hash = element[3:]
            
            if not timestamp or not signature_hash:
                return False
            
            # Create expected signature
            signed_payload = f"{timestamp}.{payload}"
            expected_signature = hmac.new(
                secret.encode('utf-8'),
                signed_payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            return hmac.compare_digest(expected_signature, signature_hash)
            
        except Exception:
            return False


def validate_process_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate process subscription request using unified patterns.
    Replacement for old validation function.
    """
    # Use unified request validation
    is_valid, errors = UnifiedPaymentValidator.validate_subscription_data(data)
    if not is_valid:
        raise ValidationException(f"Subscription validation failed: {'; '.join(errors)}")
    
    # Validate payment method if provided
    if data.get('payment_method'):
        pm_valid, pm_errors = UnifiedPaymentValidator.validate_payment_method_data(data['payment_method'])
        if not pm_valid:
            raise ValidationException(f"Payment method validation failed: {'; '.join(pm_errors)}")
    
    return data


def validate_create_subscription_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate create subscription request using unified patterns.
    Replacement for old validation function.
    """
    return validate_process_subscription_request(data)
