# Payment Service Dependencies
# Core dependencies are provided by the shared layer
# Service-specific dependencies that must be available locally

# JWT handling - Required for shared layer compatibility
PyJWT==2.8.0

# Stripe integration - Required for payment processing
stripe==7.0.0

# Date/time utilities - Required for billing operations
python-dateutil==2.8.2

# Cryptography for JWT - Lambda compatible version
cryptography==3.4.8

# HTTP requests for webhook validation
requests==2.31.0

# Email validation for customer data
email-validator==2.1.0
