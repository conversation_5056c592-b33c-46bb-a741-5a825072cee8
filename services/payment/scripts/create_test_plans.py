#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create test plans for payment service development.
Run this script to populate the database with sample plans for testing.
"""

import sys
import os
from decimal import Decimal

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.plan import Plan
from models import PlanType, PlanStatus
from shared.logger import lambda_logger


def create_test_plans():
    """Create test plans for development and testing."""
    
    plans_data = [
        {
            'plan_id': 'plan_free',
            'name': 'Free Plan',
            'plan_type': PlanType.FREE,
            'status': PlanStatus.ACTIVE,
            'monthly_price': Decimal('0.00'),
            'yearly_price': Decimal('0.00'),
            'currency': 'USD',
            'features': [
                'basic_chat',
                'single_agent',
                'email_support',
                'basic_analytics'
            ],
            'limits': {
                'max_users': 1,
                'max_agents': 1,
                'max_conversations_per_month': 100,
                'storage_gb': 0.5,
                'api_calls_per_month': 1000
            },
            'trial_days': 0,
            'description': 'Perfect for individuals getting started with AI agents'
        },
        {
            'plan_id': 'plan_pro',
            'name': 'Pro Plan',
            'plan_type': PlanType.PRO,
            'status': PlanStatus.ACTIVE,
            'monthly_price': Decimal('29.00'),
            'yearly_price': Decimal('290.00'),
            'currency': 'USD',
            'features': [
                'advanced_chat',
                'multiple_agents',
                'priority_support',
                'advanced_analytics',
                'api_access',
                'integrations',
                'custom_workflows'
            ],
            'limits': {
                'max_users': 5,
                'max_agents': 10,
                'max_conversations_per_month': 10000,
                'storage_gb': 50,
                'api_calls_per_month': 100000
            },
            'trial_days': 14,
            'description': 'Great for small teams and growing businesses'
        },
        {
            'plan_id': 'plan_enterprise',
            'name': 'Enterprise Plan',
            'plan_type': PlanType.ENTERPRISE,
            'status': PlanStatus.ACTIVE,
            'monthly_price': Decimal('299.00'),
            'yearly_price': Decimal('2990.00'),
            'currency': 'USD',
            'features': [
                'enterprise_chat',
                'unlimited_agents',
                'dedicated_support',
                'enterprise_analytics',
                'full_api_access',
                'all_integrations',
                'sso',
                'priority_support',
                'custom_branding',
                'advanced_security',
                'compliance_tools'
            ],
            'limits': {
                'max_users': 1000,
                'max_agents': -1,  # Unlimited
                'max_conversations_per_month': -1,  # Unlimited
                'storage_gb': 500,
                'api_calls_per_month': 1000000
            },
            'trial_days': 30,
            'description': 'For large organizations with advanced needs'
        }
    ]
    
    created_plans = []
    
    for plan_data in plans_data:
        try:
            # Check if plan already exists
            existing_plan = Plan.get_by_id(plan_data['plan_id'])
            if existing_plan:
                lambda_logger.info(f"Plan {plan_data['plan_id']} already exists, skipping...")
                continue
            
            # Create new plan
            plan = Plan(**plan_data)
            plan.save()
            
            created_plans.append(plan)
            lambda_logger.info(f"Created plan: {plan.plan_id} - {plan.name}")
            
        except Exception as e:
            lambda_logger.error(f"Failed to create plan {plan_data['plan_id']}: {str(e)}")
            continue
    
    return created_plans


def main():
    """Main function to create test plans."""
    try:
        lambda_logger.info("Creating test plans for payment service...")
        
        created_plans = create_test_plans()
        
        if created_plans:
            lambda_logger.info(f"Successfully created {len(created_plans)} test plans:")
            for plan in created_plans:
                lambda_logger.info(f"  - {plan.plan_id}: {plan.name} (${plan.monthly_price}/month)")
        else:
            lambda_logger.info("No new plans were created (they may already exist)")
        
        lambda_logger.info("Test plan creation completed!")
        
    except Exception as e:
        lambda_logger.error(f"Failed to create test plans: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
