#!/usr/bin/env python3
"""
Test script to verify all imports are working correctly.
"""

import sys
import os

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_imports():
    """Test all model imports."""
    try:
        print("🧪 Testing model imports...")
        
        # Test basic model imports
        from models import (
            Subscription, Plan, Customer, Invoice, PaymentMethod,
            InvoiceStatus, InvoiceType, PaymentMethodType, PaymentMethodStatus
        )
        print("✅ Basic model imports successful")
        
        # Test shared model imports
        from models import (
            SubscriptionInfo, SubscriptionStatus, BillingInterval,
            PlanInfo, PlanStatus, PlanType,
            CustomerInfo, CustomerStatus
        )
        print("✅ Shared model imports successful")
        
        # Test service imports
        from services.subscription_service import SubscriptionService, ISubscriptionService
        print("✅ Service imports successful")

        # Test service instantiation
        service = SubscriptionService()
        print("✅ SubscriptionService instantiation successful")

        # Test that get_plan_details method exists
        assert hasattr(service, 'get_plan_details'), "get_plan_details method not found"
        print("✅ get_plan_details method exists")
        
        # Test enum values
        print(f"✅ PaymentMethodType.CARD = {PaymentMethodType.CARD.value}")
        print(f"✅ PaymentMethodStatus.ACTIVE = {PaymentMethodStatus.ACTIVE.value}")
        print(f"✅ InvoiceStatus.PAID = {InvoiceStatus.PAID.value}")
        print(f"✅ InvoiceType.SUBSCRIPTION = {InvoiceType.SUBSCRIPTION.value}")
        
        # Test model instantiation
        payment_method = PaymentMethod(
            tenant_id="test_tenant",
            payment_method_type=PaymentMethodType.CARD
        )
        print(f"✅ PaymentMethod instantiation successful: {payment_method.payment_method_id}")
        
        invoice = Invoice(
            tenant_id="test_tenant",
            subscription_id="test_sub",
            amount_due=29.99
        )
        print(f"✅ Invoice instantiation successful: {invoice.invoice_id}")
        
        print("\n🎉 All imports and instantiations successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Main function."""
    print("🚀 Testing Payment Service Imports")
    print("=" * 40)
    
    success = test_imports()
    
    if success:
        print("\n✅ All tests passed! Payment service imports are working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
