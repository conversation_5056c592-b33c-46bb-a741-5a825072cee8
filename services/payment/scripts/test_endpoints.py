#!/usr/bin/env python3
"""
End-to-end testing script for payment service endpoints.
This script tests all payment endpoints with real data.
"""

import json
import requests
import sys
import os
from typing import Dict, Any, Optional

# Configuration
API_BASE_URL = os.getenv('API_BASE_URL', 'https://your-api-gateway-url.amazonaws.com/dev')
TEST_EMAIL = os.getenv('TEST_EMAIL', '<EMAIL>')
TEST_PASSWORD = os.getenv('TEST_PASSWORD', 'TestPassword123!')


class PaymentEndpointTester:
    """Test class for payment service endpoints."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.auth_token = None
        self.tenant_id = None
        self.user_id = None
        self.session = requests.Session()
        
    def authenticate(self, email: str, password: str) -> bool:
        """Authenticate and get access token."""
        try:
            # First, try to create user (in case it doesn't exist)
            self.create_test_user(email, password)
            
            # Login to get token
            login_url = f"{self.base_url}/auth/login"
            login_data = {
                "email": email,
                "password": password
            }
            
            response = self.session.post(login_url, json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('access_token')
                self.tenant_id = data.get('user', {}).get('tenant_id')
                self.user_id = data.get('user', {}).get('user_id')
                
                # Set authorization header for future requests
                self.session.headers.update({
                    'Authorization': f'Bearer {self.auth_token}',
                    'Content-Type': 'application/json'
                })
                
                print(f"✅ Authentication successful")
                print(f"   User ID: {self.user_id}")
                print(f"   Tenant ID: {self.tenant_id}")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False
    
    def create_test_user(self, email: str, password: str):
        """Create test user if it doesn't exist."""
        try:
            register_url = f"{self.base_url}/auth/register"
            register_data = {
                "email": email,
                "password": password,
                "first_name": "Test",
                "last_name": "User",
                "company_name": "Test Company"
            }
            
            response = requests.post(register_url, json=register_data)
            if response.status_code in [200, 201]:
                print(f"✅ Test user created or already exists")
            else:
                print(f"ℹ️ User creation response: {response.status_code}")
                
        except Exception as e:
            print(f"ℹ️ User creation note: {str(e)}")
    
    def test_list_plans(self) -> bool:
        """Test list available plans endpoint."""
        try:
            print("\n🧪 Testing: List Available Plans")
            
            url = f"{self.base_url}/payment/plans"
            params = {
                'active_only': 'true',
                'limit': 10
            }
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                plans = data.get('plans', [])
                print(f"✅ List plans successful - Found {len(plans)} plans")
                
                for plan in plans[:3]:  # Show first 3 plans
                    print(f"   - {plan.get('name')}: ${plan.get('monthly_price', 0)}/month")
                
                return True
            else:
                print(f"❌ List plans failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ List plans error: {str(e)}")
            return False
    
    def test_create_subscription(self, plan_id: str = 'plan_pro') -> Optional[str]:
        """Test create subscription endpoint."""
        try:
            print(f"\n🧪 Testing: Create Subscription (Plan: {plan_id})")
            
            url = f"{self.base_url}/payment/subscriptions"
            data = {
                "plan_id": plan_id,
                "billing_interval": "MONTHLY"
            }
            
            response = self.session.post(url, json=data)
            
            if response.status_code in [200, 201]:
                result = response.json()
                subscription = result.get('subscription', {})
                subscription_id = subscription.get('subscription_id')
                
                print(f"✅ Create subscription successful")
                print(f"   Subscription ID: {subscription_id}")
                print(f"   Status: {subscription.get('status')}")
                print(f"   Amount: ${subscription.get('amount', 0)}")
                
                return subscription_id
            else:
                print(f"❌ Create subscription failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Create subscription error: {str(e)}")
            return None
    
    def test_get_subscription(self, subscription_id: str) -> bool:
        """Test get subscription endpoint."""
        try:
            print(f"\n🧪 Testing: Get Subscription ({subscription_id})")
            
            url = f"{self.base_url}/payment/subscriptions/{subscription_id}"
            
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                subscription = data.get('subscription', {})
                
                print(f"✅ Get subscription successful")
                print(f"   Status: {subscription.get('status')}")
                print(f"   Plan: {subscription.get('plan_id')}")
                print(f"   Next billing: {subscription.get('next_billing_date')}")
                
                return True
            else:
                print(f"❌ Get subscription failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Get subscription error: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all endpoint tests."""
        results = {}
        
        print("🚀 Starting Payment Service End-to-End Tests")
        print("=" * 50)
        
        # Authenticate
        if not self.authenticate(TEST_EMAIL, TEST_PASSWORD):
            print("❌ Authentication failed - cannot continue tests")
            return {'authentication': False}
        
        results['authentication'] = True
        
        # Test list plans
        results['list_plans'] = self.test_list_plans()
        
        # Test create subscription
        subscription_id = self.test_create_subscription()
        results['create_subscription'] = subscription_id is not None
        
        # Test get subscription (if create was successful)
        if subscription_id:
            results['get_subscription'] = self.test_get_subscription(subscription_id)
        else:
            results['get_subscription'] = False
        
        return results
    
    def print_summary(self, results: Dict[str, bool]):
        """Print test results summary."""
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All tests passed! Payment service is working correctly.")
        else:
            print("⚠️ Some tests failed. Check the logs above for details.")


def main():
    """Main function to run tests."""
    if not API_BASE_URL or API_BASE_URL == 'https://your-api-gateway-url.amazonaws.com/dev':
        print("❌ Please set the API_BASE_URL environment variable")
        print("   Example: export API_BASE_URL=https://your-api-gateway.amazonaws.com/dev")
        sys.exit(1)
    
    tester = PaymentEndpointTester(API_BASE_URL)
    results = tester.run_all_tests()
    tester.print_summary(results)
    
    # Exit with error code if any tests failed
    if not all(results.values()):
        sys.exit(1)


if __name__ == "__main__":
    main()
