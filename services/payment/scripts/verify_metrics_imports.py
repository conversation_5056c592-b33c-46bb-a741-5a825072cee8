#!/usr/bin/env python3
"""
Script to verify that all handlers have correct metrics_manager imports.
"""

import os
import sys
import re
from pathlib import Path

def check_metrics_imports():
    """Check all handlers for correct metrics_manager imports."""
    
    handlers_dir = Path(__file__).parent.parent / "src" / "handlers"
    
    if not handlers_dir.exists():
        print(f"❌ Handlers directory not found: {handlers_dir}")
        return False
    
    print("🔍 Checking metrics_manager imports in payment handlers...")
    print("=" * 60)
    
    issues_found = []
    handlers_checked = 0
    
    for handler_file in handlers_dir.glob("*.py"):
        if handler_file.name.startswith("__"):
            continue
            
        handlers_checked += 1
        
        try:
            with open(handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if handler uses metrics_manager
            uses_metrics_manager = "metrics_manager" in content
            
            if uses_metrics_manager:
                # Check if it imports metrics_manager
                has_import = re.search(r'from shared\.metrics import.*metrics_manager', content)
                
                if not has_import:
                    issues_found.append({
                        'file': handler_file.name,
                        'issue': 'Uses metrics_manager but does not import it'
                    })
                    print(f"❌ {handler_file.name}: Uses metrics_manager but missing import")
                else:
                    print(f"✅ {handler_file.name}: Correct metrics_manager import")
            else:
                print(f"ℹ️  {handler_file.name}: Does not use metrics_manager")
                
        except Exception as e:
            issues_found.append({
                'file': handler_file.name,
                'issue': f'Error reading file: {str(e)}'
            })
            print(f"❌ {handler_file.name}: Error reading file - {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 SUMMARY:")
    print(f"   Handlers checked: {handlers_checked}")
    print(f"   Issues found: {len(issues_found)}")
    
    if issues_found:
        print("\n🚨 ISSUES FOUND:")
        for issue in issues_found:
            print(f"   - {issue['file']}: {issue['issue']}")
        return False
    else:
        print("\n🎉 All handlers have correct metrics_manager imports!")
        return True

def check_specific_handlers():
    """Check specific handlers that were recently fixed."""
    
    print("\n🔧 Checking recently fixed handlers...")
    print("-" * 40)
    
    handlers_to_check = [
        "pause_subscription.py",
        "resume_subscription.py"
    ]
    
    handlers_dir = Path(__file__).parent.parent / "src" / "handlers"
    all_good = True
    
    for handler_name in handlers_to_check:
        handler_file = handlers_dir / handler_name
        
        if not handler_file.exists():
            print(f"❌ {handler_name}: File not found")
            all_good = False
            continue
        
        try:
            with open(handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for correct import
            has_correct_import = re.search(r'from shared\.metrics import.*metrics_manager', content)
            uses_metrics_manager = "metrics_manager.business.record_api_usage" in content
            
            if uses_metrics_manager and has_correct_import:
                print(f"✅ {handler_name}: Fixed correctly")
            elif uses_metrics_manager and not has_correct_import:
                print(f"❌ {handler_name}: Still missing import")
                all_good = False
            elif not uses_metrics_manager:
                print(f"ℹ️  {handler_name}: Does not use metrics_manager")
            else:
                print(f"✅ {handler_name}: Import present")
                
        except Exception as e:
            print(f"❌ {handler_name}: Error - {str(e)}")
            all_good = False
    
    return all_good

def main():
    """Main function."""
    print("🚀 Payment Service Metrics Import Verification")
    print("=" * 60)
    
    # Check all handlers
    all_imports_correct = check_metrics_imports()
    
    # Check specific recently fixed handlers
    specific_handlers_correct = check_specific_handlers()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULT:")
    
    if all_imports_correct and specific_handlers_correct:
        print("✅ All metrics_manager imports are correct!")
        print("✅ Payment service handlers are ready for deployment!")
        sys.exit(0)
    else:
        print("❌ Some issues found with metrics_manager imports.")
        print("❌ Please fix the issues above before deployment.")
        sys.exit(1)

if __name__ == "__main__":
    main()
