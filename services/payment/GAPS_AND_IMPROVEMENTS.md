# 💳 Payment Service - Gaps and Improvements

## 📊 **Current Status: 9.0/10 - EXCELENTE**

### **Completitud:** 100% funcional, mejoras menores identificadas

---

## 🎯 **Gaps Identificados**

### **1. MINOR GAPS (5%)**

#### **1.1 Missing Advanced Payment Endpoints**
**Priority:** Medium  
**Effort:** 3-4 days  
**Impact:** Feature enhancement

**Current State:**
- Basic subscription management exists
- Missing payment method management
- Missing invoice management
- Missing payment analytics

**Required Endpoints:**

```python
# src/handlers/payment_methods.py
"""Payment methods management handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@payment_resilience("payment_methods")
@measure_performance("payment_methods")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage payment methods.
    
    GET /payment/methods
    POST /payment/methods
    DELETE /payment/methods/{method_id}
    PUT /payment/methods/{method_id}/default
    """
    pass

# src/handlers/invoices.py
"""Invoice management handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@payment_resilience("invoices")
@measure_performance("payment_invoices")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage invoices.
    
    GET /payment/invoices
    GET /payment/invoices/{invoice_id}
    GET /payment/invoices/{invoice_id}/pdf
    POST /payment/invoices/{invoice_id}/pay
    """
    pass

# src/handlers/payment_analytics.py
"""Payment analytics handler."""

@require_auth
@rate_limit(requests_per_minute=30)
@payment_resilience("payment_analytics")
@measure_performance("payment_analytics")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get payment analytics.
    
    GET /payment/analytics
    GET /payment/analytics/revenue
    GET /payment/analytics/usage
    """
    pass

# src/handlers/billing_portal.py
"""Billing portal handler."""

@require_auth
@rate_limit(requests_per_minute=20)
@payment_resilience("billing_portal")
@measure_performance("payment_billing_portal")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Create billing portal session.
    
    POST /payment/billing-portal
    """
    pass

# src/handlers/usage_tracking.py
"""Usage tracking handler."""

@require_auth
@rate_limit(requests_per_minute=60)
@payment_resilience("usage_tracking")
@measure_performance("payment_usage_tracking")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Track and report usage.
    
    POST /payment/usage
    GET /payment/usage/current
    GET /payment/usage/history
    """
    pass
```

**Serverless.yml Updates Required:**
```yaml
functions:
  # Existing functions...
  
  paymentMethods:
    handler: src/handlers/payment_methods.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/methods
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/methods
          method: post
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/methods/{method_id}
          method: delete
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/methods/{method_id}/default
          method: put
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
  
  invoices:
    handler: src/handlers/invoices.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/invoices
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/invoices/{invoice_id}
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/invoices/{invoice_id}/pdf
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/invoices/{invoice_id}/pay
          method: post
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
  
  paymentAnalytics:
    handler: src/handlers/payment_analytics.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/analytics
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/analytics/revenue
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/analytics/usage
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
  
  billingPortal:
    handler: src/handlers/billing_portal.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/billing-portal
          method: post
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
  
  usageTracking:
    handler: src/handlers/usage_tracking.handler
    timeout: 30
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/usage
          method: post
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/usage/current
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
      - http:
          path: /payment/usage/history
          method: get
          cors: true
          authorizer:
            name: ${self:custom.authorizerArn}
```

#### **1.2 Real Stripe Integration Completion**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Production readiness

**Current State:**
- Some services use mock implementations
- Missing real webhook validation
- Limited error handling for edge cases

**Required Implementation:**

```python
# src/services/real_stripe_service.py
"""Complete Stripe integration service."""

import stripe
import hmac
import hashlib
from typing import Dict, Any, Optional
from shared.logger import lambda_logger
from shared.exceptions import PaymentException, ValidationException

class RealStripeService:
    """Complete Stripe service implementation."""
    
    def __init__(self):
        stripe.api_key = os.environ.get('STRIPE_SECRET_KEY')
        self.webhook_secret = os.environ.get('STRIPE_WEBHOOK_SECRET')
    
    def validate_webhook_signature(self, payload: str, signature: str) -> bool:
        """Validate Stripe webhook signature."""
        try:
            stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            return True
        except ValueError:
            lambda_logger.error("Invalid payload in webhook")
            return False
        except stripe.error.SignatureVerificationError:
            lambda_logger.error("Invalid signature in webhook")
            return False
    
    async def create_payment_method(
        self, 
        customer_id: str, 
        payment_method_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create and attach payment method to customer."""
        try:
            # Create payment method
            payment_method = stripe.PaymentMethod.create(
                type=payment_method_data['type'],
                card=payment_method_data.get('card'),
                billing_details=payment_method_data.get('billing_details')
            )
            
            # Attach to customer
            payment_method.attach(customer=customer_id)
            
            return {
                'id': payment_method.id,
                'type': payment_method.type,
                'card': payment_method.card,
                'billing_details': payment_method.billing_details
            }
            
        except stripe.error.StripeError as e:
            lambda_logger.error(f"Stripe error creating payment method: {str(e)}")
            raise PaymentException(f"Failed to create payment method: {str(e)}")
    
    async def generate_invoice_pdf(self, invoice_id: str) -> bytes:
        """Generate PDF for invoice."""
        try:
            invoice = stripe.Invoice.retrieve(invoice_id)
            
            # Get invoice PDF URL
            pdf_url = invoice.invoice_pdf
            
            # Download PDF content
            import requests
            response = requests.get(pdf_url)
            response.raise_for_status()
            
            return response.content
            
        except stripe.error.StripeError as e:
            lambda_logger.error(f"Stripe error generating PDF: {str(e)}")
            raise PaymentException(f"Failed to generate invoice PDF: {str(e)}")
```
