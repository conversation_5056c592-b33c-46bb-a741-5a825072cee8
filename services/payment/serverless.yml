# services/payment/serverless.yml
# Payment service configuration

service: agent-scl-payment

# Custom configuration
custom:
  serviceName: payment
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName

  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"

# Functions
functions:
  # Registration flow subscription processing (no auth required)
  processSubscription:
    handler: src.handlers.process_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/process-subscription
          method: post
          cors: true
    environment:
      FUNCTION_NAME: payment_process_subscription
    tracing: Active

  # Subscription management
  createSubscription:
    handler: src.handlers.create_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/subscriptions
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_create_subscription
    tracing: Active

  getSubscription:
    handler: src.handlers.get_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/subscriptions/{id}
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_get_subscription
    tracing: Active

  updateSubscription:
    handler: src.handlers.update_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/subscriptions/{id}
          method: put
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_update_subscription
    tracing: Active

  cancelSubscription:
    handler: src.handlers.cancel_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/subscriptions/{id}/cancel
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_cancel_subscription
    tracing: Active

  # Billing and invoices
  getBillingHistory:
    handler: src.handlers.get_billing_history.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/billing/history
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_billing_history
    tracing: Active

  # Stripe webhook (no auth required)
  stripeWebhook:
    handler: src.handlers.stripe_webhook.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/webhooks/stripe
          method: post
          cors: false
    environment:
      FUNCTION_NAME: payment_stripe_webhook
    tracing: Active

  # Additional payment functions
  listPlans:
    handler: src.handlers.list_plans.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/plans
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_list_plans
    tracing: Active

  updatePaymentMethod:
    handler: src.handlers.update_payment_method.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/payment-method
          method: put
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_update_payment_method
    tracing: Active

  payuWebhook:
    handler: src.handlers.payu_webhook.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/payu/webhook
          method: post
          cors: true
          # No authorizer - webhooks use signature verification
    environment:
      FUNCTION_NAME: payment_payu_webhook
    tracing: Active

  pauseSubscription:
    handler: src.handlers.pause_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/subscriptions/{id}/pause
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_pause_subscription
    tracing: Active

  resumeSubscription:
    handler: src.handlers.resume_subscription.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/subscriptions/{id}/resume
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_resume_subscription
    tracing: Active

  applyCoupon:
    handler: src.handlers.apply_coupon.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/coupons/apply
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_apply_coupon
    tracing: Active

  downloadInvoice:
    handler: src.handlers.download_invoice.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/invoices/{id}/download
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_download_invoice
    tracing: Active

  # Customer management functions
  getCustomer:
    handler: src.handlers.customer_handlers.get_customer_handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/customer
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_get_customer
    tracing: Active

  createCustomer:
    handler: src.handlers.customer_handlers.create_customer_handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/customer
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_create_customer
    tracing: Active

  updateCustomer:
    handler: src.handlers.customer_handlers.update_customer_handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/customer
          method: put
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_update_customer
    tracing: Active

  syncCustomer:
    handler: src.handlers.customer_handlers.sync_customer_handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/customer/sync
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_sync_customer
    tracing: Active

  validateFunds:
    handler: src.handlers.validate_funds.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /payment/validate-funds
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: payment_validate_funds
    tracing: Active

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'

# Plugins
plugins:
  - serverless-python-requirements

# Resources
resources:
  Outputs:
    PaymentServiceEndpoint:
      Description: "Payment Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-payment-${self:custom.stage}-PaymentServiceEndpoint
