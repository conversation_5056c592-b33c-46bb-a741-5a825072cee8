# 🔒 Security Service - Gaps and Improvements

## 📊 **Current Status: 8.7/10 - MUY BUENO**

### **Completitud:** 90% funcional, gaps menores identificados

---

## 🎯 **Gaps Identificados**

### **1. MINOR GAPS (10%)**

#### **1.1 Real Intrusion Detection Implementation**
**Priority:** Medium  
**Effort:** 3-4 days  
**Impact:** Security enhancement

**Current State:**
- Intrusion detection patterns exist
- Missing real-time analysis
- Limited automated response

**Required Implementation:**

```python
# src/services/real_intrusion_detection_service.py
"""Real intrusion detection service implementation."""

import boto3
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from shared.database import db_client
from shared.logger import lambda_logger
from shared.exceptions import SecurityException

class RealIntrusionDetectionService:
    """Real intrusion detection service."""
    
    def __init__(self):
        self.db = db_client
        self.sns_client = boto3.client('sns')
        self.cloudwatch = boto3.client('cloudwatch')
        self.alert_topic_arn = os.environ.get('SECURITY_ALERT_TOPIC_ARN')
        
        # Detection thresholds
        self.thresholds = {
            'failed_login_attempts': 5,
            'rapid_requests': 100,  # requests per minute
            'suspicious_locations': 3,  # different countries in 1 hour
            'unusual_activity_hours': (22, 6),  # 10 PM to 6 AM
            'api_abuse_threshold': 1000  # requests per hour
        }
    
    async def analyze_security_events(
        self, 
        time_window_minutes: int = 60
    ) -> Dict[str, Any]:
        """Analyze recent security events for threats."""
        try:
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(minutes=time_window_minutes)
            
            # Get recent security events
            events = await self._get_security_events(start_time, end_time)
            
            # Analyze for different threat patterns
            threats = {
                'brute_force_attacks': await self._detect_brute_force(events),
                'rate_limit_violations': await self._detect_rate_abuse(events),
                'geo_anomalies': await self._detect_geo_anomalies(events),
                'time_anomalies': await self._detect_time_anomalies(events),
                'api_abuse': await self._detect_api_abuse(events)
            }
            
            # Calculate threat level
            threat_level = self._calculate_threat_level(threats)
            
            # Send alerts if necessary
            if threat_level >= 3:  # High threat
                await self._send_security_alert(threats, threat_level)
            
            # Store analysis results
            await self._store_analysis_results(threats, threat_level)
            
            return {
                'analysis_time': end_time.isoformat(),
                'time_window_minutes': time_window_minutes,
                'threat_level': threat_level,
                'threats_detected': threats,
                'events_analyzed': len(events)
            }
            
        except Exception as e:
            lambda_logger.error(f"Failed to analyze security events: {str(e)}")
            raise SecurityException(f"Security analysis failed: {str(e)}")
    
    async def _get_security_events(
        self, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """Get security events from the specified time window."""
        try:
            start_timestamp = int(start_time.timestamp())
            end_timestamp = int(end_time.timestamp())
            
            # Query security events
            events = await self.db.query_gsi(
                gsi_name='GSI1',
                pk='SECURITY_EVENT',
                sk_between=(f'TIMESTAMP#{start_timestamp}', f'TIMESTAMP#{end_timestamp}')
            )
            
            return events
            
        except Exception as e:
            lambda_logger.error(f"Failed to get security events: {str(e)}")
            return []
    
    async def _detect_brute_force(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect brute force attacks."""
        brute_force_attacks = []
        
        try:
            # Group failed login attempts by IP and user
            failed_attempts = {}
            
            for event in events:
                if event.get('event_type') == 'failed_login':
                    ip_address = event.get('ip_address')
                    user_id = event.get('user_id')
                    key = f"{ip_address}:{user_id}"
                    
                    if key not in failed_attempts:
                        failed_attempts[key] = []
                    failed_attempts[key].append(event)
            
            # Check for threshold violations
            for key, attempts in failed_attempts.items():
                if len(attempts) >= self.thresholds['failed_login_attempts']:
                    ip_address, user_id = key.split(':')
                    brute_force_attacks.append({
                        'type': 'brute_force_attack',
                        'ip_address': ip_address,
                        'user_id': user_id,
                        'attempt_count': len(attempts),
                        'first_attempt': min(a.get('timestamp', 0) for a in attempts),
                        'last_attempt': max(a.get('timestamp', 0) for a in attempts),
                        'severity': 'HIGH' if len(attempts) >= 10 else 'MEDIUM'
                    })
            
            return brute_force_attacks
            
        except Exception as e:
            lambda_logger.error(f"Failed to detect brute force attacks: {str(e)}")
            return []
    
    async def _detect_rate_abuse(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect rate limiting violations."""
        rate_violations = []
        
        try:
            # Group API requests by IP and user
            api_requests = {}
            
            for event in events:
                if event.get('event_type') == 'api_request':
                    ip_address = event.get('ip_address')
                    user_id = event.get('user_id', 'anonymous')
                    key = f"{ip_address}:{user_id}"
                    
                    if key not in api_requests:
                        api_requests[key] = []
                    api_requests[key].append(event)
            
            # Check for rate abuse
            for key, requests in api_requests.items():
                request_count = len(requests)
                if request_count >= self.thresholds['rapid_requests']:
                    ip_address, user_id = key.split(':')
                    rate_violations.append({
                        'type': 'rate_abuse',
                        'ip_address': ip_address,
                        'user_id': user_id,
                        'request_count': request_count,
                        'time_window': '1 hour',
                        'severity': 'HIGH' if request_count >= 500 else 'MEDIUM'
                    })
            
            return rate_violations
            
        except Exception as e:
            lambda_logger.error(f"Failed to detect rate abuse: {str(e)}")
            return []
    
    async def _detect_geo_anomalies(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect geographical anomalies."""
        geo_anomalies = []
        
        try:
            # Group login events by user
            user_logins = {}
            
            for event in events:
                if event.get('event_type') == 'successful_login':
                    user_id = event.get('user_id')
                    country = event.get('country')
                    
                    if user_id and country:
                        if user_id not in user_logins:
                            user_logins[user_id] = set()
                        user_logins[user_id].add(country)
            
            # Check for multiple countries
            for user_id, countries in user_logins.items():
                if len(countries) >= self.thresholds['suspicious_locations']:
                    geo_anomalies.append({
                        'type': 'geo_anomaly',
                        'user_id': user_id,
                        'countries': list(countries),
                        'country_count': len(countries),
                        'severity': 'HIGH' if len(countries) >= 5 else 'MEDIUM'
                    })
            
            return geo_anomalies
            
        except Exception as e:
            lambda_logger.error(f"Failed to detect geo anomalies: {str(e)}")
            return []
    
    async def _detect_time_anomalies(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect unusual time-based activities."""
        time_anomalies = []
        
        try:
            unusual_start, unusual_end = self.thresholds['unusual_activity_hours']
            
            for event in events:
                if event.get('event_type') in ['successful_login', 'api_request']:
                    timestamp = event.get('timestamp', 0)
                    event_time = datetime.fromtimestamp(timestamp)
                    hour = event_time.hour
                    
                    # Check if activity is during unusual hours
                    if unusual_start <= hour or hour <= unusual_end:
                        time_anomalies.append({
                            'type': 'time_anomaly',
                            'user_id': event.get('user_id'),
                            'ip_address': event.get('ip_address'),
                            'event_time': event_time.isoformat(),
                            'hour': hour,
                            'severity': 'LOW'
                        })
            
            return time_anomalies
            
        except Exception as e:
            lambda_logger.error(f"Failed to detect time anomalies: {str(e)}")
            return []
    
    async def _detect_api_abuse(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect API abuse patterns."""
        api_abuse = []
        
        try:
            # Group API requests by endpoint and user
            endpoint_usage = {}
            
            for event in events:
                if event.get('event_type') == 'api_request':
                    endpoint = event.get('endpoint')
                    user_id = event.get('user_id', 'anonymous')
                    key = f"{endpoint}:{user_id}"
                    
                    if key not in endpoint_usage:
                        endpoint_usage[key] = []
                    endpoint_usage[key].append(event)
            
            # Check for API abuse
            for key, requests in endpoint_usage.items():
                if len(requests) >= self.thresholds['api_abuse_threshold']:
                    endpoint, user_id = key.split(':', 1)
                    api_abuse.append({
                        'type': 'api_abuse',
                        'endpoint': endpoint,
                        'user_id': user_id,
                        'request_count': len(requests),
                        'severity': 'HIGH'
                    })
            
            return api_abuse
            
        except Exception as e:
            lambda_logger.error(f"Failed to detect API abuse: {str(e)}")
            return []
    
    def _calculate_threat_level(self, threats: Dict[str, List]) -> int:
        """Calculate overall threat level (1-5)."""
        threat_score = 0
        
        for threat_type, threat_list in threats.items():
            for threat in threat_list:
                severity = threat.get('severity', 'LOW')
                if severity == 'HIGH':
                    threat_score += 3
                elif severity == 'MEDIUM':
                    threat_score += 2
                else:
                    threat_score += 1
        
        # Convert to 1-5 scale
        if threat_score >= 10:
            return 5
        elif threat_score >= 7:
            return 4
        elif threat_score >= 4:
            return 3
        elif threat_score >= 2:
            return 2
        elif threat_score >= 1:
            return 1
        else:
            return 0
    
    async def _send_security_alert(
        self, 
        threats: Dict[str, List], 
        threat_level: int
    ) -> None:
        """Send security alert via SNS."""
        try:
            if not self.alert_topic_arn:
                return
            
            alert_message = {
                'alert_type': 'SECURITY_THREAT',
                'threat_level': threat_level,
                'threats': threats,
                'timestamp': datetime.utcnow().isoformat(),
                'requires_immediate_attention': threat_level >= 4
            }
            
            self.sns_client.publish(
                TopicArn=self.alert_topic_arn,
                Message=json.dumps(alert_message, indent=2),
                Subject=f'Security Alert - Threat Level {threat_level}'
            )
            
        except Exception as e:
            lambda_logger.error(f"Failed to send security alert: {str(e)}")
    
    async def _store_analysis_results(
        self, 
        threats: Dict[str, List], 
        threat_level: int
    ) -> None:
        """Store analysis results for historical tracking."""
        try:
            analysis_record = {
                'PK': 'SECURITY_ANALYSIS',
                'SK': f'ANALYSIS#{int(datetime.utcnow().timestamp())}',
                'entity_type': 'SECURITY_ANALYSIS',
                'threat_level': threat_level,
                'threats_detected': threats,
                'analysis_timestamp': int(datetime.utcnow().timestamp()),
                'ttl': int((datetime.utcnow() + timedelta(days=90)).timestamp())
            }
            
            await self.db.put_item(analysis_record)
            
        except Exception as e:
            lambda_logger.error(f"Failed to store analysis results: {str(e)}")
```

#### **1.2 Missing Security Management Endpoints**
**Priority:** Medium  
**Effort:** 2-3 days  
**Impact:** Feature enhancement

**Current State:**
- Basic audit logging exists
- Missing security dashboard
- Limited security configuration

**Required Endpoints:**

```python
# src/handlers/security_dashboard.py
"""Security dashboard handler."""

@require_auth
@require_role(['MASTER', 'SECURITY_ADMIN'])
@rate_limit(requests_per_minute=60)
@security_resilience("security_dashboard")
@measure_performance("security_dashboard")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Security dashboard data.
    
    GET /security/dashboard
    GET /security/threats/active
    GET /security/threats/history
    """
    pass

# src/handlers/security_policies.py
"""Security policies management handler."""

@require_auth
@require_role(['MASTER', 'SECURITY_ADMIN'])
@rate_limit(requests_per_minute=30)
@security_resilience("security_policies")
@measure_performance("security_policies")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Manage security policies.
    
    GET /security/policies
    PUT /security/policies/password
    PUT /security/policies/session
    PUT /security/policies/access
    """
    pass

# src/handlers/incident_response.py
"""Incident response handler."""

@require_auth
@require_role(['MASTER', 'SECURITY_ADMIN'])
@rate_limit(requests_per_minute=30)
@security_resilience("incident_response")
@measure_performance("security_incident_response")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Incident response management.
    
    GET /security/incidents
    POST /security/incidents/{incident_id}/respond
    PUT /security/incidents/{incident_id}/status
    """
    pass
```
