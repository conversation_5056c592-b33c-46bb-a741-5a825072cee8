# src/security/handlers/rate_limit_status.py
# Implementado según "Security Guidelines" y "Rate Limiting"

"""
Rate limit status handler.
Handles checking rate limit status for users and IPs.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.exceptions import ValidationException
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from shared.middleware.resilience_middleware import rate_limit, security_resilience
from shared.metrics import measure_performance
from ..services.rate_limit_service import rate_limit_service


@require_auth
@rate_limit(requests_per_minute=60)  # Moderate limit for rate limit status
@security_resilience("rate_limit_status")
@measure_performance("security_rate_limit_status")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get rate limit status for user or IP.
    
    GET /security/rate-limit-status?type=user&target=user_123
    GET /security/rate-limit-status?type=ip&target=***********
    """
    
    # Validate request body
    validation_result = validate_request_body(event)
    if not validation_result.is_valid:
        return APIResponse.error("Invalid request", validation_result.errors, 400)
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/security/rate-limit-status'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Parse query parameters
        query_params = event.get('queryStringParameters') or {}
        limit_type = query_params.get('type', 'user')  # user, ip, tenant
        target = query_params.get('target')
        
        lambda_logger.info("Getting rate limit status", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'limit_type': limit_type,
            'target': target
        })
        
        # Check permissions (only MASTER and ADMIN can view rate limits)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning("Insufficient permissions for rate limit status", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to view rate limit status",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Validate parameters
        if limit_type not in ['user', 'ip', 'tenant']:
            raise ValidationException("Invalid type. Must be 'user', 'ip', or 'tenant'")
        
        # Set default target if not provided
        if not target:
            if limit_type == 'user':
                target = auth_context.user_id
            elif limit_type == 'ip':
                target = client_ip
            elif limit_type == 'tenant':
                target = auth_context.tenant_id
        
        # Get rate limit status
        import asyncio
from shared.validators import validate_request_body
        rate_limit_status = asyncio.run(rate_limit_service.get_rate_limit_status(
            limit_type=limit_type,
            target=target,
            tenant_id=auth_context.tenant_id
        ))
        
        # Get rate limit configuration
        rate_limit_config = rate_limit_service.get_rate_limit_configuration(
            limit_type=limit_type,
            tenant_id=auth_context.tenant_id
        )
        
        # Calculate time until reset
        time_until_reset = rate_limit_service.calculate_time_until_reset(
            rate_limit_status.get('window_start', 0),
            rate_limit_config.get('window_duration', 3600)
        )
        
        # Prepare response data
        response_data = {
            'rate_limit_status': {
                'type': limit_type,
                'target': target,
                'current_requests': rate_limit_status.get('current_requests', 0),
                'limit': rate_limit_config.get('max_requests', 1000),
                'remaining': max(0, rate_limit_config.get('max_requests', 1000) - rate_limit_status.get('current_requests', 0)),
                'window_start': rate_limit_status.get('window_start'),
                'window_end': rate_limit_status.get('window_end'),
                'time_until_reset': time_until_reset,
                'is_limited': rate_limit_status.get('is_limited', False)
            },
            'configuration': {
                'window_duration': rate_limit_config.get('window_duration', 3600),
                'max_requests': rate_limit_config.get('max_requests', 1000),
                'burst_limit': rate_limit_config.get('burst_limit', 100),
                'enforcement_enabled': rate_limit_config.get('enforcement_enabled', True)
            },
            'recent_activity': rate_limit_status.get('recent_requests', []),
            'warnings': rate_limit_service.get_rate_limit_warnings(
                rate_limit_status, rate_limit_config
            )
        }
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/security/rate-limit-status',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.success(
            data=response_data,
            message="Rate limit status retrieved successfully"
        )
        
    except ValidationException as e:
        lambda_logger.warning("Rate limit status validation error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=400,
            error_code="VALIDATION_ERROR"
        )
        
    except Exception as e:
        lambda_logger.error("Rate limit status error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error retrieving rate limit status",
            status_code=500,
            error_code="RATE_LIMIT_STATUS_ERROR"
        )
