# src/security/handlers/audit_log.py
# Implementado según "Security Guidelines" y "Audit Logging"

"""
Audit log handler.
Handles retrieval of security audit logs.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.exceptions import (
    PlatformException,
    ValidationException
)
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from shared.middleware.resilience_middleware import rate_limit, security_resilience
from shared.metrics import measure_performance
from ..services.audit_service import audit_service


@require_auth
@rate_limit(requests_per_minute=30)  # Moderate limit for audit logs
@security_resilience("audit_log")
@measure_performance("security_audit_log")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get audit logs for tenant.
    
    GET /security/audit-log?limit=50&start_date=2024-01-01&end_date=2024-01-31&event_type=login&user_id=123
    """
    
    # Validate request body
    validation_result = validate_request_body(event)
    if not validation_result.is_valid:
        return APIResponse.error("Invalid request", validation_result.errors, 400)
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/security/audit-log'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Parse query parameters
        query_params = event.get('queryStringParameters') or {}
        limit = int(query_params.get('limit', 50))
        start_date = query_params.get('start_date')
        end_date = query_params.get('end_date')
        event_type = query_params.get('event_type')
        target_user_id = query_params.get('user_id')
        severity = query_params.get('severity')  # low, medium, high, critical
        
        # Validate parameters
        if limit < 1 or limit > 1000:
            limit = 50
        
        lambda_logger.info("Getting audit logs", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'limit': limit,
            'start_date': start_date,
            'end_date': end_date,
            'event_type': event_type,
            'target_user_id': target_user_id,
            'severity': severity
        })
        
        # Check permissions (only MASTER and ADMIN can view audit logs)
        if auth_context.role not in ['MASTER', 'ADMIN']:
            lambda_logger.warning("Insufficient permissions for audit logs", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to view audit logs",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get audit logs
        import asyncio
from shared.validators import validate_request_body
        audit_logs = asyncio.run(audit_service.get_audit_logs(
            tenant_id=auth_context.tenant_id,
            limit=limit,
            start_date=start_date,
            end_date=end_date,
            event_type=event_type,
            user_id=target_user_id,
            severity=severity
        ))
        
        # Get audit summary
        audit_summary = asyncio.run(audit_service.get_audit_summary(
            tenant_id=auth_context.tenant_id,
            start_date=start_date,
            end_date=end_date
        ))
        
        # Prepare response data
        response_data = {
            'audit_logs': audit_logs,
            'summary': audit_summary,
            'filters': {
                'limit': limit,
                'start_date': start_date,
                'end_date': end_date,
                'event_type': event_type,
                'user_id': target_user_id,
                'severity': severity
            },
            'pagination': {
                'total_records': len(audit_logs),
                'has_more': len(audit_logs) == limit
            }
        }
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/security/audit-log',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.success(
            data=response_data,
            message="Audit logs retrieved successfully"
        )
        
    except ValidationException as e:
        lambda_logger.warning("Audit log validation error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=400,
            error_code="VALIDATION_ERROR"
        )
        
    except ValueError as e:
        lambda_logger.warning("Invalid audit log parameters", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message="Invalid query parameters",
            status_code=400,
            error_code="INVALID_PARAMETERS"
        )
        
    except Exception as e:
        lambda_logger.error("Audit log error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error retrieving audit logs",
            status_code=500,
            error_code="AUDIT_LOG_ERROR"
        )
