# src/security/services/rate_limit_service.py
# Implementado según "Security Guidelines" y "Rate Limiting"

"""
Rate limit service.
Manages rate limiting for users, IPs, and tenants.
"""

import time
from typing import Dict, Any, List

from shared.database import db_client
from shared.logger import lambda_logger


class RateLimitService:
    """Service for managing rate limits."""
    
    def __init__(self):
        self.db_client = db_client
        self.default_limits = {
            'user': {'max_requests': 1000, 'window_duration': 3600, 'burst_limit': 100},
            'ip': {'max_requests': 500, 'window_duration': 3600, 'burst_limit': 50},
            'tenant': {'max_requests': 10000, 'window_duration': 3600, 'burst_limit': 1000}
        }
    
    async def get_rate_limit_status(
        self,
        limit_type: str,
        target: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """Get current rate limit status."""
        try:
            current_time = int(time.time())
            window_duration = self.default_limits[limit_type]['window_duration']
            window_start = current_time - (current_time % window_duration)
            window_end = window_start + window_duration
            
            # Get rate limit records
            rate_limit_key = f'RATE_LIMIT_{limit_type.upper()}#{target}'
            records = self.db_client.query(
                rate_limit_key,
                sk_prefix=f'REQUEST#{window_start}',
                tenant_id='global'
            )
            
            current_requests = len(records)
            max_requests = self.default_limits[limit_type]['max_requests']
            is_limited = current_requests >= max_requests
            
            # Get recent requests for analysis
            recent_requests = []
            for record in records[-10:]:  # Last 10 requests
                recent_requests.append({
                    'timestamp': record.get('timestamp'),
                    'endpoint': record.get('endpoint', 'unknown'),
                    'status_code': record.get('status_code', 200)
                })
            
            return {
                'current_requests': current_requests,
                'window_start': window_start,
                'window_end': window_end,
                'is_limited': is_limited,
                'recent_requests': recent_requests
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get rate limit status", extra={
                'limit_type': limit_type,
                'target': target,
                'error': str(e)
            })
            return {
                'current_requests': 0,
                'window_start': 0,
                'window_end': 0,
                'is_limited': False,
                'recent_requests': []
            }
    
    def get_rate_limit_configuration(
        self,
        limit_type: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """Get rate limit configuration."""
        try:
            # Get tenant-specific configuration if exists
            config = self.db_client.get_item(
                f'TENANT#{tenant_id}',
                f'RATE_LIMIT_CONFIG#{limit_type.upper()}',
                tenant_id
            )
            
            if config:
                return {
                    'max_requests': config.get('max_requests'),
                    'window_duration': config.get('window_duration'),
                    'burst_limit': config.get('burst_limit'),
                    'enforcement_enabled': config.get('enforcement_enabled', True)
                }
            
            # Return default configuration
            default_config = self.default_limits.get(limit_type, self.default_limits['user'])
            return {
                **default_config,
                'enforcement_enabled': True
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get rate limit configuration", extra={
                'limit_type': limit_type,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return self.default_limits.get(limit_type, self.default_limits['user'])
    
    def calculate_time_until_reset(self, window_start: int, window_duration: int) -> int:
        """Calculate seconds until rate limit window resets."""
        current_time = int(time.time())
        window_end = window_start + window_duration
        return max(0, window_end - current_time)
    
    def get_rate_limit_warnings(
        self,
        status: Dict[str, Any],
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get rate limit warnings."""
        warnings = []
        
        current_requests = status.get('current_requests', 0)
        max_requests = config.get('max_requests', 1000)
        
        usage_percentage = (current_requests / max_requests) * 100 if max_requests > 0 else 0
        
        if usage_percentage >= 90:
            warnings.append({
                'level': 'critical',
                'message': f'Rate limit usage at {usage_percentage:.1f}%',
                'recommendation': 'Reduce request frequency to avoid being rate limited'
            })
        elif usage_percentage >= 75:
            warnings.append({
                'level': 'warning',
                'message': f'Rate limit usage at {usage_percentage:.1f}%',
                'recommendation': 'Monitor request frequency'
            })
        
        if status.get('is_limited', False):
            time_until_reset = self.calculate_time_until_reset(
                status.get('window_start', 0),
                config.get('window_duration', 3600)
            )
            warnings.append({
                'level': 'error',
                'message': 'Rate limit exceeded',
                'recommendation': f'Wait {time_until_reset} seconds before making more requests'
            })
        
        return warnings


# Global service instance
rate_limit_service = RateLimitService()
