# src/security/services/audit_service.py
# Implementado según "Security Guidelines" y "Audit Logging"

"""
Audit service.
Manages security audit logs and compliance tracking.
"""

import time
from typing import List, Dict, Any, Optional
from datetime import datetime

from shared.database import db_client
from shared.logger import lambda_logger


class AuditService:
    """Service for managing audit logs."""
    
    def __init__(self):
        self.db_client = db_client
    
    async def get_audit_logs(
        self,
        tenant_id: str,
        limit: int = 50,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        event_type: Optional[str] = None,
        user_id: Optional[str] = None,
        severity: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get audit logs with filters."""
        try:
            # Query audit logs
            audit_logs = self.db_client.query(
                f'TENANT#{tenant_id}',
                sk_prefix='AUDIT#',
                tenant_id=tenant_id,
                limit=limit * 2  # Get more to allow for filtering
            )
            
            # Apply filters
            filtered_logs = []
            for log in audit_logs:
                # Filter by date range
                if start_date and log.get('timestamp', 0) < self._parse_date(start_date):
                    continue
                if end_date and log.get('timestamp', 0) > self._parse_date(end_date):
                    continue
                
                # Filter by event type
                if event_type and log.get('event_type') != event_type:
                    continue
                
                # Filter by user
                if user_id and log.get('user_id') != user_id:
                    continue
                
                # Filter by severity
                if severity and log.get('severity') != severity:
                    continue
                
                filtered_logs.append({
                    'id': log.get('audit_id'),
                    'timestamp': log.get('timestamp'),
                    'event_type': log.get('event_type'),
                    'user_id': log.get('user_id'),
                    'user_email': log.get('user_email'),
                    'ip_address': log.get('ip_address'),
                    'user_agent': log.get('user_agent'),
                    'resource': log.get('resource'),
                    'action': log.get('action'),
                    'result': log.get('result'),
                    'severity': log.get('severity', 'low'),
                    'details': log.get('details', {}),
                    'session_id': log.get('session_id')
                })
                
                if len(filtered_logs) >= limit:
                    break
            
            return filtered_logs[:limit]
            
        except Exception as e:
            lambda_logger.error("Failed to get audit logs", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    async def get_audit_summary(
        self,
        tenant_id: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get audit summary statistics."""
        try:
            # Get all audit logs for the period
            audit_logs = self.db_client.query(
                f'TENANT#{tenant_id}',
                sk_prefix='AUDIT#',
                tenant_id=tenant_id
            )
            
            # Apply date filters
            if start_date or end_date:
                start_ts = self._parse_date(start_date) if start_date else 0
                end_ts = self._parse_date(end_date) if end_date else int(time.time())
                
                audit_logs = [
                    log for log in audit_logs
                    if start_ts <= log.get('timestamp', 0) <= end_ts
                ]
            
            # Calculate summary statistics
            total_events = len(audit_logs)
            event_types = {}
            severity_counts = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
            user_activity = {}
            
            for log in audit_logs:
                # Count event types
                event_type = log.get('event_type', 'unknown')
                event_types[event_type] = event_types.get(event_type, 0) + 1
                
                # Count severity levels
                severity = log.get('severity', 'low')
                if severity in severity_counts:
                    severity_counts[severity] += 1
                
                # Count user activity
                user_id = log.get('user_id')
                if user_id:
                    user_activity[user_id] = user_activity.get(user_id, 0) + 1
            
            return {
                'total_events': total_events,
                'event_types': event_types,
                'severity_distribution': severity_counts,
                'most_active_users': sorted(
                    user_activity.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10],
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                }
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get audit summary", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return {}
    
    def _parse_date(self, date_str: str) -> int:
        """Parse date string to timestamp."""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return int(dt.timestamp())
        except:
            return 0


# Global service instance
audit_service = AuditService()
