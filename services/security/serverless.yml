# services/security/serverless.yml
# Security service configuration

service: agent-scl-security

# Custom configuration
custom:
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Reference shared layer
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName
    
  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - logs:DescribeLogGroups
        - logs:DescribeLogStreams
      Resource: "*"
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"

# Functions
functions:
  # Audit logging
  auditLog:
    handler: src.handlers.audit_log.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /security/audit
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      - http:
          path: /security/audit
          method: post
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: security_audit_log
    tracing: Active

  # Rate limiting status
  rateLimitStatus:
    handler: src.handlers.rate_limit_status.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /security/rate-limit
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: security_rate_limit_status
    tracing: Active

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'

# Plugins
plugins:
  - serverless-python-requirements

# Resources
resources:
  Description: Security service for Agent SCL platform
  Outputs:
    SecurityServiceEndpoint:
      Description: "Security Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-security-${self:custom.stage}-SecurityServiceEndpoint
