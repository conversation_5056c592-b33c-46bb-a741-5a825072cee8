# Tenant Service Dependencies
# Core dependencies are provided by the shared layer
# Service-specific dependencies that must be available locally

# JWT handling - Required for shared layer compatibility
PyJWT==2.8.0

# Email validation - Required for tenant registration
email-validator==2.1.0

# Date/time utilities - Required for tenant operations
python-dateutil==2.8.2

# Cryptography for JWT - Lambda compatible version
cryptography==3.4.8
