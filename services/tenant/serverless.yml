# services/tenant/serverless.yml
# Tenant service configuration

service: agent-scl-tenant

# Custom configuration
custom:
  serviceName: tenant
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
  # Python requirements optimization
  pythonRequirements:
    dockerizePip: false
    slim: true
    strip: false
    useDownloadCache: true
    useStaticCache: true
    fileName: requirements.txt  # Solo dependencias de producciÃ³n

# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE: ${self:custom.stageConfig.dynamodbTable}

  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"

# Functions
functions:
  # Tenant registration (no auth required)
  register:
    handler: src.handlers.register.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/register
          method: post
          cors: true
    environment:
      FUNCTION_NAME: tenant_register
    tracing: Active

  # Tenant management
  getProfile:
    handler: src.handlers.get_profile.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/profile
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_get_profile
    tracing: Active

  updateSettings:
    handler: src.handlers.update_settings.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/settings
          method: put
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_update_settings
    tracing: Active

  inviteUser:
    handler: src.handlers.invite_user.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/invite
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_invite_user
    tracing: Active

  listUsers:
    handler: src.handlers.list_users.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/users
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_list_users
    tracing: Active

  getUser:
    handler: src.handlers.get_user.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/users/{user_id}
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_get_user
    tracing: Active

  updateUser:
    handler: src.handlers.update_user.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/users/{user_id}
          method: put
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_update_user
    tracing: Active

  deleteUser:
    handler: src.handlers.delete_user.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/users/{user_id}
          method: delete
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_delete_user
    tracing: Active

  getUsageStats:
    handler: src.handlers.get_usage_stats.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/usage
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_get_usage_stats
    tracing: Active

  exportData:
    handler: src.handlers.export_data.handler
    timeout: 29
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/export
          method: post
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_export_data
    tracing: Active

  acceptInvitation:
    handler: src.handlers.accept_invitation.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/invitation/accept
          method: post
          cors: true
    environment:
      FUNCTION_NAME: tenant_accept_invitation
    tracing: Active

  getTenants:
    handler: src.handlers.get_tenants.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /tenant/list
          method: get
          cors: true
          authorizer:
            name: jwtAuthorizer
            type: request
            arn:
              Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: tenant_get_tenants
    tracing: Active

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'

# Plugins
plugins:
  - serverless-python-requirements

# Resources
resources:
  Outputs:
    TenantServiceEndpoint:
      Description: "Tenant Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-tenant-${self:custom.stage}-TenantServiceEndpoint
