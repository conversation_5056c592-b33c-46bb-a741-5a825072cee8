# 🏢 Tenant Service - Gaps and Improvements

## 📊 **Current Status: 8.7/10 - MUY BUENO**

### **Completitud:** 95% funcional, gaps menores identificados

---

## 🎯 **Gaps Identificados**

### **1. CRITICAL GAPS (10%)**

#### **1.1 Missing Critical Endpoints**
**Priority:** High
**Effort:** 4-5 days
**Impact:** Feature completeness

**Current State:**
- Missing user update/deactivation endpoints
- Missing tenant settings management
- Missing user role management
- Missing invitation management endpoints

#### **1.2 Real User Management Integration**
**Priority:** High
**Effort:** 3-4 days
**Impact:** Core functionality

**Current State:**
- Mock user data in list_users handler
- Limited integration with auth service
- Placeholder user operations

**Required Implementation:**

```python
# src/services/real_user_service.py
"""Real user management service integration."""

import boto3
from typing import List, Dict, Any, Optional
from shared.database import db_client
from shared.logger import lambda_logger
from shared.exceptions import ResourceNotFoundException, ValidationException

class RealUserService:
    """Real user management service."""
    
    def __init__(self):
        self.db = db_client
        self.auth_service_url = os.environ.get('AUTH_SERVICE_URL')
    
    async def get_tenant_users(
        self, 
        tenant_id: str, 
        page: int = 1, 
        page_size: int = 20,
        status_filter: Optional[str] = None,
        role_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get real users for tenant with pagination and filtering."""
        try:
            # Query users from DynamoDB
            users_response = self.db.query(
                pk=f'TENANT#{tenant_id}',
                sk_prefix='USER#',
                tenant_id=tenant_id
            )
            
            users = []
            for user_record in users_response:
                # Apply filters
                if status_filter and user_record.get('status') != status_filter.upper():
                    continue
                if role_filter and user_record.get('role') != role_filter.upper():
                    continue
                
                # Format user data
                user_data = {
                    'user_id': user_record.get('user_id'),
                    'email': user_record.get('email'),
                    'first_name': user_record.get('first_name'),
                    'last_name': user_record.get('last_name'),
                    'role': user_record.get('role'),
                    'status': user_record.get('status'),
                    'email_verified': user_record.get('email_verified', False),
                    'created_at': user_record.get('created_at'),
                    'last_login_at': user_record.get('last_login_at'),
                    'is_current_user': False  # Will be set later
                }
                users.append(user_data)
            
            # Apply pagination
            total_users = len(users)
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_users = users[start_index:end_index]
            
            return {
                'users': paginated_users,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_users': total_users,
                    'total_pages': (total_users + page_size - 1) // page_size,
                    'has_next': end_index < total_users,
                    'has_previous': page > 1
                }
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant users", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    async def invite_user_real(
        self,
        tenant_id: str,
        email: str,
        first_name: str,
        last_name: str,
        role: str,
        invited_by_user_id: str
    ) -> Dict[str, Any]:
        """Send real user invitation."""
        try:
            # Check if user already exists
            existing_user = await self._check_existing_user(email, tenant_id)
            if existing_user:
                raise ValidationException(
                    f"User with email {email} already exists in this tenant"
                )
            
            # Generate invitation token
            invitation_token = self._generate_invitation_token()
            
            # Store invitation in database
            invitation_record = {
                'PK': f'TENANT#{tenant_id}',
                'SK': f'INVITATION#{invitation_token}',
                'entity_type': 'INVITATION',
                'tenant_id': tenant_id,
                'email': email,
                'first_name': first_name,
                'last_name': last_name,
                'role': role,
                'invited_by': invited_by_user_id,
                'status': 'PENDING',
                'created_at': int(time.time()),
                'expires_at': int(time.time()) + (7 * 24 * 60 * 60),  # 7 days
                'invitation_token': invitation_token
            }
            
            self.db.put_item(invitation_record, tenant_id)
            
            # Send invitation email
            await self._send_invitation_email(
                email, 
                first_name, 
                invitation_token, 
                tenant_id
            )
            
            lambda_logger.info("User invitation sent", extra={
                'tenant_id': tenant_id,
                'email': email,
                'invited_by': invited_by_user_id
            })
            
            return {
                'invitation_id': invitation_token,
                'email': email,
                'status': 'SENT',
                'expires_at': invitation_record['expires_at']
            }
            
        except Exception as e:
            lambda_logger.error("Failed to invite user", extra={
                'tenant_id': tenant_id,
                'email': email,
                'error': str(e)
            })
            raise
    
    async def _check_existing_user(self, email: str, tenant_id: str) -> Optional[Dict]:
        """Check if user already exists in tenant."""
        try:
            # Query by email GSI
            response = self.db.query_gsi(
                gsi_name='GSI2',
                pk=f'EMAIL#{email}',
                sk=f'TENANT#{tenant_id}',
                tenant_id=tenant_id
            )
            return response[0] if response else None
        except Exception:
            return None
    
    def _generate_invitation_token(self) -> str:
        """Generate secure invitation token."""
        import secrets
        return f"inv_{secrets.token_urlsafe(32)}"
    
    async def _send_invitation_email(
        self, 
        email: str, 
        first_name: str, 
        invitation_token: str, 
        tenant_id: str
    ) -> None:
        """Send invitation email via SES."""
        try:
            # This would integrate with SES or email service
            # For now, log the email details
            lambda_logger.info("Sending invitation email", extra={
                'email': email,
                'first_name': first_name,
                'invitation_token': invitation_token,
                'tenant_id': tenant_id
            })
            
            # TODO: Implement real email sending
            # ses_client = boto3.client('ses')
            # ses_client.send_email(...)
            
        except Exception as e:
            lambda_logger.error("Failed to send invitation email", extra={
                'email': email,
                'error': str(e)
            })
            raise
```

#### **1.2 Complete User CRUD Operations**
**Priority:** High  
**Effort:** 2-3 days  
**Impact:** Feature completeness

**Current State:**
- Missing user update functionality
- Missing user deactivation
- Limited user management

**Required Implementation:**

```python
# src/handlers/update_user.py
"""Update user handler."""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body
from shared.auth import require_auth
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    AuthorizationException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
from shared.models import UserRole
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

configure_dependencies()

def validate_update_user_request(body: str) -> Dict[str, Any]:
    """Validate update user request."""
    try:
        data = json.loads(body) if isinstance(body, str) else body
        
        # Validate allowed fields
        allowed_fields = [
            'first_name', 'last_name', 'role', 'phone_number', 
            'timezone', 'language', 'is_active'
        ]
        
        update_data = {}
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # Validate role if provided
        if 'role' in update_data:
            valid_roles = ['MASTER', 'MEMBER']
            if update_data['role'] not in valid_roles:
                raise ValueError(f"Invalid role. Must be one of: {valid_roles}")
        
        # Validate names if provided
        if 'first_name' in update_data and len(update_data['first_name'].strip()) < 1:
            raise ValueError("First name cannot be empty")
        
        if 'last_name' in update_data and len(update_data['last_name'].strip()) < 1:
            raise ValueError("Last name cannot be empty")
        
        return update_data
        
    except json.JSONDecodeError:
        raise ValueError("Invalid JSON format")
    except Exception as e:
        raise ValueError(str(e))

@require_auth
@rate_limit(requests_per_minute=30)
@tenant_resilience("update_user")
@measure_performance("tenant_update_user")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Update user information.
    
    PUT /tenant/users/{user_id}
    {
        "first_name": "John",
        "last_name": "Doe",
        "role": "MEMBER",
        "phone_number": "+1234567890",
        "timezone": "America/New_York",
        "language": "en",
        "is_active": true
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Extract user ID from path
    path_parameters = event.get('pathParameters') or {}
    target_user_id = path_parameters.get('user_id')
    
    if not target_user_id:
        return APIResponse.bad_request(
            message="User ID is required in path",
            error_code="MISSING_USER_ID"
        )
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', f'/tenant/users/{target_user_id}'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Validate request body
        body = event.get('body', '{}')
        update_data = validate_update_user_request(body)
        
        if not update_data:
            return APIResponse.bad_request(
                message="No valid update fields provided",
                error_code="NO_UPDATE_FIELDS"
            )
        
        # Check permissions - only MASTER can update other users
        if auth_context.role != 'MASTER' and auth_context.user_id != target_user_id:
            return APIResponse.forbidden(
                message="Insufficient permissions to update this user",
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Prevent role escalation - only MASTER can change roles
        if 'role' in update_data and auth_context.role != 'MASTER':
            return APIResponse.forbidden(
                message="Only MASTER users can change user roles",
                error_code="ROLE_CHANGE_FORBIDDEN"
            )
        
        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)
        
        # Update user
        updated_user = await tenant_service.update_user(
            tenant_id=auth_context.tenant_id,
            user_id=target_user_id,
            update_data=update_data,
            updated_by=auth_context.user_id
        )
        
        # Audit log
        audit_log(
            action='user_updated',
            resource_type='user',
            resource_id=target_user_id,
            user_id=auth_context.user_id,
            tenant_id=auth_context.tenant_id,
            details={
                'updated_fields': list(update_data.keys()),
                'target_user_id': target_user_id
            }
        )
        
        # Prepare response
        response_data = {
            'user': updated_user,
            'updated_fields': list(update_data.keys()),
            'updated_by': auth_context.user_id,
            'updated_at': updated_user.get('updated_at')
        }
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            f'/tenant/users/{target_user_id}',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.success(
            data=response_data,
            message="User updated successfully"
        )
        
    except ValidationException as e:
        return APIResponse.bad_request(
            message=str(e),
            error_code="VALIDATION_ERROR"
        )
    except ResourceNotFoundException as e:
        return APIResponse.not_found(
            message=str(e),
            error_code="USER_NOT_FOUND"
        )
    except Exception as e:
        lambda_logger.error("Failed to update user", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': target_user_id,
            'request_id': request_id
        })
        
        return APIResponse.internal_server_error(
            message="Failed to update user",
            error_code="UPDATE_USER_FAILED"
        )
```
