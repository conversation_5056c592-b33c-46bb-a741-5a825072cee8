# services/tenant/src/config/dependencies.py
# Dependency injection configuration for tenant service

"""
Dependency injection configuration for tenant service.
Registers all services and their implementations.
"""

from shared.dependency_injection import container
from ..services.tenant_service import ITenantService, TenantService


def configure_dependencies():
    """Configure dependency injection for tenant service."""
    # Register tenant service as singleton
    container.register_singleton(
        ITenantService,
        TenantService
    )


# Auto-configure dependencies when module is imported
configure_dependencies()
