# services/tenant/src/services/tenant_service.py
# Tenant service implementation with shared layer integration

"""
Tenant service implementation.
Provides business logic for tenant management operations.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

# Import shared layer utilities
from shared.database import db_client
from shared.exceptions import (
    ResourceNotFoundException,
    ResourceConflictException,
    ValidationException,
    BusinessLogicException
)
from shared.logger import lambda_logger
from shared.auth import AuthContext
from shared.models import TenantStatus, TenantPlan
from ..models.tenant import Tenant, create_tenant


class ITenantService(ABC):
    """Interface for tenant service operations."""
    
    @abstractmethod
    def create_tenant(
        self,
        company_name: str,
        industry: Optional[str] = None,
        company_size: Optional[str] = None,
        country: Optional[str] = None,
        address: Optional[str] = None,
        phone: Optional[str] = None,
        website: Optional[str] = None
    ) -> Tenant:
        """Create a new tenant."""
        pass
    
    @abstractmethod
    def get_tenant_by_id(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID."""
        pass
    
    @abstractmethod
    def get_tenant_dashboard_data(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant dashboard data including profile and statistics."""
        pass
    
    @abstractmethod
    def update_tenant_settings(
        self,
        auth_context: AuthContext,
        settings: Dict[str, Any]
    ) -> Tenant:
        """Update tenant settings."""
        pass
    
    @abstractmethod
    def get_tenant_usage_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant usage statistics."""
        pass

    @abstractmethod
    def list_tenant_users(
        self,
        tenant_id: str,
        page: int = 1,
        page_size: int = 20,
        status_filter: Optional[str] = None,
        role_filter: Optional[str] = None,
        requesting_user_id: str = None,
        can_view_all: bool = False
    ) -> Dict[str, Any]:
        """List users in a tenant with filtering and pagination."""
        pass

    @abstractmethod
    def update_tenant_user(
        self,
        tenant_id: str,
        user_id: str,
        update_data: Dict[str, Any],
        requesting_user_id: str
    ) -> Dict[str, Any]:
        """Update user information in a tenant."""
        pass


class TenantService(ITenantService):
    """Tenant service implementation."""
    
    def __init__(self):
        """Initialize tenant service."""
        self.db = db_client
    
    def create_tenant(
        self,
        company_name: str,
        industry: Optional[str] = None,
        company_size: Optional[str] = None,
        country: Optional[str] = None,
        address: Optional[str] = None,
        phone: Optional[str] = None,
        website: Optional[str] = None
    ) -> Tenant:
        """Create a new tenant with validation and business logic."""
        try:
            # Validate company name uniqueness
            existing_tenant = self._get_tenant_by_name(company_name)
            if existing_tenant:
                raise ResourceConflictException(
                    f"A tenant with company name '{company_name}' already exists",
                    error_code="TENANT_NAME_EXISTS"
                )

            # Create tenant using unified model helper
            tenant = create_tenant(
                company_name=company_name,
                industry=industry,
                company_size=company_size,
                country=country,
                address=address,
                phone=phone,
                website=website
            )

            # Save tenant
            tenant.save()

            lambda_logger.info("Tenant created successfully", extra={
                'tenant_id': tenant.tenant_id,
                'company_name': company_name
            })

            return tenant

        except Exception as e:
            lambda_logger.error("Failed to create tenant", extra={
                'company_name': company_name,
                'error': str(e)
            })
            raise
    
    def get_tenant_by_id(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID."""
        return Tenant.get_by_id(tenant_id)
    
    def get_tenant_dashboard_data(self, tenant_id: str) -> Dict[str, Any]:
        """Get comprehensive tenant dashboard data."""
        try:
            # Get tenant profile
            tenant = self.get_tenant_by_id(tenant_id)
            if not tenant:
                raise ResourceNotFoundException(
                    f"Tenant not found: {tenant_id}",
                    error_code="TENANT_NOT_FOUND"
                )
            
            # Get tenant statistics (placeholder implementation)
            statistics = self._get_tenant_statistics(tenant_id)
            
            # Get tenant users (placeholder implementation)
            users = self._get_tenant_users(tenant_id)
            
            # Get tenant features based on plan
            features = self._get_tenant_features(tenant.plan)
            
            return {
                'tenant': tenant.to_dict(),
                'statistics': statistics,
                'users': users,
                'features': features
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant dashboard data", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    def update_tenant_settings(
        self,
        auth_context: AuthContext,
        settings: Dict[str, Any]
    ) -> Tenant:
        """Update tenant settings with authorization check and comprehensive validation."""
        try:
            # Get tenant
            tenant = self.get_tenant_by_id(auth_context.tenant_id)
            if not tenant:
                raise ResourceNotFoundException(
                    f"Tenant not found: {auth_context.tenant_id}",
                    error_code="TENANT_NOT_FOUND"
                )

            # Check authorization (only master users can update settings)
            if not auth_context.is_master():
                from shared.exceptions import AuthorizationException
                raise AuthorizationException(
                    "Only master users can update tenant settings",
                    error_code="INSUFFICIENT_PERMISSIONS"
                )

            # Get current settings or initialize empty dict
            current_settings = tenant.settings or {}

            # Update settings by merging with current settings
            updated_settings = current_settings.copy()
            updated_settings.update(settings)

            # Update tenant settings
            tenant.settings = updated_settings

            # Also update basic profile fields if provided
            profile_fields = {
                'name': 'name',
                'industry': 'industry',
                'company_size': 'company_size',
                'country': 'country',
                'address': 'address',
                'phone': 'phone',
                'website': 'website'
            }

            for setting_key, tenant_attr in profile_fields.items():
                if setting_key in settings and settings[setting_key] is not None:
                    setattr(tenant, tenant_attr, settings[setting_key])

            # Save updated tenant
            tenant.save()

            lambda_logger.info("Tenant settings updated successfully", extra={
                'tenant_id': tenant.tenant_id,
                'updated_by': auth_context.user_id,
                'settings_keys': list(settings.keys()),
                'total_settings': len(updated_settings)
            })

            return tenant

        except Exception as e:
            lambda_logger.error("Failed to update tenant settings", extra={
                'tenant_id': auth_context.tenant_id,
                'error': str(e)
            })
            raise
    
    def get_tenant_usage_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant usage statistics."""
        try:
            # Placeholder implementation - would integrate with actual usage tracking
            return {
                'users': {
                    'total_count': 0,
                    'active_count': 0,
                    'max_users': 100  # Based on plan
                },
                'api_calls': {
                    'current_month': 0,
                    'limit': 10000  # Based on plan
                },
                'storage': {
                    'used_mb': 0,
                    'limit_mb': 1000  # Based on plan
                }
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant usage stats", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise
    
    def _get_tenant_by_name(self, company_name: str) -> Optional[Tenant]:
        """Get tenant by company name (internal method)."""
        try:
            # Query by company name using GSI (if available)
            # For now, using simple scan - should be optimized with proper GSI
            response = self.db.scan(
                filter_expression="company_name = :name AND entity_type = :type",
                expression_attribute_values={
                    ":name": company_name,
                    ":type": "TENANT"
                }
            )
            
            items = response.get('Items', [])
            if items:
                return Tenant._from_db_item(items[0])
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant by name", extra={
                'company_name': company_name,
                'error': str(e)
            })
            return None
    
    def _get_tenant_statistics(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant statistics (placeholder implementation)."""
        return {
            'total_users': 0,
            'active_users': 0,
            'total_api_calls': 0,
            'storage_used': 0
        }
    
    def _get_tenant_users(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant users summary (placeholder implementation)."""
        return {
            'total_count': 0,
            'active_count': 0,
            'max_users': 100,
            'recent_users': []
        }
    
    def _get_tenant_features(self, plan: TenantPlan) -> Dict[str, Any]:
        """Get tenant features based on plan."""
        features_map = {
            TenantPlan.FREE: {
                'max_users': 5,
                'api_calls_limit': 1000,
                'storage_limit_mb': 100,
                'support_level': 'community'
            },
            TenantPlan.BASIC: {
                'max_users': 25,
                'api_calls_limit': 10000,
                'storage_limit_mb': 1000,
                'support_level': 'email'
            },
            TenantPlan.PROFESSIONAL: {
                'max_users': 100,
                'api_calls_limit': 50000,
                'storage_limit_mb': 5000,
                'support_level': 'priority'
            },
            TenantPlan.ENTERPRISE: {
                'max_users': -1,  # Unlimited
                'api_calls_limit': -1,  # Unlimited
                'storage_limit_mb': -1,  # Unlimited
                'support_level': 'dedicated'
            }
        }
        
        return features_map.get(plan, features_map[TenantPlan.FREE])

    def list_tenant_users(
        self,
        tenant_id: str,
        page: int = 1,
        page_size: int = 20,
        status_filter: Optional[str] = None,
        role_filter: Optional[str] = None,
        requesting_user_id: str = None,
        can_view_all: bool = False
    ) -> Dict[str, Any]:
        """List users in a tenant with filtering and pagination."""
        try:
            # For now, return mock data structure that matches expected format
            # In production, this would integrate with User Service
            mock_users = [
                {
                    'user_id': requesting_user_id or 'user-123',
                    'email': f'<EMAIL>',
                    'first_name': 'Current',
                    'last_name': 'User',
                    'role': 'MASTER',
                    'status': 'ACTIVE',
                    'created_at': '2025-08-13T19:00:00Z',
                    'last_login': '2025-08-13T19:30:00Z',
                    'is_current_user': True
                }
            ]

            # Apply filters
            filtered_users = mock_users
            if status_filter:
                filtered_users = [u for u in filtered_users if u['status'] == status_filter.upper()]
            if role_filter:
                filtered_users = [u for u in filtered_users if u['role'] == role_filter.upper()]

            # Apply pagination
            total_count = len(filtered_users)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_users = filtered_users[start_idx:end_idx]

            return {
                'success': True,
                'data': {
                    'users': paginated_users,
                    'total_count': total_count
                },
                'error_message': None
            }

        except Exception as e:
            lambda_logger.error(f"Error listing tenant users: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error_message': str(e)
            }

    def update_tenant_user(
        self,
        tenant_id: str,
        user_id: str,
        update_data: Dict[str, Any],
        requesting_user_id: str
    ) -> Dict[str, Any]:
        """Update user information in a tenant."""
        try:
            # For now, return mock success response
            # In production, this would integrate with User Service
            updated_user = {
                'user_id': user_id,
                'tenant_id': tenant_id,
                'updated_at': datetime.now().isoformat(),
                'updated_by': requesting_user_id,
                **update_data
            }

            return {
                'success': True,
                'data': updated_user,
                'error_message': None
            }

        except Exception as e:
            lambda_logger.error(f"Error updating tenant user: {str(e)}")
            return {
                'success': False,
                'data': None,
                'error_message': str(e)
            }


# Service instance for dependency injection
tenant_service = TenantService()
