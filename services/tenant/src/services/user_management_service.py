#!/usr/bin/env python3
# services/tenant/src/services/user_management_service.py
# User management service

"""
User management service for tenant operations.
Handles CRUD operations for users within tenants with proper authorization and audit logging.
"""

import os
import boto3
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from botocore.exceptions import ClientError

from shared.logger import lambda_logger
from shared.exceptions import ValidationException, ResourceNotFoundException, PlatformException
from shared.database import DynamoDBClient
from shared.service_communication import ServiceCommunicationManager, UserServiceClient
from shared.validators import UnifiedUserValidator
from shared.models import UserRole, UserStatus


class UserManagementService:
    """Service for managing users within tenants."""
    
    def __init__(self):
        self.dynamodb_service = DynamoDBClient()
        self.table_name = os.environ.get('DYNAMODB_TABLE')
        if not self.table_name:
            raise ValueError("DYNAMODB_TABLE environment variable not set")

        # Initialize service communication manager for cross-service operations
        self.service_comm = ServiceCommunicationManager(self.dynamodb_service)
        self.user_client = UserServiceClient(self.dynamodb_service)
    
    def get_user(self, tenant_id: str, user_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific user."""
        try:
            lambda_logger.info("Getting user information", extra={
                'tenant_id': tenant_id,
                'user_id': user_id
            })
            
            # Get user record
            user_record = self.dynamodb_service.get_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                tenant_id=tenant_id
            )
            
            if not user_record:
                raise ResourceNotFoundException(f"User {user_id} not found in tenant {tenant_id}")
            
            # Check if user belongs to the tenant
            if user_record.get('tenant_id') != tenant_id:
                raise ResourceNotFoundException(f"User {user_id} not found in tenant {tenant_id}")
            
            # Format user data
            user_data = self._format_user_data(user_record)
            
            # Get user's recent activity (optional)
            try:
                recent_activity = self._get_user_recent_activity(tenant_id, user_id)
                user_data['recent_activity'] = recent_activity
            except Exception as e:
                lambda_logger.warning("Failed to get user recent activity", extra={
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'error': str(e)
                })
                user_data['recent_activity'] = []
            
            lambda_logger.info("User information retrieved successfully", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'user_status': user_data.get('status')
            })
            
            return user_data
            
        except ResourceNotFoundException:
            raise
        except Exception as e:
            lambda_logger.error("Failed to get user information", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            raise PlatformException(f"Failed to retrieve user information: {str(e)}")
    
    def update_user(self, tenant_id: str, user_id: str, update_data: Dict[str, Any], 
                   updated_by: str) -> Dict[str, Any]:
        """Update user information."""
        try:
            lambda_logger.info("Updating user", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'updated_by': updated_by,
                'update_fields': list(update_data.keys())
            })
            
            # Get current user data
            current_user = self.dynamodb_service.get_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                tenant_id=tenant_id
            )
            
            if not current_user:
                raise ResourceNotFoundException(f"User {user_id} not found in tenant {tenant_id}")
            
            # Prepare update data
            update_expression_parts = []
            expression_attribute_names = {}
            expression_attribute_values = {}
            
            # Store previous values for audit
            previous_values = {}
            
            for field, value in update_data.items():
                if field in ['user_id', 'tenant_id', 'pk', 'sk', 'entity_type']:
                    continue  # Skip immutable fields
                
                # Store previous value
                previous_values[field] = current_user.get(field)
                
                # Add to update expression
                attr_name = f"#{field}"
                attr_value = f":{field}"
                
                update_expression_parts.append(f"{attr_name} = {attr_value}")
                expression_attribute_names[attr_name] = field
                expression_attribute_values[attr_value] = value
            
            # Add metadata
            now = datetime.utcnow().isoformat()
            update_expression_parts.append("#updated_at = :updated_at")
            update_expression_parts.append("#updated_by = :updated_by")
            
            expression_attribute_names["#updated_at"] = "updated_at"
            expression_attribute_names["#updated_by"] = "updated_by"
            expression_attribute_values[":updated_at"] = now
            expression_attribute_values[":updated_by"] = updated_by
            
            # Update the current user item with new values
            for field, value in update_data.items():
                current_user[field] = value

            # Add updated timestamp
            current_user['updated_at'] = int(datetime.utcnow().timestamp())
            current_user['updated_by'] = updated_by

            # Save updated user record
            updated_item = self.dynamodb_service.put_item(
                item=current_user,
                tenant_id=tenant_id
            )
            
            # Log user update activity
            self._log_user_activity(
                tenant_id=tenant_id,
                user_id=user_id,
                activity_type='user_updated',
                details={
                    'updated_by': updated_by,
                    'updated_fields': list(update_data.keys()),
                    'previous_values': {k: v for k, v in previous_values.items() if k != 'password'},
                    'new_values': {k: v for k, v in update_data.items() if k != 'password'}
                }
            )
            
            # Format response
            user_data = self._format_user_data(updated_item)
            
            lambda_logger.info("User updated successfully", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'updated_by': updated_by,
                'updated_fields': list(update_data.keys())
            })
            
            return {
                'user_data': user_data,
                'previous_values': previous_values,
                'updated_at': now,
                'updated_by': updated_by
            }
            
        except ResourceNotFoundException:
            raise
        except Exception as e:
            lambda_logger.error("Failed to update user", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'updated_by': updated_by,
                'error': str(e)
            })
            raise PlatformException(f"Failed to update user: {str(e)}")
    
    def delete_user(self, tenant_id: str, user_id: str, deleted_by: str,
                   force_delete: bool = False, transfer_to_user_id: Optional[str] = None) -> Dict[str, Any]:
        """Delete a user from the tenant."""
        try:
            lambda_logger.info("Deleting user", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'deleted_by': deleted_by,
                'force_delete': force_delete,
                'transfer_to_user_id': transfer_to_user_id
            })
            
            # Get current user data
            current_user = self.dynamodb_service.get_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                tenant_id=tenant_id
            )
            
            if not current_user:
                raise ResourceNotFoundException(f"User {user_id} not found in tenant {tenant_id}")
            
            # Check if user has active sessions (if not force delete)
            if not force_delete:
                active_sessions = self._check_active_sessions(tenant_id, user_id)
                if active_sessions > 0:
                    raise ValidationException(f"User has {active_sessions} active sessions. Use force=true to delete anyway.")
            
            # Transfer resources if specified
            resources_transferred = 0
            if transfer_to_user_id:
                resources_transferred = self._transfer_user_resources(
                    tenant_id, user_id, transfer_to_user_id
                )
            
            # Perform cleanup
            cleanup_summary = self._cleanup_user_resources(tenant_id, user_id)
            
            now = datetime.utcnow().isoformat()
            
            if force_delete:
                # Hard delete - remove the record completely
                self.dynamodb_service.delete_item(
                    table_name=self.table_name,
                    key={
                        'pk': f'TENANT#{tenant_id}',
                        'sk': f'USER#{user_id}'
                    }
                )
                deletion_type = 'hard'
                can_be_restored = False
            else:
                # Soft delete - mark as deleted
                current_user['status'] = 'deleted'
                current_user['deleted_at'] = now
                current_user['deleted_by'] = deleted_by
                current_user['updated_at'] = now

                self.dynamodb_service.put_item(
                    item=current_user,
                    tenant_id=tenant_id
                )
                deletion_type = 'soft'
                can_be_restored = True
            
            # Log user deletion activity
            self._log_user_activity(
                tenant_id=tenant_id,
                user_id=user_id,
                activity_type='user_deleted',
                details={
                    'deleted_by': deleted_by,
                    'deletion_type': deletion_type,
                    'force_delete': force_delete,
                    'transfer_to_user_id': transfer_to_user_id,
                    'resources_transferred': resources_transferred,
                    'cleanup_summary': cleanup_summary
                }
            )
            
            lambda_logger.info("User deleted successfully", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'deleted_by': deleted_by,
                'deletion_type': deletion_type,
                'resources_transferred': resources_transferred
            })
            
            return {
                'deletion_type': deletion_type,
                'deleted_at': now,
                'deleted_by': deleted_by,
                'resources_transferred': resources_transferred,
                'cleanup_summary': cleanup_summary,
                'can_be_restored': can_be_restored,
                'cleanup_performed': list(cleanup_summary.keys())
            }
            
        except (ResourceNotFoundException, ValidationException):
            raise
        except Exception as e:
            lambda_logger.error("Failed to delete user", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'deleted_by': deleted_by,
                'error': str(e)
            })
            raise PlatformException(f"Failed to delete user: {str(e)}")
    
    def _format_user_data(self, user_record: Dict[str, Any]) -> Dict[str, Any]:
        """Format user data for API response with backward compatibility."""
        first_name = user_record.get('first_name', '')
        last_name = user_record.get('last_name', '')

        # Create full name for backward compatibility
        full_name = f"{first_name} {last_name}".strip()

        return {
            'user_id': user_record.get('user_id'),
            'tenant_id': user_record.get('tenant_id'),
            'email': user_record.get('email'),
            'first_name': first_name,
            'last_name': last_name,
            'name': full_name,  # Backward compatibility
            'role': user_record.get('role', 'user'),
            'status': user_record.get('status', 'active'),
            'permissions': user_record.get('permissions', []),
            'created_at': user_record.get('created_at'),
            'updated_at': user_record.get('updated_at'),
            'last_login': user_record.get('last_login'),
            'login_count': user_record.get('login_count', 0),
            'profile_picture': user_record.get('profile_picture'),
            'phone': user_record.get('phone'),
            'timezone': user_record.get('timezone'),
            'language': user_record.get('language', 'en'),
            'two_factor_enabled': user_record.get('two_factor_enabled', False)
        }
    
    def _get_user_recent_activity(self, tenant_id: str, user_id: str) -> List[Dict[str, Any]]:
        """Get recent activity for a user."""
        try:
            # Query user activity records
            response = self.dynamodb_service.query(
                table_name=self.table_name,
                key_condition_expression="pk = :pk AND begins_with(sk, :sk_prefix)",
                expression_attribute_values={
                    ':pk': f'TENANT#{tenant_id}',
                    ':sk_prefix': f'ACTIVITY#{user_id}#'
                },
                scan_index_forward=False,  # Most recent first
                limit=10
            )
            
            activities = []
            for item in response.get('Items', []):
                activities.append({
                    'activity_type': item.get('activity_type'),
                    'timestamp': item.get('timestamp'),
                    'details': item.get('details', {}),
                    'ip_address': item.get('ip_address'),
                    'user_agent': item.get('user_agent')
                })
            
            return activities
            
        except Exception as e:
            lambda_logger.warning("Failed to get user recent activity", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return []
    
    def _check_active_sessions(self, tenant_id: str, user_id: str) -> int:
        """Check for active user sessions."""
        try:
            # In a real implementation, this would check session storage
            # For now, return 0 (no active sessions)
            return 0
            
        except Exception as e:
            lambda_logger.warning("Failed to check active sessions", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return 0
    
    def _transfer_user_resources(self, tenant_id: str, from_user_id: str, to_user_id: str) -> int:
        """Transfer user resources to another user."""
        try:
            # In a real implementation, this would transfer:
            # - Owned documents/files
            # - Created projects
            # - Assigned tasks
            # - etc.
            
            # For now, return 0 (no resources transferred)
            lambda_logger.info("Resource transfer completed", extra={
                'tenant_id': tenant_id,
                'from_user_id': from_user_id,
                'to_user_id': to_user_id,
                'resources_transferred': 0
            })
            
            return 0
            
        except Exception as e:
            lambda_logger.error("Failed to transfer user resources", extra={
                'tenant_id': tenant_id,
                'from_user_id': from_user_id,
                'to_user_id': to_user_id,
                'error': str(e)
            })
            return 0
    
    def _cleanup_user_resources(self, tenant_id: str, user_id: str) -> Dict[str, int]:
        """Clean up user-related resources."""
        try:
            cleanup_summary = {
                'sessions_cleared': 0,
                'tokens_revoked': 0,
                'notifications_cleared': 0,
                'temp_files_deleted': 0
            }
            
            # In a real implementation, this would:
            # - Clear user sessions
            # - Revoke access tokens
            # - Clear notifications
            # - Delete temporary files
            # - Remove from caches
            
            lambda_logger.info("User resource cleanup completed", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'cleanup_summary': cleanup_summary
            })
            
            return cleanup_summary
            
        except Exception as e:
            lambda_logger.error("Failed to cleanup user resources", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'error': str(e)
            })
            return {}
    
    def _log_user_activity(self, tenant_id: str, user_id: str, activity_type: str, 
                          details: Dict[str, Any]) -> None:
        """Log user activity for audit purposes."""
        try:
            activity_id = str(uuid.uuid4())
            timestamp = datetime.utcnow().isoformat()
            
            self.dynamodb_service.put_item(
                item={
                    'pk': f'TENANT#{tenant_id}',
                    'sk': f'ACTIVITY#{user_id}#{timestamp}#{activity_id}',
                    'entity_type': 'user_activity',
                    'user_id': user_id,
                    'activity_type': activity_type,
                    'timestamp': timestamp,
                    'details': details,
                    'ttl': int((datetime.utcnow().timestamp() + (90 * 24 * 60 * 60)))  # 90 days TTL
                },
                tenant_id=tenant_id
            )
            
        except Exception as e:
            lambda_logger.warning("Failed to log user activity", extra={
                'tenant_id': tenant_id,
                'user_id': user_id,
                'activity_type': activity_type,
                'error': str(e)
            })
