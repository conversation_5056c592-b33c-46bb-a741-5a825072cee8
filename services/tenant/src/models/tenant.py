# services/tenant/src/models/tenant.py
# Unified Tenant model using shared layer models

"""
Tenant model that extends shared layer models for tenant service operations.
Uses shared.models as the source of truth for consistency across services.
"""

import time
import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime

# Import shared layer models (source of truth)
from shared.models import TenantInfo, TenantStatus, TenantPlan
from shared.database import db_client
from shared.exceptions import (
    ResourceNotFoundException,
    ResourceConflictException,
    ValidationException
)
from shared.logger import lambda_logger


class Tenant:
    """
    Extended Tenant model for tenant service operations.

    This model wraps shared.models.TenantInfo and adds tenant service
    specific functionality while maintaining consistency with shared models.
    """

    def __init__(
        self,
        tenant_id: str,
        name: str,
        status: TenantStatus = TenantStatus.TRIAL,
        plan: TenantPlan = TenantPlan.FREE,
        created_at: Optional[datetime] = None,
        trial_ends_at: Optional[datetime] = None,
        features: Optional[Dict[str, Any]] = None,
        # Extended fields specific to tenant service
        master_user_email: Optional[str] = None,
        subscription_id: Optional[str] = None,
        industry: Optional[str] = None,
        company_size: Optional[str] = None,
        country: Optional[str] = None,
        address: Optional[str] = None,
        phone: Optional[str] = None,
        website: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Initialize Tenant instance using shared models as base."""
        # Core tenant info using shared model
        self._tenant_info = TenantInfo(
            tenant_id=tenant_id,
            name=name,
            status=status,
            plan=plan,
            created_at=created_at or datetime.utcnow(),
            trial_ends_at=trial_ends_at,
            features=features or {}
        )

        # Extended fields specific to tenant service
        self.master_user_email = master_user_email
        self.subscription_id = subscription_id
        self.industry = industry
        self.company_size = company_size
        self.country = country
        self.address = address
        self.phone = phone
        self.website = website
        self.settings = settings or {}

        # Timestamps for database operations (int format for DynamoDB)
        self.created_at_timestamp = int(self._tenant_info.created_at.timestamp())
        self.updated_at_timestamp = int(time.time())
        self.trial_ends_at_timestamp = int(trial_ends_at.timestamp()) if trial_ends_at else None

        # Additional attributes from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

    # Delegate core properties to shared TenantInfo
    @property
    def tenant_id(self) -> str:
        return self._tenant_info.tenant_id

    @property
    def name(self) -> str:
        return self._tenant_info.name

    @name.setter
    def name(self, value: str):
        self._tenant_info.name = value
        self.updated_at_timestamp = int(time.time())

    @property
    def status(self) -> TenantStatus:
        return self._tenant_info.status

    @status.setter
    def status(self, value: TenantStatus):
        self._tenant_info.status = value
        self.updated_at_timestamp = int(time.time())

    @property
    def plan(self) -> TenantPlan:
        return self._tenant_info.plan

    @plan.setter
    def plan(self, value: TenantPlan):
        self._tenant_info.plan = value
        self.updated_at_timestamp = int(time.time())

    @property
    def created_at(self) -> datetime:
        return self._tenant_info.created_at

    @property
    def trial_ends_at(self) -> Optional[datetime]:
        return self._tenant_info.trial_ends_at

    @trial_ends_at.setter
    def trial_ends_at(self, value: Optional[datetime]):
        self._tenant_info.trial_ends_at = value
        self.trial_ends_at_timestamp = int(value.timestamp()) if value else None
        self.updated_at_timestamp = int(time.time())

    @property
    def features(self) -> Dict[str, Any]:
        return self._tenant_info.features or {}

    @features.setter
    def features(self, value: Dict[str, Any]):
        self._tenant_info.features = value
        self.updated_at_timestamp = int(time.time())

    # Delegate core methods to shared TenantInfo
    def is_active(self) -> bool:
        """Check if tenant is active."""
        return self._tenant_info.is_active()

    def is_trial_expired(self) -> bool:
        """Check if trial period has expired."""
        return self._tenant_info.is_trial_expired()

    # Extended methods specific to tenant service
    def activate(self) -> None:
        """Activate the tenant."""
        self.status = TenantStatus.ACTIVE
        self.save()

    def suspend(self) -> None:
        """Suspend the tenant."""
        self.status = TenantStatus.SUSPENDED
        self.save()

    def upgrade_plan(self, new_plan: TenantPlan) -> None:
        """Upgrade tenant plan."""
        self.plan = new_plan
        self.save()

    @classmethod
    def get_by_id(cls, tenant_id: str) -> Optional['Tenant']:
        """Get tenant by ID."""
        try:
            item = db_client.get_item(
                pk=f"TENANT#{tenant_id}",
                sk="PROFILE",
                tenant_id=tenant_id
            )

            if item:
                return cls._from_db_item(item)
            return None

        except Exception as e:
            lambda_logger.error("Failed to get tenant by ID", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None

    @classmethod
    def get_by_name(cls, name: str) -> Optional['Tenant']:
        """Get tenant by name."""
        try:
            # For now, return None to skip name validation
            # TODO: Implement proper GSI for name lookup
            lambda_logger.debug("Skipping tenant name validation - GSI not implemented", extra={
                'name': name
            })
            return None

        except Exception as e:
            lambda_logger.error("Failed to get tenant by name", extra={
                'name': name,
                'error': str(e)
            })
            return None

    def save(self) -> None:
        """Save tenant to database."""
        try:
            current_time = int(time.time())

            # Prepare tenant item for DynamoDB
            tenant_item = {
                'PK': f'TENANT#{self.tenant_id}',
                'SK': 'PROFILE',
                'entity_type': 'TENANT',
                'tenant_id': self.tenant_id,
                'name': self.name,
                'status': self.status.value,
                'plan': self.plan.value,
                'master_user_email': self.master_user_email,
                'created_at': self.created_at_timestamp,
                'updated_at': current_time,
                'trial_ends_at': self.trial_ends_at_timestamp,
                'subscription_id': self.subscription_id,
                'industry': self.industry,
                'company_size': self.company_size,
                'country': self.country,
                'address': self.address,
                'phone': self.phone,
                'website': self.website,
                'settings': self.settings
            }

            # Remove None values to keep DynamoDB clean
            tenant_item = {k: v for k, v in tenant_item.items() if v is not None}

            # Debug logging
            lambda_logger.info(f"Tenant service using table: {db_client.config['table_name']}")
            lambda_logger.info(f"Saving tenant with PK: {tenant_item['PK']}, SK: {tenant_item['SK']}")

            # Save tenant profile
            db_client.put_item(tenant_item, self.tenant_id)

            # Update timestamp
            self.updated_at_timestamp = current_time

            lambda_logger.debug("Tenant saved successfully", extra={
                'tenant_id': self.tenant_id,
                'name': self.name
            })

        except Exception as e:
            lambda_logger.error("Failed to save tenant", extra={
                'tenant_id': self.tenant_id,
                'name': self.name,
                'error': str(e)
            })
            raise

    @classmethod
    def _from_db_item(cls, item: Dict[str, Any]) -> 'Tenant':
        """Create Tenant instance from database item."""
        # Convert timestamps to datetime for shared model (handle Decimal from DynamoDB)
        created_at_timestamp = item.get('created_at', time.time())
        if hasattr(created_at_timestamp, '__float__'):  # Handle Decimal
            created_at_timestamp = float(created_at_timestamp)
        created_at = datetime.fromtimestamp(created_at_timestamp)

        trial_ends_at = None
        if item.get('trial_ends_at'):
            trial_ends_at_timestamp = item['trial_ends_at']
            if hasattr(trial_ends_at_timestamp, '__float__'):  # Handle Decimal
                trial_ends_at_timestamp = float(trial_ends_at_timestamp)
            trial_ends_at = datetime.fromtimestamp(trial_ends_at_timestamp)

        return cls(
            tenant_id=item['tenant_id'],
            name=item['name'],
            status=TenantStatus(item['status']),
            plan=TenantPlan(item['plan']),
            created_at=created_at,
            trial_ends_at=trial_ends_at,
            master_user_email=item.get('master_user_email'),
            subscription_id=item.get('subscription_id'),
            industry=item.get('industry'),
            company_size=item.get('company_size'),
            country=item.get('country'),
            address=item.get('address'),
            phone=item.get('phone'),
            website=item.get('website'),
            settings=item.get('settings', {})
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert tenant to dictionary representation."""
        # Use shared model's to_dict as base and extend
        base_dict = self._tenant_info.to_dict()

        # Add extended fields
        base_dict.update({
            'master_user_email': self.master_user_email,
            'subscription_id': self.subscription_id,
            'industry': self.industry,
            'company_size': self.company_size,
            'country': self.country,
            'address': self.address,
            'phone': self.phone,
            'website': self.website,
            'settings': self.settings,
            'updated_at': datetime.fromtimestamp(self.updated_at_timestamp).isoformat()
        })

        # Remove None values for clean API responses
        return {k: v for k, v in base_dict.items() if v is not None}

    def to_tenant_info(self) -> TenantInfo:
        """Convert to shared TenantInfo for cross-service communication."""
        return self._tenant_info


# Convenience functions for creating tenants
def create_tenant(
    company_name: str,
    industry: Optional[str] = None,
    company_size: Optional[str] = None,
    country: Optional[str] = None,
    address: Optional[str] = None,
    phone: Optional[str] = None,
    website: Optional[str] = None
) -> Tenant:
    """Create a new tenant with default settings."""
    tenant_id = str(uuid.uuid4())
    trial_ends_at = datetime.fromtimestamp(time.time() + (30 * 24 * 60 * 60))  # 30 days

    return Tenant(
        tenant_id=tenant_id,
        name=company_name,
        status=TenantStatus.TRIAL,
        plan=TenantPlan.FREE,
        trial_ends_at=trial_ends_at,
        industry=industry,
        company_size=company_size,
        country=country,
        address=address,
        phone=phone,
        website=website
    )