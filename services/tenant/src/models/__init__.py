# services/tenant/src/models/__init__.py
# Models package for tenant service

"""
Models package for tenant service.
Exports unified models that use shared layer as source of truth.
"""

from .tenant import Tenant, create_tenant

# Re-export shared models for convenience
from shared.models import TenantStatus, TenantPlan, TenantInfo

__all__ = [
    'Tenant',
    'create_tenant',
    'TenantStatus',
    'TenantPlan', 
    'TenantInfo'
]
