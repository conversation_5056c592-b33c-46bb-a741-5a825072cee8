#!/usr/bin/env python3
# services/tenant/src/handlers/invite_user.py
# User invitation handler with unified models and shared layer integration

"""
User invitation handler.
Handles inviting new users to a tenant with enterprise-grade security and monitoring.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    AuthorizationException,
    ResourceConflictException,
    BusinessLogicException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import UserRole
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


def validate_invite_user_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate user invitation request data.

    Args:
        data: Invitation data to validate

    Returns:
        Validated invitation data

    Raises:
        ValidationException: If validation fails
    """
    if not isinstance(data, dict):
        raise ValidationException("Request body must be a dictionary")

    # Required fields
    required_fields = ['email', 'first_name', 'last_name']
    for field in required_fields:
        if field not in data or not data[field]:
            raise ValidationException(f"Field '{field}' is required")

    # Use unified validation from shared layer
    from shared.validators import UnifiedUserValidator
    from shared.models import UserRole

    # Validate complete user data using unified validator
    is_valid, validation_errors = UnifiedUserValidator.validate_user_data(data)
    if not is_valid:
        raise ValidationException(f"Validation failed: {'; '.join(validation_errors)}")

    # Extract validated data
    email = data['email'].strip().lower()
    first_name = data['first_name'].strip()
    last_name = data['last_name'].strip()

    # Validate role using unified role hierarchy
    role_str = data.get('role', 'MEMBER').upper()
    try:
        role = UserRole(role_str)
    except ValueError:
        valid_roles = [role.value for role in UserRole]
        raise ValidationException(f"Invalid role. Must be one of: {', '.join(valid_roles)}")

    # Validate send_email (optional, defaults to True)
    send_email = data.get('send_email', True)
    if not isinstance(send_email, bool):
        raise ValidationException("Field 'send_email' must be boolean")

    return {
        'email': email,
        'first_name': first_name,
        'last_name': last_name,
        'role': role_str,
        'send_email': send_email
    }


@rate_limit(requests_per_minute=20)  # Reasonable limit for user invitations
@tenant_resilience("invite_user")
@measure_performance("tenant_invite_user")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Invite a new user to the tenant with enterprise-grade security and monitoring.

    POST /tenant/invite
    {
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "role": "MEMBER",
        "send_email": true
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/tenant/invite'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate invitation data
        validated_data = validate_invite_user_request(body)

        email = validated_data['email']
        first_name = validated_data['first_name']
        last_name = validated_data['last_name']
        role_str = validated_data['role']
        send_email = validated_data['send_email']

        # Convert role string to enum
        try:
            role = UserRole(role_str)
        except ValueError:
            raise ValidationException(
                f"Invalid role: {role_str}",
                error_code="INVALID_ROLE"
            )

        lambda_logger.info("User invitation request", extra={
            'tenant_id': tenant_id,
            'inviting_user_id': user_id,
            'invitee_email': email,
            'invitee_role': role.value,
            'request_id': request_id
        })

        # Check permissions (only MASTER and ADMIN can invite users)
        is_master = user_role == 'MASTER'
        is_admin = user_role == 'ADMIN'
        if not is_master and not is_admin:
            lambda_logger.warning("Insufficient permissions for user invitation", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_role': user_role,
                'is_master': is_master
            })
            raise AuthorizationException(
                "Only tenant masters and admins can invite users",
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Record security metrics for invitation attempt
        metrics_manager.security.record_security_event('user_invitation_attempt', 'info')

        # Audit log for invitation attempt
        audit_log(
            action='user_invitation_attempt',
            resource_type='user',
            resource_id=email,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            invitation_data={
                'email': email,
                'role': role_str,
                'send_email': send_email
            }
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Create user invitation (simplified for now - would integrate with auth service)
        invitation_result = {
            'invitation_id': f"inv_{tenant_id}_{email.replace('@', '_').replace('.', '_')}",
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'role': role.value,
            'tenant_id': tenant_id,
            'invited_by': user_id,
            'status': 'PENDING',
            'send_email': send_email,
            'created_at': lambda_logger.get_current_timestamp()
        }

        # Record successful invitation
        audit_log(
            action='user_invited',
            resource_type='user',
            resource_id=email,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            invitation_result=invitation_result
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/invite",
            method="POST"
        )

        # Prepare response data
        response_data = {
            'invitation': invitation_result,
            'message': f"Invitation sent to {email}" if send_email else f"Invitation created for {email}"
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            201,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("User invitation created successfully", extra={
            'tenant_id': tenant_id,
            'inviting_user_id': user_id,
            'invitee_email': email,
            'invitation_id': invitation_result['invitation_id']
        })

        return APIResponse.created(
            data=response_data,
            message="User invitation created successfully"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except ResourceConflictException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            409,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.conflict(
            message=e.message,
            error_code=e.error_code
        )

    except BusinessLogicException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            409,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.conflict(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invite',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during user invitation", extra={
            'error': str(e),
            'request_id': request_id,
            'tenant_id': tenant_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while inviting the user"
        )
