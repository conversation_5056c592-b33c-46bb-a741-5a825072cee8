#!/usr/bin/env python3
# services/tenant/src/handlers/update_settings.py
# Update tenant settings handler with unified models and shared layer integration

"""
Update tenant settings handler.
Handles updating tenant configuration and settings with enterprise-grade security and monitoring.
"""

import json
from datetime import datetime
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ValidationException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import audit_log, lambda_logger, log_api_request, log_api_response
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import TenantStatus, TenantPlan
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


def validate_tenant_settings(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate tenant settings data.

    Args:
        data: Settings data to validate

    Returns:
        Validated settings data

    Raises:
        ValidationException: If validation fails
    """
    if not isinstance(data, dict):
        raise ValidationException("Settings must be a dictionary")

    # Define allowed settings with validation
    allowed_settings = {
        'timezone': str,
        'date_format': str,
        'currency': str,
        'language': str,
        'notifications': dict,
        'features': dict,
        'branding': dict
    }

    validated_settings = {}

    for key, value in data.items():
        if key not in allowed_settings:
            raise ValidationException(f"Unknown setting: {key}")

        expected_type = allowed_settings[key]
        if not isinstance(value, expected_type):
            raise ValidationException(f"Setting '{key}' must be of type {expected_type.__name__}")

        # Additional validation for specific settings
        if key == 'timezone' and value:
            # Basic timezone validation (could be enhanced)
            if not isinstance(value, str) or len(value) > 50:
                raise ValidationException("Invalid timezone format")

        elif key == 'currency' and value:
            # Basic currency validation
            if not isinstance(value, str) or len(value) != 3:
                raise ValidationException("Currency must be a 3-letter code")

        elif key == 'notifications' and value:
            # Validate notifications structure
            if not isinstance(value, dict):
                raise ValidationException("Notifications must be a dictionary")

            for notif_key, notif_value in value.items():
                if notif_key in ['email_enabled', 'sms_enabled', 'push_enabled']:
                    if not isinstance(notif_value, bool):
                        raise ValidationException(f"Notification setting '{notif_key}' must be boolean")

        validated_settings[key] = value

    return validated_settings


@rate_limit(requests_per_minute=30)  # Reasonable limit for settings updates
@tenant_resilience("update_settings")
@measure_performance("tenant_update_settings")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Update tenant settings with enterprise-grade security and monitoring.

    PUT /tenant/settings
    {
        "settings": {
            "timezone": "UTC",
            "date_format": "YYYY-MM-DD",
            "currency": "USD",
            "language": "en",
            "notifications": {
                "email_enabled": true,
                "sms_enabled": false,
                "push_enabled": true
            },
            "features": {
                "advanced_analytics": true,
                "custom_branding": false
            }
        }
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'PUT'),
        event.get('path', '/tenant/settings'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate settings data
        settings_data = body.get('settings', {})
        if not settings_data:
            raise ValidationException("Settings data is required")

        validated_settings = validate_tenant_settings(settings_data)

        lambda_logger.info("Updating tenant settings", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'request_id': request_id,
            'settings_keys': list(validated_settings.keys())
        })

        # Check permissions (only MASTER can update settings)
        is_master = user_role == 'MASTER'
        if not is_master:
            lambda_logger.warning("Insufficient permissions for settings update", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_role': user_role,
                'is_master': is_master
            })
            raise AuthorizationException(
                "Only tenant masters can update settings",
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Record security metrics for settings access
        metrics_manager.security.record_security_event('tenant_settings_update_attempt', 'info')

        # Audit log for settings update attempt
        audit_log(
            action='tenant_settings_update_attempt',
            resource_type='tenant',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            changes=validated_settings
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Get current tenant
        tenant = tenant_service.get_tenant_by_id(tenant_id)

        if not tenant:
            raise ResourceNotFoundException(
                f"Tenant not found: {tenant_id}",
                error_code="TENANT_NOT_FOUND"
            )

        # Create a simple auth context object for the service
        class SimpleAuthContext:
            def __init__(self, user_id, tenant_id, role):
                self.user_id = user_id
                self.tenant_id = tenant_id
                self.role = role

            def is_master(self):
                return self.role == 'MASTER'

        simple_auth_context = SimpleAuthContext(user_id, tenant_id, user_role)

        # Update tenant settings using service layer
        updated_tenant = tenant_service.update_tenant_settings(
            simple_auth_context,
            validated_settings
        )

        # Prepare comprehensive response data
        response_data = {
            'tenant_id': updated_tenant.tenant_id,
            'settings': updated_tenant.settings,
            'updated_at': datetime.fromtimestamp(updated_tenant.updated_at_timestamp).isoformat() if updated_tenant.updated_at_timestamp else None,
            'plan_info': {
                'current_plan': updated_tenant.plan.value,
                'status': updated_tenant.status.value
            }
        }

        # Record successful settings update
        audit_log(
            action='tenant_settings_updated',
            resource_type='tenant',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            changes=validated_settings
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/settings",
            method="PUT"
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            200,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Tenant settings updated successfully", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'settings_updated': list(validated_settings.keys())
        })

        return APIResponse.success(
            data=response_data,
            message="Tenant settings updated successfully"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            404,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.not_found(
            message=e.message,
            error_code=e.error_code
        )

    except BusinessLogicException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            409,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.conflict(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'PUT',
            '/tenant/settings',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during tenant settings update", extra={
            'error': str(e),
            'tenant_id': tenant_id,
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while updating tenant settings"
        )
