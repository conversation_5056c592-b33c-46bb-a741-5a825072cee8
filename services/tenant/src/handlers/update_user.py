#!/usr/bin/env python3
# services/tenant/src/handlers/update_user.py
# Update user handler

"""
Update user handler for the tenant service.
Updates user information with proper authorization and validation.
"""

import json
import time
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.exceptions import ValidationException, AuthorizationException, PlatformException
from shared.validators import UnifiedUserValidator
from shared.models import UserRole, UserStatus

from ..services.user_management_service import UserManagementService


@rate_limit(requests_per_minute=30)  # Lower limit for user updates
@measure_performance("tenant_update_user")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Update user handler.
    
    PUT /tenant/users/{user_id}
    {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "permissions": ["read", "write"]
    }
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    # Get request context
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    start_time = time.time()
    target_user_id = event.get('pathParameters', {}).get('user_id', 'unknown')
    tenant_id = 'unknown'  # Initialize for error handling

    try:
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'PUT'),
            f"/tenant/users/{event.get('pathParameters', {}).get('user_id', 'unknown')}",
            request_id,
            tenant_id
        )

        # Extract auth context from API Gateway authorizer
        auth_context = event.get('requestContext', {}).get('authorizer', {})
        current_user_id = auth_context.get('user_id')
        tenant_id = auth_context.get('tenant_id')
        current_user_role = auth_context.get('role', 'MEMBER')

        if not current_user_id or not tenant_id:
            lambda_logger.warning("Missing auth context", extra={
                'auth_context': auth_context,
                'request_id': request_id
            })
            return APIResponse.error(
                message="Authentication context missing",
                status_code=401
            )

        # Validate user ID from path parameters
        if not target_user_id or target_user_id == 'unknown':
            return APIResponse.error(
                message="User ID is required in path",
                status_code=400
            )

        # Parse request body
        try:
            body = event.get('body', '{}')
            if isinstance(body, str):
                data = json.loads(body) if body else {}
            else:
                data = body or {}
        except json.JSONDecodeError:
            return APIResponse.error(
                message="Invalid JSON in request body",
                status_code=400
            )
        
        lambda_logger.info("Processing update user request", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'current_user_id': current_user_id,
            'target_user_id': target_user_id,
            'current_user_role': current_user_role,
            'update_fields': list(data.keys())
        })
        
        # Validate request data
        _validate_update_request(data, current_user_role, current_user_id, target_user_id)
        
        # Check authorization
        _check_update_authorization(current_user_role, current_user_id, target_user_id, data)
        
        # Update user with enhanced service that uses unified patterns
        user_service = UserManagementService()
        updated_user = user_service.update_user(tenant_id, target_user_id, data, current_user_id)

        # Note: ServiceCommunicationManager will handle cross-service sync in future versions
        
        # Audit log
        audit_log("user_updated", {
            'tenant_id': tenant_id,
            'updater_user_id': current_user_id,
            'target_user_id': target_user_id,
            'updater_role': current_user_role,
            'updated_fields': list(data.keys()),
            'previous_values': updated_user.get('previous_values', {}),
            'new_values': {k: v for k, v in data.items() if k != 'password'}
        })
        
        # Create success response
        response = APIResponse.success(
            data=updated_user['user_data'],
            message="User updated successfully"
        )
        
        # Log API response
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(
            lambda_logger,
            'PUT',
            f"/tenant/users/{target_user_id}",
            200,
            duration_ms,
            request_id,
            tenant_id
        )
        
        lambda_logger.info("Update user request completed", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'target_user_id': target_user_id,
            'updated_fields': list(data.keys())
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Update user validation failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'PUT', f"/tenant/users/{target_user_id}", 422, duration_ms, request_id, tenant_id)
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Update user authorization failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Access denied",
            details={'error': str(e)},
            status_code=403
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'PUT', f"/tenant/users/{target_user_id}", 403, duration_ms, request_id, tenant_id)
        return response
        
    except PlatformException as e:
        lambda_logger.error("Update user platform error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to update user",
            details={'error': str(e)},
            status_code=500
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'PUT', f"/tenant/users/{target_user_id}", 500, duration_ms, request_id, tenant_id)
        return response
        
    except Exception as e:
        lambda_logger.error("Update user unexpected error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'PUT', f"/tenant/users/{target_user_id}", 500, duration_ms, request_id, tenant_id)
        return response


def _validate_update_request(data: Dict[str, Any], current_user_role: str,
                           current_user_id: str, target_user_id: str) -> None:
    """Validate update user request data using unified validators."""
    if not data:
        raise ValidationException("Request body cannot be empty")

    # Use unified user validation
    from shared.validators import UnifiedUserValidator
    from shared.models import UserRole, UserStatus

    # Validate email if provided
    if 'email' in data:
        is_valid, error_msg = UnifiedUserValidator.validate_email_format(data['email'])
        if not is_valid:
            raise ValidationException(error_msg)

    # Validate role if provided using unified role hierarchy
    if 'role' in data:
        try:
            role = UserRole(data['role'])
            # Check if current user can assign this role
            current_role = UserRole(current_user_role)
            if not current_role.can_access(role):
                raise ValidationException(f"Insufficient permissions to assign role {role.value}")
        except ValueError:
            valid_roles = [role.value for role in UserRole]
            raise ValidationException(f"Invalid role. Valid roles: {valid_roles}")

    # Validate status if provided using unified status enum
    if 'status' in data:
        try:
            UserStatus(data['status'])
        except ValueError:
            valid_statuses = [status.value for status in UserStatus]
            raise ValidationException(f"Invalid status. Valid statuses: {valid_statuses}")
    
    # Validate permissions if provided
    if 'permissions' in data:
        if not isinstance(data['permissions'], list):
            raise ValidationException("Permissions must be a list")
        
        valid_permissions = ['read', 'write', 'delete', 'admin', 'invite_users', 'manage_billing']
        for permission in data['permissions']:
            if permission not in valid_permissions:
                raise ValidationException(f"Invalid permission: {permission}")
    
    # Validate name fields
    for field in ['first_name', 'last_name']:
        if field in data:
            if not isinstance(data[field], str) or len(data[field].strip()) == 0:
                raise ValidationException(f"{field} must be a non-empty string")
            if len(data[field]) > 50:
                raise ValidationException(f"{field} must be 50 characters or less")


def _check_update_authorization(current_user_role: str, current_user_id: str, 
                              target_user_id: str, data: Dict[str, Any]) -> None:
    """Check if user has authorization to perform the update."""
    
    # Users can only update their own basic profile information
    if current_user_id == target_user_id:
        # Self-update: only allow basic profile fields
        allowed_self_fields = ['first_name', 'last_name', 'email', 'password']
        for field in data.keys():
            if field not in allowed_self_fields:
                raise AuthorizationException(f"Cannot update field '{field}' on your own profile")
        return
    
    # Use unified role hierarchy for authorization
    from shared.models import UserRole
    try:
        current_role = UserRole(current_user_role)
        # Only ADMIN and higher can update other users
        if not current_role.can_access(UserRole.ADMIN):
            raise AuthorizationException("Insufficient permissions to update other users")

        # MASTER can update anyone, ADMIN have restrictions
        if current_role == UserRole.ADMIN:
            # Admins cannot change roles or update MASTER users
            if 'role' in data:
                raise AuthorizationException("Admins cannot change user roles")

            # Additional restrictions for admin users could be added here
    except ValueError:
        raise AuthorizationException("Invalid user role")
    
    # Role-specific restrictions using unified hierarchy
    if 'role' in data:
        try:
            new_role = UserRole(data['role'])
            current_role = UserRole(current_user_role)

            # Only MASTER can assign MASTER role
            if new_role == UserRole.MASTER and current_role != UserRole.MASTER:
                raise AuthorizationException("Only MASTER users can assign MASTER role")

            # Only MASTER can promote to ADMIN
            if new_role == UserRole.ADMIN and current_role != UserRole.MASTER:
                raise AuthorizationException("Only MASTER users can assign ADMIN role")
        except ValueError:
            raise AuthorizationException("Invalid role specified")
