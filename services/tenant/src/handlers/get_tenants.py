#!/usr/bin/env python3
# services/tenant/src/handlers/get_tenants.py
# Get tenants handler with unified models and shared layer integration

"""
Get tenants handler.
Handles retrieving tenant list for platform admin users with enterprise-grade security.
"""

import json
from typing import Any, Dict, Optional
from datetime import datetime

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ValidationException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import TenantPlan, TenantStatus
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


@rate_limit(requests_per_minute=30)  # Moderate limit for admin operations
@tenant_resilience("get_tenants")
@measure_performance("tenant_get_tenants")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get tenants list (platform admin only) with enterprise-grade security and monitoring.

    GET /tenant/list?page=1&limit=20&status=active&search=company&plan=PROFESSIONAL

    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    - status: Filter by tenant status (ACTIVE, INACTIVE, SUSPENDED)
    - search: Search by tenant name or domain
    - plan: Filter by plan (FREE, PROFESSIONAL, ENTERPRISE)
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/tenant/list'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Use unified role hierarchy - Only MASTER users can list all tenants
        from shared.models import UserRole
        try:
            current_role = UserRole(user_role)
            if current_role != UserRole.MASTER:
                lambda_logger.warning("Insufficient permissions for tenant listing", extra={
                    'user_id': user_id,
                    'tenant_id': tenant_id,
                    'user_role': user_role,
                    'required_role': 'MASTER'
                })
                raise AuthorizationException(
                    "Only MASTER users can list all tenants",
                    error_code="INSUFFICIENT_PERMISSIONS"
                )
        except ValueError:
            raise AuthorizationException("Invalid user role")

        # Parse and validate query parameters
        query_params = event.get('queryStringParameters') or {}

        try:
            page = max(1, int(query_params.get('page', '1')))
            limit = min(max(1, int(query_params.get('limit', '20'))), 100)  # Max 100 per page
        except ValueError:
            raise ValidationException("Invalid pagination parameters", error_code="INVALID_PAGINATION")

        status = query_params.get('status', 'all')
        search = query_params.get('search', '').strip()
        plan = query_params.get('plan', 'all')

        # Validate status filter
        if status != 'all':
            try:
                TenantStatus(status.upper())
            except ValueError:
                raise ValidationException(f"Invalid status filter: {status}", error_code="INVALID_STATUS_FILTER")

        # Validate plan filter
        if plan != 'all':
            try:
                TenantPlan(plan.upper())
            except ValueError:
                raise ValidationException(f"Invalid plan filter: {plan}", error_code="INVALID_PLAN_FILTER")

        lambda_logger.info("Listing all tenants (admin operation)", extra={
            'admin_user_id': user_id,
            'admin_tenant_id': tenant_id,
            'page': page,
            'limit': limit,
            'status_filter': status,
            'search': search,
            'plan_filter': plan,
            'request_id': request_id
        })

        # Record security metrics for admin tenant listing
        metrics_manager.security.record_security_event('admin_tenant_list_access', 'warning')

        # Audit log for admin tenant listing (sensitive operation)
        audit_log(
            action='admin_tenant_list_access',
            resource_type='tenants',
            resource_id='all',
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            query_params={
                'page': page,
                'limit': limit,
                'status_filter': status,
                'search': search,
                'plan_filter': plan
            }
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Generate comprehensive mock tenant data with different statuses for demonstration
        mock_tenants = [
            {
                'tenant_id': 'tnt_001',
                'name': 'Acme Corporation',
                'domain': 'acme.com',
                'plan': TenantPlan.ENTERPRISE.value,
                'status': TenantStatus.ACTIVE.value,
                'created_at': '2025-01-01T00:00:00Z',
                'user_count': 45,
                'last_activity': '2025-08-13T18:30:00Z',
                'billing_email': '<EMAIL>'
            },
            {
                'tenant_id': 'tnt_002',
                'name': 'Tech Startup Inc',
                'domain': 'techstartup.io',
                'plan': TenantPlan.PROFESSIONAL.value,
                'status': TenantStatus.ACTIVE.value,
                'created_at': '2025-02-15T00:00:00Z',
                'user_count': 12,
                'last_activity': '2025-08-13T17:45:00Z',
                'billing_email': '<EMAIL>'
            },
            {
                'tenant_id': 'tnt_003',
                'name': 'Small Business LLC',
                'domain': 'smallbiz.com',
                'plan': TenantPlan.FREE.value,
                'status': TenantStatus.TRIAL.value,
                'created_at': '2025-03-10T00:00:00Z',
                'user_count': 3,
                'last_activity': '2025-08-12T14:20:00Z',
                'billing_email': '<EMAIL>'
            },
            {
                'tenant_id': 'tnt_004',
                'name': 'Suspended Company',
                'domain': 'suspended.com',
                'plan': TenantPlan.PROFESSIONAL.value,
                'status': TenantStatus.SUSPENDED.value,
                'created_at': '2025-04-01T00:00:00Z',
                'user_count': 8,
                'last_activity': '2025-07-15T10:20:00Z',
                'billing_email': '<EMAIL>'
            },
            {
                'tenant_id': 'tnt_005',
                'name': 'Inactive Business',
                'domain': 'inactive.com',
                'plan': TenantPlan.FREE.value,
                'status': TenantStatus.INACTIVE.value,
                'created_at': '2025-05-01T00:00:00Z',
                'user_count': 0,
                'last_activity': '2025-06-01T09:00:00Z',
                'billing_email': '<EMAIL>'
            },
            {
                'tenant_id': 'tnt_006',
                'name': 'New Trial Company',
                'domain': 'newtrial.com',
                'plan': TenantPlan.FREE.value,
                'status': TenantStatus.TRIAL.value,
                'created_at': '2025-08-01T00:00:00Z',
                'user_count': 2,
                'last_activity': '2025-09-03T15:30:00Z',
                'billing_email': '<EMAIL>'
            }
        ]

        # Apply filters
        filtered_tenants = mock_tenants

        if status != 'all':
            filtered_tenants = [t for t in filtered_tenants if t['status'] == status.upper()]

        if plan != 'all':
            filtered_tenants = [t for t in filtered_tenants if t['plan'] == plan.upper()]

        if search:
            search_lower = search.lower()
            filtered_tenants = [
                t for t in filtered_tenants
                if search_lower in t['name'].lower() or search_lower in t['domain'].lower()
            ]

        # Apply pagination
        total_count = len(filtered_tenants)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_tenants = filtered_tenants[start_idx:end_idx]
        total_pages = (total_count + limit - 1) // limit

        # Prepare response data
        response_data = {
            'tenants': paginated_tenants,
            'pagination': {
                'page': page,
                'limit': limit,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': end_idx < total_count,
                'has_previous': page > 1
            },
            'filters': {
                'status': status,
                'search': search,
                'plan': plan
            },
            'summary': {
                'total_tenants': total_count,
                'status_distribution': {
                    'ACTIVE': len([t for t in filtered_tenants if t['status'] == 'ACTIVE']),
                    'TRIAL': len([t for t in filtered_tenants if t['status'] == 'TRIAL']),
                    'SUSPENDED': len([t for t in filtered_tenants if t['status'] == 'SUSPENDED']),
                    'INACTIVE': len([t for t in filtered_tenants if t['status'] == 'INACTIVE'])
                },
                'plans_distribution': {
                    'FREE': len([t for t in filtered_tenants if t['plan'] == 'FREE']),
                    'PROFESSIONAL': len([t for t in filtered_tenants if t['plan'] == 'PROFESSIONAL']),
                    'ENTERPRISE': len([t for t in filtered_tenants if t['plan'] == 'ENTERPRISE'])
                }
            }
        }

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/list",
            method="GET"
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/list',
            200,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Tenants listed successfully (admin operation)", extra={
            'admin_user_id': user_id,
            'tenants_returned': len(paginated_tenants),
            'total_tenants': total_count
        })

        return APIResponse.success(
            data=response_data,
            message=f"Retrieved {len(paginated_tenants)} of {total_count} tenants"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/list',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/list',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/list',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/list',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during tenants retrieval", extra={
            'error': str(e),
            'admin_user_id': user_id,
            'admin_tenant_id': tenant_id,
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while retrieving tenants"
        )
