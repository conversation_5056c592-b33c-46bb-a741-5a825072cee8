#!/usr/bin/env python3
# services/tenant/src/handlers/register.py
# Tenant registration handler with unified models and shared layer integration

"""
Tenant registration handler.
Creates a new tenant (company) as part of the registration flow.
Implements enterprise-grade security, validation, and monitoring using unified models.
"""

import json
from typing import Any, Dict, Optional

# Import shared layer utilities
from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import sanitize_string
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    ResourceConflictException,
    BusinessLogicException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import TenantStatus, TenantPlan

from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()

def _validate_tenant_registration_request(body: str) -> Dict[str, Any]:
    """Validate tenant registration request body."""
    try:
        data = json.loads(body) if isinstance(body, str) else body

        # Validate required fields
        if not data.get('company_name'):
            raise ValidationException("Company name is required")

        # Sanitize and validate company name
        company_name = sanitize_string(data['company_name'], max_length=100)
        if len(company_name) < 2:
            raise ValidationException("Company name must be at least 2 characters long")

        # Validate optional fields
        validated_data = {
            'company_name': company_name,
            'industry': sanitize_string(data.get('industry', ''), max_length=50) if data.get('industry') else None,
            'company_size': sanitize_string(data.get('company_size', ''), max_length=20) if data.get('company_size') else None,
            'country': sanitize_string(data.get('country', ''), max_length=50) if data.get('country') else None,
            'address': sanitize_string(data.get('address', ''), max_length=200) if data.get('address') else None,
            'phone': sanitize_string(data.get('phone', ''), max_length=20) if data.get('phone') else None,
            'website': sanitize_string(data.get('website', ''), max_length=100) if data.get('website') else None
        }

        return validated_data

    except json.JSONDecodeError:
        raise ValidationException("Invalid JSON format")


@rate_limit(requests_per_minute=5)  # Strict rate limiting for tenant registration
@tenant_resilience("tenant_register")
@measure_performance("tenant_register")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Register a new tenant (company) with enterprise-grade security and monitoring.

    POST /tenant/register
    {
        "company_name": "Acme Corp",
        "industry": "logistics",
        "company_size": "medium",
        "country": "Colombia",
        "address": "Calle 123 #45-67",
        "phone": "+57 ************",
        "website": "https://acme.com"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/tenant/register'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Validate request body using shared layer
        body = event.get('body', '{}')
        validated_data = _validate_tenant_registration_request(body)

        company_name = validated_data['company_name']

        lambda_logger.info("Tenant registration attempt", extra={
            'company_name': company_name,
            'client_ip': client_ip,
            'request_id': request_id
        })

        # Record registration attempt for security metrics
        metrics_manager.security.record_security_event('tenant_registration_attempt', 'info')

        # Audit log for registration attempt
        audit_log(
            action='tenant_registration_attempt',
            resource_type='tenant',
            resource_id=company_name,
            client_ip=client_ip,
            request_id=request_id
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Create tenant using service layer with unified models
        tenant = tenant_service.create_tenant(
            company_name=company_name,
            industry=validated_data.get('industry'),
            company_size=validated_data.get('company_size'),
            country=validated_data.get('country'),
            address=validated_data.get('address'),
            phone=validated_data.get('phone'),
            website=validated_data.get('website')
        )

        # Record successful registration metrics and audit
        metrics_manager.security.record_security_event('tenant_registration_successful', 'info')
        metrics_manager.business.record_user_registration(tenant.tenant_id, tenant.plan.value)

        audit_log(
            action='tenant_registration_successful',
            resource_type='tenant',
            resource_id=tenant.tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            changes={
                'company_name': company_name,
                'status': tenant.status.value,
                'plan': tenant.plan.value
            }
        )

        # Prepare response data using unified model
        response_data = tenant.to_dict()

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/register',
            201,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Tenant registration successful", extra={
            'tenant_id': tenant.tenant_id,
            'company_name': company_name
        })

        return APIResponse.created(
            data=response_data,
            message="Tenant registered successfully"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/register',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            validation_errors=getattr(e, 'validation_errors', []),
            error_code=e.error_code
        )

    except ResourceConflictException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/register',
            409,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.conflict(
            message=e.message,
            error_code=e.error_code
        )

    except BusinessLogicException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/register',
            409,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.conflict(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/register',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/register',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during tenant registration", extra={
            'error': str(e),
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while registering the tenant"
        )
