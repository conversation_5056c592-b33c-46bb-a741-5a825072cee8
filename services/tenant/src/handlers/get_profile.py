#!/usr/bin/env python3
# services/tenant/src/handlers/get_profile.py
# Get tenant profile handler with unified models and shared layer integration

"""
Get tenant profile handler.
Handles retrieving tenant profile information with enterprise-grade security and monitoring.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ResourceNotFoundException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import TenantStatus, TenantPlan
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


@rate_limit(requests_per_minute=60)  # Higher limit for profile reads
@tenant_resilience("get_profile")
@measure_performance("tenant_get_profile")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get tenant profile information with enterprise-grade security and monitoring.

    GET /tenant/profile

    Returns comprehensive tenant profile including:
    - Basic tenant information
    - Current plan and status
    - Usage statistics
    - User permissions
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/tenant/profile'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        lambda_logger.info("Getting tenant profile", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'request_id': request_id
        })

        # Record profile access for security metrics
        metrics_manager.security.record_security_event('tenant_profile_access', 'info')

        # Audit log for profile access
        audit_log(
            action='tenant_profile_access',
            resource_type='tenant',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Get tenant profile using unified models
        tenant = tenant_service.get_tenant_by_id(tenant_id)

        if not tenant:
            raise ResourceNotFoundException(
                f"Tenant not found: {tenant_id}",
                error_code="TENANT_NOT_FOUND"
            )

        # Prepare comprehensive response data
        response_data = {
            'tenant': tenant.to_dict(),
            'permissions': {
                'can_invite_users': user_role == 'MASTER',
                'can_modify_settings': user_role == 'MASTER',
                'can_view_billing': user_role == 'MASTER',
                'can_manage_users': user_role == 'MASTER',
                'can_export_data': user_role == 'MASTER'
            },
            'plan_info': {
                'current_plan': tenant.plan.value,
                'status': tenant.status.value,
                'trial_ends_at': tenant.trial_ends_at.isoformat() if tenant.trial_ends_at else None,
                'is_trial_expired': tenant.is_trial_expired(),
                'is_active': tenant.is_active()
            },
            'user_info': {
                'user_id': user_id,
                'is_master': user_role == 'MASTER',
                'tenant_id': tenant_id
            }
        }

        # Record successful access metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/profile",
            method="GET"
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/profile',
            200,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Tenant profile retrieved successfully", extra={
            'tenant_id': tenant_id,
            'user_id': user_id
        })

        return APIResponse.success(
            data=response_data,
            message="Tenant profile retrieved successfully"
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/profile',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/profile',
            404,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.not_found(
            message=e.message,
            error_code=e.error_code
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/profile',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/profile',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during tenant profile retrieval", extra={
            'error': str(e),
            'tenant_id': tenant_id,
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while retrieving tenant profile"
        )
