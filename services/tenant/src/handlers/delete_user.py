#!/usr/bin/env python3
# services/tenant/src/handlers/delete_user.py
# Delete user handler

"""
Delete user handler for the tenant service.
Removes a user from the tenant with proper authorization and cleanup.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.exceptions import ValidationException, AuthorizationException, PlatformException


from ..services.user_management_service import UserManagementService


@rate_limit(requests_per_minute=20)  # Lower rate limit for destructive operations
@measure_performance("tenant_delete_user")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Delete user handler.
    
    DELETE /tenant/users/{user_id}
    
    Query parameters:
    - force: boolean (optional) - Force deletion even if user has active sessions
    - transfer_to: string (optional) - User ID to transfer ownership of resources
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    
    try:
        # Log API request
        log_api_request(event, "delete_user")
        
        # Extract auth context from API Gateway authorizer
        auth_context = event.get('requestContext', {}).get('authorizer', {})
        current_user_id = auth_context.get('user_id')
        tenant_id = auth_context.get('tenant_id')
        current_user_role = auth_context.get('role', 'MEMBER')

        if not current_user_id or not tenant_id:
            lambda_logger.warning("Missing auth context", extra={
                'auth_context': auth_context,
                'request_id': request_id
            })
            return APIResponse.error(
                message="Authentication context missing",
                status_code=401
            )
        
        # Extract user ID from path parameters
        path_params = event.get('pathParameters') or {}
        target_user_id = path_params.get('user_id')
        
        if not target_user_id:
            raise ValidationException("User ID is required in path")
        
        # Extract query parameters
        query_params = event.get('queryStringParameters') or {}
        force_delete = query_params.get('force', 'false').lower() == 'true'
        transfer_to_user_id = query_params.get('transfer_to')
        
        lambda_logger.info("Processing delete user request", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'current_user_id': current_user_id,
            'target_user_id': target_user_id,
            'current_user_role': current_user_role,
            'force_delete': force_delete,
            'transfer_to_user_id': transfer_to_user_id
        })
        
        # Check authorization
        _check_delete_authorization(current_user_role, current_user_id, target_user_id)
        
        # Validate transfer_to user if specified
        if transfer_to_user_id:
            _validate_transfer_user(transfer_to_user_id, current_user_id, target_user_id)
        
        # Delete user
        user_service = UserManagementService()
        deletion_result = user_service.delete_user(
            tenant_id=tenant_id,
            user_id=target_user_id,
            deleted_by=current_user_id,
            force_delete=force_delete,
            transfer_to_user_id=transfer_to_user_id
        )
        
        # Audit log
        audit_log("user_deleted", {
            'tenant_id': tenant_id,
            'deleter_user_id': current_user_id,
            'deleted_user_id': target_user_id,
            'deleter_role': current_user_role,
            'force_delete': force_delete,
            'transfer_to_user_id': transfer_to_user_id,
            'deletion_type': deletion_result.get('deletion_type', 'soft'),
            'resources_transferred': deletion_result.get('resources_transferred', 0),
            'cleanup_performed': deletion_result.get('cleanup_performed', [])
        })
        
        # Create success response
        response = APIResponse.success(
            data={
                'user_id': target_user_id,
                'deletion_type': deletion_result.get('deletion_type', 'soft'),
                'resources_transferred': deletion_result.get('resources_transferred', 0),
                'transfer_to_user_id': transfer_to_user_id,
                'cleanup_summary': deletion_result.get('cleanup_summary', {}),
                'deleted_at': deletion_result.get('deleted_at'),
                'can_be_restored': deletion_result.get('can_be_restored', True)
            },
            message="User deleted successfully"
        )
        
        # Log API response
        log_api_response(response, "delete_user")
        
        lambda_logger.info("Delete user request completed", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'target_user_id': target_user_id,
            'deletion_type': deletion_result.get('deletion_type'),
            'resources_transferred': deletion_result.get('resources_transferred', 0)
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Delete user validation failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        log_api_response(response, "delete_user")
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Delete user authorization failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Access denied",
            details={'error': str(e)},
            status_code=403
        )
        log_api_response(response, "delete_user")
        return response
        
    except PlatformException as e:
        lambda_logger.error("Delete user platform error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to delete user",
            details={'error': str(e)},
            status_code=500
        )
        log_api_response(response, "delete_user")
        return response
        
    except Exception as e:
        lambda_logger.error("Delete user unexpected error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        log_api_response(response, "delete_user")
        return response


def _check_delete_authorization(current_user_role: str, current_user_id: str, 
                              target_user_id: str) -> None:
    """Check if user has authorization to delete the target user."""
    
    # Users cannot delete themselves
    if current_user_id == target_user_id:
        raise AuthorizationException("Users cannot delete their own account")
    
    # Use unified role hierarchy for authorization
    from shared.models import UserRole
    try:
        current_role = UserRole(current_user_role)
        # Only ADMIN and higher can delete users
        if not current_role.can_access(UserRole.ADMIN):
            raise AuthorizationException("Insufficient permissions to delete users")

        # Additional role-based restrictions could be added here
        # For example, admins might not be able to delete other admins
    except ValueError:
        raise AuthorizationException("Invalid user role")


def _validate_transfer_user(transfer_to_user_id: str, current_user_id: str, 
                          target_user_id: str) -> None:
    """Validate the transfer_to user ID."""
    
    if not transfer_to_user_id.strip():
        raise ValidationException("transfer_to user ID cannot be empty")
    
    if transfer_to_user_id == target_user_id:
        raise ValidationException("Cannot transfer resources to the user being deleted")
    
    if transfer_to_user_id == current_user_id:
        # This is allowed - user can transfer resources to themselves
        pass
    
    # Additional validation could include checking if the transfer_to user exists
    # and has appropriate permissions to receive the resources
