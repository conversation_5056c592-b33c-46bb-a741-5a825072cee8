#!/usr/bin/env python3
# services/tenant/src/handlers/export_data.py
# Export tenant data handler with unified models and shared layer integration

"""
Export tenant data handler.
Handles exporting tenant data for GDPR compliance and backup with enterprise-grade security.
"""

import json
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    ValidationException,
    AuthorizationException,
    PlatformException,
    ResourceNotFoundException
)
from shared.logger import audit_log, lambda_logger, log_api_request, log_api_response
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import TenantPlan, TenantStatus
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


def validate_export_data_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate export data request.

    Args:
        data: Export request data to validate

    Returns:
        Validated export data

    Raises:
        ValidationException: If validation fails
    """
    if not isinstance(data, dict):
        raise ValidationException("Request body must be a dictionary")

    # Validate export_type
    export_type = data.get('export_type', 'full')
    valid_export_types = ['full', 'users_only', 'settings_only', 'usage_only', 'minimal']
    if export_type not in valid_export_types:
        raise ValidationException(
            f"Invalid export_type. Must be one of: {', '.join(valid_export_types)}",
            error_code="INVALID_EXPORT_TYPE"
        )

    # Validate format
    format_type = data.get('format', 'json')
    valid_formats = ['json', 'csv', 'xml']
    if format_type not in valid_formats:
        raise ValidationException(
            f"Invalid format. Must be one of: {', '.join(valid_formats)}",
            error_code="INVALID_FORMAT"
        )

    # Validate boolean fields
    include_users = data.get('include_users', True)
    include_billing = data.get('include_billing', True)
    include_usage_stats = data.get('include_usage_stats', True)
    include_settings = data.get('include_settings', True)

    if not isinstance(include_users, bool):
        raise ValidationException("Field 'include_users' must be boolean")
    if not isinstance(include_billing, bool):
        raise ValidationException("Field 'include_billing' must be boolean")
    if not isinstance(include_usage_stats, bool):
        raise ValidationException("Field 'include_usage_stats' must be boolean")
    if not isinstance(include_settings, bool):
        raise ValidationException("Field 'include_settings' must be boolean")

    # Validate reason (optional but recommended for audit)
    reason = data.get('reason', 'Data export request')
    if not isinstance(reason, str) or len(reason.strip()) == 0:
        reason = 'Data export request'
    elif len(reason) > 500:
        raise ValidationException("Reason must be less than 500 characters")

    return {
        'export_type': export_type,
        'format': format_type,
        'include_users': include_users,
        'include_billing': include_billing,
        'include_usage_stats': include_usage_stats,
        'include_settings': include_settings,
        'reason': reason.strip()
    }


@rate_limit(requests_per_minute=2)  # Very restrictive for data export
@tenant_resilience("export_data")
@measure_performance("tenant_export_data")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Export tenant data with enterprise-grade security and monitoring.

    POST /tenant/export
    {
        "export_type": "full",
        "format": "json",
        "include_users": true,
        "include_billing": true,
        "include_usage_stats": true,
        "include_settings": true,
        "reason": "GDPR compliance request"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/tenant/export'),
        request_id=request_id
    )

    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate export data
        validated_data = validate_export_data_request(body)

        export_type = validated_data['export_type']
        format_type = validated_data['format']
        include_users = validated_data['include_users']
        include_billing = validated_data['include_billing']
        include_usage_stats = validated_data['include_usage_stats']
        include_settings = validated_data['include_settings']
        reason = validated_data['reason']

        lambda_logger.info("Data export request initiated", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'export_type': export_type,
            'format': format_type,
            'reason': reason,
            'request_id': request_id
        })

        # Check permissions (only MASTER can export data)
        is_master = user_role == 'MASTER'
        if not is_master:
            lambda_logger.warning("Insufficient permissions for data export", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_role': user_role,
                'is_master': is_master
            })
            raise AuthorizationException(
                "Only tenant masters can export data",
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Record security metrics for data export attempt
        metrics_manager.security.record_security_event('tenant_data_export_attempt', 'critical')

        # Audit log for data export attempt (critical security event)
        audit_log(
            action='tenant_data_export_attempt',
            resource_type='tenant_data',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            export_details={
                'export_type': export_type,
                'format': format_type,
                'include_users': include_users,
                'include_billing': include_billing,
                'include_usage_stats': include_usage_stats,
                'include_settings': include_settings,
                'reason': reason
            }
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Get tenant to verify it exists
        tenant = tenant_service.get_tenant_by_id(tenant_id)
        if not tenant:
            raise ResourceNotFoundException(
                f"Tenant not found: {tenant_id}",
                error_code="TENANT_NOT_FOUND"
            )

        # Generate export ID and simulate export process
        export_id = f"exp_{tenant_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        estimated_completion = datetime.utcnow() + timedelta(minutes=15)  # 15 minutes estimate

        # Create export result (in real implementation, this would trigger async processing)
        export_result = {
            'export_id': export_id,
            'status': 'initiated',
            'tenant_id': tenant_id,
            'export_type': export_type,
            'format': format_type,
            'estimated_completion': estimated_completion.isoformat(),
            'download_url': None,  # Will be populated when export completes
            'created_at': datetime.utcnow().isoformat(),
            'created_by': user_id,
            'reason': reason,
            'includes': {
                'users': include_users,
                'billing': include_billing,
                'usage_stats': include_usage_stats,
                'settings': include_settings
            }
        }

        # Record successful export initiation
        audit_log(
            action='tenant_data_export_initiated',
            resource_type='tenant_data',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            export_result=export_result
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/export",
            method="POST"
        )

        # Prepare response data
        response_data = {
            'export_id': export_result['export_id'],
            'status': export_result['status'],
            'estimated_completion': export_result['estimated_completion'],
            'download_url': export_result['download_url'],
            'export_details': {
                'export_type': export_type,
                'format': format_type,
                'includes': export_result['includes'],
                'reason': reason
            },
            'instructions': {
                'message': 'Export has been initiated. You will receive an email notification when the export is ready for download.',
                'check_status_endpoint': f'/tenant/export/{export_id}/status',
                'estimated_size': 'Will be calculated during processing'
            }
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/export',
            202,  # Accepted - processing asynchronously
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Data export initiated successfully", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'export_id': export_id,
            'export_type': export_type
        })

        return APIResponse.accepted(
            data=response_data,
            message="Data export initiated successfully"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/export',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/export',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/export',
            404,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.not_found(
            message=e.message,
            error_code=e.error_code
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/export',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/export',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during data export", extra={
            'error': str(e),
            'tenant_id': tenant_id,
            'user_id': user_id,
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred during data export"
        )
