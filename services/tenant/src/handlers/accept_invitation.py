#!/usr/bin/env python3
# services/tenant/src/handlers/accept_invitation.py
# Accept invitation handler with unified models and shared layer integration

"""
Accept invitation handler.
Handles accepting user invitations and creating user accounts with enterprise-grade security.
"""

import json
import re
from typing import Any, Dict, Optional
from datetime import datetime

from shared.responses import APIResponse, handle_cors_preflight
from shared.validators import validate_request_body
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ValidationException,
    ResourceNotFoundException,
    BusinessLogicException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
from shared.models import UserRole, UserStatus
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


def validate_accept_invitation_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate accept invitation request.

    Args:
        data: Invitation acceptance data to validate

    Returns:
        Validated invitation data

    Raises:
        ValidationException: If validation fails
    """
    if not isinstance(data, dict):
        raise ValidationException("Request body must be a dictionary")

    # Validate invitation_token (required)
    invitation_token = data.get('invitation_token')
    if not invitation_token:
        raise ValidationException("Field 'invitation_token' is required")
    if not isinstance(invitation_token, str) or len(invitation_token.strip()) == 0:
        raise ValidationException("Field 'invitation_token' must be a non-empty string")
    if len(invitation_token) < 10:
        raise ValidationException("Invalid invitation token format")

    # Validate password (required)
    password = data.get('password')
    if not password:
        raise ValidationException("Field 'password' is required")
    if not isinstance(password, str):
        raise ValidationException("Field 'password' must be a string")

    # Password strength validation
    if len(password) < 8:
        raise ValidationException("Password must be at least 8 characters long")
    if len(password) > 128:
        raise ValidationException("Password must be less than 128 characters")
    if not re.search(r'[A-Z]', password):
        raise ValidationException("Password must contain at least one uppercase letter")
    if not re.search(r'[a-z]', password):
        raise ValidationException("Password must contain at least one lowercase letter")
    if not re.search(r'\d', password):
        raise ValidationException("Password must contain at least one number")
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise ValidationException("Password must contain at least one special character")

    # Validate phone (optional)
    phone = data.get('phone')
    if phone is not None:
        if not isinstance(phone, str):
            raise ValidationException("Field 'phone' must be a string")
        # Basic phone validation (international format)
        phone_pattern = r'^\+[1-9]\d{1,14}$'
        if not re.match(phone_pattern, phone.strip()):
            raise ValidationException("Phone must be in international format (e.g., +**********)")
        phone = phone.strip()

    # Validate first_name and last_name (optional, for completing profile)
    first_name = data.get('first_name')
    if first_name is not None:
        if not isinstance(first_name, str) or len(first_name.strip()) == 0:
            raise ValidationException("Field 'first_name' must be a non-empty string")
        if len(first_name.strip()) > 50:
            raise ValidationException("Field 'first_name' must be less than 50 characters")
        first_name = first_name.strip()

    last_name = data.get('last_name')
    if last_name is not None:
        if not isinstance(last_name, str) or len(last_name.strip()) == 0:
            raise ValidationException("Field 'last_name' must be a non-empty string")
        if len(last_name.strip()) > 50:
            raise ValidationException("Field 'last_name' must be less than 50 characters")
        last_name = last_name.strip()

    return {
        'invitation_token': invitation_token.strip(),
        'password': password,
        'phone': phone,
        'first_name': first_name,
        'last_name': last_name
    }


@rate_limit(requests_per_minute=20)  # Reasonable limit for invitation acceptance
@tenant_resilience("accept_invitation")
@measure_performance("tenant_accept_invitation")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Accept a user invitation and create user account with enterprise-grade security.

    POST /tenant/invitation/accept
    {
        "invitation_token": "invitation_token_here",
        "password": "SecurePassword123!",
        "phone": "+**********",
        "first_name": "John",
        "last_name": "Doe"
    }
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/tenant/invitation/accept'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate request body
        try:
            body = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            raise ValidationException("Invalid JSON in request body")

        # Validate invitation acceptance data
        validated_data = validate_accept_invitation_request(body)

        invitation_token = validated_data['invitation_token']
        password = validated_data['password']
        phone = validated_data['phone']
        first_name = validated_data['first_name']
        last_name = validated_data['last_name']

        lambda_logger.info("Invitation acceptance attempt", extra={
            'client_ip': client_ip,
            'invitation_token': invitation_token[:10] + "...",  # Log partial token for security
            'has_phone': phone is not None,
            'has_names': first_name is not None and last_name is not None,
            'request_id': request_id
        })

        # Record security metrics for invitation acceptance attempt
        metrics_manager.security.record_security_event('invitation_acceptance_attempt', 'info')

        # Audit log for invitation acceptance attempt
        audit_log(
            action='invitation_acceptance_attempt',
            resource_type='invitation',
            resource_id=invitation_token[:10] + "...",
            client_ip=client_ip,
            request_id=request_id,
            invitation_details={
                'has_phone': phone is not None,
                'has_names': first_name is not None and last_name is not None
            }
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Simulate invitation validation and user creation
        # In real implementation, this would validate the token and create the user

        # Mock invitation validation
        if not invitation_token.startswith('inv_'):
            raise ResourceNotFoundException(
                "Invalid or expired invitation token",
                error_code="INVALID_INVITATION_TOKEN"
            )

        # Extract tenant_id from token (mock implementation)
        try:
            # Format: inv_tenantId_timestamp_randomString
            token_parts = invitation_token.split('_')
            if len(token_parts) < 3:
                raise ValueError("Invalid token format")
            tenant_id = token_parts[1]
        except (IndexError, ValueError):
            raise ResourceNotFoundException(
                "Invalid invitation token format",
                error_code="INVALID_INVITATION_TOKEN"
            )

        # Verify tenant exists
        tenant = tenant_service.get_tenant_by_id(tenant_id)
        if not tenant:
            raise ResourceNotFoundException(
                "Tenant not found for this invitation",
                error_code="TENANT_NOT_FOUND"
            )

        # Create new user (mock implementation)
        new_user_id = f"usr_{tenant_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        # Mock user creation result
        acceptance_result = {
            'user': {
                'user_id': new_user_id,
                'email': f'user{new_user_id[-8:]}@{tenant.name.lower()}.com',  # Mock email from invitation
                'first_name': first_name or 'New',
                'last_name': last_name or 'User',
                'phone': phone,
                'role': UserRole.MEMBER.value,
                'status': UserStatus.ACTIVE.value,
                'tenant_id': tenant_id,
                'created_at': datetime.utcnow().isoformat(),
                'last_login': None,
                'profile_completed': bool(first_name and last_name and phone)
            },
            'invitation_accepted': True,
            'access_token': f"jwt_token_for_{new_user_id}",  # Mock JWT token
            'refresh_token': f"refresh_token_for_{new_user_id}",
            'next_steps': [
                'Complete your profile if not done',
                'Explore the platform features',
                'Contact your tenant administrator for role-specific training'
            ]
        }

        # Record successful invitation acceptance
        audit_log(
            action='invitation_accepted',
            resource_type='user',
            resource_id=new_user_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=new_user_id,
            tenant_id=tenant_id,
            acceptance_result={
                'email': acceptance_result['user']['email'],
                'role': acceptance_result['user']['role'],
                'profile_completed': acceptance_result['user']['profile_completed']
            }
        )

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/invitation/accept",
            method="POST"
        )

        # Prepare response data (exclude sensitive tokens from logs)
        response_data = {
            'user': {
                'user_id': acceptance_result['user']['user_id'],
                'email': acceptance_result['user']['email'],
                'first_name': acceptance_result['user']['first_name'],
                'last_name': acceptance_result['user']['last_name'],
                'phone': acceptance_result['user']['phone'],
                'role': acceptance_result['user']['role'],
                'status': acceptance_result['user']['status'],
                'tenant_id': acceptance_result['user']['tenant_id'],
                'created_at': acceptance_result['user']['created_at'],
                'profile_completed': acceptance_result['user']['profile_completed']
            },
            'invitation_accepted': acceptance_result['invitation_accepted'],
            'access_token': acceptance_result['access_token'],
            'refresh_token': acceptance_result['refresh_token'],
            'next_steps': acceptance_result['next_steps'],
            'welcome_message': f"Welcome to {tenant.name}! Your account has been successfully created."
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invitation/accept',
            201,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Invitation accepted successfully", extra={
            'user_id': new_user_id,
            'tenant_id': tenant_id,
            'email': acceptance_result['user']['email'],
            'profile_completed': acceptance_result['user']['profile_completed']
        })

        return APIResponse.created(
            data=response_data,
            message="Invitation accepted successfully. Your account has been created and you are now logged in."
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invitation/accept',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invitation/accept',
            404,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.not_found(
            message=e.message,
            error_code=e.error_code
        )

    except BusinessLogicException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invitation/accept',
            409,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.conflict(
            message=e.message,
            error_code=e.error_code
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invitation/accept',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/tenant/invitation/accept',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during invitation acceptance", extra={
            'error': str(e),
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while accepting the invitation"
        )
