#!/usr/bin/env python3
# services/tenant/src/handlers/list_users.py
# List tenant users handler with unified models and shared layer integration

"""
List tenant users handler.
Handles listing users within a tenant with filtering, pagination, and enterprise-grade security.
"""

import json
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    AuthorizationException,
    ValidationException,
    ResourceNotFoundException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import UserRole, UserStatus
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


@rate_limit(requests_per_minute=60)  # Higher limit for user listing
@tenant_resilience("list_users")
@measure_performance("tenant_list_users")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    List users in the tenant with enterprise-grade security and monitoring.

    GET /tenant/users?page=1&page_size=20&status=ACTIVE&role=MEMBER

    Query Parameters:
    - page: Page number (default: 1)
    - page_size: Items per page (default: 20, max: 100)
    - status: Filter by user status (ACTIVE, INACTIVE, PENDING)
    - role: Filter by user role (MASTER, ADMIN, MEMBER, VIEWER)
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/tenant/users'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate query parameters
        query_params = event.get('queryStringParameters') or {}

        try:
            page = max(1, int(query_params.get('page', '1')))
            page_size = min(max(1, int(query_params.get('page_size', '20'))), 100)  # Max 100 per page
        except ValueError:
            raise ValidationException("Invalid pagination parameters", error_code="INVALID_PAGINATION")

        status_filter = query_params.get('status')
        role_filter = query_params.get('role')

        # Validate filters if provided
        if status_filter:
            try:
                UserStatus(status_filter.upper())
            except ValueError:
                raise ValidationException(f"Invalid status filter: {status_filter}", error_code="INVALID_STATUS_FILTER")

        if role_filter:
            try:
                UserRole(role_filter.upper())
            except ValueError:
                raise ValidationException(f"Invalid role filter: {role_filter}", error_code="INVALID_ROLE_FILTER")

        lambda_logger.info("Listing tenant users", extra={
            'tenant_id': tenant_id,
            'requesting_user_id': user_id,
            'page': page,
            'page_size': page_size,
            'status_filter': status_filter,
            'role_filter': role_filter,
            'request_id': request_id
        })

        # Check permissions (all authenticated users can list users, but with different visibility)
        is_master = user_role == 'MASTER'
        is_admin = user_role == 'ADMIN'
        can_view_all = is_master or is_admin

        # Record security metrics for user listing
        metrics_manager.security.record_security_event('tenant_users_list_access', 'info')

        # Audit log for user listing access
        audit_log(
            action='tenant_users_list_access',
            resource_type='users',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            query_params={
                'page': page,
                'page_size': page_size,
                'status_filter': status_filter,
                'role_filter': role_filter,
                'can_view_all': can_view_all
            }
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Get users from tenant service with filters
        try:
            users_result = tenant_service.list_tenant_users(
                tenant_id=tenant_id,
                page=page,
                page_size=page_size,
                status_filter=status_filter,
                role_filter=role_filter,
                requesting_user_id=user_id,
                can_view_all=can_view_all
            )

            if not users_result.success:
                raise PlatformException(
                    message=f"Failed to retrieve users: {users_result.error_message}",
                    error_code="USER_RETRIEVAL_FAILED"
                )

            filtered_users = users_result.data.get('users', [])
            total_count = users_result.data.get('total_count', 0)

        except Exception as e:
            lambda_logger.error(f"Error retrieving tenant users: {str(e)}")
            # Fallback to current user only if service fails
            filtered_users = [
                {
                    'user_id': user_id,
                    'email': f'user{user_id}@tenant.com',
                    'first_name': 'Current',
                    'last_name': 'User',
                    'role': 'MASTER',
                    'status': 'ACTIVE',
                    'created_at': '2025-08-13T19:00:00Z',
                    'last_login': '2025-08-13T19:30:00Z',
                    'is_current_user': True
                }
            ]
            total_count = len(filtered_users)

        # Users are already paginated by the service
        paginated_users = filtered_users

        # Prepare response data
        response_data = {
            'users': paginated_users,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size,
                'has_next': (page * page_size) < total_count,
                'has_previous': page > 1
            },
            'filters': {
                'status': status_filter,
                'role': role_filter
            },
            'permissions': {
                'can_view_all': can_view_all,
                'can_invite_users': is_master or is_admin,
                'can_manage_users': is_master
            }
        }

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/users",
            method="GET"
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/users',
            200,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Tenant users listed successfully", extra={
            'tenant_id': tenant_id,
            'requesting_user_id': user_id,
            'users_returned': len(paginated_users),
            'total_users': total_count
        })

        return APIResponse.success(
            data=response_data,
            message=f"Retrieved {len(paginated_users)} of {total_count} users"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/users',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/users',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/users',
            404,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.not_found(
            message=e.message,
            error_code=e.error_code
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/users',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/users',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error during user listing", extra={
            'error': str(e),
            'request_id': request_id,
            'tenant_id': tenant_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while listing users"
        )
