#!/usr/bin/env python3
# services/tenant/src/handlers/get_usage_stats.py
# Get usage statistics handler with unified models and shared layer integration

"""
Get usage statistics handler.
Handles retrieval of tenant usage statistics and metrics with enterprise-grade security.
"""

import json
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, tenant_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import (
    PlatformException,
    ResourceNotFoundException,
    AuthorizationException,
    ValidationException
)
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.dependency_injection import container
# Import directly from shared.models file (bypass package conflict)
import sys
sys.path.insert(0, '/opt/python')
from shared.models import TenantPlan, TenantStatus
from ..services.tenant_service import ITenantService
from ..config.dependencies import configure_dependencies

# Ensure dependencies are configured
configure_dependencies()


@rate_limit(requests_per_minute=100)  # Higher limit for read operations
@tenant_resilience("get_usage_stats")
@measure_performance("tenant_get_usage_stats")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get usage statistics for tenant with enterprise-grade security and monitoring.

    GET /tenant/usage?period=current_month&include_details=true&metric_type=api_calls

    Query Parameters:
    - period: Time period (current_month, last_month, current_year, last_year)
    - include_details: Include detailed breakdown (true/false)
    - metric_type: Specific metric type (api_calls, storage, users, features)
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()

    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

    # Extract auth context from API Gateway authorizer
    auth_context = event.get('requestContext', {}).get('authorizer', {})
    user_id = auth_context.get('user_id')
    tenant_id = auth_context.get('tenant_id')
    user_role = auth_context.get('role', 'MEMBER')

    if not user_id or not tenant_id:
        lambda_logger.warning("Missing auth context", extra={
            'auth_context': auth_context,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Authentication context missing",
            status_code=401
        )

    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/tenant/usage'),
        request_id=request_id
    )
    
    start_time = lambda_logger.get_current_timestamp()

    try:
        # Parse and validate query parameters
        query_params = event.get('queryStringParameters') or {}
        period = query_params.get('period', 'current_month')
        include_details = query_params.get('include_details', 'false').lower() == 'true'
        metric_type = query_params.get('metric_type')

        # Validate period parameter
        valid_periods = ['current_month', 'last_month', 'current_year', 'last_year', 'last_7_days', 'last_30_days']
        if period not in valid_periods:
            raise ValidationException(
                f"Invalid period. Must be one of: {', '.join(valid_periods)}",
                error_code="INVALID_PERIOD"
            )

        # Validate metric_type if provided
        if metric_type:
            valid_metrics = ['api_calls', 'storage', 'users', 'features', 'bandwidth']
            if metric_type not in valid_metrics:
                raise ValidationException(
                    f"Invalid metric_type. Must be one of: {', '.join(valid_metrics)}",
                    error_code="INVALID_METRIC_TYPE"
                )

        lambda_logger.info("Getting usage statistics", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'period': period,
            'include_details': include_details,
            'metric_type': metric_type,
            'request_id': request_id
        })

        # Check permissions (MASTER and ADMIN can view usage stats)
        is_master = user_role == 'MASTER'
        is_admin = user_role == 'ADMIN'
        if not is_master and not is_admin:
            lambda_logger.warning("Insufficient permissions for usage stats", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'user_role': user_role,
                'is_master': is_master,
                'is_admin': is_admin
            })
            raise AuthorizationException(
                "Only tenant masters and admins can view usage statistics",
                error_code="INSUFFICIENT_PERMISSIONS"
            )

        # Record security metrics for usage stats access
        metrics_manager.security.record_security_event('tenant_usage_stats_access', 'info')

        # Audit log for usage stats access
        audit_log(
            action='tenant_usage_stats_access',
            resource_type='usage_stats',
            resource_id=tenant_id,
            client_ip=client_ip,
            request_id=request_id,
            user_id=user_id,
            tenant_id=tenant_id,
            query_params={
                'period': period,
                'include_details': include_details,
                'metric_type': metric_type
            }
        )

        # Use dependency injection to get tenant service
        tenant_service = container.resolve(ITenantService)

        # Get tenant to determine plan limits
        tenant = tenant_service.get_tenant_by_id(tenant_id)
        if not tenant:
            raise ResourceNotFoundException(
                f"Tenant not found: {tenant_id}",
                error_code="TENANT_NOT_FOUND"
            )

        # Generate mock usage statistics based on plan
        current_time = datetime.utcnow()

        # Define plan limits
        plan_limits = {
            'FREE': {
                'api_calls_per_month': 10000,
                'storage_gb': 1,
                'users': 5,
                'features': ['basic_analytics', 'email_support']
            },
            'PROFESSIONAL': {
                'api_calls_per_month': 100000,
                'storage_gb': 10,
                'users': 25,
                'features': ['advanced_analytics', 'priority_support', 'custom_branding']
            },
            'ENTERPRISE': {
                'api_calls_per_month': 1000000,
                'storage_gb': 100,
                'users': 100,
                'features': ['enterprise_analytics', '24x7_support', 'custom_integrations', 'sla']
            }
        }

        current_plan_limits = plan_limits.get(tenant.plan.value, plan_limits['FREE'])

        # Generate mock usage data
        usage_stats = {
            'api_calls': {
                'current_period': 2500,
                'limit': current_plan_limits['api_calls_per_month'],
                'percentage': 25.0
            },
            'storage': {
                'current_gb': 0.3,
                'limit_gb': current_plan_limits['storage_gb'],
                'percentage': 30.0
            },
            'users': {
                'current_count': 1,
                'limit': current_plan_limits['users'],
                'percentage': 20.0 if current_plan_limits['users'] >= 5 else 100.0
            },
            'features': {
                'enabled': current_plan_limits['features'],
                'total_available': len(current_plan_limits['features'])
            }
        }

        # Calculate warnings
        warnings = []
        for metric, data in usage_stats.items():
            if metric != 'features' and 'percentage' in data:
                if data['percentage'] >= 90:
                    warnings.append({
                        'metric': metric,
                        'message': f"{metric.replace('_', ' ').title()} usage is at {data['percentage']:.1f}% of limit",
                        'severity': 'critical'
                    })
                elif data['percentage'] >= 75:
                    warnings.append({
                        'metric': metric,
                        'message': f"{metric.replace('_', ' ').title()} usage is at {data['percentage']:.1f}% of limit",
                        'severity': 'warning'
                    })

        # Prepare response data
        response_data = {
            'usage_statistics': usage_stats,
            'plan_limits': current_plan_limits,
            'warnings': warnings,
            'period': period,
            'period_start': (current_time - timedelta(days=30)).isoformat(),
            'period_end': current_time.isoformat(),
            'generated_at': current_time.isoformat(),
            'tenant_id': tenant_id,
            'plan': tenant.plan.value
        }

        # Add detailed breakdown if requested
        if include_details:
            response_data['detailed_breakdown'] = {
                'daily_api_calls': [
                    {'date': (current_time - timedelta(days=i)).strftime('%Y-%m-%d'), 'calls': 80 + (i * 5)}
                    for i in range(7, 0, -1)
                ],
                'feature_usage': {
                    feature: {'last_used': (current_time - timedelta(days=1)).isoformat(), 'usage_count': 15}
                    for feature in current_plan_limits['features']
                },
                'storage_breakdown': {
                    'documents': 0.15,
                    'images': 0.10,
                    'other': 0.05
                }
            }

        # Record business metrics
        metrics_manager.business.record_api_usage(
            tenant_id=tenant_id,
            endpoint="/tenant/usage",
            method="GET"
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/usage',
            200,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.info("Usage statistics retrieved successfully", extra={
            'tenant_id': tenant_id,
            'user_id': user_id,
            'period': period,
            'warnings_count': len(warnings)
        })

        return APIResponse.success(
            data=response_data,
            message="Usage statistics retrieved successfully"
        )

    except ValidationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/usage',
            422,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.validation_error(
            message=e.message,
            error_code=e.error_code,
            details=e.details
        )

    except AuthorizationException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/usage',
            403,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.forbidden(
            message=e.message,
            error_code=e.error_code
        )

    except ResourceNotFoundException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/usage',
            404,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.not_found(
            message=e.message,
            error_code=e.error_code
        )

    except PlatformException as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/usage',
            e.status_code,
            duration_ms,
            request_id=request_id
        )

        return APIResponse.error(
            message=e.message,
            status_code=e.status_code,
            error_code=e.error_code,
            details=e.details
        )

    except Exception as e:
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/tenant/usage',
            500,
            duration_ms,
            request_id=request_id
        )

        lambda_logger.error("Unexpected error retrieving usage statistics", extra={
            'error': str(e),
            'tenant_id': tenant_id,
            'user_id': user_id,
            'request_id': request_id,
            'client_ip': client_ip
        })

        return APIResponse.internal_server_error(
            message="An unexpected error occurred while retrieving usage statistics"
        )
