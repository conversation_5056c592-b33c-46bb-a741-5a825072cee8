#!/usr/bin/env python3
# services/tenant/src/handlers/get_user.py
# Get specific user handler

"""
Get specific user handler for the tenant service.
Retrieves detailed information about a specific user within the tenant.
"""

import json
import time
from typing import Any, Dict, Optional

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit
from shared.metrics import measure_performance
from shared.logger import lambda_logger, log_api_request, log_api_response, audit_log
from shared.exceptions import ValidationException, AuthorizationException, PlatformException


from ..services.user_management_service import UserManagementService


@rate_limit(requests_per_minute=100)
@measure_performance("tenant_get_user")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get specific user handler.
    
    GET /tenant/users/{user_id}
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    start_time = time.time()
    target_user_id = event.get('pathParameters', {}).get('user_id', 'unknown')
    tenant_id = 'unknown'  # Initialize for error handling

    try:
        # Log API request
        log_api_request(
            lambda_logger,
            event.get('httpMethod', 'GET'),
            f"/tenant/users/{target_user_id}",
            request_id,
            'unknown'  # Will be updated once we extract tenant_id
        )
        
        # Extract auth context from API Gateway authorizer
        auth_context = event.get('requestContext', {}).get('authorizer', {})
        current_user_id = auth_context.get('user_id')
        tenant_id = auth_context.get('tenant_id')
        current_user_role = auth_context.get('role', 'MEMBER')

        if not current_user_id or not tenant_id:
            lambda_logger.warning("Missing auth context", extra={
                'auth_context': auth_context,
                'request_id': request_id
            })
            return APIResponse.error(
                message="Authentication context missing",
                status_code=401
            )
        
        # Extract user ID from path parameters
        path_params = event.get('pathParameters') or {}
        target_user_id = path_params.get('user_id')
        
        if not target_user_id:
            raise ValidationException("User ID is required in path")
        
        lambda_logger.info("Processing get user request", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'current_user_id': current_user_id,
            'target_user_id': target_user_id,
            'current_user_role': current_user_role
        })
        
        # Check authorization - users can view their own profile, MASTER and ADMIN can view any user
        if target_user_id != current_user_id and current_user_role not in ['MASTER', 'ADMIN']:
            raise AuthorizationException("Insufficient permissions to view this user")
        
        # Get user information using enhanced service with unified validation
        user_service = UserManagementService()
        user_data = user_service.get_user(tenant_id, target_user_id)

        # Note: ServiceCommunicationManager is available for future async operations
        # For now, UserManagementService already uses unified patterns
        
        # Audit log
        audit_log("user_viewed", {
            'tenant_id': tenant_id,
            'viewer_user_id': current_user_id,
            'target_user_id': target_user_id,
            'viewer_role': current_user_role
        })
        
        # Create success response
        response = APIResponse.success(
            data=user_data,
            message="User information retrieved successfully"
        )
        
        # Log API response
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(
            lambda_logger,
            'GET',
            f"/tenant/users/{target_user_id}",
            200,
            duration_ms,
            request_id,
            tenant_id
        )
        
        lambda_logger.info("Get user request completed", extra={
            'request_id': request_id,
            'tenant_id': tenant_id,
            'target_user_id': target_user_id,
            'user_status': user_data.get('status')
        })
        
        return response
        
    except ValidationException as e:
        lambda_logger.warning("Get user validation failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Validation failed",
            details={'validation_errors': [str(e)]},
            status_code=422
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'GET', f"/tenant/users/{target_user_id}", 422, duration_ms, request_id, tenant_id)
        return response
        
    except AuthorizationException as e:
        lambda_logger.warning("Get user authorization failed", extra={
            'request_id': request_id,
            'error': str(e)
        })
        
        response = APIResponse.error(
            message="Access denied",
            details={'error': str(e)},
            status_code=403
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'GET', f"/tenant/users/{target_user_id}", 403, duration_ms, request_id, tenant_id)
        return response
        
    except PlatformException as e:
        lambda_logger.error("Get user platform error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })
        
        response = APIResponse.error(
            message="Failed to retrieve user information",
            details={'error': str(e)},
            status_code=500
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'GET', f"/tenant/users/{target_user_id}", 500, duration_ms, request_id, tenant_id)
        return response

    except Exception as e:
        lambda_logger.error("Get user unexpected error", extra={
            'request_id': request_id,
            'error': str(e),
            'error_type': type(e).__name__
        })

        response = APIResponse.error(
            message="Internal server error",
            details={'error': 'An unexpected error occurred'},
            status_code=500
        )
        duration_ms = (time.time() - start_time) * 1000
        log_api_response(lambda_logger, 'GET', f"/tenant/users/{target_user_id}", 500, duration_ms, request_id, tenant_id)
        return response
