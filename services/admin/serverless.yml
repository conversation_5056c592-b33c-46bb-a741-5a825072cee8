# services/admin/serverless.yml
# Admin service configuration

service: agent-scl-admin

# Custom configuration
custom:
  serviceName: admin
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

  # Load shared variables
  sharedVars: ${file(../../serverless/shared/variables.yml)}
  stageConfig: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Infrastructure references
  dynamodbTable: ${self:custom.stageConfig.dynamodbTable}
  sharedLayerArn:
    Fn::ImportValue: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Environment variables
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    REGION: ${self:custom.region}
    DYNAMODB_TABLE:
      Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableName
    
  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
        - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
        - Fn::Join:
            - ""
            - - Fn::ImportValue: ${self:custom.projectName}-${self:custom.stage}-DynamoDBTableArn
              - "/index/*"
    - Effect: Allow
      Action:
        - secretsmanager:GetSecretValue
        - secretsmanager:CreateSecret
        - secretsmanager:UpdateSecret
        - secretsmanager:DeleteSecret
        - secretsmanager:ListSecrets
      Resource: "*"
    - Effect: Allow
      Action:
        - cloudwatch:GetMetricStatistics
        - cloudwatch:ListMetrics
        - cloudwatch:GetMetricData
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"

# Functions
functions:
  # System analytics
  systemAnalytics:
    handler: src.handlers.system_analytics.handler
    timeout: ${self:custom.stageConfig.lambda.profiles.admin.timeout}
    memorySize: ${self:custom.stageConfig.lambda.profiles.admin.memorySize}
    # reservedConcurrency: ${self:custom.stageConfig.lambda.profiles.admin.reservedConcurrency}  # DISABLED - Account limit too low
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /admin/analytics
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: admin_system_analytics
    tracing: Active

  # Metrics dashboard
  metricsDashboard:
    handler: src.handlers.metrics_dashboard.handler
    timeout: 29
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /admin/metrics
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: admin_metrics_dashboard
    tracing: Active

  # Performance stats
  performanceStats:
    handler: src.handlers.performance_stats.handler
    timeout: 29
    memorySize: 512
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /admin/performance
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: admin_performance_stats
    tracing: Active

  # Resilience status
  resilienceStatus:
    handler: src.handlers.resilience_status.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /admin/resilience
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: admin_resilience_status
    tracing: Active

  # Secrets management
  secretsManagement:
    handler: src.handlers.secrets_management.handler
    timeout: 29
    memorySize: 256
    layers:
      - ${self:custom.sharedLayerArn}
    events:
      - http:
          path: /admin/secrets
          method: get
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
      - http:
          path: /admin/secrets
          method: post
          cors: true
 authorizer:
   name: jwtAuthorizer
   type: request
   arn:
     Fn::ImportValue: sls-${self:custom.projectName}-auth-${self:custom.stage}-JwtAuthorizerLambdaFunctionQualifiedArn
    environment:
      FUNCTION_NAME: admin_secrets_management
    tracing: Active

# Package configuration
package:
  patterns:
    - '!**'
    - 'src/**'
    - '!src/**/__pycache__/**'
    - '!src/**/*.pyc'

# Plugins
plugins:
  - serverless-python-requirements

# Resources
resources:
  Description: Admin service for Agent SCL platform
  Outputs:
    AdminServiceEndpoint:
      Description: "Admin Service API Gateway endpoint URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: ApiGatewayRestApi
            - ".execute-api."
            - ${self:custom.region}
            - ".amazonaws.com/"
            - ${self:custom.stage}
      Export:
        Name: sls-${self:custom.projectName}-admin-${self:custom.stage}-AdminServiceEndpoint
