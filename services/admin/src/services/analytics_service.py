# src/admin/services/analytics_service.py
# Implementado según "Admin Analytics Guidelines"

"""
Analytics service.
Provides comprehensive analytics and metrics for system administration.
"""

import time
from typing import Dict, Any, List
from datetime import datetime, timedelta

from shared.database import db_client
from shared.logger import lambda_logger


class AnalyticsService:
    """Service for system analytics and metrics."""
    
    def __init__(self):
        self.db_client = db_client
    
    async def get_system_analytics(
        self,
        period: str = '30d',
        metrics: List[str] = None,
        requesting_user_id: str = None
    ) -> Dict[str, Any]:
        """Get comprehensive system analytics."""
        try:
            if metrics is None or 'all' in metrics:
                metrics = ['overview', 'tenants', 'users', 'revenue', 'performance']
            
            current_time = int(time.time())
            period_seconds = self._parse_period(period)
            start_time = current_time - period_seconds
            
            analytics_data = {
                'period': period,
                'generated_at': current_time,
                'generated_by': requesting_user_id,
                'metrics': {}
            }
            
            # System overview metrics
            if 'overview' in metrics:
                analytics_data['metrics']['overview'] = await self._get_overview_metrics(
                    start_time, current_time
                )
            
            # Tenant metrics
            if 'tenants' in metrics:
                analytics_data['metrics']['tenants'] = await self._get_tenant_metrics(
                    start_time, current_time
                )
            
            # User metrics
            if 'users' in metrics:
                analytics_data['metrics']['users'] = await self._get_user_metrics(
                    start_time, current_time
                )
            
            # Revenue metrics
            if 'revenue' in metrics:
                analytics_data['metrics']['revenue'] = await self._get_revenue_metrics(
                    start_time, current_time
                )
            
            # Performance metrics
            if 'performance' in metrics:
                analytics_data['metrics']['performance'] = await self._get_performance_metrics(
                    start_time, current_time
                )
            
            lambda_logger.info("Generated system analytics", extra={
                'period': period,
                'metrics': metrics,
                'requesting_user': requesting_user_id
            })
            
            return analytics_data
            
        except Exception as e:
            lambda_logger.error("Failed to get system analytics", extra={
                'period': period,
                'metrics': metrics,
                'error': str(e)
            })
            raise
    
    async def _get_overview_metrics(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """Get system overview metrics."""
        try:
            # Get total counts
            total_tenants = await self._count_entities('tenant', start_time, end_time)
            total_users = await self._count_entities('user', start_time, end_time)
            total_subscriptions = await self._count_entities('subscription', start_time, end_time)
            
            # Get active counts
            active_tenants = await self._count_active_entities('tenant', start_time, end_time)
            active_users = await self._count_active_entities('user', start_time, end_time)
            active_subscriptions = await self._count_active_entities('subscription', start_time, end_time)
            
            return {
                'totals': {
                    'tenants': total_tenants,
                    'users': total_users,
                    'subscriptions': total_subscriptions
                },
                'active': {
                    'tenants': active_tenants,
                    'users': active_users,
                    'subscriptions': active_subscriptions
                },
                'growth_rates': {
                    'tenant_growth': await self._calculate_growth_rate('tenant', start_time, end_time),
                    'user_growth': await self._calculate_growth_rate('user', start_time, end_time),
                    'subscription_growth': await self._calculate_growth_rate('subscription', start_time, end_time)
                }
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get overview metrics", extra={'error': str(e)})
            return {}
    
    async def _get_tenant_metrics(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """Get tenant-specific metrics."""
        try:
            return {
                'total_tenants': await self._count_entities('tenant', start_time, end_time),
                'active_tenants': await self._count_active_entities('tenant', start_time, end_time),
                'new_tenants': await self._count_new_entities('tenant', start_time, end_time),
                'churned_tenants': await self._count_churned_entities('tenant', start_time, end_time),
                'tenant_distribution': await self._get_tenant_distribution(),
                'top_tenants_by_users': await self._get_top_tenants_by_users(),
                'top_tenants_by_revenue': await self._get_top_tenants_by_revenue()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant metrics", extra={'error': str(e)})
            return {}
    
    async def _get_user_metrics(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """Get user-specific metrics."""
        try:
            return {
                'total_users': await self._count_entities('user', start_time, end_time),
                'active_users': await self._count_active_entities('user', start_time, end_time),
                'new_users': await self._count_new_entities('user', start_time, end_time),
                'user_roles_distribution': await self._get_user_roles_distribution(),
                'user_activity_patterns': await self._get_user_activity_patterns(start_time, end_time),
                'average_users_per_tenant': await self._get_average_users_per_tenant()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get user metrics", extra={'error': str(e)})
            return {}
    
    async def _get_revenue_metrics(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """Get revenue metrics."""
        try:
            return {
                'total_revenue': await self._calculate_total_revenue(start_time, end_time),
                'monthly_recurring_revenue': await self._calculate_mrr(),
                'average_revenue_per_user': await self._calculate_arpu(),
                'revenue_by_plan': await self._get_revenue_by_plan(start_time, end_time),
                'revenue_growth': await self._calculate_revenue_growth(start_time, end_time),
                'churn_rate': await self._calculate_churn_rate(start_time, end_time)
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get revenue metrics", extra={'error': str(e)})
            return {}
    
    async def _get_performance_metrics(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """Get system performance metrics."""
        try:
            return {
                'api_requests': await self._count_api_requests(start_time, end_time),
                'average_response_time': await self._calculate_average_response_time(start_time, end_time),
                'error_rate': await self._calculate_error_rate(start_time, end_time),
                'uptime': await self._calculate_uptime(start_time, end_time),
                'database_performance': await self._get_database_performance_metrics(),
                'resource_utilization': await self._get_resource_utilization_metrics()
            }
            
        except Exception as e:
            lambda_logger.error("Failed to get performance metrics", extra={'error': str(e)})
            return {}
    
    def _parse_period(self, period: str) -> int:
        """Parse period string to seconds."""
        period_map = {
            '1d': 24 * 60 * 60,
            '7d': 7 * 24 * 60 * 60,
            '30d': 30 * 24 * 60 * 60,
            '90d': 90 * 24 * 60 * 60,
            '1y': 365 * 24 * 60 * 60
        }
        return period_map.get(period, 30 * 24 * 60 * 60)  # Default to 30 days
    
    async def _count_entities(self, entity_type: str, start_time: int, end_time: int) -> int:
        """Count entities of a specific type."""
        # Placeholder implementation
        return 100
    
    async def _count_active_entities(self, entity_type: str, start_time: int, end_time: int) -> int:
        """Count active entities of a specific type."""
        # Placeholder implementation
        return 85
    
    async def _count_new_entities(self, entity_type: str, start_time: int, end_time: int) -> int:
        """Count new entities created in the period."""
        # Placeholder implementation
        return 15
    
    async def _count_churned_entities(self, entity_type: str, start_time: int, end_time: int) -> int:
        """Count churned entities in the period."""
        # Placeholder implementation
        return 5
    
    async def _calculate_growth_rate(self, entity_type: str, start_time: int, end_time: int) -> float:
        """Calculate growth rate for entity type."""
        # Placeholder implementation
        return 12.5
    
    async def _get_tenant_distribution(self) -> Dict[str, int]:
        """Get tenant distribution by plan."""
        # Placeholder implementation
        return {'basic': 50, 'pro': 30, 'enterprise': 20}
    
    async def _get_top_tenants_by_users(self) -> List[Dict[str, Any]]:
        """Get top tenants by user count."""
        # Placeholder implementation
        return [
            {'tenant_id': 'tenant_1', 'name': 'Acme Corp', 'user_count': 150},
            {'tenant_id': 'tenant_2', 'name': 'Tech Solutions', 'user_count': 120}
        ]
    
    async def _get_top_tenants_by_revenue(self) -> List[Dict[str, Any]]:
        """Get top tenants by revenue."""
        # Placeholder implementation
        return [
            {'tenant_id': 'tenant_1', 'name': 'Acme Corp', 'revenue': 5000},
            {'tenant_id': 'tenant_2', 'name': 'Tech Solutions', 'revenue': 3500}
        ]
    
    async def _get_user_roles_distribution(self) -> Dict[str, int]:
        """Get user roles distribution."""
        # Placeholder implementation
        return {'MASTER': 25, 'ADMIN': 75, 'USER': 400}
    
    async def _get_user_activity_patterns(self, start_time: int, end_time: int) -> Dict[str, Any]:
        """Get user activity patterns."""
        # Placeholder implementation
        return {
            'daily_active_users': 350,
            'weekly_active_users': 450,
            'monthly_active_users': 500
        }
    
    async def _get_average_users_per_tenant(self) -> float:
        """Get average users per tenant."""
        # Placeholder implementation
        return 5.2
    
    async def _calculate_total_revenue(self, start_time: int, end_time: int) -> int:
        """Calculate total revenue in cents."""
        # Placeholder implementation
        return 125000  # $1,250.00
    
    async def _calculate_mrr(self) -> int:
        """Calculate Monthly Recurring Revenue."""
        # Placeholder implementation
        return 45000  # $450.00
    
    async def _calculate_arpu(self) -> int:
        """Calculate Average Revenue Per User."""
        # Placeholder implementation
        return 2500  # $25.00
    
    async def _get_revenue_by_plan(self, start_time: int, end_time: int) -> Dict[str, int]:
        """Get revenue breakdown by plan."""
        # Placeholder implementation
        return {'basic': 25000, 'pro': 60000, 'enterprise': 40000}
    
    async def _calculate_revenue_growth(self, start_time: int, end_time: int) -> float:
        """Calculate revenue growth rate."""
        # Placeholder implementation
        return 15.3
    
    async def _calculate_churn_rate(self, start_time: int, end_time: int) -> float:
        """Calculate churn rate."""
        # Placeholder implementation
        return 2.1
    
    async def _count_api_requests(self, start_time: int, end_time: int) -> int:
        """Count API requests in period."""
        # Placeholder implementation
        return 125000
    
    async def _calculate_average_response_time(self, start_time: int, end_time: int) -> float:
        """Calculate average API response time."""
        # Placeholder implementation
        return 245.5  # milliseconds
    
    async def _calculate_error_rate(self, start_time: int, end_time: int) -> float:
        """Calculate API error rate."""
        # Placeholder implementation
        return 0.5  # 0.5%
    
    async def _calculate_uptime(self, start_time: int, end_time: int) -> float:
        """Calculate system uptime."""
        # Placeholder implementation
        return 99.9
    
    async def _get_database_performance_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        # Placeholder implementation
        return {
            'average_query_time': 15.2,
            'slow_queries': 3,
            'connection_pool_usage': 65.5
        }
    
    async def _get_resource_utilization_metrics(self) -> Dict[str, Any]:
        """Get resource utilization metrics."""
        # Placeholder implementation
        return {
            'cpu_usage': 45.2,
            'memory_usage': 62.8,
            'storage_usage': 38.5
        }


# Global service instance
analytics_service = AnalyticsService()
