# src/admin/handlers/secrets_management.py
# Secrets management and rotation handler

"""
Admin handler for secrets management, rotation, and health monitoring.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, admin_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import PlatformException, ValidationException, AuthorizationException
from shared.logger import lambda_logger, log_api_request, log_api_response, log_security_event
from shared.auth import require_auth
from shared.secrets_manager import enhanced_secrets_manager


@require_auth
@rate_limit(requests_per_minute=20)  # Restrictive for secrets management
@admin_resilience("secrets_health")
@measure_performance("admin_secrets_health")
def secrets_health_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get health status of all secrets.
    
    GET /admin/secrets/health
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/admin/secrets/health'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Check permissions (only MASTER can manage secrets)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for secrets management", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to manage secrets",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get secrets health status
        health_reports = enhanced_secrets_manager.list_secrets_health()
        
        # Calculate overall health
        total_secrets = len(health_reports)
        healthy_secrets = len([r for r in health_reports if r.get('health_status') == 'healthy'])
        rotation_recommended = len([r for r in health_reports if r.get('health_status') == 'rotation_recommended'])
        rotation_required = len([r for r in health_reports if r.get('health_status') == 'rotation_required'])
        error_secrets = len([r for r in health_reports if r.get('health_status') == 'error'])
        
        overall_health = "healthy"
        if error_secrets > 0:
            overall_health = "critical"
        elif rotation_required > 0:
            overall_health = "warning"
        elif rotation_recommended > 0:
            overall_health = "attention"
        
        response_data = {
            'overall_health': overall_health,
            'summary': {
                'total_secrets': total_secrets,
                'healthy': healthy_secrets,
                'rotation_recommended': rotation_recommended,
                'rotation_required': rotation_required,
                'errors': error_secrets
            },
            'secrets': health_reports,
            'recommendations': _generate_recommendations(health_reports)
        }
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/admin/secrets/health",
            method="GET",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/secrets/health',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Secrets health status retrieved successfully"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to get secrets health", extra={
            'error': str(e),
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'request_id': request_id
        })
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/secrets/health',
            500,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.error(
            message="Failed to retrieve secrets health status",
            status_code=500,
            error_code="SECRETS_HEALTH_ERROR"
        )


@require_auth
@rate_limit(requests_per_minute=5)  # Very restrictive for secret rotation
@admin_resilience("rotate_secret")
@measure_performance("admin_rotate_secret")
def rotate_secret_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Rotate a specific secret.
    
    POST /admin/secrets/{secret_name}/rotate
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'POST'),
        event.get('path', '/admin/secrets/rotate'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Check permissions (only MASTER can rotate secrets)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for secret rotation", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to rotate secrets",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get secret name from path parameters
        path_parameters = event.get('pathParameters', {})
        secret_name = path_parameters.get('secret_name')
        
        if not secret_name:
            return APIResponse.error(
                message="Secret name is required",
                status_code=400,
                error_code="MISSING_SECRET_NAME"
            )
        
        # Parse request body
        try:
            body = json.loads(event.get('body', '{}'))
        except json.JSONDecodeError:
            return APIResponse.error(
                message="Invalid JSON in request body",
                status_code=400,
                error_code="INVALID_JSON"
            )
        
        new_value = body.get('new_value')
        if not new_value:
            return APIResponse.error(
                message="New secret value is required",
                status_code=400,
                error_code="MISSING_NEW_VALUE"
            )
        
        # Validate secret name format
        project_name = enhanced_secrets_manager.project_name
        environment = enhanced_secrets_manager.environment
        expected_prefix = f"{project_name}/{environment}/"
        
        if not secret_name.startswith(expected_prefix):
            return APIResponse.error(
                message=f"Secret name must start with {expected_prefix}",
                status_code=400,
                error_code="INVALID_SECRET_NAME"
            )
        
        # Rotate the secret
        success = enhanced_secrets_manager.rotate_secret(secret_name, new_value)
        
        if not success:
            return APIResponse.error(
                message="Failed to rotate secret",
                status_code=500,
                error_code="ROTATION_FAILED"
            )
        
        # Log security event for secret rotation
        log_security_event(
            lambda_logger,
            "secret_rotated",
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            secret_name=secret_name
        )

        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/admin/secrets/rotate",
            method="POST",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        response_data = {
            'secret_name': secret_name,
            'rotated_at': lambda_logger.get_current_timestamp(),
            'rotated_by': auth_context.user_id
        }

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/admin/secrets/rotate',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Secret rotated successfully"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to rotate secret", extra={
            'error': str(e),
            'secret_name': secret_name,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'request_id': request_id
        })
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'POST',
            '/admin/secrets/rotate',
            500,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.error(
            message="Failed to rotate secret",
            status_code=500,
            error_code="ROTATION_ERROR"
        )


def _generate_recommendations(health_reports: list) -> list:
    """Generate recommendations based on health reports."""
    recommendations = []
    
    for report in health_reports:
        secret_name = report.get('secret_name', '')
        health_status = report.get('health_status', '')
        age_days = report.get('age_days', 0)
        
        if health_status == 'rotation_required':
            recommendations.append({
                'type': 'critical',
                'secret_name': secret_name,
                'message': f"Secret {secret_name} is {age_days} days old and requires immediate rotation",
                'action': 'rotate_immediately'
            })
        elif health_status == 'rotation_recommended':
            recommendations.append({
                'type': 'warning',
                'secret_name': secret_name,
                'message': f"Secret {secret_name} is {age_days} days old and should be rotated soon",
                'action': 'schedule_rotation'
            })
        elif health_status == 'error':
            recommendations.append({
                'type': 'error',
                'secret_name': secret_name,
                'message': f"Secret {secret_name} has errors and needs attention",
                'action': 'investigate_error'
            })
    
    return recommendations
