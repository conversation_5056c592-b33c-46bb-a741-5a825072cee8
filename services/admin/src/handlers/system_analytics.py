# src/admin/handlers/system_analytics.py
# Implementado según "Admin Analytics Guidelines"

"""
System analytics handler.
Provides comprehensive system-wide analytics and metrics.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, admin_resilience
from shared.metrics import metrics_manager, measure_performance
from shared.exceptions import AuthorizationException
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from ..services.analytics_service import analytics_service


@require_auth
@rate_limit(requests_per_minute=60)  # Higher limit for analytics
@admin_resilience("system_analytics")
@measure_performance("admin_system_analytics")
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get system-wide analytics.

    GET /admin/analytics/system?period=30d&metrics=all
    """

    # GET requests don't need body validation
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/admin/analytics/system'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Check permissions (only MASTER can access system analytics)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for system analytics", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            raise AuthorizationException("Only system masters can access system analytics")
        
        # Parse query parameters
        query_params = event.get('queryStringParameters') or {}
        period = query_params.get('period', '30d')
        metrics = query_params.get('metrics', 'all').split(',')
        
        lambda_logger.info("Getting system analytics", extra={
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'period': period,
            'metrics': metrics
        })
        
        # Get system analytics
        import asyncio
        analytics_data = asyncio.run(analytics_service.get_system_analytics(
            period=period,
            metrics=metrics,
            requesting_user_id=auth_context.user_id
        ))
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/admin/analytics/system",
            method="GET",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/analytics/system',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=analytics_data,
            message="System analytics retrieved successfully"
        )
        
    except AuthorizationException as e:
        lambda_logger.warning("System analytics authorization error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id
        })
        return APIResponse.error(
            message=str(e),
            status_code=403,
            error_code="AUTHORIZATION_ERROR"
        )
        
    except Exception as e:
        lambda_logger.error("System analytics error", extra={
            'error': str(e),
            'tenant_id': auth_context.tenant_id,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        return APIResponse.error(
            message="Internal server error retrieving system analytics",
            status_code=500,
            error_code="SYSTEM_ANALYTICS_ERROR"
        )
