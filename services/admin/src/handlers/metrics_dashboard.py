# src/admin/handlers/metrics_dashboard.py
# Metrics dashboard handler for admin interface

"""
Admin handler for retrieving platform metrics and dashboard data
for monitoring and observability purposes.
"""

import json
import boto3
from typing import Any, Dict, List
from datetime import datetime, timedelta

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, admin_resilience
from shared.exceptions import PlatformException, ValidationException
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from shared.config import settings
from shared.metrics import metrics_manager, measure_performance


@require_auth
@rate_limit(requests_per_minute=60)  # Higher limit for dashboard
@admin_resilience("metrics_dashboard")
@measure_performance("admin_metrics_dashboard")
def metrics_dashboard_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get platform metrics dashboard data.
    
    GET /admin/metrics/dashboard
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/admin/metrics/dashboard'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Check permissions (only MASTER can view metrics)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for metrics dashboard", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to view metrics dashboard",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get query parameters
        query_params = event.get('queryStringParameters') or {}
        time_range = query_params.get('timeRange', '24h')  # 1h, 6h, 24h, 7d, 30d
        
        # Calculate time range
        end_time = datetime.utcnow()
        if time_range == '1h':
            start_time_range = end_time - timedelta(hours=1)
        elif time_range == '6h':
            start_time_range = end_time - timedelta(hours=6)
        elif time_range == '24h':
            start_time_range = end_time - timedelta(hours=24)
        elif time_range == '7d':
            start_time_range = end_time - timedelta(days=7)
        elif time_range == '30d':
            start_time_range = end_time - timedelta(days=30)
        else:
            start_time_range = end_time - timedelta(hours=24)
        
        # Get CloudWatch metrics
        cloudwatch = boto3.client('cloudwatch', region_name=settings.aws_region)
        namespace = f"AgentSCL/{settings.environment}"
        
        # Get business metrics
        business_metrics = _get_business_metrics(
            cloudwatch, namespace, start_time_range, end_time
        )
        
        # Get performance metrics
        performance_metrics = _get_performance_metrics(
            cloudwatch, namespace, start_time_range, end_time
        )
        
        # Get security metrics
        security_metrics = _get_security_metrics(
            cloudwatch, namespace, start_time_range, end_time
        )
        
        # Get Lambda metrics
        lambda_metrics = _get_lambda_metrics(
            cloudwatch, start_time_range, end_time
        )
        
        # Get system health
        system_health = _get_system_health()
        
        response_data = {
            'time_range': {
                'start': start_time_range.isoformat(),
                'end': end_time.isoformat(),
                'range': time_range
            },
            'business_metrics': business_metrics,
            'performance_metrics': performance_metrics,
            'security_metrics': security_metrics,
            'lambda_metrics': lambda_metrics,
            'system_health': system_health,
            'dashboard_urls': {
                'business_metrics': f"https://{settings.aws_region}.console.aws.amazon.com/cloudwatch/home?region={settings.aws_region}#dashboards:name={settings.project_name}-{settings.environment}-business-metrics",
                'performance_metrics': f"https://{settings.aws_region}.console.aws.amazon.com/cloudwatch/home?region={settings.aws_region}#dashboards:name={settings.project_name}-{settings.environment}-performance-metrics"
            }
        }
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/admin/metrics/dashboard",
            method="GET",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/metrics/dashboard',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Metrics dashboard data retrieved successfully"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to get metrics dashboard data", extra={
            'error': str(e),
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'request_id': request_id
        })
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/metrics/dashboard',
            500,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.error(
            message="Failed to retrieve metrics dashboard data",
            status_code=500,
            error_code="METRICS_DASHBOARD_ERROR"
        )


def _get_business_metrics(cloudwatch, namespace: str, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
    """Get business metrics from CloudWatch."""
    try:
        metrics = {}
        
        # Get tenant creation metrics
        tenant_created = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='TenantCreated',
            Dimensions=[{'Name': 'MetricType', 'Value': 'business'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,  # 1 hour
            Statistics=['Sum']
        )
        
        # Get user registration metrics
        user_registered = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='UserRegistered',
            Dimensions=[{'Name': 'MetricType', 'Value': 'business'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        # Get API usage metrics
        api_calls = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='APICall',
            Dimensions=[{'Name': 'MetricType', 'Value': 'business'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        metrics = {
            'tenant_created': _format_metric_data(tenant_created),
            'user_registered': _format_metric_data(user_registered),
            'api_calls': _format_metric_data(api_calls)
        }
        
        return metrics
        
    except Exception as e:
        lambda_logger.error("Failed to get business metrics", extra={'error': str(e)})
        return {}


def _get_performance_metrics(cloudwatch, namespace: str, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
    """Get performance metrics from CloudWatch."""
    try:
        # Get function duration metrics
        function_duration = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='FunctionDuration',
            Dimensions=[{'Name': 'MetricType', 'Value': 'performance'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Average', 'Maximum']
        )
        
        # Get database query duration metrics
        db_duration = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='DatabaseQueryDuration',
            Dimensions=[{'Name': 'MetricType', 'Value': 'performance'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Average', 'Maximum']
        )
        
        return {
            'function_duration': _format_metric_data(function_duration),
            'database_duration': _format_metric_data(db_duration)
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get performance metrics", extra={'error': str(e)})
        return {}


def _get_security_metrics(cloudwatch, namespace: str, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
    """Get security metrics from CloudWatch."""
    try:
        # Get login attempt metrics
        login_attempts = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='LoginAttempt',
            Dimensions=[{'Name': 'MetricType', 'Value': 'security'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        # Get failed authentication metrics
        auth_failed = cloudwatch.get_metric_statistics(
            Namespace=namespace,
            MetricName='AuthenticationFailed',
            Dimensions=[{'Name': 'MetricType', 'Value': 'security'}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        return {
            'login_attempts': _format_metric_data(login_attempts),
            'authentication_failed': _format_metric_data(auth_failed)
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get security metrics", extra={'error': str(e)})
        return {}


def _get_lambda_metrics(cloudwatch, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
    """Get Lambda function metrics."""
    try:
        # Get Lambda duration metrics
        lambda_duration = cloudwatch.get_metric_statistics(
            Namespace='AWS/Lambda',
            MetricName='Duration',
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Average', 'Maximum']
        )
        
        # Get Lambda error metrics
        lambda_errors = cloudwatch.get_metric_statistics(
            Namespace='AWS/Lambda',
            MetricName='Errors',
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        return {
            'duration': _format_metric_data(lambda_duration),
            'errors': _format_metric_data(lambda_errors)
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get Lambda metrics", extra={'error': str(e)})
        return {}


def _get_system_health() -> Dict[str, Any]:
    """Get overall system health status."""
    try:
        from shared.service_configuration import get_service_health
        
        service_health = get_service_health()
        
        return {
            'overall_status': service_health['status'],
            'services': service_health['services'],
            'timestamp': service_health['timestamp']
        }
        
    except Exception as e:
        lambda_logger.error("Failed to get system health", extra={'error': str(e)})
        return {
            'overall_status': 'unknown',
            'services': {},
            'timestamp': lambda_logger.get_current_timestamp()
        }


def _format_metric_data(metric_response: Dict[str, Any]) -> Dict[str, Any]:
    """Format CloudWatch metric response."""
    datapoints = metric_response.get('Datapoints', [])
    
    if not datapoints:
        return {
            'datapoints': [],
            'summary': {
                'total': 0,
                'average': 0,
                'maximum': 0,
                'minimum': 0
            }
        }
    
    # Sort by timestamp
    datapoints.sort(key=lambda x: x['Timestamp'])
    
    # Calculate summary statistics
    values = []
    for point in datapoints:
        if 'Sum' in point:
            values.append(point['Sum'])
        elif 'Average' in point:
            values.append(point['Average'])
        elif 'Maximum' in point:
            values.append(point['Maximum'])
    
    summary = {
        'total': sum(values) if values else 0,
        'average': sum(values) / len(values) if values else 0,
        'maximum': max(values) if values else 0,
        'minimum': min(values) if values else 0,
        'count': len(values)
    }
    
    return {
        'datapoints': datapoints,
        'summary': summary
    }
