# src/admin/handlers/resilience_status.py
# Resilience status and health monitoring handler

"""
Admin handler for monitoring resilience patterns status,
circuit breaker states, retry statistics, and bulkhead utilization.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, admin_resilience
from shared.exceptions import PlatformException, ValidationException, AuthorizationException
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from shared.metrics import metrics_manager, measure_performance
from shared.resilience import get_resilience_stats
from shared.circuit_breaker import get_all_circuit_breaker_stats
from shared.retry import get_all_retry_stats
from shared.bulkhead import get_all_bulkhead_stats


@require_auth
@rate_limit(requests_per_minute=30)  # Moderate limit for resilience status
@admin_resilience("resilience_status")
@measure_performance("admin_resilience_status")
def resilience_status_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get resilience patterns status and health.
    
    GET /admin/resilience/status
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/admin/resilience/status'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Check permissions (only MASTER can view resilience status)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for resilience status", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to view resilience status",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get all resilience statistics
        circuit_breaker_stats = get_all_circuit_breaker_stats()
        retry_stats = get_all_retry_stats()
        bulkhead_stats = get_all_bulkhead_stats()
        
        # Calculate overall health
        overall_health = _calculate_overall_health(
            circuit_breaker_stats,
            retry_stats,
            bulkhead_stats
        )
        
        # Get service-specific status
        service_status = _get_service_status(
            circuit_breaker_stats,
            retry_stats,
            bulkhead_stats
        )
        
        # Get recommendations
        recommendations = _generate_recommendations(
            circuit_breaker_stats,
            retry_stats,
            bulkhead_stats
        )
        
        response_data = {
            'overall_health': overall_health,
            'service_status': service_status,
            'circuit_breakers': circuit_breaker_stats,
            'retry_policies': retry_stats,
            'bulkheads': bulkhead_stats,
            'recommendations': recommendations,
            'timestamp': lambda_logger.get_current_timestamp()
        }
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/admin/resilience/status",
            method="GET",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/resilience/status',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Resilience status retrieved successfully"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to get resilience status", extra={
            'error': str(e),
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'request_id': request_id
        })
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/resilience/status',
            500,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.error(
            message="Failed to retrieve resilience status",
            status_code=500,
            error_code="RESILIENCE_STATUS_ERROR"
        )


def _calculate_overall_health(
    circuit_stats: Dict[str, Any],
    retry_stats: Dict[str, Any],
    bulkhead_stats: Dict[str, Any]
) -> Dict[str, Any]:
    """Calculate overall resilience health."""
    health_score = 100
    issues = []
    
    # Check circuit breakers
    open_circuits = 0
    for name, stats in circuit_stats.items():
        if stats['state'] == 'open':
            open_circuits += 1
            health_score -= 20
            issues.append(f"Circuit breaker '{name}' is OPEN")
        elif stats['failure_rate'] > 0.5:  # 50% failure rate
            health_score -= 10
            issues.append(f"Circuit breaker '{name}' has high failure rate: {stats['failure_rate']:.2%}")
    
    # Check retry policies
    for name, stats in retry_stats.items():
        if stats['success_rate'] < 0.8:  # Less than 80% success rate
            health_score -= 10
            issues.append(f"Retry policy '{name}' has low success rate: {stats['success_rate']:.2%}")
    
    # Check bulkheads
    for name, stats in bulkhead_stats.items():
        if stats['rejection_rate'] > 0.1:  # More than 10% rejection rate
            health_score -= 15
            issues.append(f"Bulkhead '{name}' has high rejection rate: {stats['rejection_rate']:.2%}")
        elif stats['utilization'] > 0.9:  # More than 90% utilization
            health_score -= 5
            issues.append(f"Bulkhead '{name}' has high utilization: {stats['utilization']:.2%}")
    
    # Determine health status
    if health_score >= 90:
        status = "healthy"
    elif health_score >= 70:
        status = "degraded"
    elif health_score >= 50:
        status = "warning"
    else:
        status = "critical"
    
    return {
        'status': status,
        'score': max(0, health_score),
        'open_circuits': open_circuits,
        'total_circuits': len(circuit_stats),
        'issues': issues
    }


def _get_service_status(
    circuit_stats: Dict[str, Any],
    retry_stats: Dict[str, Any],
    bulkhead_stats: Dict[str, Any]
) -> Dict[str, Any]:
    """Get status for each service."""
    services = {}
    
    # Group stats by service
    for name, stats in circuit_stats.items():
        service_name = name.replace('_circuit', '')
        if service_name not in services:
            services[service_name] = {}
        services[service_name]['circuit_breaker'] = stats
    
    for name, stats in retry_stats.items():
        service_name = name.replace('_retry', '')
        if service_name not in services:
            services[service_name] = {}
        services[service_name]['retry'] = stats
    
    for name, stats in bulkhead_stats.items():
        service_name = name.replace('_bulkhead', '')
        if service_name not in services:
            services[service_name] = {}
        services[service_name]['bulkhead'] = stats
    
    # Calculate service health
    for service_name, service_data in services.items():
        circuit = service_data.get('circuit_breaker', {})
        retry = service_data.get('retry', {})
        bulkhead = service_data.get('bulkhead', {})
        
        # Determine service status
        if circuit.get('state') == 'open':
            status = 'unavailable'
        elif (bulkhead.get('rejection_rate', 0) > 0.2 or 
              retry.get('success_rate', 1) < 0.5):
            status = 'degraded'
        elif (bulkhead.get('utilization', 0) > 0.8 or 
              circuit.get('failure_rate', 0) > 0.3):
            status = 'warning'
        else:
            status = 'healthy'
        
        service_data['overall_status'] = status
    
    return services


def _generate_recommendations(
    circuit_stats: Dict[str, Any],
    retry_stats: Dict[str, Any],
    bulkhead_stats: Dict[str, Any]
) -> list:
    """Generate recommendations based on resilience patterns status."""
    recommendations = []
    
    # Circuit breaker recommendations
    for name, stats in circuit_stats.items():
        if stats['state'] == 'open':
            recommendations.append({
                'type': 'critical',
                'component': 'circuit_breaker',
                'service': name,
                'message': f"Circuit breaker '{name}' is open. Check service health and consider manual reset if service is recovered.",
                'action': 'investigate_service_health'
            })
        elif stats['failure_rate'] > 0.3:
            recommendations.append({
                'type': 'warning',
                'component': 'circuit_breaker',
                'service': name,
                'message': f"Circuit breaker '{name}' has high failure rate ({stats['failure_rate']:.2%}). Monitor service closely.",
                'action': 'monitor_service'
            })
    
    # Retry recommendations
    for name, stats in retry_stats.items():
        if stats['success_rate'] < 0.7:
            recommendations.append({
                'type': 'warning',
                'component': 'retry',
                'service': name,
                'message': f"Retry policy '{name}' has low success rate ({stats['success_rate']:.2%}). Consider adjusting retry configuration.",
                'action': 'adjust_retry_config'
            })
        elif stats['average_delay'] > 10:
            recommendations.append({
                'type': 'info',
                'component': 'retry',
                'service': name,
                'message': f"Retry policy '{name}' has high average delay ({stats['average_delay']:.2f}s). Consider optimizing backoff strategy.",
                'action': 'optimize_backoff'
            })
    
    # Bulkhead recommendations
    for name, stats in bulkhead_stats.items():
        if stats['rejection_rate'] > 0.1:
            recommendations.append({
                'type': 'warning',
                'component': 'bulkhead',
                'service': name,
                'message': f"Bulkhead '{name}' has high rejection rate ({stats['rejection_rate']:.2%}). Consider increasing capacity.",
                'action': 'increase_capacity'
            })
        elif stats['utilization'] > 0.9:
            recommendations.append({
                'type': 'info',
                'component': 'bulkhead',
                'service': name,
                'message': f"Bulkhead '{name}' has high utilization ({stats['utilization']:.2%}). Monitor for potential capacity issues.",
                'action': 'monitor_capacity'
            })
    
    return recommendations


@require_auth
@measure_performance("admin_reset_circuit_breaker")
def reset_circuit_breaker_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Reset a specific circuit breaker.
    
    POST /admin/resilience/circuit-breaker/{circuit_name}/reset
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    try:
        # Check permissions (only MASTER can reset circuit breakers)
        if auth_context.role != 'MASTER':
            return APIResponse.error(
                message="Insufficient permissions to reset circuit breakers",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get circuit breaker name from path
        path_parameters = event.get('pathParameters', {})
        circuit_name = path_parameters.get('circuit_name')
        
        if not circuit_name:
            return APIResponse.error(
                message="Circuit breaker name is required",
                status_code=400,
                error_code="MISSING_CIRCUIT_NAME"
            )
        
        # Reset circuit breaker
        from shared.circuit_breaker import circuit_breaker_manager
        
        if circuit_name not in circuit_breaker_manager.circuit_breakers:
            return APIResponse.error(
                message=f"Circuit breaker '{circuit_name}' not found",
                status_code=404,
                error_code="CIRCUIT_BREAKER_NOT_FOUND"
            )
        
        circuit_breaker = circuit_breaker_manager.circuit_breakers[circuit_name]
        circuit_breaker.reset()
        
        lambda_logger.info("Circuit breaker reset", extra={
            'circuit_name': circuit_name,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })
        
        return APIResponse.success(
            data={
                'circuit_name': circuit_name,
                'reset_at': lambda_logger.get_current_timestamp(),
                'reset_by': auth_context.user_id
            },
            message=f"Circuit breaker '{circuit_name}' reset successfully"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to reset circuit breaker", extra={
            'error': str(e),
            'circuit_name': circuit_name,
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        
        return APIResponse.error(
            message="Failed to reset circuit breaker",
            status_code=500,
            error_code="CIRCUIT_BREAKER_RESET_ERROR"
        )
