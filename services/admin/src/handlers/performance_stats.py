# src/admin/handlers/performance_stats.py
# Performance statistics and optimization insights handler

"""
Admin handler for retrieving performance statistics, cache metrics,
lazy loading stats, and query optimization insights.
"""

import json
from typing import Any, Dict

from shared.responses import APIResponse, handle_cors_preflight
from shared.middleware.resilience_middleware import rate_limit, admin_resilience
from shared.exceptions import PlatformException, ValidationException, AuthorizationException
from shared.logger import lambda_logger, log_api_request, log_api_response
from shared.auth import require_auth
from shared.metrics import metrics_manager, measure_performance
from shared.cache import get_cache_stats
from shared.lazy_loading import get_lazy_loading_stats
from shared.query_optimizer import query_optimizer


@require_auth
@rate_limit(requests_per_minute=30)  # Moderate limit for admin performance stats
@admin_resilience("performance_stats")
@measure_performance("admin_performance_stats")
def performance_stats_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Get performance statistics and optimization insights.
    
    GET /admin/performance/stats
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    # Log API request
    log_api_request(
        lambda_logger,
        event.get('httpMethod', 'GET'),
        event.get('path', '/admin/performance/stats'),
        request_id=request_id,
        tenant_id=auth_context.tenant_id,
        user_id=auth_context.user_id
    )
    
    start_time = lambda_logger.get_current_timestamp()
    
    try:
        # Check permissions (only MASTER can view performance stats)
        if auth_context.role != 'MASTER':
            lambda_logger.warning("Insufficient permissions for performance stats", extra={
                'user_id': auth_context.user_id,
                'role': auth_context.role,
                'tenant_id': auth_context.tenant_id
            })
            return APIResponse.error(
                message="Insufficient permissions to view performance statistics",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Get cache statistics
        cache_stats = get_cache_stats()
        
        # Get lazy loading statistics
        lazy_loading_stats = get_lazy_loading_stats()
        
        # Get query optimizer statistics
        query_stats = query_optimizer.get_performance_stats()
        
        # Get Lambda performance metrics
        lambda_stats = _get_lambda_performance_stats(context)
        
        # Calculate performance insights
        insights = _generate_performance_insights(
            cache_stats,
            lazy_loading_stats,
            query_stats,
            lambda_stats
        )
        
        # Get optimization recommendations
        recommendations = _generate_optimization_recommendations(
            cache_stats,
            lazy_loading_stats,
            query_stats
        )
        
        response_data = {
            'cache_statistics': cache_stats,
            'lazy_loading_statistics': lazy_loading_stats,
            'query_statistics': query_stats,
            'lambda_statistics': lambda_stats,
            'performance_insights': insights,
            'optimization_recommendations': recommendations,
            'timestamp': lambda_logger.get_current_timestamp()
        }
        
        # Record metrics
        metrics_manager.record_api_call(
            endpoint="/admin/performance/stats",
            method="GET",
            status_code=200,
            tenant_id=auth_context.tenant_id
        )

        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/performance/stats',
            200,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )

        return APIResponse.success(
            data=response_data,
            message="Performance statistics retrieved successfully"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to get performance statistics", extra={
            'error': str(e),
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id,
            'request_id': request_id
        })
        
        duration_ms = lambda_logger.get_current_timestamp() - start_time
        log_api_response(
            lambda_logger,
            'GET',
            '/admin/performance/stats',
            500,
            duration_ms,
            tenant_id=auth_context.tenant_id,
            user_id=auth_context.user_id,
            request_id=request_id
        )
        
        return APIResponse.error(
            message="Failed to retrieve performance statistics",
            status_code=500,
            error_code="PERFORMANCE_STATS_ERROR"
        )


def _get_lambda_performance_stats(context: Any) -> Dict[str, Any]:
    """Get Lambda function performance statistics."""
    try:
        import psutil
        import os
        
        # Get memory usage
        memory_info = psutil.virtual_memory()
        
        # Get Lambda context information
        lambda_stats = {
            'function_name': context.function_name if context else 'unknown',
            'function_version': context.function_version if context else 'unknown',
            'memory_limit_mb': context.memory_limit_in_mb if context else 'unknown',
            'remaining_time_ms': context.get_remaining_time_in_millis() if context else 'unknown',
            'request_id': context.aws_request_id if context else 'unknown'
        }
        
        # Add system metrics
        lambda_stats.update({
            'memory_used_mb': round((memory_info.total - memory_info.available) / 1024 / 1024, 2),
            'memory_available_mb': round(memory_info.available / 1024 / 1024, 2),
            'memory_percent': memory_info.percent,
            'cpu_count': psutil.cpu_count(),
            'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else 'unavailable'
        })
        
        return lambda_stats
        
    except Exception as e:
        lambda_logger.warning("Failed to get Lambda performance stats", extra={'error': str(e)})
        return {
            'function_name': context.function_name if context else 'unknown',
            'error': 'Failed to collect performance metrics'
        }


def _generate_performance_insights(
    cache_stats: Dict[str, Any],
    lazy_loading_stats: Dict[str, Any],
    query_stats: Dict[str, Any],
    lambda_stats: Dict[str, Any]
) -> Dict[str, Any]:
    """Generate performance insights from statistics."""
    insights = {
        'overall_performance': 'good',
        'bottlenecks': [],
        'efficiency_metrics': {}
    }
    
    # Analyze cache performance
    memory_cache = cache_stats.get('memory_cache', {})
    cache_utilization = memory_cache.get('utilization', 0)
    
    if cache_utilization > 0.9:
        insights['bottlenecks'].append({
            'type': 'cache_pressure',
            'severity': 'high',
            'description': 'Memory cache utilization is very high',
            'impact': 'Cache evictions may reduce performance'
        })
        insights['overall_performance'] = 'degraded'
    elif cache_utilization > 0.7:
        insights['bottlenecks'].append({
            'type': 'cache_pressure',
            'severity': 'medium',
            'description': 'Memory cache utilization is high',
            'impact': 'Monitor for potential cache evictions'
        })
    
    # Analyze query performance
    if query_stats.get('total_queries', 0) > 0:
        cache_hit_rate = query_stats.get('cache_hit_rate', 0)
        avg_execution_time = query_stats.get('avg_execution_time_ms', 0)
        
        insights['efficiency_metrics']['query_cache_hit_rate'] = cache_hit_rate
        insights['efficiency_metrics']['avg_query_time_ms'] = avg_execution_time
        
        if cache_hit_rate < 0.5:
            insights['bottlenecks'].append({
                'type': 'low_cache_hit_rate',
                'severity': 'medium',
                'description': f'Query cache hit rate is low ({cache_hit_rate:.2%})',
                'impact': 'More database queries than necessary'
            })
        
        if avg_execution_time > 1000:  # 1 second
            insights['bottlenecks'].append({
                'type': 'slow_queries',
                'severity': 'high',
                'description': f'Average query time is high ({avg_execution_time:.2f}ms)',
                'impact': 'Slow response times for users'
            })
            insights['overall_performance'] = 'poor'
    
    # Analyze lazy loading performance
    if lazy_loading_stats:
        total_loads = sum(stats.get('total_loads', 0) for stats in lazy_loading_stats.values())
        total_cache_hits = sum(stats.get('cache_hits', 0) for stats in lazy_loading_stats.values())
        
        if total_loads > 0:
            lazy_cache_hit_rate = total_cache_hits / total_loads
            insights['efficiency_metrics']['lazy_loading_cache_hit_rate'] = lazy_cache_hit_rate
            
            if lazy_cache_hit_rate < 0.6:
                insights['bottlenecks'].append({
                    'type': 'lazy_loading_inefficiency',
                    'severity': 'medium',
                    'description': f'Lazy loading cache hit rate is low ({lazy_cache_hit_rate:.2%})',
                    'impact': 'Repeated expensive operations'
                })
    
    # Analyze Lambda performance
    if isinstance(lambda_stats.get('memory_percent'), (int, float)):
        memory_percent = lambda_stats['memory_percent']
        
        if memory_percent > 90:
            insights['bottlenecks'].append({
                'type': 'memory_pressure',
                'severity': 'high',
                'description': f'Memory usage is very high ({memory_percent:.1f}%)',
                'impact': 'Risk of out-of-memory errors'
            })
            insights['overall_performance'] = 'critical'
        elif memory_percent > 75:
            insights['bottlenecks'].append({
                'type': 'memory_pressure',
                'severity': 'medium',
                'description': f'Memory usage is high ({memory_percent:.1f}%)',
                'impact': 'Monitor for memory issues'
            })
    
    return insights


def _generate_optimization_recommendations(
    cache_stats: Dict[str, Any],
    lazy_loading_stats: Dict[str, Any],
    query_stats: Dict[str, Any]
) -> list:
    """Generate optimization recommendations."""
    recommendations = []
    
    # Cache optimization recommendations
    memory_cache = cache_stats.get('memory_cache', {})
    utilization = memory_cache.get('utilization', 0)
    
    if utilization > 0.8:
        recommendations.append({
            'type': 'cache_optimization',
            'priority': 'high',
            'title': 'Increase cache size or optimize cache usage',
            'description': 'Memory cache utilization is high. Consider increasing cache size or implementing cache eviction strategies.',
            'actions': [
                'Increase cache max_size configuration',
                'Implement LRU eviction policy',
                'Review cached data TTL settings'
            ]
        })
    
    # Query optimization recommendations
    if query_stats.get('cache_hit_rate', 1) < 0.7:
        recommendations.append({
            'type': 'query_optimization',
            'priority': 'medium',
            'title': 'Improve query caching strategy',
            'description': 'Query cache hit rate is below optimal. Review caching strategy for frequently accessed data.',
            'actions': [
                'Increase query result cache TTL',
                'Implement predictive caching',
                'Review query patterns for optimization'
            ]
        })
    
    if query_stats.get('avg_execution_time_ms', 0) > 500:
        recommendations.append({
            'type': 'query_optimization',
            'priority': 'high',
            'title': 'Optimize slow queries',
            'description': 'Average query execution time is high. Consider query optimization techniques.',
            'actions': [
                'Add database indexes for common queries',
                'Implement query result pagination',
                'Use projection expressions to reduce data transfer',
                'Consider read replicas for read-heavy workloads'
            ]
        })
    
    # Lazy loading optimization recommendations
    if lazy_loading_stats:
        properties_with_errors = [
            prop for prop, stats in lazy_loading_stats.items()
            if stats.get('errors', 0) > 0
        ]
        
        if properties_with_errors:
            recommendations.append({
                'type': 'lazy_loading_optimization',
                'priority': 'medium',
                'title': 'Fix lazy loading errors',
                'description': f'Some lazy-loaded properties have errors: {", ".join(properties_with_errors)}',
                'actions': [
                    'Review error logs for lazy loading failures',
                    'Implement fallback values for failed loads',
                    'Add retry logic for transient failures'
                ]
            })
    
    # General performance recommendations
    recommendations.append({
        'type': 'general_optimization',
        'priority': 'low',
        'title': 'Monitor performance trends',
        'description': 'Continuously monitor performance metrics to identify trends and potential issues.',
        'actions': [
            'Set up CloudWatch alarms for performance metrics',
            'Implement automated performance testing',
            'Review performance statistics regularly'
        ]
    })
    
    return recommendations


@require_auth
@measure_performance("admin_clear_cache")
def clear_cache_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Clear application caches.
    
    POST /admin/performance/clear-cache
    """
    
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    request_id = event.get('requestContext', {}).get('requestId', 'unknown')
    auth_context = event.get('auth_context')
    
    try:
        # Check permissions (only MASTER can clear caches)
        if auth_context.role != 'MASTER':
            return APIResponse.error(
                message="Insufficient permissions to clear caches",
                status_code=403,
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        cache_types = body.get('cache_types', ['memory'])  # Default to memory cache
        
        cleared_caches = []
        
        # Clear memory cache
        if 'memory' in cache_types:
            from shared.cache import cache_manager
            cache_manager.memory_cache.clear()
            cleared_caches.append('memory')
        
        # Clear lazy loading cache
        if 'lazy_loading' in cache_types:
            from shared.lazy_loading import lazy_loader
            # Reset lazy loader stats (cache is cleared automatically by TTL)
            lazy_loader.load_stats.clear()
            cleared_caches.append('lazy_loading')
        
        lambda_logger.info("Caches cleared", extra={
            'cleared_caches': cleared_caches,
            'user_id': auth_context.user_id,
            'tenant_id': auth_context.tenant_id
        })
        
        return APIResponse.success(
            data={
                'cleared_caches': cleared_caches,
                'cleared_at': lambda_logger.get_current_timestamp(),
                'cleared_by': auth_context.user_id
            },
            message=f"Caches cleared successfully: {', '.join(cleared_caches)}"
        )
        
    except Exception as e:
        lambda_logger.error("Failed to clear caches", extra={
            'error': str(e),
            'user_id': auth_context.user_id,
            'request_id': request_id
        })
        
        return APIResponse.error(
            message="Failed to clear caches",
            status_code=500,
            error_code="CACHE_CLEAR_ERROR"
        )
