# 🏗️ **ARQUITECTURA Y RESPONSABILIDADES DE SERVICIOS**

## **📋 SEPARACIÓN CLARA DE RESPONSABILIDADES**

### **🤖 AGENT SERVICE**
**Propósito**: Gestión de agentes externos (Feedo, Forecaster) y workflows complejos

#### **✅ RESPONSABILIDADES:**
- **Gestión de Agentes**: CRUD de agentes externos (Feedo, Forecaster)
- **Configuración de Webhooks**: Setup y gestión de webhooks de agentes
- **Comunicación con Agentes**: Envío de mensajes a agentes externos (N8N)
- **Procesamiento de Respuestas**: Recepción y procesamiento de respuestas de agentes
- **Conversaciones con Agentes**: Gestión de conversaciones usuario-agente
- **Procesamiento de Documentos**: Manejo de archivos y documentos complejos
- **Workflows de Negocio**: Lógica específica de agentes y procesos complejos

#### **🔗 ENDPOINTS:**
```
POST   /agents                           # Crear agente
GET    /agents                           # Listar agentes
GET    /agents/{agentId}                 # Obtener agente
PUT    /agents/{agentId}                 # Actualizar agente
DELETE /agents/{agentId}                 # Eliminar agente
POST   /conversations/{conversationId}/messages  # Enviar mensaje a AGENTE
GET    /conversations/{conversationId}/messages  # Obtener mensajes con agente
```

#### **📁 ESTRUCTURA:**
```
services/agent/
├── src/
│   ├── handlers/
│   │   ├── create_agent.py          # ✅ CRUD de agentes
│   │   ├── list_agents.py           # ✅ CRUD de agentes
│   │   ├── get_agent.py             # ✅ CRUD de agentes
│   │   ├── update_agent.py          # ✅ CRUD de agentes
│   │   ├── delete_agent.py          # ✅ CRUD de agentes
│   │   ├── send_message.py          # ✅ Mensajes a AGENTES
│   │   └── get_messages.py          # ✅ Historial con agentes
│   └── services/
│       ├── database_service.py      # ✅ Persistencia de agentes
│       ├── webhook_service.py       # ✅ Comunicación con N8N
│       └── agent_processor.py       # ✅ Lógica de agentes
```

---

### **💬 CHAT SERVICE**
**Propósito**: Mensajería en tiempo real entre usuarios (NO agentes)

#### **✅ RESPONSABILIDADES:**
- **Mensajes de Chat**: Comunicación directa usuario-usuario
- **Historial de Chat**: Persistencia de conversaciones de chat
- **Estados de Mensaje**: Enviado/leído/entregado para chat
- **Conversaciones de Chat**: Gestión de chats entre usuarios
- **Notificaciones de Chat**: Alertas de nuevos mensajes de chat
- **Integración WebSocket**: Tiempo real para chat

#### **🔗 ENDPOINTS:**
```
POST   /chat/messages                    # Enviar mensaje de CHAT (usuario-usuario)
GET    /chat/conversations              # Listar conversaciones de chat
GET    /chat/conversations/{id}/messages # Obtener mensajes de chat
PUT    /chat/messages/{id}/read         # Marcar mensaje como leído
```

#### **📁 ESTRUCTURA:**
```
services/chat/
├── src/
│   ├── handlers/
│   │   ├── send_chat_message.py     # ✅ Mensajes usuario-usuario
│   │   ├── get_messages.py          # ✅ Historial de chat
│   │   ├── mark_as_read.py          # ✅ Estados de mensaje
│   │   └── get_conversations.py     # ✅ Conversaciones de chat
│   └── services/
│       ├── message_service.py       # ✅ Persistencia de chat
│       ├── notification_service.py  # ✅ Notificaciones de chat
│       └── hybrid_router.py         # ❌ MOVER A AGENT SERVICE
```

---

### **🔌 WEBSOCKET SERVICE**
**Propósito**: Infraestructura de tiempo real (NO lógica de negocio)

#### **✅ RESPONSABILIDADES:**
- **Gestión de Conexiones**: Conectar/desconectar WebSocket
- **Broadcasting**: Envío masivo de mensajes
- **Presencia de Usuarios**: Online/offline/typing
- **Autenticación WebSocket**: Validación de conexiones
- **Rate Limiting**: Control de frecuencia de mensajes
- **Health Monitoring**: Salud de conexiones WebSocket

#### **🔗 ENDPOINTS:**
```
WebSocket: wss://api.domain.com/ws
Routes:
  $connect                            # ✅ Conectar WebSocket
  $disconnect                         # ✅ Desconectar WebSocket
  $default                            # ✅ Manejar mensajes WebSocket
  ping                                # ✅ Health check
```

#### **📁 ESTRUCTURA:**
```
services/websocket/
├── src/
│   ├── handlers/
│   │   ├── connect.py               # ✅ Gestión de conexiones
│   │   ├── disconnect.py            # ✅ Gestión de conexiones
│   │   ├── default.py               # ✅ Routing de mensajes WebSocket
│   │   └── ping.py                  # ✅ Health check
│   └── services/
│       ├── connection_manager.py    # ✅ Gestión de conexiones
│       ├── message_router.py        # ✅ Routing WebSocket
│       ├── presence_manager.py      # ✅ Presencia de usuarios
│       ├── broadcast_service.py     # ✅ Broadcasting
│       └── auth_service.py          # ✅ Auth WebSocket
```

---

## **🔄 FLUJOS DE COMUNICACIÓN CORRECTOS**

### **📤 ENVÍO DE MENSAJE A AGENTE:**
```
Usuario → Agent Service → N8N/Webhook → Agente Externo
                ↓
        WebSocket Service (notificación tiempo real)
```

### **💬 ENVÍO DE MENSAJE DE CHAT:**
```
Usuario → Chat Service → Database
                ↓
        WebSocket Service (notificación tiempo real)
```

### **🔔 NOTIFICACIÓN EN TIEMPO REAL:**
```
Agent/Chat Service → WebSocket Service → Usuario Conectado
```

---

## **❌ ANTI-PATRONES CORREGIDOS**

### **ANTES (Problemático):**
```
❌ Agent Service: send_message.py (confuso)
❌ Chat Service: send_message.py (duplicado)
❌ Chat Service: hybrid_router.py (responsabilidad incorrecta)
❌ WebSocket Service: lógica de persistencia (no es su responsabilidad)
```

### **DESPUÉS (Correcto):**
```
✅ Agent Service: send_message.py (solo para agentes)
✅ Chat Service: send_chat_message.py (solo para chat usuario-usuario)
✅ Agent Service: hybrid_router.py (movido desde Chat)
✅ WebSocket Service: solo infraestructura de tiempo real
```

---

## **🎯 REGLAS DE SEPARACIÓN**

### **🚫 LO QUE CADA SERVICIO NO DEBE HACER:**

#### **Agent Service NO debe:**
- ❌ Manejar chat directo usuario-usuario
- ❌ Gestionar conexiones WebSocket
- ❌ Implementar broadcasting de tiempo real

#### **Chat Service NO debe:**
- ❌ Comunicarse con agentes externos
- ❌ Manejar webhooks de N8N
- ❌ Gestionar conexiones WebSocket directamente
- ❌ Tener lógica de routing híbrido (es responsabilidad de Agent)

#### **WebSocket Service NO debe:**
- ❌ Persistir mensajes en base de datos
- ❌ Tener lógica de negocio de chat o agentes
- ❌ Manejar webhooks externos
- ❌ Procesar archivos o documentos

---

## **📊 MATRIZ DE RESPONSABILIDADES**

| Funcionalidad | Agent | Chat | WebSocket |
|---------------|-------|------|-----------|
| CRUD Agentes | ✅ | ❌ | ❌ |
| Mensajes a Agentes | ✅ | ❌ | ❌ |
| Chat Usuario-Usuario | ❌ | ✅ | ❌ |
| Conexiones WebSocket | ❌ | ❌ | ✅ |
| Broadcasting | ❌ | ❌ | ✅ |
| Presencia Online/Offline | ❌ | ❌ | ✅ |
| Webhooks N8N | ✅ | ❌ | ❌ |
| Routing Híbrido | ✅ | ❌ | ❌ |
| Persistencia Mensajes | ✅ | ✅ | ❌ |
| Notificaciones Tiempo Real | ❌ | ❌ | ✅ |

---

## **🔧 ACCIONES CORRECTIVAS APLICADAS**

1. **✅ Eliminado**: `services/chat/src/handlers/send_message.py` (duplicado)
2. **✅ Creado**: `services/chat/src/handlers/send_chat_message.py` (específico para chat)
3. **✅ Clarificado**: `services/agent/src/handlers/send_message.py` (solo para agentes)
4. **✅ Documentado**: Responsabilidades claras en cada handler
5. **🔄 Pendiente**: Mover `hybrid_router.py` de Chat a Agent Service

---

## **📈 BENEFICIOS DE ESTA SEPARACIÓN**

1. **🎯 Claridad**: Cada servicio tiene un propósito específico y claro
2. **🔧 Mantenibilidad**: Cambios en un área no afectan otras
3. **📊 Escalabilidad**: Cada servicio puede escalar independientemente
4. **🧪 Testabilidad**: Tests más focalizados y específicos
5. **👥 Desarrollo**: Equipos pueden trabajar independientemente
6. **🚀 Deployment**: Despliegues independientes por servicio
