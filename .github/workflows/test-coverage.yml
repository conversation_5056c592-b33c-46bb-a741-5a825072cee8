# .github/workflows/test-coverage.yml
# Implementado según "Testing Guidelines" y Fase 1 Plan - CI/CD Integration

name: Test Coverage and Quality Assurance

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.12'
  AWS_DEFAULT_REGION: us-east-1

jobs:
  # Code Quality Checks
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Run Black (code formatting)
      run: black --check --diff src/ tests/
      
    - name: Run isort (import sorting)
      run: isort --check-only --diff src/ tests/
      
    - name: Run flake8 (linting)
      run: flake8 src/ tests/
      
    - name: Run mypy (type checking)
      run: mypy src/

  # Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Create reports directory
      run: mkdir -p reports/coverage reports/junit
      
    - name: Run unit tests
      run: |
        python -m pytest tests/unit/ \
          --cov=src \
          --cov-report=term-missing \
          --cov-report=html:reports/coverage/unit \
          --cov-report=json:reports/coverage/unit.json \
          --junit-xml=reports/junit/unit.xml \
          -v
          
    - name: Upload unit test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: |
          reports/junit/unit.xml
          reports/coverage/unit.json
          reports/coverage/unit/
          
    - name: Publish unit test results
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Unit Test Results
        path: reports/junit/unit.xml
        reporter: java-junit

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      localstack:
        image: localstack/localstack:latest
        env:
          SERVICES: dynamodb,s3,secretsmanager,ses
          DEBUG: 1
        ports:
          - 4566:4566
        options: >-
          --health-cmd="curl -f http://localhost:4566/_localstack/health"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Wait for LocalStack
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:4566/_localstack/health; do sleep 2; done'
        
    - name: Create reports directory
      run: mkdir -p reports/coverage reports/junit
      
    - name: Run integration tests
      env:
        AWS_ENDPOINT_URL: http://localhost:4566
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
      run: |
        python -m pytest tests/integration/ \
          --cov=src \
          --cov-report=term-missing \
          --cov-report=html:reports/coverage/integration \
          --cov-report=json:reports/coverage/integration.json \
          --junit-xml=reports/junit/integration.xml \
          -v
          
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          reports/junit/integration.xml
          reports/coverage/integration.json
          reports/coverage/integration/

  # E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    services:
      localstack:
        image: localstack/localstack:latest
        env:
          SERVICES: dynamodb,s3,secretsmanager,ses
          DEBUG: 1
        ports:
          - 4566:4566
        options: >-
          --health-cmd="curl -f http://localhost:4566/_localstack/health"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Wait for LocalStack
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:4566/_localstack/health; do sleep 2; done'
        
    - name: Create reports directory
      run: mkdir -p reports/coverage reports/junit
      
    - name: Run E2E tests
      env:
        AWS_ENDPOINT_URL: http://localhost:4566
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
      run: |
        python -m pytest tests/e2e/ \
          --cov=src \
          --cov-report=term-missing \
          --cov-report=html:reports/coverage/e2e \
          --cov-report=json:reports/coverage/e2e.json \
          --junit-xml=reports/junit/e2e.xml \
          -v
          
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: |
          reports/junit/e2e.xml
          reports/coverage/e2e.json
          reports/coverage/e2e/

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Create reports directory
      run: mkdir -p reports/junit reports/performance
      
    - name: Run performance tests
      run: |
        python -m pytest tests/performance/ \
          --junit-xml=reports/junit/performance.xml \
          -v
          
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: |
          reports/junit/performance.xml
          reports/performance/

  # Security Tests
  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install -r requirements.txt
        pip install bandit safety
        
    - name: Run security linting (Bandit)
      run: bandit -r src/ -f json -o reports/bandit.json || true
      
    - name: Run dependency security check (Safety)
      run: safety check --json --output reports/safety.json || true
      
    - name: Create reports directory
      run: mkdir -p reports/junit reports/security
      
    - name: Run security tests
      run: |
        python -m pytest tests/security/ \
          --junit-xml=reports/junit/security.xml \
          -v
          
    - name: Upload security test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-results
        path: |
          reports/junit/security.xml
          reports/bandit.json
          reports/safety.json
          reports/security/

  # Combined Coverage Report
  coverage-report:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Download all test artifacts
      uses: actions/download-artifact@v3
      
    - name: Generate combined coverage report
      run: |
        mkdir -p reports/coverage
        python scripts/run_tests.py --types unit integration e2e
        
    - name: Upload combined coverage report
      uses: actions/upload-artifact@v3
      with:
        name: combined-coverage-report
        path: reports/coverage/combined/
        
    - name: Comment coverage on PR
      if: github.event_name == 'pull_request'
      uses: py-cov-action/python-coverage-comment-action@v3
      with:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        COVERAGE_PATH: reports/coverage/combined.json
