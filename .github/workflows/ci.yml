# .github/workflows/ci.yml
# CI/CD Pipeline for The Jungle Agents Platform
# Implements comprehensive testing, security scanning, and deployment

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
        - dev
        - staging
        - prod

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  AWS_REGION: 'us-east-1'

jobs:
  # Job 1: Code Quality and Linting
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install black pylint flake8 bandit safety
    
    - name: Run Black formatter check
      run: black --check --diff src/ tests/
    
    - name: Run Pylint
      run: pylint src/ --fail-under=8.0
    
    - name: Run Flake8
      run: flake8 src/ tests/ --max-line-length=100 --ignore=E203,W503
    
    - name: Run Bandit security linter
      run: bandit -r src/ -f json -o bandit-report.json
    
    - name: Run Safety check
      run: safety check --json --output safety-report.json
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Job 2: Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio pytest-mock
    
    - name: Run unit tests with coverage
      env:
        ENVIRONMENT: test
        DYNAMODB_TABLE: test-table
        S3_BUCKET: test-bucket
        JWT_SECRET_KEY: test-secret
        ENCRYPTION_KEY: test-encryption-key-32-characters
        STRIPE_TEST_SECRET_KEY: sk_test_51ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890
        STRIPE_WEBHOOK_SECRET: whsec_test_webhook_secret_for_testing
      run: |
        pytest tests/unit/ \
          --cov=src \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --cov-fail-under=85 \
          --junitxml=junit/test-results.xml
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-reports
        path: |
          coverage.xml
          htmlcov/
          junit/
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # Job 3: Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      dynamodb-local:
        image: amazon/dynamodb-local:latest
        ports:
          - 8000:8000
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio boto3
    
    - name: Run integration tests
      env:
        ENVIRONMENT: test
        DYNAMODB_ENDPOINT: http://localhost:8000
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
        AWS_REGION: us-east-1
      run: |
        pytest tests/integration/ \
          --junitxml=junit/integration-test-results.xml \
          -v
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: junit/integration-test-results.xml

  # Job 4: Security Tests
  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio
    
    - name: Run security tests
      env:
        ENVIRONMENT: test
        DYNAMODB_TABLE: test-table
        S3_BUCKET: test-bucket
        JWT_SECRET_KEY: test-secret
        ENCRYPTION_KEY: test-encryption-key-32-characters
      run: |
        pytest tests/security/ \
          --junitxml=junit/security-test-results.xml \
          -v
    
    - name: Upload security test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-results
        path: junit/security-test-results.xml

  # Job 5: Infrastructure Validation
  infrastructure-validation:
    name: Infrastructure Validation
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Terraform
      uses: hashicorp/setup-terraform@v2
      with:
        terraform_version: 1.5.0
    
    - name: Terraform Format Check
      run: terraform fmt -check -recursive terraform/
    
    - name: Terraform Validate
      run: |
        cd terraform/environments/dev
        terraform init -backend=false
        terraform validate
    
    - name: Terraform Plan (Dev)
      if: github.ref == 'refs/heads/develop'
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: |
        cd terraform/environments/dev
        terraform init
        terraform plan -out=tfplan

  # Job 6: Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-tests]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Install Serverless Framework
      run: npm install -g serverless
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Deploy Infrastructure
      run: |
        cd terraform/environments/dev
        terraform init
        terraform apply -auto-approve
    
    - name: Deploy Services
      run: |
        chmod +x scripts/deploy-services.sh
        ./scripts/deploy-services.sh dev
    
    - name: Run smoke tests
      run: |
        pytest tests/e2e/smoke_tests.py \
          --environment=dev \
          -v

  # Job 7: Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-tests]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Install Serverless Framework
      run: npm install -g serverless
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Deploy Infrastructure
      run: |
        cd terraform/environments/staging
        terraform init
        terraform apply -auto-approve
    
    - name: Deploy Services
      run: |
        chmod +x scripts/deploy-services.sh
        ./scripts/deploy-services.sh staging
    
    - name: Run E2E tests
      run: |
        pytest tests/e2e/ \
          --environment=staging \
          -v

  # Job 8: Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: deploy-dev
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest locust
    
    - name: Run performance tests
      env:
        TARGET_URL: ${{ secrets.DEV_API_URL }}
      run: |
        pytest tests/performance/ \
          --junitxml=junit/performance-test-results.xml \
          -v
    
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: junit/performance-test-results.xml

  # Job 9: Generate Reports
  generate-reports:
    name: Generate Test Reports
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-tests]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate combined report
      run: |
        echo "# Test Results Summary" > test-summary.md
        echo "" >> test-summary.md
        echo "## Coverage Report" >> test-summary.md
        echo "Coverage reports available in artifacts" >> test-summary.md
        echo "" >> test-summary.md
        echo "## Security Scan Results" >> test-summary.md
        echo "Security reports available in artifacts" >> test-summary.md
    
    - name: Upload combined report
      uses: actions/upload-artifact@v3
      with:
        name: test-summary
        path: test-summary.md
