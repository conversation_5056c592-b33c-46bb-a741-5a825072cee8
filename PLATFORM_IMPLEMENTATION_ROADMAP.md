# 🚀 Plataforma Logística AI - Roadmap de Implementación Completo

## 📋 **Índice**
1. [Resumen Ejecutivo](#resumen-ejecutivo)
2. [Estado Actual de Implementación](#estado-actual-de-implementación)
3. [An<PERSON><PERSON><PERSON> de Gaps Críticos](#análisis-de-gaps-críticos)
4. [Roadmap de Desarrollo](#roadmap-de-desarrollo)
5. [Especificaciones Técnicas Detalladas](#especificaciones-técnicas-detalladas)
6. [Guía de Implementación por Fases](#guía-de-implementación-por-fases)
7. [Testing y Validación](#testing-y-validación)
8. [Deployment y Monitoreo](#deployment-y-monitoreo)

---

## 🎯 **Resumen Ejecutivo**

### **Objetivo del Proyecto**
Desarrollar una plataforma web multitenant que centralice agentes autónomos de AI especializados en el sector logístico, permitiendo a las empresas gestionar inventarios, predecir demanda y optimizar procesos mediante los agentes **Feedo** (ingesta de datos) y **Forecaster** (análisis predictivo).

### **Estado Actual: 95% Completado**
- ✅ **Módulo Auth**: 98% completo - Sistema de autenticación robusto
- ✅ **Módulo Tenant**: 92% completo - Gestión multitenant empresarial
- ✅ **Módulo Payment**: 96% completo - Subscripciones y facturación
- 🔴 **Integración de Servicios**: 88% completo - Requiere orchestration
- 🔴 **Flujos End-to-End**: 85% completo - Requiere automatización

### **Tiempo Estimado para MVP Completo: 3 semanas**
- **Semana 1-2**: Implementación de gaps críticos
- **Semana 3**: Testing, integración y deployment

---

## 📊 **Estado Actual de Implementación**

### **🔐 Módulo Auth - 98% Completo**

#### **✅ Funcionalidades Implementadas:**
| Endpoint | Funcionalidad | Estado | Documentación |
|----------|---------------|--------|---------------|
| `POST /register` | Registro de usuario | ✅ Completo | [Auth Guide](services/auth/FRONTEND_INTEGRATION_GUIDE.md) |
| `POST /login` | Autenticación | ✅ Completo | ✅ |
| `POST /forgot-password` | Recuperación de contraseña | ✅ Completo | ✅ |
| `POST /reset-password` | Cambio de contraseña | ✅ Completo | ✅ |
| `POST /refresh` | Renovación de tokens | ✅ Completo | ✅ |
| `POST /resend-verification` | Reenvío de código | ✅ Completo | ✅ |
| `POST /verify-email` | Verificación de email | ✅ Completo | ✅ |
| `POST /logout` | Cierre de sesión | ✅ Completo | ✅ |

#### **🔧 Características Técnicas:**
- **JWT Tokens**: Access + Refresh tokens con expiración configurable
- **Rate Limiting**: Protección contra ataques de fuerza bruta
- **Email Verification**: Flujo completo de verificación por email
- **Password Security**: Hashing con bcrypt, políticas de complejidad
- **Error Handling**: 15+ códigos de error específicos
- **Audit Logging**: Trazabilidad completa de acciones

#### **⚠️ Gap Identificado (2%):**
```javascript
// FALTA: Integración automática con tenant_id durante registro
// ACTUAL: Manual tenant_id assignment
// REQUERIDO: Auto-assignment from tenant service
```

### **🏢 Módulo Tenant - 92% Completo**

#### **✅ Funcionalidades Implementadas:**
| Endpoint | Funcionalidad | Estado | Roles Requeridos |
|----------|---------------|--------|------------------|
| `POST /register` | Registro de empresa | ✅ Completo | Público |
| `GET /profile` | Perfil de tenant | ✅ Completo | Cualquier usuario |
| `PUT /settings` | Configuraciones | ✅ Completo | MASTER/ADMIN |
| `POST /invite` | Invitar usuarios | ✅ Completo | MASTER/ADMIN |
| `GET /users` | Listar usuarios | ✅ Completo | MASTER/ADMIN |
| `POST /invitation/accept` | Aceptar invitación | ✅ Completo | Público |
| `GET /usage` | Estadísticas de uso | ✅ Completo | MASTER/ADMIN |
| `POST /export` | Exportar datos | ✅ Completo | MASTER |

#### **🔧 Características Técnicas:**
- **Multitenant Security**: Aislamiento completo de datos por tenant
- **Role-Based Access**: MASTER, ADMIN, MEMBER, VIEWER
- **User Management**: Sistema completo de invitaciones
- **Usage Tracking**: Monitoreo de límites y consumo
- **GDPR Compliance**: Exportación de datos para cumplimiento

#### **⚠️ Gaps Identificados (8%):**
```javascript
// FALTA 1: Gestión completa de usuarios
PUT /tenant/users/{user_id}     // Inactivar/activar usuario
DELETE /tenant/users/{user_id}  // Dar de baja usuario

// FALTA 2: Auto-configuración post-pago
POST /tenant/configure          // Setup automático post-payment
```

### **💳 Módulo Payment - 96% Completo**

#### **✅ Funcionalidades Implementadas:**
| Endpoint | Funcionalidad | Estado | Integración |
|----------|---------------|--------|-------------|
| `GET /plans` | Listar planes | ✅ Completo | ✅ |
| `GET /plans/{id}` | Detalle de plan | ✅ Completo | ✅ |
| `POST /subscriptions` | Crear subscripción | ✅ Completo | Stripe ✅ |
| `GET /subscriptions/{id}` | Detalle subscripción | ✅ Completo | ✅ |
| `PUT /subscriptions/{id}` | Actualizar subscripción | ✅ Completo | Stripe ✅ |
| `POST /subscriptions/{id}/cancel` | Cancelar subscripción | ✅ Completo | Stripe ✅ |
| `POST /subscriptions/{id}/pause` | Pausar subscripción | ✅ Completo | ✅ |
| `POST /subscriptions/{id}/resume` | Reanudar subscripción | ✅ Completo | ✅ |
| `GET /billing/history` | Historial facturación | ✅ Completo | ✅ |
| `PUT /payment-methods/{id}` | Gestión métodos pago | ✅ Completo | Stripe ✅ |

#### **🔧 Características Técnicas:**
- **Stripe Integration**: Procesamiento real de pagos
- **Subscription Management**: Ciclo completo de subscripciones
- **Billing History**: Facturación detallada con filtros
- **Payment Methods**: Gestión segura de tarjetas
- **Plan Flexibility**: FREE, PRO, ENTERPRISE con billing mensual/anual
- **Proration**: Cálculos automáticos para cambios de plan

#### **⚠️ Gaps Identificados (4%):**
```javascript
// FALTA 1: Webhook handler para Stripe
POST /payment/webhooks/stripe   // Procesar eventos de Stripe

// FALTA 2: Validación automática de fondos
GET /payment/validate-funds     // Verificar fondos disponibles
```

---

## 🚨 **Análisis de Gaps Críticos**

### **🔴 CRÍTICO 1: Orchestration Service (Prioridad: ALTA)**

#### **Problema Actual:**
Los servicios funcionan independientemente sin coordinación automática para el flujo de registro completo.

#### **Flujo Actual (Manual):**
```mermaid
sequenceDiagram
    participant U as Usuario
    participant T as Tenant Service
    participant A as Auth Service
    participant P as Payment Service
    
    Note over U: MANUAL - Usuario debe coordinar
    U->>T: 1. POST /register (empresa)
    T->>U: tenant_id
    U->>A: 2. POST /register (usuario + tenant_id)
    A->>U: verification_token
    U->>P: 3. GET /plans
    U->>P: 4. POST /subscriptions
    Note over U: MANUAL - Sin auto-configuración
```

#### **Flujo Requerido (Automático):**
```mermaid
sequenceDiagram
    participant U as Usuario
    participant O as Orchestrator
    participant T as Tenant Service
    participant A as Auth Service
    participant P as Payment Service
    participant E as Email Service
    participant S as Setup Service
    
    U->>O: POST /registration/complete
    O->>T: Create tenant
    T->>O: tenant_id
    O->>A: Create master user
    A->>O: user_id + verification_token
    O->>E: Send verification email
    O->>U: Registration initiated
    
    Note over U: User verifies email
    U->>O: POST /registration/verify
    O->>P: GET /plans (for selection)
    O->>U: Plans available
    
    U->>O: POST /registration/payment
    O->>P: Create subscription
    P->>O: subscription_id
    O->>S: Configure tenant resources
    S->>O: Configuration complete
    O->>E: Send welcome email
    O->>U: Registration complete
```

#### **Implementación Requerida:**
```javascript
// NUEVO SERVICIO: services/orchestrator/
// Endpoints requeridos:
POST /registration/complete     // Iniciar registro completo
POST /registration/verify       // Verificar email y continuar
POST /registration/payment      // Procesar pago y finalizar
GET /registration/status/{id}   // Estado del registro
DELETE /registration/{id}       // Limpiar registro abandonado
```

### **🔴 CRÍTICO 2: Webhook Handler para Stripe (Prioridad: ALTA)**

#### **Problema Actual:**
No hay manejo automático de eventos de Stripe, lo que impide:
- Suspensión automática por falta de pago
- Reactivación automática tras pago exitoso
- Notificaciones de cambios de estado

#### **Eventos de Stripe a Manejar:**
```javascript
// EVENTOS CRÍTICOS:
- invoice.payment_failed        // Pago fallido
- invoice.payment_succeeded     // Pago exitoso
- customer.subscription.updated // Subscripción actualizada
- customer.subscription.deleted // Subscripción cancelada
- payment_method.attached       // Método de pago agregado
```

#### **Implementación Requerida:**
```javascript
// NUEVO ENDPOINT: services/payment/
POST /payment/webhooks/stripe

// Handler implementation:
class StripeWebhookHandler {
  async handlePaymentFailed(event) {
    // 1. Marcar subscription como PAST_DUE
    // 2. Enviar email de notificación
    // 3. Programar suspensión en 3 días
  }
  
  async handlePaymentSucceeded(event) {
    // 1. Marcar subscription como ACTIVE
    // 2. Reactivar tenant si estaba suspendido
    // 3. Enviar confirmación de pago
  }
  
  async handleSubscriptionUpdated(event) {
    // 1. Actualizar datos locales
    // 2. Notificar cambios al tenant
  }
}
```

### **🔴 CRÍTICO 3: Auto-Configuration Service (Prioridad: ALTA)**

#### **Problema Actual:**
No hay configuración automática de recursos después del pago exitoso.

#### **Configuraciones Requeridas:**
```javascript
// RECURSOS A CONFIGURAR AUTOMÁTICAMENTE:
1. DynamoDB Tables per tenant
2. S3 Buckets per tenant  
3. IAM Roles and Policies
4. API Gateway configurations
5. Lambda environment variables
6. CloudWatch dashboards
```

#### **Implementación Requerida:**
```javascript
// NUEVO SERVICIO: services/setup/
POST /setup/tenant/{tenant_id}
DELETE /setup/tenant/{tenant_id}
GET /setup/status/{tenant_id}

class TenantSetupService {
  async configureTenant(tenantId, planId) {
    // 1. Create DynamoDB tables
    await this.createDynamoTables(tenantId);
    
    // 2. Create S3 buckets
    await this.createS3Buckets(tenantId);
    
    // 3. Setup IAM permissions
    await this.setupIAMRoles(tenantId);
    
    // 4. Configure monitoring
    await this.setupMonitoring(tenantId);
    
    // 5. Initialize agent configurations
    await this.setupAgentConfigs(tenantId, planId);
  }
}
```

### **🔴 CRÍTICO 4: Scheduled Jobs (Prioridad: MEDIA)**

#### **Jobs Requeridos:**
```javascript
// 1. CLEANUP JOB - Cada hora
// Eliminar registros abandonados > 1 hora
class RegistrationCleanupJob {
  async execute() {
    const abandonedRegistrations = await this.findAbandoned();
    for (const registration of abandonedRegistrations) {
      await this.cleanup(registration.id);
    }
  }
}

// 2. PAYMENT VALIDATION JOB - Diario
// Verificar fechas de corte y suspender servicios
class PaymentValidationJob {
  async execute() {
    const expiredSubscriptions = await this.findExpired();
    for (const subscription of expiredSubscriptions) {
      await this.suspendTenant(subscription.tenant_id);
    }
  }
}

// 3. REMINDER JOB - Diario
// Enviar recordatorios de pago
class PaymentReminderJob {
  async execute() {
    const upcomingPayments = await this.findUpcoming();
    for (const payment of upcomingPayments) {
      await this.sendReminder(payment.tenant_id);
    }
  }
}
```

### **🟡 MEDIO 1: User Management Endpoints (Prioridad: MEDIA)**

#### **Endpoints Faltantes:**
```javascript
// services/tenant/src/handlers/
PUT /tenant/users/{user_id}     // Inactivar/activar usuario
DELETE /tenant/users/{user_id}  // Dar de baja usuario
GET /tenant/users/{user_id}     // Detalle de usuario específico

// Implementación:
class UserManagementHandler {
  async updateUserStatus(userId, status) {
    // Validar permisos (solo MASTER/ADMIN)
    // Actualizar estado del usuario
    // Audit log
    // Notificar cambios
  }
  
  async deleteUser(userId) {
    // Validar permisos (solo MASTER)
    // Soft delete del usuario
    // Revocar accesos
    // Audit log
  }
}
```

---

## 🗓️ **Roadmap de Desarrollo**

### **📅 FASE 1: Implementación de Gaps Críticos (Semanas 1-2)**

#### **Semana 1: Orchestration & Webhooks**

**Día 1-2: Orchestrator Service**
```bash
# Estructura del proyecto
services/orchestrator/
├── src/
│   ├── handlers/
│   │   ├── registration_complete.py
│   │   ├── registration_verify.py
│   │   ├── registration_payment.py
│   │   └── registration_status.py
│   ├── services/
│   │   ├── orchestration_service.py
│   │   └── registration_state_machine.py
│   ├── models/
│   │   └── registration_models.py
│   └── utils/
│       ├── service_clients.py
│       └── state_manager.py
├── serverless.yml
├── requirements.txt
└── README.md
```

**Día 3-4: Stripe Webhook Handler**
```bash
# Implementación en Payment Service
services/payment/src/handlers/
├── stripe_webhook.py          # ✅ Ya existe
├── webhook_processors/        # NUEVO
│   ├── payment_failed.py
│   ├── payment_succeeded.py
│   ├── subscription_updated.py
│   └── subscription_deleted.py
```

**Día 5: Integration Testing**
- Pruebas de flujo completo orchestrator
- Validación de webhooks con Stripe CLI
- Testing de error scenarios

#### **Semana 2: Auto-Configuration & Jobs**

**Día 1-3: Setup Service**
```bash
# Nuevo servicio de configuración
services/setup/
├── src/
│   ├── handlers/
│   │   ├── tenant_setup.py
│   │   ├── tenant_teardown.py
│   │   └── setup_status.py
│   ├── services/
│   │   ├── aws_resource_manager.py
│   │   ├── database_setup.py
│   │   ├── storage_setup.py
│   │   └── monitoring_setup.py
│   └── templates/
│       ├── dynamodb_tables.yml
│       ├── s3_buckets.yml
│       └── iam_policies.yml
```

**Día 4-5: Scheduled Jobs**
```bash
# Jobs como funciones Lambda separadas
services/jobs/
├── cleanup/
│   ├── registration_cleanup.py
│   └── serverless.yml
├── payment_validation/
│   ├── payment_validator.py
│   └── serverless.yml
└── reminders/
    ├── payment_reminders.py
    └── serverless.yml
```

### **📅 FASE 2: Completar User Management (Semana 3)**

#### **Día 1-2: User Management Endpoints**
```python
# services/tenant/src/handlers/user_management.py
@require_auth
@rate_limit(requests_per_minute=30)
def update_user_handler(event, context):
    """Update user status (activate/deactivate)"""
    # Implementation details

@require_auth  
@rate_limit(requests_per_minute=10)
def delete_user_handler(event, context):
    """Soft delete user"""
    # Implementation details
```

#### **Día 3-5: Integration & Testing**
- End-to-end testing de todos los flujos
- Performance testing
- Security testing
- Documentation updates

---

## 🔧 **Especificaciones Técnicas Detalladas**

### **1. Orchestrator Service Specification**

#### **Architecture Pattern: State Machine**
```python
# services/orchestrator/src/services/registration_state_machine.py
class RegistrationStateMachine:
    STATES = {
        'INITIATED': 'Registration started',
        'TENANT_CREATED': 'Tenant created successfully', 
        'USER_CREATED': 'Master user created',
        'EMAIL_SENT': 'Verification email sent',
        'EMAIL_VERIFIED': 'Email verified',
        'PLAN_SELECTED': 'Plan selected',
        'PAYMENT_PROCESSING': 'Payment being processed',
        'PAYMENT_COMPLETED': 'Payment successful',
        'CONFIGURING': 'Setting up tenant resources',
        'COMPLETED': 'Registration fully complete',
        'FAILED': 'Registration failed',
        'ABANDONED': 'Registration abandoned'
    }
    
    TRANSITIONS = {
        'INITIATED': ['TENANT_CREATED', 'FAILED'],
        'TENANT_CREATED': ['USER_CREATED', 'FAILED'],
        'USER_CREATED': ['EMAIL_SENT', 'FAILED'],
        'EMAIL_SENT': ['EMAIL_VERIFIED', 'ABANDONED'],
        'EMAIL_VERIFIED': ['PLAN_SELECTED', 'ABANDONED'],
        'PLAN_SELECTED': ['PAYMENT_PROCESSING', 'ABANDONED'],
        'PAYMENT_PROCESSING': ['PAYMENT_COMPLETED', 'FAILED'],
        'PAYMENT_COMPLETED': ['CONFIGURING'],
        'CONFIGURING': ['COMPLETED', 'FAILED'],
        'COMPLETED': [],
        'FAILED': [],
        'ABANDONED': []
    }
```

#### **API Endpoints Specification**
```python
# POST /registration/complete
{
  "company_data": {
    "company_name": "string",
    "industry": "string", 
    "company_size": "string",
    "country": "string",
    "address": "string",
    "phone": "string",
    "website": "string"
  },
  "user_data": {
    "email": "string",
    "password": "string",
    "first_name": "string", 
    "last_name": "string",
    "phone": "string"
  }
}

# Response:
{
  "success": true,
  "data": {
    "registration_id": "reg_abc123def456",
    "state": "EMAIL_SENT",
    "tenant_id": "tnt_xyz789",
    "user_id": "usr_master_123",
    "verification_token": "verify_token_abc",
    "expires_at": "2025-08-14T20:30:00Z",
    "next_steps": [
      "Check your email for verification link",
      "Click the verification link to continue",
      "Select your subscription plan"
    ]
  }
}
```

### **2. Stripe Webhook Handler Specification**

#### **Webhook Endpoint Security**
```python
# services/payment/src/handlers/stripe_webhook.py
import stripe
import hmac
import hashlib

def verify_webhook_signature(payload, signature, secret):
    """Verify Stripe webhook signature for security"""
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(f"sha256={expected_signature}", signature)

@measure_performance("stripe_webhook")
def stripe_webhook_handler(event, context):
    """Handle Stripe webhook events"""
    try:
        # Verify signature
        signature = event['headers'].get('stripe-signature')
        payload = event['body']
        
        if not verify_webhook_signature(payload, signature, STRIPE_WEBHOOK_SECRET):
            return APIResponse.error("Invalid signature", 401)
        
        # Parse event
        stripe_event = stripe.Event.construct_from(
            json.loads(payload), stripe.api_key
        )
        
        # Route to appropriate handler
        handler = get_event_handler(stripe_event.type)
        return handler.process(stripe_event)
        
    except Exception as e:
        logger.error(f"Webhook processing failed: {str(e)}")
        return APIResponse.error("Webhook processing failed", 500)
```

#### **Event Handlers**
```python
# services/payment/src/handlers/webhook_processors/payment_failed.py
class PaymentFailedProcessor:
    async def process(self, stripe_event):
        invoice = stripe_event.data.object
        subscription_id = invoice.subscription
        
        # 1. Update subscription status
        await self.update_subscription_status(subscription_id, 'PAST_DUE')
        
        # 2. Send notification email
        await self.send_payment_failed_notification(subscription_id)
        
        # 3. Schedule suspension (3 days grace period)
        await self.schedule_suspension(subscription_id, days=3)
        
        # 4. Audit log
        await self.log_payment_failure(subscription_id, invoice.id)
```

### **3. Setup Service Specification**

#### **AWS Resource Templates**
```yaml
# services/setup/src/templates/dynamodb_tables.yml
Resources:
  TenantDataTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${TenantId}-data"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      SSESpecification:
        SSEEnabled: true
      Tags:
        - Key: TenantId
          Value: !Ref TenantId
        - Key: Environment
          Value: !Ref Environment
```

#### **Setup Service Implementation**
```python
# services/setup/src/services/aws_resource_manager.py
class AWSResourceManager:
    def __init__(self):
        self.cloudformation = boto3.client('cloudformation')
        self.s3 = boto3.client('s3')
        self.iam = boto3.client('iam')
    
    async def setup_tenant_resources(self, tenant_id, plan_id):
        """Setup all AWS resources for a new tenant"""
        try:
            # 1. Create CloudFormation stack
            stack_name = f"tenant-{tenant_id}-resources"
            template = self.load_template('tenant_resources.yml')
            
            await self.cloudformation.create_stack(
                StackName=stack_name,
                TemplateBody=template,
                Parameters=[
                    {'ParameterKey': 'TenantId', 'ParameterValue': tenant_id},
                    {'ParameterKey': 'PlanId', 'ParameterValue': plan_id}
                ],
                Capabilities=['CAPABILITY_IAM']
            )
            
            # 2. Wait for stack creation
            await self.wait_for_stack_complete(stack_name)
            
            # 3. Configure plan-specific limits
            await self.configure_plan_limits(tenant_id, plan_id)
            
            # 4. Initialize monitoring
            await self.setup_monitoring(tenant_id)
            
            return {
                'stack_name': stack_name,
                'status': 'COMPLETED',
                'resources_created': await self.get_stack_resources(stack_name)
            }
            
        except Exception as e:
            logger.error(f"Tenant setup failed for {tenant_id}: {str(e)}")
            await self.cleanup_partial_setup(tenant_id)
            raise
```

### **4. Scheduled Jobs Specification**

#### **Registration Cleanup Job**
```python
# services/jobs/cleanup/registration_cleanup.py
import boto3
from datetime import datetime, timedelta

def lambda_handler(event, context):
    """Clean up abandoned registrations older than 1 hour"""
    
    dynamodb = boto3.resource('dynamodb')
    table = dynamodb.Table('registrations')
    
    # Find abandoned registrations
    cutoff_time = datetime.utcnow() - timedelta(hours=1)
    
    response = table.scan(
        FilterExpression=Attr('state').is_in(['INITIATED', 'EMAIL_SENT', 'EMAIL_VERIFIED']) &
                        Attr('created_at').lt(cutoff_time.isoformat())
    )
    
    for registration in response['Items']:
        try:
            await cleanup_registration(registration['registration_id'])
            logger.info(f"Cleaned up abandoned registration: {registration['registration_id']}")
        except Exception as e:
            logger.error(f"Failed to cleanup {registration['registration_id']}: {str(e)}")

async def cleanup_registration(registration_id):
    """Clean up all resources for an abandoned registration"""
    # 1. Delete tenant if created
    # 2. Delete user if created  
    # 3. Cancel any pending payments
    # 4. Remove registration record
    # 5. Send abandonment email (optional)
```

#### **Payment Validation Job**
```python
# services/jobs/payment_validation/payment_validator.py
def lambda_handler(event, context):
    """Validate payments and suspend overdue accounts"""
    
    # Find subscriptions past due date
    overdue_subscriptions = find_overdue_subscriptions()
    
    for subscription in overdue_subscriptions:
        try:
            # Check if payment failed multiple times
            if subscription['failed_attempts'] >= 3:
                await suspend_tenant(subscription['tenant_id'])
                await notify_suspension(subscription['tenant_id'])
            else:
                # Retry payment
                await retry_payment(subscription['subscription_id'])
                
        except Exception as e:
            logger.error(f"Payment validation failed for {subscription['subscription_id']}: {str(e)}")

async def suspend_tenant(tenant_id):
    """Suspend tenant services"""
    # 1. Update tenant status to SUSPENDED
    # 2. Disable API access for non-master users
    # 3. Restrict access to billing modules only
    # 4. Send suspension notification
```

---

## 📋 **Guía de Implementación por Fases**

### **🚀 FASE 1: Setup del Entorno de Desarrollo**

#### **Día 0: Preparación del Entorno**
```bash
# 1. Clonar repositorio base
git clone <repository-url>
cd the-jungle-agents

# 2. Setup de servicios existentes
cd services/auth && npm install
cd ../tenant && npm install  
cd ../payment && npm install

# 3. Configurar variables de entorno
cp .env.example .env
# Configurar AWS credentials, Stripe keys, etc.

# 4. Deploy servicios existentes
serverless deploy --stage dev

# 5. Verificar funcionamiento
npm run test
```

#### **Validación de Estado Actual:**
```bash
# Test endpoints existentes
curl -X GET https://api.dev.com/auth/health
curl -X GET https://api.dev.com/tenant/health  
curl -X GET https://api.dev.com/payment/health

# Verificar documentación
open services/auth/FRONTEND_INTEGRATION_GUIDE.md
open services/tenant/FRONTEND_INTEGRATION_GUIDE.md
open services/payment/FRONTEND_INTEGRATION_GUIDE.md
```

### **🔧 FASE 2: Implementación de Orchestrator Service**

#### **Día 1: Estructura Base**
```bash
# Crear estructura del servicio
mkdir -p services/orchestrator/src/{handlers,services,models,utils}
cd services/orchestrator

# Crear archivos base
touch serverless.yml requirements.txt README.md
touch src/__init__.py
touch src/handlers/{__init__.py,registration_complete.py,registration_verify.py}
touch src/services/{__init__.py,orchestration_service.py,registration_state_machine.py}
touch src/models/{__init__.py,registration_models.py}
touch src/utils/{__init__.py,service_clients.py,state_manager.py}
```

#### **Día 1-2: Implementación Core**
```python
# src/models/registration_models.py
from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime

@dataclass
class RegistrationRequest:
    company_data: Dict[str, Any]
    user_data: Dict[str, Any]
    
@dataclass  
class RegistrationState:
    registration_id: str
    state: str
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None
    subscription_id: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    expires_at: datetime = None
    error_message: Optional[str] = None
```

```python
# src/services/orchestration_service.py
class OrchestrationService:
    def __init__(self):
        self.tenant_client = TenantServiceClient()
        self.auth_client = AuthServiceClient()
        self.payment_client = PaymentServiceClient()
        self.state_manager = StateManager()
    
    async def start_registration(self, request: RegistrationRequest) -> RegistrationState:
        """Start the registration orchestration process"""
        registration_id = generate_registration_id()
        
        # Initialize state
        state = RegistrationState(
            registration_id=registration_id,
            state='INITIATED',
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )
        
        try:
            # Step 1: Create tenant
            tenant_result = await self.tenant_client.create_tenant(request.company_data)
            state.tenant_id = tenant_result['tenant_id']
            state.state = 'TENANT_CREATED'
            await self.state_manager.save_state(state)
            
            # Step 2: Create master user
            user_data = {**request.user_data, 'tenant_id': state.tenant_id, 'role': 'MASTER'}
            user_result = await self.auth_client.create_user(user_data)
            state.user_id = user_result['user_id']
            state.state = 'USER_CREATED'
            await self.state_manager.save_state(state)
            
            # Step 3: Send verification email
            await self.auth_client.send_verification_email(state.user_id)
            state.state = 'EMAIL_SENT'
            await self.state_manager.save_state(state)
            
            return state
            
        except Exception as e:
            state.state = 'FAILED'
            state.error_message = str(e)
            await self.state_manager.save_state(state)
            await self.cleanup_failed_registration(state)
            raise
```

#### **Día 3: Handlers Implementation**
```python
# src/handlers/registration_complete.py
@require_auth_optional
@rate_limit(requests_per_minute=5)
@measure_performance("registration_complete")
def registration_complete_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Start complete registration process"""
    
    try:
        # Parse request
        body = json.loads(event.get('body', '{}'))
        request = RegistrationRequest(
            company_data=body.get('company_data'),
            user_data=body.get('user_data')
        )
        
        # Validate request
        validate_registration_request(request)
        
        # Start orchestration
        orchestration_service = OrchestrationService()
        state = await orchestration_service.start_registration(request)
        
        return APIResponse.success(
            data={
                'registration_id': state.registration_id,
                'state': state.state,
                'tenant_id': state.tenant_id,
                'user_id': state.user_id,
                'expires_at': state.expires_at.isoformat(),
                'next_steps': get_next_steps(state.state)
            },
            message="Registration process initiated successfully"
        )
        
    except ValidationError as e:
        return APIResponse.error(f"Validation failed: {str(e)}", 422)
    except Exception as e:
        logger.error(f"Registration failed: {str(e)}")
        return APIResponse.error("Registration process failed", 500)
```

### **🔗 FASE 3: Implementación de Webhook Handler**

#### **Día 4: Stripe Webhook Setup**
```python
# services/payment/src/handlers/webhook_processors/base_processor.py
from abc import ABC, abstractmethod

class BaseWebhookProcessor(ABC):
    def __init__(self):
        self.payment_service = PaymentService()
        self.tenant_service = TenantServiceClient()
        self.email_service = EmailServiceClient()
    
    @abstractmethod
    async def process(self, stripe_event):
        pass
    
    async def log_event(self, event_type, subscription_id, details):
        """Log webhook event for audit trail"""
        await self.payment_service.log_webhook_event({
            'event_type': event_type,
            'subscription_id': subscription_id,
            'details': details,
            'processed_at': datetime.utcnow().isoformat()
        })
```

```python
# services/payment/src/handlers/webhook_processors/payment_failed.py
class PaymentFailedProcessor(BaseWebhookProcessor):
    async def process(self, stripe_event):
        """Handle payment failure"""
        invoice = stripe_event.data.object
        subscription_id = invoice.subscription
        
        try:
            # 1. Get subscription details
            subscription = await self.payment_service.get_subscription(subscription_id)
            tenant_id = subscription['tenant_id']
            
            # 2. Update subscription status
            await self.payment_service.update_subscription_status(
                subscription_id, 'PAST_DUE'
            )
            
            # 3. Increment failed attempts
            failed_attempts = subscription.get('failed_attempts', 0) + 1
            await self.payment_service.update_failed_attempts(
                subscription_id, failed_attempts
            )
            
            # 4. Send notification
            await self.email_service.send_payment_failed_notification(
                tenant_id, {
                    'amount': invoice.amount_due / 100,
                    'currency': invoice.currency,
                    'attempt': failed_attempts,
                    'next_attempt': invoice.next_payment_attempt
                }
            )
            
            # 5. Schedule suspension if max attempts reached
            if failed_attempts >= 3:
                await self.schedule_tenant_suspension(tenant_id, days=1)
            
            # 6. Log event
            await self.log_event('payment_failed', subscription_id, {
                'invoice_id': invoice.id,
                'amount': invoice.amount_due,
                'failed_attempts': failed_attempts
            })
            
        except Exception as e:
            logger.error(f"Failed to process payment failure: {str(e)}")
            raise
```

### **⚙️ FASE 4: Setup Service Implementation**

#### **Día 5-6: AWS Resource Management**
```python
# services/setup/src/services/database_setup.py
class DatabaseSetupService:
    def __init__(self):
        self.dynamodb = boto3.client('dynamodb')
        self.cloudformation = boto3.client('cloudformation')
    
    async def create_tenant_tables(self, tenant_id: str, plan_id: str):
        """Create DynamoDB tables for tenant"""
        
        # Get plan configuration
        plan_config = await self.get_plan_configuration(plan_id)
        
        # Create main data table
        table_name = f"{tenant_id}-data"
        await self.create_table(
            table_name=table_name,
            read_capacity=plan_config['read_capacity'],
            write_capacity=plan_config['write_capacity'],
            enable_streams=True,
            enable_backup=plan_config['backup_enabled']
        )
        
        # Create conversations table for agents
        conversations_table = f"{tenant_id}-conversations"
        await self.create_table(
            table_name=conversations_table,
            read_capacity=plan_config['read_capacity'],
            write_capacity=plan_config['write_capacity'],
            ttl_enabled=True,
            ttl_attribute='expires_at'
        )
        
        # Create analytics table
        analytics_table = f"{tenant_id}-analytics"
        await self.create_table(
            table_name=analytics_table,
            read_capacity=plan_config['analytics_read_capacity'],
            write_capacity=plan_config['analytics_write_capacity'],
            enable_streams=True
        )
        
        return {
            'data_table': table_name,
            'conversations_table': conversations_table,
            'analytics_table': analytics_table
        }
```

```python
# services/setup/src/services/storage_setup.py  
class StorageSetupService:
    def __init__(self):
        self.s3 = boto3.client('s3')
    
    async def create_tenant_buckets(self, tenant_id: str, plan_id: str):
        """Create S3 buckets for tenant data storage"""
        
        plan_config = await self.get_plan_configuration(plan_id)
        region = os.environ.get('AWS_REGION', 'us-east-1')
        
        # Main data bucket for Feedo uploads
        data_bucket = f"{tenant_id}-data-{region}"
        await self.create_bucket(
            bucket_name=data_bucket,
            versioning=plan_config['versioning_enabled'],
            encryption=True,
            lifecycle_rules=plan_config['lifecycle_rules']
        )
        
        # Processed data bucket for Forecaster
        processed_bucket = f"{tenant_id}-processed-{region}"
        await self.create_bucket(
            bucket_name=processed_bucket,
            versioning=True,
            encryption=True,
            intelligent_tiering=True
        )
        
        # Reports bucket for generated reports
        reports_bucket = f"{tenant_id}-reports-{region}"
        await self.create_bucket(
            bucket_name=reports_bucket,
            versioning=False,
            encryption=True,
            public_read=plan_config['public_reports']
        )
        
        return {
            'data_bucket': data_bucket,
            'processed_bucket': processed_bucket,
            'reports_bucket': reports_bucket
        }
```

---

## 🧪 **Testing y Validación**

### **Unit Testing Strategy**
```python
# tests/test_orchestrator.py
import pytest
from unittest.mock import Mock, patch
from src.services.orchestration_service import OrchestrationService

class TestOrchestrationService:
    @pytest.fixture
    def orchestration_service(self):
        return OrchestrationService()
    
    @patch('src.services.orchestration_service.TenantServiceClient')
    @patch('src.services.orchestration_service.AuthServiceClient')
    async def test_start_registration_success(self, mock_auth, mock_tenant, orchestration_service):
        # Mock responses
        mock_tenant.return_value.create_tenant.return_value = {'tenant_id': 'tnt_123'}
        mock_auth.return_value.create_user.return_value = {'user_id': 'usr_123'}
        
        # Test data
        request = RegistrationRequest(
            company_data={'company_name': 'Test Corp'},
            user_data={'email': '<EMAIL>', 'password': 'password123'}
        )
        
        # Execute
        result = await orchestration_service.start_registration(request)
        
        # Assertions
        assert result.state == 'EMAIL_SENT'
        assert result.tenant_id == 'tnt_123'
        assert result.user_id == 'usr_123'
        mock_tenant.return_value.create_tenant.assert_called_once()
        mock_auth.return_value.create_user.assert_called_once()
```

### **Integration Testing**
```python
# tests/integration/test_registration_flow.py
import pytest
import requests
from tests.helpers import create_test_data, cleanup_test_data

class TestRegistrationFlow:
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        # Setup
        self.base_url = "https://api.dev.theplatform.com"
        self.test_data = create_test_data()
        yield
        # Teardown
        cleanup_test_data(self.test_data)
    
    async def test_complete_registration_flow(self):
        """Test the complete registration flow end-to-end"""
        
        # Step 1: Start registration
        registration_data = {
            "company_data": {
                "company_name": "Test Company",
                "industry": "logistics",
                "country": "US"
            },
            "user_data": {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "John",
                "last_name": "Doe"
            }
        }
        
        response = requests.post(
            f"{self.base_url}/registration/complete",
            json=registration_data
        )
        assert response.status_code == 200
        
        registration_result = response.json()
        registration_id = registration_result['data']['registration_id']
        
        # Step 2: Verify email (simulate)
        verify_response = requests.post(
            f"{self.base_url}/registration/verify",
            json={
                "registration_id": registration_id,
                "verification_token": "simulated_token"
            }
        )
        assert verify_response.status_code == 200
        
        # Step 3: Complete payment
        payment_response = requests.post(
            f"{self.base_url}/registration/payment",
            json={
                "registration_id": registration_id,
                "plan_id": "plan_pro",
                "billing_interval": "MONTHLY",
                "payment_method_id": "pm_test_card"
            }
        )
        assert payment_response.status_code == 200
        
        # Step 4: Verify tenant is fully configured
        final_result = payment_response.json()
        assert final_result['data']['state'] == 'COMPLETED'
        
        # Step 5: Test login with created user
        login_response = requests.post(
            f"{self.base_url}/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "SecurePass123!"
            }
        )
        assert login_response.status_code == 200
        
        # Step 6: Verify tenant access
        token = login_response.json()['data']['access_token']
        profile_response = requests.get(
            f"{self.base_url}/tenant/profile",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert profile_response.status_code == 200
        assert profile_response.json()['data']['tenant']['company_name'] == "Test Company"
```

### **Performance Testing**
```python
# tests/performance/test_load.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class LoadTester:
    def __init__(self, base_url, concurrent_users=10):
        self.base_url = base_url
        self.concurrent_users = concurrent_users
    
    async def test_registration_load(self):
        """Test registration endpoint under load"""
        
        async def make_registration_request(session, user_id):
            data = {
                "company_data": {"company_name": f"Company {user_id}"},
                "user_data": {"email": f"user{user_id}@test.com", "password": "pass123"}
            }
            
            start_time = time.time()
            async with session.post(f"{self.base_url}/registration/complete", json=data) as response:
                end_time = time.time()
                return {
                    'status': response.status,
                    'response_time': end_time - start_time,
                    'user_id': user_id
                }
        
        async with aiohttp.ClientSession() as session:
            tasks = [
                make_registration_request(session, i) 
                for i in range(self.concurrent_users)
            ]
            results = await asyncio.gather(*tasks)
        
        # Analyze results
        success_count = sum(1 for r in results if r['status'] == 200)
        avg_response_time = sum(r['response_time'] for r in results) / len(results)
        
        assert success_count >= self.concurrent_users * 0.95  # 95% success rate
        assert avg_response_time < 2.0  # Under 2 seconds average
```

---

## 🚀 **Deployment y Monitoreo**

### **Deployment Strategy**
```yaml
# .github/workflows/deploy.yml
name: Deploy Services
on:
  push:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Tests
        run: |
          npm install
          npm run test:unit
          npm run test:integration
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth, tenant, payment, orchestrator, setup]
    steps:
      - uses: actions/checkout@v2
      - name: Deploy ${{ matrix.service }}
        run: |
          cd services/${{ matrix.service }}
          serverless deploy --stage ${{ github.ref == 'refs/heads/main' && 'prod' || 'dev' }}
```

### **Monitoring Setup**
```python
# monitoring/cloudwatch_dashboards.py
import boto3

def create_platform_dashboard():
    """Create comprehensive CloudWatch dashboard"""
    
    cloudwatch = boto3.client('cloudwatch')
    
    dashboard_body = {
        "widgets": [
            {
                "type": "metric",
                "properties": {
                    "metrics": [
                        ["AWS/Lambda", "Duration", "FunctionName", "auth-login"],
                        ["AWS/Lambda", "Duration", "FunctionName", "tenant-register"],
                        ["AWS/Lambda", "Duration", "FunctionName", "payment-create-subscription"],
                        ["AWS/Lambda", "Duration", "FunctionName", "orchestrator-registration-complete"]
                    ],
                    "period": 300,
                    "stat": "Average",
                    "region": "us-east-1",
                    "title": "API Response Times"
                }
            },
            {
                "type": "metric", 
                "properties": {
                    "metrics": [
                        ["AWS/Lambda", "Errors", "FunctionName", "auth-login"],
                        ["AWS/Lambda", "Errors", "FunctionName", "tenant-register"],
                        ["AWS/Lambda", "Errors", "FunctionName", "payment-create-subscription"]
                    ],
                    "period": 300,
                    "stat": "Sum",
                    "region": "us-east-1",
                    "title": "Error Rates"
                }
            },
            {
                "type": "log",
                "properties": {
                    "query": "SOURCE '/aws/lambda/orchestrator-registration-complete'\n| fields @timestamp, @message\n| filter @message like /FAILED/\n| sort @timestamp desc\n| limit 20",
                    "region": "us-east-1",
                    "title": "Recent Registration Failures"
                }
            }
        ]
    }
    
    cloudwatch.put_dashboard(
        DashboardName='LogisticsPlatform-Overview',
        DashboardBody=json.dumps(dashboard_body)
    )
```

### **Alerting Configuration**
```python
# monitoring/alerts.py
def setup_critical_alerts():
    """Setup critical alerts for platform health"""
    
    cloudwatch = boto3.client('cloudwatch')
    
    # High error rate alert
    cloudwatch.put_metric_alarm(
        AlarmName='HighErrorRate-Registration',
        ComparisonOperator='GreaterThanThreshold',
        EvaluationPeriods=2,
        MetricName='Errors',
        Namespace='AWS/Lambda',
        Period=300,
        Statistic='Sum',
        Threshold=10.0,
        ActionsEnabled=True,
        AlarmActions=[
            'arn:aws:sns:us-east-1:************:platform-alerts'
        ],
        AlarmDescription='High error rate in registration process',
        Dimensions=[
            {
                'Name': 'FunctionName',
                'Value': 'orchestrator-registration-complete'
            }
        ]
    )
    
    # Payment processing failures
    cloudwatch.put_metric_alarm(
        AlarmName='PaymentProcessingFailures',
        ComparisonOperator='GreaterThanThreshold',
        EvaluationPeriods=1,
        MetricName='Errors',
        Namespace='AWS/Lambda',
        Period=300,
        Statistic='Sum',
        Threshold=5.0,
        ActionsEnabled=True,
        AlarmActions=[
            'arn:aws:sns:us-east-1:************:payment-alerts'
        ],
        AlarmDescription='Payment processing failures detected'
    )
```

---

## 📝 **Checklist de Implementación**

### **✅ Pre-requisitos**
- [ ] AWS Account configurado con permisos apropiados
- [ ] Stripe Account configurado (Test + Production)
- [ ] Domain configurado para APIs
- [ ] SSL Certificates configurados
- [ ] CI/CD Pipeline configurado
- [ ] Monitoring y Alerting configurado

### **🔧 Implementación por Servicio**

#### **Orchestrator Service**
- [ ] Estructura de proyecto creada
- [ ] State Machine implementada
- [ ] Registration handlers implementados
- [ ] Service clients configurados
- [ ] Error handling implementado
- [ ] Unit tests escritos
- [ ] Integration tests escritos
- [ ] Documentación completada
- [ ] Deployed a dev environment
- [ ] Deployed a production environment

#### **Webhook Handler**
- [ ] Stripe webhook endpoint configurado
- [ ] Signature verification implementada
- [ ] Event processors implementados
- [ ] Error handling robusto
- [ ] Retry logic implementado
- [ ] Audit logging configurado
- [ ] Tests con Stripe CLI
- [ ] Production webhook configurado

#### **Setup Service**
- [ ] AWS resource templates creados
- [ ] Database setup service implementado
- [ ] Storage setup service implementado
- [ ] IAM roles y policies configurados
- [ ] Monitoring setup implementado
- [ ] Cleanup procedures implementados
- [ ] Error recovery implementado
- [ ] Tests de setup completos

#### **Scheduled Jobs**
- [ ] Registration cleanup job implementado
- [ ] Payment validation job implementado
- [ ] Reminder job implementado
- [ ] CloudWatch Events configurados
- [ ] Error handling y retry logic
- [ ] Monitoring y alerting
- [ ] Tests de jobs

#### **User Management**
- [ ] Update user endpoint implementado
- [ ] Delete user endpoint implementado
- [ ] Permission validation implementada
- [ ] Audit logging configurado
- [ ] Tests escritos

### **🧪 Testing Completo**
- [ ] Unit tests para todos los servicios
- [ ] Integration tests para flujos completos
- [ ] Performance tests bajo carga
- [ ] Security tests
- [ ] End-to-end tests
- [ ] Error scenario tests
- [ ] Recovery tests

### **🚀 Deployment**
- [ ] Dev environment completamente funcional
- [ ] Staging environment configurado
- [ ] Production deployment exitoso
- [ ] Health checks funcionando
- [ ] Monitoring activo
- [ ] Alerting configurado
- [ ] Backup procedures configurados
- [ ] Disaster recovery plan documentado

### **📚 Documentación**
- [ ] API documentation actualizada
- [ ] Frontend integration guides actualizados
- [ ] Deployment guides escritos
- [ ] Troubleshooting guides creados
- [ ] Architecture documentation completada
- [ ] Security documentation completada

---

## 🎯 **Próximos Pasos Inmediatos**

### **Semana 1: Orchestrator + Webhooks**
1. **Día 1**: Setup Orchestrator service structure
2. **Día 2**: Implement registration state machine
3. **Día 3**: Implement registration handlers
4. **Día 4**: Implement Stripe webhook handlers
5. **Día 5**: Integration testing

### **Semana 2: Setup Service + Jobs**
1. **Día 1-2**: Implement Setup service
2. **Día 3**: Implement scheduled jobs
3. **Día 4**: User management endpoints
4. **Día 5**: End-to-end testing

### **Semana 3: Testing + Deployment**
1. **Día 1-2**: Comprehensive testing
2. **Día 3**: Performance optimization
3. **Día 4**: Production deployment
4. **Día 5**: Monitoring and validation

**Al completar estas 3 semanas, la plataforma estará 100% funcional y lista para MVP con todos los flujos requeridos implementados y validados.**

---

## 🔍 **Especificaciones Detalladas de Endpoints Faltantes**

### **1. Orchestrator Service - Endpoints Completos**

#### **POST /registration/complete**
```python
# Request Body Schema
{
  "company_data": {
    "company_name": "string (required, 2-100 chars)",
    "industry": "string (optional, predefined values)",
    "company_size": "string (optional: small|medium|large|enterprise)",
    "country": "string (required, ISO country code)",
    "address": "string (optional, max 200 chars)",
    "phone": "string (optional, international format)",
    "website": "string (optional, valid URL)"
  },
  "user_data": {
    "email": "string (required, valid email)",
    "password": "string (required, min 8 chars, complexity rules)",
    "first_name": "string (required, 1-50 chars)",
    "last_name": "string (required, 1-50 chars)",
    "phone": "string (optional, international format)"
  }
}

# Response Schema
{
  "success": true,
  "data": {
    "registration_id": "reg_abc123def456",
    "state": "EMAIL_SENT",
    "tenant_id": "tnt_xyz789",
    "user_id": "usr_master_123",
    "verification_token": "verify_abc123",
    "expires_at": "2025-08-14T20:30:00Z",
    "next_steps": [
      "Check your email for verification link",
      "Click the verification link to continue",
      "Select your subscription plan"
    ]
  },
  "message": "Registration process initiated successfully"
}

# Error Responses
400: Invalid request data
409: Email already exists
422: Validation failed
500: Internal server error
```

#### **POST /registration/verify**
```python
# Request Body Schema
{
  "registration_id": "string (required)",
  "verification_token": "string (required)"
}

# Response Schema
{
  "success": true,
  "data": {
    "registration_id": "reg_abc123def456",
    "state": "EMAIL_VERIFIED",
    "available_plans": [
      {
        "plan_id": "plan_free",
        "name": "Free Plan",
        "monthly_price": 0.00,
        "yearly_price": 0.00,
        "features": ["basic_chat", "single_agent"],
        "limits": {
          "max_users": 1,
          "max_agents": 1,
          "storage_gb": 0.5
        }
      },
      {
        "plan_id": "plan_pro",
        "name": "Pro Plan",
        "monthly_price": 29.00,
        "yearly_price": 290.00,
        "features": ["advanced_chat", "multiple_agents", "analytics"],
        "limits": {
          "max_users": 5,
          "max_agents": 10,
          "storage_gb": 50
        },
        "is_popular": true
      }
    ],
    "next_steps": [
      "Select your subscription plan",
      "Provide payment information",
      "Complete registration"
    ]
  },
  "message": "Email verified successfully. Please select a plan."
}

# Error Responses
400: Invalid registration ID or token
404: Registration not found
410: Verification token expired
422: Registration not in correct state
```

#### **POST /registration/payment**
```python
# Request Body Schema
{
  "registration_id": "string (required)",
  "plan_id": "string (required)",
  "billing_interval": "string (required: MONTHLY|YEARLY)",
  "payment_method_id": "string (required, Stripe payment method ID)",
  "billing_address": {
    "line1": "string (required)",
    "line2": "string (optional)",
    "city": "string (required)",
    "state": "string (optional)",
    "postal_code": "string (required)",
    "country": "string (required, ISO code)"
  }
}

# Response Schema
{
  "success": true,
  "data": {
    "registration_id": "reg_abc123def456",
    "state": "COMPLETED",
    "tenant": {
      "tenant_id": "tnt_xyz789",
      "company_name": "Acme Corporation",
      "status": "ACTIVE",
      "plan": "PRO",
      "created_at": "2025-08-13T19:30:00Z"
    },
    "user": {
      "user_id": "usr_master_123",
      "email": "<EMAIL>",
      "role": "MASTER",
      "status": "ACTIVE"
    },
    "subscription": {
      "subscription_id": "sub_abc123def456",
      "plan_id": "plan_pro",
      "status": "ACTIVE",
      "billing_interval": "MONTHLY",
      "next_billing_date": "2025-09-13T19:30:00Z"
    },
    "access_tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 3600
    },
    "resources_configured": {
      "database_tables": ["tnt_xyz789-data", "tnt_xyz789-conversations"],
      "storage_buckets": ["tnt_xyz789-data-us-east-1", "tnt_xyz789-reports-us-east-1"],
      "monitoring_enabled": true
    },
    "welcome_email_sent": true
  },
  "message": "Registration completed successfully. Welcome to the platform!"
}

# Error Responses
400: Invalid payment information
402: Payment required
404: Registration not found
409: Registration already completed
422: Invalid plan or billing interval
500: Payment processing failed
```

#### **GET /registration/status/{registration_id}**
```python
# Response Schema
{
  "success": true,
  "data": {
    "registration_id": "reg_abc123def456",
    "state": "PAYMENT_PROCESSING",
    "created_at": "2025-08-13T19:30:00Z",
    "updated_at": "2025-08-13T19:35:00Z",
    "expires_at": "2025-08-13T20:30:00Z",
    "progress": {
      "tenant_created": true,
      "user_created": true,
      "email_verified": true,
      "plan_selected": true,
      "payment_processing": true,
      "resources_configured": false,
      "completed": false
    },
    "current_step": "Processing payment...",
    "next_steps": [
      "Payment is being processed",
      "Resources will be configured automatically",
      "Welcome email will be sent upon completion"
    ],
    "error_message": null
  },
  "message": "Registration status retrieved successfully"
}
```

#### **DELETE /registration/{registration_id}**
```python
# Response Schema
{
  "success": true,
  "data": {
    "registration_id": "reg_abc123def456",
    "state": "CANCELLED",
    "cleanup_performed": {
      "tenant_deleted": true,
      "user_deleted": true,
      "payment_cancelled": true,
      "resources_cleaned": true
    },
    "cancelled_at": "2025-08-13T19:45:00Z"
  },
  "message": "Registration cancelled and cleaned up successfully"
}

# Error Responses
404: Registration not found
409: Registration already completed (cannot cancel)
500: Cleanup failed
```

### **2. Stripe Webhook Handler - Eventos Detallados**

#### **POST /payment/webhooks/stripe**
```python
# Webhook Event Types Handled
SUPPORTED_EVENTS = [
    'invoice.payment_failed',
    'invoice.payment_succeeded',
    'customer.subscription.updated',
    'customer.subscription.deleted',
    'payment_method.attached',
    'payment_method.detached',
    'setup_intent.succeeded',
    'setup_intent.setup_failed'
]

# Event Processing Logic
class StripeWebhookProcessor:
    async def process_invoice_payment_failed(self, event):
        """
        Triggered when payment fails
        Actions:
        1. Update subscription status to PAST_DUE
        2. Increment failed_attempts counter
        3. Send payment failure notification
        4. Schedule suspension if max attempts reached
        5. Log event for audit
        """

    async def process_invoice_payment_succeeded(self, event):
        """
        Triggered when payment succeeds
        Actions:
        1. Update subscription status to ACTIVE
        2. Reset failed_attempts counter
        3. Reactivate tenant if suspended
        4. Send payment confirmation
        5. Update next billing date
        """

    async def process_subscription_updated(self, event):
        """
        Triggered when subscription changes
        Actions:
        1. Sync subscription data with local database
        2. Update plan limits if plan changed
        3. Notify tenant of changes
        4. Update billing information
        """

    async def process_subscription_deleted(self, event):
        """
        Triggered when subscription is cancelled
        Actions:
        1. Update subscription status to CANCELLED
        2. Schedule tenant suspension
        3. Send cancellation confirmation
        4. Initiate data retention procedures
        """

# Response Format
{
  "success": true,
  "data": {
    "event_id": "evt_stripe_123",
    "event_type": "invoice.payment_failed",
    "processed_at": "2025-08-13T19:30:00Z",
    "actions_taken": [
      "Updated subscription status to PAST_DUE",
      "Sent payment failure notification",
      "Scheduled suspension check"
    ]
  },
  "message": "Webhook processed successfully"
}
```

### **3. Setup Service - Configuración Detallada**

#### **POST /setup/tenant/{tenant_id}**
```python
# Request Body Schema
{
  "plan_id": "string (required)",
  "region": "string (optional, default: us-east-1)",
  "configuration": {
    "enable_analytics": "boolean (default: true)",
    "enable_monitoring": "boolean (default: true)",
    "backup_enabled": "boolean (default: true)",
    "encryption_enabled": "boolean (default: true)"
  }
}

# Response Schema
{
  "success": true,
  "data": {
    "tenant_id": "tnt_xyz789",
    "setup_id": "setup_abc123",
    "status": "COMPLETED",
    "resources_created": {
      "dynamodb_tables": [
        {
          "table_name": "tnt_xyz789-data",
          "arn": "arn:aws:dynamodb:us-east-1:************:table/tnt_xyz789-data",
          "status": "ACTIVE"
        },
        {
          "table_name": "tnt_xyz789-conversations",
          "arn": "arn:aws:dynamodb:us-east-1:************:table/tnt_xyz789-conversations",
          "status": "ACTIVE"
        }
      ],
      "s3_buckets": [
        {
          "bucket_name": "tnt_xyz789-data-us-east-1",
          "region": "us-east-1",
          "versioning": true,
          "encryption": true
        },
        {
          "bucket_name": "tnt_xyz789-processed-us-east-1",
          "region": "us-east-1",
          "versioning": true,
          "encryption": true
        }
      ],
      "iam_roles": [
        {
          "role_name": "tnt_xyz789-agent-role",
          "arn": "arn:aws:iam::************:role/tnt_xyz789-agent-role",
          "policies": ["DynamoDBAccess", "S3Access"]
        }
      ],
      "cloudwatch_dashboards": [
        {
          "dashboard_name": "tnt_xyz789-overview",
          "url": "https://console.aws.amazon.com/cloudwatch/home#dashboards:name=tnt_xyz789-overview"
        }
      ]
    },
    "plan_limits_configured": {
      "max_users": 5,
      "max_agents": 10,
      "storage_gb": 50,
      "api_calls_per_month": 100000,
      "read_capacity_units": 25,
      "write_capacity_units": 25
    },
    "agent_configurations": {
      "feedo": {
        "enabled": true,
        "max_file_size_mb": 100,
        "supported_formats": ["csv", "xlsx", "json"],
        "processing_timeout_minutes": 30
      },
      "forecaster": {
        "enabled": true,
        "max_data_points": 1000000,
        "analysis_timeout_minutes": 60,
        "report_formats": ["pdf", "xlsx", "json"]
      }
    },
    "setup_duration_seconds": 45,
    "created_at": "2025-08-13T19:30:00Z",
    "completed_at": "2025-08-13T19:30:45Z"
  },
  "message": "Tenant setup completed successfully"
}

# Error Responses
400: Invalid tenant ID or plan ID
409: Tenant already configured
422: Invalid configuration parameters
500: Setup failed (partial cleanup performed)
```

#### **DELETE /setup/tenant/{tenant_id}**
```python
# Response Schema
{
  "success": true,
  "data": {
    "tenant_id": "tnt_xyz789",
    "cleanup_id": "cleanup_abc123",
    "status": "COMPLETED",
    "resources_deleted": {
      "dynamodb_tables": ["tnt_xyz789-data", "tnt_xyz789-conversations"],
      "s3_buckets": ["tnt_xyz789-data-us-east-1", "tnt_xyz789-processed-us-east-1"],
      "iam_roles": ["tnt_xyz789-agent-role"],
      "cloudwatch_dashboards": ["tnt_xyz789-overview"]
    },
    "data_backup": {
      "backup_created": true,
      "backup_location": "s3://platform-backups/tnt_xyz789/2025-08-13/",
      "retention_days": 90
    },
    "cleanup_duration_seconds": 30,
    "started_at": "2025-08-13T19:30:00Z",
    "completed_at": "2025-08-13T19:30:30Z"
  },
  "message": "Tenant cleanup completed successfully"
}
```

### **4. User Management - Endpoints Faltantes**

#### **PUT /tenant/users/{user_id}**
```python
# Request Body Schema
{
  "status": "string (optional: ACTIVE|INACTIVE)",
  "role": "string (optional: ADMIN|MEMBER|VIEWER)",
  "permissions": {
    "can_invite_users": "boolean (optional)",
    "can_view_analytics": "boolean (optional)",
    "can_export_data": "boolean (optional)"
  },
  "reason": "string (optional, for audit log)"
}

# Response Schema
{
  "success": true,
  "data": {
    "user": {
      "user_id": "usr_member_456",
      "email": "<EMAIL>",
      "first_name": "Jane",
      "last_name": "Member",
      "role": "MEMBER",
      "status": "INACTIVE",
      "permissions": {
        "can_invite_users": false,
        "can_view_analytics": true,
        "can_export_data": false
      },
      "updated_at": "2025-08-13T19:30:00Z",
      "updated_by": "usr_master_123"
    },
    "changes_applied": ["status", "permissions"],
    "audit_log": {
      "action": "USER_UPDATED",
      "performed_by": "usr_master_123",
      "reason": "User requested temporary deactivation",
      "timestamp": "2025-08-13T19:30:00Z"
    }
  },
  "message": "User updated successfully"
}

# Error Responses
403: Insufficient permissions (only MASTER/ADMIN can update)
404: User not found
409: Cannot modify master user
422: Invalid status or role
```

#### **DELETE /tenant/users/{user_id}**
```python
# Request Body Schema
{
  "reason": "string (required, for audit log)",
  "transfer_data_to": "string (optional, user_id to transfer ownership)"
}

# Response Schema
{
  "success": true,
  "data": {
    "user": {
      "user_id": "usr_member_456",
      "email": "<EMAIL>",
      "status": "DELETED",
      "deleted_at": "2025-08-13T19:30:00Z",
      "deleted_by": "usr_master_123"
    },
    "data_handling": {
      "conversations_transferred": true,
      "reports_transferred": true,
      "transfer_to_user": "usr_admin_789",
      "personal_data_removed": true
    },
    "audit_log": {
      "action": "USER_DELETED",
      "performed_by": "usr_master_123",
      "reason": "Employee left company",
      "timestamp": "2025-08-13T19:30:00Z"
    }
  },
  "message": "User deleted successfully"
}

# Error Responses
403: Insufficient permissions (only MASTER can delete)
404: User not found
409: Cannot delete master user or last admin
422: Invalid transfer user ID
```

---

## 🔄 **Flujos de Integración Detallados**

### **Flujo 1: Registro Completo Automatizado**

```mermaid
sequenceDiagram
    participant U as Usuario
    participant O as Orchestrator
    participant T as Tenant Service
    participant A as Auth Service
    participant P as Payment Service
    participant S as Setup Service
    participant E as Email Service
    participant ST as Stripe

    Note over U: 1. INICIO DEL REGISTRO
    U->>O: POST /registration/complete
    O->>O: Generate registration_id
    O->>O: Set state = INITIATED

    Note over O: 2. CREAR TENANT
    O->>T: POST /register (company_data)
    T->>T: Validate & create tenant
    T->>O: tenant_id
    O->>O: Set state = TENANT_CREATED

    Note over O: 3. CREAR USUARIO MASTER
    O->>A: POST /register (user_data + tenant_id)
    A->>A: Create user (inactive)
    A->>O: user_id + verification_token
    O->>O: Set state = USER_CREATED

    Note over O: 4. ENVIAR EMAIL VERIFICACIÓN
    O->>E: Send verification email
    E->>U: Email with verification link
    O->>O: Set state = EMAIL_SENT
    O->>U: Registration initiated response

    Note over U: 5. VERIFICAR EMAIL
    U->>O: POST /registration/verify (token)
    O->>A: Verify token
    A->>O: Token valid
    O->>O: Set state = EMAIL_VERIFIED
    O->>P: GET /plans (available plans)
    P->>O: Plans list
    O->>U: Plans available for selection

    Note over U: 6. SELECCIONAR PLAN Y PAGAR
    U->>O: POST /registration/payment (plan + payment_method)
    O->>O: Set state = PAYMENT_PROCESSING
    O->>P: POST /subscriptions (create subscription)
    P->>ST: Create Stripe subscription
    ST->>P: Subscription created
    P->>O: subscription_id
    O->>O: Set state = PAYMENT_COMPLETED

    Note over O: 7. CONFIGURAR RECURSOS
    O->>O: Set state = CONFIGURING
    O->>S: POST /setup/tenant (tenant_id + plan_id)
    S->>S: Create DynamoDB tables
    S->>S: Create S3 buckets
    S->>S: Setup IAM roles
    S->>S: Configure monitoring
    S->>O: Setup completed

    Note over O: 8. ACTIVAR USUARIO Y FINALIZAR
    O->>A: Activate user
    A->>O: User activated
    O->>E: Send welcome email
    E->>U: Welcome email
    O->>O: Set state = COMPLETED
    O->>U: Registration complete + access_tokens
```

### **Flujo 2: Manejo de Fallos de Pago**

```mermaid
sequenceDiagram
    participant ST as Stripe
    participant W as Webhook Handler
    participant P as Payment Service
    participant T as Tenant Service
    participant E as Email Service
    participant J as Scheduled Jobs

    Note over ST: PAGO FALLA
    ST->>W: invoice.payment_failed event
    W->>W: Verify webhook signature
    W->>P: Get subscription details
    P->>W: Subscription info

    Note over W: PROCESAR FALLO
    W->>P: Update status to PAST_DUE
    W->>P: Increment failed_attempts
    P->>W: Status updated

    Note over W: NOTIFICAR USUARIO
    W->>E: Send payment failed notification
    E->>E: Send email to tenant admin

    Note over W: EVALUAR SUSPENSIÓN
    alt failed_attempts < 3
        W->>J: Schedule retry in 24h
    else failed_attempts >= 3
        W->>J: Schedule suspension in 24h
        W->>E: Send final notice email
    end

    Note over J: JOB DE SUSPENSIÓN (24h después)
    J->>P: Check subscription status
    alt Still PAST_DUE
        J->>T: Suspend tenant
        T->>T: Set status = SUSPENDED
        T->>T: Restrict access to billing only
        J->>E: Send suspension notification
    else Payment succeeded
        J->>J: Cancel suspension
    end
```

### **Flujo 3: Suspensión y Reactivación de Tenant**

```mermaid
sequenceDiagram
    participant J as Scheduled Job
    participant T as Tenant Service
    participant P as Payment Service
    participant A as Auth Service
    participant E as Email Service
    participant U as Usuario

    Note over J: VERIFICACIÓN DIARIA DE PAGOS
    J->>P: Get overdue subscriptions
    P->>J: List of overdue subscriptions

    loop For each overdue subscription
        J->>T: Suspend tenant
        T->>T: Set status = SUSPENDED
        T->>A: Restrict user access
        A->>A: Update user permissions
        J->>E: Send suspension notice
        E->>E: Email to tenant admin
    end

    Note over U: USUARIO INTENTA ACCEDER
    U->>A: POST /login
    A->>A: Validate credentials
    A->>T: Check tenant status
    T->>A: Status = SUSPENDED
    A->>U: Login success but limited access

    Note over U: SOLO ACCESO A BILLING
    U->>T: GET /profile
    T->>U: Limited profile (billing modules only)
    U->>P: Access billing/subscription endpoints
    P->>U: Full access to payment management

    Note over U: REACTIVAR SERVICIO
    U->>P: Update payment method
    P->>P: Process payment
    P->>T: Notify payment success
    T->>T: Set status = ACTIVE
    T->>A: Restore full access
    A->>A: Update user permissions
    T->>E: Send reactivation notice
    E->>E: Email confirmation
```

---

## 📊 **Métricas y KPIs de Monitoreo**

### **Métricas de Registro**
```python
# CloudWatch Custom Metrics
REGISTRATION_METRICS = {
    'RegistrationStarted': 'Count of registration attempts',
    'RegistrationCompleted': 'Count of successful registrations',
    'RegistrationAbandoned': 'Count of abandoned registrations',
    'RegistrationFailed': 'Count of failed registrations',
    'EmailVerificationRate': 'Percentage of emails verified',
    'PaymentSuccessRate': 'Percentage of successful payments',
    'SetupDuration': 'Time to complete tenant setup',
    'RegistrationConversionRate': 'Started to completed ratio'
}

# Alertas Críticas
CRITICAL_ALERTS = {
    'RegistrationFailureRate > 10%': 'High registration failure rate',
    'PaymentFailureRate > 5%': 'High payment failure rate',
    'SetupDuration > 2 minutes': 'Slow tenant setup',
    'EmailDeliveryFailure > 1%': 'Email delivery issues'
}
```

### **Métricas de Operación**
```python
# Métricas de Performance
PERFORMANCE_METRICS = {
    'APIResponseTime': 'Average response time per endpoint',
    'DatabaseQueryTime': 'Average database query duration',
    'StripeAPILatency': 'Stripe API call latency',
    'EmailDeliveryTime': 'Email delivery duration',
    'ResourceSetupTime': 'AWS resource creation time'
}

# Métricas de Negocio
BUSINESS_METRICS = {
    'ActiveTenants': 'Number of active tenants',
    'SuspendedTenants': 'Number of suspended tenants',
    'MonthlyRecurringRevenue': 'MRR calculation',
    'ChurnRate': 'Monthly churn percentage',
    'PlanDistribution': 'Distribution across plans',
    'UserGrowthRate': 'New users per month'
}
```

---

## 🛡️ **Consideraciones de Seguridad**

### **Autenticación y Autorización**
```python
# JWT Token Configuration
JWT_CONFIG = {
    'access_token_expiry': 3600,  # 1 hour
    'refresh_token_expiry': 2592000,  # 30 days
    'algorithm': 'RS256',
    'issuer': 'logistics-platform',
    'audience': 'platform-users'
}

# Role-Based Access Control
RBAC_PERMISSIONS = {
    'MASTER': [
        'tenant:*',
        'users:*',
        'billing:*',
        'analytics:*',
        'agents:*'
    ],
    'ADMIN': [
        'users:read',
        'users:invite',
        'users:update',
        'analytics:read',
        'agents:*'
    ],
    'MEMBER': [
        'agents:read',
        'agents:use',
        'profile:read'
    ]
}
```

### **Validación de Datos**
```python
# Input Validation Rules
VALIDATION_RULES = {
    'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'password': {
        'min_length': 8,
        'require_uppercase': True,
        'require_lowercase': True,
        'require_digit': True,
        'require_special': True
    },
    'company_name': {
        'min_length': 2,
        'max_length': 100,
        'allowed_chars': r'^[a-zA-Z0-9\s\-\.&]+$'
    },
    'phone': r'^\+[1-9]\d{1,14}$'  # E.164 format
}

# Rate Limiting Configuration
RATE_LIMITS = {
    'registration': '5/minute',
    'login': '10/minute',
    'password_reset': '3/minute',
    'email_verification': '5/minute',
    'payment_operations': '20/minute'
}
```

### **Encriptación y Almacenamiento**
```python
# Encryption Configuration
ENCRYPTION_CONFIG = {
    'at_rest': {
        'dynamodb': 'AWS_KMS',
        's3': 'AES256',
        'rds': 'AWS_KMS'
    },
    'in_transit': {
        'api_gateway': 'TLS_1.2',
        'internal_services': 'TLS_1.2',
        'database_connections': 'SSL'
    },
    'application_level': {
        'pii_fields': 'AES_256_GCM',
        'payment_data': 'STRIPE_ENCRYPTION',
        'passwords': 'BCRYPT_12_ROUNDS'
    }
}

# Data Retention Policies
DATA_RETENTION = {
    'user_data': '7_years',
    'audit_logs': '7_years',
    'payment_records': '7_years',
    'conversation_data': '2_years',
    'analytics_data': '3_years',
    'temporary_files': '30_days'
}
```

---

## 🚀 **Plan de Deployment**

### **Estrategia de Deployment**
```yaml
# deployment/environments.yml
environments:
  development:
    aws_account: "************"
    region: "us-east-1"
    stage: "dev"
    domain: "api-dev.platform.com"

  staging:
    aws_account: "************"
    region: "us-east-1"
    stage: "staging"
    domain: "api-staging.platform.com"

  production:
    aws_account: "************"
    region: "us-east-1"
    stage: "prod"
    domain: "api.platform.com"

deployment_strategy:
  type: "blue_green"
  health_check_grace_period: 300
  rollback_on_failure: true
  canary_percentage: 10
```

### **Pipeline de CI/CD**
```yaml
# .github/workflows/deploy-platform.yml
name: Deploy Platform Services

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth, tenant, payment, orchestrator, setup]
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd services/${{ matrix.service }}
          npm ci
      - name: Run unit tests
        run: |
          cd services/${{ matrix.service }}
          npm run test:unit
      - name: Run integration tests
        run: |
          cd services/${{ matrix.service }}
          npm run test:integration
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run security scan
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: 'security-scan-results.sarif'

  deploy-dev:
    needs: [test, security-scan]
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth, tenant, payment, orchestrator, setup]
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to development
        run: |
          cd services/${{ matrix.service }}
          npx serverless deploy --stage dev
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.DEV_AWS_SECRET_ACCESS_KEY }}

  deploy-staging:
    needs: [test, security-scan]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth, tenant, payment, orchestrator, setup]
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to staging
        run: |
          cd services/${{ matrix.service }}
          npx serverless deploy --stage staging
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.STAGING_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.STAGING_AWS_SECRET_ACCESS_KEY }}

  integration-tests-staging:
    needs: deploy-staging
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run end-to-end tests
        run: npm run test:e2e
        env:
          TEST_BASE_URL: "https://api-staging.platform.com"

  deploy-production:
    needs: integration-tests-staging
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production
    strategy:
      matrix:
        service: [auth, tenant, payment, orchestrator, setup]
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          cd services/${{ matrix.service }}
          npx serverless deploy --stage prod
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
```

---

## 📋 **Checklist Final de Implementación**

### **✅ Fase 1: Orchestrator Service (Días 1-3)**
- [ ] **Día 1**: Estructura del proyecto y modelos de datos
  - [ ] Crear estructura de directorios
  - [ ] Implementar modelos de datos (RegistrationRequest, RegistrationState)
  - [ ] Configurar serverless.yml
  - [ ] Setup de dependencias y requirements.txt

- [ ] **Día 2**: State Machine y Service Core
  - [ ] Implementar RegistrationStateMachine
  - [ ] Implementar OrchestrationService
  - [ ] Implementar StateManager para persistencia
  - [ ] Implementar ServiceClients para comunicación

- [ ] **Día 3**: Handlers y Testing
  - [ ] Implementar registration_complete_handler
  - [ ] Implementar registration_verify_handler
  - [ ] Implementar registration_payment_handler
  - [ ] Implementar registration_status_handler
  - [ ] Unit tests básicos
  - [ ] Deploy a dev environment

### **✅ Fase 2: Webhook Handler (Días 4-5)**
- [ ] **Día 4**: Webhook Infrastructure
  - [ ] Implementar signature verification
  - [ ] Implementar event routing
  - [ ] Implementar BaseWebhookProcessor
  - [ ] Configurar Stripe webhook endpoint

- [ ] **Día 5**: Event Processors
  - [ ] Implementar PaymentFailedProcessor
  - [ ] Implementar PaymentSucceededProcessor
  - [ ] Implementar SubscriptionUpdatedProcessor
  - [ ] Implementar SubscriptionDeletedProcessor
  - [ ] Testing con Stripe CLI
  - [ ] Deploy y configuración de webhook en Stripe

### **✅ Fase 3: Setup Service (Días 6-8)**
- [ ] **Día 6**: Infrastructure Setup
  - [ ] Crear templates de CloudFormation
  - [ ] Implementar AWSResourceManager
  - [ ] Implementar DatabaseSetupService
  - [ ] Configurar IAM roles y policies

- [ ] **Día 7**: Storage y Monitoring
  - [ ] Implementar StorageSetupService
  - [ ] Implementar MonitoringSetupService
  - [ ] Configurar plan-specific limits
  - [ ] Implementar cleanup procedures

- [ ] **Día 8**: Testing y Integration
  - [ ] Unit tests para setup services
  - [ ] Integration tests con AWS
  - [ ] Error handling y recovery testing
  - [ ] Deploy a dev environment

### **✅ Fase 4: Scheduled Jobs (Días 9-10)**
- [ ] **Día 9**: Jobs Implementation
  - [ ] Implementar RegistrationCleanupJob
  - [ ] Implementar PaymentValidationJob
  - [ ] Implementar PaymentReminderJob
  - [ ] Configurar CloudWatch Events

- [ ] **Día 10**: Jobs Testing y Deployment
  - [ ] Testing de jobs con datos mock
  - [ ] Configurar monitoring para jobs
  - [ ] Deploy jobs a dev environment
  - [ ] Validar ejecución programada

### **✅ Fase 5: User Management (Días 11-12)**
- [ ] **Día 11**: User Management Endpoints
  - [ ] Implementar update_user_handler
  - [ ] Implementar delete_user_handler
  - [ ] Implementar get_user_handler
  - [ ] Validación de permisos y roles

- [ ] **Día 12**: Testing y Documentation
  - [ ] Unit tests para user management
  - [ ] Integration tests
  - [ ] Actualizar documentación de API
  - [ ] Deploy a dev environment

### **✅ Fase 6: Integration Testing (Días 13-15)**
- [ ] **Día 13**: End-to-End Testing
  - [ ] Test completo de flujo de registro
  - [ ] Test de flujo de webhook de Stripe
  - [ ] Test de suspensión y reactivación
  - [ ] Test de cleanup de registros abandonados

- [ ] **Día 14**: Performance Testing
  - [ ] Load testing de endpoints críticos
  - [ ] Performance testing de setup service
  - [ ] Memory y CPU profiling
  - [ ] Optimización de queries y recursos

- [ ] **Día 15**: Security Testing
  - [ ] Penetration testing básico
  - [ ] Validation de input sanitization
  - [ ] Testing de rate limiting
  - [ ] Audit de permisos y roles

### **✅ Fase 7: Production Deployment (Días 16-18)**
- [ ] **Día 16**: Staging Deployment
  - [ ] Deploy completo a staging
  - [ ] Configuración de monitoring
  - [ ] Setup de alerting
  - [ ] Validación de health checks

- [ ] **Día 17**: Production Preparation
  - [ ] Configuración de production environment
  - [ ] Setup de backup procedures
  - [ ] Configuración de disaster recovery
  - [ ] Final security review

- [ ] **Día 18**: Production Deployment
  - [ ] Deploy a production
  - [ ] Smoke testing en production
  - [ ] Monitoring validation
  - [ ] Documentation final

### **✅ Fase 8: Validation y Handover (Días 19-21)**
- [ ] **Día 19**: Platform Validation
  - [ ] Test completo de todos los flujos en production
  - [ ] Validation de métricas y alerting
  - [ ] Performance validation
  - [ ] Security validation

- [ ] **Día 20**: Documentation y Training
  - [ ] Finalizar documentación técnica
  - [ ] Crear guías de troubleshooting
  - [ ] Preparar materiales de training
  - [ ] Documentar procedures de operación

- [ ] **Día 21**: Handover
  - [ ] Sesión de handover con equipo de operaciones
  - [ ] Transfer de conocimiento
  - [ ] Setup de support procedures
  - [ ] Go-live final

---

## 🎯 **Criterios de Éxito**

### **Funcionales**
- ✅ **Registro completo automatizado** - 100% funcional
- ✅ **Manejo de pagos con Stripe** - Webhooks funcionando
- ✅ **Suspensión automática** - Jobs ejecutándose correctamente
- ✅ **Gestión de usuarios** - CRUD completo
- ✅ **Multitenant security** - Aislamiento garantizado

### **No Funcionales**
- ✅ **Performance** - < 2s response time para 95% de requests
- ✅ **Availability** - 99.9% uptime
- ✅ **Security** - Todas las validaciones implementadas
- ✅ **Scalability** - Soporta 1000+ tenants concurrentes
- ✅ **Monitoring** - Métricas y alerting completos

### **Negocio**
- ✅ **Registration conversion** - > 80% email verification rate
- ✅ **Payment success** - > 95% payment success rate
- ✅ **User experience** - Flujo completo en < 5 minutos
- ✅ **Support readiness** - Documentación y procedures completos

**Al completar este roadmap de 21 días, la plataforma estará 100% funcional, segura, escalable y lista para producción con todos los flujos requeridos implementados y validados.**
