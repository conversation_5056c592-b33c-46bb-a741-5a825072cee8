# serverless.yml
# Configuración principal de infraestructura - Serverless Framework
# Migrado desde Terraform para unificar infraestructura y código

service: ${self:custom.projectName}-infrastructure

frameworkVersion: '4'

# Variables personalizadas
custom:
  # Configuración del proyecto
  projectName: agent-scl
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  
  # Importar variables compartidas
  sharedVars: ${file(serverless/shared/variables.yml)}
  
  # Configuración específica del ambiente (usar variables compartidas)
  environment: ${self:custom.sharedVars.stages.${self:custom.stage}}

  # Configuración específica adicional para infraestructura
  infrastructureConfig:
    dev:
      domainName: api-platform-dev.agentscl.com
      certificateArn: arn:aws:acm:us-east-1:485950502364:certificate/c38576b6-b73d-487b-b52c-db60d617a4ca
      hostedZoneId: Z1D633PJN98FT9
      logLevel: DEBUG
      deletionProtection: false
      enableBackups: true
      dynamodbBillingMode: PAY_PER_REQUEST
    staging:
      domainName: api-platform-staging.agentscl.com
      certificateArn: arn:aws:acm:us-east-1:485950502364:certificate/staging-cert-id
      hostedZoneId: Z1D633PJN98FT9
      logLevel: INFO
      deletionProtection: true
      enableMonitoring: true
      dynamodbBillingMode: PAY_PER_REQUEST
      enableBackups: true
    prod:
      domainName: api-platform.agentscl.com
      certificateArn: arn:aws:acm:us-east-1:485950502364:certificate/prod-cert-id
      hostedZoneId: Z1D633PJN98FT9
      logLevel: WARN
      deletionProtection: true
      enableMonitoring: true
      dynamodbBillingMode: PROVISIONED
      enableBackups: true
  
  # Configuración de Python requirements
  # pythonRequirements (comentado para primer deploy de infraestructura)
  # pythonRequirements:
  #   dockerizePip: false
  #   slim: true
  #   strip: false
  #   layer: false
  #   useDownloadCache: true
  #   useStaticCache: true
  #   pipCmdExtraArgs:
  #     - --no-cache-dir
  
  # Configuración de dominio personalizado (comentado para primer deploy)
  # customDomain:
  #   domainName: ${self:custom.infrastructureConfig.${self:custom.stage}.domainName}
  #   certificateArn: ${self:custom.infrastructureConfig.${self:custom.stage}.certificateArn}
  #   hostedZoneId: ${self:custom.infrastructureConfig.${self:custom.stage}.hostedZoneId}
  #   stage: ${self:custom.stage}
  #   createRoute53Record: true
  #   endpointType: 'regional'
  #   securityPolicy: tls_1_2
  #   apiType: rest
  #   autoDomain: false

# Configuración del proveedor AWS
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  
  # Variables de entorno globales
  environment:
    PROJECT_NAME: ${self:custom.projectName}
    ENVIRONMENT: ${self:custom.stage}
    AWS_REGION: ${self:custom.region}
    LOG_LEVEL: ${self:custom.infrastructureConfig.${self:custom.stage}.logLevel, 'INFO'}
    DYNAMODB_TABLE: ${self:custom.projectName}-main-${self:custom.stage}
    S3_BUCKET: ${self:custom.projectName}-data-${self:custom.stage}

  # Configuración de trazabilidad
  tracing:
    lambda: true
    apiGateway: true

  # Configuración de logs
  logs:
    restApi: true
    level: ${self:custom.infrastructureConfig.${self:custom.stage}.logLevel, 'INFO'}
  
  # Configuración de API Gateway
  apiGateway:
    shouldStartNameWithService: true
    minimumCompressionSize: 1024
    binaryMediaTypes:
      - 'multipart/form-data'
      - 'application/octet-stream'
      - 'image/*'
    
  # Configuración de deployment (comentado para primer deploy)
  # deploymentBucket:
  #   name: ${self:custom.projectName}-serverless-deployments-${self:custom.stage}
  #   serverSideEncryption: AES256
  #   blockPublicAccess: true
  
  # Tags por defecto
  tags:
    Project: "Agent SCL - Supply Chain & Logistics"
    Environment: ${self:custom.stage}
    ManagedBy: "Serverless Framework"
    Service: infrastructure
    Owner: "Platform Team"
    CostCenter: "Engineering"

# Recursos de infraestructura AWS
resources:
  # Importar recursos desde archivos separados
  - ${file(serverless/resources/dynamodb.yml)}
  - ${file(serverless/resources/s3.yml)}
  - ${file(serverless/resources/iam.yml)}
  - ${file(serverless/resources/vpc.yml)}
  - ${file(serverless/resources/api-gateway.yml)}
  - ${file(serverless/resources/monitoring.yml)}
  - ${file(serverless/resources/outputs.yml)}
  # - ${file(serverless/resources/secrets.yml)}  # Comentado porque los secrets ya existen

# Plugins de Serverless (comentados para primer deploy de infraestructura)
# plugins:
#   - serverless-python-requirements
  # - serverless-domain-manager  # Comentado para primer deploy
  # - serverless-plugin-tracing  # Comentado para primer deploy
  # - serverless-plugin-aws-alerts  # No compatible con Serverless v4
  # - serverless-prune-plugin  # Comentado para primer deploy
  # - serverless-plugin-resource-tagging  # Comentado para primer deploy

# Configuración de plugins
package:
  patterns:
    - '!terraform/**'
    - '!tests/**'
    - '!docs/**'
    - '!.git/**'
    - '!.pytest_cache/**'
    - '!__pycache__/**'
    - '!*.pyc'
    - '!.env*'
    - '!README.md'

# Configuración de alertas (comentado para primer deploy - no compatible con Serverless v4)
# alerts:
#   stages:
#     - dev
#     - staging
#     - prod
#   topics:
#     alarm:
#       topic: ${self:custom.projectName}-${self:custom.stage}-alerts
#       notifications:
#         - protocol: email
#           endpoint: <EMAIL>
#   alarms:
#     - functionErrors
#     - functionDuration
#     - functionThrottles

# Configuración de limpieza de versiones (comentado para primer deploy - no compatible con Serverless v4)
# prune:
#   automatic: true
#   number: 5

# Outputs están definidos en la sección resources.Outputs
