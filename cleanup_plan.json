{"immediate_delete": {"backup_dirs": [".\\backup", ".\\backup\\serverless-framework", ".\\backup\\serverless-framework\\serverless", ".\\backup\\serverless-framework\\serverless\\services", ".\\backup\\serverless-framework\\serverless\\services\\api-gateway", ".\\backup\\serverless-framework\\serverless\\services\\auth", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\dateutil", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\dateutil\\parser", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\dateutil\\tz", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\dateutil\\zoneinfo", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\email_validator", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\jwt", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\crypto", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\crypto\\scrypt", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\crypto\\_blowfish", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\ext", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\ext\\django", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\handlers", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\tests", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\utils", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\utils\\compat", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\_data", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\passlib\\_data\\wordsets", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\pydantic", ".\\backup\\serverless-framework\\serverless\\services\\auth\\.serverless\\requirements\\requests", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\.bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\2-thenable", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\2-thenable\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\@iarna", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\@iarna\\toml", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\@iarna\\toml\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ansi-regex", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ansi-styles", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\appdirectory", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\appdirectory\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\appdirectory\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\balanced-match", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\balanced-match\\.github", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\bluebird", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\bluebird\\js", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\bluebird\\js\\browser", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\bluebird\\js\\release", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\brace-expansion", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\camelcase", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext\\lib\\private", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext\\lib\\private\\spawn", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext\\test\\spawn", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\child-process-ext\\test\\spawn\\_playground", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cliui", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\color-convert", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\color-name", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\concat-map", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\concat-map\\example", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\concat-map\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\core-util-is", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\core-util-is\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn\\lib\\util", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn\\node_modules", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn\\node_modules\\.bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn\\node_modules\\semver", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\cross-spawn\\node_modules\\semver\\bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\d", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\decamelize", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\duration", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\duration\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\emoji-regex", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\emoji-regex\\es2015", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\@@iterator", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\concat", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\copy-within", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\entries", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\fill", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\filter", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\find", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\find-index", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\keys", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\map", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\slice", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\splice", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\#\\values", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\from", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\array\\of", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\boolean", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\date", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\date\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\error", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\error\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\function\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\iterable", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\json", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\acosh", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\asinh", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\atanh", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\cbrt", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\clz32", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\cosh", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\expm1", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\fround", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\hypot", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\imul", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\log10", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\log1p", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\log2", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\sign", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\sinh", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\tanh", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\math\\trunc", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\epsilon", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\is-finite", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\is-integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\is-nan", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\is-safe-integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\max-safe-integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\number\\min-safe-integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\object\\assign", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\object\\entries", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\object\\keys", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\object\\set-prototype-of", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\promise", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\promise\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\promise\\#\\finally", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#\\match", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#\\replace", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#\\search", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#\\split", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#\\sticky", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\reg-exp\\#\\unicode", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\@@iterator", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\code-point-at", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\contains", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\ends-with", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\normalize", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\repeat", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\#\\starts-with", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\from-code-point", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es5-ext\\string\\raw", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-iterator", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-iterator\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-iterator\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-iterator\\test\\#", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-symbol", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-symbol\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-symbol\\lib\\private", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\es6-symbol\\lib\\private\\setup", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\esniff", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\esniff\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\esniff\\utils", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\event-emitter", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\event-emitter\\benchmark", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\event-emitter\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\math", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\promise", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\string", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\string_", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\docs\\thenable_", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\global-this", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\lib\\private", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\math", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\object\\entries", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\promise", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\string", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\string_", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\string_\\includes", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\ext\\thenable_", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\find-up", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\copy", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\empty", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\ensure", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\fs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\json", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\mkdirs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\move", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\output-file", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\path-exists", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\remove", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs-extra\\lib\\util", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\fs.realpath", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\get-caller-file", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\.github", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\.github\\workflows", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\example", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\example\\files", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\example\\files\\x", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\glob-all\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\graceful-fs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\immediate", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\immediate\\dist", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\immediate\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\inflight", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\inherits", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\is-docker", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\is-fullwidth-code-point", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\is-plain-object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\is-primitive", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\is-stream", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\is-wsl", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\isarray", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\isexe", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\isexe\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\isobject", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jsonfile", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\.github", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\.github\\workflows", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\dist", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\lib\\generate", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\lib\\nodejs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\lib\\reader", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\lib\\stream", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\jszip\\vendor", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lie", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lie\\dist", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lie\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\locate-path", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lodash", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lodash\\fp", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lodash.get", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lodash.uniqby", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\lodash.values", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\log", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\log\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\log\\lib\\private", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\log\\lib\\private\\abstract-writer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\log\\lib\\private\\logger-prototype", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\minimatch", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\next-tick", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\next-tick\\.github", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\next-tick\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\nice-try", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\nice-try\\src", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\once", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\p-limit", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\p-locate", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\p-try", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\pako", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\pako\\dist", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\pako\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\pako\\lib\\utils", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\pako\\lib\\zlib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\path-exists", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\path-is-absolute", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\path-key", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\process-nextick-args", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\readable-stream", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\readable-stream\\doc", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\readable-stream\\doc\\wg-meetings", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\readable-stream\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\readable-stream\\lib\\internal", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\readable-stream\\lib\\internal\\streams", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\require-directory", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\require-main-filename", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\rimraf", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\safe-buffer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\semver", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\semver\\bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\semver\\classes", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\semver\\functions", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\semver\\internal", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\semver\\ranges", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-iam-roles-per-function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-iam-roles-per-function\\dist", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-iam-roles-per-function\\dist\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-iam-roles-per-function\\src", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-iam-roles-per-function\\src\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-iam-roles-per-function\\src\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-plugin-tracing", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-plugin-tracing\\.nyc_output", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-python-requirements", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\serverless-python-requirements\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\set-blocking", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\set-value", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\setimmediate", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\sha256-file", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\shebang-command", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\shebang-regex", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\shell-quote", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\shell-quote\\.github", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\shell-quote\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\split2", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\split2\\node_modules", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\split2\\node_modules\\readable-stream", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\split2\\node_modules\\readable-stream\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\split2\\node_modules\\readable-stream\\lib\\internal", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\split2\\node_modules\\readable-stream\\lib\\internal\\streams", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\sprintf-kit", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\sprintf-kit\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\sprintf-kit\\modifiers", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\stream-promise", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\stream-promise\\test", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\string-width", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\string_decoder", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\string_decoder\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\strip-ansi", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\array", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\array-length", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\array-like", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\big-int", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\constructor", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\date", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\docs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\error", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\finite", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\iterable", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\lib\\ensure", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\map", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\natural-number", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\number", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\plain-function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\plain-object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\promise", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\prototype", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\reg-exp", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\safe-integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\set", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\string", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\thenable", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\time-value", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\array", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\array-length", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\array-like", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\big-int", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\constructor", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\date", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\error", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\finite", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\iterable", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\map", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\natural-number", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\number", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\plain-function", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\plain-object", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\promise", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\prototype", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\reg-exp", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\safe-integer", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\set", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\string", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\thenable", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\time-value", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\ts-types\\value", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\type\\value", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\uni-global", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\uni-global\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\universalify", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\util-deprecate", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\which", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\which\\bin", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\which-module", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\wrap-ansi", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\wrappy", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\y18n", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\yargs", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\yargs\\build", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\yargs\\build\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\yargs\\locales", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\yargs-parser", ".\\backup\\serverless-framework\\serverless\\services\\auth\\node_modules\\yargs-parser\\lib", ".\\backup\\serverless-framework\\serverless\\services\\auth\\schemas", ".\\backup\\serverless-framework\\serverless\\services\\payment", ".\\backup\\serverless-framework\\serverless\\services\\shared", ".\\backup\\serverless-framework\\serverless\\services\\shared\\layer", ".\\backup\\serverless-framework\\serverless\\services\\tenant", ".\\backup\\serverless-framework\\serverless\\shared"], "cache_dirs": [".\\.pytest_cache", ".\\htmlcov", ".\\services\\auth\\src\\models\\__pycache__", ".\\services\\auth\\src\\services\\__pycache__", ".\\services\\tenant\\src\\models\\__pycache__", ".\\services\\user\\src\\handlers\\__pycache__", ".\\shared\\python\\shared\\__pycache__", ".\\tests\\__pycache__", ".\\tests\\e2e\\auth\\__pycache__", ".\\tests\\e2e\\payment\\__pycache__", ".\\tests\\infrastructure\\__pycache__", ".\\tests\\integration\\__pycache__", ".\\tests\\integration\\payment\\__pycache__", ".\\tests\\performance\\__pycache__", ".\\tests\\security\\__pycache__", ".\\tests\\unit\\__pycache__", ".\\tests\\unit\\auth\\models\\__pycache__", ".\\tests\\unit\\auth\\services\\__pycache__", ".\\tests\\unit\\payment\\__pycache__", ".\\tests\\unit\\payment\\models\\__pycache__", ".\\tests\\unit\\payment\\services\\__pycache__", ".\\tests\\unit\\shared\\__pycache__", ".\\tests\\unit\\tenant\\__pycache__", ".\\tests\\unit\\tenant\\services\\__pycache__"], "temp_files": [".\\.coverage", ".\\services\\auth\\src\\models\\__pycache__\\tenant.cpython-312.pyc", ".\\services\\auth\\src\\models\\__pycache__\\user.cpython-312.pyc", ".\\services\\auth\\src\\services\\__pycache__\\email_service.cpython-312.pyc", ".\\services\\auth\\src\\services\\__pycache__\\__init__.cpython-312.pyc", ".\\services\\tenant\\src\\models\\__pycache__\\tenant.cpython-312.pyc", ".\\services\\user\\src\\handlers\\__pycache__\\__init__.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\auth.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\cache.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\config.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\database.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\exceptions.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\logger.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\resilience.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\secrets_manager.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\validators.cpython-312.pyc", ".\\shared\\python\\shared\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\e2e\\auth\\__pycache__\\test_login_flow.cpython-312-pytest-8.2.2.pyc", ".\\tests\\e2e\\auth\\__pycache__\\test_password_reset_flow.cpython-312-pytest-8.2.2.pyc", ".\\tests\\e2e\\auth\\__pycache__\\test_registration_flow.cpython-312-pytest-8.2.2.pyc", ".\\tests\\e2e\\auth\\__pycache__\\test_tenant_isolation.cpython-312-pytest-8.2.2.pyc", ".\\tests\\e2e\\auth\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\e2e\\payment\\__pycache__\\test_subscription_lifecycle.cpython-312-pytest-8.2.2.pyc", ".\\tests\\e2e\\payment\\__pycache__\\test_webhook_processing.cpython-312-pytest-8.2.2.pyc", ".\\tests\\e2e\\payment\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\infrastructure\\__pycache__\\test_aws_connectivity.cpython-312-pytest-8.2.2.pyc", ".\\tests\\infrastructure\\__pycache__\\test_deployment.cpython-312-pytest-8.2.2.pyc", ".\\tests\\infrastructure\\__pycache__\\test_terraform_validation.cpython-312-pytest-8.2.2.pyc", ".\\tests\\infrastructure\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\integration\\payment\\__pycache__\\test_subscription_flow.cpython-312-pytest-8.2.2.pyc", ".\\tests\\integration\\payment\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\integration\\__pycache__\\test_auth_flow.cpython-312-pytest-8.2.2.pyc", ".\\tests\\integration\\__pycache__\\test_complete_system_flow.cpython-312-pytest-8.2.2.pyc", ".\\tests\\integration\\__pycache__\\test_email_integration.cpython-312-pytest-8.2.2.pyc", ".\\tests\\integration\\__pycache__\\test_stripe_integration.cpython-312-pytest-8.2.2.pyc", ".\\tests\\performance\\__pycache__\\test_api_performance.cpython-312-pytest-8.2.2.pyc", ".\\tests\\performance\\__pycache__\\test_service_performance.cpython-312-pytest-8.2.2.pyc", ".\\tests\\performance\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\security\\__pycache__\\test_authentication_security.cpython-312-pytest-8.2.2.pyc", ".\\tests\\security\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\auth\\models\\__pycache__\\test_tenant.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\auth\\models\\__pycache__\\test_user.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\auth\\models\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\auth\\services\\__pycache__\\test_email_service.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\auth\\services\\__pycache__\\test_password_service.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\auth\\services\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\payment\\models\\__pycache__\\test_plan.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\payment\\models\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\payment\\services\\__pycache__\\test_stripe_client.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\payment\\services\\__pycache__\\test_webhook_service.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\payment\\services\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\payment\\__pycache__\\test_plan_model.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\payment\\__pycache__\\test_subscription_model.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\payment\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\shared\\__pycache__\\test_validators.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\shared\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\tenant\\services\\__pycache__\\test_data_isolation_service.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\tenant\\services\\__pycache__\\test_provisioning_service.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\tenant\\services\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\tenant\\__pycache__\\__init__.cpython-312.pyc", ".\\tests\\unit\\__pycache__\\test_auth_models.cpython-312-pytest-8.2.2.pyc", ".\\tests\\unit\\__pycache__\\test_shared_auth.cpython-312-pytest-8.2.2.pyc", ".\\tests\\__pycache__\\conftest.cpython-312-pytest-8.2.2.pyc", ".\\tests\\__pycache__\\test_migration_success.cpython-312-pytest-8.2.2.pyc"], "obsolete_serverless": [], "backup_serverless": [".\\backup\\serverless-framework\\serverless\\services\\api-gateway\\serverless.yml", ".\\backup\\serverless-framework\\serverless\\services\\auth\\serverless.yml", ".\\backup\\serverless-framework\\serverless\\services\\payment\\serverless.yml", ".\\backup\\serverless-framework\\serverless\\services\\shared\\serverless.yml", ".\\backup\\serverless-framework\\serverless\\services\\tenant\\serverless.yml"]}, "consolidate": {"report_files": ["ARCHITECTURE_10_10_FINAL_REPORT.md", "ARCHITECTURE_AUDIT_REPORT.md", "BUSINESS_LOGIC_10_10_FINAL_REPORT.md", "DEPLOYMENT_READINESS_AUDIT.md", "DEPLOY_COMPLETE_REPORT.md", "MIGRATION_100_PERCENT_COMPLETE.md", "MIGRATION_100_PERCENT_REAL_COMPLETE.md", "MIGRATION_AUDIT_COMPLETE.md", "validation_report.md"], "migration_files": ["MIGRATION_SUCCESS_SUMMARY.md"], "duplicate_scripts": []}, "keep": {"active_services": [".\\serverless.yml", ".\\serverless\\services\\api-gateway\\serverless.yml", ".\\serverless\\services\\shared\\serverless.yml", ".\\services\\admin\\serverless.yml", ".\\services\\auth\\serverless.yml", ".\\services\\events\\serverless.yml", ".\\services\\payment\\serverless.yml", ".\\services\\security\\serverless.yml", ".\\services\\tenant\\serverless.yml", ".\\services\\user\\serverless.yml", ".\\shared\\serverless.yml"], "main_docs": ["DOCUMENTATION_INDEX.md", "README.md"], "essential_scripts": ["scripts\\deploy-modular.sh", "scripts\\deploy-services.sh", "scripts\\deploy-ses.ps1", "scripts\\deploy-ses.sh", "scripts\\deploy.sh", "scripts\\setup-environment.ps1", "scripts\\setup-environment.sh", "scripts\\validate-and-deploy.sh", "scripts\\generate_coverage_report.py", "scripts\\run-tests.ps1", "scripts\\run-tests.sh", "scripts\\run_tests.py"]}}