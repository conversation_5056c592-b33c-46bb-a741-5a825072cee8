# 📝 **CODING STANDARDS - PLATAFORMA LOGÍSTICA AI**

## 🎯 **Objetivo**

Este documento define los estándares de codificación y convenciones para la plataforma logística AI para asegurar consistencia, mantenibilidad y calidad en todos los servicios.

---

## 🏗️ **ESTRUCTURA DE ARCHIVOS**

### **Handlers**
```python
# services/{service}/src/handlers/{action}.py
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """Standard handler function signature."""
    pass
```

### **Services**
```python
# services/{service}/src/services/{service}_service.py
class {Service}Service:
    """Service class for {service} operations."""
    pass
```

### **Models**
```python
# services/{service}/src/models/{model}_models.py
@dataclass
class {Model}:
    """Model class for {model} data."""
    pass
```

---

## 📦 **ESTÁNDARES DE IMPORTS**

### **Orden de Imports**
1. Standard library imports
2. Third-party imports  
3. Shared library imports
4. Local imports

### **Ejemplo Correcto**
```python
import os
import json
from typing import Dict, Any
from datetime import datetime

import boto3
import requests

from shared.responses import APIResponse, handle_cors_preflight
from shared.logger import lambda_logger
from shared.exceptions import ValidationException
from shared.validators import validate_email_address
from shared.request_utils import parse_request_body

from ..services.user_service import UserService
```

---

## 🔧 **ESTÁNDARES DE FUNCIONES**

### **Handler Functions**
```python
@rate_limit(requests_per_minute=X)
@measure_performance("service_operation")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Handler description.
    
    Args:
        event: API Gateway event
        context: Lambda context
        
    Returns:
        API response dictionary
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    try:
        # Parse request using shared utility
        data = parse_request_body(event)
        
        # Implementation
        result = perform_operation(data)
        return APIResponse.success(data=result)
        
    except ValidationException as e:
        return APIResponse.error(message=str(e), status_code=422)
    except Exception as e:
        lambda_logger.error("Unexpected error", extra={'error': str(e)})
        return APIResponse.error(message="Internal server error", status_code=500)
```

---

## 📝 **CONVENCIONES DE NAMING**

### **Archivos**
- `snake_case.py` para todos los archivos Python
- Nombres descriptivos: `create_user.py` no `user.py`

### **Funciones**
- `snake_case` para todas las funciones
- Nombres descriptivos: `validate_email_address()` no `validate()`

### **Variables**
- `snake_case` para variables
- Nombres descriptivos: `user_id` no `id`

### **Clases**
- `PascalCase` para clases
- Nombres descriptivos: `UserService` no `Service`

### **Constantes**
- `UPPER_SNAKE_CASE` para constantes
- Nombres descriptivos: `MAX_RETRY_ATTEMPTS` no `MAX`

---

## 🚨 **MANEJO DE ERRORES**

### **Patrón Estándar**
```python
try:
    # Operation
    result = perform_operation()
    return APIResponse.success(data=result)
except ValidationException as e:
    return APIResponse.error(message=str(e), status_code=422)
except AuthorizationException as e:
    return APIResponse.error(message=str(e), status_code=403)
except PlatformException as e:
    return APIResponse.error(message=str(e), status_code=500)
except Exception as e:
    lambda_logger.error("Unexpected error", extra={'error': str(e)})
    return APIResponse.error(message="Internal server error", status_code=500)
```

### **Logging de Errores**
```python
lambda_logger.error("Operation failed", extra={
    'request_id': request_id,
    'operation': 'specific_action',
    'error': str(e),
    'error_type': type(e).__name__
})
```

---

## ✅ **VALIDACIÓN**

### **Usar Shared Validators**
```python
from shared.validators import validate_email_address, validate_required_fields
from shared.request_utils import parse_request_body

# Email validation
email = validate_email_address(email)

# Required fields
validate_required_fields(data, ['field1', 'field2'])

# Request parsing
data = parse_request_body(event)
```

### **NO Duplicar Validaciones**
```python
# ❌ INCORRECTO - Validación duplicada
if '@' not in email or '.' not in email:
    raise ValidationException("Invalid email")

# ✅ CORRECTO - Usar shared validator
email = validate_email_address(email)
```

---

## 📚 **DOCUMENTACIÓN**

### **Docstrings**
```python
def function_name(param1: str, param2: int) -> Dict[str, Any]:
    """
    Brief description of function.
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        Description of return value
        
    Raises:
        ValidationException: When validation fails
        PlatformException: When operation fails
    """
```

### **Comentarios en Código**
```python
# Validate user permissions before proceeding
if not user.has_permission('create_tenant'):
    raise AuthorizationException("Insufficient permissions")

# TODO: Implement caching for better performance
result = expensive_operation()
```

---

## 🧪 **TESTING**

### **Naming de Tests**
- `test_{feature}.py` para archivos de test
- `Test{Feature}` para clases de test
- `test_{scenario}_{expected_outcome}` para métodos de test

### **Estructura de Tests**
```python
def test_create_user_success(self, fixtures):
    # Arrange
    user_data = {
        'email': '<EMAIL>',
        'name': 'Test User'
    }
    
    # Act
    result = create_user(user_data)
    
    # Assert
    assert result['success'] == True
    assert result['user_id'] is not None
```

---

## 🔄 **CHECKLIST DE CODE REVIEW**

### **Antes de Commit**
- [ ] Sigue naming conventions
- [ ] Usa shared utilities (no duplicación)
- [ ] Tiene manejo de errores apropiado
- [ ] Incluye docstrings comprehensivos
- [ ] Tiene tests correspondientes
- [ ] Sigue estándares de imports
- [ ] Usa formato de respuesta estándar
- [ ] Imports de CORS al nivel de módulo

### **Patrones Requeridos**
- [ ] `handler(event, context)` como función principal
- [ ] `APIResponse.success()` y `APIResponse.error()` para respuestas
- [ ] `validate_email_address()` para validación de email
- [ ] `parse_request_body()` para parsing de requests
- [ ] `lambda_logger` para logging
- [ ] `handle_cors_preflight()` importado al inicio

---

## 🚫 **ANTI-PATRONES A EVITAR**

### **NO Hacer**
```python
# ❌ Validación de email duplicada
if '@' not in email:
    return error

# ❌ Parsing de request duplicado
body = event.get('body', '{}')
data = json.loads(body)

# ❌ Import de CORS dentro de función
if event.get('httpMethod') == 'OPTIONS':
    from shared.responses import handle_cors_preflight

# ❌ Nombres de handler específicos
def registration_complete_handler():

# ❌ Manejo de errores inconsistente
return {'statusCode': 400, 'body': 'Error'}
```

### **SÍ Hacer**
```python
# ✅ Usar shared validator
email = validate_email_address(email)

# ✅ Usar shared utility
data = parse_request_body(event)

# ✅ Import de CORS al inicio
from shared.responses import handle_cors_preflight

# ✅ Nombre de handler estándar
def handler(event, context):

# ✅ Manejo de errores consistente
return APIResponse.error(message="Error", status_code=400)
```

---

## 📋 **HERRAMIENTAS DE CALIDAD**

### **Linting**
```bash
# Usar flake8 para linting
flake8 src/ tests/ --max-line-length=100

# Usar black para formateo
black src/ tests/

# Usar isort para imports
isort src/ tests/
```

### **Type Checking**
```bash
# Usar mypy para type checking
mypy src/
```

### **Security**
```bash
# Usar bandit para security scanning
bandit -r src/
```

---

## 🎯 **OBJETIVOS DE CALIDAD**

- **Consistencia**: 100% de handlers siguen el patrón estándar
- **Reutilización**: 0% duplicación de código crítico
- **Documentación**: 95%+ coverage de docstrings
- **Type Hints**: 90%+ coverage
- **Testing**: 85%+ code coverage

---

**Documento actualizado**: 2025-01-20  
**Versión**: 1.0  
**Próxima revisión**: Post-deployment a producción
