#!/usr/bin/env python3
"""
Setup script for Agent SCL Shared Layer
This allows installing the shared layer as a local package for development.
"""

from setuptools import setup, find_packages
import os

# Read requirements from requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

# Read long description from README if it exists
def read_long_description():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Agent SCL Shared Layer - Common utilities and models for the platform"

setup(
    name="agent-scl-shared",
    version="1.0.0",
    description="Agent SCL Shared Layer - Common utilities and models",
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    author="Platform Team",
    author_email="<EMAIL>",
    url="https://github.com/your-org/agent-scl-platform",
    
    # Package configuration
    packages=find_packages(),
    include_package_data=True,
    zip_safe=False,
    
    # Python version requirement
    python_requires=">=3.11",
    
    # Dependencies
    install_requires=read_requirements(),
    
    # Development dependencies
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'black>=22.0.0',
            'flake8>=5.0.0',
            'mypy>=1.0.0',
        ]
    },
    
    # Package classification
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
    ],
    
    # Entry points (if needed)
    entry_points={
        'console_scripts': [
            # Add any CLI commands here if needed
        ],
    },
    
    # Package data
    package_data={
        'shared': [
            '*.yml',
            '*.yaml',
            '*.json',
            '*.txt',
        ],
    },
)
