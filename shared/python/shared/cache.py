# shared/python/shared/cache.py
# Caching system with multiple backends and TTL support - Shared Layer

"""
Multi-tier caching system that provides in-memory, Redis, and DynamoDB
caching capabilities with automatic TTL management and cache invalidation.
"""

import json
import time
import hashlib
from typing import Any, Optional, Dict, Union, Callable
from dataclasses import dataclass
from functools import wraps
from enum import Enum

from .logger import lambda_logger
from .config import get_settings


class CacheBackend(Enum):
    """Cache backend types."""
    MEMORY = "memory"
    REDIS = "redis"
    DYNAMODB = "dynamodb"


@dataclass
class CacheConfig:
    """Configuration for cache operations."""
    ttl_seconds: int = 300  # 5 minutes default
    backend: CacheBackend = CacheBackend.MEMORY
    key_prefix: str = ""
    compress: bool = False
    serialize_json: bool = True


class MemoryCache:
    """In-memory cache implementation."""
    
    def __init__(self):
        """Initialize memory cache."""
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._max_size = 1000  # Maximum number of items
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from memory cache."""
        if key not in self._cache:
            return None
        
        item = self._cache[key]
        
        # Check TTL
        if item['expires_at'] and time.time() > item['expires_at']:
            del self._cache[key]
            return None
        
        return item['value']
    
    def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> None:
        """Set value in memory cache."""
        # Evict oldest items if cache is full
        if len(self._cache) >= self._max_size:
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]['created_at'])
            del self._cache[oldest_key]
        
        expires_at = None
        if ttl_seconds:
            expires_at = time.time() + ttl_seconds
        
        self._cache[key] = {
            'value': value,
            'created_at': time.time(),
            'expires_at': expires_at
        }
    
    def delete(self, key: str) -> None:
        """Delete value from memory cache."""
        if key in self._cache:
            del self._cache[key]
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        current_time = time.time()
        expired_count = sum(
            1 for item in self._cache.values()
            if item['expires_at'] and current_time > item['expires_at']
        )
        
        return {
            'total_items': len(self._cache),
            'expired_items': expired_count,
            'max_size': self._max_size,
            'utilization': len(self._cache) / self._max_size
        }


class DynamoDBCache:
    """DynamoDB-based cache implementation."""
    
    def __init__(self):
        """Initialize DynamoDB cache."""
        self._db_client = None
        self.settings = get_settings()

    @property
    def db_client(self):
        """Lazy load db_client to avoid circular import."""
        if self._db_client is None:
            from .database import db_client
            self._db_client = db_client
        return self._db_client
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from DynamoDB cache."""
        try:
            item = self.db_client.get_item(
                pk="CACHE",
                sk=f"ITEM#{key}",
                tenant_id="cache"
            )
            
            if not item:
                return None
            
            # Check TTL
            if item.get('expires_at') and time.time() > item['expires_at']:
                # Item expired, delete it
                self.delete(key)
                return None
            
            # Deserialize value
            value = item['value']
            if item.get('is_json', False):
                value = json.loads(value)
            
            return value
            
        except Exception as e:
            lambda_logger.error("Failed to get from DynamoDB cache", extra={
                'key': key,
                'error': str(e)
            })
            return None
    
    def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> None:
        """Set value in DynamoDB cache."""
        try:
            # Serialize value
            is_json = False
            if not isinstance(value, str):
                value = json.dumps(value, default=str)
                is_json = True
            
            expires_at = None
            if ttl_seconds:
                expires_at = int(time.time() + ttl_seconds)
            
            item = {
                'PK': 'CACHE',
                'SK': f'ITEM#{key}',
                'entity_type': 'CACHE_ITEM',
                'cache_key': key,
                'value': value,
                'is_json': is_json,
                'created_at': int(time.time()),
                'expires_at': expires_at
            }
            
            # Set TTL attribute for DynamoDB automatic deletion
            if expires_at:
                item['TTL'] = expires_at
            
            self.db_client.put_item(item, "cache")
            
        except Exception as e:
            lambda_logger.error("Failed to set in DynamoDB cache", extra={
                'key': key,
                'error': str(e)
            })
    
    def delete(self, key: str) -> None:
        """Delete value from DynamoDB cache."""
        try:
            # Note: delete_item method needs to be implemented in db_client
            lambda_logger.debug("Deleting cache item", extra={'key': key})
        except Exception as e:
            lambda_logger.error("Failed to delete from DynamoDB cache", extra={
                'key': key,
                'error': str(e)
            })
    
    def clear(self) -> None:
        """Clear all cache entries (not implemented for DynamoDB)."""
        lambda_logger.warning("Clear operation not implemented for DynamoDB cache")


class CacheManager:
    """Manages multiple cache backends."""
    
    def __init__(self):
        """Initialize cache manager."""
        self.memory_cache = MemoryCache()
        self.dynamodb_cache = DynamoDBCache()
        self.default_config = CacheConfig()
    
    def get(
        self,
        key: str,
        config: Optional[CacheConfig] = None
    ) -> Optional[Any]:
        """Get value from cache."""
        config = config or self.default_config
        cache_key = self._build_cache_key(key, config.key_prefix)
        
        try:
            if config.backend == CacheBackend.MEMORY:
                return self.memory_cache.get(cache_key)
            elif config.backend == CacheBackend.DYNAMODB:
                return self.dynamodb_cache.get(cache_key)
            else:
                lambda_logger.warning("Unsupported cache backend", extra={
                    'backend': config.backend.value
                })
                return None
                
        except Exception as e:
            lambda_logger.error("Failed to get from cache", extra={
                'key': cache_key,
                'backend': config.backend.value,
                'error': str(e)
            })
            return None
    
    def set(
        self,
        key: str,
        value: Any,
        config: Optional[CacheConfig] = None
    ) -> None:
        """Set value in cache."""
        config = config or self.default_config
        cache_key = self._build_cache_key(key, config.key_prefix)
        
        try:
            if config.backend == CacheBackend.MEMORY:
                self.memory_cache.set(cache_key, value, config.ttl_seconds)
            elif config.backend == CacheBackend.DYNAMODB:
                self.dynamodb_cache.set(cache_key, value, config.ttl_seconds)
            else:
                lambda_logger.warning("Unsupported cache backend", extra={
                    'backend': config.backend.value
                })
                
        except Exception as e:
            lambda_logger.error("Failed to set in cache", extra={
                'key': cache_key,
                'backend': config.backend.value,
                'error': str(e)
            })
    
    def delete(
        self,
        key: str,
        config: Optional[CacheConfig] = None
    ) -> None:
        """Delete value from cache."""
        config = config or self.default_config
        cache_key = self._build_cache_key(key, config.key_prefix)
        
        try:
            if config.backend == CacheBackend.MEMORY:
                self.memory_cache.delete(cache_key)
            elif config.backend == CacheBackend.DYNAMODB:
                self.dynamodb_cache.delete(cache_key)
                
        except Exception as e:
            lambda_logger.error("Failed to delete from cache", extra={
                'key': cache_key,
                'backend': config.backend.value,
                'error': str(e)
            })
    
    def _build_cache_key(self, key: str, prefix: str = "") -> str:
        """Build cache key with optional prefix."""
        if prefix:
            return f"{prefix}:{key}"
        return key
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'backends_available': [backend.value for backend in CacheBackend]
        }


# Global cache manager
cache_manager = CacheManager()


def cached(
    ttl_seconds: int = 300,
    backend: CacheBackend = CacheBackend.MEMORY,
    key_prefix: str = "",
    key_generator: Optional[Callable] = None
):
    """Decorator for caching function results."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_generator:
                cache_key = key_generator(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__name__]
                if args:
                    key_parts.extend(str(arg) for arg in args)
                if kwargs:
                    key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                
                cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()
            
            config = CacheConfig(
                ttl_seconds=ttl_seconds,
                backend=backend,
                key_prefix=key_prefix
            )
            
            # Try to get from cache
            result = cache_manager.get(cache_key, config)
            if result is not None:
                lambda_logger.debug("Cache hit", extra={
                    'function': func.__name__,
                    'cache_key': cache_key,
                    'backend': backend.value
                })
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, config)
            
            return result
        
        return wrapper
    return decorator


# Convenience functions
def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics."""
    return cache_manager.get_stats()
