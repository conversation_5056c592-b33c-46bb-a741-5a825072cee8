# shared/python/shared/circuit_breaker.py
# Circuit breaker pattern implementation for resilience - Shared Layer

"""
Circuit breaker pattern implementation that provides fault tolerance
and prevents cascading failures in distributed systems.
"""

import time
from enum import Enum
from typing import Callable, Any, Optional, Dict
from dataclasses import dataclass
from functools import wraps
from threading import Lock

from .logger import lambda_logger
from .exceptions import PlatformException


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, calls fail fast
    HALF_OPEN = "half_open"  # Testing if service is back


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5          # Number of failures to open circuit
    recovery_timeout: int = 60          # Seconds to wait before trying again
    expected_exception: tuple = (Exception,)  # Exceptions that count as failures
    success_threshold: int = 3          # Successful calls needed to close circuit in half-open state
    timeout: Optional[float] = None     # Call timeout in seconds


class CircuitBreakerStats:
    """Statistics for circuit breaker."""
    
    def __init__(self):
        self.failure_count = 0
        self.success_count = 0
        self.total_calls = 0
        self.last_failure_time = None
        self.last_success_time = None
        self.state_changes = 0


class CircuitBreaker:
    """Circuit breaker implementation."""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        """Initialize circuit breaker."""
        self.name = name
        self.config = config
        self.state = CircuitState.CLOSED
        self.stats = CircuitBreakerStats()
        self.lock = Lock()
        self.half_open_success_count = 0
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        with self.lock:
            self.stats.total_calls += 1
            
            # Check if circuit should be opened
            if self.state == CircuitState.CLOSED:
                if self.stats.failure_count >= self.config.failure_threshold:
                    self._open_circuit()
            
            # Check if circuit should transition to half-open
            elif self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self._half_open_circuit()
            
            # Fail fast if circuit is open
            if self.state == CircuitState.OPEN:
                lambda_logger.warning("Circuit breaker is open - failing fast", extra={
                    'circuit_name': self.name,
                    'failure_count': self.stats.failure_count,
                    'last_failure_time': self.stats.last_failure_time
                })
                raise PlatformException(f"Circuit breaker '{self.name}' is open")
        
        # Execute the function
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
            
        except self.config.expected_exception as e:
            self._on_failure()
            raise
        except Exception as e:
            # Unexpected exceptions don't count as failures
            lambda_logger.error("Unexpected error in circuit breaker", extra={
                'circuit_name': self.name,
                'error': str(e)
            })
            raise
    
    def _on_success(self) -> None:
        """Handle successful execution."""
        with self.lock:
            self.stats.success_count += 1
            self.stats.last_success_time = time.time()
            
            if self.state == CircuitState.HALF_OPEN:
                self.half_open_success_count += 1
                
                if self.half_open_success_count >= self.config.success_threshold:
                    self._close_circuit()
            
            elif self.state == CircuitState.CLOSED:
                # Reset failure count on success
                self.stats.failure_count = 0
    
    def _on_failure(self) -> None:
        """Handle failed execution."""
        with self.lock:
            self.stats.failure_count += 1
            self.stats.last_failure_time = time.time()
            
            if self.state == CircuitState.HALF_OPEN:
                # Go back to open state
                self._open_circuit()
            
            elif self.state == CircuitState.CLOSED:
                if self.stats.failure_count >= self.config.failure_threshold:
                    self._open_circuit()
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        if not self.stats.last_failure_time:
            return False
        
        return (time.time() - self.stats.last_failure_time) >= self.config.recovery_timeout
    
    def _open_circuit(self) -> None:
        """Open the circuit."""
        if self.state != CircuitState.OPEN:
            self.state = CircuitState.OPEN
            self.stats.state_changes += 1
            
            lambda_logger.warning("Circuit breaker opened", extra={
                'circuit_name': self.name,
                'failure_count': self.stats.failure_count,
                'threshold': self.config.failure_threshold
            })
    
    def _half_open_circuit(self) -> None:
        """Transition to half-open state."""
        if self.state != CircuitState.HALF_OPEN:
            self.state = CircuitState.HALF_OPEN
            self.half_open_success_count = 0
            self.stats.state_changes += 1
            
            lambda_logger.info("Circuit breaker half-open", extra={
                'circuit_name': self.name
            })
    
    def _close_circuit(self) -> None:
        """Close the circuit."""
        if self.state != CircuitState.CLOSED:
            self.state = CircuitState.CLOSED
            self.stats.failure_count = 0
            self.half_open_success_count = 0
            self.stats.state_changes += 1
            
            lambda_logger.info("Circuit breaker closed", extra={
                'circuit_name': self.name
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        with self.lock:
            return {
                'name': self.name,
                'state': self.state.value,
                'config': {
                    'failure_threshold': self.config.failure_threshold,
                    'recovery_timeout': self.config.recovery_timeout,
                    'success_threshold': self.config.success_threshold
                },
                'stats': {
                    'failure_count': self.stats.failure_count,
                    'success_count': self.stats.success_count,
                    'total_calls': self.stats.total_calls,
                    'last_failure_time': self.stats.last_failure_time,
                    'last_success_time': self.stats.last_success_time,
                    'state_changes': self.stats.state_changes,
                    'success_rate': (
                        self.stats.success_count / self.stats.total_calls 
                        if self.stats.total_calls > 0 else 0
                    )
                }
            }


class CircuitBreakerManager:
    """Manages multiple circuit breakers."""
    
    def __init__(self):
        """Initialize circuit breaker manager."""
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.default_configs = {
            'database': CircuitBreakerConfig(failure_threshold=5, recovery_timeout=30),
            'external_api': CircuitBreakerConfig(failure_threshold=3, recovery_timeout=60),
            'email': CircuitBreakerConfig(failure_threshold=2, recovery_timeout=120),
            'payment': CircuitBreakerConfig(failure_threshold=2, recovery_timeout=300)
        }
    
    def get_circuit_breaker(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """Get or create circuit breaker."""
        if name not in self.circuit_breakers:
            if config is None:
                config = self.default_configs.get(name, CircuitBreakerConfig())
            
            self.circuit_breakers[name] = CircuitBreaker(name, config)
            
            lambda_logger.info("Circuit breaker created", extra={
                'circuit_name': name,
                'failure_threshold': config.failure_threshold,
                'recovery_timeout': config.recovery_timeout
            })
        
        return self.circuit_breakers[name]
    
    def execute_with_circuit_breaker(
        self,
        circuit_name: str,
        func: Callable,
        *args,
        config: Optional[CircuitBreakerConfig] = None,
        **kwargs
    ) -> Any:
        """Execute function with specified circuit breaker."""
        circuit_breaker = self.get_circuit_breaker(circuit_name, config)
        return circuit_breaker.call(func, *args, **kwargs)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all circuit breakers."""
        return {
            name: cb.get_stats()
            for name, cb in self.circuit_breakers.items()
        }


# Global circuit breaker manager
circuit_breaker_manager = CircuitBreakerManager()


def circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    success_threshold: int = 3
):
    """Decorator for applying circuit breaker pattern."""
    config = CircuitBreakerConfig(
        failure_threshold=failure_threshold,
        recovery_timeout=recovery_timeout,
        success_threshold=success_threshold
    )
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return circuit_breaker_manager.execute_with_circuit_breaker(
                name, func, *args, config=config, **kwargs
            )
        return wrapper
    return decorator
