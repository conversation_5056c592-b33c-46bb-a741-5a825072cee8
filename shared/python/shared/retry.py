# shared/python/shared/retry.py
# Retry logic with exponential backoff and jitter - Shared Layer

"""
Retry mechanism with exponential backoff, jitter, and configurable
retry policies for improved resilience and fault tolerance.
"""

import time
import random
from enum import Enum
from typing import Callable, Any, Optional, Tuple
from dataclasses import dataclass
from functools import wraps

from .logger import lambda_logger
from .exceptions import PlatformException


class BackoffStrategy(Enum):
    """Backoff strategies for retry logic."""
    FIXED = "fixed"
    LINEAR = "linear"
    EXPONENTIAL = "exponential"
    EXPONENTIAL_JITTER = "exponential_jitter"


@dataclass
class RetryConfig:
    """Configuration for retry logic."""
    max_attempts: int = 3
    base_delay: float = 1.0  # Base delay in seconds
    max_delay: float = 60.0  # Maximum delay in seconds
    backoff_strategy: BackoffStrategy = BackoffStrategy.EXPONENTIAL_JITTER
    retryable_exceptions: tuple = (Exception,)
    non_retryable_exceptions: tuple = ()
    jitter_factor: float = 0.1  # Jitter factor (0.0 to 1.0)


class RetryStats:
    """Statistics for retry operations."""
    
    def __init__(self):
        self.total_attempts = 0
        self.successful_attempts = 0
        self.failed_attempts = 0
        self.total_delay = 0.0
        self.max_attempts_reached = 0


class RetryExecutor:
    """Executes functions with retry logic."""
    
    def __init__(self, config: RetryConfig):
        """Initialize retry executor."""
        self.config = config
        self.stats = RetryStats()
    
    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with retry logic."""
        last_exception = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            self.stats.total_attempts += 1
            
            try:
                lambda_logger.debug("Retry attempt", extra={
                    'attempt': attempt,
                    'max_attempts': self.config.max_attempts,
                    'function': func.__name__ if hasattr(func, '__name__') else str(func)
                })
                
                result = func(*args, **kwargs)
                
                self.stats.successful_attempts += 1
                
                if attempt > 1:
                    lambda_logger.info("Retry succeeded", extra={
                        'attempt': attempt,
                        'function': func.__name__ if hasattr(func, '__name__') else str(func)
                    })
                
                return result
                
            except self.config.non_retryable_exceptions as e:
                lambda_logger.warning("Non-retryable exception encountered", extra={
                    'attempt': attempt,
                    'exception': str(e),
                    'exception_type': type(e).__name__,
                    'function': func.__name__ if hasattr(func, '__name__') else str(func)
                })
                self.stats.failed_attempts += 1
                raise
                
            except self.config.retryable_exceptions as e:
                last_exception = e
                self.stats.failed_attempts += 1
                
                if attempt == self.config.max_attempts:
                    self.stats.max_attempts_reached += 1
                    lambda_logger.error("Max retry attempts reached", extra={
                        'attempt': attempt,
                        'max_attempts': self.config.max_attempts,
                        'exception': str(e),
                        'exception_type': type(e).__name__,
                        'function': func.__name__ if hasattr(func, '__name__') else str(func)
                    })
                    break
                
                # Calculate delay
                delay = self._calculate_delay(attempt)
                self.stats.total_delay += delay
                
                lambda_logger.warning("Retry attempt failed, retrying", extra={
                    'attempt': attempt,
                    'max_attempts': self.config.max_attempts,
                    'delay_seconds': delay,
                    'exception': str(e),
                    'exception_type': type(e).__name__,
                    'function': func.__name__ if hasattr(func, '__name__') else str(func)
                })
                
                time.sleep(delay)
        
        # All attempts failed
        raise PlatformException(f"Max retry attempts ({self.config.max_attempts}) exceeded. Last error: {str(last_exception)}")
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt."""
        if self.config.backoff_strategy == BackoffStrategy.FIXED:
            delay = self.config.base_delay
            
        elif self.config.backoff_strategy == BackoffStrategy.LINEAR:
            delay = self.config.base_delay * attempt
            
        elif self.config.backoff_strategy == BackoffStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (2 ** (attempt - 1))
            
        elif self.config.backoff_strategy == BackoffStrategy.EXPONENTIAL_JITTER:
            base_delay = self.config.base_delay * (2 ** (attempt - 1))
            jitter = base_delay * self.config.jitter_factor * random.random()
            delay = base_delay + jitter
            
        else:
            delay = self.config.base_delay
        
        # Cap at max delay
        return min(delay, self.config.max_delay)
    
    def get_stats(self) -> dict:
        """Get retry statistics."""
        return {
            'total_attempts': self.stats.total_attempts,
            'successful_attempts': self.stats.successful_attempts,
            'failed_attempts': self.stats.failed_attempts,
            'total_delay': self.stats.total_delay,
            'max_attempts_reached': self.stats.max_attempts_reached,
            'success_rate': (
                self.stats.successful_attempts / self.stats.total_attempts
                if self.stats.total_attempts > 0 else 0
            ),
            'avg_delay': (
                self.stats.total_delay / self.stats.failed_attempts
                if self.stats.failed_attempts > 0 else 0
            )
        }


class RetryManager:
    """Manages retry configurations and executors."""
    
    def __init__(self):
        """Initialize retry manager."""
        self.default_configs = {
            'database': RetryConfig(
                max_attempts=3,
                base_delay=0.5,
                max_delay=10.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL_JITTER
            ),
            'external_api': RetryConfig(
                max_attempts=5,
                base_delay=1.0,
                max_delay=30.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL_JITTER
            ),
            'email': RetryConfig(
                max_attempts=3,
                base_delay=2.0,
                max_delay=60.0,
                backoff_strategy=BackoffStrategy.EXPONENTIAL
            ),
            'file_processing': RetryConfig(
                max_attempts=2,
                base_delay=5.0,
                max_delay=120.0,
                backoff_strategy=BackoffStrategy.LINEAR
            )
        }
    
    def execute_with_retry(
        self,
        func: Callable,
        *args,
        retry_name: str = 'default',
        config: Optional[RetryConfig] = None,
        **kwargs
    ) -> Any:
        """Execute function with retry logic."""
        if config is None:
            config = self.default_configs.get(retry_name, RetryConfig())
        
        executor = RetryExecutor(config)
        return executor.execute(func, *args, **kwargs)


# Global retry manager
retry_manager = RetryManager()


def retry(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_strategy: BackoffStrategy = BackoffStrategy.EXPONENTIAL_JITTER,
    retryable_exceptions: tuple = (Exception,),
    non_retryable_exceptions: tuple = ()
):
    """Decorator for adding retry logic to functions."""
    config = RetryConfig(
        max_attempts=max_attempts,
        base_delay=base_delay,
        max_delay=max_delay,
        backoff_strategy=backoff_strategy,
        retryable_exceptions=retryable_exceptions,
        non_retryable_exceptions=non_retryable_exceptions
    )
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            executor = RetryExecutor(config)
            return executor.execute(func, *args, **kwargs)
        return wrapper
    return decorator


# Convenience functions
def database_retry(func: Callable, *args, **kwargs) -> Any:
    """Execute function with database retry policy."""
    return retry_manager.execute_with_retry(func, *args, retry_name='database', **kwargs)


def external_api_retry(func: Callable, *args, **kwargs) -> Any:
    """Execute function with external API retry policy."""
    return retry_manager.execute_with_retry(func, *args, retry_name='external_api', **kwargs)


def email_retry(func: Callable, *args, **kwargs) -> Any:
    """Execute function with email retry policy."""
    return retry_manager.execute_with_retry(func, *args, retry_name='email', **kwargs)
