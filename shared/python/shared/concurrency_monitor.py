# shared/python/shared/concurrency_monitor.py
# Concurrency monitoring and alerting utilities

"""
Concurrency monitoring utilities for tracking Lambda performance
and detecting throttling issues.
"""

import time
import boto3
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta

from .logger import lambda_logger
from .metrics import metrics_manager


@dataclass
class ConcurrencyMetrics:
    """Concurrency metrics data class."""
    function_name: str
    concurrent_executions: int
    reserved_concurrency: Optional[int]
    throttles: int
    duration_avg: float
    duration_max: float
    invocations: int
    errors: int
    timestamp: datetime


class ConcurrencyMonitor:
    """Monitor Lambda concurrency and performance metrics."""
    
    def __init__(self):
        self._cloudwatch = None
        self._lambda_client = None
    
    def _get_cloudwatch_client(self):
        """Get CloudWatch client."""
        if self._cloudwatch is None:
            self._cloudwatch = boto3.client('cloudwatch')
        return self._cloudwatch
    
    def _get_lambda_client(self):
        """Get Lambda client."""
        if self._lambda_client is None:
            self._lambda_client = boto3.client('lambda')
        return self._lambda_client
    
    def record_concurrency_metrics(
        self,
        function_name: str,
        concurrent_executions: int,
        reserved_concurrency: Optional[int] = None
    ):
        """Record concurrency metrics for monitoring."""
        try:
            # Record custom metrics
            metrics_manager.put_metric(
                name="ConcurrentExecutions",
                value=concurrent_executions,
                unit="Count",
                dimensions={
                    "FunctionName": function_name,
                    "Service": self._extract_service_name(function_name)
                }
            )
            
            if reserved_concurrency:
                # Calculate utilization percentage
                utilization = (concurrent_executions / reserved_concurrency) * 100
                
                metrics_manager.put_metric(
                    name="ConcurrencyUtilization",
                    value=utilization,
                    unit="Percent",
                    dimensions={
                        "FunctionName": function_name,
                        "Service": self._extract_service_name(function_name)
                    }
                )
                
                # Alert if utilization is high
                if utilization > 80:
                    lambda_logger.warning(
                        "High concurrency utilization detected",
                        extra={
                            "function_name": function_name,
                            "utilization": utilization,
                            "concurrent_executions": concurrent_executions,
                            "reserved_concurrency": reserved_concurrency
                        }
                    )
            
            lambda_logger.debug(
                "Concurrency metrics recorded",
                extra={
                    "function_name": function_name,
                    "concurrent_executions": concurrent_executions,
                    "reserved_concurrency": reserved_concurrency
                }
            )
            
        except Exception as e:
            lambda_logger.error(
                "Failed to record concurrency metrics",
                extra={"error": str(e), "function_name": function_name}
            )
    
    def check_throttling(self, function_name: str, time_window_minutes: int = 5) -> Dict[str, Any]:
        """Check for throttling in the specified time window."""
        try:
            cloudwatch = self._get_cloudwatch_client()
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(minutes=time_window_minutes)
            
            # Get throttle metrics
            response = cloudwatch.get_metric_statistics(
                Namespace='AWS/Lambda',
                MetricName='Throttles',
                Dimensions=[
                    {
                        'Name': 'FunctionName',
                        'Value': function_name
                    }
                ],
                StartTime=start_time,
                EndTime=end_time,
                Period=60,  # 1 minute periods
                Statistics=['Sum']
            )
            
            total_throttles = sum(point['Sum'] for point in response['Datapoints'])
            
            # Get invocation count for context
            invocation_response = cloudwatch.get_metric_statistics(
                Namespace='AWS/Lambda',
                MetricName='Invocations',
                Dimensions=[
                    {
                        'Name': 'FunctionName',
                        'Value': function_name
                    }
                ],
                StartTime=start_time,
                EndTime=end_time,
                Period=300,  # 5 minute period
                Statistics=['Sum']
            )
            
            total_invocations = sum(point['Sum'] for point in invocation_response['Datapoints'])
            throttle_rate = (total_throttles / total_invocations * 100) if total_invocations > 0 else 0
            
            result = {
                'function_name': function_name,
                'time_window_minutes': time_window_minutes,
                'total_throttles': total_throttles,
                'total_invocations': total_invocations,
                'throttle_rate_percent': throttle_rate,
                'is_throttling': total_throttles > 0,
                'severity': self._get_throttle_severity(throttle_rate)
            }
            
            if total_throttles > 0:
                lambda_logger.warning(
                    "Throttling detected",
                    extra=result
                )
            
            return result
            
        except Exception as e:
            lambda_logger.error(
                "Failed to check throttling",
                extra={"error": str(e), "function_name": function_name}
            )
            return {
                'function_name': function_name,
                'error': str(e),
                'is_throttling': False
            }
    
    def get_concurrency_utilization(self, function_name: str) -> Dict[str, Any]:
        """Get current concurrency utilization for a function."""
        try:
            lambda_client = self._get_lambda_client()
            
            # Get function configuration
            function_config = lambda_client.get_function_configuration(
                FunctionName=function_name
            )
            
            reserved_concurrency = function_config.get('ReservedConcurrencyConfig', {}).get('ReservedConcurrentExecutions')
            
            # Get current concurrent executions from CloudWatch
            cloudwatch = self._get_cloudwatch_client()
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(minutes=1)
            
            response = cloudwatch.get_metric_statistics(
                Namespace='AWS/Lambda',
                MetricName='ConcurrentExecutions',
                Dimensions=[
                    {
                        'Name': 'FunctionName',
                        'Value': function_name
                    }
                ],
                StartTime=start_time,
                EndTime=end_time,
                Period=60,
                Statistics=['Maximum']
            )
            
            current_concurrent = 0
            if response['Datapoints']:
                current_concurrent = max(point['Maximum'] for point in response['Datapoints'])
            
            utilization = 0
            if reserved_concurrency and reserved_concurrency > 0:
                utilization = (current_concurrent / reserved_concurrency) * 100
            
            return {
                'function_name': function_name,
                'current_concurrent_executions': current_concurrent,
                'reserved_concurrency': reserved_concurrency,
                'utilization_percent': utilization,
                'status': self._get_utilization_status(utilization)
            }
            
        except Exception as e:
            lambda_logger.error(
                "Failed to get concurrency utilization",
                extra={"error": str(e), "function_name": function_name}
            )
            return {
                'function_name': function_name,
                'error': str(e)
            }
    
    def _extract_service_name(self, function_name: str) -> str:
        """Extract service name from function name."""
        # Function names typically follow pattern: agent-scl-{service}-{stage}-{function}
        parts = function_name.split('-')
        if len(parts) >= 3:
            return parts[2]  # service name
        return 'unknown'
    
    def _get_throttle_severity(self, throttle_rate: float) -> str:
        """Get throttle severity level."""
        if throttle_rate == 0:
            return 'none'
        elif throttle_rate < 1:
            return 'low'
        elif throttle_rate < 5:
            return 'medium'
        else:
            return 'high'
    
    def _get_utilization_status(self, utilization: float) -> str:
        """Get utilization status."""
        if utilization < 50:
            return 'healthy'
        elif utilization < 80:
            return 'warning'
        else:
            return 'critical'
    
    def create_concurrency_alarm(
        self,
        function_name: str,
        threshold_percent: float = 80,
        alarm_name: Optional[str] = None
    ) -> str:
        """Create CloudWatch alarm for high concurrency utilization."""
        try:
            cloudwatch = self._get_cloudwatch_client()
            
            if not alarm_name:
                alarm_name = f"{function_name}-high-concurrency-utilization"
            
            cloudwatch.put_metric_alarm(
                AlarmName=alarm_name,
                ComparisonOperator='GreaterThanThreshold',
                EvaluationPeriods=2,
                MetricName='ConcurrencyUtilization',
                Namespace='AgentSCL/Lambda',
                Period=300,  # 5 minutes
                Statistic='Average',
                Threshold=threshold_percent,
                ActionsEnabled=True,
                AlarmDescription=f'High concurrency utilization for {function_name}',
                Dimensions=[
                    {
                        'Name': 'FunctionName',
                        'Value': function_name
                    }
                ],
                Unit='Percent'
            )
            
            lambda_logger.info(
                "Concurrency alarm created",
                extra={
                    "alarm_name": alarm_name,
                    "function_name": function_name,
                    "threshold_percent": threshold_percent
                }
            )
            
            return alarm_name
            
        except Exception as e:
            lambda_logger.error(
                "Failed to create concurrency alarm",
                extra={"error": str(e), "function_name": function_name}
            )
            raise


# Global instance
_concurrency_monitor = None

def get_concurrency_monitor() -> ConcurrencyMonitor:
    """Get concurrency monitor instance."""
    global _concurrency_monitor
    if _concurrency_monitor is None:
        _concurrency_monitor = ConcurrencyMonitor()
    return _concurrency_monitor

def record_concurrency_metrics(function_name: str, concurrent_executions: int, reserved_concurrency: Optional[int] = None):
    """Record concurrency metrics - convenience function."""
    monitor = get_concurrency_monitor()
    monitor.record_concurrency_metrics(function_name, concurrent_executions, reserved_concurrency)

def check_function_throttling(function_name: str, time_window_minutes: int = 5) -> Dict[str, Any]:
    """Check function throttling - convenience function."""
    monitor = get_concurrency_monitor()
    return monitor.check_throttling(function_name, time_window_minutes)
