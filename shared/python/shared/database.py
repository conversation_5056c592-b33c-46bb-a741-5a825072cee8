# shared/python/shared/database.py
# Database utilities and connection management for DynamoDB - Shared Layer

"""
Database utilities and connection management for DynamoDB.
Implements single-table design with multi-tenant isolation.
"""

import boto3
import time
from typing import Any, Dict, List, Optional, Union
from botocore.exceptions import ClientError
from botocore.config import Config
import threading
from boto3.dynamodb.conditions import Key, Attr

from .config import get_settings, get_database_config
from .exceptions import DatabaseException, ResourceNotFoundException
from .logger import lambda_logger, log_database_operation
from .resilience import resilient


class ConnectionPool:
    """Connection pool for DynamoDB clients."""
    
    def __init__(self, max_connections: int = 10):
        """Initialize connection pool."""
        self.max_connections = max_connections
        self._connections = []
        self._lock = threading.Lock()
        
        # Create optimized boto3 config
        self.boto_config = Config(
            retries={
                'max_attempts': 3,
                'mode': 'adaptive'
            },
            max_pool_connections=max_connections,
            tcp_keepalive=True
        )
    
    def get_connection(self, region: str, endpoint_url: Optional[str] = None):
        """Get a connection from the pool."""
        with self._lock:
            if self._connections:
                return self._connections.pop()
            
            # Create new connection if pool is empty
            if endpoint_url:
                return boto3.resource(
                    'dynamodb',
                    region_name=region,
                    endpoint_url=endpoint_url,
                    config=self.boto_config
                )
            else:
                return boto3.resource(
                    'dynamodb',
                    region_name=region,
                    config=self.boto_config
                )
    
    def return_connection(self, connection):
        """Return a connection to the pool."""
        with self._lock:
            if len(self._connections) < self.max_connections:
                self._connections.append(connection)


# Global connection pool
_connection_pool = ConnectionPool(max_connections=20)


class DynamoDBClient:
    """DynamoDB client with multi-tenant support."""
    
    def __init__(self):
        self.settings = get_settings()
        self.config = get_database_config()
        
        # Use connection pool for DynamoDB resource
        self.dynamodb = _connection_pool.get_connection(
            region=self.config["region"],
            endpoint_url=self.config.get("endpoint_url")
        )
        
        self.table = self.dynamodb.Table(self.config["table_name"])
        
        # Cache frequently accessed data (lazy loading to avoid circular import)
        self._cache_manager = None
        self._cache_config = None
    
    def _add_tenant_context(self, pk: str, tenant_id: str) -> str:
        """Add tenant context to partition key."""
        if not tenant_id:
            raise DatabaseException("Tenant ID is required for all operations")
        return f"TENANT#{tenant_id}#{pk}"
    
    def _remove_tenant_context(self, pk: str) -> str:
        """Remove tenant context from partition key."""
        if pk.startswith("TENANT#"):
            parts = pk.split("#", 2)
            return parts[2] if len(parts) > 2 else pk
        return pk

    @property
    def cache_manager(self):
        """Lazy load cache manager to avoid circular import."""
        if self._cache_manager is None:
            from .cache import cache_manager
            self._cache_manager = cache_manager
        return self._cache_manager

    @property
    def cache_config(self):
        """Lazy load cache config to avoid circular import."""
        if self._cache_config is None:
            from .cache import CacheConfig, CacheBackend
            self._cache_config = CacheConfig(
                ttl_seconds=300,  # 5 minutes
                backend=CacheBackend.MEMORY,
                key_prefix="db"
            )
        return self._cache_config

    @resilient("database", timeout_seconds=10.0, max_attempts=3, max_concurrent=20)
    def get_item(
        self,
        pk: str,
        sk: str,
        tenant_id: str,
        consistent_read: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Get a single item from the table with caching."""
        start_time = time.time()
        
        # Generate cache key
        cache_key = f"{tenant_id}:{pk}:{sk}"
        
        # Try cache first (only for consistent reads = False)
        if not consistent_read:
            cached_item = self.cache_manager.get(cache_key, self.cache_config)
            if cached_item is not None:
                lambda_logger.debug("Cache hit for get_item", extra={
                    'pk': pk,
                    'sk': sk,
                    'tenant_id': tenant_id
                })
                return cached_item
        
        try:
            full_pk = self._add_tenant_context(pk, tenant_id)
            
            response = self.table.get_item(
                Key={
                    'PK': full_pk,
                    'SK': sk
                },
                ConsistentRead=consistent_read
            )
            
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "get_item",
                self.config["table_name"],
                tenant_id,
                duration_ms,
                True
            )
            
            item = response.get('Item')
            if item:
                # Remove tenant context from PK for client
                item['PK'] = self._remove_tenant_context(item['PK'])
                
                # Cache the item (only for non-consistent reads)
                if not consistent_read:
                    self.cache_manager.set(cache_key, item, self.cache_config)
            
            return item
            
        except ClientError as e:
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "get_item",
                self.config["table_name"],
                tenant_id,
                duration_ms,
                False
            )
            
            raise DatabaseException(
                message=f"Failed to get item: {str(e)}",
                operation="get_item",
                table_name=self.config["table_name"]
            )
    
    @resilient("database", timeout_seconds=10.0, max_attempts=3, max_concurrent=20)
    def put_item(
        self,
        item: Dict[str, Any],
        tenant_id: str,
        condition_expression: Optional[str] = None
    ) -> Dict[str, Any]:
        """Put an item into the table."""
        start_time = time.time()
        
        try:
            # Add tenant context to PK
            if 'PK' in item:
                item['PK'] = self._add_tenant_context(item['PK'], tenant_id)
            
            # Add tenant_id to item for additional security
            item['tenant_id'] = tenant_id
            
            # Add timestamps
            current_time = int(time.time())
            if 'created_at' not in item:
                item['created_at'] = current_time
            item['updated_at'] = current_time
            
            put_kwargs = {'Item': item}
            if condition_expression:
                put_kwargs['ConditionExpression'] = condition_expression
            
            self.table.put_item(**put_kwargs)
            
            # Invalidate cache for this item
            if 'PK' in item and 'SK' in item:
                cache_key = f"{tenant_id}:{self._remove_tenant_context(item['PK'])}:{item['SK']}"
                self.cache_manager.delete(cache_key, self.cache_config)
            
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "put_item",
                self.config["table_name"],
                tenant_id,
                duration_ms,
                True
            )
            
            # Remove tenant context from PK for response
            if 'PK' in item:
                item['PK'] = self._remove_tenant_context(item['PK'])
            
            return item
            
        except ClientError as e:
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "put_item",
                self.config["table_name"],
                tenant_id,
                duration_ms,
                False
            )
            
            raise DatabaseException(
                message=f"Failed to put item: {str(e)}",
                operation="put_item",
                table_name=self.config["table_name"]
            )
    
    def query(
        self,
        key_condition_expression: str,
        tenant_id: str,
        filter_expression: Optional[str] = None,
        expression_attribute_values: Optional[Dict[str, Any]] = None,
        expression_attribute_names: Optional[Dict[str, str]] = None,
        index_name: Optional[str] = None,
        limit: Optional[int] = None,
        scan_index_forward: bool = True,
        consistent_read: bool = False
    ) -> Dict[str, Any]:
        """Query items from the table."""
        start_time = time.time()
        
        try:
            query_kwargs = {
                'KeyConditionExpression': key_condition_expression,
                'ScanIndexForward': scan_index_forward,
                'ConsistentRead': consistent_read
            }
            
            if filter_expression:
                query_kwargs['FilterExpression'] = filter_expression
            
            if expression_attribute_values:
                query_kwargs['ExpressionAttributeValues'] = expression_attribute_values
            
            if expression_attribute_names:
                query_kwargs['ExpressionAttributeNames'] = expression_attribute_names
            
            if index_name:
                query_kwargs['IndexName'] = index_name
            
            if limit:
                query_kwargs['Limit'] = limit
            
            response = self.table.query(**query_kwargs)
            
            # Remove tenant context from PKs in response
            items = response.get('Items', [])
            for item in items:
                if 'PK' in item:
                    item['PK'] = self._remove_tenant_context(item['PK'])
            
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "query",
                self.config["table_name"],
                tenant_id,
                duration_ms,
                True
            )
            
            return response
            
        except ClientError as e:
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "query",
                self.config["table_name"],
                tenant_id,
                duration_ms,
                False
            )
            
            raise DatabaseException(
                message=f"Failed to query items: {str(e)}",
                operation="query",
                table_name=self.config["table_name"]
            )

    def query_gsi(
        self,
        gsi_name: str,
        pk: str,
        sk: Optional[str] = None,
        tenant_id: Optional[str] = None,
        filter_expression: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Query items using Global Secondary Index."""
        start_time = time.time()

        try:
            # Build key condition expression
            if sk:
                key_condition = f"GSI{gsi_name[-1]}PK = :pk AND GSI{gsi_name[-1]}SK = :sk"
                expression_values = {':pk': pk, ':sk': sk}
            else:
                key_condition = f"GSI{gsi_name[-1]}PK = :pk"
                expression_values = {':pk': pk}

            query_kwargs = {
                'IndexName': gsi_name,
                'KeyConditionExpression': key_condition,
                'ExpressionAttributeValues': expression_values
            }

            if filter_expression:
                query_kwargs['FilterExpression'] = filter_expression
            if limit:
                query_kwargs['Limit'] = limit

            response = self.table.query(**query_kwargs)
            items = response.get('Items', [])

            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "query_gsi",
                self.config["table_name"],
                tenant_id or "global",
                duration_ms,
                True
            )

            return items

        except ClientError as e:
            duration_ms = (time.time() - start_time) * 1000
            log_database_operation(
                lambda_logger,
                "query_gsi",
                self.config["table_name"],
                tenant_id or "global",
                duration_ms,
                False
            )

            raise DatabaseException(
                message=f"Failed to query GSI items: {str(e)}",
                operation="query_gsi",
                table_name=self.config["table_name"]
            )


# Global database client instance
db_client = DynamoDBClient()


def get_database_client():
    """Get the global database client instance."""
    return boto3.resource('dynamodb')

