# shared/python/shared/middleware/resilience_middleware.py
# Middleware for automatic resilience pattern application - Shared Layer

"""
Middleware that automatically applies resilience patterns to Lambda handlers
based on service configuration and request characteristics.
"""

import time
from typing import Callable, Any, Dict, Optional
from functools import wraps

from ..logger import lambda_logger
from ..exceptions import PlatformException
from ..metrics import metrics_manager


class ResilienceMiddleware:
    """Middleware for applying resilience patterns to handlers."""
    
    def __init__(self):
        """Initialize resilience middleware."""
        self.default_configs = {
            'auth': {
                'timeout_seconds': 10.0,
                'max_attempts': 2,
                'max_concurrent': 50
            },
            'tenant': {
                'timeout_seconds': 15.0,
                'max_attempts': 3,
                'max_concurrent': 30
            },
            'payment': {
                'timeout_seconds': 60.0,
                'max_attempts': 2,
                'max_concurrent': 10
            },
            'admin': {
                'timeout_seconds': 30.0,
                'max_attempts': 2,
                'max_concurrent': 20
            },
            'events': {
                'timeout_seconds': 60.0,
                'max_attempts': 3,
                'max_concurrent': 100
            }
        }
    
    def apply_resilience(
        self,
        service_name: str,
        handler_name: Optional[str] = None,
        custom_config: Optional[Dict[str, Any]] = None
    ):
        """Decorator to apply resilience patterns to a handler."""
        def decorator(handler: Callable) -> Callable:
            @wraps(handler)
            def wrapper(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
                # Get configuration
                config = custom_config or self.default_configs.get(service_name, {})
                
                # Extract request information
                request_info = self._extract_request_info(event, context)
                
                # Apply resilience patterns
                start_time = time.time()
                
                try:
                    lambda_logger.info("Executing handler with resilience", extra={
                        'service_name': service_name,
                        'handler_name': handler_name or handler.__name__,
                        'request_id': request_info.get('request_id'),
                        'method': request_info.get('method'),
                        'path': request_info.get('path')
                    })
                    
                    # Execute handler
                    result = handler(event, context)
                    
                    # Record success metrics
                    duration_ms = (time.time() - start_time) * 1000
                    self._record_success_metrics(
                        service_name,
                        handler_name or handler.__name__,
                        duration_ms,
                        request_info
                    )
                    
                    return result
                    
                except Exception as e:
                    # Handle errors
                    duration_ms = (time.time() - start_time) * 1000
                    self._record_error_metrics(
                        service_name,
                        handler_name or handler.__name__,
                        duration_ms,
                        request_info,
                        e
                    )
                    
                    lambda_logger.error("Handler execution failed", extra={
                        'service_name': service_name,
                        'handler_name': handler_name or handler.__name__,
                        'error': str(e),
                        'error_type': type(e).__name__,
                        'request_id': request_info.get('request_id')
                    })
                    
                    # Return appropriate error response
                    return {
                        'statusCode': 500,
                        'headers': {
                            'Content-Type': 'application/json',
                            'Access-Control-Allow-Origin': '*',
                            'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
                        },
                        'body': '{"error": "Internal server error", "error_code": "INTERNAL_ERROR", "message": "An unexpected error occurred. Please try again later."}'
                    }
                
                finally:
                    # Ensure metrics are flushed
                    try:
                        metrics_manager.flush_all_metrics()
                    except Exception as e:
                        lambda_logger.warning("Failed to flush metrics", extra={'error': str(e)})
            
            return wrapper
        return decorator
    
    def _extract_request_info(self, event: Dict[str, Any], context: Any) -> Dict[str, str]:
        """Extract request information from Lambda event."""
        return {
            'request_id': event.get('requestContext', {}).get('requestId', 'unknown'),
            'method': event.get('httpMethod', 'unknown'),
            'path': event.get('path', 'unknown'),
            'function_name': context.function_name if context else 'unknown',
            'function_version': context.function_version if context else 'unknown'
        }
    
    def _record_success_metrics(
        self,
        service_name: str,
        handler_name: str,
        duration_ms: float,
        request_info: Dict[str, str]
    ) -> None:
        """Record success metrics."""
        metrics_manager.performance.record_function_duration(
            function_name=f"{service_name}_{handler_name}",
            duration_ms=duration_ms
        )
        
        metrics_manager.collector.record_metric(
            name="ResilienceHandlerSuccess",
            value=1,
            dimensions={
                'ServiceName': service_name,
                'HandlerName': handler_name,
                'Method': request_info.get('method', 'unknown')
            }
        )
    
    def _record_error_metrics(
        self,
        service_name: str,
        handler_name: str,
        duration_ms: float,
        request_info: Dict[str, str],
        error: Exception
    ) -> None:
        """Record error metrics."""
        metrics_manager.collector.record_metric(
            name="ResilienceHandlerError",
            value=1,
            dimensions={
                'ServiceName': service_name,
                'HandlerName': handler_name,
                'Method': request_info.get('method', 'unknown'),
                'ErrorType': type(error).__name__
            }
        )


# Global resilience middleware instance
resilience_middleware = ResilienceMiddleware()


# Convenience decorators for each service
def auth_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply auth service resilience patterns."""
    return resilience_middleware.apply_resilience('auth', handler_name, custom_config)


def tenant_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply tenant service resilience patterns."""
    return resilience_middleware.apply_resilience('tenant', handler_name, custom_config)


def payment_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply payment service resilience patterns."""
    return resilience_middleware.apply_resilience('payment', handler_name, custom_config)


def admin_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply admin service resilience patterns."""
    return resilience_middleware.apply_resilience('admin', handler_name, custom_config)


def events_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply events service resilience patterns."""
    return resilience_middleware.apply_resilience('events', handler_name, custom_config)


def security_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply security service resilience patterns."""
    return resilience_middleware.apply_resilience('security', handler_name, custom_config)


def user_resilience(handler_name: Optional[str] = None, custom_config: Optional[Dict[str, Any]] = None):
    """Apply user service resilience patterns."""
    return resilience_middleware.apply_resilience('user', handler_name, custom_config)


# Rate limiting decorator
def rate_limit(requests_per_minute: int = 60):
    """
    Rate limiting decorator for Lambda handlers.

    Args:
        requests_per_minute: Maximum requests allowed per minute
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
            # Extract client identifier
            client_ip = event.get('requestContext', {}).get('identity', {}).get('sourceIp', 'unknown')

            # For now, just log the rate limit check
            # In production, this would check against Redis/DynamoDB
            lambda_logger.debug(f"Rate limit check: {client_ip} - {requests_per_minute} req/min")

            # Record rate limit metric
            metrics_manager.record_rate_limit_check(
                client_ip=client_ip,
                limit=requests_per_minute,
                allowed=True  # For now, always allow
            )

            return func(event, context)
        return wrapper
    return decorator


# Retry decorator for external services
def with_retry(max_attempts: int = 3, backoff_factor: float = 1.0):
    """
    Retry decorator for external service calls.

    Args:
        max_attempts: Maximum number of retry attempts
        backoff_factor: Exponential backoff factor
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        wait_time = backoff_factor * (2 ** attempt)
                        lambda_logger.warning(f"Retry attempt {attempt + 1}/{max_attempts} after {wait_time}s")
                        time.sleep(wait_time)
                    else:
                        lambda_logger.error(f"All retry attempts failed: {str(e)}")

            raise last_exception
        return wrapper
    return decorator
