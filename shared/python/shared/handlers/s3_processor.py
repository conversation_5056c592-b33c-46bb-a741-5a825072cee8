# src/shared/handlers/s3_processor.py
# Handler para procesar archivos subidos a S3
# Parte de la infraestructura migrada desde Terraform

"""
S3 file processor handler.
Processes files uploaded to S3 buckets and triggers appropriate workflows.
"""

import json
import boto3
import os
from typing import Any, Dict, List
from urllib.parse import unquote_plus

from shared.logger import lambda_logger
from shared.exceptions import PlatformException


def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process S3 events when files are uploaded.
    
    This function is triggered when files are uploaded to the platform S3 bucket.
    It processes different types of files and triggers appropriate workflows.
    """
    
    request_id = context.aws_request_id if context else 'unknown'
    
    lambda_logger.info("S3 processor started", extra={
        'request_id': request_id,
        'event_records': len(event.get('Records', []))
    })
    
    try:
        # Process each S3 record
        processed_files = []
        failed_files = []
        
        for record in event.get('Records', []):
            try:
                # Extract S3 information
                s3_info = record.get('s3', {})
                bucket_name = s3_info.get('bucket', {}).get('name')
                object_key = unquote_plus(s3_info.get('object', {}).get('key', ''))
                object_size = s3_info.get('object', {}).get('size', 0)
                event_name = record.get('eventName', '')
                
                lambda_logger.info("Processing S3 object", extra={
                    'bucket': bucket_name,
                    'key': object_key,
                    'size': object_size,
                    'event': event_name,
                    'request_id': request_id
                })
                
                # Process the file based on its type and location
                result = process_s3_object(
                    bucket_name=bucket_name,
                    object_key=object_key,
                    object_size=object_size,
                    event_name=event_name,
                    request_id=request_id
                )
                
                processed_files.append({
                    'bucket': bucket_name,
                    'key': object_key,
                    'status': 'processed',
                    'result': result
                })
                
            except Exception as e:
                lambda_logger.error("Failed to process S3 record", extra={
                    'error': str(e),
                    'record': record,
                    'request_id': request_id
                })
                
                failed_files.append({
                    'bucket': bucket_name if 'bucket_name' in locals() else 'unknown',
                    'key': object_key if 'object_key' in locals() else 'unknown',
                    'status': 'failed',
                    'error': str(e)
                })
        
        # Log summary
        lambda_logger.info("S3 processing completed", extra={
            'processed_count': len(processed_files),
            'failed_count': len(failed_files),
            'request_id': request_id
        })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'processed_files': processed_files,
                'failed_files': failed_files,
                'summary': {
                    'total_records': len(event.get('Records', [])),
                    'processed': len(processed_files),
                    'failed': len(failed_files)
                }
            })
        }
        
    except Exception as e:
        lambda_logger.error("S3 processor failed", extra={
            'error': str(e),
            'request_id': request_id
        })
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e),
                'request_id': request_id
            })
        }


def process_s3_object(bucket_name: str, object_key: str, object_size: int, 
                     event_name: str, request_id: str) -> Dict[str, Any]:
    """
    Process a specific S3 object based on its type and location.
    
    Args:
        bucket_name: Name of the S3 bucket
        object_key: Key of the S3 object
        object_size: Size of the object in bytes
        event_name: S3 event name (e.g., 's3:ObjectCreated:Put')
        request_id: Request ID for tracking
    
    Returns:
        Dict containing processing results
    """
    
    # Determine file type and processing strategy
    file_type = determine_file_type(object_key)
    processing_strategy = get_processing_strategy(object_key, file_type)
    
    lambda_logger.info("Determined processing strategy", extra={
        'file_type': file_type,
        'strategy': processing_strategy,
        'object_key': object_key,
        'request_id': request_id
    })
    
    # Process based on strategy
    if processing_strategy == 'user_upload':
        return process_user_upload(bucket_name, object_key, object_size, request_id)
    elif processing_strategy == 'system_backup':
        return process_system_backup(bucket_name, object_key, object_size, request_id)
    elif processing_strategy == 'log_file':
        return process_log_file(bucket_name, object_key, object_size, request_id)
    elif processing_strategy == 'document':
        return process_document(bucket_name, object_key, object_size, request_id)
    else:
        return process_generic_file(bucket_name, object_key, object_size, request_id)


def determine_file_type(object_key: str) -> str:
    """Determine file type based on object key and extension."""
    
    key_lower = object_key.lower()
    
    # Image files
    if any(key_lower.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
        return 'image'
    
    # Document files
    elif any(key_lower.endswith(ext) for ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx']):
        return 'document'
    
    # Archive files
    elif any(key_lower.endswith(ext) for ext in ['.zip', '.tar', '.gz', '.rar']):
        return 'archive'
    
    # Log files
    elif any(key_lower.endswith(ext) for ext in ['.log', '.txt']) or 'logs/' in key_lower:
        return 'log'
    
    # Backup files
    elif 'backup' in key_lower or key_lower.endswith('.bak'):
        return 'backup'
    
    # Default
    else:
        return 'unknown'


def get_processing_strategy(object_key: str, file_type: str) -> str:
    """Determine processing strategy based on object location and type."""
    
    key_lower = object_key.lower()
    
    # User uploads
    if key_lower.startswith('uploads/'):
        return 'user_upload'
    
    # System backups
    elif key_lower.startswith('backups/'):
        return 'system_backup'
    
    # Log files
    elif key_lower.startswith('logs/') or file_type == 'log':
        return 'log_file'
    
    # Documents
    elif file_type == 'document':
        return 'document'
    
    # Default
    else:
        return 'generic'


def process_user_upload(bucket_name: str, object_key: str, object_size: int, request_id: str) -> Dict[str, Any]:
    """Process user uploaded files."""
    
    lambda_logger.info("Processing user upload", extra={
        'bucket': bucket_name,
        'key': object_key,
        'size': object_size,
        'request_id': request_id
    })
    
    # TODO: Implement user upload processing
    # - Virus scanning
    # - File validation
    # - Metadata extraction
    # - Thumbnail generation for images
    # - User notification
    
    return {
        'type': 'user_upload',
        'status': 'processed',
        'actions': ['validated', 'metadata_extracted'],
        'size': object_size
    }


def process_system_backup(bucket_name: str, object_key: str, object_size: int, request_id: str) -> Dict[str, Any]:
    """Process system backup files."""
    
    lambda_logger.info("Processing system backup", extra={
        'bucket': bucket_name,
        'key': object_key,
        'size': object_size,
        'request_id': request_id
    })
    
    # TODO: Implement backup processing
    # - Backup validation
    # - Integrity checks
    # - Backup catalog update
    # - Retention policy enforcement
    
    return {
        'type': 'system_backup',
        'status': 'processed',
        'actions': ['validated', 'cataloged'],
        'size': object_size
    }


def process_log_file(bucket_name: str, object_key: str, object_size: int, request_id: str) -> Dict[str, Any]:
    """Process log files."""
    
    lambda_logger.info("Processing log file", extra={
        'bucket': bucket_name,
        'key': object_key,
        'size': object_size,
        'request_id': request_id
    })
    
    # TODO: Implement log processing
    # - Log parsing
    # - Error detection
    # - Metrics extraction
    # - Alert generation
    
    return {
        'type': 'log_file',
        'status': 'processed',
        'actions': ['parsed', 'analyzed'],
        'size': object_size
    }


def process_document(bucket_name: str, object_key: str, object_size: int, request_id: str) -> Dict[str, Any]:
    """Process document files."""
    
    lambda_logger.info("Processing document", extra={
        'bucket': bucket_name,
        'key': object_key,
        'size': object_size,
        'request_id': request_id
    })
    
    # TODO: Implement document processing
    # - Text extraction
    # - Document indexing
    # - Preview generation
    # - Content analysis
    
    return {
        'type': 'document',
        'status': 'processed',
        'actions': ['indexed', 'preview_generated'],
        'size': object_size
    }


def process_generic_file(bucket_name: str, object_key: str, object_size: int, request_id: str) -> Dict[str, Any]:
    """Process generic files."""
    
    lambda_logger.info("Processing generic file", extra={
        'bucket': bucket_name,
        'key': object_key,
        'size': object_size,
        'request_id': request_id
    })
    
    # Basic processing for unknown file types
    return {
        'type': 'generic',
        'status': 'processed',
        'actions': ['logged'],
        'size': object_size
    }

