# shared/python/shared/secrets_manager.py
# Enhanced Secrets Manager with rotation and caching - Shared Layer

"""
Enhanced AWS Secrets Manager client with automatic rotation,
caching, and comprehensive error handling.
"""

import json
import time
import boto3
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from botocore.exceptions import Client<PERSON>rror

from .logger import lambda_logger
from .config import get_settings
from .exceptions import PlatformException


class EnhancedSecretsManager:
    """Enhanced Secrets Manager with rotation and caching capabilities."""
    
    def __init__(self):
        """Initialize the enhanced secrets manager."""
        self.settings = get_settings()
        self.client = boto3.client('secretsmanager', region_name=self.settings.region)
        self._cache = {}
        self._cache_ttl = 300  # 5 minutes cache TTL
        self.project_name = self.settings.project_name
        self.environment = self.settings.environment
        
    def get_secret(self, secret_name: str, force_refresh: bool = False) -> str:
        """
        Get secret value with caching and automatic refresh.
        
        Args:
            secret_name: Name of the secret
            force_refresh: Force refresh from AWS
            
        Returns:
            Secret value as string
            
        Raises:
            PlatformException: If secret cannot be retrieved
        """
        cache_key = f"{secret_name}:{self.environment}"
        current_time = time.time()
        
        # Check cache first (unless force refresh)
        if not force_refresh and cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if current_time - cached_data['timestamp'] < self._cache_ttl:
                lambda_logger.debug("Secret retrieved from cache", extra={
                    'secret_name': secret_name,
                    'cache_age_seconds': current_time - cached_data['timestamp']
                })
                return cached_data['value']
        
        # Retrieve from AWS Secrets Manager
        try:
            lambda_logger.debug("Retrieving secret from AWS", extra={
                'secret_name': secret_name,
                'force_refresh': force_refresh
            })
            
            response = self.client.get_secret_value(SecretId=secret_name)
            
            # Extract secret value
            if 'SecretString' in response:
                secret_value = response['SecretString']
            else:
                # Handle binary secrets
                secret_value = response['SecretBinary'].decode('utf-8')
            
            # Cache the secret
            self._cache[cache_key] = {
                'value': secret_value,
                'timestamp': current_time,
                'version_id': response.get('VersionId'),
                'version_stage': response.get('VersionStage', 'AWSCURRENT')
            }
            
            lambda_logger.info("Secret retrieved successfully", extra={
                'secret_name': secret_name,
                'version_id': response.get('VersionId'),
                'version_stage': response.get('VersionStage', 'AWSCURRENT')
            })
            
            return secret_value
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            
            if error_code == 'ResourceNotFoundException':
                lambda_logger.error("Secret not found", extra={
                    'secret_name': secret_name,
                    'error_code': error_code
                })
                raise PlatformException(f"Secret '{secret_name}' not found")
            
            elif error_code == 'InvalidRequestException':
                lambda_logger.error("Invalid secret request", extra={
                    'secret_name': secret_name,
                    'error_code': error_code
                })
                raise PlatformException(f"Invalid request for secret '{secret_name}'")
            
            elif error_code == 'InvalidParameterException':
                lambda_logger.error("Invalid secret parameter", extra={
                    'secret_name': secret_name,
                    'error_code': error_code
                })
                raise PlatformException(f"Invalid parameter for secret '{secret_name}'")
            
            else:
                lambda_logger.error("Failed to retrieve secret", extra={
                    'secret_name': secret_name,
                    'error_code': error_code,
                    'error_message': str(e)
                })
                raise PlatformException(f"Failed to retrieve secret '{secret_name}': {str(e)}")
        
        except Exception as e:
            lambda_logger.error("Unexpected error retrieving secret", extra={
                'secret_name': secret_name,
                'error': str(e)
            })
            raise PlatformException(f"Unexpected error retrieving secret '{secret_name}': {str(e)}")
    
    def get_secret_json(self, secret_name: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get secret value as JSON dictionary.
        
        Args:
            secret_name: Name of the secret
            force_refresh: Force refresh from AWS
            
        Returns:
            Secret value as dictionary
        """
        secret_string = self.get_secret(secret_name, force_refresh)
        
        try:
            return json.loads(secret_string)
        except json.JSONDecodeError as e:
            lambda_logger.error("Failed to parse secret as JSON", extra={
                'secret_name': secret_name,
                'error': str(e)
            })
            raise PlatformException(f"Secret '{secret_name}' is not valid JSON")
    
    def create_secret(
        self,
        secret_name: str,
        secret_value: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None
    ) -> str:
        """
        Create a new secret.
        
        Args:
            secret_name: Name of the secret
            secret_value: Value of the secret
            description: Optional description
            tags: Optional tags
            
        Returns:
            ARN of the created secret
        """
        try:
            # Prepare tags
            secret_tags = [
                {'Key': 'Project', 'Value': self.project_name},
                {'Key': 'Environment', 'Value': self.environment},
                {'Key': 'ManagedBy', 'Value': 'agent-scl'}
            ]
            
            if tags:
                for key, value in tags.items():
                    secret_tags.append({'Key': key, 'Value': value})
            
            # Create secret
            response = self.client.create_secret(
                Name=secret_name,
                SecretString=secret_value,
                Description=description or f"Secret for {self.project_name} {self.environment}",
                Tags=secret_tags
            )
            
            lambda_logger.info("Secret created successfully", extra={
                'secret_name': secret_name,
                'secret_arn': response['ARN']
            })
            
            return response['ARN']
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            
            if error_code == 'ResourceExistsException':
                lambda_logger.warning("Secret already exists", extra={
                    'secret_name': secret_name
                })
                raise PlatformException(f"Secret '{secret_name}' already exists")
            
            else:
                lambda_logger.error("Failed to create secret", extra={
                    'secret_name': secret_name,
                    'error_code': error_code,
                    'error_message': str(e)
                })
                raise PlatformException(f"Failed to create secret '{secret_name}': {str(e)}")
    
    def update_secret(self, secret_name: str, secret_value: str) -> str:
        """
        Update an existing secret.
        
        Args:
            secret_name: Name of the secret
            secret_value: New value of the secret
            
        Returns:
            Version ID of the updated secret
        """
        try:
            response = self.client.update_secret(
                SecretId=secret_name,
                SecretString=secret_value
            )
            
            # Invalidate cache
            cache_key = f"{secret_name}:{self.environment}"
            if cache_key in self._cache:
                del self._cache[cache_key]
            
            lambda_logger.info("Secret updated successfully", extra={
                'secret_name': secret_name,
                'version_id': response['VersionId']
            })
            
            return response['VersionId']
            
        except ClientError as e:
            lambda_logger.error("Failed to update secret", extra={
                'secret_name': secret_name,
                'error': str(e)
            })
            raise PlatformException(f"Failed to update secret '{secret_name}': {str(e)}")
    
    def delete_secret(self, secret_name: str, force_delete: bool = False) -> None:
        """
        Delete a secret.
        
        Args:
            secret_name: Name of the secret
            force_delete: Force immediate deletion (cannot be recovered)
        """
        try:
            if force_delete:
                self.client.delete_secret(
                    SecretId=secret_name,
                    ForceDeleteWithoutRecovery=True
                )
            else:
                self.client.delete_secret(SecretId=secret_name)
            
            # Remove from cache
            cache_key = f"{secret_name}:{self.environment}"
            if cache_key in self._cache:
                del self._cache[cache_key]
            
            lambda_logger.info("Secret deleted successfully", extra={
                'secret_name': secret_name,
                'force_delete': force_delete
            })
            
        except ClientError as e:
            lambda_logger.error("Failed to delete secret", extra={
                'secret_name': secret_name,
                'error': str(e)
            })
            raise PlatformException(f"Failed to delete secret '{secret_name}': {str(e)}")
    
    def clear_cache(self) -> None:
        """Clear the secrets cache."""
        self._cache.clear()
        lambda_logger.info("Secrets cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        current_time = time.time()
        
        stats = {
            'total_cached_secrets': len(self._cache),
            'cache_ttl_seconds': self._cache_ttl,
            'cached_secrets': []
        }
        
        for cache_key, cached_data in self._cache.items():
            age_seconds = current_time - cached_data['timestamp']
            stats['cached_secrets'].append({
                'secret_name': cache_key,
                'age_seconds': age_seconds,
                'expires_in_seconds': max(0, self._cache_ttl - age_seconds),
                'version_id': cached_data.get('version_id'),
                'version_stage': cached_data.get('version_stage')
            })
        
        return stats


# Global secrets manager instance
secrets_client = EnhancedSecretsManager()
