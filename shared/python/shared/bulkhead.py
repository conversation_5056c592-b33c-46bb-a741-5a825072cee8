# shared/python/shared/bulkhead.py
# Bulkhead pattern implementation for resource isolation - Shared Layer

"""
Bulkhead pattern implementation that provides resource isolation
and prevents resource exhaustion from affecting other operations.
"""

import time
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from threading import Semaphore, Lock
from functools import wraps

from .logger import lambda_logger
from .exceptions import PlatformException


@dataclass
class BulkheadConfig:
    """Configuration for bulkhead."""
    max_concurrent_calls: int = 10
    max_wait_time: float = 30.0  # Maximum time to wait for resource
    timeout: Optional[float] = None  # Call timeout


class BulkheadStats:
    """Statistics for bulkhead."""
    
    def __init__(self):
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.rejected_calls = 0
        self.timeout_calls = 0
        self.current_active_calls = 0
        self.max_active_calls = 0
        self.total_wait_time = 0.0


class Bulkhead:
    """Bulkhead implementation for resource isolation."""
    
    def __init__(self, name: str, config: BulkheadConfig):
        """Initialize bulkhead."""
        self.name = name
        self.config = config
        self.stats = BulkheadStats()
        self.semaphore = Semaphore(config.max_concurrent_calls)
        self.lock = Lock()
    
    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with bulkhead protection."""
        start_time = time.time()
        
        with self.lock:
            self.stats.total_calls += 1
        
        # Try to acquire semaphore
        acquired = self.semaphore.acquire(timeout=self.config.max_wait_time)
        
        if not acquired:
            with self.lock:
                self.stats.rejected_calls += 1
            
            lambda_logger.warning("Bulkhead rejected call - resource exhausted", extra={
                'bulkhead_name': self.name,
                'max_concurrent': self.config.max_concurrent_calls,
                'wait_time': self.config.max_wait_time
            })
            
            raise PlatformException(f"Bulkhead '{self.name}' rejected call - resource exhausted")
        
        try:
            with self.lock:
                self.stats.current_active_calls += 1
                if self.stats.current_active_calls > self.stats.max_active_calls:
                    self.stats.max_active_calls = self.stats.current_active_calls
            
            wait_time = time.time() - start_time
            with self.lock:
                self.stats.total_wait_time += wait_time
            
            lambda_logger.debug("Bulkhead executing call", extra={
                'bulkhead_name': self.name,
                'active_calls': self.stats.current_active_calls,
                'wait_time_ms': wait_time * 1000
            })
            
            # Execute the function
            result = func(*args, **kwargs)
            
            with self.lock:
                self.stats.successful_calls += 1
            
            return result
            
        except Exception as e:
            with self.lock:
                self.stats.failed_calls += 1
            
            lambda_logger.error("Bulkhead call failed", extra={
                'bulkhead_name': self.name,
                'error': str(e)
            })
            raise
            
        finally:
            with self.lock:
                self.stats.current_active_calls -= 1
            self.semaphore.release()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get bulkhead statistics."""
        with self.lock:
            return {
                'name': self.name,
                'config': {
                    'max_concurrent_calls': self.config.max_concurrent_calls,
                    'max_wait_time': self.config.max_wait_time,
                    'timeout': self.config.timeout
                },
                'stats': {
                    'total_calls': self.stats.total_calls,
                    'successful_calls': self.stats.successful_calls,
                    'failed_calls': self.stats.failed_calls,
                    'rejected_calls': self.stats.rejected_calls,
                    'timeout_calls': self.stats.timeout_calls,
                    'current_active_calls': self.stats.current_active_calls,
                    'max_active_calls': self.stats.max_active_calls,
                    'avg_wait_time': (
                        self.stats.total_wait_time / self.stats.total_calls 
                        if self.stats.total_calls > 0 else 0
                    ),
                    'success_rate': (
                        self.stats.successful_calls / self.stats.total_calls 
                        if self.stats.total_calls > 0 else 0
                    )
                }
            }


class BulkheadManager:
    """Manages multiple bulkheads."""
    
    def __init__(self):
        """Initialize bulkhead manager."""
        self.bulkheads: Dict[str, Bulkhead] = {}
        self.default_configs = {
            'database': BulkheadConfig(max_concurrent_calls=20, max_wait_time=10.0),
            'external_api': BulkheadConfig(max_concurrent_calls=10, max_wait_time=30.0),
            'email': BulkheadConfig(max_concurrent_calls=5, max_wait_time=60.0),
            'file_processing': BulkheadConfig(max_concurrent_calls=3, max_wait_time=120.0)
        }
    
    def get_bulkhead(self, name: str, config: Optional[BulkheadConfig] = None) -> Bulkhead:
        """Get or create bulkhead."""
        if name not in self.bulkheads:
            if config is None:
                config = self.default_configs.get(name, BulkheadConfig())
            
            self.bulkheads[name] = Bulkhead(name, config)
            
            lambda_logger.info("Bulkhead created", extra={
                'bulkhead_name': name,
                'max_concurrent': config.max_concurrent_calls,
                'max_wait_time': config.max_wait_time
            })
        
        return self.bulkheads[name]
    
    def execute_with_bulkhead(
        self,
        bulkhead_name: str,
        func: Callable,
        *args,
        config: Optional[BulkheadConfig] = None,
        **kwargs
    ) -> Any:
        """Execute function with specified bulkhead."""
        bulkhead = self.get_bulkhead(bulkhead_name, config)
        return bulkhead.execute(func, *args, **kwargs)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all bulkheads."""
        return {
            name: bulkhead.get_stats()
            for name, bulkhead in self.bulkheads.items()
        }


# Global bulkhead manager
bulkhead_manager = BulkheadManager()


def bulkhead(
    name: str,
    max_concurrent_calls: int = 10,
    max_wait_time: float = 30.0,
    timeout: Optional[float] = None
):
    """Decorator for applying bulkhead pattern."""
    config = BulkheadConfig(
        max_concurrent_calls=max_concurrent_calls,
        max_wait_time=max_wait_time,
        timeout=timeout
    )
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return bulkhead_manager.execute_with_bulkhead(
                name, func, *args, config=config, **kwargs
            )
        return wrapper
    return decorator


# Convenience functions
def database_bulkhead(func: Callable, *args, **kwargs) -> Any:
    """Execute function with database bulkhead."""
    return bulkhead_manager.execute_with_bulkhead('database', func, *args, **kwargs)


def external_api_bulkhead(func: Callable, *args, **kwargs) -> Any:
    """Execute function with external API bulkhead."""
    return bulkhead_manager.execute_with_bulkhead('external_api', func, *args, **kwargs)


def email_bulkhead(func: Callable, *args, **kwargs) -> Any:
    """Execute function with email bulkhead."""
    return bulkhead_manager.execute_with_bulkhead('email', func, *args, **kwargs)
