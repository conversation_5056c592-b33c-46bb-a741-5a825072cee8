# shared/python/shared/query_optimizer.py
# DynamoDB query optimization and performance tuning - Shared Layer

"""
DynamoDB query optimizer that provides intelligent query planning,
batch operations, and performance optimizations for common access patterns.
"""

import time
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from .logger import lambda_logger
from .cache import cache_manager, CacheConfig, CacheBackend


class QueryType(Enum):
    """Types of DynamoDB queries."""
    GET_ITEM = "get_item"
    QUERY = "query"
    SCAN = "scan"
    BATCH_GET = "batch_get"
    BATCH_WRITE = "batch_write"


@dataclass
class QueryMetrics:
    """Metrics for query performance."""
    query_type: QueryType
    execution_time_ms: float
    consumed_capacity: Optional[float]
    item_count: int
    scanned_count: Optional[int]
    cache_hit: bool = False


class QueryOptimizer:
    """Optimizes DynamoDB queries for better performance."""
    
    def __init__(self):
        """Initialize query optimizer."""
        self.query_metrics: List[QueryMetrics] = []
        self.cache_config = CacheConfig(
            ttl_seconds=600,  # 10 minutes for query results
            backend=CacheBackend.MEMORY,
            key_prefix="query"
        )
    
    def optimize_get_item(
        self,
        pk: str,
        sk: str,
        tenant_id: str,
        projection_expression: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """Optimize get_item operation with caching and projection."""
        start_time = time.time()
        
        # Generate cache key
        cache_key = f"get:{tenant_id}:{pk}:{sk}"
        if projection_expression:
            cache_key += f":{projection_expression}"
        
        # Try cache first
        if use_cache:
            cached_result = cache_manager.get(cache_key, self.cache_config)
            if cached_result is not None:
                execution_time = (time.time() - start_time) * 1000
                self._record_metrics(QueryType.GET_ITEM, execution_time, 0, 1, cache_hit=True)
                return cached_result
        
        # Build optimized query parameters
        query_params = {
            'Key': {'PK': pk, 'SK': sk},
            'ReturnConsumedCapacity': 'TOTAL'
        }
        
        # Add projection expression to reduce data transfer
        if projection_expression:
            query_params['ProjectionExpression'] = projection_expression
        
        execution_time = (time.time() - start_time) * 1000
        
        return {
            'optimized_params': query_params,
            'cache_key': cache_key,
            'use_cache': use_cache,
            'execution_time_ms': execution_time
        }
    
    def optimize_query(
        self,
        pk: str,
        sk_condition: Optional[str] = None,
        tenant_id: str = "",
        limit: Optional[int] = None,
        projection_expression: Optional[str] = None,
        filter_expression: Optional[str] = None,
        index_name: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """Optimize query operation with intelligent parameter tuning."""
        start_time = time.time()
        
        # Generate cache key for query results
        cache_key_parts = [f"query:{tenant_id}:{pk}"]
        if sk_condition:
            cache_key_parts.append(sk_condition)
        if limit:
            cache_key_parts.append(f"limit:{limit}")
        if projection_expression:
            cache_key_parts.append(f"proj:{projection_expression}")
        if filter_expression:
            cache_key_parts.append(f"filter:{filter_expression}")
        if index_name:
            cache_key_parts.append(f"index:{index_name}")
        
        cache_key = ":".join(cache_key_parts)
        
        # Try cache first
        if use_cache:
            cached_result = cache_manager.get(cache_key, self.cache_config)
            if cached_result is not None:
                execution_time = (time.time() - start_time) * 1000
                self._record_metrics(
                    QueryType.QUERY, 
                    execution_time, 
                    0, 
                    len(cached_result.get('Items', [])),
                    cache_hit=True
                )
                return cached_result
        
        # Build optimized query parameters
        query_params = {
            'KeyConditionExpression': f'PK = :pk',
            'ExpressionAttributeValues': {':pk': pk},
            'ReturnConsumedCapacity': 'TOTAL'
        }
        
        # Add sort key condition
        if sk_condition:
            query_params['KeyConditionExpression'] += f' AND {sk_condition}'
        
        # Optimize limit based on expected result size
        if limit:
            # Add buffer for filter expressions that might reduce results
            if filter_expression:
                query_params['Limit'] = min(limit * 2, 1000)  # Cap at 1000
            else:
                query_params['Limit'] = limit
        else:
            # Default reasonable limit to prevent large scans
            query_params['Limit'] = 100
        
        # Add projection expression to reduce bandwidth
        if projection_expression:
            query_params['ProjectionExpression'] = projection_expression
        
        # Add filter expression
        if filter_expression:
            query_params['FilterExpression'] = filter_expression
        
        # Use index if specified
        if index_name:
            query_params['IndexName'] = index_name
        
        # Enable consistent read for primary table queries (not indexes)
        if not index_name:
            query_params['ConsistentRead'] = False  # Eventually consistent for better performance
        
        execution_time = (time.time() - start_time) * 1000
        
        return {
            'optimized_params': query_params,
            'cache_key': cache_key,
            'use_cache': use_cache,
            'execution_time_ms': execution_time
        }
    
    def _record_metrics(
        self,
        query_type: QueryType,
        execution_time_ms: float,
        consumed_capacity: Optional[float],
        item_count: int,
        scanned_count: Optional[int] = None,
        cache_hit: bool = False
    ) -> None:
        """Record query metrics for analysis."""
        metrics = QueryMetrics(
            query_type=query_type,
            execution_time_ms=execution_time_ms,
            consumed_capacity=consumed_capacity,
            item_count=item_count,
            scanned_count=scanned_count,
            cache_hit=cache_hit
        )
        
        self.query_metrics.append(metrics)
        
        # Keep only last 1000 metrics to prevent memory issues
        if len(self.query_metrics) > 1000:
            self.query_metrics = self.query_metrics[-1000:]
        
        lambda_logger.debug("Query metrics recorded", extra={
            'query_type': query_type.value,
            'execution_time_ms': execution_time_ms,
            'item_count': item_count,
            'cache_hit': cache_hit
        })
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all queries."""
        if not self.query_metrics:
            return {'total_queries': 0}
        
        total_queries = len(self.query_metrics)
        cache_hits = sum(1 for m in self.query_metrics if m.cache_hit)
        
        avg_execution_time = sum(m.execution_time_ms for m in self.query_metrics) / total_queries
        
        query_type_stats = {}
        for query_type in QueryType:
            type_metrics = [m for m in self.query_metrics if m.query_type == query_type]
            if type_metrics:
                query_type_stats[query_type.value] = {
                    'count': len(type_metrics),
                    'avg_execution_time_ms': sum(m.execution_time_ms for m in type_metrics) / len(type_metrics),
                    'avg_item_count': sum(m.item_count for m in type_metrics) / len(type_metrics)
                }
        
        return {
            'total_queries': total_queries,
            'cache_hit_rate': cache_hits / total_queries if total_queries > 0 else 0,
            'avg_execution_time_ms': avg_execution_time,
            'query_type_stats': query_type_stats
        }


# Global query optimizer instance
query_optimizer = QueryOptimizer()
