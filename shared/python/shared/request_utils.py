#!/usr/bin/env python3
# shared/python/shared/request_utils.py
# Shared request utilities

"""
Shared utilities for handling API Gateway requests.
Eliminates code duplication across handlers.
"""

import json
from typing import Dict, Any, List, Optional

from .exceptions import ValidationException
from .validators import validate_required_fields


def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse request body from API Gateway event.
    
    Args:
        event: API Gateway event
        
    Returns:
        Parsed request body as dictionary
        
    Raises:
        ValidationException: If JSON is invalid
    """
    body = event.get('body', '{}')
    
    if body is None:
        return {}
    
    try:
        if isinstance(body, str):
            return json.loads(body)
        else:
            return body
    except json.JSONDecodeError as e:
        raise ValidationException(f"Invalid JSON format: {str(e)}")


def parse_and_validate_request(event: Dict[str, Any], 
                             required_fields: List[str]) -> Dict[str, Any]:
    """
    Parse request body and validate required fields.
    
    Args:
        event: API Gateway event
        required_fields: List of required field names
        
    Returns:
        Validated request data
        
    Raises:
        ValidationException: If validation fails
    """
    data = parse_request_body(event)
    validate_required_fields(data, required_fields)
    return data


def extract_path_parameters(event: Dict[str, Any], 
                          required_params: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Extract and validate path parameters.
    
    Args:
        event: API Gateway event
        required_params: List of required parameter names
        
    Returns:
        Path parameters dictionary
        
    Raises:
        ValidationException: If required parameters are missing
    """
    path_params = event.get('pathParameters') or {}
    
    if required_params:
        for param in required_params:
            if not path_params.get(param):
                raise ValidationException(f"Missing required path parameter: {param}")
    
    return path_params


def extract_query_parameters(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract query parameters from event.
    
    Args:
        event: API Gateway event
        
    Returns:
        Query parameters dictionary
    """
    return event.get('queryStringParameters') or {}


def get_request_context(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract request context information.
    
    Args:
        event: API Gateway event
        
    Returns:
        Request context dictionary
    """
    request_context = event.get('requestContext', {})
    
    return {
        'request_id': request_context.get('requestId', 'unknown'),
        'stage': request_context.get('stage', 'unknown'),
        'http_method': event.get('httpMethod', 'GET'),
        'path': event.get('path', '/'),
        'source_ip': request_context.get('identity', {}).get('sourceIp', 'unknown'),
        'user_agent': event.get('headers', {}).get('User-Agent', 'unknown')
    }
