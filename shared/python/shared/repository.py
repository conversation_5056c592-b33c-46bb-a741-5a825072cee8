# shared/python/shared/repository.py
# Repository pattern implementation - Shared Layer

"""
Repository pattern implementation that provides a clean abstraction
layer for data access with dependency injection capabilities.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, TypeVar, Generic, Type
from dataclasses import dataclass
import time

from .database import db_client
from .logger import lambda_logger
from .exceptions import PlatformException


T = TypeVar('T')


@dataclass
class QueryOptions:
    """Options for repository queries."""
    limit: Optional[int] = None
    offset: Optional[int] = None
    sort_key: Optional[str] = None
    sort_direction: str = 'ASC'
    filters: Optional[Dict[str, Any]] = None
    include_deleted: bool = False


class IRepository(ABC, Generic[T]):
    """Interface for repository pattern."""
    
    @abstractmethod
    def get_by_id(self, entity_id: str, tenant_id: str) -> Optional[T]:
        """Get entity by ID."""
        pass
    
    @abstractmethod
    def create(self, entity: T, tenant_id: str) -> T:
        """Create a new entity."""
        pass
    
    @abstractmethod
    def update(self, entity: T, tenant_id: str) -> T:
        """Update an existing entity."""
        pass
    
    @abstractmethod
    def delete(self, entity_id: str, tenant_id: str) -> bool:
        """Delete an entity."""
        pass
    
    @abstractmethod
    def list(self, tenant_id: str, options: Optional[QueryOptions] = None) -> List[T]:
        """List entities with optional filtering."""
        pass


class BaseRepository(IRepository[T]):
    """Base repository implementation for DynamoDB."""
    
    def __init__(self, entity_type: str, model_class: Type[T]):
        """Initialize repository."""
        self.entity_type = entity_type
        self.model_class = model_class
        self.db_client = db_client
    
    def get_by_id(self, entity_id: str, tenant_id: str) -> Optional[T]:
        """Get entity by ID."""
        try:
            item = self.db_client.get_item(
                pk=f"{self.entity_type}#{entity_id}",
                sk="PROFILE",
                tenant_id=tenant_id
            )
            
            if item:
                return self._from_db_item(item)
            return None
            
        except Exception as e:
            lambda_logger.error("Failed to get entity by ID", extra={
                'entity_type': self.entity_type,
                'entity_id': entity_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None
    
    def create(self, entity: T, tenant_id: str) -> T:
        """Create a new entity."""
        try:
            # Convert entity to database item
            db_item = self._to_db_item(entity, tenant_id)
            
            # Save to database
            self.db_client.put_item(db_item, tenant_id)
            
            lambda_logger.info("Entity created successfully", extra={
                'entity_type': self.entity_type,
                'tenant_id': tenant_id
            })
            
            return entity
            
        except Exception as e:
            lambda_logger.error("Failed to create entity", extra={
                'entity_type': self.entity_type,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise PlatformException(f"Failed to create {self.entity_type}: {str(e)}")
    
    def update(self, entity: T, tenant_id: str) -> T:
        """Update an existing entity."""
        try:
            # Convert entity to database item
            db_item = self._to_db_item(entity, tenant_id)
            db_item['updated_at'] = int(time.time())
            
            # Save to database
            self.db_client.put_item(db_item, tenant_id)
            
            lambda_logger.info("Entity updated successfully", extra={
                'entity_type': self.entity_type,
                'tenant_id': tenant_id
            })
            
            return entity
            
        except Exception as e:
            lambda_logger.error("Failed to update entity", extra={
                'entity_type': self.entity_type,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            raise PlatformException(f"Failed to update {self.entity_type}: {str(e)}")
    
    def delete(self, entity_id: str, tenant_id: str) -> bool:
        """Delete an entity (soft delete)."""
        try:
            # Get current entity
            entity = self.get_by_id(entity_id, tenant_id)
            if not entity:
                return False
            
            # Mark as deleted
            db_item = self._to_db_item(entity, tenant_id)
            db_item['deleted_at'] = int(time.time())
            db_item['is_deleted'] = True
            
            # Save updated item
            self.db_client.put_item(db_item, tenant_id)
            
            lambda_logger.info("Entity deleted successfully", extra={
                'entity_type': self.entity_type,
                'entity_id': entity_id,
                'tenant_id': tenant_id
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to delete entity", extra={
                'entity_type': self.entity_type,
                'entity_id': entity_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return False
    
    def list(self, tenant_id: str, options: Optional[QueryOptions] = None) -> List[T]:
        """List entities with optional filtering."""
        try:
            options = options or QueryOptions()
            
            # Build query parameters
            query_params = {
                'key_condition_expression': 'PK = :pk',
                'expression_attribute_values': {':pk': f"{self.entity_type}#"}
            }
            
            # Add filters
            if options.filters:
                filter_expressions = []
                for key, value in options.filters.items():
                    filter_expressions.append(f"{key} = :{key}")
                    query_params['expression_attribute_values'][f":{key}"] = value
                
                if filter_expressions:
                    query_params['filter_expression'] = ' AND '.join(filter_expressions)
            
            # Add deleted filter
            if not options.include_deleted:
                deleted_filter = "attribute_not_exists(is_deleted) OR is_deleted = :false"
                query_params['expression_attribute_values'][':false'] = False
                
                if 'filter_expression' in query_params:
                    query_params['filter_expression'] += f" AND ({deleted_filter})"
                else:
                    query_params['filter_expression'] = deleted_filter
            
            # Add limit
            if options.limit:
                query_params['limit'] = options.limit
            
            # Execute query
            response = self.db_client.query(**query_params)
            
            # Convert items to entities
            entities = []
            for item in response.get('Items', []):
                entity = self._from_db_item(item)
                if entity:
                    entities.append(entity)
            
            return entities
            
        except Exception as e:
            lambda_logger.error("Failed to list entities", extra={
                'entity_type': self.entity_type,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return []
    
    def _to_db_item(self, entity: T, tenant_id: str) -> Dict[str, Any]:
        """Convert entity to database item."""
        # This should be implemented by subclasses
        raise NotImplementedError("Subclasses must implement _to_db_item")
    
    def _from_db_item(self, item: Dict[str, Any]) -> Optional[T]:
        """Convert database item to entity."""
        # This should be implemented by subclasses
        raise NotImplementedError("Subclasses must implement _from_db_item")
