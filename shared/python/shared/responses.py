# shared/python/shared/responses.py
# Standardized HTTP response utilities - Shared Layer

"""
Standardized HTTP response utilities for the platform.
Provides consistent response formatting across all services.
"""

import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from .config import get_settings


class APIResponse:
    """Standardized API response builder."""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "Success",
        status_code: int = 200,
        meta: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a successful API response."""
        response = {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "status_code": status_code
        }
        
        if meta:
            response["meta"] = meta
            
        return {
            "statusCode": status_code,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Tenant-Id",
                "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS"
            },
            "body": json.dumps(response, default=str, ensure_ascii=False)
        }
    
    @staticmethod
    def error(
        message: str,
        status_code: int = 400,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        validation_errors: Optional[List[Dict[str, str]]] = None
    ) -> Dict[str, Any]:
        """Create an error API response."""
        response = {
            "success": False,
            "message": message,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "status_code": status_code
        }
        
        if error_code:
            response["error_code"] = error_code
            
        if details:
            response["details"] = details
            
        if validation_errors:
            response["validation_errors"] = validation_errors
            
        return {
            "statusCode": status_code,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Tenant-Id",
                "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS"
            },
            "body": json.dumps(response, default=str, ensure_ascii=False)
        }
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "Resource created successfully",
        location: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a 201 Created response."""
        headers = {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Tenant-Id",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS"
        }
        
        if location:
            headers["Location"] = location
            
        return APIResponse.success(data, message, 201)
    
    @staticmethod
    def no_content(message: str = "Operation completed successfully") -> Dict[str, Any]:
        """Create a 204 No Content response."""
        return {
            "statusCode": 204,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Tenant-Id",
                "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS"
            },
            "body": ""
        }
    
    @staticmethod
    def unauthorized(
        message: str = "Unauthorized access",
        error_code: str = "UNAUTHORIZED"
    ) -> Dict[str, Any]:
        """Create a 401 Unauthorized response."""
        return APIResponse.error(message, 401, error_code)
    
    @staticmethod
    def forbidden(
        message: str = "Access forbidden",
        error_code: str = "FORBIDDEN"
    ) -> Dict[str, Any]:
        """Create a 403 Forbidden response."""
        return APIResponse.error(message, 403, error_code)
    
    @staticmethod
    def not_found(
        message: str = "Resource not found",
        error_code: str = "NOT_FOUND"
    ) -> Dict[str, Any]:
        """Create a 404 Not Found response."""
        return APIResponse.error(message, 404, error_code)
    
    @staticmethod
    def conflict(
        message: str = "Resource conflict",
        error_code: str = "CONFLICT"
    ) -> Dict[str, Any]:
        """Create a 409 Conflict response."""
        return APIResponse.error(message, 409, error_code)
    
    @staticmethod
    def validation_error(
        message: str = "Validation failed",
        validation_errors: List[Dict[str, str]] = None,
        error_code: str = "VALIDATION_ERROR"
    ) -> Dict[str, Any]:
        """Create a 422 Validation Error response."""
        return APIResponse.error(
            message, 
            422, 
            error_code, 
            validation_errors=validation_errors
        )
    
    @staticmethod
    def rate_limit_exceeded(
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create a 429 Rate Limit Exceeded response."""
        headers = {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Tenant-Id",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS"
        }
        
        if retry_after:
            headers["Retry-After"] = str(retry_after)
            
        response = APIResponse.error(message, 429, "RATE_LIMIT_EXCEEDED")
        response["headers"].update(headers)
        return response
    
    @staticmethod
    def internal_server_error(
        message: str = "Internal server error",
        error_code: str = "INTERNAL_ERROR"
    ) -> Dict[str, Any]:
        """Create a 500 Internal Server Error response."""
        return APIResponse.error(message, 500, error_code)
    
    @staticmethod
    def service_unavailable(
        message: str = "Service temporarily unavailable",
        error_code: str = "SERVICE_UNAVAILABLE"
    ) -> Dict[str, Any]:
        """Create a 503 Service Unavailable response."""
        return APIResponse.error(message, 503, error_code)


class PaginatedResponse:
    """Helper for paginated responses."""
    
    @staticmethod
    def create(
        items: List[Any],
        total_count: int,
        page: int = 1,
        page_size: int = 20,
        message: str = "Data retrieved successfully"
    ) -> Dict[str, Any]:
        """Create a paginated response."""
        total_pages = (total_count + page_size - 1) // page_size
        has_next = page < total_pages
        has_previous = page > 1
        
        meta = {
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        }
        
        return APIResponse.success(
            data=items,
            message=message,
            meta=meta
        )


def handle_cors_preflight() -> Dict[str, Any]:
    """Handle CORS preflight requests."""
    return {
        "statusCode": 200,
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Tenant-Id",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS",
            "Access-Control-Max-Age": "86400"
        },
        "body": ""
    }

    @staticmethod
    def service_unavailable(
        message: str = "Service temporarily unavailable",
        error_code: str = "SERVICE_UNAVAILABLE",
        details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a 503 Service Unavailable response."""
        return APIResponse.error(
            message=message,
            status_code=503,
            error_code=error_code,
            details=details
        )


# Standardized Error Codes
class ErrorCodes:
    """Standardized error codes for consistent error handling."""

    # Authentication & Authorization
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    TOKEN_INVALID = "TOKEN_INVALID"
    ACCOUNT_LOCKED = "ACCOUNT_LOCKED"
    EMAIL_NOT_VERIFIED = "EMAIL_NOT_VERIFIED"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"

    # Validation
    VALIDATION_FAILED = "VALIDATION_FAILED"
    MISSING_BODY = "MISSING_BODY"
    INVALID_JSON = "INVALID_JSON"
    INVALID_EMAIL = "INVALID_EMAIL"
    INVALID_PASSWORD = "INVALID_PASSWORD"
    INVALID_UUID = "INVALID_UUID"
    INVALID_PHONE = "INVALID_PHONE"
    FIELD_REQUIRED = "FIELD_REQUIRED"
    FIELD_TOO_LONG = "FIELD_TOO_LONG"
    FIELD_TOO_SHORT = "FIELD_TOO_SHORT"

    # Resources
    NOT_FOUND = "NOT_FOUND"
    ALREADY_EXISTS = "ALREADY_EXISTS"
    CONFLICT = "CONFLICT"
    RESOURCE_LOCKED = "RESOURCE_LOCKED"

    # Business Logic
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED"
    BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION"
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"

    # External Services
    PAYMENT_FAILED = "PAYMENT_FAILED"
    PAYMENT_REQUIRED = "PAYMENT_REQUIRED"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    EMAIL_DELIVERY_FAILED = "EMAIL_DELIVERY_FAILED"

    # System
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    TIMEOUT = "TIMEOUT"
    DATABASE_ERROR = "DATABASE_ERROR"

    # Platform Specific
    TENANT_NOT_FOUND = "TENANT_NOT_FOUND"
    USER_NOT_FOUND = "USER_NOT_FOUND"
    SUBSCRIPTION_NOT_FOUND = "SUBSCRIPTION_NOT_FOUND"
    PLAN_NOT_FOUND = "PLAN_NOT_FOUND"


class StandardResponses:
    """Pre-configured standard responses for common scenarios."""

    @staticmethod
    def user_not_found() -> Dict[str, Any]:
        """Standard user not found response."""
        return APIResponse.not_found(
            message="User not found",
            error_code=ErrorCodes.USER_NOT_FOUND
        )

    @staticmethod
    def tenant_not_found() -> Dict[str, Any]:
        """Standard tenant not found response."""
        return APIResponse.not_found(
            message="Tenant not found",
            error_code=ErrorCodes.TENANT_NOT_FOUND
        )

    @staticmethod
    def invalid_credentials() -> Dict[str, Any]:
        """Standard invalid credentials response."""
        return APIResponse.unauthorized(
            message="Invalid email or password",
            error_code=ErrorCodes.INVALID_CREDENTIALS
        )

    @staticmethod
    def account_locked() -> Dict[str, Any]:
        """Standard account locked response."""
        return APIResponse.unauthorized(
            message="Account is temporarily locked due to too many failed attempts",
            error_code=ErrorCodes.ACCOUNT_LOCKED
        )

    @staticmethod
    def email_not_verified() -> Dict[str, Any]:
        """Standard email not verified response."""
        return APIResponse.unauthorized(
            message="Email address must be verified before login",
            error_code=ErrorCodes.EMAIL_NOT_VERIFIED
        )

    @staticmethod
    def insufficient_permissions() -> Dict[str, Any]:
        """Standard insufficient permissions response."""
        return APIResponse.forbidden(
            message="Insufficient permissions to perform this action",
            error_code=ErrorCodes.INSUFFICIENT_PERMISSIONS
        )

    @staticmethod
    def validation_failed(errors: List[str]) -> Dict[str, Any]:
        """Standard validation failed response."""
        return APIResponse.validation_error(
            message="Request validation failed",
            validation_errors=[
                {'field': 'request', 'message': error}
                for error in errors
            ],
            error_code=ErrorCodes.VALIDATION_FAILED
        )

    @staticmethod
    def payment_required(
        message: str = "Payment required",
        error_code: str = "PAYMENT_REQUIRED",
        details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a 402 Payment Required response."""
        return APIResponse.error(
            message=message,
            status_code=402,
            error_code=error_code,
            details=details
        )


# Auth Service specific response helpers
def auth_success_response(data: Dict[str, Any], message: str = "Authentication successful") -> Dict[str, Any]:
    """Create Auth Service compatible success response."""
    return {
        'statusCode': 200,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        'body': json.dumps({
            'success': True,
            'message': message,
            **data
        })
    }


def auth_error_response(message: str, error_code: str, status_code: int = 401) -> Dict[str, Any]:
    """Create Auth Service compatible error response."""
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        'body': json.dumps({
            'success': False,
            'message': message,
            'error_code': error_code
        })
    }


def rate_limit_response() -> Dict[str, Any]:
    """Create rate limit exceeded response."""
    return auth_error_response(
        'Too many attempts. Please try again later.',
        'RATE_LIMIT_EXCEEDED',
        429
    )
