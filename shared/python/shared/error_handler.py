#!/usr/bin/env python3
# shared/python/shared/error_handler.py
# Standardized error handling utilities

"""
Standardized error handling for all services.
Provides consistent error handling patterns, logging, and response formatting.
"""

import traceback
from typing import Dict, Any, Optional, Callable
from functools import wraps

from .logger import lambda_logger
from .responses import APIResponse
from .exceptions import (
    PlatformException,
    ValidationException,
    AuthenticationException,
    AuthorizationException,
    ResourceNotFoundException,
    ResourceConflictException,
    ExternalServiceException,
    DatabaseException,
    PaymentException,
    BusinessLogicException
)


class ErrorHandler:
    """Centralized error handling for consistent error management."""
    
    def __init__(self, service_name: str, operation_name: str):
        """Initialize error handler."""
        self.service_name = service_name
        self.operation_name = operation_name
        self.start_time = lambda_logger.get_current_timestamp()
    
    def handle_exception(self, 
                        exception: Exception, 
                        request_id: str = None,
                        method: str = "POST",
                        path: str = None,
                        context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Handle exception with standardized logging and response."""
        
        duration_ms = lambda_logger.get_current_timestamp() - self.start_time
        error_context = {
            'service': self.service_name,
            'operation': self.operation_name,
            'request_id': request_id,
            'duration_ms': duration_ms,
            'error_type': type(exception).__name__,
            'error_message': str(exception)
        }
        
        if context:
            error_context.update(context)
        
        # Handle specific exception types
        if isinstance(exception, ValidationException):
            return self._handle_validation_error(exception, error_context, method, path)
        
        elif isinstance(exception, AuthenticationException):
            return self._handle_authentication_error(exception, error_context, method, path)
        
        elif isinstance(exception, AuthorizationException):
            return self._handle_authorization_error(exception, error_context, method, path)
        
        elif isinstance(exception, ResourceNotFoundException):
            return self._handle_not_found_error(exception, error_context, method, path)
        
        elif isinstance(exception, ResourceConflictException):
            return self._handle_conflict_error(exception, error_context, method, path)
        
        elif isinstance(exception, ExternalServiceException):
            return self._handle_external_service_error(exception, error_context, method, path)
        
        elif isinstance(exception, DatabaseException):
            return self._handle_database_error(exception, error_context, method, path)
        
        elif isinstance(exception, PaymentException):
            return self._handle_payment_error(exception, error_context, method, path)
        
        elif isinstance(exception, BusinessLogicException):
            return self._handle_business_logic_error(exception, error_context, method, path)
        
        elif isinstance(exception, PlatformException):
            return self._handle_platform_error(exception, error_context, method, path)
        
        else:
            return self._handle_unexpected_error(exception, error_context, method, path)
    
    def _handle_validation_error(self, exception: ValidationException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle validation errors."""
        lambda_logger.warning("Validation error", extra=context)
        
        return APIResponse.validation_error(
            message=exception.message,
            validation_errors=getattr(exception, 'validation_errors', []),
            error_code=exception.error_code
        )
    
    def _handle_authentication_error(self, exception: AuthenticationException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle authentication errors."""
        lambda_logger.warning("Authentication error", extra=context)
        
        return APIResponse.unauthorized(
            message=exception.message,
            error_code=exception.error_code
        )
    
    def _handle_authorization_error(self, exception: AuthorizationException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle authorization errors."""
        lambda_logger.warning("Authorization error", extra=context)
        
        return APIResponse.forbidden(
            message=exception.message,
            error_code=exception.error_code
        )
    
    def _handle_not_found_error(self, exception: ResourceNotFoundException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle not found errors."""
        lambda_logger.info("Resource not found", extra=context)
        
        return APIResponse.not_found(
            message=exception.message,
            error_code=exception.error_code
        )
    
    def _handle_conflict_error(self, exception: ResourceConflictException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle conflict errors."""
        lambda_logger.warning("Resource conflict", extra=context)
        
        return APIResponse.conflict(
            message=exception.message,
            error_code=exception.error_code
        )
    
    def _handle_external_service_error(self, exception: ExternalServiceException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle external service errors."""
        lambda_logger.error("External service error", extra=context)
        
        return APIResponse.service_unavailable(
            message="External service temporarily unavailable",
            error_code=exception.error_code
        )
    
    def _handle_database_error(self, exception: DatabaseException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle database errors."""
        lambda_logger.error("Database error", extra=context)
        
        return APIResponse.internal_server_error(
            message="Database operation failed",
            error_code=exception.error_code
        )
    
    def _handle_payment_error(self, exception: PaymentException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle payment errors."""
        lambda_logger.error("Payment error", extra=context)
        
        return APIResponse.payment_required(
            message=exception.message,
            error_code=exception.error_code
        )
    
    def _handle_business_logic_error(self, exception: BusinessLogicException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle business logic errors."""
        lambda_logger.warning("Business logic error", extra=context)
        
        return APIResponse.bad_request(
            message=exception.message,
            error_code=exception.error_code
        )
    
    def _handle_platform_error(self, exception: PlatformException, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle generic platform errors."""
        lambda_logger.error("Platform error", extra=context)
        
        return APIResponse.error(
            message=exception.message,
            status_code=getattr(exception, 'status_code', 500),
            error_code=exception.error_code,
            details=exception.details
        )
    
    def _handle_unexpected_error(self, exception: Exception, context: Dict, method: str, path: str) -> Dict[str, Any]:
        """Handle unexpected errors."""
        # Add stack trace for unexpected errors
        context['stack_trace'] = traceback.format_exc()
        
        lambda_logger.error("Unexpected error", extra=context)
        
        return APIResponse.internal_server_error(
            message="An unexpected error occurred",
            error_code="UNEXPECTED_ERROR"
        )


def with_error_handling(service_name: str, operation_name: str, method: str = "POST", path: str = None):
    """Decorator for standardized error handling."""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(event: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
            error_handler = ErrorHandler(service_name, operation_name)
            request_id = event.get('requestContext', {}).get('requestId', 'unknown')
            
            try:
                return func(event, context)
                
            except Exception as e:
                return error_handler.handle_exception(
                    exception=e,
                    request_id=request_id,
                    method=method,
                    path=path or event.get('path', 'unknown'),
                    context={
                        'event_source': event.get('source'),
                        'user_agent': event.get('headers', {}).get('User-Agent'),
                        'ip_address': event.get('requestContext', {}).get('identity', {}).get('sourceIp')
                    }
                )
        
        return wrapper
    return decorator


def create_error_handler(service_name: str, operation_name: str) -> ErrorHandler:
    """Create error handler instance."""
    return ErrorHandler(service_name, operation_name)
