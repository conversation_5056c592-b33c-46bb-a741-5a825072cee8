# shared/python/shared/events.py
# Domain events system with SNS/SQS integration - Shared Layer

"""
Domain events system that provides event publishing and handling
capabilities for implementing event-driven architecture.
"""

import json
import time
import uuid
import boto3
from enum import Enum
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from datetime import datetime

from .logger import lambda_logger
from .config import get_settings
from .exceptions import PlatformException


class EventType(Enum):
    """Domain event types."""
    # Tenant events
    TENANT_CREATED = "tenant.created"
    TENANT_UPDATED = "tenant.updated"
    TENANT_DELETED = "tenant.deleted"
    TENANT_ACTIVATED = "tenant.activated"
    TENANT_SUSPENDED = "tenant.suspended"
    
    # User events
    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"
    USER_DELETED = "user.deleted"
    USER_ACTIVATED = "user.activated"
    USER_DEACTIVATED = "user.deactivated"
    USER_INVITED = "user.invited"
    USER_LOGIN = "user.login"
    
    # Payment events
    PAYMENT_SUCCEEDED = "payment.succeeded"
    PAYMENT_FAILED = "payment.failed"
    SUBSCRIPTION_CREATED = "subscription.created"
    SUBSCRIPTION_UPDATED = "subscription.updated"
    SUBSCRIPTION_CANCELLED = "subscription.cancelled"
    INVOICE_CREATED = "invoice.created"
    INVOICE_PAID = "invoice.paid"
    
    # Security events
    SECURITY_BREACH = "security.breach"
    SUSPICIOUS_ACTIVITY = "security.suspicious_activity"
    AUTHENTICATION_FAILED = "security.authentication_failed"


@dataclass
class DomainEvent:
    """Base domain event."""
    event_id: str
    event_type: EventType
    aggregate_id: str
    aggregate_type: str
    tenant_id: str
    event_data: Dict[str, Any]
    event_version: str = "1.0"
    occurred_at: Optional[datetime] = None
    correlation_id: Optional[str] = None
    causation_id: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if not self.occurred_at:
            self.occurred_at = datetime.utcnow()
        
        if not self.event_id:
            self.event_id = str(uuid.uuid4())


class EventPublisher:
    """Publishes domain events to SNS topics."""
    
    def __init__(self):
        """Initialize event publisher."""
        self.settings = get_settings()
        self.sns_client = boto3.client('sns', region_name=self.settings.region)
        
        # Topic ARNs (these would be imported from infrastructure)
        self.topic_arns = {
            'tenant': f"arn:aws:sns:{self.settings.region}:*:{self.settings.project_name}-{self.settings.environment}-tenant-events",
            'user': f"arn:aws:sns:{self.settings.region}:*:{self.settings.project_name}-{self.settings.environment}-user-events",
            'payment': f"arn:aws:sns:{self.settings.region}:*:{self.settings.project_name}-{self.settings.environment}-payment-events",
            'security': f"arn:aws:sns:{self.settings.region}:*:{self.settings.project_name}-{self.settings.environment}-security-events"
        }
    
    def publish(self, event: DomainEvent) -> str:
        """Publish domain event to appropriate SNS topic."""
        try:
            # Determine topic based on event type
            topic_arn = self._get_topic_for_event(event.event_type)
            
            # Prepare message
            message = {
                'event_id': event.event_id,
                'event_type': event.event_type.value,
                'aggregate_id': event.aggregate_id,
                'aggregate_type': event.aggregate_type,
                'tenant_id': event.tenant_id,
                'event_data': event.event_data,
                'event_version': event.event_version,
                'occurred_at': event.occurred_at.isoformat() if event.occurred_at else None,
                'correlation_id': event.correlation_id,
                'causation_id': event.causation_id
            }
            
            # Message attributes for filtering
            message_attributes = {
                'event_type': {
                    'DataType': 'String',
                    'StringValue': event.event_type.value
                },
                'aggregate_type': {
                    'DataType': 'String',
                    'StringValue': event.aggregate_type
                },
                'tenant_id': {
                    'DataType': 'String',
                    'StringValue': event.tenant_id
                }
            }
            
            # Publish to SNS
            response = self.sns_client.publish(
                TopicArn=topic_arn,
                Message=json.dumps(message, default=str),
                MessageAttributes=message_attributes,
                Subject=f"Event: {event.event_type.value}"
            )
            
            message_id = response['MessageId']
            
            lambda_logger.info("Event published successfully", extra={
                'event_id': event.event_id,
                'event_type': event.event_type.value,
                'message_id': message_id,
                'topic_arn': topic_arn,
                'tenant_id': event.tenant_id
            })
            
            return message_id
            
        except Exception as e:
            lambda_logger.error("Failed to publish event", extra={
                'event_id': event.event_id,
                'event_type': event.event_type.value,
                'error': str(e),
                'tenant_id': event.tenant_id
            })
            raise PlatformException(f"Failed to publish event: {str(e)}")
    
    def _get_topic_for_event(self, event_type: EventType) -> str:
        """Get SNS topic ARN for event type."""
        if event_type.value.startswith('tenant.'):
            return self.topic_arns['tenant']
        elif event_type.value.startswith('user.'):
            return self.topic_arns['user']
        elif event_type.value.startswith('payment.') or event_type.value.startswith('subscription.') or event_type.value.startswith('invoice.'):
            return self.topic_arns['payment']
        elif event_type.value.startswith('security.'):
            return self.topic_arns['security']
        else:
            # Default to user events topic
            return self.topic_arns['user']


class EventHandler:
    """Base class for event handlers."""
    
    def __init__(self):
        """Initialize event handler."""
        self.handled_events: List[str] = []
    
    def can_handle(self, event_type: EventType) -> bool:
        """Check if handler can handle event type."""
        return event_type.value in self.handled_events
    
    def handle(self, event: DomainEvent) -> None:
        """Handle domain event."""
        raise NotImplementedError("Subclasses must implement handle method")


class EventBus:
    """In-process event bus for local event handling."""
    
    def __init__(self):
        """Initialize event bus."""
        self.handlers: Dict[str, List[EventHandler]] = {}
        self.publisher = EventPublisher()
    
    def register_handler(self, event_type: EventType, handler: EventHandler) -> None:
        """Register event handler."""
        event_key = event_type.value
        
        if event_key not in self.handlers:
            self.handlers[event_key] = []
        
        self.handlers[event_key].append(handler)
        
        lambda_logger.debug("Event handler registered", extra={
            'event_type': event_type.value,
            'handler_class': handler.__class__.__name__
        })
    
    def publish_event(self, event: DomainEvent) -> None:
        """Publish event locally and to SNS."""
        try:
            # Handle locally first
            self._handle_locally(event)
            
            # Publish to SNS for external consumers
            self.publisher.publish(event)
            
        except Exception as e:
            lambda_logger.error("Failed to publish event", extra={
                'event_id': event.event_id,
                'event_type': event.event_type.value,
                'error': str(e)
            })
            raise
    
    def _handle_locally(self, event: DomainEvent) -> None:
        """Handle event with local handlers."""
        event_key = event.event_type.value
        
        if event_key in self.handlers:
            for handler in self.handlers[event_key]:
                try:
                    handler.handle(event)
                    
                    lambda_logger.debug("Event handled locally", extra={
                        'event_id': event.event_id,
                        'event_type': event.event_type.value,
                        'handler_class': handler.__class__.__name__
                    })
                    
                except Exception as e:
                    lambda_logger.error("Event handler failed", extra={
                        'event_id': event.event_id,
                        'event_type': event.event_type.value,
                        'handler_class': handler.__class__.__name__,
                        'error': str(e)
                    })
                    # Continue with other handlers


# Global event bus instance
event_bus = EventBus()


# Convenience functions for creating events
def create_tenant_event(
    event_type: EventType,
    tenant_id: str,
    event_data: Dict[str, Any],
    correlation_id: Optional[str] = None
) -> DomainEvent:
    """Create tenant-related event."""
    return DomainEvent(
        event_id=str(uuid.uuid4()),
        event_type=event_type,
        aggregate_id=tenant_id,
        aggregate_type="Tenant",
        tenant_id=tenant_id,
        event_data=event_data,
        correlation_id=correlation_id
    )


def create_user_event(
    event_type: EventType,
    user_id: str,
    tenant_id: str,
    event_data: Dict[str, Any],
    correlation_id: Optional[str] = None
) -> DomainEvent:
    """Create user-related event."""
    return DomainEvent(
        event_id=str(uuid.uuid4()),
        event_type=event_type,
        aggregate_id=user_id,
        aggregate_type="User",
        tenant_id=tenant_id,
        event_data=event_data,
        correlation_id=correlation_id
    )


def create_payment_event(
    event_type: EventType,
    payment_id: str,
    tenant_id: str,
    event_data: Dict[str, Any],
    correlation_id: Optional[str] = None
) -> DomainEvent:
    """Create payment-related event."""
    return DomainEvent(
        event_id=str(uuid.uuid4()),
        event_type=event_type,
        aggregate_id=payment_id,
        aggregate_type="Payment",
        tenant_id=tenant_id,
        event_data=event_data,
        correlation_id=correlation_id
    )
