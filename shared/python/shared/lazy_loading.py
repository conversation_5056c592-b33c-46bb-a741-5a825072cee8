# shared/python/shared/lazy_loading.py
# Lazy loading system for optimized data access - Shared Layer

"""
Lazy loading system that defers expensive operations until they are actually needed,
reducing initial load times and improving overall performance.
"""

import time
from typing import Any, Callable, Optional, Dict, Union
from functools import wraps
from dataclasses import dataclass, field

from .logger import lambda_logger
from .cache import cache_manager, CacheConfig, CacheBackend


@dataclass
class LazyProperty:
    """Represents a lazy-loaded property."""
    loader: Callable[[], Any]
    cache_key: Optional[str] = None
    ttl_seconds: int = 300
    _value: Any = field(default=None, init=False)
    _loaded: bool = field(default=False, init=False)
    _load_time: Optional[float] = field(default=None, init=False)


class LazyLoader:
    """Manages lazy loading of properties and data."""
    
    def __init__(self):
        """Initialize lazy loader."""
        self.cache_config = CacheConfig(
            ttl_seconds=300,
            backend=CacheBackend.MEMORY,
            key_prefix="lazy"
        )
        self.load_stats = {}
    
    def lazy_property(
        self,
        cache_key: Optional[str] = None,
        ttl_seconds: int = 300,
        use_cache: bool = True
    ):
        """Decorator for creating lazy-loaded properties."""
        def decorator(func: Callable) -> property:
            attr_name = f"_lazy_{func.__name__}"
            
            def getter(self):
                # Check if already loaded
                if hasattr(self, attr_name):
                    lazy_prop = getattr(self, attr_name)
                    if lazy_prop._loaded:
                        return lazy_prop._value
                else:
                    # Create lazy property
                    lazy_prop = LazyProperty(
                        loader=lambda: func(self),
                        cache_key=cache_key,
                        ttl_seconds=ttl_seconds
                    )
                    setattr(self, attr_name, lazy_prop)
                
                # Load the value
                return self._load_lazy_property(lazy_prop, func.__name__, use_cache)
            
            def setter(self, value):
                # Create or update lazy property with direct value
                lazy_prop = LazyProperty(
                    loader=lambda: value,
                    cache_key=cache_key,
                    ttl_seconds=ttl_seconds
                )
                lazy_prop._value = value
                lazy_prop._loaded = True
                lazy_prop._load_time = time.time()
                setattr(self, attr_name, lazy_prop)
                
                # Update cache if enabled
                if use_cache and cache_key:
                    cache_manager.set(cache_key, value, self.cache_config)
            
            return property(getter, setter)
        
        return decorator
    
    def _load_lazy_property(
        self,
        lazy_prop: LazyProperty,
        property_name: str,
        use_cache: bool
    ) -> Any:
        """Load a lazy property value."""
        start_time = time.time()
        
        try:
            # Try cache first
            if use_cache and lazy_prop.cache_key:
                cached_value = cache_manager.get(lazy_prop.cache_key, self.cache_config)
                if cached_value is not None:
                    lazy_prop._value = cached_value
                    lazy_prop._loaded = True
                    lazy_prop._load_time = time.time()
                    
                    load_time_ms = (time.time() - start_time) * 1000
                    self._record_load_stats(property_name, load_time_ms, True)
                    
                    lambda_logger.debug("Lazy property loaded from cache", extra={
                        'property_name': property_name,
                        'cache_key': lazy_prop.cache_key,
                        'load_time_ms': load_time_ms
                    })
                    
                    return lazy_prop._value
            
            # Load using the loader function
            lambda_logger.debug("Loading lazy property", extra={
                'property_name': property_name,
                'cache_key': lazy_prop.cache_key
            })
            
            value = lazy_prop.loader()
            lazy_prop._value = value
            lazy_prop._loaded = True
            lazy_prop._load_time = time.time()
            
            # Cache the loaded value
            if use_cache and lazy_prop.cache_key:
                cache_manager.set(lazy_prop.cache_key, value, self.cache_config)
            
            load_time_ms = (time.time() - start_time) * 1000
            self._record_load_stats(property_name, load_time_ms, False)
            
            lambda_logger.debug("Lazy property loaded", extra={
                'property_name': property_name,
                'load_time_ms': load_time_ms,
                'cached': use_cache and lazy_prop.cache_key is not None
            })
            
            return value
            
        except Exception as e:
            load_time_ms = (time.time() - start_time) * 1000
            self._record_load_stats(property_name, load_time_ms, False, error=True)
            
            lambda_logger.error("Failed to load lazy property", extra={
                'property_name': property_name,
                'error': str(e),
                'load_time_ms': load_time_ms
            })
            raise
    
    def _record_load_stats(
        self,
        property_name: str,
        load_time_ms: float,
        cache_hit: bool,
        error: bool = False
    ) -> None:
        """Record loading statistics."""
        if property_name not in self.load_stats:
            self.load_stats[property_name] = {
                'total_loads': 0,
                'cache_hits': 0,
                'errors': 0,
                'total_load_time_ms': 0,
                'avg_load_time_ms': 0
            }
        
        stats = self.load_stats[property_name]
        stats['total_loads'] += 1
        stats['total_load_time_ms'] += load_time_ms
        stats['avg_load_time_ms'] = stats['total_load_time_ms'] / stats['total_loads']
        
        if cache_hit:
            stats['cache_hits'] += 1
        
        if error:
            stats['errors'] += 1
    
    def get_load_stats(self) -> Dict[str, Any]:
        """Get loading statistics."""
        return self.load_stats.copy()


class LazyCollection:
    """Lazy-loaded collection that loads items on demand."""
    
    def __init__(
        self,
        loader: Callable[[], list],
        cache_key: Optional[str] = None,
        ttl_seconds: int = 300
    ):
        """Initialize lazy collection."""
        self.loader = loader
        self.cache_key = cache_key
        self.ttl_seconds = ttl_seconds
        self._items: Optional[list] = None
        self._loaded = False
        self._load_time: Optional[float] = None
        
        self.cache_config = CacheConfig(
            ttl_seconds=ttl_seconds,
            backend=CacheBackend.MEMORY,
            key_prefix="lazy_collection"
        )
    
    def __len__(self) -> int:
        """Get collection length."""
        self._ensure_loaded()
        return len(self._items)
    
    def __iter__(self):
        """Iterate over collection items."""
        self._ensure_loaded()
        return iter(self._items)
    
    def __getitem__(self, index):
        """Get item by index."""
        self._ensure_loaded()
        return self._items[index]
    
    def __bool__(self) -> bool:
        """Check if collection has items."""
        self._ensure_loaded()
        return bool(self._items)
    
    def _ensure_loaded(self) -> None:
        """Ensure collection is loaded."""
        if self._loaded:
            return
        
        start_time = time.time()
        
        try:
            # Try cache first
            if self.cache_key:
                cached_items = cache_manager.get(self.cache_key, self.cache_config)
                if cached_items is not None:
                    self._items = cached_items
                    self._loaded = True
                    self._load_time = time.time()
                    
                    load_time_ms = (time.time() - start_time) * 1000
                    lambda_logger.debug("Lazy collection loaded from cache", extra={
                        'cache_key': self.cache_key,
                        'item_count': len(self._items),
                        'load_time_ms': load_time_ms
                    })
                    return
            
            # Load using loader function
            lambda_logger.debug("Loading lazy collection", extra={
                'cache_key': self.cache_key
            })
            
            self._items = self.loader()
            self._loaded = True
            self._load_time = time.time()
            
            # Cache the loaded items
            if self.cache_key:
                cache_manager.set(self.cache_key, self._items, self.cache_config)
            
            load_time_ms = (time.time() - start_time) * 1000
            lambda_logger.debug("Lazy collection loaded", extra={
                'item_count': len(self._items),
                'load_time_ms': load_time_ms,
                'cached': self.cache_key is not None
            })
            
        except Exception as e:
            load_time_ms = (time.time() - start_time) * 1000
            lambda_logger.error("Failed to load lazy collection", extra={
                'cache_key': self.cache_key,
                'error': str(e),
                'load_time_ms': load_time_ms
            })
            raise
    
    def invalidate(self) -> None:
        """Invalidate the collection cache."""
        self._loaded = False
        self._items = None
        self._load_time = None
        
        if self.cache_key:
            cache_manager.delete(self.cache_key, self.cache_config)
    
    def refresh(self) -> None:
        """Force refresh the collection."""
        self.invalidate()
        self._ensure_loaded()


# Global lazy loader instance
lazy_loader = LazyLoader()


def lazy_property(
    cache_key: Optional[str] = None,
    ttl_seconds: int = 300,
    use_cache: bool = True
):
    """Decorator for creating lazy-loaded properties."""
    return lazy_loader.lazy_property(cache_key, ttl_seconds, use_cache)


def get_lazy_loading_stats() -> Dict[str, Any]:
    """Get lazy loading statistics."""
    return lazy_loader.get_load_stats()
