#!/usr/bin/env python3
# shared/python/shared/business_logic_decorator.py
# Unified decorator for business logic handlers

"""
Unified decorator that combines error handling, validation, logging, and monitoring
for consistent business logic implementation across all services.
"""

import time
from typing import Dict, Any, Optional, Type, Callable, List
from functools import wraps

from shared.auth import get_auth_context
from shared.error_handler import <PERSON>rrorHandler
from shared.validation_manager import ValidationManager

from .logger import lambda_logger, log_api_request, log_api_response, log_business_operation
from .exceptions import AuthorizationException, ValidationException, AuthenticationException
from .responses import auth_error_response


class BusinessLogicHandler:
    """Unified business logic handler with all standard patterns."""
    
    def __init__(self, 
                 service_name: str,
                 operation_name: str,
                 method: str = "POST",
                 path: str = None,
                 require_auth: bool = True,
                 required_roles: Optional[List[str]] = None,
                 body_validator: Optional[Type] = None,
                 path_params: Optional[List[str]] = None,
                 query_params: Optional[List[str]] = None,
                 required_headers: Optional[List[str]] = None):
        """Initialize business logic handler."""
        
        self.service_name = service_name
        self.operation_name = operation_name
        self.method = method
        self.path = path
        self.require_auth = require_auth
        self.required_roles = required_roles or []
        self.body_validator = body_validator
        self.path_params = path_params or []
        self.query_params = query_params or []
        self.required_headers = required_headers or []
        
        self.error_handler = ErrorHandler(service_name, operation_name)
        self.validation_manager = ValidationManager(service_name)
    
    def __call__(self, func: Callable) -> Callable:
        """Apply all business logic patterns to the function."""
        
        @wraps(func)
        def wrapper(event: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
            start_time = time.time()
            request_id = event.get('requestContext', {}).get('requestId', 'unknown')
            
            try:
                # 1. Log incoming request
                log_api_request(
                    lambda_logger,
                    self.method,
                    self.path or event.get('path', 'unknown'),
                    request_id=request_id
                )
                
                # 2. Authentication and authorization
                auth_context = None
                if self.require_auth:
                    auth_context = get_auth_context(event)
                    if not auth_context:
                        raise AuthorizationException(
                            "Authentication required",
                            error_code="AUTH_REQUIRED"
                        )
                    
                    # Check required roles
                    if self.required_roles:
                        has_required_role = any(
                            auth_context.has_role(role) for role in self.required_roles
                        )
                        if not has_required_role:
                            raise AuthorizationException(
                                f"Insufficient permissions. Required roles: {', '.join(self.required_roles)}",
                                error_code="INSUFFICIENT_PERMISSIONS"
                            )
                
                # 3. Request validation
                if self.body_validator:
                    validated_body = self.validation_manager.validate_request_body(
                        event, self.body_validator
                    )
                    event['validated_body'] = validated_body
                
                if self.path_params:
                    validated_path = self.validation_manager.validate_path_parameters(
                        event, self.path_params
                    )
                    event['validated_path'] = validated_path
                
                if self.query_params:
                    validated_query = self.validation_manager.validate_query_parameters(
                        event, self.query_params
                    )
                    event['validated_query'] = validated_query
                
                if self.required_headers:
                    validated_headers = self.validation_manager.validate_headers(
                        event, self.required_headers
                    )
                    event['validated_headers'] = validated_headers
                
                # 4. Add auth context to event
                if auth_context:
                    event['auth_context'] = auth_context
                
                # 5. Log business operation start
                log_business_operation(
                    lambda_logger,
                    self.operation_name,
                    "request",
                    tenant_id=auth_context.tenant_id if auth_context else None,
                    user_id=auth_context.user_id if auth_context else None,
                    status="started"
                )
                
                # 6. Execute business logic
                result = func(event, context)
                
                # 7. Log successful completion
                duration_ms = (time.time() - start_time) * 1000
                
                log_business_operation(
                    lambda_logger,
                    self.operation_name,
                    "request",
                    tenant_id=auth_context.tenant_id if auth_context else None,
                    user_id=auth_context.user_id if auth_context else None,
                    status="completed",
                    details={"duration_ms": duration_ms}
                )
                
                log_api_response(
                    lambda_logger,
                    self.method,
                    self.path or event.get('path', 'unknown'),
                    result.get('statusCode', 200),
                    duration_ms,
                    request_id=request_id,
                    tenant_id=auth_context.tenant_id if auth_context else None,
                    user_id=auth_context.user_id if auth_context else None
                )
                
                return result
                
            except Exception as e:
                # 8. Handle all errors consistently
                duration_ms = (time.time() - start_time) * 1000
                
                # Log business operation failure
                log_business_operation(
                    lambda_logger,
                    self.operation_name,
                    "request",
                    tenant_id=auth_context.tenant_id if auth_context else None,
                    user_id=auth_context.user_id if auth_context else None,
                    status="failed",
                    details={
                        "duration_ms": duration_ms,
                        "error": str(e),
                        "error_type": type(e).__name__
                    }
                )
                
                # Use standardized error handling
                error_response = self.error_handler.handle_exception(
                    exception=e,
                    request_id=request_id,
                    method=self.method,
                    path=self.path or event.get('path', 'unknown'),
                    context={
                        'service': self.service_name,
                        'operation': self.operation_name,
                        'tenant_id': auth_context.tenant_id if auth_context else None,
                        'user_id': auth_context.user_id if auth_context else None,
                        'duration_ms': duration_ms
                    }
                )
                
                return error_response
        
        return wrapper


def business_logic_handler(service_name: str,
                          operation_name: str,
                          method: str = "POST",
                          path: str = None,
                          require_auth: bool = True,
                          required_roles: Optional[List[str]] = None,
                          body_validator: Optional[Type] = None,
                          path_params: Optional[List[str]] = None,
                          query_params: Optional[List[str]] = None,
                          required_headers: Optional[List[str]] = None):
    """
    Decorator for standardized business logic handling.
    
    Combines:
    - Request/response logging
    - Authentication and authorization
    - Request validation
    - Error handling
    - Performance monitoring
    
    Args:
        service_name: Name of the service
        operation_name: Name of the operation
        method: HTTP method (default: POST)
        path: API path
        require_auth: Whether authentication is required (default: True)
        required_roles: List of required roles for authorization
        body_validator: Validator class for request body
        path_params: List of required path parameters
        query_params: List of allowed query parameters
        required_headers: List of required headers
    
    Example:
        @business_logic_handler(
            service_name="auth",
            operation_name="login",
            method="POST",
            path="/auth/login",
            body_validator=LoginRequestValidator
        )
        def login_handler(event, context):
            # Business logic here
            validated_data = event['validated_body']
            return APIResponse.success(data=result)
    """
    return BusinessLogicHandler(
        service_name=service_name,
        operation_name=operation_name,
        method=method,
        path=path,
        require_auth=require_auth,
        required_roles=required_roles,
        body_validator=body_validator,
        path_params=path_params,
        query_params=query_params,
        required_headers=required_headers
    )


# Convenience decorators for common patterns
def authenticated_handler(service_name: str, operation_name: str, **kwargs):
    """Decorator for handlers that require authentication."""
    return business_logic_handler(
        service_name=service_name,
        operation_name=operation_name,
        require_auth=True,
        **kwargs
    )


def public_handler(service_name: str, operation_name: str, **kwargs):
    """Decorator for public handlers that don't require authentication."""
    return business_logic_handler(
        service_name=service_name,
        operation_name=operation_name,
        require_auth=False,
        **kwargs
    )


def admin_handler(service_name: str, operation_name: str, **kwargs):
    """Decorator for handlers that require admin permissions."""
    return business_logic_handler(
        service_name=service_name,
        operation_name=operation_name,
        require_auth=True,
        required_roles=["MASTER"],
        **kwargs
    )


# Simple decorator for Auth Service compatibility
def auth_handler(operation_name: str):
    """Simple decorator for Auth Service handlers."""
    def decorator(func):
        @wraps(func)
        def wrapper(event, context):
            start_time = time.time()

            try:
                lambda_logger.info(f"Starting {operation_name}", extra={
                    'operation': operation_name,
                    'request_id': context.aws_request_id if context else 'unknown'
                })

                # Call the actual handler
                result = func(event, context)

                # Log success
                duration = time.time() - start_time
                lambda_logger.info(f"Completed {operation_name}", extra={
                    'operation': operation_name,
                    'duration_ms': round(duration * 1000, 2),
                    'status_code': result.get('statusCode', 200)
                })

                return result

            except ValueError as e:
                lambda_logger.error(f"Validation error in {operation_name}: {str(e)}")
                return auth_error_response(str(e), 'VALIDATION_ERROR', 400)

            except Exception as e:
                lambda_logger.error(f"Error in {operation_name}: {str(e)}")
                return auth_error_response('Internal server error', 'INTERNAL_ERROR', 500)

        return wrapper
    return decorator
