# shared/python/shared/models/message.py
# Unified message model for all services

"""
Unified message model that supports all types of messages:
- Chat messages between users
- Messages to/from agents
- System messages
- File attachments
"""

from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timezone
import uuid


class MessageType(Enum):
    """Types of messages supported by the platform."""
    TEXT = "text"
    IMAGE = "image"
    DOCUMENT = "document"
    AUDIO = "audio"
    VIDEO = "video"
    SYSTEM = "system"
    TYPING_INDICATOR = "typing_indicator"
    PRESENCE_UPDATE = "presence_update"


class MessageDirection(Enum):
    """Direction of the message."""
    INBOUND = "inbound"    # Message coming into the system (from user/agent)
    OUTBOUND = "outbound"  # Message going out of the system (to user/agent)
    INTERNAL = "internal"  # Internal system message


class MessageStatus(Enum):
    """Status of message delivery/processing."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"
    PROCESSING = "processing"


class MessagePriority(Enum):
    """Priority levels for messages."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class MessageAttachment:
    """Represents a file attachment in a message."""
    attachment_id: str
    filename: str
    content_type: str
    size_bytes: int
    url: Optional[str] = None
    s3_key: Optional[str] = None
    thumbnail_url: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'attachment_id': self.attachment_id,
            'filename': self.filename,
            'content_type': self.content_type,
            'size_bytes': self.size_bytes,
            'url': self.url,
            's3_key': self.s3_key,
            'thumbnail_url': self.thumbnail_url,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageAttachment':
        """Create from dictionary."""
        return cls(
            attachment_id=data['attachment_id'],
            filename=data['filename'],
            content_type=data['content_type'],
            size_bytes=data['size_bytes'],
            url=data.get('url'),
            s3_key=data.get('s3_key'),
            thumbnail_url=data.get('thumbnail_url'),
            metadata=data.get('metadata', {})
        )


@dataclass
class MessageReaction:
    """Represents a reaction to a message."""
    user_id: str
    reaction: str  # emoji or reaction type
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'user_id': self.user_id,
            'reaction': self.reaction,
            'timestamp': self.timestamp.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageReaction':
        """Create from dictionary."""
        return cls(
            user_id=data['user_id'],
            reaction=data['reaction'],
            timestamp=datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
        )


@dataclass
class Message:
    """
    Unified message model for all types of messages.
    
    This model supports:
    - Chat messages between users
    - Messages to/from agents
    - System messages
    - File attachments
    - Message reactions
    """
    message_id: str
    conversation_id: str
    tenant_id: str
    sender_id: str
    sender_type: str  # user, agent, system
    sender_name: str
    message_type: MessageType
    content: str
    direction: MessageDirection = MessageDirection.INBOUND
    status: MessageStatus = MessageStatus.PENDING
    priority: MessagePriority = MessagePriority.NORMAL
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    attachments: List[MessageAttachment] = field(default_factory=list)
    reactions: List[MessageReaction] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Reply/thread support
    reply_to_message_id: Optional[str] = None
    thread_id: Optional[str] = None
    
    # Agent-specific fields
    agent_id: Optional[str] = None
    agent_type: Optional[str] = None
    webhook_url: Optional[str] = None
    
    # Processing fields
    processing_started_at: Optional[datetime] = None
    processing_completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    
    def __post_init__(self):
        """Post-initialization validation."""
        if not self.message_id:
            self.message_id = str(uuid.uuid4())
        
        # Set thread_id to message_id if this is not a reply
        if not self.reply_to_message_id and not self.thread_id:
            self.thread_id = self.message_id
    
    def add_attachment(self, attachment: MessageAttachment) -> None:
        """Add an attachment to the message."""
        self.attachments.append(attachment)
        self.updated_at = datetime.now(timezone.utc)
    
    def add_reaction(self, user_id: str, reaction: str) -> None:
        """Add a reaction to the message."""
        # Remove existing reaction from this user
        self.reactions = [r for r in self.reactions if r.user_id != user_id]
        
        # Add new reaction
        self.reactions.append(MessageReaction(user_id=user_id, reaction=reaction))
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_reaction(self, user_id: str) -> bool:
        """Remove a reaction from the message."""
        original_count = len(self.reactions)
        self.reactions = [r for r in self.reactions if r.user_id != user_id]
        
        if len(self.reactions) < original_count:
            self.updated_at = datetime.now(timezone.utc)
            return True
        return False
    
    def mark_as_sent(self, timestamp: Optional[datetime] = None) -> None:
        """Mark message as sent."""
        self.status = MessageStatus.SENT
        self.sent_at = timestamp or datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def mark_as_delivered(self, timestamp: Optional[datetime] = None) -> None:
        """Mark message as delivered."""
        self.status = MessageStatus.DELIVERED
        self.delivered_at = timestamp or datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def mark_as_read(self, timestamp: Optional[datetime] = None) -> None:
        """Mark message as read."""
        self.status = MessageStatus.READ
        self.read_at = timestamp or datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark message as failed."""
        self.status = MessageStatus.FAILED
        self.error_message = error_message
        self.updated_at = datetime.now(timezone.utc)
    
    def start_processing(self) -> None:
        """Mark message processing as started."""
        self.status = MessageStatus.PROCESSING
        self.processing_started_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def complete_processing(self) -> None:
        """Mark message processing as completed."""
        self.processing_completed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def increment_retry(self) -> None:
        """Increment retry count."""
        self.retry_count += 1
        self.updated_at = datetime.now(timezone.utc)
    
    def is_from_user(self) -> bool:
        """Check if message is from a user."""
        return self.sender_type == "user"
    
    def is_from_agent(self) -> bool:
        """Check if message is from an agent."""
        return self.sender_type == "agent"
    
    def is_system_message(self) -> bool:
        """Check if message is a system message."""
        return self.sender_type == "system"
    
    def has_attachments(self) -> bool:
        """Check if message has attachments."""
        return len(self.attachments) > 0
    
    def get_content_preview(self, max_length: int = 100) -> str:
        """Get a preview of the message content."""
        if len(self.content) <= max_length:
            return self.content
        return self.content[:max_length] + "..."
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization."""
        return {
            'message_id': self.message_id,
            'conversation_id': self.conversation_id,
            'tenant_id': self.tenant_id,
            'sender_id': self.sender_id,
            'sender_type': self.sender_type,
            'sender_name': self.sender_name,
            'message_type': self.message_type.value,
            'content': self.content,
            'direction': self.direction.value,
            'status': self.status.value,
            'priority': self.priority.value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'delivered_at': self.delivered_at.isoformat() if self.delivered_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'attachments': [a.to_dict() for a in self.attachments],
            'reactions': [r.to_dict() for r in self.reactions],
            'metadata': self.metadata,
            'reply_to_message_id': self.reply_to_message_id,
            'thread_id': self.thread_id,
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'webhook_url': self.webhook_url,
            'processing_started_at': self.processing_started_at.isoformat() if self.processing_started_at else None,
            'processing_completed_at': self.processing_completed_at.isoformat() if self.processing_completed_at else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create from dictionary."""
        attachments = [
            MessageAttachment.from_dict(a) for a in data.get('attachments', [])
        ]
        
        reactions = [
            MessageReaction.from_dict(r) for r in data.get('reactions', [])
        ]
        
        return cls(
            message_id=data['message_id'],
            conversation_id=data['conversation_id'],
            tenant_id=data['tenant_id'],
            sender_id=data['sender_id'],
            sender_type=data['sender_type'],
            sender_name=data['sender_name'],
            message_type=MessageType(data['message_type']),
            content=data['content'],
            direction=MessageDirection(data.get('direction', 'inbound')),
            status=MessageStatus(data.get('status', 'pending')),
            priority=MessagePriority(data.get('priority', 'normal')),
            created_at=datetime.fromisoformat(data['created_at'].replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00')),
            sent_at=datetime.fromisoformat(data['sent_at'].replace('Z', '+00:00')) if data.get('sent_at') else None,
            delivered_at=datetime.fromisoformat(data['delivered_at'].replace('Z', '+00:00')) if data.get('delivered_at') else None,
            read_at=datetime.fromisoformat(data['read_at'].replace('Z', '+00:00')) if data.get('read_at') else None,
            attachments=attachments,
            reactions=reactions,
            metadata=data.get('metadata', {}),
            reply_to_message_id=data.get('reply_to_message_id'),
            thread_id=data.get('thread_id'),
            agent_id=data.get('agent_id'),
            agent_type=data.get('agent_type'),
            webhook_url=data.get('webhook_url'),
            processing_started_at=datetime.fromisoformat(data['processing_started_at'].replace('Z', '+00:00')) if data.get('processing_started_at') else None,
            processing_completed_at=datetime.fromisoformat(data['processing_completed_at'].replace('Z', '+00:00')) if data.get('processing_completed_at') else None,
            error_message=data.get('error_message'),
            retry_count=data.get('retry_count', 0)
        )
    
    @classmethod
    def create_user_message(
        cls,
        conversation_id: str,
        tenant_id: str,
        user_id: str,
        user_name: str,
        content: str,
        message_type: MessageType = MessageType.TEXT,
        reply_to_message_id: Optional[str] = None
    ) -> 'Message':
        """Create a message from a user."""
        return cls(
            message_id=str(uuid.uuid4()),
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            sender_id=user_id,
            sender_type="user",
            sender_name=user_name,
            message_type=message_type,
            content=content,
            direction=MessageDirection.INBOUND,
            reply_to_message_id=reply_to_message_id
        )
    
    @classmethod
    def create_agent_message(
        cls,
        conversation_id: str,
        tenant_id: str,
        agent_id: str,
        agent_name: str,
        agent_type: str,
        content: str,
        message_type: MessageType = MessageType.TEXT,
        reply_to_message_id: Optional[str] = None
    ) -> 'Message':
        """Create a message from an agent."""
        return cls(
            message_id=str(uuid.uuid4()),
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            sender_id=agent_id,
            sender_type="agent",
            sender_name=agent_name,
            message_type=message_type,
            content=content,
            direction=MessageDirection.OUTBOUND,
            agent_id=agent_id,
            agent_type=agent_type,
            reply_to_message_id=reply_to_message_id
        )
    
    @classmethod
    def create_system_message(
        cls,
        conversation_id: str,
        tenant_id: str,
        content: str,
        message_type: MessageType = MessageType.SYSTEM
    ) -> 'Message':
        """Create a system message."""
        return cls(
            message_id=str(uuid.uuid4()),
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            sender_id="system",
            sender_type="system",
            sender_name="System",
            message_type=message_type,
            content=content,
            direction=MessageDirection.INTERNAL
        )


# Convenience functions for creating messages
def create_chat_message(conversation_id: str, tenant_id: str, user_id: str, 
                       user_name: str, content: str) -> Message:
    """Create a chat message between users."""
    return Message.create_user_message(
        conversation_id, tenant_id, user_id, user_name, content
    )


def create_agent_request_message(conversation_id: str, tenant_id: str, user_id: str,
                               user_name: str, content: str, agent_id: str) -> Message:
    """Create a message from user to agent."""
    message = Message.create_user_message(
        conversation_id, tenant_id, user_id, user_name, content
    )
    message.agent_id = agent_id
    message.direction = MessageDirection.OUTBOUND  # Going to agent
    return message


def create_agent_response_message(conversation_id: str, tenant_id: str, agent_id: str,
                                agent_name: str, agent_type: str, content: str,
                                reply_to_message_id: Optional[str] = None) -> Message:
    """Create a response message from agent."""
    return Message.create_agent_message(
        conversation_id, tenant_id, agent_id, agent_name, agent_type, 
        content, reply_to_message_id=reply_to_message_id
    )
