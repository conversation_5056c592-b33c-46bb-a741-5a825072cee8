# shared/python/shared/models/conversation.py
# Unified conversation model for all services

"""
Unified conversation model that supports all types of conversations:
- User-to-User chat
- User-to-Agent communication
- Group conversations (future)
"""

from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timezone
import uuid


class ConversationType(Enum):
    """Types of conversations supported by the platform."""
    USER_TO_USER = "user_to_user"           # Direct chat between users
    USER_TO_AGENT = "user_to_agent"         # Communication with AI agents
    GROUP_CHAT = "group_chat"               # Group conversations (future)
    SUPPORT_TICKET = "support_ticket"       # Support conversations (future)


class ConversationStatus(Enum):
    """Status of conversations."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"
    CLOSED = "closed"


class ParticipantType(Enum):
    """Types of conversation participants."""
    USER = "user"
    AGENT = "agent"
    SYSTEM = "system"


@dataclass
class ConversationParticipant:
    """Represents a participant in a conversation."""
    participant_id: str
    participant_type: ParticipantType
    tenant_id: str
    display_name: str
    role: str = "member"  # member, admin, moderator
    joined_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_seen_at: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'participant_id': self.participant_id,
            'participant_type': self.participant_type.value,
            'tenant_id': self.tenant_id,
            'display_name': self.display_name,
            'role': self.role,
            'joined_at': self.joined_at.isoformat(),
            'last_seen_at': self.last_seen_at.isoformat() if self.last_seen_at else None,
            'is_active': self.is_active,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationParticipant':
        """Create from dictionary."""
        return cls(
            participant_id=data['participant_id'],
            participant_type=ParticipantType(data['participant_type']),
            tenant_id=data['tenant_id'],
            display_name=data['display_name'],
            role=data.get('role', 'member'),
            joined_at=datetime.fromisoformat(data['joined_at'].replace('Z', '+00:00')),
            last_seen_at=datetime.fromisoformat(data['last_seen_at'].replace('Z', '+00:00')) if data.get('last_seen_at') else None,
            is_active=data.get('is_active', True),
            metadata=data.get('metadata', {})
        )


@dataclass
class Conversation:
    """
    Unified conversation model for all types of conversations.
    
    This model supports:
    - User-to-User chat
    - User-to-Agent communication
    - Group conversations
    - Support tickets
    """
    conversation_id: str
    conversation_type: ConversationType
    tenant_id: str
    title: Optional[str] = None
    description: Optional[str] = None
    status: ConversationStatus = ConversationStatus.ACTIVE
    participants: List[ConversationParticipant] = field(default_factory=list)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_message_at: Optional[datetime] = None
    message_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Agent-specific fields (only used for USER_TO_AGENT conversations)
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None
    agent_type: Optional[str] = None  # feedo, forecaster, etc.
    
    # Chat-specific fields (only used for USER_TO_USER conversations)
    is_direct_message: bool = True
    
    def __post_init__(self):
        """Post-initialization validation."""
        if not self.conversation_id:
            self.conversation_id = str(uuid.uuid4())
        
        # Validate agent fields for agent conversations
        if self.conversation_type == ConversationType.USER_TO_AGENT:
            if not self.agent_id:
                raise ValueError("agent_id is required for USER_TO_AGENT conversations")
    
    def add_participant(self, participant: ConversationParticipant) -> None:
        """Add a participant to the conversation."""
        # Check if participant already exists
        existing = self.get_participant(participant.participant_id)
        if existing:
            # Update existing participant
            existing.is_active = True
            existing.joined_at = datetime.now(timezone.utc)
        else:
            self.participants.append(participant)
        
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_participant(self, participant_id: str) -> bool:
        """Remove a participant from the conversation."""
        for participant in self.participants:
            if participant.participant_id == participant_id:
                participant.is_active = False
                self.updated_at = datetime.now(timezone.utc)
                return True
        return False
    
    def get_participant(self, participant_id: str) -> Optional[ConversationParticipant]:
        """Get a specific participant."""
        for participant in self.participants:
            if participant.participant_id == participant_id:
                return participant
        return None
    
    def get_active_participants(self) -> List[ConversationParticipant]:
        """Get all active participants."""
        return [p for p in self.participants if p.is_active]
    
    def get_user_participants(self) -> List[ConversationParticipant]:
        """Get all user participants."""
        return [p for p in self.participants if p.participant_type == ParticipantType.USER and p.is_active]
    
    def get_agent_participants(self) -> List[ConversationParticipant]:
        """Get all agent participants."""
        return [p for p in self.participants if p.participant_type == ParticipantType.AGENT and p.is_active]
    
    def is_participant(self, participant_id: str) -> bool:
        """Check if someone is an active participant."""
        participant = self.get_participant(participant_id)
        return participant is not None and participant.is_active
    
    def update_last_message(self, timestamp: Optional[datetime] = None) -> None:
        """Update last message timestamp and increment message count."""
        self.last_message_at = timestamp or datetime.now(timezone.utc)
        self.message_count += 1
        self.updated_at = datetime.now(timezone.utc)
    
    def generate_title(self) -> str:
        """Generate a title for the conversation based on type and participants."""
        if self.title:
            return self.title
        
        if self.conversation_type == ConversationType.USER_TO_AGENT:
            return f"Conversation with {self.agent_name or 'Agent'}"
        elif self.conversation_type == ConversationType.USER_TO_USER:
            user_participants = self.get_user_participants()
            if len(user_participants) == 2:
                return f"Chat between {user_participants[0].display_name} and {user_participants[1].display_name}"
            else:
                return f"Chat with {len(user_participants)} participants"
        else:
            return f"{self.conversation_type.value.replace('_', ' ').title()} Conversation"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization."""
        return {
            'conversation_id': self.conversation_id,
            'conversation_type': self.conversation_type.value,
            'tenant_id': self.tenant_id,
            'title': self.title or self.generate_title(),
            'description': self.description,
            'status': self.status.value,
            'participants': [p.to_dict() for p in self.participants],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_message_at': self.last_message_at.isoformat() if self.last_message_at else None,
            'message_count': self.message_count,
            'metadata': self.metadata,
            'agent_id': self.agent_id,
            'agent_name': self.agent_name,
            'agent_type': self.agent_type,
            'is_direct_message': self.is_direct_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create from dictionary."""
        participants = [
            ConversationParticipant.from_dict(p) for p in data.get('participants', [])
        ]
        
        return cls(
            conversation_id=data['conversation_id'],
            conversation_type=ConversationType(data['conversation_type']),
            tenant_id=data['tenant_id'],
            title=data.get('title'),
            description=data.get('description'),
            status=ConversationStatus(data.get('status', 'active')),
            participants=participants,
            created_at=datetime.fromisoformat(data['created_at'].replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00')),
            last_message_at=datetime.fromisoformat(data['last_message_at'].replace('Z', '+00:00')) if data.get('last_message_at') else None,
            message_count=data.get('message_count', 0),
            metadata=data.get('metadata', {}),
            agent_id=data.get('agent_id'),
            agent_name=data.get('agent_name'),
            agent_type=data.get('agent_type'),
            is_direct_message=data.get('is_direct_message', True)
        )
    
    @classmethod
    def create_user_to_user_conversation(
        cls,
        tenant_id: str,
        user1_id: str,
        user1_name: str,
        user2_id: str,
        user2_name: str,
        title: Optional[str] = None
    ) -> 'Conversation':
        """Create a user-to-user conversation."""
        conversation = cls(
            conversation_id=str(uuid.uuid4()),
            conversation_type=ConversationType.USER_TO_USER,
            tenant_id=tenant_id,
            title=title,
            is_direct_message=True
        )
        
        # Add participants
        conversation.add_participant(ConversationParticipant(
            participant_id=user1_id,
            participant_type=ParticipantType.USER,
            tenant_id=tenant_id,
            display_name=user1_name
        ))
        
        conversation.add_participant(ConversationParticipant(
            participant_id=user2_id,
            participant_type=ParticipantType.USER,
            tenant_id=tenant_id,
            display_name=user2_name
        ))
        
        return conversation
    
    @classmethod
    def create_user_to_agent_conversation(
        cls,
        tenant_id: str,
        user_id: str,
        user_name: str,
        agent_id: str,
        agent_name: str,
        agent_type: str,
        title: Optional[str] = None
    ) -> 'Conversation':
        """Create a user-to-agent conversation."""
        conversation = cls(
            conversation_id=str(uuid.uuid4()),
            conversation_type=ConversationType.USER_TO_AGENT,
            tenant_id=tenant_id,
            title=title,
            agent_id=agent_id,
            agent_name=agent_name,
            agent_type=agent_type
        )
        
        # Add user participant
        conversation.add_participant(ConversationParticipant(
            participant_id=user_id,
            participant_type=ParticipantType.USER,
            tenant_id=tenant_id,
            display_name=user_name
        ))
        
        # Add agent participant
        conversation.add_participant(ConversationParticipant(
            participant_id=agent_id,
            participant_type=ParticipantType.AGENT,
            tenant_id=tenant_id,
            display_name=agent_name,
            metadata={'agent_type': agent_type}
        ))
        
        return conversation


# Convenience functions for creating conversations
def create_chat_conversation(tenant_id: str, user1_id: str, user1_name: str, 
                           user2_id: str, user2_name: str) -> Conversation:
    """Create a user-to-user chat conversation."""
    return Conversation.create_user_to_user_conversation(
        tenant_id, user1_id, user1_name, user2_id, user2_name
    )


def create_agent_conversation(tenant_id: str, user_id: str, user_name: str,
                            agent_id: str, agent_name: str, agent_type: str) -> Conversation:
    """Create a user-to-agent conversation."""
    return Conversation.create_user_to_agent_conversation(
        tenant_id, user_id, user_name, agent_id, agent_name, agent_type
    )
