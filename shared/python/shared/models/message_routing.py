# shared/python/shared/models/message_routing.py
# Message routing models and logic

"""
Message routing models that define how messages are routed
between different services and handlers.
"""

from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timezone
import uuid

from .conversation import ConversationType
from .message import MessageType, MessagePriority


class RoutingDestination(Enum):
    """Possible destinations for message routing."""
    CHAT_SERVICE = "chat_service"           # User-to-user chat
    AGENT_SERVICE = "agent_service"         # User-to-agent communication
    WEBSOCKET_SERVICE = "websocket_service" # Real-time notifications
    SYSTEM_SERVICE = "system_service"       # System messages
    EXTERNAL_WEBHOOK = "external_webhook"   # External integrations


class RoutingStrategy(Enum):
    """Strategies for routing messages."""
    DIRECT = "direct"                       # Direct routing to specific service
    BROADCAST = "broadcast"                 # Broadcast to multiple services
    CONDITIONAL = "conditional"             # Route based on conditions
    QUEUE = "queue"                        # Queue for later processing
    HYBRID = "hybrid"                      # Combination of strategies


class RoutingPriority(Enum):
    """Priority levels for routing."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


@dataclass
class RoutingRule:
    """Defines a rule for message routing."""
    rule_id: str
    name: str
    description: str
    conditions: Dict[str, Any]
    destination: RoutingDestination
    strategy: RoutingStrategy
    priority: RoutingPriority = RoutingPriority.NORMAL
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def matches(self, context: 'RoutingContext') -> bool:
        """Check if this rule matches the routing context."""
        try:
            # Check conversation type
            if 'conversation_type' in self.conditions:
                expected_type = self.conditions['conversation_type']
                if context.conversation_type.value != expected_type:
                    return False
            
            # Check message type
            if 'message_type' in self.conditions:
                expected_types = self.conditions['message_type']
                if isinstance(expected_types, str):
                    expected_types = [expected_types]
                if context.message_type.value not in expected_types:
                    return False
            
            # Check sender type
            if 'sender_type' in self.conditions:
                expected_sender = self.conditions['sender_type']
                if context.sender_type != expected_sender:
                    return False
            
            # Check content keywords
            if 'content_keywords' in self.conditions:
                keywords = self.conditions['content_keywords']
                content_lower = context.message_content.lower()
                if not any(keyword.lower() in content_lower for keyword in keywords):
                    return False
            
            # Check agent type (for agent conversations)
            if 'agent_type' in self.conditions:
                expected_agent_type = self.conditions['agent_type']
                if context.agent_type != expected_agent_type:
                    return False
            
            # Check tenant-specific rules
            if 'tenant_rules' in self.conditions:
                tenant_rules = self.conditions['tenant_rules']
                if context.tenant_id in tenant_rules:
                    tenant_rule = tenant_rules[context.tenant_id]
                    # Apply tenant-specific conditions
                    if not self._check_tenant_conditions(tenant_rule, context):
                        return False
            
            return True
            
        except Exception:
            return False
    
    def _check_tenant_conditions(self, tenant_rule: Dict[str, Any], context: 'RoutingContext') -> bool:
        """Check tenant-specific conditions."""
        # This can be extended for tenant-specific routing logic
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'rule_id': self.rule_id,
            'name': self.name,
            'description': self.description,
            'conditions': self.conditions,
            'destination': self.destination.value,
            'strategy': self.strategy.value,
            'priority': self.priority.value,
            'is_active': self.is_active,
            'metadata': self.metadata
        }


@dataclass
class RoutingContext:
    """Context information for message routing decisions."""
    message_id: str
    conversation_id: str
    conversation_type: ConversationType
    tenant_id: str
    user_id: str
    sender_type: str
    message_type: MessageType
    message_content: str
    message_priority: MessagePriority = MessagePriority.NORMAL
    agent_id: Optional[str] = None
    agent_type: Optional[str] = None
    recipient_id: Optional[str] = None
    is_reply: bool = False
    has_attachments: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'message_id': self.message_id,
            'conversation_id': self.conversation_id,
            'conversation_type': self.conversation_type.value,
            'tenant_id': self.tenant_id,
            'user_id': self.user_id,
            'sender_type': self.sender_type,
            'message_type': self.message_type.value,
            'message_content': self.message_content,
            'message_priority': self.message_priority.value,
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'recipient_id': self.recipient_id,
            'is_reply': self.is_reply,
            'has_attachments': self.has_attachments,
            'metadata': self.metadata
        }


@dataclass
class RoutingDecision:
    """Result of message routing decision."""
    decision_id: str
    context: RoutingContext
    primary_destination: RoutingDestination
    secondary_destinations: List[RoutingDestination] = field(default_factory=list)
    strategy: RoutingStrategy = RoutingStrategy.DIRECT
    priority: RoutingPriority = RoutingPriority.NORMAL
    routing_rules_applied: List[str] = field(default_factory=list)
    estimated_processing_time: Optional[float] = None
    requires_response: bool = False
    webhook_url: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'decision_id': self.decision_id,
            'context': self.context.to_dict(),
            'primary_destination': self.primary_destination.value,
            'secondary_destinations': [d.value for d in self.secondary_destinations],
            'strategy': self.strategy.value,
            'priority': self.priority.value,
            'routing_rules_applied': self.routing_rules_applied,
            'estimated_processing_time': self.estimated_processing_time,
            'requires_response': self.requires_response,
            'webhook_url': self.webhook_url,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat()
        }


class MessageRouter:
    """Core message routing logic."""
    
    def __init__(self):
        self.routing_rules = self._initialize_default_rules()
    
    def route_message(self, context: RoutingContext) -> RoutingDecision:
        """Route a message based on context and rules."""
        decision_id = str(uuid.uuid4())
        
        # Find matching rules
        matching_rules = [
            rule for rule in self.routing_rules 
            if rule.is_active and rule.matches(context)
        ]
        
        # Sort by priority
        matching_rules.sort(key=lambda r: self._get_priority_weight(r.priority), reverse=True)
        
        if not matching_rules:
            # Default routing
            return self._create_default_routing_decision(decision_id, context)
        
        # Apply the highest priority rule
        primary_rule = matching_rules[0]
        
        # Create routing decision
        decision = RoutingDecision(
            decision_id=decision_id,
            context=context,
            primary_destination=primary_rule.destination,
            strategy=primary_rule.strategy,
            priority=primary_rule.priority,
            routing_rules_applied=[rule.rule_id for rule in matching_rules]
        )
        
        # Add secondary destinations for broadcast strategy
        if primary_rule.strategy == RoutingStrategy.BROADCAST:
            decision.secondary_destinations = self._get_broadcast_destinations(context)
        
        # Set webhook URL for external destinations
        if primary_rule.destination == RoutingDestination.EXTERNAL_WEBHOOK:
            decision.webhook_url = self._get_webhook_url(context)
        
        # Determine if response is required
        decision.requires_response = self._requires_response(context, primary_rule)
        
        return decision
    
    def _initialize_default_rules(self) -> List[RoutingRule]:
        """Initialize default routing rules."""
        return [
            # User-to-user chat messages
            RoutingRule(
                rule_id="user_chat_rule",
                name="User Chat Routing",
                description="Route user-to-user chat messages to chat service",
                conditions={
                    'conversation_type': 'user_to_user',
                    'sender_type': 'user'
                },
                destination=RoutingDestination.CHAT_SERVICE,
                strategy=RoutingStrategy.DIRECT,
                priority=RoutingPriority.NORMAL
            ),
            
            # User-to-agent messages
            RoutingRule(
                rule_id="user_agent_rule",
                name="User to Agent Routing",
                description="Route user messages to agents via agent service",
                conditions={
                    'conversation_type': 'user_to_agent',
                    'sender_type': 'user'
                },
                destination=RoutingDestination.AGENT_SERVICE,
                strategy=RoutingStrategy.DIRECT,
                priority=RoutingPriority.NORMAL
            ),
            
            # Agent responses
            RoutingRule(
                rule_id="agent_response_rule",
                name="Agent Response Routing",
                description="Route agent responses back to users",
                conditions={
                    'sender_type': 'agent'
                },
                destination=RoutingDestination.CHAT_SERVICE,
                strategy=RoutingStrategy.BROADCAST,  # Also notify via WebSocket
                priority=RoutingPriority.HIGH
            ),
            
            # Real-time notifications
            RoutingRule(
                rule_id="realtime_notification_rule",
                name="Real-time Notification Routing",
                description="Route typing indicators and presence updates to WebSocket",
                conditions={
                    'message_type': ['typing_indicator', 'presence_update']
                },
                destination=RoutingDestination.WEBSOCKET_SERVICE,
                strategy=RoutingStrategy.DIRECT,
                priority=RoutingPriority.HIGH
            ),
            
            # System messages
            RoutingRule(
                rule_id="system_message_rule",
                name="System Message Routing",
                description="Route system messages appropriately",
                conditions={
                    'sender_type': 'system'
                },
                destination=RoutingDestination.SYSTEM_SERVICE,
                strategy=RoutingStrategy.DIRECT,
                priority=RoutingPriority.LOW
            ),
            
            # Urgent messages
            RoutingRule(
                rule_id="urgent_message_rule",
                name="Urgent Message Routing",
                description="Route urgent messages with high priority",
                conditions={
                    'message_priority': 'urgent'
                },
                destination=RoutingDestination.CHAT_SERVICE,
                strategy=RoutingStrategy.BROADCAST,
                priority=RoutingPriority.URGENT
            )
        ]
    
    def _create_default_routing_decision(self, decision_id: str, context: RoutingContext) -> RoutingDecision:
        """Create default routing decision when no rules match."""
        # Default to chat service for most messages
        destination = RoutingDestination.CHAT_SERVICE
        
        # Override for specific conversation types
        if context.conversation_type == ConversationType.USER_TO_AGENT:
            destination = RoutingDestination.AGENT_SERVICE
        
        return RoutingDecision(
            decision_id=decision_id,
            context=context,
            primary_destination=destination,
            strategy=RoutingStrategy.DIRECT,
            priority=RoutingPriority.NORMAL,
            routing_rules_applied=["default_rule"]
        )
    
    def _get_priority_weight(self, priority: RoutingPriority) -> int:
        """Get numeric weight for priority comparison."""
        weights = {
            RoutingPriority.LOW: 1,
            RoutingPriority.NORMAL: 2,
            RoutingPriority.HIGH: 3,
            RoutingPriority.URGENT: 4,
            RoutingPriority.CRITICAL: 5
        }
        return weights.get(priority, 2)
    
    def _get_broadcast_destinations(self, context: RoutingContext) -> List[RoutingDestination]:
        """Get secondary destinations for broadcast strategy."""
        destinations = [RoutingDestination.WEBSOCKET_SERVICE]
        
        # Add additional destinations based on context
        if context.conversation_type == ConversationType.USER_TO_AGENT:
            destinations.append(RoutingDestination.AGENT_SERVICE)
        
        return destinations
    
    def _get_webhook_url(self, context: RoutingContext) -> Optional[str]:
        """Get webhook URL for external routing."""
        # This would typically look up the webhook URL from agent configuration
        # For now, return None
        return None
    
    def _requires_response(self, context: RoutingContext, rule: RoutingRule) -> bool:
        """Determine if the message requires a response."""
        # Agent messages typically require responses
        if context.conversation_type == ConversationType.USER_TO_AGENT:
            return True
        
        # System messages typically don't require responses
        if context.sender_type == 'system':
            return False
        
        # Default to not requiring response for chat messages
        return False
    
    def add_routing_rule(self, rule: RoutingRule) -> None:
        """Add a new routing rule."""
        self.routing_rules.append(rule)
    
    def remove_routing_rule(self, rule_id: str) -> bool:
        """Remove a routing rule."""
        original_count = len(self.routing_rules)
        self.routing_rules = [r for r in self.routing_rules if r.rule_id != rule_id]
        return len(self.routing_rules) < original_count
    
    def get_routing_rules(self) -> List[RoutingRule]:
        """Get all routing rules."""
        return self.routing_rules.copy()


# Global router instance
message_router = MessageRouter()


# Convenience functions
def route_message(context: RoutingContext) -> RoutingDecision:
    """Route a message using the global router."""
    return message_router.route_message(context)


def create_routing_context_from_message(message_data: Dict[str, Any], 
                                      conversation_data: Dict[str, Any]) -> RoutingContext:
    """Create routing context from message and conversation data."""
    from .conversation import ConversationType
    from .message import MessageType, MessagePriority
    
    return RoutingContext(
        message_id=message_data['message_id'],
        conversation_id=message_data['conversation_id'],
        conversation_type=ConversationType(conversation_data['conversation_type']),
        tenant_id=message_data['tenant_id'],
        user_id=message_data['sender_id'],
        sender_type=message_data['sender_type'],
        message_type=MessageType(message_data['message_type']),
        message_content=message_data['content'],
        message_priority=MessagePriority(message_data.get('priority', 'normal')),
        agent_id=conversation_data.get('agent_id'),
        agent_type=conversation_data.get('agent_type'),
        is_reply=bool(message_data.get('reply_to_message_id')),
        has_attachments=len(message_data.get('attachments', [])) > 0,
        metadata=message_data.get('metadata', {})
    )
