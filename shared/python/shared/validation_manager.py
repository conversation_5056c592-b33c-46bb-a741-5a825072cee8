#!/usr/bin/env python3
# shared/python/shared/validation_manager.py
# Centralized validation management system

"""
Centralized validation management for consistent validation across all services.
Provides standardized validation patterns, decorators, and error handling.
"""

import json
from typing import Dict, Any, Optional, Type, Callable, List
from functools import wraps

from .logger import lambda_logger
from .exceptions import ValidationException
from .validators import ValidationResult


class ValidationManager:
    """Centralized validation management."""
    
    def __init__(self, service_name: str):
        """Initialize validation manager."""
        self.service_name = service_name
    
    def validate_request_body(self, 
                             event: Dict[str, Any], 
                             validator_class: Type,
                             required: bool = True) -> Dict[str, Any]:
        """Validate request body with standardized error handling."""
        
        # Extract body
        body_str = event.get('body', '{}')
        if not body_str and required:
            raise ValidationException(
                "Request body is required",
                error_code="MISSING_BODY"
            )
        
        # Parse JSON
        try:
            body = json.loads(body_str) if body_str else {}
        except json.JSONDecodeError as e:
            lambda_logger.warning("Invalid JSON in request body", extra={
                'service': self.service_name,
                'error': str(e),
                'body_preview': body_str[:100] if body_str else None
            })
            raise ValidationException(
                "Invalid JSON format in request body",
                error_code="INVALID_JSON"
            )
        
        # Validate using validator class
        if hasattr(validator_class, 'validate'):
            result = validator_class.validate(body)
        else:
            # Fallback for Pydantic-style validators
            try:
                validated_data = validator_class(**body)
                result = ValidationResult(
                    is_valid=True,
                    errors=[],
                    cleaned_data=validated_data.dict() if hasattr(validated_data, 'dict') else validated_data
                )
            except Exception as e:
                result = ValidationResult(
                    is_valid=False,
                    errors=[str(e)],
                    cleaned_data=None
                )
        
        # Handle validation result
        if not result.is_valid:
            lambda_logger.warning("Request validation failed", extra={
                'service': self.service_name,
                'errors': result.errors,
                'body_keys': list(body.keys()) if isinstance(body, dict) else None
            })
            
            raise ValidationException(
                "Request validation failed",
                error_code="VALIDATION_FAILED",
                details={
                    'validation_errors': [
                        {'field': 'request', 'message': error}
                        for error in result.errors
                    ]
                }
            )
        
        return result.cleaned_data
    
    def validate_path_parameters(self, 
                                event: Dict[str, Any], 
                                required_params: List[str],
                                validators: Optional[Dict[str, Callable]] = None) -> Dict[str, Any]:
        """Validate path parameters."""
        
        path_params = event.get('pathParameters') or {}
        validated_params = {}
        errors = []
        
        # Check required parameters
        for param in required_params:
            if param not in path_params or not path_params[param]:
                errors.append(f"Missing required path parameter: {param}")
                continue
            
            value = path_params[param]
            
            # Apply validator if provided
            if validators and param in validators:
                try:
                    validated_params[param] = validators[param](value)
                except Exception as e:
                    errors.append(f"Invalid {param}: {str(e)}")
            else:
                validated_params[param] = value
        
        if errors:
            lambda_logger.warning("Path parameter validation failed", extra={
                'service': self.service_name,
                'errors': errors,
                'provided_params': list(path_params.keys())
            })
            
            raise ValidationException(
                "Path parameter validation failed",
                error_code="INVALID_PATH_PARAMS",
                details={'validation_errors': errors}
            )
        
        return validated_params
    
    def validate_query_parameters(self, 
                                 event: Dict[str, Any], 
                                 allowed_params: List[str],
                                 validators: Optional[Dict[str, Callable]] = None) -> Dict[str, Any]:
        """Validate query parameters."""
        
        query_params = event.get('queryStringParameters') or {}
        validated_params = {}
        errors = []
        
        for param, value in query_params.items():
            if param not in allowed_params:
                errors.append(f"Unknown query parameter: {param}")
                continue
            
            # Apply validator if provided
            if validators and param in validators:
                try:
                    validated_params[param] = validators[param](value)
                except Exception as e:
                    errors.append(f"Invalid {param}: {str(e)}")
            else:
                validated_params[param] = value
        
        if errors:
            lambda_logger.warning("Query parameter validation failed", extra={
                'service': self.service_name,
                'errors': errors,
                'provided_params': list(query_params.keys())
            })
            
            raise ValidationException(
                "Query parameter validation failed",
                error_code="INVALID_QUERY_PARAMS",
                details={'validation_errors': errors}
            )
        
        return validated_params
    
    def validate_headers(self, 
                        event: Dict[str, Any], 
                        required_headers: List[str],
                        validators: Optional[Dict[str, Callable]] = None) -> Dict[str, Any]:
        """Validate request headers."""
        
        headers = event.get('headers') or {}
        # Convert to lowercase for case-insensitive comparison
        headers_lower = {k.lower(): v for k, v in headers.items()}
        
        validated_headers = {}
        errors = []
        
        for header in required_headers:
            header_lower = header.lower()
            if header_lower not in headers_lower:
                errors.append(f"Missing required header: {header}")
                continue
            
            value = headers_lower[header_lower]
            
            # Apply validator if provided
            if validators and header in validators:
                try:
                    validated_headers[header] = validators[header](value)
                except Exception as e:
                    errors.append(f"Invalid {header}: {str(e)}")
            else:
                validated_headers[header] = value
        
        if errors:
            lambda_logger.warning("Header validation failed", extra={
                'service': self.service_name,
                'errors': errors,
                'provided_headers': list(headers.keys())
            })
            
            raise ValidationException(
                "Header validation failed",
                error_code="INVALID_HEADERS",
                details={'validation_errors': errors}
            )
        
        return validated_headers


def with_validation(service_name: str, 
                   body_validator: Optional[Type] = None,
                   path_params: Optional[List[str]] = None,
                   query_params: Optional[List[str]] = None,
                   required_headers: Optional[List[str]] = None,
                   param_validators: Optional[Dict[str, Callable]] = None):
    """Decorator for standardized request validation."""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(event: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
            validation_manager = ValidationManager(service_name)
            
            try:
                # Validate request body
                if body_validator:
                    validated_body = validation_manager.validate_request_body(event, body_validator)
                    event['validated_body'] = validated_body
                
                # Validate path parameters
                if path_params:
                    validated_path = validation_manager.validate_path_parameters(
                        event, path_params, param_validators
                    )
                    event['validated_path'] = validated_path
                
                # Validate query parameters
                if query_params:
                    validated_query = validation_manager.validate_query_parameters(
                        event, query_params, param_validators
                    )
                    event['validated_query'] = validated_query
                
                # Validate headers
                if required_headers:
                    validated_headers = validation_manager.validate_headers(
                        event, required_headers, param_validators
                    )
                    event['validated_headers'] = validated_headers
                
                return func(event, context)
                
            except ValidationException:
                # Re-raise validation exceptions to be handled by error handler
                raise
            except Exception as e:
                lambda_logger.error("Unexpected error during validation", extra={
                    'service': service_name,
                    'error': str(e)
                })
                raise ValidationException(
                    "Validation system error",
                    error_code="VALIDATION_SYSTEM_ERROR"
                )
        
        return wrapper
    return decorator


def create_validation_manager(service_name: str) -> ValidationManager:
    """Create validation manager instance."""
    return ValidationManager(service_name)
