# shared/python/shared/dependency_injection.py
# Dependency injection container and decorators - Shared Layer

"""
Dependency injection system that provides IoC container functionality
for managing service dependencies and lifecycle.
"""

import inspect
from typing import Dict, Any, Type, TypeVar, Callable, Optional, Union
from functools import wraps
from enum import Enum

from .logger import lambda_logger
from .exceptions import PlatformException


T = TypeVar('T')


class ServiceLifetime(Enum):
    """Service lifetime options."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ServiceDescriptor:
    """Describes a service registration."""
    
    def __init__(
        self,
        service_type: Type,
        implementation_type: Optional[Type] = None,
        factory: Optional[Callable] = None,
        instance: Optional[Any] = None,
        lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
    ):
        self.service_type = service_type
        self.implementation_type = implementation_type
        self.factory = factory
        self.instance = instance
        self.lifetime = lifetime
        
        if not any([implementation_type, factory, instance]):
            raise PlatformException("Service descriptor must have implementation, factory, or instance")


class ServiceContainer:
    """Dependency injection container."""
    
    def __init__(self):
        """Initialize service container."""
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._scoped_instances: Dict[Type, Any] = {}
    
    def register_singleton(self, service_type: Type, implementation_type: Optional[Type] = None) -> 'ServiceContainer':
        """Register a singleton service."""
        impl_type = implementation_type or service_type
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            lifetime=ServiceLifetime.SINGLETON
        )
        self._services[service_type] = descriptor
        return self
    
    def register_transient(self, service_type: Type, implementation_type: Optional[Type] = None) -> 'ServiceContainer':
        """Register a transient service."""
        impl_type = implementation_type or service_type
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            lifetime=ServiceLifetime.TRANSIENT
        )
        self._services[service_type] = descriptor
        return self
    
    def register_scoped(self, service_type: Type, implementation_type: Optional[Type] = None) -> 'ServiceContainer':
        """Register a scoped service."""
        impl_type = implementation_type or service_type
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            lifetime=ServiceLifetime.SCOPED
        )
        self._services[service_type] = descriptor
        return self
    
    def register_instance(self, service_type: Type, instance: Any) -> 'ServiceContainer':
        """Register a service instance."""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            instance=instance,
            lifetime=ServiceLifetime.SINGLETON
        )
        self._services[service_type] = descriptor
        self._singletons[service_type] = instance
        return self
    
    def register_factory(self, service_type: Type, factory: Callable, lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'ServiceContainer':
        """Register a service factory."""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            factory=factory,
            lifetime=lifetime
        )
        self._services[service_type] = descriptor
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """Resolve a service instance."""
        if service_type not in self._services:
            raise PlatformException(f"Service {service_type.__name__} is not registered")
        
        descriptor = self._services[service_type]
        
        # Handle singleton lifetime
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if service_type in self._singletons:
                return self._singletons[service_type]
            
            instance = self._create_instance(descriptor)
            self._singletons[service_type] = instance
            return instance
        
        # Handle scoped lifetime
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if service_type in self._scoped_instances:
                return self._scoped_instances[service_type]
            
            instance = self._create_instance(descriptor)
            self._scoped_instances[service_type] = instance
            return instance
        
        # Handle transient lifetime
        else:
            return self._create_instance(descriptor)
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """Create service instance."""
        try:
            # Use existing instance
            if descriptor.instance is not None:
                return descriptor.instance
            
            # Use factory
            if descriptor.factory is not None:
                return descriptor.factory()
            
            # Use implementation type
            if descriptor.implementation_type is not None:
                return self._create_with_dependencies(descriptor.implementation_type)
            
            raise PlatformException("No way to create instance for service")
            
        except Exception as e:
            lambda_logger.error("Failed to create service instance", extra={
                'service_type': descriptor.service_type.__name__,
                'error': str(e)
            })
            raise PlatformException(f"Failed to create instance of {descriptor.service_type.__name__}: {str(e)}")
    
    def _create_with_dependencies(self, implementation_type: Type) -> Any:
        """Create instance with dependency injection."""
        try:
            # Get constructor signature
            signature = inspect.signature(implementation_type.__init__)
            parameters = signature.parameters
            
            # Skip 'self' parameter
            param_names = [name for name in parameters.keys() if name != 'self']
            
            # Resolve dependencies
            dependencies = {}
            for param_name in param_names:
                param = parameters[param_name]
                
                # Skip parameters with default values
                if param.default != inspect.Parameter.empty:
                    continue
                
                # Get parameter type annotation
                param_type = param.annotation
                if param_type == inspect.Parameter.empty:
                    raise PlatformException(f"Parameter '{param_name}' in {implementation_type.__name__} has no type annotation")
                
                # Resolve dependency
                dependencies[param_name] = self.resolve(param_type)
            
            # Create instance
            return implementation_type(**dependencies)
            
        except Exception as e:
            lambda_logger.error("Failed to create instance with dependencies", extra={
                'implementation_type': implementation_type.__name__,
                'error': str(e)
            })
            raise
    
    def clear_scoped(self) -> None:
        """Clear scoped instances."""
        self._scoped_instances.clear()
    
    def is_registered(self, service_type: Type) -> bool:
        """Check if service is registered."""
        return service_type in self._services
    
    def get_registered_services(self) -> Dict[Type, ServiceDescriptor]:
        """Get all registered services."""
        return self._services.copy()


# Global service container
container = ServiceContainer()


def inject(service_type: Type[T]) -> T:
    """Inject a service dependency."""
    return container.resolve(service_type)


def injectable(service_type: Optional[Type] = None, lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT):
    """Decorator to mark a class as injectable."""
    def decorator(cls):
        registration_type = service_type or cls
        
        if lifetime == ServiceLifetime.SINGLETON:
            container.register_singleton(registration_type, cls)
        elif lifetime == ServiceLifetime.SCOPED:
            container.register_scoped(registration_type, cls)
        else:
            container.register_transient(registration_type, cls)
        
        return cls
    
    return decorator


def singleton(service_type: Optional[Type] = None):
    """Decorator to register a class as singleton."""
    return injectable(service_type, ServiceLifetime.SINGLETON)


def transient(service_type: Optional[Type] = None):
    """Decorator to register a class as transient."""
    return injectable(service_type, ServiceLifetime.TRANSIENT)


def scoped(service_type: Optional[Type] = None):
    """Decorator to register a class as scoped."""
    return injectable(service_type, ServiceLifetime.SCOPED)
