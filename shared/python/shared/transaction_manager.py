# shared/python/shared/transaction_manager.py
# DynamoDB transaction manager for atomic operations - Shared Layer

"""
DynamoDB transaction manager that provides atomic operations
for simple multi-item transactions within DynamoDB.
"""

import boto3
import time
from typing import List, Dict, Any, Optional
from botocore.exceptions import ClientError

from .logger import lambda_logger
from .config import get_settings
from .exceptions import PlatformException


class DynamoDBTransaction:
    """Represents a DynamoDB transaction with multiple operations."""
    
    def __init__(self):
        """Initialize the transaction."""
        self.operations = []
        self.max_operations = 25  # DynamoDB limit
        self.settings = get_settings()
    
    def put_item(self, item: Dict[str, Any], condition_expression: str = None) -> 'DynamoDBTransaction':
        """Add a put item operation to the transaction."""
        if len(self.operations) >= self.max_operations:
            raise PlatformException(f"Transaction cannot exceed {self.max_operations} operations")
        
        operation = {
            'Put': {
                'TableName': self.settings.dynamodb_table,
                'Item': item
            }
        }
        
        if condition_expression:
            operation['Put']['ConditionExpression'] = condition_expression
        
        self.operations.append(operation)
        return self
    
    def update_item(
        self,
        key: Dict[str, Any],
        update_expression: str,
        expression_attribute_values: Dict[str, Any] = None,
        expression_attribute_names: Dict[str, str] = None,
        condition_expression: str = None
    ) -> 'DynamoDBTransaction':
        """Add an update item operation to the transaction."""
        if len(self.operations) >= self.max_operations:
            raise PlatformException(f"Transaction cannot exceed {self.max_operations} operations")
        
        operation = {
            'Update': {
                'TableName': self.settings.dynamodb_table,
                'Key': key,
                'UpdateExpression': update_expression
            }
        }
        
        if expression_attribute_values:
            operation['Update']['ExpressionAttributeValues'] = expression_attribute_values
        
        if expression_attribute_names:
            operation['Update']['ExpressionAttributeNames'] = expression_attribute_names
        
        if condition_expression:
            operation['Update']['ConditionExpression'] = condition_expression
        
        self.operations.append(operation)
        return self
    
    def delete_item(self, key: Dict[str, Any], condition_expression: str = None) -> 'DynamoDBTransaction':
        """Add a delete item operation to the transaction."""
        if len(self.operations) >= self.max_operations:
            raise PlatformException(f"Transaction cannot exceed {self.max_operations} operations")
        
        operation = {
            'Delete': {
                'TableName': self.settings.dynamodb_table,
                'Key': key
            }
        }
        
        if condition_expression:
            operation['Delete']['ConditionExpression'] = condition_expression
        
        self.operations.append(operation)
        return self
    
    def condition_check(self, key: Dict[str, Any], condition_expression: str) -> 'DynamoDBTransaction':
        """Add a condition check operation to the transaction."""
        if len(self.operations) >= self.max_operations:
            raise PlatformException(f"Transaction cannot exceed {self.max_operations} operations")
        
        operation = {
            'ConditionCheck': {
                'TableName': self.settings.dynamodb_table,
                'Key': key,
                'ConditionExpression': condition_expression
            }
        }
        
        self.operations.append(operation)
        return self


class TransactionManager:
    """Manages DynamoDB transactions."""
    
    def __init__(self):
        """Initialize transaction manager."""
        self.settings = get_settings()
        self.dynamodb = boto3.client('dynamodb', region_name=self.settings.region)
    
    def create_transaction(self) -> DynamoDBTransaction:
        """Create a new transaction."""
        return DynamoDBTransaction()
    
    def execute_transaction(self, transaction: DynamoDBTransaction) -> bool:
        """Execute a transaction atomically."""
        if not transaction.operations:
            lambda_logger.warning("Attempting to execute empty transaction")
            return True
        
        try:
            start_time = time.time()
            
            lambda_logger.info("Executing DynamoDB transaction", extra={
                'operation_count': len(transaction.operations),
                'table_name': self.settings.dynamodb_table
            })
            
            # Execute transaction
            response = self.dynamodb.transact_write_items(
                TransactItems=transaction.operations
            )
            
            duration_ms = (time.time() - start_time) * 1000
            
            lambda_logger.info("Transaction executed successfully", extra={
                'operation_count': len(transaction.operations),
                'duration_ms': duration_ms,
                'consumed_capacity': response.get('ConsumedCapacity')
            })
            
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            
            if error_code == 'TransactionCanceledException':
                # Transaction was cancelled due to condition failures
                cancellation_reasons = e.response.get('CancellationReasons', [])
                
                lambda_logger.warning("Transaction cancelled", extra={
                    'error_code': error_code,
                    'cancellation_reasons': cancellation_reasons,
                    'operation_count': len(transaction.operations)
                })
                
                raise PlatformException("Transaction cancelled due to condition failures")
            
            elif error_code == 'ValidationException':
                lambda_logger.error("Transaction validation failed", extra={
                    'error_code': error_code,
                    'error_message': str(e),
                    'operation_count': len(transaction.operations)
                })
                
                raise PlatformException(f"Transaction validation failed: {str(e)}")
            
            else:
                lambda_logger.error("Transaction execution failed", extra={
                    'error_code': error_code,
                    'error_message': str(e),
                    'operation_count': len(transaction.operations)
                })
                
                raise PlatformException(f"Transaction failed: {str(e)}")
        
        except Exception as e:
            lambda_logger.error("Unexpected transaction error", extra={
                'error': str(e),
                'operation_count': len(transaction.operations)
            })
            
            raise PlatformException(f"Unexpected transaction error: {str(e)}")
    
    def execute_read_transaction(self, get_operations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute a read transaction."""
        try:
            start_time = time.time()
            
            lambda_logger.info("Executing DynamoDB read transaction", extra={
                'operation_count': len(get_operations)
            })
            
            # Execute read transaction
            response = self.dynamodb.transact_get_items(
                TransactItems=get_operations
            )
            
            duration_ms = (time.time() - start_time) * 1000
            
            lambda_logger.info("Read transaction executed successfully", extra={
                'operation_count': len(get_operations),
                'duration_ms': duration_ms
            })
            
            # Extract items from response
            items = []
            for response_item in response.get('Responses', []):
                if 'Item' in response_item:
                    items.append(response_item['Item'])
                else:
                    items.append(None)  # Item not found
            
            return items
            
        except ClientError as e:
            lambda_logger.error("Read transaction failed", extra={
                'error_code': e.response['Error']['Code'],
                'error_message': str(e),
                'operation_count': len(get_operations)
            })
            
            raise PlatformException(f"Read transaction failed: {str(e)}")
        
        except Exception as e:
            lambda_logger.error("Unexpected read transaction error", extra={
                'error': str(e),
                'operation_count': len(get_operations)
            })
            
            raise PlatformException(f"Unexpected read transaction error: {str(e)}")


# Global transaction manager
transaction_manager = TransactionManager()


# Convenience functions
def create_transaction() -> DynamoDBTransaction:
    """Create a new transaction."""
    return transaction_manager.create_transaction()


def execute_transaction(transaction: DynamoDBTransaction) -> bool:
    """Execute a transaction."""
    return transaction_manager.execute_transaction(transaction)


def atomic_operation(func):
    """Decorator for atomic operations."""
    def wrapper(*args, **kwargs):
        transaction = create_transaction()
        try:
            result = func(transaction, *args, **kwargs)
            execute_transaction(transaction)
            return result
        except Exception as e:
            lambda_logger.error("Atomic operation failed", extra={
                'function': func.__name__,
                'error': str(e)
            })
            raise
    
    return wrapper
