# shared/python/shared/config.py
# Configuration management - Shared Layer

"""
Configuration management for the platform.
Handles environment variables and settings.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class Settings:
    """Application settings."""
    # Project configuration
    project_name: str = "agent-scl"
    environment: str = "dev"
    region: str = "us-east-1"
    
    # Database configuration
    dynamodb_table: str = ""
    
    # JWT configuration
    jwt_access_token_expiry: int = 3600  # 1 hour
    jwt_refresh_token_expiry: int = 604800  # 7 days
    
    # Password configuration
    password_min_length: int = 12  # Enterprise security standard
    max_login_attempts: int = 5
    account_lock_duration: int = 1800  # 30 minutes
    
    # Email configuration
    email_from_address: str = "<EMAIL>"
    email_verification_expiry: int = 86400  # 24 hours
    password_reset_expiry: int = 3600  # 1 hour


def get_settings() -> Settings:
    """Get application settings from environment variables."""
    return Settings(
        project_name=os.getenv('PROJECT_NAME', 'agent-scl'),
        environment=os.getenv('ENVIRONMENT', 'dev'),
        region=os.getenv('REGION', 'us-east-1'),
        dynamodb_table=os.getenv('DYNAMODB_TABLE', 'agent-scl-main-dev'),
        jwt_access_token_expiry=int(os.getenv('JWT_ACCESS_TOKEN_EXPIRY', '3600')),
        jwt_refresh_token_expiry=int(os.getenv('JWT_REFRESH_TOKEN_EXPIRY', '604800')),
        password_min_length=int(os.getenv('PASSWORD_MIN_LENGTH', '12')),
        max_login_attempts=int(os.getenv('MAX_LOGIN_ATTEMPTS', '5')),
        account_lock_duration=int(os.getenv('ACCOUNT_LOCK_DURATION', '1800')),
        email_from_address=os.getenv('EMAIL_FROM_ADDRESS', '<EMAIL>'),
        email_verification_expiry=int(os.getenv('EMAIL_VERIFICATION_EXPIRY', '86400')),
        password_reset_expiry=int(os.getenv('PASSWORD_RESET_EXPIRY', '3600'))
    )


def get_database_config() -> Dict[str, Any]:
    """Get database configuration."""
    settings = get_settings()
    
    return {
        "table_name": settings.dynamodb_table,
        "region": settings.region,
        "endpoint_url": os.getenv('DYNAMODB_ENDPOINT_URL')  # For local development
    }
