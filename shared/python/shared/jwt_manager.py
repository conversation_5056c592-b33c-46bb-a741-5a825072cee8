# shared/python/shared/jwt_manager.py
# Unified JWT Manager for the platform

"""
Unified JWT Manager that combines all JWT functionality:
- Multi-key support with rotation
- Blacklist support for token revocation
- Access and refresh token generation
- Centralized configuration
- Backward compatibility with existing APIs
"""

import jwt
import json
import time
import boto3
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union

from .logger import lambda_logger
from .jwt_config import get_jwt_config, JWTConfig
from .exceptions import (
    InvalidTokenException,
    TokenExpiredException,
    AuthenticationException
)


class UnifiedJWTManager:
    """
    Unified JWT Manager that combines all JWT functionality.
    
    Features:
    - Multi-key support with automatic rotation
    - Token blacklist for revocation
    - Access and refresh token types
    - Centralized configuration
    - Backward compatibility
    """
    
    def __init__(self):
        self.config: Optional[JWTConfig] = None
        self._secrets_client = None
        self._jwt_keys_cache: Optional[Dict[str, Any]] = None
        self._keys_last_refresh: Optional[float] = None
        self._blacklist_cache: Dict[str, float] = {}  # token_hash -> expiry_time
        self._blacklist_last_cleanup: Optional[float] = None
    
    def _get_secrets_client(self):
        """Get AWS Secrets Manager client."""
        if self._secrets_client is None:
            self._secrets_client = boto3.client('secretsmanager', region_name='us-east-1')
        return self._secrets_client
    
    def _get_config(self) -> JWTConfig:
        """Get JWT configuration."""
        if self.config is None:
            self.config = get_jwt_config()
        return self.config
    
    def _get_jwt_keys(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Get JWT keys from AWS Secrets Manager with caching."""
        current_time = time.time()
        config = self._get_config()
        
        # Check if we need to refresh
        if (force_refresh or 
            self._jwt_keys_cache is None or 
            (self._keys_last_refresh and current_time - self._keys_last_refresh > 300)):  # 5 min cache
            
            try:
                if config.multi_key_enabled and config.secret_source == "aws_secrets":
                    # Try to get multi-key configuration
                    secrets_client = self._get_secrets_client()
                    response = secrets_client.get_secret_value(SecretId=config.secret_name)
                    self._jwt_keys_cache = json.loads(response['SecretString'])
                else:
                    # Fallback to single key
                    secret_key = self._get_single_secret_key()
                    self._jwt_keys_cache = {
                        'algorithm': config.algorithm,
                        'issuer': config.issuer,
                        'audience': config.audience,
                        'keys': [{
                            'kid': 'default',
                            'secret_key': secret_key,
                            'active': True,
                            'created_at': datetime.utcnow().isoformat()
                        }]
                    }
                
                self._keys_last_refresh = current_time
                lambda_logger.debug("JWT keys refreshed", extra={
                    'multi_key': config.multi_key_enabled,
                    'key_count': len(self._jwt_keys_cache.get('keys', []))
                })
                
            except Exception as e:
                lambda_logger.error("Failed to refresh JWT keys", extra={'error': str(e)})
                if self._jwt_keys_cache is None:
                    raise AuthenticationException("JWT configuration not available")
        
        return self._jwt_keys_cache
    
    def _get_single_secret_key(self) -> str:
        """Get single JWT secret key."""
        config = self._get_config()
        
        if config.secret_source == "aws_secrets":
            try:
                secrets_client = self._get_secrets_client()
                response = secrets_client.get_secret_value(SecretId=config.secret_name)
                return response['SecretString']
            except Exception as e:
                lambda_logger.warning("Failed to get JWT secret from AWS", extra={'error': str(e)})
        
        # Fallback to environment variable
        import os
        jwt_secret = os.getenv('JWT_SECRET')
        
        if not jwt_secret:
            raise AuthenticationException(
                "JWT secret not available. Configure AWS Secrets Manager or set JWT_SECRET environment variable."
            )
        
        if len(jwt_secret) < 32:
            raise AuthenticationException("JWT secret is too weak. Use a strong secret (>32 characters).")
        
        return jwt_secret
    
    def get_active_key(self) -> Dict[str, Any]:
        """Get the currently active JWT key."""
        try:
            keys_config = self._get_jwt_keys()
            lambda_logger.info(f"JWT keys config retrieved: {keys_config}")

            # Find active key
            for key in keys_config.get('keys', []):
                if key.get('active', False):
                    lambda_logger.info(f"Found active JWT key: {key.get('kid')}")
                    return key

            # If no active key found, use the first one
            keys = keys_config.get('keys', [])
            lambda_logger.info(f"No active key found, using first key. Available keys: {len(keys)}")
            if keys:
                lambda_logger.info(f"Using first key: {keys[0].get('kid')}")
                return keys[0]

            lambda_logger.error("No JWT keys available in configuration")
            raise AuthenticationException("No JWT keys available")
        except Exception as e:
            lambda_logger.error(f"Error getting active JWT key: {str(e)}")
            raise
    
    def generate_access_token(
        self,
        user_id: str,
        email: str,
        tenant_id: str,
        role: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate an access token."""
        config = self._get_config()
        active_key = self.get_active_key()
        
        now = datetime.now(timezone.utc)
        payload = {
            'user_id': user_id,
            'email': email,
            'tenant_id': tenant_id,
            'role': role,
            'type': 'access',
            'iat': now,
            'exp': now + timedelta(seconds=config.access_token_expiry),
            'iss': config.issuer,
            'aud': config.audience,
            'sub': user_id,  # Standard JWT subject claim
            'jti': self._generate_token_id()  # Unique token ID for blacklist
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        # Add key ID if multi-key is enabled
        if config.multi_key_enabled:
            payload['kid'] = active_key['kid']
        
        headers = {}
        if config.multi_key_enabled:
            headers['kid'] = active_key['kid']
        
        token = jwt.encode(
            payload, 
            active_key['secret_key'], 
            algorithm=config.algorithm,
            headers=headers if headers else None
        )
        
        lambda_logger.debug("Access token generated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'expires_in': config.access_token_expiry,
            'key_id': active_key.get('kid', 'default')
        })
        
        return token
    
    def generate_refresh_token(
        self,
        user_id: str,
        tenant_id: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate a refresh token."""
        config = self._get_config()
        active_key = self.get_active_key()
        
        now = datetime.now(timezone.utc)
        payload = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'type': 'refresh',
            'iat': now,
            'exp': now + timedelta(seconds=config.refresh_token_expiry),
            'iss': config.issuer,
            'aud': config.audience,
            'sub': user_id,
            'jti': self._generate_token_id()
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        if config.multi_key_enabled:
            payload['kid'] = active_key['kid']
        
        headers = {}
        if config.multi_key_enabled:
            headers['kid'] = active_key['kid']
        
        token = jwt.encode(
            payload, 
            active_key['secret_key'], 
            algorithm=config.algorithm,
            headers=headers if headers else None
        )
        
        lambda_logger.debug("Refresh token generated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'expires_in': config.refresh_token_expiry
        })
        
        return token
    
    def _generate_token_id(self) -> str:
        """Generate unique token ID for blacklist support."""
        import secrets
        return secrets.token_urlsafe(32)
    
    def verify_token(self, token: str, token_type: str = 'access') -> Dict[str, Any]:
        """Verify and decode a JWT token."""
        config = self._get_config()
        
        # Check blacklist first if enabled
        if config.blacklist_enabled and self._is_token_blacklisted(token):
            raise InvalidTokenException("Token has been revoked")
        
        # Try to validate with available keys
        keys_config = self._get_jwt_keys()
        keys_to_try = []
        
        # If multi-key is enabled, try to get key from token header
        if config.multi_key_enabled:
            try:
                unverified_header = jwt.get_unverified_header(token)
                kid = unverified_header.get('kid')
                
                if kid:
                    for key in keys_config.get('keys', []):
                        if key.get('kid') == kid:
                            keys_to_try.append(key)
                            break
            except Exception:
                pass
        
        # Add active key if not already included
        active_key = self.get_active_key()
        if active_key not in keys_to_try:
            keys_to_try.append(active_key)
        
        # Add all other keys as fallback
        for key in keys_config.get('keys', []):
            if key not in keys_to_try:
                keys_to_try.append(key)
        
        # Try to validate with each key
        last_exception = None
        for key in keys_to_try:
            try:
                payload = jwt.decode(
                    token,
                    key['secret_key'],
                    algorithms=[config.algorithm],
                    audience=config.audience,
                    issuer=config.issuer
                )
                
                # Verify token type
                if payload.get('type') != token_type:
                    continue
                
                lambda_logger.debug("Token validated", extra={
                    'token_type': token_type,
                    'user_id': payload.get('user_id'),
                    'key_id': key.get('kid', 'default')
                })
                
                return payload
                
            except jwt.ExpiredSignatureError as e:
                raise TokenExpiredException("Token has expired")
            except jwt.InvalidTokenError as e:
                last_exception = e
                continue
        
        # If we get here, no key worked
        raise InvalidTokenException(f"Invalid token: {str(last_exception)}")
    
    def _is_token_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted."""
        # Simple in-memory blacklist for now
        # In production, this should use Redis or DynamoDB
        token_hash = self._hash_token(token)
        
        # Cleanup expired entries periodically
        self._cleanup_blacklist()
        
        return token_hash in self._blacklist_cache
    
    def blacklist_token(self, token: str, expiry_time: Optional[float] = None):
        """Add token to blacklist."""
        if expiry_time is None:
            # Extract expiry from token
            try:
                unverified_payload = jwt.decode(token, options={"verify_signature": False})
                expiry_time = unverified_payload.get('exp', time.time() + 3600)
            except Exception:
                expiry_time = time.time() + 3600  # Default 1 hour
        
        token_hash = self._hash_token(token)
        self._blacklist_cache[token_hash] = expiry_time
        
        lambda_logger.info("Token blacklisted", extra={'token_hash': token_hash[:8]})
    
    def _hash_token(self, token: str) -> str:
        """Create hash of token for blacklist storage."""
        import hashlib
        return hashlib.sha256(token.encode()).hexdigest()
    
    def _cleanup_blacklist(self):
        """Remove expired tokens from blacklist."""
        current_time = time.time()
        
        # Only cleanup every 5 minutes
        if (self._blacklist_last_cleanup and 
            current_time - self._blacklist_last_cleanup < 300):
            return
        
        expired_tokens = [
            token_hash for token_hash, expiry in self._blacklist_cache.items()
            if expiry < current_time
        ]
        
        for token_hash in expired_tokens:
            del self._blacklist_cache[token_hash]
        
        self._blacklist_last_cleanup = current_time
        
        if expired_tokens:
            lambda_logger.debug("Blacklist cleanup completed", extra={
                'removed_count': len(expired_tokens)
            })


# Global instance
_unified_jwt_manager = None

def get_jwt_manager() -> UnifiedJWTManager:
    """Get unified JWT manager instance."""
    global _unified_jwt_manager
    if _unified_jwt_manager is None:
        _unified_jwt_manager = UnifiedJWTManager()
    return _unified_jwt_manager

# Backward compatibility functions
def generate_access_token(user_id: str, email: str, tenant_id: str, role: str, **kwargs) -> str:
    """Generate access token - backward compatibility."""
    return get_jwt_manager().generate_access_token(user_id, email, tenant_id, role, kwargs)

def generate_refresh_token(user_id: str, tenant_id: str, **kwargs) -> str:
    """Generate refresh token - backward compatibility."""
    return get_jwt_manager().generate_refresh_token(user_id, tenant_id, kwargs)

def verify_token(token: str, token_type: str = 'access') -> Dict[str, Any]:
    """Verify token - backward compatibility."""
    return get_jwt_manager().verify_token(token, token_type)
