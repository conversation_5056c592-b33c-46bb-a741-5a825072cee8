# shared/python/shared/saga.py
# Saga pattern implementation for distributed transactions - Shared Layer

"""
Saga pattern implementation for managing distributed transactions
across multiple services and ensuring data consistency.
"""

import json
import time
import uuid
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

from .logger import lambda_logger
from .database import db_client
from .exceptions import PlatformException


class SagaState(Enum):
    """Saga execution states."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    COMPENSATING = "compensating"
    COMPENSATED = "compensated"
    FAILED = "failed"


class StepState(Enum):
    """Individual step states."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    COMPENSATING = "compensating"
    COMPENSATED = "compensated"


@dataclass
class SagaStep:
    """Represents a single step in a saga."""
    step_id: str
    name: str
    action: Callable
    compensation: Optional[Callable] = None
    state: StepState = StepState.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: Optional[int] = None
    completed_at: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class SagaContext:
    """Context passed between saga steps."""
    saga_id: str
    tenant_id: str
    user_id: Optional[str] = None
    correlation_id: Optional[str] = None
    data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}


class Saga:
    """Saga orchestrator for distributed transactions."""
    
    def __init__(self, saga_id: str, name: str, context: SagaContext):
        """Initialize saga."""
        self.saga_id = saga_id
        self.name = name
        self.context = context
        self.steps: List[SagaStep] = []
        self.state = SagaState.PENDING
        self.created_at = int(time.time())
        self.started_at: Optional[int] = None
        self.completed_at: Optional[int] = None
        self.error: Optional[str] = None
        self.current_step_index = 0
    
    def add_step(
        self,
        name: str,
        action: Callable,
        compensation: Optional[Callable] = None,
        max_retries: int = 3
    ) -> 'Saga':
        """Add a step to the saga."""
        step_id = f"{self.saga_id}_step_{len(self.steps)}"
        step = SagaStep(
            step_id=step_id,
            name=name,
            action=action,
            compensation=compensation,
            max_retries=max_retries
        )
        self.steps.append(step)
        return self
    
    def execute(self) -> bool:
        """Execute the saga."""
        try:
            self.state = SagaState.RUNNING
            self.started_at = int(time.time())
            
            lambda_logger.info("Starting saga execution", extra={
                'saga_id': self.saga_id,
                'saga_name': self.name,
                'total_steps': len(self.steps),
                'tenant_id': self.context.tenant_id
            })
            
            # Save initial state
            self._save_state()
            
            # Execute steps sequentially
            for i, step in enumerate(self.steps):
                self.current_step_index = i
                
                if not self._execute_step(step):
                    # Step failed, start compensation
                    lambda_logger.error("Saga step failed, starting compensation", extra={
                        'saga_id': self.saga_id,
                        'failed_step': step.name,
                        'step_index': i
                    })
                    
                    self._compensate()
                    return False
                
                # Save state after each step
                self._save_state()
            
            # All steps completed successfully
            self.state = SagaState.COMPLETED
            self.completed_at = int(time.time())
            self._save_state()
            
            lambda_logger.info("Saga completed successfully", extra={
                'saga_id': self.saga_id,
                'saga_name': self.name,
                'duration_ms': (self.completed_at - self.started_at) * 1000
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Saga execution failed", extra={
                'saga_id': self.saga_id,
                'error': str(e)
            })
            
            self.state = SagaState.FAILED
            self.error = str(e)
            self._save_state()
            
            # Attempt compensation
            self._compensate()
            return False
    
    def _execute_step(self, step: SagaStep) -> bool:
        """Execute a single saga step."""
        step.state = StepState.RUNNING
        step.started_at = int(time.time())
        
        lambda_logger.info("Executing saga step", extra={
            'saga_id': self.saga_id,
            'step_id': step.step_id,
            'step_name': step.name,
            'retry_count': step.retry_count
        })
        
        try:
            # Execute the step action
            result = step.action(self.context)
            
            step.state = StepState.COMPLETED
            step.completed_at = int(time.time())
            step.result = result
            
            # Update context with step result
            if isinstance(result, dict):
                self.context.data.update(result)
            
            lambda_logger.info("Saga step completed", extra={
                'saga_id': self.saga_id,
                'step_id': step.step_id,
                'step_name': step.name,
                'duration_ms': (step.completed_at - step.started_at) * 1000
            })
            
            return True
            
        except Exception as e:
            step.state = StepState.FAILED
            step.error = str(e)
            step.completed_at = int(time.time())
            
            lambda_logger.error("Saga step failed", extra={
                'saga_id': self.saga_id,
                'step_id': step.step_id,
                'step_name': step.name,
                'error': str(e),
                'retry_count': step.retry_count
            })
            
            # Retry logic
            if step.retry_count < step.max_retries:
                step.retry_count += 1
                step.state = StepState.PENDING
                
                lambda_logger.info("Retrying saga step", extra={
                    'saga_id': self.saga_id,
                    'step_id': step.step_id,
                    'retry_count': step.retry_count,
                    'max_retries': step.max_retries
                })
                
                # Wait before retry (exponential backoff)
                wait_time = 2 ** step.retry_count
                time.sleep(wait_time)
                
                return self._execute_step(step)
            
            return False
    
    def _compensate(self) -> None:
        """Execute compensation for completed steps."""
        self.state = SagaState.COMPENSATING
        
        lambda_logger.info("Starting saga compensation", extra={
            'saga_id': self.saga_id,
            'current_step_index': self.current_step_index
        })
        
        # Compensate completed steps in reverse order
        for i in range(self.current_step_index, -1, -1):
            step = self.steps[i]
            
            if step.state == StepState.COMPLETED and step.compensation:
                self._compensate_step(step)
        
        self.state = SagaState.COMPENSATED
        self.completed_at = int(time.time())
        self._save_state()
        
        lambda_logger.info("Saga compensation completed", extra={
            'saga_id': self.saga_id
        })
    
    def _compensate_step(self, step: SagaStep) -> None:
        """Compensate a single step."""
        step.state = StepState.COMPENSATING
        
        lambda_logger.info("Compensating saga step", extra={
            'saga_id': self.saga_id,
            'step_id': step.step_id,
            'step_name': step.name
        })
        
        try:
            step.compensation(self.context)
            step.state = StepState.COMPENSATED
            
            lambda_logger.info("Saga step compensated", extra={
                'saga_id': self.saga_id,
                'step_id': step.step_id,
                'step_name': step.name
            })
            
        except Exception as e:
            lambda_logger.error("Saga step compensation failed", extra={
                'saga_id': self.saga_id,
                'step_id': step.step_id,
                'step_name': step.name,
                'error': str(e)
            })
    
    def _save_state(self) -> None:
        """Save saga state to database."""
        try:
            saga_item = {
                'PK': f'SAGA#{self.saga_id}',
                'SK': 'STATE',
                'entity_type': 'SAGA',
                'saga_id': self.saga_id,
                'name': self.name,
                'state': self.state.value,
                'context': asdict(self.context),
                'steps': [asdict(step) for step in self.steps],
                'created_at': self.created_at,
                'started_at': self.started_at,
                'completed_at': self.completed_at,
                'current_step_index': self.current_step_index,
                'error': self.error
            }
            
            db_client.put_item(saga_item, self.context.tenant_id)
            
        except Exception as e:
            lambda_logger.error("Failed to save saga state", extra={
                'saga_id': self.saga_id,
                'error': str(e)
            })


class SagaManager:
    """Manages saga creation and execution."""
    
    def create_saga(self, name: str, context: SagaContext) -> Saga:
        """Create a new saga."""
        saga_id = str(uuid.uuid4())
        return Saga(saga_id, name, context)
    
    def get_saga(self, saga_id: str, tenant_id: str) -> Optional[Saga]:
        """Get saga by ID."""
        try:
            item = db_client.get_item(
                pk=f'SAGA#{saga_id}',
                sk='STATE',
                tenant_id=tenant_id
            )
            
            if not item:
                return None
            
            # Reconstruct saga from saved state
            context = SagaContext(**item['context'])
            saga = Saga(saga_id, item['name'], context)
            saga.state = SagaState(item['state'])
            saga.created_at = item['created_at']
            saga.started_at = item.get('started_at')
            saga.completed_at = item.get('completed_at')
            saga.current_step_index = item.get('current_step_index', 0)
            saga.error = item.get('error')
            
            # Reconstruct steps (without action/compensation functions)
            for step_data in item.get('steps', []):
                step = SagaStep(**step_data)
                saga.steps.append(step)
            
            return saga
            
        except Exception as e:
            lambda_logger.error("Failed to get saga", extra={
                'saga_id': saga_id,
                'error': str(e)
            })
            return None


# Global saga manager
saga_manager = SagaManager()
