# shared/python/shared/jwt_config.py
# Centralized JWT configuration management

"""
Centralized JWT configuration that unifies all JWT settings
and provides a single source of truth for JWT parameters.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .logger import lambda_logger
from .config import get_settings


@dataclass
class JWTConfig:
    """JWT configuration data class."""
    algorithm: str = "HS256"
    issuer: str = "agent-scl-platform"
    audience: str = "agent-scl-api"
    access_token_expiry: int = 3600      # 1 hour in seconds
    refresh_token_expiry: int = 604800   # 7 days in seconds
    secret_source: str = "aws_secrets"   # aws_secrets | environment
    secret_name: str = "agent-scl/dev/jwt"
    multi_key_enabled: bool = True
    blacklist_enabled: bool = True
    cache_config: bool = True
    cache_ttl: int = 300                 # 5 minutes


class JWTConfigManager:
    """Manages JWT configuration from multiple sources."""
    
    def __init__(self):
        self._config: Optional[JWTConfig] = None
        self._last_refresh: Optional[float] = None
        self.settings = get_settings()
    
    def get_config(self, force_refresh: bool = False) -> JWTConfig:
        """Get JWT configuration with caching."""
        import time
        
        current_time = time.time()
        
        # Check if we need to refresh
        if (force_refresh or 
            self._config is None or 
            (self._last_refresh and current_time - self._last_refresh > self._config.cache_ttl)):
            
            self._config = self._load_config()
            self._last_refresh = current_time
            
            lambda_logger.debug("JWT configuration refreshed", extra={
                'source': self._config.secret_source,
                'multi_key_enabled': self._config.multi_key_enabled,
                'blacklist_enabled': self._config.blacklist_enabled
            })
        
        return self._config
    
    def _load_config(self) -> JWTConfig:
        """Load JWT configuration from environment and settings."""
        
        # Start with defaults
        config = JWTConfig()
        
        # Override with environment variables if present
        config.algorithm = os.getenv('JWT_ALGORITHM', config.algorithm)
        config.issuer = os.getenv('JWT_ISSUER', config.issuer)
        config.audience = os.getenv('JWT_AUDIENCE', config.audience)
        
        # Token expiry from environment or settings
        try:
            config.access_token_expiry = int(os.getenv('JWT_ACCESS_TOKEN_EXPIRY', 
                                                      getattr(self.settings, 'jwt_access_token_expiry', 
                                                             config.access_token_expiry)))
            config.refresh_token_expiry = int(os.getenv('JWT_REFRESH_TOKEN_EXPIRY',
                                                       getattr(self.settings, 'jwt_refresh_token_expiry',
                                                              config.refresh_token_expiry)))
        except (ValueError, TypeError):
            lambda_logger.warning("Invalid JWT expiry configuration, using defaults")
        
        # Secret configuration
        config.secret_source = os.getenv('JWT_SECRET_SOURCE', config.secret_source)
        config.secret_name = os.getenv('JWT_SECRET_NAME',
                                      f"{self.settings.project_name}/{self.settings.environment}/jwt-secret")
        
        # Feature flags
        config.multi_key_enabled = os.getenv('JWT_MULTI_KEY_ENABLED', 'true').lower() == 'true'
        config.blacklist_enabled = os.getenv('JWT_BLACKLIST_ENABLED', 'true').lower() == 'true'
        
        # Cache settings
        config.cache_config = os.getenv('JWT_CACHE_CONFIG', 'true').lower() == 'true'
        try:
            config.cache_ttl = int(os.getenv('JWT_CACHE_TTL', config.cache_ttl))
        except (ValueError, TypeError):
            pass
        
        return config
    
    def validate_config(self, config: JWTConfig) -> bool:
        """Validate JWT configuration."""
        errors = []
        
        if not config.algorithm:
            errors.append("JWT algorithm is required")
        
        if not config.issuer:
            errors.append("JWT issuer is required")
        
        if not config.audience:
            errors.append("JWT audience is required")
        
        if config.access_token_expiry <= 0:
            errors.append("JWT access token expiry must be positive")
        
        if config.refresh_token_expiry <= 0:
            errors.append("JWT refresh token expiry must be positive")
        
        if config.secret_source not in ['aws_secrets', 'environment']:
            errors.append("JWT secret source must be 'aws_secrets' or 'environment'")
        
        if errors:
            lambda_logger.error("JWT configuration validation failed", extra={'errors': errors})
            return False
        
        return True


# Global configuration manager
_jwt_config_manager = None

def get_jwt_config_manager() -> JWTConfigManager:
    """Get JWT configuration manager instance."""
    global _jwt_config_manager
    if _jwt_config_manager is None:
        _jwt_config_manager = JWTConfigManager()
    return _jwt_config_manager

def get_jwt_config(force_refresh: bool = False) -> JWTConfig:
    """Get JWT configuration."""
    return get_jwt_config_manager().get_config(force_refresh)

def validate_jwt_config() -> bool:
    """Validate current JWT configuration."""
    manager = get_jwt_config_manager()
    config = manager.get_config()
    return manager.validate_config(config)
