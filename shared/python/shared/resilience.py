# shared/python/shared/resilience.py
# Resilience patterns implementation - Shared Layer

"""
Resilience patterns implementation including circuit breaker,
retry logic, and bulkhead patterns.
"""

import time
import threading
from typing import Callable, Any, Dict, Optional
from functools import wraps
from enum import Enum

from .logger import lambda_logger
from .exceptions import ResilienceException, CircuitBreakerException, MaxRetriesExceededException


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class CircuitBreaker:
    """Circuit breaker implementation."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception
    ):
        """Initialize circuit breaker."""
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        self._lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        with self._lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    lambda_logger.info("Circuit breaker half-open")
                else:
                    raise CircuitBreakerException("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self) -> None:
        """Handle successful execution."""
        with self._lock:
            self.failure_count = 0
            self.state = CircuitState.CLOSED
    
    def _on_failure(self) -> None:
        """Handle failed execution."""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
                lambda_logger.warning("Circuit breaker opened", extra={
                    'failure_count': self.failure_count,
                    'threshold': self.failure_threshold
                })


def resilient(
    service_name: str,
    timeout_seconds: float = 30.0,
    max_attempts: int = 3,
    max_concurrent: int = 10
):
    """Decorator for applying resilience patterns."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Simple retry logic for now
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        wait_time = 2 ** attempt  # Exponential backoff
                        lambda_logger.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time}s", extra={
                            'service': service_name,
                            'error': str(e)
                        })
                        time.sleep(wait_time)
                    else:
                        lambda_logger.error(f"All {max_attempts} attempts failed", extra={
                            'service': service_name,
                            'error': str(e)
                        })
            
            raise MaxRetriesExceededException(f"Failed after {max_attempts} attempts: {str(last_exception)}")
        
        return wrapper
    return decorator
