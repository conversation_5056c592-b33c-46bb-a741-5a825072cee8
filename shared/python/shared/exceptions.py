# shared/python/shared/exceptions.py
# Custom exceptions - Shared Layer

"""
Custom exception classes for the platform.
Provides specific error types for different scenarios.
"""


class PlatformException(Exception):
    """Base exception for platform-specific errors."""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(PlatformException):
    """Exception raised for validation errors."""
    pass


class AuthenticationException(PlatformException):
    """Exception raised for authentication errors."""
    pass


class AuthorizationException(PlatformException):
    """Exception raised for authorization errors."""
    pass


class InvalidTokenException(AuthenticationException):
    """Exception raised for invalid tokens."""
    pass


class TokenExpiredException(AuthenticationException):
    """Exception raised for expired tokens."""
    pass


class InvalidCredentialsException(AuthenticationException):
    """Exception raised for invalid credentials."""
    pass


class AccountLockedException(AuthenticationException):
    """Exception raised for locked accounts."""
    pass


class EmailNotVerifiedException(AuthenticationException):
    """Exception raised for unverified email addresses."""
    pass


class WeakPasswordException(ValidationException):
    """Exception raised for weak passwords."""
    pass


class ResourceNotFoundException(PlatformException):
    """Exception raised when a resource is not found."""
    pass


class ResourceConflictException(PlatformException):
    """Exception raised when a resource conflict occurs."""
    pass


class DatabaseException(PlatformException):
    """Exception raised for database errors."""
    
    def __init__(self, message: str, operation: str = None, table_name: str = None):
        self.operation = operation
        self.table_name = table_name
        super().__init__(message)


class PaymentException(PlatformException):
    """Exception raised for payment-related errors."""
    pass


class BusinessLogicException(PlatformException):
    """Exception raised for business logic violations."""
    pass


class ExternalServiceException(PlatformException):
    """Exception raised for external service errors."""

    def __init__(self, message: str, service_name: str = None, service_error_code: str = None):
        self.service_name = service_name
        self.service_error_code = service_error_code
        super().__init__(message)


class ResilienceException(PlatformException):
    """Exception raised by resilience patterns."""
    pass


class CircuitBreakerException(ResilienceException):
    """Exception raised when circuit breaker is open."""
    pass


class MaxRetriesExceededException(ResilienceException):
    """Exception raised when maximum retries are exceeded."""
    pass


class BulkheadException(ResilienceException):
    """Exception raised when bulkhead capacity is exceeded."""
    pass
