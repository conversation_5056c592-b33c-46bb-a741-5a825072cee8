# src/shared/service_configuration.py
# Service configuration and dependency registration

"""
Service configuration module that registers all services
and their dependencies in the IoC container.
"""

from .dependency_injection import service_container, ServiceLifetime
from .logger import lambda_logger


def configure_services() -> None:
    """Configure all services and their dependencies."""
    lambda_logger.info("Configuring services and dependencies")
    
    # Configure repositories
    _configure_repositories()
    
    # Configure services
    _configure_business_services()
    
    # Configure infrastructure services
    _configure_infrastructure_services()
    
    lambda_logger.info("Service configuration completed")


def _configure_repositories() -> None:
    """Configure repository dependencies."""
    from ..auth.repositories.tenant_repository import ITenantRepository, TenantRepository
    from ..auth.repositories.user_repository import IUserRepository, UserRepository
    
    # Register repositories as scoped (one instance per request scope)
    service_container.register_scoped(ITenantRepository, TenantRepository)
    service_container.register_scoped(IUserRepository, UserRepository)
    
    lambda_logger.info("Repositories configured")


def _configure_business_services() -> None:
    """Configure business service dependencies."""
    from ..auth.services.tenant_service import ITenantService, TenantService
    
    # Register business services as scoped
    service_container.register_scoped(ITenantService, TenantService)
    
    lambda_logger.info("Business services configured")


def _configure_infrastructure_services() -> None:
    """Configure infrastructure service dependencies."""
    from .database import DatabaseClient
    from .events import EventPublisher, EventHandler
    from .secrets_manager import EnhancedSecretsManager
    from .saga import SagaOrchestrator
    from .transaction_manager import TransactionManager
    
    # Register infrastructure services as singletons
    service_container.register_singleton(DatabaseClient, instance=DatabaseClient())
    service_container.register_singleton(EventPublisher, instance=EventPublisher())
    service_container.register_singleton(EventHandler, instance=EventHandler())
    service_container.register_singleton(EnhancedSecretsManager, instance=EnhancedSecretsManager())
    service_container.register_singleton(SagaOrchestrator, instance=SagaOrchestrator())
    service_container.register_singleton(TransactionManager, instance=TransactionManager())
    
    lambda_logger.info("Infrastructure services configured")


def get_service_health() -> dict:
    """Get health status of registered services."""
    try:
        health_status = {
            'status': 'healthy',
            'services': {},
            'timestamp': lambda_logger.get_current_timestamp()
        }
        
        # Check repository health
        try:
            from ..auth.repositories.tenant_repository import ITenantRepository
            tenant_repo = service_container.resolve(ITenantRepository)
            health_status['services']['tenant_repository'] = 'healthy'
        except Exception as e:
            health_status['services']['tenant_repository'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'degraded'
        
        try:
            from ..auth.repositories.user_repository import IUserRepository
            user_repo = service_container.resolve(IUserRepository)
            health_status['services']['user_repository'] = 'healthy'
        except Exception as e:
            health_status['services']['user_repository'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'degraded'
        
        # Check business service health
        try:
            from ..auth.services.tenant_service import ITenantService
            tenant_service = service_container.resolve(ITenantService)
            health_status['services']['tenant_service'] = 'healthy'
        except Exception as e:
            health_status['services']['tenant_service'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'degraded'
        
        # Check infrastructure service health
        try:
            database_client = service_container.resolve(DatabaseClient)
            health_status['services']['database_client'] = 'healthy'
        except Exception as e:
            health_status['services']['database_client'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'degraded'
        
        return health_status
        
    except Exception as e:
        lambda_logger.error("Failed to get service health", extra={'error': str(e)})
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': lambda_logger.get_current_timestamp()
        }


# Auto-configure services when module is imported
try:
    configure_services()
except Exception as e:
    lambda_logger.error("Failed to configure services", extra={'error': str(e)})
    # Don't fail module import, but log the error
