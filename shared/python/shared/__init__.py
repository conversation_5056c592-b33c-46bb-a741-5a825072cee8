# shared/python/shared/__init__.py
# Shared layer package initialization

"""
Shared utilities and common code for Agent SCL services.
This package provides common functionality across all microservices.

Import modules directly to avoid circular dependencies:
    from shared.auth import jwt_manager
    from shared.validators import LoginRequestValidator
    from shared.responses import APIResponse
"""

__version__ = "1.0.0"

# Note: Import modules directly to avoid circular dependencies
# Example usage:
#   from shared.auth import jwt_manager
#   from shared.validators import LoginRequestValidator
#   from shared.responses import APIResponse
