"""
Service Communication Utilities for Agent SCL Platform.
This module provides unified patterns for inter-service communication
to ensure consistency and eliminate direct database access across services.

CRITICAL: This is the single source of truth for service-to-service communication.
"""

import json
import time
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict

from .models import UserInfo, TenantInfo, UserRole, UserStatus
from .database import DynamoDBClient
from .logger import lambda_logger
from .exceptions import ValidationException


@dataclass
class ServiceResponse:
    """Standardized response format for service communication."""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class UserServiceClient:
    """
    Unified client for user-related operations across services.
    Provides consistent interface for auth and tenant services.
    """
    
    def __init__(self, db_client: DynamoDBClient):
        self.db = db_client
    
    async def get_user_by_id(self, user_id: str, tenant_id: str) -> ServiceResponse:
        """
        Get user by ID with unified data format.
        SINGLE SOURCE OF TRUTH for user retrieval.
        """
        try:
            # Try tenant service pattern first
            user_record = self.db.get_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                tenant_id=tenant_id
            )
            
            if not user_record:
                # Try auth service pattern as fallback
                user_record = self.db.get_item(
                    pk=f'USER#{user_id}',
                    sk='PROFILE',
                    tenant_id=tenant_id
                )
            
            if not user_record:
                return ServiceResponse(
                    success=False,
                    error="User not found",
                    error_code="USER_NOT_FOUND"
                )
            
            # Convert to unified format
            user_info = self._convert_to_user_info(user_record)
            
            return ServiceResponse(
                success=True,
                data=user_info.to_dict()
            )
            
        except Exception as e:
            lambda_logger.error("Failed to get user", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return ServiceResponse(
                success=False,
                error="Internal server error",
                error_code="INTERNAL_ERROR"
            )
    
    async def get_user_by_email(self, email: str, tenant_id: str) -> ServiceResponse:
        """
        Get user by email with unified data format.
        SINGLE SOURCE OF TRUTH for email-based user lookup.
        """
        try:
            # Query using email GSI
            users = self.db.query_gsi(
                gsi_name='GSI2',
                pk=f'EMAIL#{email}',
                sk=f'TENANT#{tenant_id}',
                tenant_id=tenant_id
            )
            
            if not users:
                return ServiceResponse(
                    success=False,
                    error="User not found",
                    error_code="USER_NOT_FOUND"
                )
            
            # Convert to unified format
            user_info = self._convert_to_user_info(users[0])
            
            return ServiceResponse(
                success=True,
                data=user_info.to_dict()
            )
            
        except Exception as e:
            lambda_logger.error("Failed to get user by email", extra={
                'email': email,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return ServiceResponse(
                success=False,
                error="Internal server error",
                error_code="INTERNAL_ERROR"
            )
    
    async def validate_user_exists(self, user_id: str, tenant_id: str) -> bool:
        """
        Validate if user exists in tenant.
        UNIFIED VALIDATION for cross-service checks.
        """
        response = await self.get_user_by_id(user_id, tenant_id)
        return response.success
    
    async def validate_user_permissions(self, user_id: str, tenant_id: str, required_role: UserRole) -> ServiceResponse:
        """
        Validate user permissions across services.
        UNIFIED PERMISSION VALIDATION for cross-service authorization.
        """
        user_response = await self.get_user_by_id(user_id, tenant_id)
        
        if not user_response.success:
            return user_response
        
        user_data = user_response.data
        user_role = UserRole(user_data['role'])
        
        if not user_role.can_access(required_role):
            return ServiceResponse(
                success=False,
                error=f"Insufficient permissions. Required: {required_role.value}",
                error_code="INSUFFICIENT_PERMISSIONS"
            )
        
        return ServiceResponse(success=True, data=user_data)
    
    def _convert_to_user_info(self, user_record: Dict[str, Any]) -> UserInfo:
        """Convert database record to unified UserInfo format."""
        # Handle different field naming conventions
        first_name = user_record.get('first_name') or user_record.get('name', '').split(' ')[0]
        last_name = user_record.get('last_name') or ' '.join(user_record.get('name', '').split(' ')[1:])
        
        return UserInfo(
            user_id=user_record['user_id'],
            tenant_id=user_record['tenant_id'],
            email=user_record['email'],
            first_name=first_name,
            last_name=last_name,
            role=UserRole(user_record['role']),
            status=UserStatus(user_record['status']),
            email_verified=user_record.get('email_verified', False),
            last_login=user_record.get('last_login_at'),
            created_at=user_record.get('created_at')
        )


class TenantServiceClient:
    """
    Unified client for tenant-related operations across services.
    Provides consistent interface for tenant data access.
    """
    
    def __init__(self, db_client: DynamoDBClient):
        self.db = db_client
    
    async def get_tenant_by_id(self, tenant_id: str) -> ServiceResponse:
        """
        Get tenant by ID with unified data format.
        SINGLE SOURCE OF TRUTH for tenant retrieval.
        """
        try:
            tenant_record = self.db.get_item(
                pk=f'TENANT#{tenant_id}',
                sk='PROFILE',
                tenant_id=tenant_id
            )
            
            if not tenant_record:
                return ServiceResponse(
                    success=False,
                    error="Tenant not found",
                    error_code="TENANT_NOT_FOUND"
                )
            
            # Convert to unified format
            tenant_info = self._convert_to_tenant_info(tenant_record)
            
            return ServiceResponse(
                success=True,
                data=tenant_info.to_dict()
            )
            
        except Exception as e:
            lambda_logger.error("Failed to get tenant", extra={
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return ServiceResponse(
                success=False,
                error="Internal server error",
                error_code="INTERNAL_ERROR"
            )
    
    async def validate_tenant_exists(self, tenant_id: str) -> bool:
        """
        Validate if tenant exists.
        UNIFIED VALIDATION for cross-service checks.
        """
        response = await self.get_tenant_by_id(tenant_id)
        return response.success
    
    def _convert_to_tenant_info(self, tenant_record: Dict[str, Any]) -> TenantInfo:
        """Convert database record to unified TenantInfo format."""
        from .models import TenantStatus, TenantPlan
        from datetime import datetime
        
        return TenantInfo(
            tenant_id=tenant_record['tenant_id'],
            name=tenant_record['name'],
            status=TenantStatus(tenant_record['status']),
            plan=TenantPlan(tenant_record['plan']),
            created_at=datetime.fromtimestamp(tenant_record['created_at']),
            trial_ends_at=datetime.fromtimestamp(tenant_record['trial_ends_at']) if tenant_record.get('trial_ends_at') else None,
            features=tenant_record.get('features')
        )


class ServiceCommunicationManager:
    """
    Central manager for all inter-service communication.
    SINGLE POINT OF CONTROL for service-to-service operations.
    """
    
    def __init__(self, db_client: DynamoDBClient):
        self.user_client = UserServiceClient(db_client)
        self.tenant_client = TenantServiceClient(db_client)
    
    async def sync_user_data(self, user_id: str, tenant_id: str, updated_data: Dict[str, Any]) -> ServiceResponse:
        """
        Synchronize user data across services.
        UNIFIED DATA SYNC to maintain consistency.
        """
        try:
            # Update in both auth and tenant patterns
            current_time = int(time.time())
            updated_data['updated_at'] = current_time
            
            # Update tenant service pattern
            tenant_user_item = {
                'pk': f'TENANT#{tenant_id}',
                'sk': f'USER#{user_id}',
                **updated_data
            }
            
            # Update auth service pattern  
            auth_user_item = {
                'pk': f'USER#{user_id}',
                'sk': 'PROFILE',
                **updated_data
            }
            
            # Perform atomic updates
            self.db.put_item(item=tenant_user_item, tenant_id=tenant_id)
            self.db.put_item(item=auth_user_item, tenant_id=tenant_id)
            
            return ServiceResponse(success=True, data={'synced': True})
            
        except Exception as e:
            lambda_logger.error("Failed to sync user data", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return ServiceResponse(
                success=False,
                error="Failed to sync user data",
                error_code="SYNC_ERROR"
            )


# Export main classes
__all__ = [
    'ServiceResponse',
    'UserServiceClient',
    'TenantServiceClient', 
    'ServiceCommunicationManager'
]
