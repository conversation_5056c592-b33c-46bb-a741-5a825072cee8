# shared/python/shared/logger.py
# Centralized logging configuration - Shared Layer

"""
Centralized logging configuration for the platform.
Provides structured logging with proper formatting and context.
"""

import json
import logging
import sys
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional, List

from .config import get_settings


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        settings = get_settings()
        
        # Base log structure
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "message": record.getMessage(),
            "service": getattr(settings, 'service_name', 'agent-scl'),
            "function": getattr(settings, 'function_name', 'unknown'),
            "environment": settings.environment,
            "region": settings.region,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in [
                "name", "msg", "args", "levelname", "levelno", "pathname",
                "filename", "module", "lineno", "funcName", "created",
                "msecs", "relativeCreated", "thread", "threadName",
                "processName", "process", "getMessage", "exc_info",
                "exc_text", "stack_info"
            ]:
                log_entry[key] = value
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)


class LambdaLogger:
    """Enhanced logger for Lambda functions."""
    
    def __init__(self, name: str = "agent-scl"):
        """Initialize the lambda_logger."""
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Add structured handler
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(StructuredFormatter())
        self.logger.addHandler(handler)
        
        # Prevent duplicate logs
        self.logger.propagate = False
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log debug message."""
        self.logger.debug(message, extra=extra or {})
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log info message."""
        self.logger.info(message, extra=extra or {})
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log warning message."""
        self.logger.warning(message, extra=extra or {})
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log error message."""
        self.logger.error(message, extra=extra or {})
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log critical message."""
        self.logger.critical(message, extra=extra or {})
    
    def get_current_timestamp(self) -> int:
        """Get current timestamp in milliseconds."""
        return int(time.time() * 1000)


# Global logger instances
logger = LambdaLogger("agent-scl")
lambda_logger = logger


def log_api_request(
    logger_instance: LambdaLogger,
    method: str,
    path: str,
    request_id: str,
    tenant_id: Optional[str] = None,
    user_id: Optional[str] = None,
    query_params: Optional[Dict[str, Any]] = None
):
    """Log API request details."""
    extra = {
        "event_type": "api_request",
        "method": method,
        "path": path,
        "request_id": request_id
    }
    
    if tenant_id:
        extra["tenant_id"] = tenant_id
    
    if user_id:
        extra["user_id"] = user_id
    
    if query_params:
        extra["query_params"] = query_params
    
    logger_instance.info(f"API Request: {method} {path}", extra=extra)


def log_api_response(
    logger_instance: LambdaLogger,
    method: str,
    path: str,
    status_code: int,
    duration_ms: float,
    tenant_id: Optional[str] = None,
    user_id: Optional[str] = None,
    request_id: Optional[str] = None
):
    """Log API response details."""
    extra = {
        "event_type": "api_response",
        "method": method,
        "path": path,
        "status_code": status_code,
        "duration_ms": duration_ms
    }
    
    if tenant_id:
        extra["tenant_id"] = tenant_id
    
    if user_id:
        extra["user_id"] = user_id
    
    if request_id:
        extra["request_id"] = request_id
    
    logger_instance.info(f"API Response: {method} {path} - {status_code}", extra=extra)


def log_security_event(
    logger_instance: LambdaLogger,
    event_type: str,
    event_category: str,
    user_id: Optional[str] = None,
    email: Optional[str] = None,
    tenant_id: Optional[str] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    request_id: Optional[str] = None,
    **kwargs
):
    """Log security-related events."""
    extra = {
        "event_type": "security_event",
        "security_event_type": event_type,
        "security_category": event_category
    }
    
    if user_id:
        extra["user_id"] = user_id
    
    if email:
        extra["email"] = email
    
    if tenant_id:
        extra["tenant_id"] = tenant_id
    
    if ip_address:
        extra["ip_address"] = ip_address
    
    if user_agent:
        extra["user_agent"] = user_agent
    
    if request_id:
        extra["request_id"] = request_id
    
    # Add any additional kwargs
    extra.update(kwargs)
    
    logger_instance.warning(f"Security Event: {event_type} - {event_category}", extra=extra)


def log_database_operation(
    logger_instance: LambdaLogger,
    operation: str,
    table_name: str,
    tenant_id: str,
    duration_ms: float,
    success: bool,
    item_count: Optional[int] = None,
    error: Optional[str] = None
):
    """Log database operation details."""
    extra = {
        "event_type": "database_operation",
        "operation": operation,
        "table_name": table_name,
        "tenant_id": tenant_id,
        "duration_ms": duration_ms,
        "success": success
    }
    
    if item_count is not None:
        extra["item_count"] = item_count
    
    if error:
        extra["error"] = error
    
    if success:
        logger_instance.debug(f"DB Operation: {operation} on {table_name}", extra=extra)
    else:
        logger_instance.error(f"DB Operation Failed: {operation} on {table_name}", extra=extra)


def log_business_event(
    logger_instance: LambdaLogger,
    event_type: str,
    entity_type: str,
    entity_id: str,
    tenant_id: str,
    user_id: Optional[str] = None,
    changes: Optional[Dict[str, Any]] = None,
    **kwargs
):
    """Log business logic events."""
    extra = {
        "event_type": "business_event",
        "business_event_type": event_type,
        "entity_type": entity_type,
        "entity_id": entity_id,
        "tenant_id": tenant_id
    }
    
    if user_id:
        extra["user_id"] = user_id
    
    if changes:
        extra["changes"] = changes
    
    # Add any additional kwargs
    extra.update(kwargs)
    
    logger_instance.info(f"Business Event: {event_type} - {entity_type}:{entity_id}", extra=extra)


def log_performance_metric(
    logger_instance: LambdaLogger,
    metric_name: str,
    value: float,
    unit: str = "ms",
    tenant_id: Optional[str] = None,
    user_id: Optional[str] = None,
    **kwargs
):
    """Log performance metrics."""
    extra = {
        "event_type": "performance_metric",
        "metric_name": metric_name,
        "value": value,
        "unit": unit
    }
    
    if tenant_id:
        extra["tenant_id"] = tenant_id
    
    if user_id:
        extra["user_id"] = user_id
    
    # Add any additional kwargs
    extra.update(kwargs)
    
    logger_instance.info(f"Performance Metric: {metric_name} = {value}{unit}", extra=extra)


def audit_log(
    action: str,
    resource_type: str,
    resource_id: Optional[str] = None,
    user_id: Optional[str] = None,
    tenant_id: Optional[str] = None,
    changes: Optional[Dict[str, Any]] = None,
    **kwargs
) -> None:
    """
    Log audit trail for compliance and security.

    Args:
        action: Action performed (create, update, delete, etc.)
        resource_type: Type of resource affected
        resource_id: ID of the resource
        user_id: ID of user performing action
        tenant_id: ID of tenant
        changes: Dictionary of changes made
        **kwargs: Additional audit data
    """
    extra = {
        'audit': True,
        'action': action,
        'resource_type': resource_type,
        'resource_id': resource_id,
        'user_id': user_id,
        'tenant_id': tenant_id,
        'changes': changes or {},
        'timestamp': datetime.utcnow().isoformat()
    }

    # Add any additional audit data
    extra.update(kwargs)

    lambda_logger.info(f"Audit: {action} {resource_type}", extra=extra)


def log_business_operation(
    logger_instance: LambdaLogger,
    operation: str,
    entity_type: str,
    entity_id: str = None,
    tenant_id: str = None,
    user_id: str = None,
    status: str = "started",
    details: Optional[Dict[str, Any]] = None
):
    """Log business operation with structured format."""
    extra = {
        "event_type": "business_operation",
        "operation": operation,
        "entity_type": entity_type,
        "entity_id": entity_id,
        "tenant_id": tenant_id,
        "user_id": user_id,
        "status": status,
        "timestamp": int(time.time() * 1000)
    }

    if details:
        extra["details"] = details

    logger_instance.info(f"Business operation: {operation} {entity_type}", extra=extra)


def log_validation_error(
    logger_instance: LambdaLogger,
    operation: str,
    validation_errors: List[str],
    request_data: Optional[Dict[str, Any]] = None,
    tenant_id: str = None,
    user_id: str = None
):
    """Log validation errors with structured format."""
    extra = {
        "event_type": "validation_error",
        "operation": operation,
        "validation_errors": validation_errors,
        "tenant_id": tenant_id,
        "user_id": user_id,
        "timestamp": int(time.time() * 1000)
    }

    if request_data:
        # Log only safe fields (no passwords, tokens, etc.)
        safe_data = {k: v for k, v in request_data.items()
                    if k not in ['password', 'token', 'secret', 'key']}
        extra["request_data"] = safe_data

    logger_instance.warning(f"Validation failed for {operation}", extra=extra)

