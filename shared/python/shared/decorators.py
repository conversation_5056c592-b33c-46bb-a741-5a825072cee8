# shared/python/shared/decorators.py
# Unified decorators for validation and error handling

"""
Unified decorators that combine validation, error handling, authentication,
and logging for consistent behavior across all handlers.
"""

import json
import time
from typing import Any, Dict, Optional, Type, Callable, List
from functools import wraps

from .logger import lambda_logger, log_api_request, log_api_response
from .responses import APIResponse
from .error_handler import ErrorHandler
from .validation_manager import ValidationManager
from .auth import require_auth
from .exceptions import ValidationException, AuthenticationException
from .metrics import metrics_manager


def with_standard_handling(
    service_name: str,
    operation_name: str,
    method: str = "POST",
    validator: Optional[Type] = None,
    auth_required: bool = True,
    path_params: Optional[List[str]] = None,
    query_params: Optional[List[str]] = None,
    required_headers: Optional[List[str]] = None,
    rate_limit_key: Optional[str] = None
):
    """
    Unified decorator for standard request handling.
    
    Combines:
    - Request validation
    - Authentication
    - Error handling
    - Logging
    - Metrics
    - CORS handling
    
    Args:
        service_name: Name of the service
        operation_name: Name of the operation
        method: HTTP method
        validator: Request body validator class
        auth_required: Whether authentication is required
        path_params: List of required path parameters
        query_params: List of required query parameters
        required_headers: List of required headers
        rate_limit_key: Key for rate limiting
    """
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(event: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
            start_time = lambda_logger.get_current_timestamp()
            request_id = event.get('requestContext', {}).get('requestId', 'unknown')
            path = event.get('path', 'unknown')
            
            # Initialize managers
            error_handler = ErrorHandler(service_name, operation_name)
            validation_manager = ValidationManager(service_name)
            
            try:
                # Handle CORS preflight
                if event.get('httpMethod') == 'OPTIONS':
                    return APIResponse.success(message="CORS preflight handled")
                
                # Log request
                log_api_request(
                    lambda_logger,
                    method,
                    path,
                    request_id=request_id,
                    user_agent=event.get('headers', {}).get('User-Agent'),
                    ip_address=event.get('requestContext', {}).get('identity', {}).get('sourceIp')
                )
                
                # Authentication
                auth_context = None
                if auth_required:
                    auth_context = require_auth(event)
                
                # Validation
                validated_data = {}
                
                # Validate request body
                if validator:
                    validated_data['body'] = validation_manager.validate_request_body(
                        event, validator, required=True
                    )
                
                # Validate path parameters
                if path_params:
                    validated_data['path_params'] = validation_manager.validate_path_parameters(
                        event, path_params
                    )
                
                # Validate query parameters
                if query_params:
                    validated_data['query_params'] = validation_manager.validate_query_parameters(
                        event, query_params
                    )
                
                # Validate headers
                if required_headers:
                    validated_data['headers'] = validation_manager.validate_headers(
                        event, required_headers
                    )
                
                # Add auth context to validated data
                if auth_context:
                    validated_data['auth'] = auth_context
                
                # Add event and context for backward compatibility
                validated_data['event'] = event
                validated_data['context'] = context
                
                # Call the actual handler
                result = func(validated_data)
                
                # Log successful response
                duration_ms = lambda_logger.get_current_timestamp() - start_time
                log_api_response(
                    lambda_logger,
                    method,
                    path,
                    200,
                    duration_ms,
                    request_id=request_id,
                    tenant_id=auth_context.tenant_id if auth_context else None,
                    user_id=auth_context.user_id if auth_context else None
                )
                
                return result
                
            except Exception as e:
                # Handle all exceptions through error handler
                return error_handler.handle_exception(
                    exception=e,
                    request_id=request_id,
                    method=method,
                    path=path,
                    context={
                        'service': service_name,
                        'operation': operation_name,
                        'event_source': event.get('source'),
                        'user_agent': event.get('headers', {}).get('User-Agent'),
                        'ip_address': event.get('requestContext', {}).get('identity', {}).get('sourceIp'),
                        'tenant_id': auth_context.tenant_id if auth_context else None,
                        'user_id': auth_context.user_id if auth_context else None
                    }
                )
            
            finally:
                # Ensure metrics are flushed
                try:
                    metrics_manager.flush_all_metrics()
                except Exception as e:
                    lambda_logger.warning("Failed to flush metrics", extra={'error': str(e)})
        
        return wrapper
    return decorator


def with_validation_only(validator: Type, required: bool = True):
    """
    Simple validation decorator for handlers that need custom error handling.
    
    Args:
        validator: Request body validator class
        required: Whether request body is required
    """
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(event: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
            # Extract and validate body
            body_str = event.get('body', '{}')
            
            if not body_str and required:
                raise ValidationException(
                    "Request body is required",
                    error_code="MISSING_BODY"
                )
            
            try:
                body = json.loads(body_str) if body_str else {}
            except json.JSONDecodeError:
                raise ValidationException(
                    "Invalid JSON format in request body",
                    error_code="INVALID_JSON"
                )
            
            # Validate using validator class
            if hasattr(validator, 'validate'):
                result = validator.validate(body)
                if not result.is_valid:
                    raise ValidationException(
                        "Request validation failed",
                        error_code="VALIDATION_FAILED",
                        validation_errors=[
                            {'field': 'request', 'message': error}
                            for error in result.errors
                        ]
                    )
                validated_body = result.cleaned_data
            else:
                # Fallback for other validator types
                try:
                    validated_instance = validator(**body)
                    validated_body = validated_instance.dict() if hasattr(validated_instance, 'dict') else validated_instance
                except Exception as e:
                    raise ValidationException(
                        f"Validation failed: {str(e)}",
                        error_code="VALIDATION_FAILED"
                    )
            
            # Call handler with validated data
            return func(event, context, validated_body)
        
        return wrapper
    return decorator


def with_auth_only(func: Callable) -> Callable:
    """Simple authentication decorator."""
    @wraps(func)
    def wrapper(event: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
        auth_context = require_auth(event)
        return func(event, context, auth_context)
    
    return wrapper
