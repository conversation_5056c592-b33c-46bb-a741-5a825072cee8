"""
Unified Database Schema Patterns for Agent SCL Platform.
This module defines standardized DynamoDB key patterns and access patterns
to ensure consistency across all services.

CRITICAL: This is the single source of truth for all database schemas.
All services MUST use these patterns instead of implementing their own.
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum


class EntityType(Enum):
    """Standardized entity types for DynamoDB records."""
    USER = "USER"
    TENANT = "TENANT"
    INVITATION = "INVITATION"
    ACTIVITY = "ACTIVITY"
    SUBSCRIPTION = "SUBSCRIPTION"
    USAGE = "USAGE"
    EXPORT = "EXPORT"


@dataclass
class KeyPattern:
    """Standardized key pattern for DynamoDB operations."""
    pk: str
    sk: str
    gsi1pk: Optional[str] = None
    gsi1sk: Optional[str] = None
    gsi2pk: Optional[str] = None
    gsi2sk: Optional[str] = None
    entity_type: Optional[str] = None


class DatabaseSchemas:
    """
    UNIFIED DATABASE SCHEMAS - Single source of truth for all key patterns.
    All services MUST use these patterns for consistency.
    """
    
    @staticmethod
    def user_profile_keys(user_id: str, tenant_id: str, email: str) -> KeyPattern:
        """
        UNIFIED USER PROFILE SCHEMA
        Primary access: Get user by ID within tenant
        GSI1: Get user across tenants (for auth)
        GSI2: Get user by email within tenant
        """
        return KeyPattern(
            pk=f'TENANT#{tenant_id}',
            sk=f'USER#{user_id}',
            gsi1pk=f'USER#{user_id}',
            gsi1sk='PROFILE',
            gsi2pk=f'EMAIL#{email}',
            gsi2sk=f'TENANT#{tenant_id}',
            entity_type=EntityType.USER.value
        )
    
    @staticmethod
    def tenant_profile_keys(tenant_id: str) -> KeyPattern:
        """
        UNIFIED TENANT PROFILE SCHEMA
        Primary access: Get tenant by ID
        """
        return KeyPattern(
            pk=f'TENANT#{tenant_id}',
            sk='PROFILE',
            entity_type=EntityType.TENANT.value
        )
    
    @staticmethod
    def user_invitation_keys(tenant_id: str, invitation_id: str, email: str) -> KeyPattern:
        """
        UNIFIED USER INVITATION SCHEMA
        Primary access: Get invitation by ID within tenant
        GSI2: Get invitations by email
        """
        return KeyPattern(
            pk=f'TENANT#{tenant_id}',
            sk=f'INVITATION#{invitation_id}',
            gsi2pk=f'EMAIL#{email}',
            gsi2sk=f'INVITATION#{invitation_id}',
            entity_type=EntityType.INVITATION.value
        )
    
    @staticmethod
    def user_activity_keys(tenant_id: str, user_id: str, timestamp: int, activity_id: str) -> KeyPattern:
        """
        UNIFIED USER ACTIVITY SCHEMA
        Primary access: Get activities for user within tenant (sorted by timestamp)
        GSI1: Get activities by user across time
        """
        return KeyPattern(
            pk=f'TENANT#{tenant_id}',
            sk=f'ACTIVITY#{user_id}#{timestamp}#{activity_id}',
            gsi1pk=f'USER#{user_id}',
            gsi1sk=f'ACTIVITY#{timestamp}#{activity_id}',
            entity_type=EntityType.ACTIVITY.value
        )
    
    @staticmethod
    def tenant_usage_keys(tenant_id: str, period: str) -> KeyPattern:
        """
        UNIFIED TENANT USAGE SCHEMA
        Primary access: Get usage for tenant by period (daily, monthly, etc.)
        """
        return KeyPattern(
            pk=f'TENANT#{tenant_id}',
            sk=f'USAGE#{period}',
            entity_type=EntityType.USAGE.value
        )
    
    @staticmethod
    def data_export_keys(tenant_id: str, export_id: str, created_by: str) -> KeyPattern:
        """
        UNIFIED DATA EXPORT SCHEMA
        Primary access: Get export by ID within tenant
        GSI1: Get exports by creator
        """
        return KeyPattern(
            pk=f'TENANT#{tenant_id}',
            sk=f'EXPORT#{export_id}',
            gsi1pk=f'USER#{created_by}',
            gsi1sk=f'EXPORT#{export_id}',
            entity_type=EntityType.EXPORT.value
        )


class QueryPatterns:
    """
    UNIFIED QUERY PATTERNS - Standardized access patterns for common operations.
    All services MUST use these patterns for consistent data access.
    """
    
    @staticmethod
    def get_user_profile(tenant_id: str, user_id: str) -> Dict[str, str]:
        """Get user profile within tenant."""
        return {
            'pk': f'TENANT#{tenant_id}',
            'sk': f'USER#{user_id}'
        }
    
    @staticmethod
    def get_user_by_email(email: str, tenant_id: str) -> Dict[str, Any]:
        """Get user by email within tenant using GSI2."""
        return {
            'gsi_name': 'GSI2',
            'pk': f'EMAIL#{email}',
            'sk': f'TENANT#{tenant_id}'
        }
    
    @staticmethod
    def get_user_across_tenants(user_id: str) -> Dict[str, Any]:
        """Get user across all tenants using GSI1."""
        return {
            'gsi_name': 'GSI1',
            'pk': f'USER#{user_id}',
            'sk': 'PROFILE'
        }
    
    @staticmethod
    def list_tenant_users(tenant_id: str) -> Dict[str, Any]:
        """List all users in tenant."""
        return {
            'pk': f'TENANT#{tenant_id}',
            'sk_begins_with': 'USER#'
        }
    
    @staticmethod
    def list_user_activities(tenant_id: str, user_id: str, limit: int = 50) -> Dict[str, Any]:
        """List user activities within tenant."""
        return {
            'pk': f'TENANT#{tenant_id}',
            'sk_begins_with': f'ACTIVITY#{user_id}#',
            'limit': limit,
            'scan_index_forward': False  # Latest first
        }
    
    @staticmethod
    def list_pending_invitations(tenant_id: str) -> Dict[str, Any]:
        """List pending invitations for tenant."""
        return {
            'pk': f'TENANT#{tenant_id}',
            'sk_begins_with': 'INVITATION#'
        }


class DataMigrationPatterns:
    """
    Patterns for migrating existing data to unified schemas.
    Use these to standardize existing inconsistent data.
    """
    
    @staticmethod
    def migrate_auth_user_to_unified(auth_user_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate auth service user record to unified schema.
        Handles field name differences and adds missing fields.
        """
        # Extract name components
        name = auth_user_record.get('name', '')
        name_parts = name.split(' ', 1) if name else ['', '']
        first_name = auth_user_record.get('first_name') or name_parts[0]
        last_name = auth_user_record.get('last_name') or (name_parts[1] if len(name_parts) > 1 else '')
        
        # Create unified record
        unified_record = {
            'pk': f"TENANT#{auth_user_record['tenant_id']}",
            'sk': f"USER#{auth_user_record['user_id']}",
            'gsi1pk': f"USER#{auth_user_record['user_id']}",
            'gsi1sk': 'PROFILE',
            'gsi2pk': f"EMAIL#{auth_user_record['email']}",
            'gsi2sk': f"TENANT#{auth_user_record['tenant_id']}",
            'entity_type': EntityType.USER.value,
            
            # Core fields
            'user_id': auth_user_record['user_id'],
            'tenant_id': auth_user_record['tenant_id'],
            'email': auth_user_record['email'],
            'first_name': first_name,
            'last_name': last_name,
            'role': auth_user_record['role'],
            'status': auth_user_record['status'],
            
            # Auth-specific fields
            'password_hash': auth_user_record.get('password_hash'),
            'email_verified': auth_user_record.get('email_verified', False),
            'email_verification_token': auth_user_record.get('email_verification_token'),
            'last_login_at': auth_user_record.get('last_login_at'),
            'failed_login_attempts': auth_user_record.get('failed_login_attempts', 0),
            'locked_until': auth_user_record.get('locked_until'),
            
            # Timestamps
            'created_at': auth_user_record.get('created_at'),
            'updated_at': auth_user_record.get('updated_at'),
        }
        
        return unified_record
    
    @staticmethod
    def migrate_tenant_user_to_unified(tenant_user_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate tenant service user record to unified schema.
        Ensures consistent field naming and structure.
        """
        # Tenant records should already have first_name/last_name
        unified_record = {
            'pk': f"TENANT#{tenant_user_record['tenant_id']}",
            'sk': f"USER#{tenant_user_record['user_id']}",
            'gsi1pk': f"USER#{tenant_user_record['user_id']}",
            'gsi1sk': 'PROFILE',
            'gsi2pk': f"EMAIL#{tenant_user_record['email']}",
            'gsi2sk': f"TENANT#{tenant_user_record['tenant_id']}",
            'entity_type': EntityType.USER.value,
            
            # Copy all existing fields
            **tenant_user_record
        }
        
        return unified_record


# Export main classes
__all__ = [
    'EntityType',
    'KeyPattern',
    'DatabaseSchemas',
    'QueryPatterns',
    'DataMigrationPatterns'
]
