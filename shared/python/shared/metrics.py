# shared/python/shared/metrics.py
# Custom metrics and observability system - Shared Layer

"""
Custom metrics system that provides comprehensive observability
for business metrics, performance monitoring, and operational insights.
"""

import time
import boto3
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from functools import wraps

from .logger import lambda_logger
from .config import get_settings


class MetricUnit(Enum):
    """CloudWatch metric units."""
    COUNT = "Count"
    SECONDS = "Seconds"
    MILLISECONDS = "Milliseconds"
    BYTES = "Bytes"
    KILOBYTES = "Kilobytes"
    MEGABYTES = "Megabytes"
    PERCENT = "Percent"
    COUNT_PER_SECOND = "Count/Second"


class MetricType(Enum):
    """Types of metrics."""
    BUSINESS = "business"
    PERFORMANCE = "performance"
    SECURITY = "security"
    OPERATIONAL = "operational"
    USER_BEHAVIOR = "user_behavior"


@dataclass
class MetricData:
    """Represents a metric data point."""
    name: str
    value: Union[int, float]
    unit: MetricUnit
    metric_type: MetricType
    dimensions: Dict[str, str]
    timestamp: Optional[datetime] = None
    tenant_id: Optional[str] = None


class MetricsCollector:
    """Collects and sends metrics to CloudWatch."""
    
    def __init__(self):
        """Initialize metrics collector."""
        self.settings = get_settings()
        self.cloudwatch = boto3.client('cloudwatch', region_name=self.settings.region)
        self.namespace = f"AgentSCL/{self.settings.environment}"
        self.metrics_buffer: List[MetricData] = []
        self.max_buffer_size = 20  # CloudWatch limit
    
    def record_metric(
        self,
        name: str,
        value: Union[int, float],
        unit: MetricUnit = MetricUnit.COUNT,
        metric_type: MetricType = MetricType.OPERATIONAL,
        dimensions: Optional[Dict[str, str]] = None,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record a metric."""
        metric = MetricData(
            name=name,
            value=value,
            unit=unit,
            metric_type=metric_type,
            dimensions=dimensions or {},
            timestamp=datetime.utcnow(),
            tenant_id=tenant_id
        )
        
        self.metrics_buffer.append(metric)
        
        # Auto-flush if buffer is full
        if len(self.metrics_buffer) >= self.max_buffer_size:
            self.flush_metrics()
    
    def flush_metrics(self) -> None:
        """Send buffered metrics to CloudWatch."""
        if not self.metrics_buffer:
            return
        
        try:
            metric_data = []
            
            for metric in self.metrics_buffer:
                # Prepare dimensions
                dimensions = [
                    {'Name': 'Environment', 'Value': self.settings.environment},
                    {'Name': 'MetricType', 'Value': metric.metric_type.value}
                ]
                
                # Add tenant dimension if available
                if metric.tenant_id:
                    dimensions.append({'Name': 'TenantId', 'Value': metric.tenant_id})
                
                # Add custom dimensions
                for key, value in metric.dimensions.items():
                    dimensions.append({'Name': key, 'Value': str(value)})
                
                metric_data.append({
                    'MetricName': metric.name,
                    'Value': metric.value,
                    'Unit': metric.unit.value,
                    'Timestamp': metric.timestamp,
                    'Dimensions': dimensions
                })
            
            # Send to CloudWatch
            self.cloudwatch.put_metric_data(
                Namespace=self.namespace,
                MetricData=metric_data
            )
            
            lambda_logger.debug(f"Sent {len(metric_data)} metrics to CloudWatch")
            
            # Clear buffer
            self.metrics_buffer.clear()
            
        except Exception as e:
            lambda_logger.error(f"Failed to send metrics to CloudWatch: {str(e)}")


class BusinessMetrics:
    """Business-specific metrics."""
    
    def __init__(self, collector: MetricsCollector):
        self.collector = collector
    
    def record_user_registration(self, tenant_id: str, plan: str = "FREE") -> None:
        """Record user registration."""
        self.collector.record_metric(
            name="UserRegistration",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={"Plan": plan},
            tenant_id=tenant_id
        )
    
    def record_subscription_created(self, tenant_id: str, plan: str, amount: float) -> None:
        """Record subscription creation."""
        self.collector.record_metric(
            name="SubscriptionCreated",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={"Plan": plan},
            tenant_id=tenant_id
        )
        
        self.collector.record_metric(
            name="Revenue",
            value=amount,
            unit=MetricUnit.COUNT,
            metric_type=MetricType.BUSINESS,
            dimensions={"Plan": plan},
            tenant_id=tenant_id
        )
    
    def record_api_usage(self, tenant_id: str, endpoint: str, method: str) -> None:
        """Record API usage."""
        self.collector.record_metric(
            name="APIUsage",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={"Endpoint": endpoint, "Method": method},
            tenant_id=tenant_id
        )

    def record_user_login(self, tenant_id: str) -> None:
        """Record user login."""
        self.collector.record_metric(
            name="UserLogin",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_user_registration(self, tenant_id: str, plan: str) -> None:
        """Record user registration."""
        self.collector.record_metric(
            name="UserRegistration",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={"Plan": plan},
            tenant_id=tenant_id
        )

    def record_token_refresh(self, tenant_id: str) -> None:
        """Record token refresh."""
        self.collector.record_metric(
            name="TokenRefresh",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_user_logout(self, tenant_id: str) -> None:
        """Record user logout."""
        self.collector.record_metric(
            name="UserLogout",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_email_verification(self, tenant_id: str) -> None:
        """Record email verification."""
        self.collector.record_metric(
            name="EmailVerification",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_password_reset_request(self, tenant_id: str) -> None:
        """Record password reset request."""
        self.collector.record_metric(
            name="PasswordResetRequest",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_registration_activation(self, tenant_id: str) -> None:
        """Record registration activation."""
        self.collector.record_metric(
            name="RegistrationActivation",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_token_revocation(self, tenant_id: str) -> None:
        """Record token revocation."""
        self.collector.record_metric(
            name="TokenRevocation",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_verification_resend(self, tenant_id: str) -> None:
        """Record verification email resend."""
        self.collector.record_metric(
            name="VerificationResend",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_password_reset(self, tenant_id: str) -> None:
        """Record password reset."""
        self.collector.record_metric(
            name="PasswordReset",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_token_validation(self, tenant_id: str) -> None:
        """Record token validation."""
        self.collector.record_metric(
            name="TokenValidation",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_token_refresh(self, tenant_id: str) -> None:
        """Record token refresh."""
        self.collector.record_metric(
            name="TokenRefresh",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_authorization(self, tenant_id: str) -> None:
        """Record authorization event."""
        self.collector.record_metric(
            name="Authorization",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )

    def record_logout(self, tenant_id: str) -> None:
        """Record user logout."""
        self.collector.record_metric(
            name="UserLogout",
            value=1,
            metric_type=MetricType.BUSINESS,
            dimensions={},
            tenant_id=tenant_id
        )


class PerformanceMetrics:
    """Performance-specific metrics."""
    
    def __init__(self, collector: MetricsCollector):
        self.collector = collector
    
    def record_function_duration(
        self,
        function_name: str,
        duration_ms: float,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record function execution duration."""
        self.collector.record_metric(
            name="FunctionDuration",
            value=duration_ms,
            unit=MetricUnit.MILLISECONDS,
            metric_type=MetricType.PERFORMANCE,
            dimensions={"FunctionName": function_name},
            tenant_id=tenant_id
        )
    
    def record_database_operation(
        self,
        operation: str,
        duration_ms: float,
        success: bool,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record database operation metrics."""
        self.collector.record_metric(
            name="DatabaseOperationDuration",
            value=duration_ms,
            unit=MetricUnit.MILLISECONDS,
            metric_type=MetricType.PERFORMANCE,
            dimensions={"Operation": operation, "Success": str(success)},
            tenant_id=tenant_id
        )
    
    def record_cache_hit_rate(
        self,
        cache_type: str,
        hit_rate: float,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record cache hit rate."""
        self.collector.record_metric(
            name="CacheHitRate",
            value=hit_rate,
            unit=MetricUnit.PERCENT,
            metric_type=MetricType.PERFORMANCE,
            dimensions={"CacheType": cache_type},
            tenant_id=tenant_id
        )


class SecurityMetrics:
    """Security-specific metrics."""
    
    def __init__(self, collector: MetricsCollector):
        self.collector = collector
    
    def record_successful_login(
        self,
        user_id: str,
        tenant_id: str,
        role: str
    ) -> None:
        """Record successful login."""
        self.collector.record_metric(
            name="SuccessfulLogin",
            value=1,
            metric_type=MetricType.SECURITY,
            dimensions={"Role": role},
            tenant_id=tenant_id
        )
    
    def record_failed_login(
        self,
        email: str,
        reason: str,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record failed login attempt."""
        self.collector.record_metric(
            name="FailedLogin",
            value=1,
            metric_type=MetricType.SECURITY,
            dimensions={"Reason": reason},
            tenant_id=tenant_id
        )
    
    def record_security_event(
        self,
        event_type: str,
        severity: str,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record security event."""
        self.collector.record_metric(
            name="SecurityEvent",
            value=1,
            metric_type=MetricType.SECURITY,
            dimensions={"EventType": event_type, "Severity": severity},
            tenant_id=tenant_id
        )


class MetricsManager:
    """Central metrics manager."""
    
    def __init__(self):
        """Initialize metrics manager."""
        self.collector = MetricsCollector()
        self.business = BusinessMetrics(self.collector)
        self.performance = PerformanceMetrics(self.collector)
        self.security = SecurityMetrics(self.collector)
    
    def flush_all_metrics(self) -> None:
        """Flush all buffered metrics."""
        self.collector.flush_metrics()

    def record_rate_limit_check(
        self,
        client_ip: str,
        limit: int,
        allowed: bool,
        tenant_id: Optional[str] = None
    ) -> None:
        """Record rate limit check."""
        self.collector.record_metric(
            name="RateLimitCheck",
            value=1,
            metric_type=MetricType.SECURITY,
            dimensions={
                "ClientIP": client_ip[:10] + "..." if len(client_ip) > 10 else client_ip,  # Truncate for privacy
                "Limit": str(limit),
                "Allowed": str(allowed)
            },
            tenant_id=tenant_id
        )


# Global metrics manager
metrics_manager = MetricsManager()


def measure_performance(function_name: str):
    """Decorator to measure function performance."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                # Extract tenant_id if available
                tenant_id = None
                if args and hasattr(args[0], 'get'):
                    # Try to get tenant_id from event
                    auth_context = args[0].get('auth_context')
                    if auth_context and hasattr(auth_context, 'tenant_id'):
                        tenant_id = auth_context.tenant_id
                
                metrics_manager.performance.record_function_duration(
                    function_name=function_name,
                    duration_ms=duration_ms,
                    tenant_id=tenant_id
                )
                
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                metrics_manager.performance.record_function_duration(
                    function_name=f"{function_name}_error",
                    duration_ms=duration_ms
                )
                raise
        
        return wrapper
    return decorator
