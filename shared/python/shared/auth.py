# shared/python/shared/auth.py
# Authentication and authorization utilities - Shared Layer

"""
Authentication and authorization utilities for the platform.
Handles JWT tokens, password hashing, and user context.
"""

import jwt
import hashlib
import secrets
import re
import base64
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, Tuple, List

from .config import get_settings
from .exceptions import (
    AuthenticationException,
    AuthorizationException,
    InvalidTokenException,
    TokenExpiredException,
    WeakPasswordException,
    InvalidCredentialsException
)
from .logger import lambda_logger, log_security_event


class PasswordManager:
    """Password hashing and validation utilities compatible with Auth Service."""

    def __init__(self):
        self.settings = get_settings()

    def hash_password(self, password: str, user_id: str) -> str:
        """
        Hash a password using SHA256 with user_id as salt.
        Compatible with Auth Service implementation.
        """
        return hashlib.sha256(f"{password}{user_id}".encode('utf-8')).hexdigest()

    def verify_password(self, plain_password: str, hashed_password: str, user_id: str) -> bool:
        """
        Verify a password against its hash using user_id as salt.
        Compatible with Auth Service implementation.
        """
        expected_hash = self.hash_password(plain_password, user_id)
        return expected_hash == hashed_password
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """Validate password strength according to security requirements."""
        errors = []
        
        # Check minimum length
        if len(password) < self.settings.password_min_length:
            errors.append(f"Password must be at least {self.settings.password_min_length} characters long")
        
        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        
        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        
        # Check for digit
        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")
        
        # Check for special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")
        
        # Check for common patterns
        common_patterns = [
            r'123456',
            r'password',
            r'qwerty',
            r'abc123'
        ]
        
        for pattern in common_patterns:
            if re.search(pattern, password.lower()):
                errors.append("Password contains common patterns and is not secure")
                break
        
        return len(errors) == 0, errors
    
    def generate_secure_password(self, length: int = 12) -> str:
        """Generate a secure random password."""
        import string
        import random
        
        # Ensure we have at least one character from each required category
        password = [
            random.choice(string.ascii_uppercase),
            random.choice(string.ascii_lowercase),
            random.choice(string.digits),
            random.choice('!@#$%^&*(),.?":{}|<>')
        ]
        
        # Fill the rest with random characters
        all_chars = string.ascii_letters + string.digits + '!@#$%^&*(),.?":{}|<>'
        for _ in range(length - 4):
            password.append(random.choice(all_chars))
        
        # Shuffle the password
        random.shuffle(password)

        return ''.join(password)

    def generate_secure_token(self, length: int = 32) -> str:
        """Generate a secure random token using URL-safe base64 encoding."""
        import secrets
        import base64

        # Generate random bytes
        random_bytes = secrets.token_bytes(length)

        # Encode as URL-safe base64
        token = base64.urlsafe_b64encode(random_bytes).decode('utf-8')

        # Remove padding characters
        return token.rstrip('=')


class JWTManager:
    """JWT token management utilities."""
    
    def __init__(self):
        self.settings = get_settings()
        self.secret_key = self._get_jwt_secret()
        self.algorithm = "HS256"
        self.access_token_expiry = self.settings.jwt_access_token_expiry  # seconds
        self.refresh_token_expiry = self.settings.jwt_refresh_token_expiry  # seconds
    
    def _get_jwt_secret(self) -> str:
        """Get JWT secret from AWS Secrets Manager with secure fallback."""
        try:
            from .secrets_manager import secrets_client
            return secrets_client.get_secret(f"{self.settings.project_name}/{self.settings.environment}/jwt-secret")
        except Exception as e:
            lambda_logger.error("Failed to get JWT secret", extra={'error': str(e)})

            # Secure fallback: require environment variable
            import os
            jwt_secret = os.getenv('JWT_SECRET')

            if not jwt_secret:
                lambda_logger.critical("No JWT secret available - neither AWS Secrets Manager nor JWT_SECRET env var")
                raise RuntimeError(
                    "JWT secret not available. Set JWT_SECRET environment variable or configure AWS Secrets Manager."
                )

            if jwt_secret == 'fallback-secret-key' or len(jwt_secret) < 32:
                lambda_logger.critical("Insecure JWT secret detected")
                raise RuntimeError(
                    "JWT secret is too weak. Use a strong secret (>32 characters) or configure AWS Secrets Manager."
                )

            lambda_logger.warning("Using JWT_SECRET environment variable - not recommended for production")
            return jwt_secret

    def _is_token_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted."""
        try:
            import hashlib

            # Hash token for lookup
            token_hash = hashlib.sha256(token.encode()).hexdigest()

            # Try to check blacklist in database (if available)
            try:
                from .database import db_client
                result = db_client.get_item(
                    pk=f'BLACKLIST#{token_hash}',
                    sk='TOKEN'
                )
                return result is not None
            except ImportError:
                # Database not available in this context, skip blacklist check
                return False

        except Exception as e:
            lambda_logger.error("Failed to check token blacklist", extra={'error': str(e)})
            # Fail secure: if we can't check blacklist, allow token (but log)
            return False

    def revoke_token(self, token: str, reason: str = "manual_revocation") -> bool:
        """Revoke a token by adding it to blacklist."""
        try:
            import hashlib
            import time

            # Hash token for storage
            token_hash = hashlib.sha256(token.encode()).hexdigest()

            # Try to decode token to get expiration (for cleanup)
            try:
                payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm], options={"verify_exp": False})
                exp = payload.get('exp', int(time.time()) + 86400)  # Default 24h if no exp
            except:
                exp = int(time.time()) + 86400  # Default 24h for invalid tokens

            # Try to add to blacklist (if database available)
            try:
                from .database import db_client
                db_client.put_item({
                    'pk': f'BLACKLIST#{token_hash}',
                    'sk': 'TOKEN',
                    'token_hash': token_hash,
                    'revoked_at': int(time.time()),
                    'expires_at': exp,
                    'reason': reason,
                    'ttl': exp  # DynamoDB TTL for automatic cleanup
                })

                lambda_logger.info("Token revoked successfully", extra={
                    'token_hash': token_hash[:16] + "...",  # Log partial hash for security
                    'reason': reason
                })
                return True

            except ImportError:
                lambda_logger.warning("Database not available for token blacklisting", extra={'reason': reason})
                return False

        except Exception as e:
            lambda_logger.error("Failed to revoke token", extra={'error': str(e), 'reason': reason})
            return False
    
    def generate_access_token(
        self,
        user_id: str,
        email: str,
        tenant_id: str,
        role: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate an access token."""
        now = datetime.now(timezone.utc)
        payload = {
            'user_id': user_id,
            'email': email,
            'tenant_id': tenant_id,
            'role': role,
            'type': 'access',
            'iat': now,
            'exp': now + timedelta(seconds=self.access_token_expiry),
            'iss': self.settings.project_name,
            'aud': 'agent-scl-api'
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        lambda_logger.debug("Access token generated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'expires_in': self.access_token_expiry
        })
        
        return token
    
    def generate_refresh_token(
        self,
        user_id: str,
        tenant_id: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate a refresh token."""
        now = datetime.now(timezone.utc)
        payload = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'type': 'refresh',
            'iat': now,
            'exp': now + timedelta(seconds=self.refresh_token_expiry),
            'iss': self.settings.project_name,
            'aud': 'agent-scl-api',
            'jti': secrets.token_urlsafe(32)  # Unique token ID
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        lambda_logger.debug("Refresh token generated", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'expires_in': self.refresh_token_expiry
        })
        
        return token
    
    def verify_token(self, token: str, token_type: str = 'access') -> Dict[str, Any]:
        """Verify and decode a JWT token with blacklist checking."""
        try:
            # Check if token is blacklisted first (before expensive JWT verification)
            if self._is_token_blacklisted(token):
                lambda_logger.warning("Blacklisted token attempted", extra={'token_type': token_type})
                raise InvalidTokenException("Token has been revoked")

            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                audience='agent-scl-api',
                issuer=self.settings.project_name
            )

            # Verify token type
            if payload.get('type') != token_type:
                raise InvalidTokenException(f"Invalid token type. Expected {token_type}")

            lambda_logger.debug("Token verified successfully", extra={
                'user_id': payload.get('user_id'),
                'tenant_id': payload.get('tenant_id'),
                'token_type': token_type
            })

            return payload
            
        except jwt.ExpiredSignatureError:
            lambda_logger.warning("Token expired", extra={'token_type': token_type})
            raise TokenExpiredException("Token has expired")
        
        except jwt.InvalidTokenError as e:
            lambda_logger.warning("Invalid token", extra={
                'token_type': token_type,
                'error': str(e)
            })
            raise InvalidTokenException("Invalid token")

    def extract_user_context(self, token: str) -> Optional['AuthContext']:
        """Extract user context from JWT token."""
        try:
            payload = self.verify_token(token, 'access')

            return AuthContext(
                user_id=payload.get('user_id'),
                email=payload.get('email'),
                tenant_id=payload.get('tenant_id'),
                role=payload.get('role'),
                token_payload=payload
            )
        except (InvalidTokenException, TokenExpiredException):
            return None

    def refresh_access_token(self, refresh_token: str) -> Tuple[str, str]:
        """Generate new access token using refresh token."""
        # Verify refresh token
        payload = self.verify_token(refresh_token, 'refresh')
        
        user_id = payload['user_id']
        tenant_id = payload['tenant_id']
        
        # Generate new tokens
        new_access_token = self.generate_access_token(
            user_id=user_id,
            email=payload.get('email', ''),
            tenant_id=tenant_id,
            role=payload.get('role', 'MEMBER')
        )
        
        new_refresh_token = self.generate_refresh_token(
            user_id=user_id,
            tenant_id=tenant_id
        )
        
        lambda_logger.info("Tokens refreshed", extra={
            'user_id': user_id,
            'tenant_id': tenant_id
        })
        
        return new_access_token, new_refresh_token


class AuthContext:
    """Authentication context for requests."""
    
    def __init__(
        self,
        user_id: str,
        email: str,
        tenant_id: str,
        role: str,
        token_payload: Dict[str, Any]
    ):
        self.user_id = user_id
        self.email = email
        self.tenant_id = tenant_id
        self.role = role
        self.token_payload = token_payload
        self.is_authenticated = True
    
    def has_role(self, required_role: str) -> bool:
        """Check if user has required role using unified hierarchy."""
        from .models import UserRole, ROLE_HIERARCHY

        try:
            user_role = UserRole(self.role)
            required_role_enum = UserRole(required_role)
            return user_role.can_access(required_role_enum)
        except ValueError:
            # Fallback for invalid roles
            return False
    
    def can_access_tenant(self, tenant_id: str) -> bool:
        """Check if user can access specific tenant."""
        return self.tenant_id == tenant_id
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert auth context to dictionary."""
        return {
            'user_id': self.user_id,
            'email': self.email,
            'tenant_id': self.tenant_id,
            'role': self.role,
            'is_authenticated': self.is_authenticated
        }


# Global instances
password_manager = PasswordManager()
# jwt_manager is now lazy-loaded via get_jwt_manager()


def require_auth(func):
    """Decorator to require authentication for a function."""
    def wrapper(event, context):
        # Extract token from Authorization header
        headers = event.get('headers', {})
        auth_header = headers.get('Authorization') or headers.get('authorization')
        
        if not auth_header:
            return {
                'statusCode': 401,
                'body': '{"error": "Missing Authorization header"}'
            }
        
        if not auth_header.startswith('Bearer '):
            return {
                'statusCode': 401,
                'body': '{"error": "Invalid Authorization header format"}'
            }
        
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        try:
            payload = jwt_manager.verify_token(token)
            
            # Create auth context
            auth_context = AuthContext(
                user_id=payload['user_id'],
                email=payload['email'],
                tenant_id=payload['tenant_id'],
                role=payload['role'],
                token_payload=payload
            )
            
            # Add auth context to event
            event['auth_context'] = auth_context
            
            return func(event, context)
            
        except (InvalidTokenException, TokenExpiredException) as e:
            lambda_logger.warning("Authentication failed", extra={'error': str(e)})
            return {
                'statusCode': 401,
                'body': f'{{"error": "{str(e)}"}}'
            }
    
    return wrapper


def require_role(required_role: str):
    """Decorator to require specific role for a function."""
    def decorator(func):
        def wrapper(event, context):
            auth_context = event.get('auth_context')
            
            if not auth_context:
                return {
                    'statusCode': 401,
                    'body': '{"error": "Authentication required"}'
                }
            
            if not auth_context.has_role(required_role):
                lambda_logger.warning("Authorization failed", extra={
                    'user_id': auth_context.user_id,
                    'user_role': auth_context.role,
                    'required_role': required_role
                })
                return {
                    'statusCode': 403,
                    'body': '{"error": "Insufficient permissions"}'
                }
            
            return func(event, context)
        
        return wrapper
    return decorator


def get_auth_context(event: Dict[str, Any]) -> Optional[AuthContext]:
    """Extract authentication context from Lambda event."""
    try:
        # Check if auth_context is already in event (from authorizer)
        if 'auth_context' in event:
            return event['auth_context']

        # Extract token from Authorization header
        headers = event.get('headers', {})
        auth_header = headers.get('Authorization') or headers.get('authorization')

        if not auth_header:
            return None

        if not auth_header.startswith('Bearer '):
            return None

        token = auth_header[7:]  # Remove 'Bearer ' prefix

        # Verify token
        payload = jwt_manager.verify_token(token)

        # Create auth context
        auth_context = AuthContext(
            user_id=payload['user_id'],
            email=payload['email'],
            tenant_id=payload['tenant_id'],
            role=payload['role'],
            token_payload=payload
        )

        return auth_context

    except Exception as e:
        lambda_logger.warning("Failed to extract auth context", extra={'error': str(e)})
        return None


# Global instances with lazy initialization
password_manager = PasswordManager()

# JWT manager will be initialized on first use
_jwt_manager = None

def get_jwt_manager():
    """Get JWT manager instance with lazy initialization."""
    global _jwt_manager
    if _jwt_manager is None:
        # Use the new unified JWT manager
        from .jwt_manager import get_jwt_manager as get_unified_jwt_manager
        _jwt_manager = get_unified_jwt_manager()
    return _jwt_manager

# Backward compatibility - jwt_manager will be lazy-loaded when accessed
# jwt_manager = get_jwt_manager()  # Commented to avoid circular import

# Create a property-like access to jwt_manager
class JWTManagerProxy:
    """Proxy to provide lazy access to JWT manager."""

    def __getattr__(self, name):
        """Delegate attribute access to the actual JWT manager."""
        return getattr(get_jwt_manager(), name)

# Create the proxy instance
jwt_manager = JWTManagerProxy()

# Legacy method aliases for backward compatibility
def create_access_token(*args, **kwargs):
    """Legacy alias for generate_access_token."""
    return get_jwt_manager().generate_access_token(*args, **kwargs)

def create_refresh_token(*args, **kwargs):
    """Legacy alias for generate_refresh_token."""
    return get_jwt_manager().generate_refresh_token(*args, **kwargs)

def decode_token(token: str, token_type: str = 'access'):
    """Legacy alias for verify_token."""
    return get_jwt_manager().verify_token(token, token_type)

