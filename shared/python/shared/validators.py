# shared/python/shared/validators.py
# Input validation utilities - Shared Layer

"""
UNIFIED VALIDATION UTILITIES for Agent SCL Platform.
This module provides centralized validation functions to eliminate duplication
across auth, tenant, and other services.

CRITICAL: This is the single source of truth for all validation logic.
All services MUST use these validators instead of implementing their own.
"""

import re
import uuid
import html
import json
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from email_validator import validate_email, EmailNotValidError

from .exceptions import ValidationException
from .models import UserRole, UserStatus, TenantStatus, TenantPlan


# Sanitization utilities
def sanitize_html(text: str) -> str:
    """Sanitize HTML content to prevent XSS attacks."""
    if not text:
        return text

    # Simple HTML sanitization - escape all HTML
    # For production, consider using a proper HTML sanitization library
    return html.escape(text)


def sanitize_sql(text: str) -> str:
    """Sanitize text to prevent SQL injection (basic protection)."""
    if not text:
        return text

    # Remove common SQL injection patterns
    dangerous_patterns = [
        r"('|(\\')|(;)|(\\;))",  # Single quotes and semicolons
        r"(--)|(/\\*.*\\*/)",     # SQL comments
        r"\\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\\b",  # SQL keywords
    ]

    sanitized = text
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

    return sanitized.strip()


def sanitize_string(text: str, max_length: int = None, allow_html: bool = False) -> str:
    """General string sanitization."""
    if not text:
        return text

    # HTML escape if HTML not allowed
    if not allow_html:
        text = html.escape(text)
    else:
        text = sanitize_html(text)

    # SQL sanitization
    text = sanitize_sql(text)

    # Trim whitespace
    text = text.strip()

    # Length limit
    if max_length and len(text) > max_length:
        text = text[:max_length]

    return text


@dataclass
class ValidationResult:
    """Result of validation operation."""
    is_valid: bool
    errors: List[str]
    cleaned_data: Optional[Dict[str, Any]] = None


def validate_email_address(email: str) -> str:
    """Validate email address format."""
    if not email:
        raise ValidationException("Email address is required")
    
    email = email.strip().lower()
    
    # Basic email regex pattern
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if not re.match(email_pattern, email):
        raise ValidationException("Invalid email address format")
    
    return email


def validate_uuid(value: str, field_name: str = "ID") -> str:
    """Validate UUID format."""
    try:
        uuid.UUID(value)
        return value
    except ValueError:
        raise ValidationException(f"Invalid {field_name} format. Must be a valid UUID.")


def validate_tenant_name(name: str) -> str:
    """Validate tenant name format."""
    if not name or len(name.strip()) == 0:
        raise ValidationException("Tenant name is required")
    
    name = name.strip()
    
    if len(name) < 2:
        raise ValidationException("Tenant name must be at least 2 characters long")
    
    if len(name) > 100:
        raise ValidationException("Tenant name must be less than 100 characters")
    
    # Allow letters, numbers, spaces, hyphens, and underscores
    if not re.match(r'^[a-zA-Z0-9\s\-_]+$', name):
        raise ValidationException("Tenant name contains invalid characters")
    
    return name


def validate_password(password: str) -> str:
    """Validate password strength."""
    if not password:
        raise ValidationException("Password is required")
    
    errors = []
    
    # Check minimum length
    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")
    
    # Check for uppercase letter
    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain at least one uppercase letter")
    
    # Check for lowercase letter
    if not re.search(r'[a-z]', password):
        errors.append("Password must contain at least one lowercase letter")
    
    # Check for digit
    if not re.search(r'\d', password):
        errors.append("Password must contain at least one digit")
    
    # Check for special character
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        errors.append("Password must contain at least one special character")
    
    if errors:
        raise ValidationException("; ".join(errors))
    
    return password


def validate_phone_number(phone: str) -> str:
    """Validate phone number format."""
    if not phone:
        raise ValidationException("Phone number is required")
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (10-15 digits)
    if len(digits_only) < 10 or len(digits_only) > 15:
        raise ValidationException("Phone number must be between 10 and 15 digits")
    
    return digits_only


def validate_url(url: str) -> str:
    """Validate URL format."""
    if not url:
        raise ValidationException("URL is required")
    
    url_pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    
    if not re.match(url_pattern, url):
        raise ValidationException("Invalid URL format")
    
    return url


class LoginRequestValidator:
    """Validator for login requests compatible with Auth Service."""

    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        """Validate login request data."""
        errors = []
        cleaned_data = {}

        # Validate email
        email = data.get('email')
        if not email:
            errors.append("Email is required")
        else:
            try:
                cleaned_data['email'] = validate_email_address(email)
            except ValidationException as e:
                errors.append(str(e))

        # Validate password
        password = data.get('password')
        if not password:
            errors.append("Password is required")
        else:
            cleaned_data['password'] = password

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )

    @staticmethod
    def validate_from_body(body: str) -> Dict[str, Any]:
        """Validate login request from JSON body string (Auth Service compatible)."""
        try:
            data = json.loads(body) if isinstance(body, str) else body

            if not data.get('email'):
                raise ValueError("Email is required")
            if not data.get('password'):
                raise ValueError("Password is required")

            return {
                'email': data['email'].strip().lower(),
                'password': data['password']
            }
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format")


class RegisterRequestValidator:
    """Validator for registration requests compatible with Auth Service."""

    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        """Validate registration request data."""
        errors = []
        cleaned_data = {}

        # Validate email
        email = data.get('email')
        if not email:
            errors.append("Email is required")
        else:
            try:
                cleaned_data['email'] = validate_email_address(email)
            except ValidationException as e:
                errors.append(str(e))

        # Validate password
        password = data.get('password')
        if not password:
            errors.append("Password is required")
        else:
            try:
                cleaned_data['password'] = validate_password(password)
            except ValidationException as e:
                errors.append(str(e))
        
        # Validate tenant name
        tenant_name = data.get('tenant_name')
        if not tenant_name:
            errors.append("Tenant name is required")
        else:
            try:
                cleaned_data['tenant_name'] = validate_tenant_name(tenant_name)
            except ValidationException as e:
                errors.append(str(e))
        
        # Validate optional phone
        phone = data.get('phone')
        if phone:
            try:
                cleaned_data['phone'] = validate_phone_number(phone)
            except ValidationException as e:
                errors.append(str(e))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )


class TenantCreateValidator:
    """Validator for tenant creation requests."""
    
    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        """Validate tenant creation request data."""
        errors = []
        cleaned_data = {}
        
        # Validate name
        name = data.get('name')
        if not name:
            errors.append("Tenant name is required")
        else:
            try:
                cleaned_data['name'] = validate_tenant_name(name)
            except ValidationException as e:
                errors.append(str(e))
        
        # Validate master user email
        master_user_email = data.get('master_user_email')
        if not master_user_email:
            errors.append("Master user email is required")
        else:
            try:
                cleaned_data['master_user_email'] = validate_email_address(master_user_email)
            except ValidationException as e:
                errors.append(str(e))
        
        # Validate plan (optional)
        plan = data.get('plan', 'FREE')
        valid_plans = ['FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE']
        if plan not in valid_plans:
            errors.append(f"Invalid plan. Must be one of: {', '.join(valid_plans)}")
        else:
            cleaned_data['plan'] = plan
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )

    @staticmethod
    def validate_from_body(body: str) -> Dict[str, Any]:
        """Validate registration request from JSON body string (Auth Service compatible)."""
        try:
            data = json.loads(body) if isinstance(body, str) else body

            required_fields = ['tenant_id', 'email', 'password', 'name']
            for field in required_fields:
                if not data.get(field):
                    raise ValueError(f"{field.replace('_', ' ').title()} is required")

            return {
                'tenant_id': data['tenant_id'].strip(),
                'email': data['email'].strip().lower(),
                'password': data['password'],
                'name': data['name'].strip(),
                'role': data.get('role', 'MASTER').upper()
            }
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format")


def validate_request_body(body: Dict[str, Any], validator_class) -> ValidationResult:
    """Validate request body using specified validator."""
    if not body:
        return ValidationResult(
            is_valid=False,
            errors=["Request body is required"]
        )
    
    return validator_class.validate(body)


def validate_pagination_params(
    page: Optional[str] = None,
    page_size: Optional[str] = None,
    max_page_size: int = 100
) -> Dict[str, int]:
    """Validate and normalize pagination parameters."""
    # Default values
    normalized_page = 1
    normalized_page_size = 20
    
    # Validate page
    if page:
        try:
            normalized_page = int(page)
            if normalized_page < 1:
                raise ValidationException("Page must be greater than 0")
        except ValueError:
            raise ValidationException("Page must be a valid integer")
    
    # Validate page_size
    if page_size:
        try:
            normalized_page_size = int(page_size)
            if normalized_page_size < 1:
                raise ValidationException("Page size must be greater than 0")
            if normalized_page_size > max_page_size:
                raise ValidationException(f"Page size must not exceed {max_page_size}")
        except ValueError:
            raise ValidationException("Page size must be a valid integer")
    
    return {
        'page': normalized_page,
        'page_size': normalized_page_size
    }


class ForgotPasswordRequestValidator:
    """Validator for forgot password requests."""

    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        """Validate forgot password request data."""
        errors = []
        cleaned_data = {}

        # Validate email
        email = data.get('email', '').strip()
        if not email:
            errors.append("Email is required")
        else:
            try:
                cleaned_data['email'] = validate_email_address(email)
            except ValidationException as e:
                errors.append("Invalid email format")

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )


class ResetPasswordRequestValidator:
    """Validator for reset password requests."""

    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        """Validate reset password request data."""
        errors = []
        cleaned_data = {}

        # Validate token
        token = data.get('token', '').strip()
        if not token:
            errors.append("Reset token is required")
        else:
            cleaned_data['token'] = token

        # Validate new password
        password = data.get('password', '')
        if not password:
            errors.append("Password is required")
        elif not validate_password_strength(password):
            errors.append("Password does not meet security requirements")
        else:
            cleaned_data['password'] = password

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )


class CreateSubscriptionRequestValidator:
    """Validator for create subscription requests."""

    @staticmethod
    def validate(data: Dict[str, Any]) -> ValidationResult:
        """Validate create subscription request data."""
        errors = []
        cleaned_data = {}

        # Validate plan_id
        plan_id = data.get('plan_id', '').strip()
        if not plan_id:
            errors.append("Plan ID is required")
        else:
            cleaned_data['plan_id'] = plan_id

        # Validate billing_interval
        billing_interval = data.get('billing_interval', '').strip().upper()
        if not billing_interval:
            errors.append("Billing interval is required")
        elif billing_interval not in ['MONTHLY', 'YEARLY']:
            errors.append("Billing interval must be MONTHLY or YEARLY")
        else:
            cleaned_data['billing_interval'] = billing_interval

        # Validate payment_method_id (optional for trials)
        payment_method_id = data.get('payment_method_id', '').strip()
        if payment_method_id:
            cleaned_data['payment_method_id'] = payment_method_id

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )


# Enhanced validators with sanitization
class EnhancedValidators:
    """Enhanced validators with automatic sanitization."""

    @staticmethod
    def validate_and_sanitize_string(
        value: str,
        field_name: str,
        required: bool = True,
        min_length: int = None,
        max_length: int = None,
        allow_html: bool = False,
        pattern: str = None
    ) -> str:
        """Validate and sanitize string input."""
        if not value and required:
            raise ValidationException(f"{field_name} is required")

        if not value:
            return ""

        # Sanitize
        sanitized = sanitize_string(value, max_length, allow_html)

        # Length validation
        if min_length and len(sanitized) < min_length:
            raise ValidationException(f"{field_name} must be at least {min_length} characters long")

        if max_length and len(sanitized) > max_length:
            raise ValidationException(f"{field_name} cannot exceed {max_length} characters")

        # Pattern validation
        if pattern and not re.match(pattern, sanitized):
            raise ValidationException(f"{field_name} format is invalid")

        return sanitized

    @staticmethod
    def validate_email_enhanced(email: str) -> str:
        """Enhanced email validation with sanitization."""
        if not email:
            raise ValidationException("Email address is required")

        # Sanitize and normalize
        email = sanitize_string(email, max_length=254).lower()

        # Enhanced email regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        if not re.match(email_pattern, email):
            raise ValidationException("Invalid email address format")

        # Additional checks
        if '..' in email:
            raise ValidationException("Email address cannot contain consecutive dots")

        if email.startswith('.') or email.endswith('.'):
            raise ValidationException("Email address cannot start or end with a dot")

        return email

    @staticmethod
    def validate_name_enhanced(name: str, field_name: str = "Name") -> str:
        """Enhanced name validation with sanitization."""
        return EnhancedValidators.validate_and_sanitize_string(
            value=name,
            field_name=field_name,
            required=True,
            min_length=1,
            max_length=100,
            allow_html=False,
            pattern=r'^[a-zA-Z\s\-\'\.]+$'  # Letters, spaces, hyphens, apostrophes, dots
        )

    @staticmethod
    def validate_description_enhanced(description: str, max_length: int = 1000) -> str:
        """Enhanced description validation with HTML sanitization."""
        return EnhancedValidators.validate_and_sanitize_string(
            value=description,
            field_name="Description",
            required=False,
            max_length=max_length,
            allow_html=True  # Allow safe HTML in descriptions
        )


# =============================================================================
# UNIFIED VALIDATION CLASSES - SINGLE SOURCE OF TRUTH
# =============================================================================

class UnifiedUserValidator:
    """
    UNIFIED USER VALIDATION - Single source of truth for all services.
    All auth and tenant services MUST use these validators.
    """

    @staticmethod
    def validate_email_format(email: str) -> Tuple[bool, str]:
        """
        Validate email format using email-validator library.
        REPLACES all duplicate email validation across services.
        """
        if not email:
            return False, "Email is required"

        try:
            # Use email-validator for comprehensive validation
            validated_email = validate_email(email)
            return True, validated_email.email
        except EmailNotValidError as e:
            return False, f"Invalid email format: {str(e)}"

    @staticmethod
    def validate_password_strength(password: str) -> Tuple[bool, List[str]]:
        """
        UNIFIED PASSWORD VALIDATION - Single source of truth.
        REPLACES duplicate password validation in auth and shared.
        """
        errors = []

        if not password:
            errors.append("Password is required")
            return False, errors

        # Check minimum length
        if len(password) < 8:
            errors.append("Password must be at least 8 characters long")

        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")

        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")

        # Check for digit
        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")

        # Check for special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")

        # Check for common patterns
        common_patterns = [
            r'123456', r'password', r'qwerty', r'abc123', r'admin'
        ]

        for pattern in common_patterns:
            if re.search(pattern, password.lower()):
                errors.append("Password contains common patterns and is not secure")
                break

        return len(errors) == 0, errors

    @staticmethod
    def validate_user_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        UNIFIED USER DATA VALIDATION - Single source of truth.
        REPLACES duplicate validation across auth and tenant services.
        """
        errors = []

        # Required fields
        required_fields = ['email', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                errors.append(f"{field.replace('_', ' ').title()} is required")

        # Email validation
        if data.get('email'):
            is_valid, email_error = UnifiedUserValidator.validate_email_format(data['email'])
            if not is_valid:
                errors.append(email_error)

        # Name validation
        if data.get('first_name'):
            if len(data['first_name'].strip()) < 2:
                errors.append("First name must be at least 2 characters long")
            if not re.match(r'^[a-zA-Z\s\-\'\.]+$', data['first_name']):
                errors.append("First name contains invalid characters")

        if data.get('last_name'):
            if len(data['last_name'].strip()) < 2:
                errors.append("Last name must be at least 2 characters long")
            if not re.match(r'^[a-zA-Z\s\-\'\.]+$', data['last_name']):
                errors.append("Last name contains invalid characters")

        # Role validation
        if data.get('role'):
            try:
                UserRole(data['role'])
            except ValueError:
                valid_roles = [role.value for role in UserRole]
                errors.append(f"Invalid role. Must be one of: {', '.join(valid_roles)}")

        return len(errors) == 0, errors

    @staticmethod
    def validate_role_permissions(user_role: UserRole, required_role: UserRole) -> bool:
        """
        UNIFIED PERMISSION VALIDATION - Single source of truth.
        REPLACES inconsistent role checking across services.
        """
        return user_role.can_access(required_role)


class UnifiedRequestValidator:
    """
    UNIFIED REQUEST VALIDATION - Single source of truth.
    REPLACES duplicate request parsing across 8+ handlers.
    """

    @staticmethod
    def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
        """
        UNIFIED REQUEST PARSING - Single source of truth.
        REPLACES duplicate parsing logic across multiple handlers.
        """
        try:
            body = event.get('body', '{}')
            if isinstance(body, str):
                return json.loads(body) if body else {}
            return body or {}
        except json.JSONDecodeError as e:
            raise ValidationException(f"Invalid JSON in request body: {str(e)}")

    @staticmethod
    def validate_uuid_format(uuid_str: str, field_name: str = "ID") -> Tuple[bool, str]:
        """Validate UUID format."""
        if not uuid_str:
            return False, f"{field_name} is required"

        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if not re.match(uuid_pattern, uuid_str.lower()):
            return False, f"Invalid {field_name} format"

        return True, ""


# Export unified validators for easy import
__all__ = [
    'ValidationException',
    'UnifiedUserValidator',
    'UnifiedRequestValidator',
    # ... existing exports
]
