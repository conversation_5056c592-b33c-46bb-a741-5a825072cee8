#!/usr/bin/env python3
# shared/python/shared/health.py
# Health check utilities for all services

"""
Health check utilities for monitoring service status.
Provides standardized health endpoints and dependency checking.
"""

import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from .logger import lambda_logger
from .config import get_settings


class HealthStatus:
    """Health status constants."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class HealthCheck:
    """Health check utilities for services."""
    
    def __init__(self, service_name: str):
        """Initialize health checker."""
        self.service_name = service_name
        self.settings = get_settings()
        self.start_time = time.time()
    
    def get_basic_health(self) -> Dict[str, Any]:
        """Get basic health information."""
        current_time = datetime.now(timezone.utc)
        uptime = time.time() - self.start_time
        
        return {
            "service": self.service_name,
            "status": HealthStatus.HEALTHY,
            "timestamp": current_time.isoformat(),
            "uptime_seconds": round(uptime, 2),
            "environment": self.settings.environment,
            "region": self.settings.region,
            "version": "1.0.0"
        }
    
    def check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity."""
        try:
            from .database import db_client
            
            # Simple connectivity test
            start_time = time.time()
            
            # Try to describe table (lightweight operation)
            table_name = self.settings.dynamodb_table
            if table_name:
                response = db_client.dynamodb.describe_table(TableName=table_name)
                duration = time.time() - start_time
                
                return {
                    "component": "database",
                    "status": HealthStatus.HEALTHY,
                    "response_time_ms": round(duration * 1000, 2),
                    "table_status": response.get('Table', {}).get('TableStatus', 'UNKNOWN')
                }
            else:
                return {
                    "component": "database",
                    "status": HealthStatus.DEGRADED,
                    "message": "No database table configured"
                }
                
        except Exception as e:
            lambda_logger.error("Database health check failed", extra={'error': str(e)})
            return {
                "component": "database",
                "status": HealthStatus.UNHEALTHY,
                "error": str(e)
            }
    
    def check_secrets_health(self) -> Dict[str, Any]:
        """Check secrets manager connectivity."""
        try:
            from .secrets_manager import secrets_client
            
            start_time = time.time()
            
            # Try to list secrets (lightweight operation)
            secrets_client.list_secrets(MaxResults=1)
            duration = time.time() - start_time
            
            return {
                "component": "secrets_manager",
                "status": HealthStatus.HEALTHY,
                "response_time_ms": round(duration * 1000, 2)
            }
            
        except Exception as e:
            lambda_logger.error("Secrets manager health check failed", extra={'error': str(e)})
            return {
                "component": "secrets_manager",
                "status": HealthStatus.UNHEALTHY,
                "error": str(e)
            }
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """Check memory usage."""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            
            # Determine status based on memory usage
            if memory.percent < 70:
                status = HealthStatus.HEALTHY
            elif memory.percent < 85:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
            
            return {
                "component": "memory",
                "status": status,
                "usage_percent": round(memory.percent, 2),
                "available_mb": round(memory.available / 1024 / 1024, 2),
                "total_mb": round(memory.total / 1024 / 1024, 2)
            }
            
        except ImportError:
            return {
                "component": "memory",
                "status": HealthStatus.DEGRADED,
                "message": "psutil not available"
            }
        except Exception as e:
            return {
                "component": "memory",
                "status": HealthStatus.UNHEALTHY,
                "error": str(e)
            }
    
    def get_comprehensive_health(self, include_dependencies: bool = True) -> Dict[str, Any]:
        """Get comprehensive health check including dependencies."""
        health_data = self.get_basic_health()
        
        if include_dependencies:
            dependencies = []
            
            # Check database
            db_health = self.check_database_health()
            dependencies.append(db_health)
            
            # Check secrets manager
            secrets_health = self.check_secrets_health()
            dependencies.append(secrets_health)
            
            # Check memory
            memory_health = self.check_memory_usage()
            dependencies.append(memory_health)
            
            health_data["dependencies"] = dependencies
            
            # Determine overall status
            unhealthy_deps = [d for d in dependencies if d["status"] == HealthStatus.UNHEALTHY]
            degraded_deps = [d for d in dependencies if d["status"] == HealthStatus.DEGRADED]
            
            if unhealthy_deps:
                health_data["status"] = HealthStatus.UNHEALTHY
                health_data["message"] = f"{len(unhealthy_deps)} dependencies unhealthy"
            elif degraded_deps:
                health_data["status"] = HealthStatus.DEGRADED
                health_data["message"] = f"{len(degraded_deps)} dependencies degraded"
        
        return health_data


def create_health_handler(service_name: str):
    """Create a health check handler for a service."""
    
    def health_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
        """Health check endpoint handler."""
        try:
            health_checker = HealthCheck(service_name)
            
            # Check if this is a deep health check
            query_params = event.get('queryStringParameters') or {}
            deep_check = query_params.get('deep', 'false').lower() == 'true'
            
            if deep_check:
                health_data = health_checker.get_comprehensive_health()
            else:
                health_data = health_checker.get_basic_health()
            
            # Determine HTTP status code
            status_code = 200
            if health_data["status"] == HealthStatus.UNHEALTHY:
                status_code = 503
            elif health_data["status"] == HealthStatus.DEGRADED:
                status_code = 200  # Still operational
            
            return {
                'statusCode': status_code,
                'headers': {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                'body': json.dumps(health_data, indent=2)
            }
            
        except Exception as e:
            lambda_logger.error("Health check failed", extra={
                'service': service_name,
                'error': str(e)
            })
            
            return {
                'statusCode': 503,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    "service": service_name,
                    "status": HealthStatus.UNHEALTHY,
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            }
    
    return health_handler


# Global health checker instance
def get_health_checker(service_name: str) -> HealthCheck:
    """Get health checker instance for a service."""
    return HealthCheck(service_name)
