# shared/python/shared/user_model.py
# Unified User model for Agent SCL Platform

"""
Unified User model that consolidates all user-related functionality
from both auth and user services. This eliminates duplication and
ensures consistency across the platform.
"""

import uuid
import time
from typing import Any, Dict, List, Optional
from enum import Enum

# Import shared utilities
from .database import db_client
from .exceptions import (
    ResourceNotFoundException,
    ResourceConflictException,
    InvalidCredentialsException,
    AccountLockedException,
    EmailNotVerifiedException,
    ValidationException,
    PlatformException
)
from .logger import lambda_logger
from .models import UserRole, UserStatus


class User:
    """
    Unified User model for Agent SCL Platform.
    
    This model consolidates all user functionality from both auth and user services,
    providing a single source of truth for user data and operations.
    """
    
    def __init__(
        self,
        user_id: str,
        email: str,
        tenant_id: str,
        role: UserRole,
        status: UserStatus,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        password_hash: Optional[str] = None,
        email_verified: bool = False,
        email_verified_at: Optional[int] = None,
        email_verification_token: Optional[str] = None,
        last_login_at: Optional[int] = None,
        failed_login_attempts: int = 0,
        locked_until: Optional[int] = None,
        created_at: Optional[int] = None,
        updated_at: Optional[int] = None,
        profile_picture_url: Optional[str] = None,
        phone_number: Optional[str] = None,
        timezone: str = 'UTC',
        language: str = 'en',
        preferences: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        # Core fields
        self.user_id = user_id
        self.email = email
        self.tenant_id = tenant_id
        self.role = role if isinstance(role, UserRole) else UserRole(role)
        self.status = status if isinstance(status, UserStatus) else UserStatus(status)
        
        # Name fields
        self.first_name = first_name
        self.last_name = last_name
        
        # Authentication fields
        self.password_hash = password_hash
        self.email_verified = email_verified
        self.email_verified_at = email_verified_at
        self.email_verification_token = email_verification_token
        self.last_login_at = last_login_at
        self.failed_login_attempts = failed_login_attempts
        self.locked_until = locked_until
        
        # Contact information
        self.phone_number = phone_number
        
        # Profile information
        self.profile_picture_url = profile_picture_url
        self.timezone = timezone
        self.language = language
        
        # Metadata and preferences
        self.preferences = preferences or {}
        self.metadata = metadata or {}
        
        # Timestamps
        self.created_at = created_at or int(time.time())
        self.updated_at = updated_at or int(time.time())
        
        # Handle additional kwargs for backward compatibility
        for key, value in kwargs.items():
            if not hasattr(self, key):
                setattr(self, key, value)
    
    @property
    def full_name(self) -> str:
        """Get full name from first_name and last_name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        else:
            return ""
    
    @property
    def display_name(self) -> str:
        """Get display name (full name or email)."""
        full_name = self.full_name
        return full_name if full_name else self.email
    
    @property
    def name(self) -> str:
        """Backward compatibility property for auth service."""
        return self.full_name or self.email
    
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if not self.locked_until:
            return False
        return int(time.time()) < self.locked_until
    
    def is_email_verified(self) -> bool:
        """Check if email is verified."""
        return self.email_verified
    
    def can_login(self) -> bool:
        """Check if user can login."""
        return (
            self.status == UserStatus.ACTIVE and
            not self.is_locked() and
            self.is_email_verified()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary."""
        return {
            'user_id': self.user_id,
            'email': self.email,
            'tenant_id': self.tenant_id,
            'role': self.role.value,
            'status': self.status.value,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'password_hash': self.password_hash,
            'email_verified': self.email_verified,
            'email_verified_at': self.email_verified_at,
            'last_login_at': self.last_login_at,
            'failed_login_attempts': self.failed_login_attempts,
            'locked_until': self.locked_until,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'profile_picture_url': self.profile_picture_url,
            'phone_number': self.phone_number,
            'timezone': self.timezone,
            'language': self.language,
            'preferences': self.preferences,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create user from dictionary."""
        return cls(
            user_id=data['user_id'],
            email=data['email'],
            tenant_id=data['tenant_id'],
            role=UserRole(data['role']),
            status=UserStatus(data['status']),
            first_name=data.get('first_name'),
            last_name=data.get('last_name'),
            password_hash=data.get('password_hash'),
            email_verified=data.get('email_verified', False),
            email_verified_at=data.get('email_verified_at'),
            last_login_at=data.get('last_login_at'),
            failed_login_attempts=data.get('failed_login_attempts', 0),
            locked_until=data.get('locked_until'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            profile_picture_url=data.get('profile_picture_url'),
            phone_number=data.get('phone_number'),
            timezone=data.get('timezone', 'UTC'),
            language=data.get('language', 'en'),
            preferences=data.get('preferences', {}),
            metadata=data.get('metadata', {})
        )
    
    def save(self) -> None:
        """Save user to database using unified pattern."""
        try:
            self.updated_at = int(time.time())
            
            # Use the more robust pattern from user service
            item = {
                'PK': f'TENANT#{self.tenant_id}',
                'SK': f'USER#{self.user_id}',
                'entity_type': 'USER',
                'GSI1PK': f'USER#{self.user_id}',
                'GSI1SK': f'TENANT#{self.tenant_id}',
                'GSI2PK': f'EMAIL#{self.email}',
                'GSI2SK': f'TENANT#{self.tenant_id}',
                **self.to_dict()
            }
            
            db_client.put_item(item, self.tenant_id)
            
            lambda_logger.info("User saved", extra={
                'user_id': self.user_id,
                'tenant_id': self.tenant_id,
                'email': self.email
            })
            
        except Exception as e:
            lambda_logger.error("Failed to save user", extra={
                'user_id': self.user_id,
                'tenant_id': self.tenant_id,
                'error': str(e)
            })
            raise PlatformException(f"Failed to save user: {str(e)}")

    @classmethod
    def create(
        cls,
        tenant_id: str,
        email: str,
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        role: UserRole = UserRole.MEMBER,
        phone_number: Optional[str] = None
    ) -> 'User':
        """Create a new user with unified approach."""
        user_id = str(uuid.uuid4())
        current_time = int(time.time())

        # Validate inputs
        cls._validate_email(email)
        if first_name:
            cls._validate_name(first_name)
        if last_name:
            cls._validate_name(last_name)
        if phone_number:
            cls._validate_phone(phone_number)

        # Hash password
        from .auth import password_manager
        password_hash = password_manager.hash_password(password, user_id)

        # Generate email verification token
        email_verification_token = str(uuid.uuid4())

        # Check if user already exists
        existing_user = cls.get_by_email(email, tenant_id)
        if existing_user:
            raise ResourceConflictException(
                "User with this email already exists",
                resource_type="user",
                conflict_field="email"
            )

        # Create user instance
        user = cls(
            user_id=user_id,
            email=email,
            tenant_id=tenant_id,
            role=role,
            status=UserStatus.PENDING_VERIFICATION,
            first_name=first_name,
            last_name=last_name,
            password_hash=password_hash,
            phone_number=phone_number,
            email_verified=False,
            email_verification_token=email_verification_token,
            created_at=current_time,
            updated_at=current_time
        )

        # Save to database
        user.save()

        lambda_logger.info("User created successfully", extra={
            'user_id': user_id,
            'tenant_id': tenant_id,
            'email': email,
            'role': role.value
        })

        return user

    @classmethod
    def get_by_id(cls, user_id: str, tenant_id: str) -> Optional['User']:
        """Get user by ID using unified pattern."""
        try:
            item = db_client.get_item(
                pk=f'TENANT#{tenant_id}',
                sk=f'USER#{user_id}',
                tenant_id=tenant_id
            )

            if not item:
                return None

            return cls.from_dict(item)

        except Exception as e:
            lambda_logger.error("Failed to get user by ID", extra={
                'user_id': user_id,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None

    @classmethod
    def get_by_email(cls, email: str, tenant_id: str = None) -> Optional['User']:
        """Get user by email using unified pattern."""
        try:
            if tenant_id:
                # Query with specific tenant
                items = db_client.query_gsi(
                    gsi_name='GSI2',
                    pk=f'EMAIL#{email}',
                    sk=f'TENANT#{tenant_id}',
                    tenant_id=tenant_id
                )
            else:
                # Query without tenant filter (for login)
                items = db_client.query_gsi(
                    gsi_name='GSI2',
                    pk=f'EMAIL#{email}'
                )

            if not items:
                return None

            return cls.from_dict(items[0])

        except Exception as e:
            lambda_logger.error("Failed to get user by email", extra={
                'email': email,
                'tenant_id': tenant_id,
                'error': str(e)
            })
            return None

    @classmethod
    def find_by_email_global(cls, email: str) -> Optional['User']:
        """Find user by email across all tenants (for login)."""
        try:
            # Query GSI2 to find user by email across all tenants
            items = db_client.query_gsi(
                gsi_name='GSI2',
                pk=f'EMAIL#{email}',
                # No SK filter to search across all tenants
                limit=1
            )

            if not items:
                return None

            return cls.from_dict(items[0])

        except Exception as e:
            lambda_logger.error("Failed to find user by email globally", extra={
                'email': email,
                'error': str(e)
            })
            return None

    def authenticate(self, password: str) -> bool:
        """Authenticate user with password."""
        try:
            # Check if account is locked
            if self.is_locked():
                raise AccountLockedException("Account is temporarily locked due to too many failed attempts")

            # Check if email is verified (for non-master users)
            if self.role != UserRole.MASTER and not self.email_verified:
                raise EmailNotVerifiedException("Email address must be verified before login")

            # Verify password
            from .auth import password_manager
            if not password_manager.verify_password(password, self.password_hash, self.user_id):
                self._increment_login_attempts()
                raise InvalidCredentialsException("Invalid email or password")

            # Reset failed attempts on successful authentication
            self._reset_login_attempts()
            self._update_last_login()

            return True

        except (AccountLockedException, EmailNotVerifiedException, InvalidCredentialsException):
            raise
        except Exception as e:
            lambda_logger.error("Authentication error", extra={
                'user_id': self.user_id,
                'tenant_id': self.tenant_id,
                'error': str(e)
            })
            raise InvalidCredentialsException("Authentication failed")

    def _increment_login_attempts(self) -> None:
        """Increment failed login attempts and lock account if necessary."""
        self.failed_login_attempts += 1

        # Lock account after 5 failed attempts for 30 minutes
        if self.failed_login_attempts >= 5:
            self.locked_until = int(time.time()) + (30 * 60)  # 30 minutes

        self.updated_at = int(time.time())
        self.save()

        lambda_logger.warning("Failed login attempt", extra={
            'user_id': self.user_id,
            'tenant_id': self.tenant_id,
            'attempts': self.failed_login_attempts,
            'locked': self.is_locked()
        })

    def _reset_login_attempts(self) -> None:
        """Reset failed login attempts."""
        self.failed_login_attempts = 0
        self.locked_until = None
        self.updated_at = int(time.time())
        self.save()

    def _update_last_login(self) -> None:
        """Update last login timestamp."""
        self.last_login_at = int(time.time())
        self.updated_at = int(time.time())
        self.save()

    def update_last_login(self) -> None:
        """Public method to update last login timestamp."""
        self._update_last_login()

    @staticmethod
    def _validate_email(email: str) -> None:
        """Validate email format using shared validator."""
        from .validators import validate_email_address
        validate_email_address(email)

    @staticmethod
    def _validate_name(name: str) -> None:
        """Validate name format."""
        if not name or len(name.strip()) < 1:
            raise ValidationException("Name must be at least 1 character long")

        if len(name) > 100:
            raise ValidationException("Name cannot exceed 100 characters")

    @staticmethod
    def _validate_phone(phone: str) -> None:
        """Validate phone number format."""
        if not phone:
            return

        import re
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)

        # Check if it's a valid length (10-15 digits)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise ValidationException("Phone number must be between 10 and 15 digits")
