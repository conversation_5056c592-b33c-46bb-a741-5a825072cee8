# shared/python/shared/models.py
# Shared models for cross-service communication

"""
Shared data models that can be used across services.
These models provide a contract for data exchange without creating dependencies.
"""

from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
import time

# Import unified models - temporarily commented to fix circular imports
# from .models.conversation import (
#     Conversation, ConversationType, ConversationStatus, ConversationParticipant, ParticipantType,
#     create_chat_conversation, create_agent_conversation
# )
# from .models.message import (
#     Message, MessageType, MessageDirection, MessageStatus, MessagePriority,
#     MessageAttachment, MessageReaction,
#     create_chat_message, create_agent_request_message, create_agent_response_message
# )
# from .models.message_routing import (
#     RoutingDestination, RoutingStrategy, RoutingPriority, RoutingRule, RoutingContext, RoutingDecision,
#     MessageRouter, message_router, route_message, create_routing_context_from_message
# )


class TenantStatus(Enum):
    """Tenant status enumeration."""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    SUSPENDED = "SUSPENDED"
    TRIAL = "TRIAL"
    TRIAL_EXPIRED = "TRIAL_EXPIRED"


class TenantPlan(Enum):
    """Tenant plan enumeration."""
    FREE = "FREE"
    BASIC = "BASIC"
    PROFESSIONAL = "PROFESSIONAL"
    ENTERPRISE = "ENTERPRISE"


@dataclass
class TenantInfo:
    """
    Shared tenant information for cross-service communication.
    This is a lightweight representation of tenant data.
    """
    tenant_id: str
    name: str
    status: TenantStatus
    plan: TenantPlan
    created_at: datetime
    trial_ends_at: Optional[datetime] = None
    features: Optional[Dict[str, Any]] = None
    
    def is_active(self) -> bool:
        """Check if tenant is active."""
        return self.status == TenantStatus.ACTIVE
    
    def is_trial_expired(self) -> bool:
        """Check if trial has expired."""
        if self.trial_ends_at is None:
            return False
        return datetime.utcnow() > self.trial_ends_at
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            'tenant_id': self.tenant_id,
            'name': self.name,
            'status': self.status.value,
            'plan': self.plan.value,
            'created_at': self.created_at.isoformat(),
            'trial_ends_at': self.trial_ends_at.isoformat() if self.trial_ends_at else None,
            'features': self.features or []
        }


class UserRole(Enum):
    """
    User role enumeration with hierarchical levels.
    UNIFIED ROLES - Source of truth for all services.
    """
    MASTER = "MASTER"    # Level 4 - Full platform access, tenant owner
    ADMIN = "ADMIN"      # Level 3 - User management, billing access
    MEMBER = "MEMBER"    # Level 2 - Standard user access
    VIEWER = "VIEWER"    # Level 1 - Read-only access

    @property
    def level(self) -> int:
        """Get role hierarchy level."""
        return ROLE_HIERARCHY[self]

    def can_access(self, required_role: 'UserRole') -> bool:
        """Check if this role can access resources requiring another role."""
        return self.level >= required_role.level

    def is_admin_or_higher(self) -> bool:
        """Check if role has admin privileges."""
        return self.level >= UserRole.ADMIN.level

    def is_master(self) -> bool:
        """Check if role is master."""
        return self == UserRole.MASTER


# Role hierarchy mapping
ROLE_HIERARCHY = {
    UserRole.VIEWER: 1,
    UserRole.MEMBER: 2,
    UserRole.ADMIN: 3,
    UserRole.MASTER: 4
}


class UserStatus(Enum):
    """User status enumeration."""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PENDING_VERIFICATION = "PENDING_VERIFICATION"
    SUSPENDED = "SUSPENDED"


@dataclass
class UserInfo:
    """
    Shared user information for cross-service communication.
    This is a lightweight representation of user data.
    UNIFIED MODEL - Source of truth for all services.
    """
    user_id: str
    tenant_id: str
    email: str
    first_name: str  # Changed from 'name' to 'first_name' for consistency
    last_name: str   # Added last_name field
    role: UserRole
    status: UserStatus
    email_verified: bool = False
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None

    @property
    def full_name(self) -> str:
        """Get full name for backward compatibility."""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def name(self) -> str:
        """Backward compatibility property."""
        return self.full_name
    
    def is_active(self) -> bool:
        """Check if user is active."""
        return self.status == UserStatus.ACTIVE
    
    def is_verified(self) -> bool:
        """Check if user email is verified."""
        return self.email_verified
    
    def has_role(self, required_role: UserRole) -> bool:
        """Check if user has required role."""
        if required_role == UserRole.MEMBER:
            return True  # Both MASTER and MEMBER can access MEMBER resources
        return self.role == required_role
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            'user_id': self.user_id,
            'tenant_id': self.tenant_id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'name': self.full_name,  # For backward compatibility
            'role': self.role.value,
            'status': self.status.value,
            'email_verified': self.email_verified,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class SubscriptionStatus(Enum):
    """Subscription status enumeration."""
    TRIAL = "TRIAL"
    ACTIVE = "ACTIVE"
    PAST_DUE = "PAST_DUE"
    CANCELLED = "CANCELLED"
    SUSPENDED = "SUSPENDED"


@dataclass
class SubscriptionInfo:
    """
    Shared subscription information for cross-service communication.
    """
    subscription_id: str
    tenant_id: str
    plan_id: str
    status: SubscriptionStatus
    current_period_start: datetime
    current_period_end: datetime
    stripe_subscription_id: Optional[str] = None
    
    def is_active(self) -> bool:
        """Check if subscription is active."""
        return self.status in [SubscriptionStatus.TRIAL, SubscriptionStatus.ACTIVE]


# ============================================================================
# PAYMENT MODELS
# ============================================================================

class BillingInterval(Enum):
    """Billing interval enumeration."""
    MONTHLY = "MONTHLY"
    YEARLY = "YEARLY"
    QUARTERLY = "QUARTERLY"


class PlanStatus(Enum):
    """Plan status enumeration."""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    DEPRECATED = "DEPRECATED"


class PlanType(Enum):
    """Plan type enumeration."""
    FREE = "FREE"
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"


class CustomerStatus(Enum):
    """Customer status enumeration."""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    SUSPENDED = "SUSPENDED"


@dataclass
class PlanInfo:
    """
    Shared plan information for cross-service communication.
    Lightweight representation of plan data.
    """
    plan_id: str
    name: str
    plan_type: PlanType
    status: PlanStatus
    monthly_price: Decimal
    yearly_price: Optional[Decimal] = None
    currency: str = "USD"
    features: List[str] = None
    limits: Dict[str, Any] = None
    trial_days: int = 0
    description: Optional[str] = None

    def __post_init__(self):
        """Post-initialization setup."""
        if self.features is None:
            self.features = []
        if self.limits is None:
            self.limits = {}

    def get_price_for_interval(self, interval: BillingInterval) -> Decimal:
        """Get price for billing interval."""
        if interval == BillingInterval.YEARLY and self.yearly_price:
            return self.yearly_price
        return self.monthly_price


@dataclass
class CustomerInfo:
    """
    Shared customer information for cross-service communication.
    Lightweight representation of customer data.
    """
    customer_id: str
    tenant_id: str
    stripe_customer_id: Optional[str] = None
    billing_email: Optional[str] = None
    company_name: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[Dict[str, str]] = None
    tax_id: Optional[str] = None
    status: CustomerStatus = CustomerStatus.ACTIVE
    created_at: Optional[int] = None
    updated_at: Optional[int] = None

    def is_active(self) -> bool:
        """Check if customer is active."""
        return self.status == CustomerStatus.ACTIVE


# Factory functions for creating shared models from service-specific models
def create_tenant_info(tenant_data: Dict[str, Any]) -> TenantInfo:
    """Create TenantInfo from tenant data dictionary."""
    return TenantInfo(
        tenant_id=tenant_data['tenant_id'],
        name=tenant_data['name'],
        status=TenantStatus(tenant_data['status']),
        plan=TenantPlan(tenant_data['plan']),
        created_at=datetime.fromisoformat(tenant_data['created_at']),
        trial_ends_at=datetime.fromisoformat(tenant_data['trial_ends_at']) if tenant_data.get('trial_ends_at') else None,
        features=tenant_data.get('features')
    )


def create_user_info(user_data: Dict[str, Any]) -> UserInfo:
    """Create UserInfo from user data dictionary."""
    return UserInfo(
        user_id=user_data['user_id'],
        tenant_id=user_data['tenant_id'],
        email=user_data['email'],
        name=user_data['name'],
        role=UserRole(user_data['role']),
        status=UserStatus(user_data['status']),
        email_verified=user_data.get('email_verified', False),
        last_login=datetime.fromisoformat(user_data['last_login']) if user_data.get('last_login') else None,
        created_at=datetime.fromisoformat(user_data['created_at']) if user_data.get('created_at') else None
    )


def create_subscription_info(subscription_data: Dict[str, Any]) -> SubscriptionInfo:
    """Create SubscriptionInfo from subscription data dictionary."""
    return SubscriptionInfo(
        subscription_id=subscription_data['subscription_id'],
        tenant_id=subscription_data['tenant_id'],
        plan_id=subscription_data['plan_id'],
        status=SubscriptionStatus(subscription_data['status']),
        current_period_start=datetime.fromisoformat(subscription_data['current_period_start']),
        current_period_end=datetime.fromisoformat(subscription_data['current_period_end']),
        stripe_subscription_id=subscription_data.get('stripe_subscription_id')
    )


def create_plan_info(plan_data: Dict[str, Any]) -> PlanInfo:
    """Create PlanInfo from plan data dictionary."""
    return PlanInfo(
        plan_id=plan_data['plan_id'],
        name=plan_data['name'],
        plan_type=PlanType(plan_data['plan_type']),
        status=PlanStatus(plan_data['status']),
        monthly_price=Decimal(str(plan_data['monthly_price'])),
        yearly_price=Decimal(str(plan_data['yearly_price'])) if plan_data.get('yearly_price') else None,
        currency=plan_data.get('currency', 'USD'),
        features=plan_data.get('features', []),
        limits=plan_data.get('limits', {}),
        trial_days=plan_data.get('trial_days', 0),
        description=plan_data.get('description')
    )


def create_customer_info(customer_data: Dict[str, Any]) -> CustomerInfo:
    """Create CustomerInfo from customer data dictionary."""
    return CustomerInfo(
        customer_id=customer_data['customer_id'],
        tenant_id=customer_data['tenant_id'],
        stripe_customer_id=customer_data.get('stripe_customer_id'),
        billing_email=customer_data.get('billing_email'),
        company_name=customer_data.get('company_name'),
        phone=customer_data.get('phone'),
        address=customer_data.get('address'),
        tax_id=customer_data.get('tax_id'),
        status=CustomerStatus(customer_data.get('status', 'ACTIVE')),
        created_at=customer_data.get('created_at'),
        updated_at=customer_data.get('updated_at')
    )
