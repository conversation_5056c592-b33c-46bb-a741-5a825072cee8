# shared/python/shared/email.py
# Shared email service - Shared Layer

"""
Shared email service.
Manages email sending functionality across all services.
"""

import time
import boto3
from typing import Dict, Any, Optional

from .logger import lambda_logger
from .config import get_settings
from .exceptions import PlatformException


class EmailService:
    """Service for sending emails."""
    
    def __init__(self):
        """Initialize email service."""
        self.settings = get_settings()
        self.ses_client = boto3.client('ses', region_name=self.settings.region)
        self.provider = "ses"  # AWS SES
    
    def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: str,
        email_type: str = "notification"
    ) -> bool:
        """
        Send email using AWS SES.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            text_content: Plain text email content
            email_type: Type of email (verification, notification, etc.)
            
        Returns:
            bool: True if email was sent successfully
        """
        try:
            lambda_logger.info("Sending email", extra={
                'to_email': to_email,
                'subject': subject,
                'email_type': email_type,
                'provider': self.provider
            })
            
            # Send email via AWS SES
            response = self.ses_client.send_email(
                Source=self.settings.email_from_address,
                Destination={
                    'ToAddresses': [to_email]
                },
                Message={
                    'Subject': {
                        'Data': subject,
                        'Charset': 'UTF-8'
                    },
                    'Body': {
                        'Html': {
                            'Data': html_content,
                            'Charset': 'UTF-8'
                        },
                        'Text': {
                            'Data': text_content,
                            'Charset': 'UTF-8'
                        }
                    }
                }
            )
            
            message_id = response['MessageId']
            
            lambda_logger.info("Email sent successfully", extra={
                'to_email': to_email,
                'subject': subject,
                'email_type': email_type,
                'message_id': message_id
            })
            
            return True
            
        except Exception as e:
            lambda_logger.error("Failed to send email", extra={
                'to_email': to_email,
                'subject': subject,
                'email_type': email_type,
                'error': str(e)
            })
            return False
    
    def send_verification_email(
        self,
        to_email: str,
        verification_token: str,
        user_name: str = ""
    ) -> bool:
        """Send email verification email."""
        subject = "Verify your email address"
        
        html_content = f"""
        <html>
        <body>
            <h2>Welcome to Agent SCL!</h2>
            <p>Hello {user_name or 'there'},</p>
            <p>Please verify your email address by clicking the link below:</p>
            <p><a href="https://app.agentscl.com/verify-email?token={verification_token}">Verify Email</a></p>
            <p>If you didn't create an account, please ignore this email.</p>
            <p>Best regards,<br>The Agent SCL Team</p>
        </body>
        </html>
        """
        
        text_content = f"""
        Welcome to Agent SCL!
        
        Hello {user_name or 'there'},
        
        Please verify your email address by visiting:
        https://app.agentscl.com/verify-email?token={verification_token}
        
        If you didn't create an account, please ignore this email.
        
        Best regards,
        The Agent SCL Team
        """
        
        return self.send_email(
            to_email=to_email,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
            email_type="verification"
        )
    
    def send_password_reset_email(
        self,
        to_email: str,
        reset_token: str,
        user_name: str = ""
    ) -> bool:
        """Send password reset email."""
        subject = "Reset your password"
        
        html_content = f"""
        <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>Hello {user_name or 'there'},</p>
            <p>You requested to reset your password. Click the link below to reset it:</p>
            <p><a href="https://app.agentscl.com/reset-password?token={reset_token}">Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this, please ignore this email.</p>
            <p>Best regards,<br>The Agent SCL Team</p>
        </body>
        </html>
        """
        
        text_content = f"""
        Password Reset Request
        
        Hello {user_name or 'there'},
        
        You requested to reset your password. Visit this link to reset it:
        https://app.agentscl.com/reset-password?token={reset_token}
        
        This link will expire in 1 hour.
        
        If you didn't request this, please ignore this email.
        
        Best regards,
        The Agent SCL Team
        """
        
        return self.send_email(
            to_email=to_email,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
            email_type="password_reset"
        )


# Global email service instance
email_service = EmailService()
