# src/shared/secrets.py
# Implementado según "Security Guidelines" y "Development Standards"

"""
AWS Secrets Manager integration for secure credential management.
Handles JWT keys, API keys, and other sensitive configuration.
"""

import json
import boto3
from typing import Any, Dict, Optional
from botocore.exceptions import Client<PERSON>rror

from .config import get_settings
from .exceptions import ConfigurationException
from .logger import lambda_logger


class SecretsManager:
    """AWS Secrets Manager client for secure credential management."""

    def __init__(self):
        self.settings = get_settings()
        self.client = boto3.client('secretsmanager', region_name=self.settings.aws_region)
        self._cache = {}  # Simple in-memory cache for secrets

    def get_secret(self, secret_name: str, use_cache: bool = True) -> str:
        """
        Get a secret value from AWS Secrets Manager.

        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            use_cache: Whether to use cached value if available

        Returns:
            Secret value as string

        Raises:
            ConfigurationException: If secret cannot be retrieved
        """
        # Check cache first
        if use_cache and secret_name in self._cache:
            return self._cache[secret_name]

        try:
            response = self.client.get_secret_value(SecretId=secret_name)
            secret_value = response['SecretString']

            # Cache the secret
            if use_cache:
                self._cache[secret_name] = secret_value

            lambda_logger.info(f"Successfully retrieved secret: {secret_name}")
            return secret_value

        except ClientError as e:
            error_code = e.response['Error']['Code']

            if error_code == 'ResourceNotFoundException':
                lambda_logger.error(f"Secret not found: {secret_name}")
                raise ConfigurationException(
                    f"Secret '{secret_name}' not found in AWS Secrets Manager",
                    config_key=secret_name
                )
            elif error_code == 'InvalidRequestException':
                lambda_logger.error(f"Invalid request for secret: {secret_name}")
                raise ConfigurationException(
                    f"Invalid request for secret '{secret_name}'",
                    config_key=secret_name
                )
            elif error_code == 'InvalidParameterException':
                lambda_logger.error(f"Invalid parameter for secret: {secret_name}")
                raise ConfigurationException(
                    f"Invalid parameter for secret '{secret_name}'",
                    config_key=secret_name
                )
            else:
                lambda_logger.error(f"Failed to retrieve secret {secret_name}: {str(e)}")
                raise ConfigurationException(
                    f"Failed to retrieve secret '{secret_name}': {str(e)}",
                    config_key=secret_name
                )
        except Exception as e:
            lambda_logger.error(f"Unexpected error retrieving secret {secret_name}: {str(e)}")
            raise ConfigurationException(
                f"Unexpected error retrieving secret '{secret_name}': {str(e)}",
                config_key=secret_name
            )

    def get_secret_json(self, secret_name: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get a secret value as JSON from AWS Secrets Manager.

        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            use_cache: Whether to use cached value if available

        Returns:
            Secret value as dictionary

        Raises:
            ConfigurationException: If secret cannot be retrieved or parsed
        """
        secret_string = self.get_secret(secret_name, use_cache)

        try:
            return json.loads(secret_string)
        except json.JSONDecodeError as e:
            lambda_logger.error(f"Failed to parse secret {secret_name} as JSON: {str(e)}")
            raise ConfigurationException(
                f"Secret '{secret_name}' is not valid JSON: {str(e)}",
                config_key=secret_name
            )

    def create_secret(self, secret_name: str, secret_value: str, description: str = "") -> bool:
        """
        Create a new secret in AWS Secrets Manager.

        Args:
            secret_name: Name of the secret
            secret_value: Value of the secret
            description: Description of the secret

        Returns:
            True if secret was created successfully

        Raises:
            ConfigurationException: If secret cannot be created
        """
        try:
            self.client.create_secret(
                Name=secret_name,
                SecretString=secret_value,
                Description=description
            )

            lambda_logger.info(f"Successfully created secret: {secret_name}")
            return True

        except ClientError as e:
            error_code = e.response['Error']['Code']

            if error_code == 'ResourceExistsException':
                lambda_logger.warning(f"Secret already exists: {secret_name}")
                return False
            else:
                lambda_logger.error(f"Failed to create secret {secret_name}: {str(e)}")
                raise ConfigurationException(
                    f"Failed to create secret '{secret_name}': {str(e)}",
                    config_key=secret_name
                )
        except Exception as e:
            lambda_logger.error(f"Unexpected error creating secret {secret_name}: {str(e)}")
            raise ConfigurationException(
                f"Unexpected error creating secret '{secret_name}': {str(e)}",
                config_key=secret_name
            )

    def update_secret(self, secret_name: str, secret_value: str) -> bool:
        """
        Update an existing secret in AWS Secrets Manager.

        Args:
            secret_name: Name of the secret
            secret_value: New value of the secret

        Returns:
            True if secret was updated successfully

        Raises:
            ConfigurationException: If secret cannot be updated
        """
        try:
            self.client.update_secret(
                SecretId=secret_name,
                SecretString=secret_value
            )

            # Clear cache for this secret
            if secret_name in self._cache:
                del self._cache[secret_name]

            lambda_logger.info(f"Successfully updated secret: {secret_name}")
            return True

        except ClientError as e:
            error_code = e.response['Error']['Code']

            if error_code == 'ResourceNotFoundException':
                lambda_logger.error(f"Secret not found for update: {secret_name}")
                raise ConfigurationException(
                    f"Secret '{secret_name}' not found for update",
                    config_key=secret_name
                )
            else:
                lambda_logger.error(f"Failed to update secret {secret_name}: {str(e)}")
                raise ConfigurationException(
                    f"Failed to update secret '{secret_name}': {str(e)}",
                    config_key=secret_name
                )
        except Exception as e:
            lambda_logger.error(f"Unexpected error updating secret {secret_name}: {str(e)}")
            raise ConfigurationException(
                f"Unexpected error updating secret '{secret_name}': {str(e)}",
                config_key=secret_name
            )

    def get_jwt_keys(self) -> Dict[str, str]:
        """
        Get JWT signing keys from AWS Secrets Manager.

        Returns:
            Dictionary with 'private_key' and 'public_key'

        Raises:
            ConfigurationException: If JWT keys cannot be retrieved
        """
        secret_name = f"{self.settings.project_name}/{self.settings.environment}/jwt-keys"

        try:
            jwt_keys = self.get_secret_json(secret_name)

            if 'private_key' not in jwt_keys or 'public_key' not in jwt_keys:
                raise ConfigurationException(
                    f"JWT keys secret '{secret_name}' missing required keys",
                    config_key=secret_name
                )

            return jwt_keys

        except ConfigurationException:
            # If secret doesn't exist, create default keys for development
            if self.settings.environment == "dev":
                lambda_logger.warning("JWT keys not found, creating default keys for development")
                return self._create_default_jwt_keys(secret_name)
            else:
                raise

    def _create_default_jwt_keys(self, secret_name: str) -> Dict[str, str]:
        """
        Create secure default JWT keys for development environment.

        Args:
            secret_name: Name of the secret to create

        Returns:
            Dictionary with 'private_key' and 'public_key'
        """
        import secrets
        import base64

        # Generate cryptographically secure random keys
        private_key = base64.b64encode(secrets.token_bytes(64)).decode('utf-8')
        public_key = base64.b64encode(secrets.token_bytes(64)).decode('utf-8')

        default_keys = {
            "private_key": private_key,
            "public_key": public_key,
            "algorithm": "HS256",
            "generated_at": lambda_logger.get_timestamp(),
            "environment": self.settings.environment
        }

        try:
            self.create_secret(
                secret_name=secret_name,
                secret_value=json.dumps(default_keys),
                description=f"Auto-generated JWT keys for {self.settings.environment} environment"
            )

            lambda_logger.warning(f"Created secure auto-generated JWT keys for {self.settings.environment}")
            return default_keys

        except Exception as e:
            lambda_logger.error(f"Failed to create default JWT keys: {str(e)}")
            raise ConfigurationException(
                f"Cannot create or access JWT keys for {secret_name}",
                config_key=secret_name
            )

    def get_integration_credentials(self, service_name: str) -> Dict[str, Any]:
        """
        Get integration credentials for external services.

        Args:
            service_name: Name of the service (e.g., 'stripe', 'payu', 'n8n')

        Returns:
            Dictionary with service credentials

        Raises:
            ConfigurationException: If credentials cannot be retrieved
        """
        secret_name = f"{self.settings.project_name}/{self.settings.environment}/{service_name}-credentials"

        try:
            return self.get_secret_json(secret_name)
        except ConfigurationException as e:
            lambda_logger.warning(f"Integration credentials not found for {service_name}: {str(e)}")
            # Return empty dict for optional integrations
            return {}

    def clear_cache(self) -> None:
        """Clear the secrets cache."""
        self._cache.clear()
        lambda_logger.info("Secrets cache cleared")


# Global secrets manager instance
secrets_manager = SecretsManager()


def get_jwt_keys() -> Dict[str, str]:
    """Get JWT signing keys (legacy function for backward compatibility)."""
    return secrets_manager.get_jwt_keys()


def get_jwt_config() -> Dict[str, Any]:
    """Get JWT configuration with secure fallback."""
    secret_name = f"{secrets_manager.settings.project_name}/{secrets_manager.settings.environment}/jwt"

    try:
        return secrets_manager.get_secret_json(secret_name)
    except ConfigurationException as e:
        lambda_logger.warning(f"JWT config not found: {str(e)}")

        # Secure fallback: require environment variable
        import os
        fallback_secret = os.getenv('JWT_SECRET')

        if not fallback_secret:
            lambda_logger.critical("No JWT configuration available")
            raise ConfigurationException(
                f"JWT configuration not found in {secret_name} and no JWT_SECRET environment variable set",
                config_key=secret_name
            )

        if len(fallback_secret) < 32:
            lambda_logger.critical("Insecure JWT secret in environment variable")
            raise ConfigurationException(
                "JWT_SECRET environment variable must be at least 32 characters long",
                config_key="JWT_SECRET"
            )

        lambda_logger.warning("Using secure fallback JWT configuration from environment")
        return {
            'keys': [
                {
                    'kid': f'env-{secrets_manager.settings.environment}',
                    'secret_key': fallback_secret,
                    'active': True,
                    'created_at': lambda_logger.get_timestamp()
                }
            ],
            'algorithm': 'HS256',
            'issuer': secrets_manager.settings.project_name,
            'audience': f'{secrets_manager.settings.project_name}-api',
            'rotation_policy': {
                'rotate_every_days': 90,
                'keep_old_keys_days': 7
            }
        }


def get_integration_credentials(service_name: str) -> Dict[str, Any]:
    """Get integration credentials for a service."""
    return secrets_manager.get_integration_credentials(service_name)
