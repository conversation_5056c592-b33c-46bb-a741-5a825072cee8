# shared/python/requirements.txt
# Shared layer dependencies

# AWS SDK - Latest stable versions for Lambda
boto3==1.35.36
botocore==1.35.36

# JWT handling - Latest stable
PyJWT==2.9.0

# Password hashing - Using hashlib (compatible with Auth Service)
# bcrypt and passlib removed for Auth Service compatibility

# HTTP requests - Latest stable
requests==2.32.3

# Date/time utilities - Latest stable
python-dateutil==2.9.0

# Validation - Latest stable
email-validator==2.2.0

# Metrics and monitoring - Latest stable
psutil==6.0.0

# Additional dependencies for complete functionality
# Using Lambda-compatible version of cryptography
cryptography==43.0.1
pydantic==2.9.2
typing-extensions==4.12.2

# Lambda powertools for better observability
aws-lambda-powertools==2.43.1
