# shared/serverless.yml
# Shared Lambda Layer configuration

service: agent-scl-shared-layer

# Custom configuration
custom:
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: agent-scl

# Provider configuration
provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}

# Lambda Layers
layers:
  sharedLayer:
    path: .
    name: ${self:custom.projectName}-shared-${self:custom.stage}-fixed
    description: "Shared utilities and common code for Agent SCL services (FIXED - Cryptography Compatible)"
    compatibleRuntimes:
      - python3.11
    compatibleArchitectures:
      - x86_64
    retain: false

# Outputs consolidados
resources:
  - ${file(outputs.yml)}

# Package configuration
package:
  patterns:
    - 'python/**'
    - '!python/**/__pycache__/**'
    - '!python/**/*.pyc'
    - '!outputs.yml'
    - '!serverless.yml'
