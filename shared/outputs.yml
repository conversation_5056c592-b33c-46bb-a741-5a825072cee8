# shared/outputs.yml
# Outputs consolidados para el shared layer

Outputs:
  # Layer outputs principales
  SharedLayerLambdaLayerQualifiedArn:
    Description: "Shared Layer ARN - Qualified version for Lambda functions (FIXED)"
    Value:
      Ref: SharedLayerLambdaLayer
    Export:
      Name: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerQualifiedArn
  
  SharedLayerLambdaLayerHash:
    Description: "Shared Layer content hash for version tracking"
    Value:
      Fn::GetAtt: [SharedLayerLambdaLayer, LayerVersionArn]
    Export:
      Name: sls-${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerLambdaLayerHash
  
  # Compatibility outputs (para servicios que usen nombres alternativos)
  SharedLayerArn:
    Description: "Shared Layer ARN - Alternative name for compatibility"
    Value:
      Ref: SharedLayerLambdaLayer
    Export:
      Name: ${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerArn
  
  SharedLayerArnQualified:
    Description: "Shared Layer ARN - Fully qualified for cross-stack references"
    Value:
      Ref: SharedLayerLambdaLayer
    Export:
      Name: ${self:custom.projectName}-shared-layer-${self:custom.stage}-SharedLayerArnQualified
