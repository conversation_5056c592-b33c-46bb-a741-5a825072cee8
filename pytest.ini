[tool:pytest]
# Pytest configuration for the project
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Asyncio configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Coverage configuration
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=services
    --cov=shared
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=80

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    performance: Performance tests
    slow: Slow running tests
    auth: Authentication related tests
    payment: Payment related tests
    tenant: Tenant related tests
    user: User related tests
    email: Email service related tests
    registration: Registration flow tests
    setup: Setup service tests
    jobs: Scheduled jobs tests
    api_gateway: API Gateway tests
    orchestrator: Orchestrator service tests

# Warnings
filterwarnings =
    ignore::DeprecationWarning:botocore.*
    ignore::DeprecationWarning:boto3.*
    ignore::PendingDeprecationWarning
    ignore::pytest.PytestUnhandledCoroutineWarning
