# Agent SCL - Domain Configuration
# Supply Chain & Logistics Platform

brand:
  name: "Agent SCL"
  full_name: "Agent - Supply Chain & Logistics"
  description: "Supply Chain & Logistics Platform"

domain:
  primary: "api-platform-dev.agentscl.com"
  api: "api-platform-dev.agentscl.com"
  app: "app.agentscl.com"
  admin: "admin.agentscl.com"

ssl:
  certificate_id: "c38576b6-b73d-487b-b52c-db60d617a4ca"
  certificate_arn: "arn:aws:acm:us-east-1:************:certificate/c38576b6-b73d-487b-b52c-db60d617a4ca"
  region: "us-east-1"

aws:
  account_id: "************"
  region: "us-east-1"

environments:
  dev:
    domain: "api-platform-dev.agentscl.com"
    api_domain: "api-platform-dev.agentscl.com"
  # staging:
  #   domain: "staging.agentscl.com"
  #   api_domain: "api-staging.agentscl.com"
  # prod:
  #   domain: "agentscl.com"
  #   api_domain: "api.agentscl.com"

email:
  from_email: "<EMAIL>"
  support_email: "<EMAIL>"
  admin_email: "<EMAIL>"
