#!/bin/bash

# Script para instalar la capa shared localmente para desarrollo
# Simula el comportamiento de AWS Lambda Layers en desarrollo local

echo "🔧 Installing Agent SCL Shared Layer for Local Development"
echo "=========================================================="

# Verificar que estamos en el directorio correcto
if [ ! -d "shared/python" ]; then
    echo "❌ Error: shared/python directory not found."
    echo "   Make sure you're running this script from the project root."
    exit 1
fi

# Verificar que Python está disponible
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is not installed or not in PATH"
    exit 1
fi

# Verificar que pip está disponible
if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
    echo "❌ Error: pip is not installed or not in PATH"
    exit 1
fi

# Usar pip3 si está disponible, sino pip
PIP_CMD="pip"
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
fi

echo "📦 Using pip command: $PIP_CMD"
echo ""

# Opción 1: Instalación editable (recomendada)
echo "🚀 Installing shared layer as editable package..."
echo "   This allows changes in shared/ to be reflected immediately"
echo ""

cd shared/python

# Instalar en modo editable
$PIP_CMD install -e .

if [ $? -eq 0 ]; then
    echo "✅ Shared layer installed successfully as editable package!"
    echo ""
    echo "📋 What this means:"
    echo "   - You can import 'shared' from anywhere in your Python environment"
    echo "   - Changes in shared/ are reflected immediately (no reinstall needed)"
    echo "   - Works exactly like AWS Lambda Layers in local development"
    echo ""
    echo "🧪 Test the installation:"
    echo "   python3 -c \"from shared.models import UserRole; print('✅ Import successful!')\""
    echo ""
    echo "🔄 To uninstall later:"
    echo "   pip uninstall agent-scl-shared"
else
    echo "❌ Failed to install shared layer"
    echo ""
    echo "🔧 Alternative installation methods:"
    echo ""
    echo "Method 2 - Add to PYTHONPATH:"
    echo "   export PYTHONPATH=\"\$PYTHONPATH:$(pwd)\""
    echo ""
    echo "Method 3 - Symlink (Linux/Mac only):"
    echo "   ln -sf $(pwd)/shared /path/to/your/venv/lib/python3.11/site-packages/"
    exit 1
fi

cd ../..

echo ""
echo "🎉 Setup complete! You can now use 'from shared import ...' in your services."
echo ""
echo "💡 Pro tip: If you're using a virtual environment, make sure it's activated"
echo "   before running your serverless offline services."
