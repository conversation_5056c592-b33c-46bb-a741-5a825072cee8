{"name": "agent-scl-platform", "version": "1.0.0", "description": "Agent SCL - Supply Chain & Logistics Platform", "main": "index.js", "scripts": {"deploy": "bash scripts/deploy.sh", "deploy:dev": "bash scripts/deploy.sh --stage dev", "deploy:staging": "bash scripts/deploy.sh --stage staging", "deploy:prod": "bash scripts/deploy.sh --stage prod", "deploy:infrastructure": "bash scripts/deploy.sh --infrastructure-only", "deploy:services": "bash scripts/deploy.sh --services-only", "deploy:auth": "bash scripts/deploy.sh --services auth", "deploy:payment": "bash scripts/deploy.sh --services payment", "deploy:tenant": "bash scripts/deploy.sh --services tenant", "deploy:user": "bash scripts/deploy.sh --services user", "destroy": "bash scripts/destroy.sh", "destroy:dev": "bash scripts/destroy.sh --stage dev", "destroy:staging": "bash scripts/destroy.sh --stage staging", "destroy:prod": "bash scripts/destroy.sh --stage prod", "destroy:services": "bash scripts/destroy.sh --services-only", "destroy:infrastructure": "bash scripts/destroy.sh --infrastructure-only", "validate": "bash scripts/deploy.sh --validate-only", "validate:infrastructure": "bash scripts/deploy.sh --infrastructure-only --validate-only", "validate:services": "bash scripts/deploy.sh --services-only --validate-only", "test": "python -m pytest tests/ -v", "test:unit": "python -m pytest tests/unit/ -v", "test:integration": "python -m pytest tests/integration/ -v", "test:coverage": "python -m pytest tests/ --cov=services --cov=shared --cov-report=html --cov-report=term-missing", "lint": "python -m flake8 services/", "format": "python -m black services/", "type-check": "python -m mypy services/", "install:dev": "pip install -r requirements-dev.txt", "install:prod": "pip install -r requirements.txt", "install:all": "pip install -r requirements.txt -r requirements-dev.txt", "install:serverless": "npm install -g serverless", "install:plugins": "npm install serverless-python-requirements serverless-domain-manager serverless-plugin-tracing serverless-plugin-aws-alerts serverless-prune-plugin serverless-plugin-resource-tagging", "setup": "npm run install:serverless && npm run install:plugins", "logs:auth": "serverless logs --function login --stage dev --tail", "logs:payment": "serverless logs --function createSubscription --stage dev --tail", "logs:tenant": "serverless logs --function getProfile --stage dev --tail", "logs:user": "serverless logs --function getProfile --stage dev --tail", "info": "serverless info --stage dev", "info:auth": "cd serverless/services/auth && serverless info --stage dev", "info:payment": "cd serverless/services/payment && serverless info --stage dev", "info:tenant": "cd serverless/services/tenant && serverless info --stage dev", "info:user": "cd serverless/services/user && serverless info --stage dev"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/agent-scl-platform.git"}, "keywords": ["serverless", "aws", "lambda", "api-gateway", "dynamodb", "supply-chain", "logistics", "agent", "platform"], "author": "Platform Team", "license": "MIT", "bugs": {"url": "https://github.com/your-org/agent-scl-platform/issues"}, "homepage": "https://github.com/your-org/agent-scl-platform#readme", "devDependencies": {"serverless": "^4.17.0", "serverless-python-requirements": "^6.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"serverless-plugin-common-excludes": "^4.0.0"}}