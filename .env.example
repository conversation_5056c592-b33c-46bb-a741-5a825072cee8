# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Application Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
DYNAMODB_TABLE_NAME=agentscl-table-dev
AWS_REGION=us-east-1

# Authentication & Security
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
ENCRYPTION_KEY=your-32-character-encryption-key

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Email Configuration (SES)
FROM_EMAIL=<EMAIL>
REPLY_TO_EMAIL=<EMAIL>

# Stripe Configuration (CRITICAL - Required for Payment Processing)
# Test Environment Keys (for development)
STRIPE_TEST_SECRET_KEY=sk_test_51...your_test_secret_key_here
STRIPE_TEST_PUBLISHABLE_KEY=pk_test_51...your_test_publishable_key_here

# Production Environment Keys (for production deployment)
STRIPE_LIVE_SECRET_KEY=sk_live_51...your_live_secret_key_here
STRIPE_LIVE_PUBLISHABLE_KEY=pk_live_51...your_live_publishable_key_here

# Stripe Webhook Configuration
STRIPE_WEBHOOK_SECRET=whsec_...your_webhook_secret_here
STRIPE_WEBHOOK_TOLERANCE=300

# Legacy Stripe Configuration (deprecated - use above instead)
STRIPE_PUBLISHABLE_KEY=pk_test_...deprecated
STRIPE_SECRET_KEY=sk_test_...deprecated

# PayU Configuration (if using PayU)
PAYU_MERCHANT_ID=your_payu_merchant_id
PAYU_ACCOUNT_ID=your_payu_account_id
PAYU_API_KEY=your_payu_api_key
PAYU_API_LOGIN=your_payu_api_login

# N8N Integration (if using)
N8N_BASE_URL=https://your-n8n-instance.com
N8N_API_KEY=your_n8n_api_key
N8N_WEBHOOK_SECRET=your_n8n_webhook_secret

# IMPORTANT SECURITY NOTES:
# 1. Never commit the actual .env file to version control
# 2. Use different keys for development, staging, and production
# 3. Rotate keys regularly
# 4. Store production keys in AWS Secrets Manager or similar
# 5. Validate all Stripe keys start with correct prefixes:
#    - Test Secret: sk_test_
#    - Live Secret: sk_live_
#    - Test Publishable: pk_test_
#    - Live Publishable: pk_live_
#    - Webhook Secret: whsec_

# Stripe Key Validation Examples:
# ✅ CORRECT: STRIPE_TEST_SECRET_KEY=sk_test_51ABC123...
# ❌ WRONG:   STRIPE_TEST_SECRET_KEY=sk_live_51ABC123...
# ✅ CORRECT: STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
# ❌ WRONG:   STRIPE_WEBHOOK_SECRET=webhook_secret_123...
