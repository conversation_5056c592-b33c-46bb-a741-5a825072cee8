{"statusCode": 200, "body": "{\n  \"message\": \"Test handler working\",\n  \"python_path\": [\n    \"/var/task\",\n    \"/var/runtime\",\n    \"/opt/python\",\n    \"/var/runtime\",\n    \"/var/task\",\n    \"/var/task/src\",\n    \"/var/lang/lib/python311.zip\",\n    \"/var/lang/lib/python3.11\",\n    \"/var/lang/lib/python3.11/lib-dynload\",\n    \"/var/lang/lib/python3.11/site-packages\"\n  ],\n  \"opt_exists\": true,\n  \"opt_python_exists\": true,\n  \"opt_python_shared_exists\": true,\n  \"environment_pythonpath\": \"/opt/python:/var/runtime:/var/task:/var/task/src\",\n  \"opt_contents\": [\n    \"python\"\n  ],\n  \"opt_python_contents\": [\n    \"__init__.py\",\n    \"requirements.txt\",\n    \"shared\"\n  ],\n  \"opt_python_shared_contents\": [\n    \"__init__.py\",\n    \"auth.py\",\n    \"bulkhead.py\",\n    \"business_logic_decorator.py\",\n    \"cache.py\",\n    \"circuit_breaker.py\",\n    \"config.py\",\n    \"database.py\",\n    \"dependency_injection.py\",\n    \"email.py\",\n    \"error_handler.py\",\n    \"events.py\",\n    \"exceptions.py\",\n    \"handlers\",\n    \"health.py\",\n    \"lazy_loading.py\",\n    \"logger.py\",\n    \"metrics.py\",\n    \"middleware\",\n    \"models.py\",\n    \"query_optimizer.py\",\n    \"repository.py\",\n    \"resilience.py\",\n    \"responses.py\",\n    \"retry.py\",\n    \"saga.py\",\n    \"secrets.py\",\n    \"secrets_manager.py\",\n    \"service_configuration.py\",\n    \"transaction_manager.py\",\n    \"validation_manager.py\",\n    \"validators.py\"\n  ],\n  \"shared_import\": \"SUCCESS\",\n  \"shared_file\": \"/opt/python/shared/__init__.py\"\n}"}