#!/usr/bin/env python3
# scripts/fix_audit_issues.py
# Automated script to fix critical audit issues

"""
Automated script to fix critical issues identified in the software audit.
Addresses code duplication, inconsistencies, and standardization issues.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Any


class AuditFixer:
    """Automated fixer for audit issues."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.fixes_applied = []
        self.errors_encountered = []
    
    def fix_email_validation_duplication(self) -> None:
        """Fix duplicated email validation code."""
        print("🔧 Fixing email validation duplication...")
        
        # Files with duplicated email validation
        files_to_fix = [
            "services/auth/src/handlers/forgot_password.py",
            "services/auth/src/handlers/resend_verification.py",
            "services/orchestrator/src/handlers/registration_complete.py"
        ]
        
        for file_path in files_to_fix:
            full_path = self.project_root / file_path
            if not full_path.exists():
                self.errors_encountered.append(f"File not found: {file_path}")
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Replace duplicated email validation
                old_pattern = r"if '@' not in email or '\.' not in email:"
                new_code = """from shared.validators import validate_email_address
                try:
                    email = validate_email_address(email)
                except ValidationException as e:
                    return APIResponse.error(message=str(e), status_code=422)"""
                
                if re.search(old_pattern, content):
                    content = re.sub(old_pattern, new_code, content)
                    
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.fixes_applied.append(f"Fixed email validation in {file_path}")
                    print(f"  ✅ Fixed: {file_path}")
                else:
                    print(f"  ℹ️  No duplicated email validation found in {file_path}")
                    
            except Exception as e:
                self.errors_encountered.append(f"Error fixing {file_path}: {str(e)}")
                print(f"  ❌ Error: {file_path} - {str(e)}")
    
    def create_request_utils(self) -> None:
        """Create shared request utilities to eliminate duplication."""
        print("🔧 Creating shared request utilities...")
        
        utils_path = self.project_root / "shared/python/shared/request_utils.py"
        
        utils_content = '''#!/usr/bin/env python3
# shared/python/shared/request_utils.py
# Shared request utilities

"""
Shared utilities for handling API Gateway requests.
Eliminates code duplication across handlers.
"""

import json
from typing import Dict, Any, List, Optional

from .exceptions import ValidationException
from .validators import validate_required_fields


def parse_request_body(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse and validate request body from API Gateway event.
    
    Args:
        event: API Gateway event
        
    Returns:
        Parsed request body as dictionary
        
    Raises:
        ValidationException: If JSON is invalid
    """
    body = event.get('body', '{}')
    
    if body is None:
        return {}
    
    try:
        if isinstance(body, str):
            return json.loads(body)
        else:
            return body
    except json.JSONDecodeError as e:
        raise ValidationException(f"Invalid JSON format: {str(e)}")


def parse_and_validate_request(event: Dict[str, Any], 
                             required_fields: List[str]) -> Dict[str, Any]:
    """
    Parse request body and validate required fields.
    
    Args:
        event: API Gateway event
        required_fields: List of required field names
        
    Returns:
        Validated request data
        
    Raises:
        ValidationException: If validation fails
    """
    data = parse_request_body(event)
    validate_required_fields(data, required_fields)
    return data


def extract_path_parameters(event: Dict[str, Any], 
                          required_params: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Extract and validate path parameters.
    
    Args:
        event: API Gateway event
        required_params: List of required parameter names
        
    Returns:
        Path parameters dictionary
        
    Raises:
        ValidationException: If required parameters are missing
    """
    path_params = event.get('pathParameters') or {}
    
    if required_params:
        for param in required_params:
            if not path_params.get(param):
                raise ValidationException(f"Missing required path parameter: {param}")
    
    return path_params


def extract_query_parameters(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract query parameters from event.
    
    Args:
        event: API Gateway event
        
    Returns:
        Query parameters dictionary
    """
    return event.get('queryStringParameters') or {}
'''
        
        try:
            utils_path.parent.mkdir(parents=True, exist_ok=True)
            with open(utils_path, 'w', encoding='utf-8') as f:
                f.write(utils_content)
            
            self.fixes_applied.append("Created shared/request_utils.py")
            print("  ✅ Created shared request utilities")
            
        except Exception as e:
            self.errors_encountered.append(f"Error creating request utils: {str(e)}")
            print(f"  ❌ Error creating request utils: {str(e)}")
    
    def fix_cors_imports(self) -> None:
        """Fix inconsistent CORS imports."""
        print("🔧 Fixing CORS import inconsistencies...")
        
        # Find all handler files
        handler_files = []
        for service_dir in (self.project_root / "services").iterdir():
            if service_dir.is_dir():
                handlers_dir = service_dir / "src" / "handlers"
                if handlers_dir.exists():
                    for handler_file in handlers_dir.glob("*.py"):
                        if not handler_file.name.startswith("__"):
                            handler_files.append(handler_file)
        
        for handler_file in handler_files:
            try:
                with open(handler_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check if file uses CORS but imports it inconsistently
                if "handle_cors_preflight" in content:
                    # Check if import is at module level
                    if not re.search(r'^from shared\.responses import.*handle_cors_preflight', content, re.MULTILINE):
                        # Add import at the top
                        lines = content.split('\n')
                        import_line = "from shared.responses import APIResponse, handle_cors_preflight"
                        
                        # Find where to insert import
                        insert_index = 0
                        for i, line in enumerate(lines):
                            if line.startswith('from shared.') or line.startswith('import '):
                                insert_index = i + 1
                        
                        lines.insert(insert_index, import_line)
                        content = '\n'.join(lines)
                        
                        with open(handler_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        self.fixes_applied.append(f"Fixed CORS import in {handler_file.name}")
                        print(f"  ✅ Fixed CORS import: {handler_file.name}")
                
            except Exception as e:
                self.errors_encountered.append(f"Error fixing CORS in {handler_file}: {str(e)}")
                print(f"  ❌ Error: {handler_file.name} - {str(e)}")
    
    def standardize_handler_names(self) -> None:
        """Standardize handler function names."""
        print("🔧 Standardizing handler function names...")
        
        # Files with non-standard handler names
        files_to_fix = [
            "services/orchestrator/src/handlers/registration_complete.py",
            "services/setup/src/handlers/setup_tenant.py"
        ]
        
        for file_path in files_to_fix:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Replace specific handler names with standard 'handler'
                patterns = [
                    r'def registration_complete_handler\(',
                    r'def setup_tenant_handler\(',
                    r'def payment_validation_handler\(',
                    r'def health_aggregator_handler\('
                ]
                
                for pattern in patterns:
                    if re.search(pattern, content):
                        content = re.sub(pattern, 'def handler(', content)
                        
                        with open(full_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        self.fixes_applied.append(f"Standardized handler name in {file_path}")
                        print(f"  ✅ Standardized: {file_path}")
                        break
                
            except Exception as e:
                self.errors_encountered.append(f"Error standardizing {file_path}: {str(e)}")
                print(f"  ❌ Error: {file_path} - {str(e)}")
    
    def create_coding_standards(self) -> None:
        """Create coding standards documentation."""
        print("🔧 Creating coding standards documentation...")
        
        standards_path = self.project_root / "CODING_STANDARDS.md"
        
        standards_content = '''# 📝 Coding Standards - Logistics AI Platform

## 🎯 Overview

This document defines the coding standards and conventions for the logistics AI platform to ensure consistency, maintainability, and quality across all services.

## 🏗️ File Structure

### Handler Files
```python
# services/{service}/src/handlers/{action}.py
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """Standard handler function signature."""
    pass
```

### Service Files
```python
# services/{service}/src/services/{service}_service.py
class {Service}Service:
    """Service class for {service} operations."""
    pass
```

## 📦 Import Standards

### Order
1. Standard library imports
2. Third-party imports
3. Shared library imports
4. Local imports

### Example
```python
import os
import json
from typing import Dict, Any
from datetime import datetime

import boto3
import requests

from shared.responses import APIResponse
from shared.logger import lambda_logger
from shared.exceptions import ValidationException

from ..services.user_service import UserService
```

## 🔧 Function Standards

### Handler Functions
```python
@rate_limit(requests_per_minute=X)
@measure_performance("service_operation")
def handler(event: Dict[str, Any], context: Optional[Any] = None) -> Dict[str, Any]:
    """
    Handler description.
    
    Args:
        event: API Gateway event
        context: Lambda context
        
    Returns:
        API response dictionary
    """
    # Handle CORS preflight
    if event.get('httpMethod') == 'OPTIONS':
        return handle_cors_preflight()
    
    try:
        # Implementation
        return APIResponse.success(data=result)
    except ValidationException as e:
        return APIResponse.error(message=str(e), status_code=422)
```

## 📝 Naming Conventions

### Files
- `snake_case.py` for all Python files
- Descriptive names: `create_user.py` not `user.py`

### Functions
- `snake_case` for all functions
- Descriptive names: `validate_email_address()` not `validate()`

### Variables
- `snake_case` for variables
- Descriptive names: `user_id` not `id`

### Classes
- `PascalCase` for classes
- Descriptive names: `UserService` not `Service`

## 🚨 Error Handling

### Standard Pattern
```python
try:
    # Operation
    result = perform_operation()
    return APIResponse.success(data=result)
except ValidationException as e:
    return APIResponse.error(message=str(e), status_code=422)
except PlatformException as e:
    return APIResponse.error(message=str(e), status_code=500)
```

### Logging
```python
lambda_logger.info("Operation started", extra={
    'request_id': request_id,
    'operation': 'specific_action'
})
```

## ✅ Validation

### Use Shared Validators
```python
from shared.validators import validate_email_address, validate_required_fields

# Email validation
email = validate_email_address(email)

# Required fields
validate_required_fields(data, ['field1', 'field2'])
```

### Request Parsing
```python
from shared.request_utils import parse_and_validate_request

data = parse_and_validate_request(event, ['required_field'])
```

## 📚 Documentation

### Docstrings
```python
def function_name(param1: str, param2: int) -> Dict[str, Any]:
    """
    Brief description of function.
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        Description of return value
        
    Raises:
        ValidationException: When validation fails
    """
```

## 🧪 Testing

### Test File Naming
- `test_{feature}.py` for test files
- `Test{Feature}` for test classes
- `test_{scenario}_{expected_outcome}` for test methods

### Test Structure
```python
def test_create_user_success(self, fixtures):
    # Arrange
    user_data = {...}
    
    # Act
    result = create_user(user_data)
    
    # Assert
    assert result['success'] == True
```

## 🔄 Code Review Checklist

- [ ] Follows naming conventions
- [ ] Uses shared utilities
- [ ] Has proper error handling
- [ ] Includes comprehensive docstrings
- [ ] Has corresponding tests
- [ ] Follows import standards
- [ ] Uses standard response format
'''
        
        try:
            with open(standards_path, 'w', encoding='utf-8') as f:
                f.write(standards_content)
            
            self.fixes_applied.append("Created CODING_STANDARDS.md")
            print("  ✅ Created coding standards documentation")
            
        except Exception as e:
            self.errors_encountered.append(f"Error creating coding standards: {str(e)}")
            print(f"  ❌ Error creating coding standards: {str(e)}")
    
    def generate_report(self) -> None:
        """Generate fix report."""
        print("\n" + "="*60)
        print("📊 AUDIT FIXES REPORT")
        print("="*60)
        
        print(f"\n✅ FIXES APPLIED ({len(self.fixes_applied)}):")
        for fix in self.fixes_applied:
            print(f"  • {fix}")
        
        if self.errors_encountered:
            print(f"\n❌ ERRORS ENCOUNTERED ({len(self.errors_encountered)}):")
            for error in self.errors_encountered:
                print(f"  • {error}")
        
        print(f"\nSUMMARY:")
        print(f"  Total fixes applied: {len(self.fixes_applied)}")
        print(f"  Errors encountered: {len(self.errors_encountered)}")
        
        success_rate = len(self.fixes_applied) / (len(self.fixes_applied) + len(self.errors_encountered)) * 100 if (len(self.fixes_applied) + len(self.errors_encountered)) > 0 else 100
        print(f"  Success rate: {success_rate:.1f}%")
        
        print("="*60)


def main():
    """Main function."""
    print("🔧 Automated Audit Issue Fixer")
    print("="*50)
    
    fixer = AuditFixer()
    
    # Apply fixes
    fixer.fix_email_validation_duplication()
    fixer.create_request_utils()
    fixer.fix_cors_imports()
    fixer.standardize_handler_names()
    fixer.create_coding_standards()
    
    # Generate report
    fixer.generate_report()
    
    # Exit with appropriate code
    if fixer.errors_encountered:
        print("\n⚠️  Some issues encountered. Please review and fix manually.")
        sys.exit(1)
    else:
        print("\n🎉 All critical audit issues have been fixed!")
        sys.exit(0)


if __name__ == "__main__":
    main()
