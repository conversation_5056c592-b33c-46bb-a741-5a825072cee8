#!/bin/bash

# Script de validación y despliegue para Agent SCL Platform
# Ejecuta validaciones completas antes del despliegue

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración
STAGE=${1:-dev}
PROJECT_ROOT=$(pwd)

echo -e "${BLUE}🚀 Agent SCL Platform - Validación y Despliegue${NC}"
echo -e "${BLUE}=================================================${NC}"
echo -e "Stage: ${YELLOW}$STAGE${NC}"
echo -e "Directorio: ${YELLOW}$PROJECT_ROOT${NC}"
echo ""

# Función para mostrar mensajes
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar que estamos en el directorio correcto
if [ ! -f "serverless.yml" ]; then
    log_error "No se encontró serverless.yml. Ejecuta este script desde la raíz del proyecto."
    exit 1
fi

# 1. Validar dependencias con Python
log_info "Ejecutando validación de dependencias..."
if python3 scripts/validate-dependencies.py; then
    log_success "Validación de dependencias completada"
else
    log_error "Falló la validación de dependencias"
    exit 1
fi

echo ""

# 2. Validar sintaxis con Serverless Framework
log_info "Validando sintaxis con Serverless Framework..."

# Infraestructura principal
log_info "Validando infraestructura principal..."
if serverless print --stage $STAGE > /dev/null 2>&1; then
    log_success "Infraestructura principal: Sintaxis válida"
else
    log_error "Infraestructura principal: Error de sintaxis"
    exit 1
fi

# Servicios
SERVICES=("auth" "payment" "tenant" "user")
for service in "${SERVICES[@]}"; do
    log_info "Validando servicio: $service..."
    if (cd "serverless/services/$service" && serverless print --stage $STAGE > /dev/null 2>&1); then
        log_success "Servicio $service: Sintaxis válida"
    else
        log_error "Servicio $service: Error de sintaxis"
        exit 1
    fi
done

echo ""

# 3. Verificar que AWS CLI esté configurado
log_info "Verificando configuración de AWS..."
if aws sts get-caller-identity > /dev/null 2>&1; then
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    REGION=$(aws configure get region)
    log_success "AWS configurado - Cuenta: $ACCOUNT_ID, Región: $REGION"
else
    log_error "AWS CLI no está configurado correctamente"
    exit 1
fi

echo ""

# 4. Verificar exports existentes (si ya hay un stack desplegado)
log_info "Verificando exports existentes..."
EXISTING_EXPORTS=$(aws cloudformation list-exports --region $REGION --query "Exports[?starts_with(Name, 'agent-scl-$STAGE-')].Name" --output text 2>/dev/null || echo "")

if [ -n "$EXISTING_EXPORTS" ]; then
    log_warning "Se encontraron exports existentes:"
    echo "$EXISTING_EXPORTS" | tr '\t' '\n' | sed 's/^/  - /'
    echo ""
    read -p "¿Continuar con el despliegue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Despliegue cancelado por el usuario"
        exit 0
    fi
else
    log_info "No se encontraron exports existentes (primer despliegue)"
fi

echo ""

# 5. Función para desplegar con retry
deploy_with_retry() {
    local service_path=$1
    local service_name=$2
    local max_attempts=3
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_info "Desplegando $service_name (intento $attempt/$max_attempts)..."
        
        if (cd "$service_path" && serverless deploy --stage $STAGE); then
            log_success "$service_name desplegado exitosamente"
            return 0
        else
            log_warning "$service_name falló en el intento $attempt"
            if [ $attempt -eq $max_attempts ]; then
                log_error "$service_name falló después de $max_attempts intentos"
                return 1
            fi
            attempt=$((attempt + 1))
            sleep 10
        fi
    done
}

# 6. Despliegue ordenado
log_info "Iniciando despliegue ordenado..."

# Paso 1: Infraestructura principal
log_info "Paso 1: Desplegando infraestructura principal..."
if deploy_with_retry "." "Infraestructura Principal"; then
    log_success "Infraestructura principal desplegada"
else
    log_error "Falló el despliegue de la infraestructura principal"
    exit 1
fi

echo ""

# Paso 2: Servicio Auth (necesario para el autorizador JWT)
log_info "Paso 2: Desplegando servicio de autenticación..."
if deploy_with_retry "serverless/services/auth" "Auth Service"; then
    log_success "Servicio Auth desplegado"
else
    log_error "Falló el despliegue del servicio Auth"
    exit 1
fi

echo ""

# Paso 3: Otros servicios (en paralelo)
log_info "Paso 3: Desplegando servicios restantes..."

# Función para despliegue en paralelo
deploy_parallel() {
    local pids=()
    
    # Iniciar despliegues en background
    (deploy_with_retry "serverless/services/payment" "Payment Service") &
    pids+=($!)
    
    (deploy_with_retry "serverless/services/tenant" "Tenant Service") &
    pids+=($!)
    
    (deploy_with_retry "serverless/services/user" "User Service") &
    pids+=($!)
    
    # Esperar a que todos terminen
    local all_success=true
    for pid in "${pids[@]}"; do
        if ! wait $pid; then
            all_success=false
        fi
    done
    
    return $([ "$all_success" = true ] && echo 0 || echo 1)
}

if deploy_parallel; then
    log_success "Todos los servicios desplegados exitosamente"
else
    log_error "Algunos servicios fallaron en el despliegue"
    exit 1
fi

echo ""

# 7. Verificación post-despliegue
log_info "Ejecutando verificaciones post-despliegue..."

# Verificar que todos los exports estén disponibles
log_info "Verificando exports finales..."
FINAL_EXPORTS=$(aws cloudformation list-exports --region $REGION --query "Exports[?starts_with(Name, 'agent-scl-$STAGE-')].Name" --output text)

if [ -n "$FINAL_EXPORTS" ]; then
    log_success "Exports disponibles:"
    echo "$FINAL_EXPORTS" | tr '\t' '\n' | sed 's/^/  - /'
else
    log_warning "No se encontraron exports después del despliegue"
fi

echo ""

# 8. Resumen final
log_success "🎉 ¡Despliegue completado exitosamente!"
echo ""
echo -e "${GREEN}📋 Resumen:${NC}"
echo -e "  ✅ Infraestructura principal desplegada"
echo -e "  ✅ Servicio Auth desplegado"
echo -e "  ✅ Servicio Payment desplegado"
echo -e "  ✅ Servicio Tenant desplegado"
echo -e "  ✅ Servicio User desplegado"
echo ""
echo -e "${BLUE}🔗 Próximos pasos:${NC}"
echo -e "  1. Verificar logs en CloudWatch"
echo -e "  2. Probar endpoints de API"
echo -e "  3. Configurar monitoreo"
echo ""
echo -e "${YELLOW}📚 Documentación:${NC}"
echo -e "  - Dependencias: docs/DEPENDENCIES.md"
echo -e "  - API: docs/API.md"
echo ""

log_success "Despliegue completado en $(date)"
