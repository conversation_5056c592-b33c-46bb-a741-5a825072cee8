#!/bin/bash
# scripts/deploy-ses.sh
# Script para deployment de AWS SES configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STAGE=${1:-dev}
REGION=${2:-us-east-1}
PROJECT_NAME="the-jungle-agents"

echo -e "${BLUE}🚀 Starting SES Deployment for ${PROJECT_NAME}${NC}"
echo -e "${BLUE}📍 Stage: ${STAGE}${NC}"
echo -e "${BLUE}🌍 Region: ${REGION}${NC}"
echo ""

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ AWS CLI configured${NC}"

# Check if Serverless Framework is installed
if ! command -v serverless &> /dev/null; then
    echo -e "${RED}❌ Serverless Framework is not installed. Please install it first.${NC}"
    echo -e "${YELLOW}💡 Run: npm install -g serverless${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Serverless Framework available${NC}"

# Verify SES configuration file exists
if [ ! -f "serverless/resources/ses.yml" ]; then
    echo -e "${RED}❌ SES configuration file not found: serverless/resources/ses.yml${NC}"
    exit 1
fi

echo -e "${GREEN}✅ SES configuration file found${NC}"

# Create temporary serverless.yml for SES deployment
echo -e "${YELLOW}📝 Creating temporary SES deployment configuration...${NC}"

cat > serverless-ses.yml << EOF
service: ${PROJECT_NAME}-ses

provider:
  name: aws
  runtime: python3.12
  region: ${REGION}
  stage: ${STAGE}
  
custom:
  stage: ${STAGE}
  projectName: ${PROJECT_NAME}

resources:
  - \${file(serverless/resources/ses.yml)}

plugins:
  - serverless-python-requirements
EOF

echo -e "${GREEN}✅ Temporary configuration created${NC}"

# Deploy SES resources
echo -e "${YELLOW}🚀 Deploying SES resources...${NC}"
echo ""

if serverless deploy --config serverless-ses.yml --stage ${STAGE} --region ${REGION}; then
    echo ""
    echo -e "${GREEN}✅ SES deployment completed successfully!${NC}"
    
    # Clean up temporary file
    rm -f serverless-ses.yml
    
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo -e "${YELLOW}1. Verify domain ownership in AWS SES Console${NC}"
    echo -e "${YELLOW}2. Add DNS records for domain verification${NC}"
    echo -e "${YELLOW}3. Configure DKIM authentication${NC}"
    echo -e "${YELLOW}4. Test email sending functionality${NC}"
    echo ""
    echo -e "${BLUE}🔗 AWS SES Console: https://console.aws.amazon.com/ses/home?region=${REGION}${NC}"
    
else
    echo ""
    echo -e "${RED}❌ SES deployment failed!${NC}"
    
    # Clean up temporary file
    rm -f serverless-ses.yml
    
    exit 1
fi
