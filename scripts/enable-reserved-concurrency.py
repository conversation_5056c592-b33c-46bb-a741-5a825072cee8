#!/usr/bin/env python3
"""
Script to re-enable reserved concurrency in all serverless.yml files
after AWS increases the account concurrency limit.
"""

import os
import re
from pathlib import Path


def enable_reserved_concurrency_in_file(file_path):
    """Re-enable reserved concurrency in a single serverless.yml file."""
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match commented reservedConcurrency lines
    pattern = r'^(\s+)# reservedConcurrency:\s*(.+?)\s*# DISABLED - Account limit too low$'
    
    # Replace with uncommented version
    def replace_func(match):
        indent = match.group(1)
        value = match.group(2)
        return f"{indent}reservedConcurrency: {value}"
    
    new_content = re.sub(pattern, replace_func, content, flags=re.MULTILINE)
    
    # Count changes
    changes = len(re.findall(pattern, content, flags=re.MULTILINE))
    
    if changes > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✅ Re-enabled {changes} reservedConcurrency configurations")
    else:
        print(f"  ℹ️ No disabled reservedConcurrency configurations found")
    
    return changes


def enable_reserved_concurrency_in_variables():
    """Re-enable reserved concurrency in shared variables.yml file."""
    variables_file = Path(__file__).parent.parent / "serverless/shared/variables.yml"
    
    if not variables_file.exists():
        print(f"❌ Variables file not found: {variables_file}")
        return 0
    
    print(f"Processing variables: {variables_file}")
    
    with open(variables_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match commented reservedConcurrency lines in variables
    pattern = r'^(\s+)# reservedConcurrency:\s*(.+?)\s*# DISABLED - Account limit too low$'
    
    # Replace with uncommented version
    def replace_func(match):
        indent = match.group(1)
        value = match.group(2)
        return f"{indent}reservedConcurrency: {value}"
    
    new_content = re.sub(pattern, replace_func, content, flags=re.MULTILINE)
    
    # Count changes
    changes = len(re.findall(pattern, content, flags=re.MULTILINE))
    
    if changes > 0:
        with open(variables_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✅ Re-enabled {changes} reservedConcurrency configurations in variables")
    else:
        print(f"  ℹ️ No disabled reservedConcurrency configurations found in variables")
    
    return changes


def main():
    """Main function to process all serverless.yml files."""
    project_root = Path(__file__).parent.parent
    total_changes = 0
    
    print("🚀 Re-enabling reserved concurrency after AWS limit increase...")
    print("=" * 60)
    
    # First, re-enable in variables.yml
    variables_changes = enable_reserved_concurrency_in_variables()
    total_changes += variables_changes
    
    # Find all serverless.yml files
    serverless_files = []
    
    # Services
    services_dir = project_root / "services"
    if services_dir.exists():
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir():
                serverless_file = service_dir / "serverless.yml"
                if serverless_file.exists():
                    serverless_files.append(serverless_file)
    
    # Root serverless.yml
    root_serverless = project_root / "serverless.yml"
    if root_serverless.exists():
        serverless_files.append(root_serverless)
    
    # Shared serverless.yml
    shared_serverless = project_root / "shared" / "serverless.yml"
    if shared_serverless.exists():
        serverless_files.append(shared_serverless)
    
    # Process each file
    for file_path in serverless_files:
        changes = enable_reserved_concurrency_in_file(file_path)
        total_changes += changes
    
    print("=" * 60)
    print(f"✅ Total changes: {total_changes} reservedConcurrency configurations re-enabled")
    print()
    print("📋 Next steps:")
    print("1. Verify AWS account concurrency limit is now 1000+")
    print("2. Deploy services with optimized concurrency")
    print("3. Monitor performance improvements")
    print()
    print("💡 Check account limit with: aws lambda get-account-settings --region us-east-1")


if __name__ == "__main__":
    main()
