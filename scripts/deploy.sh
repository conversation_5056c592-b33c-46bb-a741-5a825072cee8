#!/bin/bash
# scripts/deploy.sh
# Script de deployment unificado para Serverless Framework
# Migrado desde Terraform para simplificar deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Default values
STAGE="dev"
REGION="us-east-1"
SERVICES=""
INFRASTRUCTURE_ONLY=false
SERVICES_ONLY=false
VALIDATE_ONLY=false
VERBOSE=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --stage STAGE          Deployment stage (dev, staging, prod) [default: dev]"
    echo "  -r, --region REGION        AWS region [default: us-east-1]"
    echo "  --services SERVICES        Comma-separated list of services to deploy (auth,payment,tenant,user)"
    echo "  --infrastructure-only      Deploy only infrastructure stack"
    echo "  --services-only           Deploy only services (skip infrastructure)"
    echo "  --validate-only           Only validate configurations"
    echo "  -v, --verbose             Verbose output"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --stage dev                           # Deploy everything to dev"
    echo "  $0 --stage prod --infrastructure-only    # Deploy only infrastructure to prod"
    echo "  $0 --stage staging --services auth,user  # Deploy only auth and user services to staging"
    echo "  $0 --validate-only                       # Validate all configurations"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--stage)
            STAGE="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        --services)
            SERVICES="$2"
            shift 2
            ;;
        --infrastructure-only)
            INFRASTRUCTURE_ONLY=true
            shift
            ;;
        --services-only)
            SERVICES_ONLY=true
            shift
            ;;
        --validate-only)
            VALIDATE_ONLY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate stage
if [[ ! "$STAGE" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid stage: $STAGE. Must be one of: dev, staging, prod"
    exit 1
fi

# Validate mutually exclusive options
if [[ "$INFRASTRUCTURE_ONLY" == true && "$SERVICES_ONLY" == true ]]; then
    print_error "Cannot use --infrastructure-only and --services-only together"
    exit 1
fi

# Set verbose mode
if [[ "$VERBOSE" == true ]]; then
    set -x
fi

print_header "SERVERLESS DEPLOYMENT - STAGE: $STAGE"

# Check prerequisites
print_info "Checking prerequisites..."

# Check if serverless is installed
if ! command -v serverless &> /dev/null; then
    print_error "Serverless Framework is not installed. Please install it first:"
    print_error "npm install -g serverless"
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured or credentials are invalid"
    exit 1
fi

# Check if we're in the right directory
if [[ ! -f "serverless.yml" ]]; then
    print_error "serverless.yml not found. Please run this script from the project root"
    exit 1
fi

print_success "Prerequisites check passed"

# Function to validate serverless configuration
validate_config() {
    local config_path=$1
    local service_name=$2
    
    print_info "Validating $service_name configuration..."
    
    if [[ ! -f "$config_path" ]]; then
        print_error "Configuration file not found: $config_path"
        return 1
    fi
    
    # Check YAML syntax
    if ! python -c "import yaml; yaml.safe_load(open('$config_path'))" 2>/dev/null; then
        print_error "Invalid YAML syntax in $config_path"
        return 1
    fi
    
    print_success "$service_name configuration is valid"
    return 0
}

# Function to deploy infrastructure
deploy_infrastructure() {
    print_header "DEPLOYING INFRASTRUCTURE"
    
    print_info "Validating infrastructure configuration..."
    validate_config "serverless.yml" "Infrastructure"
    
    print_info "Deploying infrastructure stack..."
    
    if [[ "$VALIDATE_ONLY" == true ]]; then
        print_info "Validation mode - skipping actual deployment"
        return 0
    fi
    
    # Deploy infrastructure
    serverless deploy \
        --stage "$STAGE" \
        --region "$REGION" \
        --verbose
    
    if [[ $? -eq 0 ]]; then
        print_success "Infrastructure deployed successfully"
    else
        print_error "Infrastructure deployment failed"
        exit 1
    fi
}

# Function to deploy a service
deploy_service() {
    local service=$1
    local service_path="services/$service"

    print_info "Deploying $service service..."

    if [[ ! -d "$service_path" ]]; then
        print_error "Service directory not found: $service_path"
        return 1
    fi
    
    # Validate service configuration
    validate_config "$service_path/serverless.yml" "$service"
    
    if [[ "$VALIDATE_ONLY" == true ]]; then
        print_info "Validation mode - skipping actual deployment"
        return 0
    fi
    
    # Change to service directory and deploy
    cd "$service_path"
    
    serverless deploy \
        --stage "$STAGE" \
        --region "$REGION" \
        --verbose
    
    local deploy_result=$?
    
    # Return to project root
    cd - > /dev/null
    
    if [[ $deploy_result -eq 0 ]]; then
        print_success "$service service deployed successfully"
    else
        print_error "$service service deployment failed"
        return 1
    fi
}

# Function to deploy services
deploy_services() {
    print_header "DEPLOYING SERVICES"
    
    local services_to_deploy
    
    if [[ -n "$SERVICES" ]]; then
        # Deploy specific services
        IFS=',' read -ra services_to_deploy <<< "$SERVICES"
    else
        # Deploy all services
        services_to_deploy=("auth" "payment" "tenant" "user")
    fi
    
    local failed_services=()
    
    for service in "${services_to_deploy[@]}"; do
        service=$(echo "$service" | xargs)  # Trim whitespace
        
        if [[ ! "$service" =~ ^(auth|payment|tenant|user)$ ]]; then
            print_warning "Unknown service: $service. Skipping..."
            continue
        fi
        
        if ! deploy_service "$service"; then
            failed_services+=("$service")
        fi
    done
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        print_error "Failed to deploy services: ${failed_services[*]}"
        exit 1
    fi
    
    print_success "All services deployed successfully"
}

# Main deployment logic
main() {
    local start_time=$(date +%s)
    
    if [[ "$VALIDATE_ONLY" == true ]]; then
        print_header "VALIDATION MODE"
    fi
    
    # Deploy infrastructure if not services-only
    if [[ "$SERVICES_ONLY" != true ]]; then
        deploy_infrastructure
    fi
    
    # Deploy services if not infrastructure-only
    if [[ "$INFRASTRUCTURE_ONLY" != true ]]; then
        deploy_services
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_header "DEPLOYMENT COMPLETED"
    print_success "Total deployment time: ${duration} seconds"
    
    if [[ "$VALIDATE_ONLY" != true ]]; then
        print_info "Infrastructure and services are now available at:"
        print_info "API Gateway: https://api-platform-${STAGE}.agentscl.com"
        print_info "CloudWatch Dashboard: https://${REGION}.console.aws.amazon.com/cloudwatch/home?region=${REGION}#dashboards:name=agent-scl-${STAGE}-platform"
    fi
}

# Run main function
main
