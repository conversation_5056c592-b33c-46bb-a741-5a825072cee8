# 🛠️ **Scripts de Validación y Despliegue**

Este directorio contiene scripts automatizados para validar y desplegar la infraestructura de Agent SCL Platform.

## 📋 **Scripts Disponibles**

### **1. validate-dependencies.py**
**Propósito**: Validación completa de dependencias entre servicios

**Características**:
- ✅ Validación de sintaxis YAML
- ✅ Verificación de exports de infraestructura
- ✅ Validación de imports entre servicios
- ✅ Verificación de convenciones de nomenclatura
- ✅ Detección de dependencias faltantes

**Uso**:
```bash
# Ejecutar validación
python scripts/validate-dependencies.py

# Desde cualquier directorio
python /path/to/project/scripts/validate-dependencies.py
```

**Salida Esperada**:
```
🚀 Iniciando validación de dependencias para Agent SCL Platform
============================================================
🔍 Validando sintaxis YAML...
✅ serverless.yml: Sintaxis válida
✅ auth/serverless.yml: Sintaxis válida
...

🏗️ Validando exports de infraestructura...
✅ Todos los exports requeridos están presentes

🔒 Validando exports del servicio auth...
✅ Export del autorizador JWT presente

🔗 Validando dependencias de servicios...
✅ auth: Todas las dependencias satisfechas
✅ payment: Todas las dependencias satisfechas
...

📝 Validando convenciones de nomenclatura...
✅ Todas las convenciones de nomenclatura son correctas

============================================================
🎉 ¡Todas las validaciones pasaron exitosamente!
✅ La infraestructura está lista para el despliegue
```

---

### **2. validate-and-deploy.sh**
**Propósito**: Script completo de validación y despliegue automatizado

**Características**:
- ✅ Validación previa con Python script
- ✅ Validación de sintaxis con Serverless Framework
- ✅ Verificación de configuración AWS
- ✅ Despliegue ordenado con reintentos
- ✅ Despliegue paralelo de servicios independientes
- ✅ Verificación post-despliegue

**Uso**:
```bash
# Desplegar en dev (por defecto)
./scripts/validate-and-deploy.sh

# Desplegar en staging
./scripts/validate-and-deploy.sh staging

# Desplegar en prod
./scripts/validate-and-deploy.sh prod
```

**Flujo de Despliegue**:
1. **Validación de dependencias** (Python)
2. **Validación de sintaxis** (Serverless)
3. **Verificación AWS** (Credenciales y región)
4. **Verificación de exports existentes**
5. **Despliegue de infraestructura principal**
6. **Despliegue del servicio Auth**
7. **Despliegue paralelo de otros servicios**
8. **Verificación post-despliegue**

---

## 🔧 **Requisitos**

### **Software Necesario**:
- **Python 3.7+** con PyYAML
- **Node.js 18+** 
- **Serverless Framework 4.x**
- **AWS CLI** configurado
- **Bash** (para el script de despliegue)

### **Instalación de Dependencias**:
```bash
# Python dependencies
pip install PyYAML

# Node.js dependencies
npm install -g serverless

# AWS CLI (si no está instalado)
# Windows: https://aws.amazon.com/cli/
# macOS: brew install awscli
# Linux: apt-get install awscli
```

### **Configuración AWS**:
```bash
# Configurar credenciales
aws configure

# Verificar configuración
aws sts get-caller-identity
```

---

## 🚨 **Solución de Problemas**

### **Error: "No se encontró serverless.yml"**
**Causa**: Script ejecutado desde directorio incorrecto
**Solución**: Ejecutar desde la raíz del proyecto

### **Error: "AWS CLI no está configurado"**
**Causa**: Credenciales AWS no configuradas
**Solución**: 
```bash
aws configure
# Ingresar Access Key, Secret Key, región
```

### **Error: "Dependencias faltantes"**
**Causa**: Referencias a exports que no existen
**Solución**: 
1. Verificar que la infraestructura esté desplegada
2. Revisar nombres de exports en CloudFormation
3. Actualizar referencias en servicios

### **Error: "Sintaxis YAML inválida"**
**Causa**: Error de formato en archivos serverless.yml
**Solución**:
1. Verificar indentación (usar espacios, no tabs)
2. Verificar comillas y caracteres especiales
3. Usar validador YAML online

### **Error: "Plugin no encontrado"**
**Causa**: Plugins de Serverless no instalados
**Solución**:
```bash
npm install serverless-python-requirements
npm install serverless-domain-manager
# ... otros plugins según sea necesario
```

---

## 📊 **Logs y Debugging**

### **Logs del Script Python**:
- Salida detallada en consola
- Códigos de salida: 0 (éxito), 1 (error)

### **Logs del Script Bash**:
- Colores para diferentes tipos de mensajes
- Logs de cada paso del despliegue
- Información de reintentos

### **Debugging Serverless**:
```bash
# Modo verbose
serverless deploy --stage dev --verbose

# Solo validar sin desplegar
serverless print --stage dev

# Ver logs de CloudFormation
serverless logs -f function-name --stage dev
```

---

## 🔄 **Integración con CI/CD**

### **GitHub Actions**:
```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          pip install PyYAML
          npm install -g serverless
      
      - name: Configure AWS
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: aws configure set region us-east-1
      
      - name: Validate and Deploy
        run: ./scripts/validate-and-deploy.sh prod
```

### **GitLab CI**:
```yaml
deploy:
  stage: deploy
  image: node:18
  before_script:
    - apt-get update && apt-get install -y python3 python3-pip
    - pip3 install PyYAML
    - npm install -g serverless
  script:
    - ./scripts/validate-and-deploy.sh prod
  only:
    - main
```

---

## 📈 **Métricas y Monitoreo**

### **Tiempo de Despliegue Típico**:
- **Infraestructura**: 5-10 minutos
- **Servicio Auth**: 2-3 minutos  
- **Otros servicios**: 2-3 minutos cada uno (paralelo)
- **Total**: 10-15 minutos

### **Puntos de Fallo Comunes**:
1. **Límites de AWS** (IAM roles, VPC limits)
2. **Timeouts de CloudFormation**
3. **Conflictos de nombres de recursos**
4. **Permisos insuficientes**

### **Monitoreo Post-Despliegue**:
- Verificar CloudWatch Logs
- Probar endpoints de API Gateway
- Verificar métricas de Lambda
- Revisar alarmas de CloudWatch

---

## 🔗 **Referencias**

- [Documentación de Dependencias](../docs/DEPENDENCIES.md)
- [Serverless Framework Docs](https://www.serverless.com/framework/docs/)
- [AWS CloudFormation Docs](https://docs.aws.amazon.com/cloudformation/)
- [AWS CLI Reference](https://docs.aws.amazon.com/cli/)
