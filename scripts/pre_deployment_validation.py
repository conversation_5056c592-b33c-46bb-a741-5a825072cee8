#!/usr/bin/env python3
# scripts/pre_deployment_validation.py
# Pre-deployment validation script

"""
Pre-deployment validation script.
Validates all services are ready for deployment.
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import List, Dict, Any, Tuple


class PreDeploymentValidator:
    """Pre-deployment validation manager."""
    
    def __init__(self):
        """Initialize validator."""
        self.project_root = Path(__file__).parent.parent
        self.validation_results = []
        self.errors = []
        self.warnings = []
        
        # Services to validate
        self.services = [
            'shared',
            'services/auth',
            'services/tenant',
            'services/payment',
            'services/setup',
            'services/orchestrator',
            'services/jobs'
        ]
    
    def validate_service_structure(self, service_path: str) -> Tuple[bool, List[str]]:
        """
        Validate service structure.
        
        Args:
            service_path: Path to service
            
        Returns:
            Tuple of (is_valid, issues)
        """
        issues = []
        full_path = self.project_root / service_path
        
        if not full_path.exists():
            issues.append(f"Service directory does not exist: {service_path}")
            return False, issues
        
        # Check serverless.yml
        serverless_file = full_path / "serverless.yml"
        if not serverless_file.exists():
            issues.append(f"serverless.yml missing in {service_path}")
            return False, issues
        
        # Validate serverless.yml syntax
        try:
            with open(serverless_file, 'r') as f:
                yaml.safe_load(f)
        except yaml.YAMLError as e:
            issues.append(f"Invalid YAML in {service_path}/serverless.yml: {str(e)}")
            return False, issues
        
        # Check for src directory (except shared)
        if service_path != 'shared':
            src_dir = full_path / "src"
            if not src_dir.exists():
                issues.append(f"src directory missing in {service_path}")
                return False, issues
            
            # Check for handlers directory
            handlers_dir = src_dir / "handlers"
            if not handlers_dir.exists():
                issues.append(f"handlers directory missing in {service_path}/src")
                return False, issues
        
        return True, issues
    
    def validate_dependencies(self, service_path: str) -> Tuple[bool, List[str]]:
        """
        Validate service dependencies.
        
        Args:
            service_path: Path to service
            
        Returns:
            Tuple of (is_valid, issues)
        """
        issues = []
        full_path = self.project_root / service_path
        
        # Check requirements.txt for Python services
        requirements_file = full_path / "requirements.txt"
        if requirements_file.exists():
            try:
                with open(requirements_file, 'r') as f:
                    requirements = f.read()
                    if not requirements.strip():
                        issues.append(f"Empty requirements.txt in {service_path}")
            except Exception as e:
                issues.append(f"Error reading requirements.txt in {service_path}: {str(e)}")
        
        # Check package.json for Node.js dependencies
        package_file = full_path / "package.json"
        if package_file.exists():
            try:
                with open(package_file, 'r') as f:
                    package_data = json.load(f)
                    if 'dependencies' not in package_data and 'devDependencies' not in package_data:
                        issues.append(f"No dependencies in package.json for {service_path}")
            except Exception as e:
                issues.append(f"Error reading package.json in {service_path}: {str(e)}")
        
        return len(issues) == 0, issues
    
    def validate_serverless_config(self, service_path: str) -> Tuple[bool, List[str]]:
        """
        Validate serverless configuration.
        
        Args:
            service_path: Path to service
            
        Returns:
            Tuple of (is_valid, issues)
        """
        issues = []
        full_path = self.project_root / service_path
        serverless_file = full_path / "serverless.yml"
        
        try:
            with open(serverless_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check required fields
            required_fields = ['service', 'provider', 'functions']
            for field in required_fields:
                if field not in config:
                    issues.append(f"Missing required field '{field}' in {service_path}/serverless.yml")
            
            # Check provider configuration
            if 'provider' in config:
                provider = config['provider']
                if provider.get('name') != 'aws':
                    issues.append(f"Provider should be 'aws' in {service_path}")
                
                if 'runtime' not in provider:
                    issues.append(f"Missing runtime in provider config for {service_path}")
            
            # Check functions
            if 'functions' in config:
                functions = config['functions']
                if not functions:
                    issues.append(f"No functions defined in {service_path}")
                
                for func_name, func_config in functions.items():
                    if 'handler' not in func_config:
                        issues.append(f"Missing handler for function '{func_name}' in {service_path}")
            
        except Exception as e:
            issues.append(f"Error validating serverless config for {service_path}: {str(e)}")
        
        return len(issues) == 0, issues
    
    def validate_handler_files(self, service_path: str) -> Tuple[bool, List[str]]:
        """
        Validate handler files exist.
        
        Args:
            service_path: Path to service
            
        Returns:
            Tuple of (is_valid, issues)
        """
        issues = []
        
        if service_path == 'shared':
            return True, issues  # Skip for shared layer
        
        full_path = self.project_root / service_path
        serverless_file = full_path / "serverless.yml"
        
        try:
            with open(serverless_file, 'r') as f:
                config = yaml.safe_load(f)
            
            if 'functions' in config:
                for func_name, func_config in config['functions'].items():
                    if 'handler' in func_config:
                        handler_path = func_config['handler']
                        
                        # Convert handler path to file path
                        # e.g., "src.handlers.register.handler" -> "src/handlers/register.py"
                        parts = handler_path.split('.')
                        if len(parts) >= 2:
                            file_path = '/'.join(parts[:-1]) + '.py'
                            full_handler_path = full_path / file_path
                            
                            if not full_handler_path.exists():
                                issues.append(f"Handler file missing: {service_path}/{file_path}")
        
        except Exception as e:
            issues.append(f"Error validating handlers for {service_path}: {str(e)}")
        
        return len(issues) == 0, issues
    
    def validate_environment_variables(self) -> Tuple[bool, List[str]]:
        """
        Validate required environment variables.
        
        Returns:
            Tuple of (is_valid, issues)
        """
        issues = []
        
        # Check for AWS credentials
        aws_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY']
        for var in aws_vars:
            if var not in os.environ:
                issues.append(f"Missing AWS environment variable: {var}")
        
        # Check for AWS region
        if 'AWS_DEFAULT_REGION' not in os.environ and 'AWS_REGION' not in os.environ:
            issues.append("Missing AWS region environment variable (AWS_DEFAULT_REGION or AWS_REGION)")
        
        return len(issues) == 0, issues
    
    def validate_all_services(self) -> bool:
        """
        Validate all services.
        
        Returns:
            True if all validations pass
        """
        print("🔍 Starting Pre-Deployment Validation")
        print("=" * 60)
        
        all_valid = True
        
        # Validate environment
        print("\n🌍 Validating Environment Variables...")
        env_valid, env_issues = self.validate_environment_variables()
        if env_valid:
            print("✅ Environment variables valid")
        else:
            print("❌ Environment validation failed")
            self.errors.extend(env_issues)
            all_valid = False
        
        # Validate each service
        for service_path in self.services:
            print(f"\n📦 Validating {service_path}...")
            
            service_valid = True
            service_issues = []
            
            # Structure validation
            struct_valid, struct_issues = self.validate_service_structure(service_path)
            if not struct_valid:
                service_valid = False
                service_issues.extend(struct_issues)
            
            # Dependencies validation
            deps_valid, deps_issues = self.validate_dependencies(service_path)
            if not deps_valid:
                service_issues.extend(deps_issues)
            
            # Serverless config validation
            config_valid, config_issues = self.validate_serverless_config(service_path)
            if not config_valid:
                service_valid = False
                service_issues.extend(config_issues)
            
            # Handler files validation
            handlers_valid, handlers_issues = self.validate_handler_files(service_path)
            if not handlers_valid:
                service_valid = False
                service_issues.extend(handlers_issues)
            
            # Report service validation result
            if service_valid:
                print(f"✅ {service_path} validation passed")
                if service_issues:
                    print(f"⚠️  {len(service_issues)} warnings:")
                    for issue in service_issues:
                        print(f"   • {issue}")
                    self.warnings.extend(service_issues)
            else:
                print(f"❌ {service_path} validation failed")
                for issue in service_issues:
                    print(f"   • {issue}")
                self.errors.extend(service_issues)
                all_valid = False
        
        # Generate validation report
        self.generate_validation_report()
        
        return all_valid
    
    def generate_validation_report(self) -> None:
        """Generate validation report."""
        print("\n" + "=" * 60)
        print("📊 VALIDATION REPORT")
        print("=" * 60)
        
        print(f"\n📦 Services Validated: {len(self.services)}")
        print(f"❌ Errors Found: {len(self.errors)}")
        print(f"⚠️  Warnings Found: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ ERRORS:")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS:")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        if len(self.errors) == 0:
            print(f"\n✅ VALIDATION SUCCESSFUL!")
            print(f"   All services are ready for deployment")
            if self.warnings:
                print(f"   ⚠️  {len(self.warnings)} warnings should be reviewed")
        else:
            print(f"\n❌ VALIDATION FAILED!")
            print(f"   {len(self.errors)} errors must be fixed before deployment")
        
        print("=" * 60)


def main():
    """Main function."""
    validator = PreDeploymentValidator()
    
    success = validator.validate_all_services()
    
    if success:
        print("\n🎉 Pre-deployment validation passed!")
        print("🚀 Ready for deployment!")
        sys.exit(0)
    else:
        print("\n🚨 Pre-deployment validation failed!")
        print("🔧 Fix the errors above before deploying")
        sys.exit(1)


if __name__ == "__main__":
    main()
