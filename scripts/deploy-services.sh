#!/bin/bash

# scripts/deploy-services.sh
# Deployment script for Agent SCL - Supply Chain & Logistics services
# Supports deployment to dev, staging, and production environments

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-dev}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        dev|staging|prod)
            log_info "Deploying to environment: $ENVIRONMENT"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            log_error "Valid environments: dev, staging, prod"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if serverless is installed
    if ! command -v serverless &> /dev/null; then
        log_error "Serverless Framework is not installed"
        log_error "Install with: npm install -g serverless"
        exit 1
    fi
    
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi
    
    # Check if Python dependencies are installed
    if [ ! -d "venv" ] && [ ! -f "requirements.txt" ]; then
        log_warning "Virtual environment not found, creating one..."
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
    fi
    
    log_success "Prerequisites check passed"
}

# Set environment variables
set_environment_variables() {
    log_info "Setting environment variables for $ENVIRONMENT..."
    
    case $ENVIRONMENT in
        dev)
            export STAGE=dev
            export AWS_REGION=us-east-1
            export DYNAMODB_TABLE=agent-scl-main-dev
            export S3_BUCKET=agent-scl-data-dev
            ;;
        staging)
            export STAGE=staging
            export AWS_REGION=us-east-1
            export DYNAMODB_TABLE=agent-scl-main-staging
            export S3_BUCKET=agent-scl-data-staging
            ;;
        prod)
            export STAGE=prod
            export AWS_REGION=us-east-1
            export DYNAMODB_TABLE=agent-scl-main-prod
            export S3_BUCKET=agent-scl-data-prod
            ;;
    esac
    
    log_success "Environment variables set for $ENVIRONMENT"
}

# Package services
package_services() {
    log_info "Packaging services..."
    
    cd "$PROJECT_ROOT"
    
    # Create deployment package
    if [ -d "dist" ]; then
        rm -rf dist
    fi
    mkdir -p dist
    
    # Copy source code
    cp -r src/ dist/
    
    # Copy requirements
    cp requirements.txt dist/
    
    # Install dependencies in dist directory
    pip install -r requirements.txt -t dist/
    
    log_success "Services packaged successfully"
}

# Deploy infrastructure first
deploy_infrastructure() {
    log_info "Deploying infrastructure with Terraform..."

    cd "$PROJECT_ROOT/terraform/environments/$ENVIRONMENT"

    # Initialize and deploy Terraform
    terraform init
    terraform plan -out=tfplan
    terraform apply tfplan

    log_success "Infrastructure deployed successfully"
}

# Deploy shared resources (layers, security groups)
deploy_shared_resources() {
    log_info "Deploying shared resources..."

    cd "$PROJECT_ROOT/serverless/services/shared"

    # Deploy using serverless
    serverless deploy \
        --stage $STAGE \
        --region $AWS_REGION \
        --verbose

    log_success "Shared resources deployed successfully"
}

# Deploy API Gateway
deploy_api_gateway() {
    log_info "Deploying API Gateway..."

    cd "$PROJECT_ROOT/serverless/services/api-gateway"

    # Deploy using serverless
    serverless deploy \
        --stage $STAGE \
        --region $AWS_REGION \
        --verbose

    log_success "API Gateway deployed successfully"
}

# Deploy auth service
deploy_auth_service() {
    log_info "Deploying auth service..."

    cd "$PROJECT_ROOT/serverless/services/auth"

    # Deploy using serverless
    serverless deploy \
        --stage $STAGE \
        --region $AWS_REGION \
        --verbose

    log_success "Auth service deployed successfully"
}

# Deploy tenant service
deploy_tenant_service() {
    log_info "Deploying tenant service..."

    # Check if tenant service exists
    if [ -d "$PROJECT_ROOT/serverless/services/tenant" ]; then
        cd "$PROJECT_ROOT/serverless/services/tenant"

        # Deploy using serverless
        serverless deploy \
            --stage $STAGE \
            --region $AWS_REGION \
            --verbose

        log_success "Tenant service deployed successfully"
    else
        log_warning "Tenant service not found, skipping..."
    fi
}

# Deploy payment service
deploy_payment_service() {
    log_info "Deploying payment service..."

    # Check if payment service exists
    if [ -d "$PROJECT_ROOT/serverless/services/payment" ]; then
        cd "$PROJECT_ROOT/serverless/services/payment"

        # Deploy using serverless
        serverless deploy \
            --stage $STAGE \
            --region $AWS_REGION \
            --verbose

        log_success "Payment service deployed successfully"
    else
        log_warning "Payment service not found, skipping..."
    fi
}

# Run post-deployment tests
run_post_deployment_tests() {
    log_info "Running post-deployment tests..."
    
    cd "$PROJECT_ROOT"
    
    # Set test environment variables
    export ENVIRONMENT=$ENVIRONMENT
    export API_BASE_URL="https://api-$STAGE.thejungleagents.com"
    
    # Run smoke tests
    if [ -f "tests/e2e/smoke_tests.py" ]; then
        python -m pytest tests/e2e/smoke_tests.py \
            --environment=$ENVIRONMENT \
            -v \
            --tb=short
        
        if [ $? -eq 0 ]; then
            log_success "Post-deployment tests passed"
        else
            log_error "Post-deployment tests failed"
            exit 1
        fi
    else
        log_warning "No smoke tests found, skipping..."
    fi
}

# Update API documentation
update_api_documentation() {
    log_info "Updating API documentation..."
    
    cd "$PROJECT_ROOT"
    
    # Generate OpenAPI spec
    if [ -f "scripts/generate-openapi.py" ]; then
        python scripts/generate-openapi.py --environment=$ENVIRONMENT
        log_success "API documentation updated"
    else
        log_warning "OpenAPI generation script not found, skipping..."
    fi
}

# Send deployment notification
send_deployment_notification() {
    log_info "Sending deployment notification..."
    
    # This would typically integrate with Slack, Teams, or email
    # For now, just log the deployment completion
    
    DEPLOYMENT_TIME=$(date '+%Y-%m-%d %H:%M:%S UTC')
    API_URL="https://api-$STAGE.thejungleagents.com"
    
    cat << EOF
========================================
🚀 DEPLOYMENT COMPLETED SUCCESSFULLY 🚀
========================================
Environment: $ENVIRONMENT
Stage: $STAGE
Time: $DEPLOYMENT_TIME
API URL: $API_URL
Region: $AWS_REGION
========================================
EOF
    
    log_success "Deployment notification sent"
}

# Rollback function
rollback_deployment() {
    log_error "Deployment failed, initiating rollback..."
    
    # This would implement rollback logic
    # For now, just log the rollback intention
    
    log_warning "Rollback functionality not implemented yet"
    log_warning "Manual rollback may be required"
    
    exit 1
}

# Main deployment function
main() {
    log_info "Starting deployment process..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Timestamp: $(date)"
    
    # Trap errors and rollback
    trap rollback_deployment ERR
    
    # Execute deployment steps
    validate_environment
    check_prerequisites
    set_environment_variables
    package_services
    
    # Deploy infrastructure and services in order
    deploy_infrastructure
    deploy_shared_resources
    deploy_api_gateway
    deploy_auth_service
    deploy_tenant_service
    deploy_payment_service
    
    # Post-deployment tasks
    run_post_deployment_tests
    update_api_documentation
    send_deployment_notification
    
    log_success "Deployment completed successfully!"
    log_info "API URL: https://api-$STAGE.thejungleagents.com"
    log_info "Environment: $ENVIRONMENT"
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [ENVIRONMENT]

Deploy Agent SCL - Supply Chain & Logistics services to specified environment.

ENVIRONMENT:
    dev         Deploy to development environment
    staging     Deploy to staging environment
    prod        Deploy to production environment

Examples:
    $0 dev      Deploy to development
    $0 staging  Deploy to staging
    $0 prod     Deploy to production

Options:
    -h, --help  Show this help message

Prerequisites:
    - AWS CLI configured with appropriate credentials
    - Serverless Framework installed
    - Python 3.11+ with virtual environment
    - Terraform (for infrastructure deployment)

EOF
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    "")
        log_warning "No environment specified, defaulting to 'dev'"
        ENVIRONMENT="dev"
        ;;
    *)
        ENVIRONMENT="$1"
        ;;
esac

# Run main function
main
