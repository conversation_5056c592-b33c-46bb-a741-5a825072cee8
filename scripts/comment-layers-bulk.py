#!/usr/bin/env python3
"""
Script to comment all layers references in serverless.yml files
"""

import os
import re
from pathlib import Path


def comment_layers_in_file(file_path):
    """Comment layers references in a serverless.yml file."""
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    changes = 0
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this line contains "layers:" (not already commented)
        if re.match(r'^(\s+)layers:\s*$', line) and not line.strip().startswith('#'):
            indent = re.match(r'^(\s+)', line).group(1)
            # Comment this line
            new_lines.append(f"{indent}# layers:  # DISABLED - Coordinated deployment\n")
            changes += 1
            
            # Check next line for layer reference
            if i + 1 < len(lines):
                next_line = lines[i + 1]
                if re.match(r'^(\s+)- \$\{self:custom\.sharedLayerArn\}\s*$', next_line):
                    next_indent = re.match(r'^(\s+)', next_line).group(1)
                    new_lines.append(f"{next_indent}#   - ${{self:custom.sharedLayerArn}}  # DISABLED - Coordinated deployment\n")
                    i += 1  # Skip the next line since we processed it
                    changes += 1
        else:
            new_lines.append(line)
        
        i += 1
    
    if changes > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        print(f"  ✅ Commented {changes} layer references")
    else:
        print(f"  ℹ️ No layer references found to comment")
    
    return changes


def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    total_changes = 0
    
    print("🔧 Commenting layers references in all services...")
    print("=" * 60)
    
    # Process all service serverless.yml files
    services_dir = project_root / "services"
    if services_dir.exists():
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir():
                serverless_file = service_dir / "serverless.yml"
                if serverless_file.exists():
                    changes = comment_layers_in_file(serverless_file)
                    total_changes += changes
    
    print("=" * 60)
    print(f"✅ Total changes: {total_changes} layer references commented")


if __name__ == "__main__":
    main()
