#!/usr/bin/env python3
"""
Auditoría final exhaustiva - Validación 10/10
Verifica TODOS los aspectos: imports, referencias, lógica de negocio, coherencia
"""

import os
import re
import glob
import ast
from pathlib import Path
from typing import Dict, List, Any

class ExhaustiveAuditor:
    def __init__(self):
        self.issues = []
        self.critical_issues = []
        self.warnings = []
        self.total_files = 0
        self.perfect_files = 0
        
    def audit_file(self, file_path: str) -> Dict[str, Any]:
        """Audita un archivo específico exhaustivamente"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 1. AUDITORÍA DE IMPORTS
            import_issues = self._audit_imports(content, file_path)
            issues.extend(import_issues)
            
            # 2. AUDITORÍA DE LÓGICA DE NEGOCIO
            logic_issues = self._audit_business_logic(content, file_path)
            issues.extend(logic_issues)
            
            # 3. AUDITORÍA DE COHERENCIA
            coherence_issues = self._audit_coherence(content, file_path)
            issues.extend(coherence_issues)
            
            # 4. AUDITORÍA DE FUNCIONALIDAD
            functionality_issues = self._audit_functionality(content, file_path)
            issues.extend(functionality_issues)
            
            return {
                'file': file_path,
                'issues': issues,
                'critical_count': len([i for i in issues if i.startswith('🚨')]),
                'warning_count': len([i for i in issues if i.startswith('⚠️')]),
                'is_perfect': len(issues) == 0
            }
            
        except Exception as e:
            return {
                'file': file_path,
                'issues': [f"🚨 Error leyendo archivo: {e}"],
                'critical_count': 1,
                'warning_count': 0,
                'is_perfect': False
            }
    
    def _audit_imports(self, content: str, file_path: str) -> List[str]:
        """Audita imports exhaustivamente"""
        issues = []
        
        # 1. Verificar imports incorrectos de shared layer
        if re.search(r'from \.\.\.shared\.', content):
            issues.append("🚨 CRÍTICO: Import incorrecto '...shared.' en lugar de 'shared.'")
        
        # 2. Verificar logger incorrecto
        if re.search(r'(\s+)logger\.', content) and 'lambda_logger' not in content:
            issues.append("🚨 CRÍTICO: Uso de 'logger.' sin lambda_logger importado")
        
        # 3. Verificar referencias cruzadas entre servicios
        if re.search(r'from src\.', content):
            issues.append("🚨 CRÍTICO: Referencia absoluta incorrecta 'from src.'")
        
        # 4. Verificar funciones usadas sin importar
        functions_to_check = [
            'log_security_event', 'audit_log', 'validate_request_body',
            'require_auth', 'rate_limit', 'with_retry'
        ]

        for func in functions_to_check:
            if f'{func}(' in content and func not in content.split(f'{func}(')[0]:
                issues.append(f"🚨 CRÍTICO: Función '{func}' usada sin importar")
        
        # 5. Verificar validadores específicos
        validators = [
            'LoginRequestValidator', 'RegisterRequestValidator',
            'CreateSubscriptionRequestValidator', 'ForgotPasswordRequestValidator'
        ]

        for validator in validators:
            if f'{validator}' in content and validator not in content.split(f'{validator}')[0]:
                issues.append(f"⚠️ Validador '{validator}' usado sin importar específicamente")
        
        return issues
    
    def _audit_business_logic(self, content: str, file_path: str) -> List[str]:
        """Audita lógica de negocio exhaustivamente"""
        issues = []
        
        # 1. Verificar handlers sin validación
        if 'def handler(' in content:
            if 'validate_request_body' not in content and 'event.get(' in content:
                issues.append("🚨 CRÍTICO: Handler sin validación de entrada")
        
        # 2. Verificar autenticación en handlers críticos
        if 'def handler(' in content and any(x in file_path for x in ['payment', 'user', 'admin']):
            if '@require_auth' not in content and 'auth_context' not in content:
                issues.append("🚨 CRÍTICO: Handler crítico sin autenticación")
        
        # 3. Verificar manejo de errores
        if 'try:' in content:
            if 'except Exception as e:' not in content:
                issues.append("⚠️ Try block sin manejo genérico de excepciones")
            elif 'lambda_logger.error' not in content:
                issues.append("⚠️ Manejo de errores sin logging")
        
        # 4. Verificar operaciones críticas
        critical_ops = ['create_subscription', 'delete_tenant', 'create_user']
        for op in critical_ops:
            if op in content and 'transaction' not in content and 'saga' not in content:
                issues.append(f"⚠️ Operación crítica '{op}' sin transacción atómica")
        
        # 5. Verificar multi-tenancy
        if 'tenant_id' in content and 'def ' in content:
            if 'auth_context.tenant_id' not in content and 'validate_tenant' not in content:
                issues.append("⚠️ Operación multi-tenant sin validación de tenant")
        
        return issues
    
    def _audit_coherence(self, content: str, file_path: str) -> List[str]:
        """Audita coherencia arquitectural"""
        issues = []
        
        # 1. Verificar consistencia de decoradores
        if '@auth_resilience' in content or '@payment_resilience' in content:
            if 'resilience_middleware' not in content:
                issues.append("🚨 CRÍTICO: Decorador resilience sin import")
        
        # 2. Verificar consistencia de responses
        if 'def handler(' in content:
            if 'APIResponse' not in content:
                issues.append("🚨 CRÍTICO: Handler sin APIResponse")
        
        # 3. Verificar consistencia de logging
        if 'def handler(' in content:
            if 'lambda_logger' not in content:
                issues.append("🚨 CRÍTICO: Handler sin lambda_logger")
        
        # 4. Verificar patrones de naming
        if 'def handler(' in content:
            if not re.search(r'""".*?"""', content, re.DOTALL):
                issues.append("⚠️ Handler sin documentación")
        
        return issues
    
    def _audit_functionality(self, content: str, file_path: str) -> List[str]:
        """Audita funcionalidad completa"""
        issues = []
        
        # 1. Verificar funcionalidad de auth
        if 'login' in file_path and 'def handler(' in content:
            required_elements = ['email', 'password', 'jwt', 'refresh_token']
            for element in required_elements:
                if element not in content.lower():
                    issues.append(f"🚨 CRÍTICO: Login sin '{element}'")
        
        # 2. Verificar funcionalidad de payment
        if 'subscription' in file_path and 'create' in file_path:
            required_elements = ['plan_id', 'stripe', 'customer']
            for element in required_elements:
                if element not in content.lower():
                    issues.append(f"🚨 CRÍTICO: Subscription sin '{element}'")
        
        # 3. Verificar funcionalidad de user management
        if 'user' in file_path and any(x in file_path for x in ['create', 'update', 'delete']):
            if 'tenant_id' not in content:
                issues.append("🚨 CRÍTICO: User operation sin tenant_id")
        
        return issues
    
    def run_exhaustive_audit(self):
        """Ejecuta auditoría exhaustiva completa"""
        print("🔍 INICIANDO AUDITORÍA FINAL EXHAUSTIVA...")
        
        # Auditar handlers críticos
        print("\n📁 AUDITANDO HANDLERS CRÍTICOS...")
        critical_handlers = [
            "services/**/handlers/login.py",
            "services/**/handlers/register.py",
            "services/**/handlers/create_subscription.py",
            "services/**/handlers/stripe_webhook.py"
        ]
        
        for pattern in critical_handlers:
            for file_path in glob.glob(pattern, recursive=True):
                self.total_files += 1
                result = self.audit_file(file_path)
                
                if result['is_perfect']:
                    self.perfect_files += 1
                    print(f"✅ {os.path.basename(file_path)} - PERFECTO")
                else:
                    print(f"❌ {os.path.basename(file_path)} - {len(result['issues'])} problemas")
                    for issue in result['issues']:
                        print(f"   {issue}")
        
        # Auditar todos los handlers
        print("\n📁 AUDITANDO TODOS LOS HANDLERS...")
        for file_path in glob.glob("services/**/handlers/*.py", recursive=True):
            if '__init__' in file_path:
                continue
            if file_path in [f for pattern in critical_handlers for f in glob.glob(pattern, recursive=True)]:
                continue
                
            self.total_files += 1
            result = self.audit_file(file_path)
            
            if result['is_perfect']:
                self.perfect_files += 1
                print(f"✅ {os.path.basename(file_path)}")
            else:
                print(f"❌ {os.path.basename(file_path)} - {len(result['issues'])} problemas")
        
        # Auditar servicios críticos
        print("\n📁 AUDITANDO SERVICIOS CRÍTICOS...")
        for file_path in glob.glob("services/**/services/*.py", recursive=True):
            if '__init__' in file_path:
                continue
                
            self.total_files += 1
            result = self.audit_file(file_path)
            
            if result['is_perfect']:
                self.perfect_files += 1
                print(f"✅ {os.path.basename(file_path)}")
            else:
                print(f"❌ {os.path.basename(file_path)} - {len(result['issues'])} problemas")
        
        # Auditar shared layer
        print("\n📁 AUDITANDO SHARED LAYER...")
        for file_path in glob.glob("shared/**/*.py", recursive=True):
            if '__init__' in file_path:
                continue
                
            self.total_files += 1
            result = self.audit_file(file_path)
            
            if result['is_perfect']:
                self.perfect_files += 1
                print(f"✅ {os.path.basename(file_path)}")
            else:
                print(f"❌ {os.path.basename(file_path)} - {len(result['issues'])} problemas")
        
        # Calcular score final
        score = round((self.perfect_files / self.total_files) * 10, 1) if self.total_files > 0 else 0
        
        print(f"\n📊 REPORTE FINAL EXHAUSTIVO:")
        print(f"Total archivos auditados: {self.total_files}")
        print(f"Archivos perfectos: {self.perfect_files}")
        print(f"Archivos con problemas: {self.total_files - self.perfect_files}")
        
        if score >= 9.5:
            print(f"\n🎉 ARQUITECTURA Y LÓGICA PERFECTAS: {score}/10 ✅")
            print("🚀 DEPLOYMENT-READY AL 100%")
        elif score >= 8.0:
            print(f"\n✅ ARQUITECTURA EXCELENTE: {score}/10")
            print("⚠️ Problemas menores que no afectan funcionalidad")
        else:
            print(f"\n❌ NECESITA CORRECCIONES: {score}/10")
            print("🚨 Problemas críticos encontrados")
        
        return score >= 9.0

def main():
    auditor = ExhaustiveAuditor()
    success = auditor.run_exhaustive_audit()
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
