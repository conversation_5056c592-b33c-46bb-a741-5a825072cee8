#!/usr/bin/env python3
"""
Script to clean duplicate serverless configurations.
Removes serverless/services/* that point to non-existent src/ directory.
"""

import os
import shutil
import sys
from pathlib import Path

def main():
    """Clean duplicate serverless configurations."""
    project_root = Path(__file__).parent.parent
    serverless_services_dir = project_root / "serverless" / "services"
    
    print(f"🔍 Checking for duplicate serverless configs at: {serverless_services_dir}")
    
    if not serverless_services_dir.exists():
        print("✅ No duplicate serverless/services/ directory found - already clean!")
        return
    
    # List of service directories to remove (duplicates)
    services_to_remove = [
        "auth", "payment", "tenant", "user", 
        "admin", "security", "events"
    ]
    
    removed_count = 0
    
    for service in services_to_remove:
        service_path = serverless_services_dir / service
        if service_path.exists():
            try:
                print(f"🗑️  Removing duplicate config: serverless/services/{service}")
                shutil.rmtree(service_path)
                removed_count += 1
                print(f"✅ Removed: serverless/services/{service}")
            except Exception as e:
                print(f"❌ Error removing {service}: {e}")
    
    # Keep only shared and api-gateway if they exist
    remaining_dirs = []
    if serverless_services_dir.exists():
        for item in serverless_services_dir.iterdir():
            if item.is_dir():
                remaining_dirs.append(item.name)
    
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"✅ Removed {removed_count} duplicate service configs")
    if remaining_dirs:
        print(f"📁 Remaining in serverless/services/: {', '.join(remaining_dirs)}")
    else:
        print("📁 serverless/services/ is now clean")
    
    print("\n🎉 DUPLICATE CONFIGURATIONS CLEANED!")
    print("✅ Only services/*/serverless.yml configs remain (correct structure)")

if __name__ == "__main__":
    main()
