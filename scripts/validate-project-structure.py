#!/usr/bin/env python3
# scripts/validate-project-structure.py
# Script para validar la estructura del proyecto y configuraciones

"""
Project structure validation script.
Validates that all required files and configurations are in place.
"""

import os
import sys
import json
import yaml
from pathlib import Path
from typing import List, Dict, Any

# Colors for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_success(message: str):
    print(f"{Colors.GREEN}✅ {message}{Colors.ENDC}")

def print_error(message: str):
    print(f"{Colors.RED}❌ {message}{Colors.ENDC}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.ENDC}")

def print_info(message: str):
    print(f"{Colors.BLUE}ℹ️  {message}{Colors.ENDC}")

def print_header(message: str):
    print(f"\n{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.ENDC}")
    print(f"{Colors.BOLD}{Colors.BLUE}{message}{Colors.ENDC}")
    print(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.ENDC}")

class ProjectValidator:
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.errors = []
        self.warnings = []
        self.success_count = 0
        
    def validate_directory_structure(self):
        """Validate basic directory structure."""
        print_header("VALIDATING DIRECTORY STRUCTURE")
        
        required_dirs = [
            "services",
            "services/auth",
            "services/auth/src",
            "services/auth/src/handlers",
            "services/auth/src/models",
            "services/payment",
            "services/payment/src",
            "services/payment/src/handlers",
            "services/payment/src/models",
            "services/tenant",
            "services/tenant/src",
            "services/tenant/src/handlers",
            "services/user",
            "services/user/src",
            "services/user/src/handlers",
            "shared",
            "shared/python",
            "shared/python/shared",
            "serverless",
            "serverless/services",
            "serverless/services/auth",
            "serverless/services/payment",
            "serverless/services/tenant",
            "serverless/services/user",
            "serverless/resources",
            "serverless/shared",
            "tests",
            "tests/unit",
            "tests/integration",
            "project-context",
            "scripts"
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                print_success(f"Directory exists: {dir_path}")
                self.success_count += 1
            else:
                print_error(f"Missing directory: {dir_path}")
                self.errors.append(f"Missing directory: {dir_path}")
    
    def validate_serverless_configs(self):
        """Validate serverless configurations."""
        print_header("VALIDATING SERVERLESS CONFIGURATIONS")
        
        services = ["auth", "payment", "tenant", "user"]
        
        for service in services:
            config_path = self.project_root / f"serverless/services/{service}/serverless.yml"
            
            if config_path.exists():
                print_success(f"Serverless config exists: {service}")
                self.success_count += 1
                
                # Validate YAML syntax
                try:
                    with open(config_path, 'r') as f:
                        config = yaml.safe_load(f)
                    
                    # Check required fields
                    required_fields = ["service", "provider", "functions"]
                    for field in required_fields:
                        if field in config:
                            print_success(f"  {service}: Has {field} configuration")
                        else:
                            print_error(f"  {service}: Missing {field} configuration")
                            self.errors.append(f"{service} missing {field}")
                    
                    # Check Python runtime
                    if config.get("provider", {}).get("runtime") == "python3.11":
                        print_success(f"  {service}: Correct Python runtime")
                    else:
                        print_warning(f"  {service}: Runtime is not python3.11")
                        self.warnings.append(f"{service} runtime not python3.11")
                        
                except yaml.YAMLError as e:
                    print_error(f"  {service}: Invalid YAML syntax - {e}")
                    self.errors.append(f"{service} invalid YAML")
                    
            else:
                print_error(f"Missing serverless config: {service}")
                self.errors.append(f"Missing serverless config: {service}")
    
    def validate_handlers(self):
        """Validate that all handlers exist."""
        print_header("VALIDATING HANDLERS")
        
        # Auth handlers
        auth_handlers = [
            "register.py", "login.py", "refresh.py", "forgot_password.py",
            "reset_password.py", "verify_email.py", "authorizer.py"
        ]
        
        for handler in auth_handlers:
            handler_path = self.project_root / f"src/auth/handlers/{handler}"
            if handler_path.exists():
                print_success(f"Auth handler exists: {handler}")
                self.success_count += 1
            else:
                print_error(f"Missing auth handler: {handler}")
                self.errors.append(f"Missing auth handler: {handler}")
        
        # Payment handlers
        payment_handlers = [
            "list_plans.py", "create_subscription.py", "get_subscription.py",
            "cancel_subscription.py", "stripe_webhook.py"
        ]
        
        for handler in payment_handlers:
            handler_path = self.project_root / f"src/payment/handlers/{handler}"
            if handler_path.exists():
                print_success(f"Payment handler exists: {handler}")
                self.success_count += 1
            else:
                print_error(f"Missing payment handler: {handler}")
                self.errors.append(f"Missing payment handler: {handler}")
        
        # Tenant handlers
        tenant_handlers = [
            "get_profile.py", "update_settings.py", "invite_user.py",
            "accept_invitation.py", "list_users.py"
        ]
        
        for handler in tenant_handlers:
            handler_path = self.project_root / f"src/tenant/handlers/{handler}"
            if handler_path.exists():
                print_success(f"Tenant handler exists: {handler}")
                self.success_count += 1
            else:
                print_error(f"Missing tenant handler: {handler}")
                self.errors.append(f"Missing tenant handler: {handler}")
        
        # User handlers
        user_handlers = [
            "get_profile.py", "update_profile.py", "change_password.py",
            "update_role.py", "deactivate_user.py"
        ]
        
        for handler in user_handlers:
            handler_path = self.project_root / f"src/user/handlers/{handler}"
            if handler_path.exists():
                print_success(f"User handler exists: {handler}")
                self.success_count += 1
            else:
                print_error(f"Missing user handler: {handler}")
                self.errors.append(f"Missing user handler: {handler}")
    
    def validate_models(self):
        """Validate that all models exist."""
        print_header("VALIDATING MODELS")
        
        models = [
            ("auth", ["user.py", "tenant.py"]),
            ("payment", ["customer.py", "subscription.py", "plan.py"])
        ]
        
        for module, model_files in models:
            for model_file in model_files:
                model_path = self.project_root / f"src/{module}/models/{model_file}"
                if model_path.exists():
                    print_success(f"{module.title()} model exists: {model_file}")
                    self.success_count += 1
                else:
                    print_error(f"Missing {module} model: {model_file}")
                    self.errors.append(f"Missing {module} model: {model_file}")
    
    def validate_shared_modules(self):
        """Validate shared modules."""
        print_header("VALIDATING SHARED MODULES")
        
        shared_files = [
            "responses.py", "validators.py", "exceptions.py", "logger.py",
            "database.py", "config.py"
        ]
        
        for shared_file in shared_files:
            file_path = self.project_root / f"src/shared/{shared_file}"
            if file_path.exists():
                print_success(f"Shared module exists: {shared_file}")
                self.success_count += 1
            else:
                print_error(f"Missing shared module: {shared_file}")
                self.errors.append(f"Missing shared module: {shared_file}")
    
    def validate_serverless_resources(self):
        """Validate serverless resources configuration."""
        print_header("VALIDATING SERVERLESS RESOURCES")

        resource_files = [
            "dynamodb.yml", "s3.yml", "iam.yml", "api-gateway.yml",
            "monitoring.yml", "secrets.yml", "vpc.yml"
        ]

        for resource_file in resource_files:
            resource_path = self.project_root / f"serverless/resources/{resource_file}"
            if resource_path.exists():
                print_success(f"Resource file exists: {resource_file}")
                self.success_count += 1

                # Validate YAML syntax
                try:
                    with open(resource_path, 'r') as f:
                        config = yaml.safe_load(f)

                    if 'Resources' in config:
                        print_success(f"  {resource_file}: Has Resources section")
                    else:
                        print_warning(f"  {resource_file}: Missing Resources section")
                        self.warnings.append(f"{resource_file} missing Resources")

                except yaml.YAMLError as e:
                    print_error(f"  {resource_file}: Invalid YAML syntax - {e}")
                    self.errors.append(f"{resource_file} invalid YAML")

            else:
                print_error(f"Missing resource file: {resource_file}")
                self.errors.append(f"Missing resource file: {resource_file}")

    def check_for_duplicated_code(self):
        """Check for duplicated code in serverless services."""
        print_header("CHECKING FOR CODE DUPLICATION")

        services = ["auth", "payment", "tenant", "user"]

        for service in services:
            src_path = self.project_root / f"serverless/services/{service}/src"
            if src_path.exists():
                print_error(f"Found duplicated code in: serverless/services/{service}/src")
                self.errors.append(f"Duplicated code in {service} service")
            else:
                print_success(f"No code duplication in {service} service")
                self.success_count += 1
    
    def generate_report(self):
        """Generate validation report."""
        print_header("VALIDATION REPORT")
        
        total_checks = self.success_count + len(self.errors) + len(self.warnings)
        
        print(f"Total checks performed: {total_checks}")
        print_success(f"Successful checks: {self.success_count}")
        print_warning(f"Warnings: {len(self.warnings)}")
        print_error(f"Errors: {len(self.errors)}")
        
        if self.warnings:
            print(f"\n{Colors.YELLOW}WARNINGS:{Colors.ENDC}")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        if self.errors:
            print(f"\n{Colors.RED}ERRORS:{Colors.ENDC}")
            for error in self.errors:
                print(f"  - {error}")
        
        # Overall status
        if len(self.errors) == 0:
            if len(self.warnings) == 0:
                print_success("\n🎉 PROJECT STRUCTURE IS VALID!")
                return 0
            else:
                print_warning("\n⚠️  PROJECT STRUCTURE IS VALID WITH WARNINGS")
                return 0
        else:
            print_error("\n💥 PROJECT STRUCTURE HAS CRITICAL ISSUES")
            return 1
    
    def run_validation(self):
        """Run all validations."""
        print_info("Starting project structure validation...")

        self.validate_directory_structure()
        self.validate_serverless_configs()
        self.validate_serverless_resources()
        self.validate_handlers()
        self.validate_models()
        self.validate_shared_modules()
        self.check_for_duplicated_code()

        return self.generate_report()

def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    
    print_info(f"Project root: {project_root}")
    
    validator = ProjectValidator(project_root)
    exit_code = validator.run_validation()
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
