#!/usr/bin/env python3
# scripts/project-manager.py
# Master script for project management operations

"""
Master project management script for Agent SCL Platform.
Consolidates common operations into a single, easy-to-use interface.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

class ProjectManager:
    """Master project manager for common operations."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        os.chdir(self.project_root)
    
    def setup_environment(self):
        """Setup development environment."""
        print("🚀 Setting up development environment...")
        
        # Install Python dependencies
        print("📦 Installing Python dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements-dev.txt"], check=True)
        
        # Install Node.js dependencies
        print("📦 Installing Node.js dependencies...")
        subprocess.run(["npm", "install"], check=True)
        
        print("✅ Environment setup complete!")
    
    def run_tests(self, coverage=True, verbose=False):
        """Run test suite with optional coverage."""
        print("🧪 Running test suite...")
        
        cmd = [sys.executable, "-m", "pytest"]
        
        if coverage:
            cmd.extend(["--cov=services", "--cov=shared", "--cov-report=html", "--cov-report=term"])
        
        if verbose:
            cmd.append("-v")
        
        cmd.append("tests/")
        
        result = subprocess.run(cmd)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            if coverage:
                print("📊 Coverage report generated in htmlcov/")
        else:
            print("❌ Some tests failed!")
            return False
        
        return True
    
    def validate_structure(self):
        """Validate project structure."""
        print("🔍 Validating project structure...")
        
        # Check required directories
        required_dirs = [
            "services/auth/src",
            "services/payment/src", 
            "services/tenant/src",
            "services/user/src",
            "shared/python/shared",
            "tests",
            "docs"
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"❌ Missing directories: {missing_dirs}")
            return False
        
        # Check required files
        required_files = [
            "requirements.txt",
            "requirements-dev.txt",
            "package.json",
            "README.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ Missing files: {missing_files}")
            return False
        
        print("✅ Project structure is valid!")
        return True
    
    def lint_code(self):
        """Run code linting."""
        print("🔍 Running code linting...")
        
        # Run flake8
        result = subprocess.run([
            sys.executable, "-m", "flake8", 
            "services/", "shared/", "tests/",
            "--max-line-length=100",
            "--exclude=__pycache__,*.pyc"
        ])
        
        if result.returncode == 0:
            print("✅ Code linting passed!")
            return True
        else:
            print("❌ Code linting failed!")
            return False
    
    def deploy_service(self, service_name, stage="dev"):
        """Deploy a specific service."""
        print(f"🚀 Deploying {service_name} to {stage}...")
        
        service_path = Path(f"services/{service_name}")
        if not service_path.exists():
            print(f"❌ Service {service_name} not found!")
            return False
        
        # Change to service directory
        original_cwd = os.getcwd()
        os.chdir(service_path)
        
        try:
            # Deploy with serverless
            result = subprocess.run([
                "npx", "serverless", "deploy", 
                "--stage", stage,
                "--verbose"
            ])
            
            if result.returncode == 0:
                print(f"✅ {service_name} deployed successfully!")
                return True
            else:
                print(f"❌ Failed to deploy {service_name}!")
                return False
        
        finally:
            os.chdir(original_cwd)
    
    def deploy_all(self, stage="dev"):
        """Deploy all services."""
        print(f"🚀 Deploying all services to {stage}...")
        
        services = ["auth", "payment", "tenant", "user", "admin", "events", "security"]
        
        for service in services:
            if not self.deploy_service(service, stage):
                print(f"❌ Deployment failed at {service}")
                return False
        
        print("✅ All services deployed successfully!")
        return True
    
    def clean_project(self):
        """Clean project artifacts."""
        print("🧹 Cleaning project artifacts...")
        
        # Remove Python cache
        subprocess.run(["find", ".", "-name", "__pycache__", "-type", "d", "-exec", "rm", "-rf", "{}", "+"], 
                      capture_output=True)
        
        # Remove coverage files
        for pattern in [".coverage", "htmlcov", ".pytest_cache"]:
            subprocess.run(["find", ".", "-name", pattern, "-exec", "rm", "-rf", "{}", "+"], 
                          capture_output=True)
        
        print("✅ Project cleaned!")

def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="Agent SCL Project Manager")
    parser.add_argument("command", choices=[
        "setup", "test", "validate", "lint", "deploy", "deploy-all", "clean"
    ], help="Command to execute")
    
    parser.add_argument("--service", help="Service name for deploy command")
    parser.add_argument("--stage", default="dev", help="Deployment stage")
    parser.add_argument("--no-coverage", action="store_true", help="Skip coverage in tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    manager = ProjectManager()
    
    try:
        if args.command == "setup":
            manager.setup_environment()
        
        elif args.command == "test":
            manager.run_tests(coverage=not args.no_coverage, verbose=args.verbose)
        
        elif args.command == "validate":
            manager.validate_structure()
        
        elif args.command == "lint":
            manager.lint_code()
        
        elif args.command == "deploy":
            if not args.service:
                print("❌ --service required for deploy command")
                sys.exit(1)
            manager.deploy_service(args.service, args.stage)
        
        elif args.command == "deploy-all":
            manager.deploy_all(args.stage)
        
        elif args.command == "clean":
            manager.clean_project()
    
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
