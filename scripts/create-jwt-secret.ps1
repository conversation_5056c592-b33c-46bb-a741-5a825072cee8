# Create JWT Secret in AWS Secrets Manager
# This script creates the JWT secret required for authentication

param(
    [string]$Region = "us-east-1",
    [string]$SecretName = "agent-scl/dev/jwt-secret"
)

Write-Host "Creating JWT Secret in AWS Secrets Manager..." -ForegroundColor Yellow

# Generate a secure random JWT secret (256-bit)
$JwtSecret = [System.Convert]::ToBase64String([System.Security.Cryptography.RandomNumberGenerator]::GetBytes(32))

# Create the secret value as JSON
$SecretValue = @{
    "jwt_secret" = $JwtSecret
    "algorithm" = "HS256"
    "issuer" = "agent-scl-platform"
    "audience" = "agent-scl-api"
    "created_at" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
} | ConvertTo-Json

try {
    # Check if secret already exists
    $existingSecret = aws secretsmanager describe-secret --secret-id $SecretName --region $Region 2>$null
    
    if ($existingSecret) {
        Write-Host "Secret already exists. Updating..." -ForegroundColor Yellow
        
        # Update existing secret
        aws secretsmanager update-secret `
            --secret-id $SecretName `
            --secret-string $SecretValue `
            --region $Region
            
        if ($LASTEXITCODE -eq 0) {
            Write-Host "JWT Secret updated successfully!" -ForegroundColor Green
        } else {
            Write-Host "Failed to update JWT Secret" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "Creating new secret..." -ForegroundColor Yellow
        
        # Create new secret
        aws secretsmanager create-secret `
            --name $SecretName `
            --description "JWT secret for agent-scl platform authentication" `
            --secret-string $SecretValue `
            --region $Region
            
        if ($LASTEXITCODE -eq 0) {
            Write-Host "JWT Secret created successfully!" -ForegroundColor Green
        } else {
            Write-Host "Failed to create JWT Secret" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host ""
    Write-Host "Secret Details:" -ForegroundColor Cyan
    Write-Host "  Name: $SecretName" -ForegroundColor White
    Write-Host "  Region: $Region" -ForegroundColor White
    Write-Host "  Algorithm: HS256" -ForegroundColor White
    Write-Host ""
    Write-Host "The JWT secret has been configured and is ready for use." -ForegroundColor Green
    
} catch {
    Write-Host "Error creating JWT Secret: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
