#!/usr/bin/env python3
# scripts/validate-infrastructure.py
# Infrastructure validation script

"""
Infrastructure validation script for:
- Configuration validation
- Health check validation
- Deployment readiness
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, List, Tuple


class InfrastructureValidator:
    """Validates infrastructure configuration and readiness."""
    
    def __init__(self):
        """Initialize validator."""
        self.project_root = Path(__file__).parent.parent
        self.errors = []
        self.warnings = []
        
    def log_error(self, message: str):
        """Log an error."""
        self.errors.append(message)
        print(f"❌ ERROR: {message}")
    
    def log_warning(self, message: str):
        """Log a warning."""
        self.warnings.append(message)
        print(f"⚠️  WARNING: {message}")
    
    def log_success(self, message: str):
        """Log a success."""
        print(f"✅ {message}")
    
    def validate_shared_configuration(self) -> bool:
        """Validate shared configuration files."""
        print("🔍 Validating shared configuration...")
        
        # Check shared variables
        variables_path = self.project_root / "serverless" / "shared" / "variables.yml"
        if not variables_path.exists():
            self.log_error("Shared variables file not found")
            return False
        
        try:
            with open(variables_path, 'r', encoding='utf-8') as f:
                variables = yaml.safe_load(f)
            
            # Validate required sections
            required_sections = ['projectName', 'stages']
            for section in required_sections:
                if section not in variables:
                    self.log_error(f"Missing required section in variables.yml: {section}")
                else:
                    self.log_success(f"Found required section: {section}")

            # Check for lambda configuration in stages
            if 'stages' in variables and 'dev' in variables['stages']:
                dev_stage = variables['stages']['dev']
                if 'lambda' in dev_stage:
                    self.log_success("Found lambda configuration in dev stage")
                else:
                    self.log_warning("Lambda configuration not found in dev stage")
            else:
                self.log_warning("Dev stage configuration not found")
            
        except yaml.YAMLError as e:
            self.log_error(f"Invalid YAML in variables.yml: {e}")
            return False
        except Exception as e:
            self.log_error(f"Error reading variables.yml: {e}")
            return False
        
        # Check service template
        template_path = self.project_root / "serverless" / "templates" / "service-template.yml"
        if not template_path.exists():
            self.log_error("Service template not found")
            return False
        else:
            self.log_success("Service template found")
        
        return len(self.errors) == 0
    
    def validate_service_configurations(self) -> bool:
        """Validate individual service configurations."""
        print("🔍 Validating service configurations...")
        
        services_dir = self.project_root / "services"
        if not services_dir.exists():
            self.log_error("Services directory not found")
            return False
        
        services = ["auth", "payment", "tenant", "user"]
        valid_services = 0
        
        for service in services:
            service_config = services_dir / service / "serverless.yml"
            if service_config.exists():
                try:
                    with open(service_config, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Basic validation - check for required sections in text
                    if 'service:' in content and 'provider:' in content:
                        self.log_success(f"Service {service} configuration valid")
                        valid_services += 1
                    else:
                        self.log_error(f"Service {service} missing required sections")

                except Exception as e:
                    self.log_error(f"Error reading {service} configuration: {e}")
            else:
                self.log_warning(f"Service {service} configuration not found")
        
        if valid_services == 0:
            self.log_error("No valid service configurations found")
            return False
        
        return True
    
    def validate_health_check_implementation(self) -> bool:
        """Validate health check implementation."""
        print("🔍 Validating health check implementation...")
        
        # Check shared health module
        health_module = self.project_root / "shared" / "python" / "shared" / "health.py"
        if not health_module.exists():
            self.log_error("Health check module not found")
            return False
        else:
            self.log_success("Health check module found")
        
        # Check auth service health handler
        auth_health = self.project_root / "services" / "auth" / "src" / "handlers" / "health.py"
        if not auth_health.exists():
            self.log_error("Auth service health handler not found")
            return False
        else:
            self.log_success("Auth service health handler found")
        
        return True
    
    def validate_python_requirements(self) -> bool:
        """Validate Python requirements."""
        print("🔍 Validating Python requirements...")
        
        # Check shared requirements
        shared_req = self.project_root / "shared" / "requirements.txt"
        if shared_req.exists():
            self.log_success("Shared requirements.txt found")
        else:
            self.log_warning("Shared requirements.txt not found")
        
        # Check service requirements
        services_with_req = 0
        services_dir = self.project_root / "services"
        
        if services_dir.exists():
            for service_dir in services_dir.iterdir():
                if service_dir.is_dir():
                    req_file = service_dir / "requirements.txt"
                    if req_file.exists():
                        services_with_req += 1
                        self.log_success(f"Requirements found for {service_dir.name}")
        
        if services_with_req == 0:
            self.log_warning("No service requirements.txt files found")
        
        return True
    
    def validate_environment_variables(self) -> bool:
        """Validate environment variables setup."""
        print("🔍 Validating environment variables...")
        
        # Check for JWT secret (required for testing)
        jwt_secret = os.getenv('JWT_SECRET')
        if jwt_secret:
            if len(jwt_secret) >= 32:
                self.log_success("JWT_SECRET environment variable is properly configured")
            else:
                self.log_warning("JWT_SECRET environment variable is too short")
        else:
            self.log_warning("JWT_SECRET environment variable not set (required for testing)")
        
        # Check AWS credentials
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        
        if aws_access_key and aws_secret_key:
            self.log_success("AWS credentials found in environment")
        else:
            self.log_warning("AWS credentials not found in environment variables")
        
        return True
    
    def test_health_check_functionality(self) -> bool:
        """Test health check functionality."""
        print("🔍 Testing health check functionality...")
        
        try:
            # Import and test health check
            sys.path.insert(0, str(self.project_root / "shared" / "python"))
            from shared.health import HealthCheck, create_health_handler
            
            # Test basic health check
            health_checker = HealthCheck("test-service")
            health_data = health_checker.get_basic_health()
            
            if health_data and "service" in health_data:
                self.log_success("Health check functionality working")
                return True
            else:
                self.log_error("Health check returned invalid data")
                return False
                
        except Exception as e:
            self.log_error(f"Health check functionality test failed: {e}")
            return False
    
    def run_full_validation(self) -> bool:
        """Run full infrastructure validation."""
        print("🚀 Starting infrastructure validation...\n")
        
        validations = [
            ("Shared Configuration", self.validate_shared_configuration),
            ("Service Configurations", self.validate_service_configurations),
            ("Health Check Implementation", self.validate_health_check_implementation),
            ("Python Requirements", self.validate_python_requirements),
            ("Environment Variables", self.validate_environment_variables),
            ("Health Check Functionality", self.test_health_check_functionality),
        ]
        
        passed = 0
        total = len(validations)
        
        for name, validation_func in validations:
            print(f"\n--- {name} ---")
            if validation_func():
                passed += 1
            print()
        
        # Summary
        print("=" * 50)
        print(f"📊 VALIDATION SUMMARY:")
        print(f"   ✅ Passed: {passed}/{total}")
        print(f"   ❌ Errors: {len(self.errors)}")
        print(f"   ⚠️  Warnings: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ ERRORS:")
            for error in self.errors:
                print(f"   - {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS:")
            for warning in self.warnings:
                print(f"   - {warning}")
        
        success = len(self.errors) == 0
        
        if success:
            print(f"\n🎉 Infrastructure validation PASSED!")
        else:
            print(f"\n❌ Infrastructure validation FAILED!")
        
        return success


def main():
    """Main validation function."""
    validator = InfrastructureValidator()
    
    try:
        success = validator.run_full_validation()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during validation: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
