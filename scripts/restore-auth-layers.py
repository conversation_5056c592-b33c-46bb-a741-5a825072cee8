#!/usr/bin/env python3
"""
Script to restore shared layer references in Auth Service only.
"""

import re
from pathlib import Path


def restore_auth_layers():
    """Restore shared layer references in Auth Service."""
    auth_serverless = Path("services/auth/serverless.yml")
    
    if not auth_serverless.exists():
        print("❌ Auth serverless.yml not found")
        return
    
    print("🔧 Restoring shared layer references in Auth Service...")
    
    with open(auth_serverless, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to uncomment layers
    pattern = r'^(\s+)# layers:\s*\n(\s+)#\s+- \$\{self:custom\.sharedLayerArn\}\s*# Temporarily disabled.*$'
    replacement = r'\1layers:\n\2  - ${self:custom.sharedLayerArn}'
    
    new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    if new_content != content:
        with open(auth_serverless, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print("✅ Auth Service shared layer references restored")
    else:
        print("ℹ️ No changes needed")


if __name__ == "__main__":
    restore_auth_layers()
