#!/bin/bash
# scripts/deploy-modular.sh
# Coordinated deployment script for modular architecture

set -e

# Configuration
STAGE=${1:-dev}
REGION=${2:-us-east-1}
PROJECT_NAME="agent-scl"

echo "🚀 Starting modular deployment for stage: $STAGE, region: $REGION"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service exists
check_service_exists() {
    local service_path=$1
    if [ ! -d "$service_path" ]; then
        print_error "Service directory not found: $service_path"
        return 1
    fi
    
    if [ ! -f "$service_path/serverless.yml" ]; then
        print_error "serverless.yml not found in: $service_path"
        return 1
    fi
    
    return 0
}

# Function to deploy a service
deploy_service() {
    local service_name=$1
    local service_path=$2
    
    print_status "Deploying $service_name..."
    
    if ! check_service_exists "$service_path"; then
        return 1
    fi
    
    cd "$service_path"
    
    # Install dependencies if requirements.txt exists
    if [ -f "requirements.txt" ]; then
        print_status "Installing dependencies for $service_name..."
        pip install -r requirements.txt --quiet
    fi
    
    # Deploy with serverless
    if serverless deploy --stage "$STAGE" --region "$REGION" --verbose; then
        print_success "$service_name deployed successfully"
        cd - > /dev/null
        return 0
    else
        print_error "Failed to deploy $service_name"
        cd - > /dev/null
        return 1
    fi
}

# Function to check deployment status
check_deployment() {
    local service_name=$1
    local service_path=$2
    
    print_status "Checking deployment status for $service_name..."
    
    cd "$service_path"
    
    if serverless info --stage "$STAGE" --region "$REGION" > /dev/null 2>&1; then
        print_success "$service_name is deployed and accessible"
        cd - > /dev/null
        return 0
    else
        print_warning "$service_name deployment status unclear"
        cd - > /dev/null
        return 1
    fi
}

# Main deployment sequence
main() {
    print_status "Starting coordinated deployment sequence..."
    
    # Step 1: Deploy shared layer first
    print_status "=== STEP 1: Deploying Shared Layer ==="
    if deploy_service "Shared Layer" "shared"; then
        print_success "Shared layer deployed successfully"
    else
        print_error "Failed to deploy shared layer. Aborting deployment."
        exit 1
    fi
    
    # Wait a moment for the layer to be available
    print_status "Waiting for shared layer to be available..."
    sleep 10
    
    # Step 2: Deploy core infrastructure (if exists)
    if [ -d "infrastructure/core" ]; then
        print_status "=== STEP 2: Deploying Core Infrastructure ==="
        if deploy_service "Core Infrastructure" "infrastructure/core"; then
            print_success "Core infrastructure deployed successfully"
        else
            print_warning "Core infrastructure deployment failed, continuing..."
        fi
        
        # Wait for infrastructure to be ready
        print_status "Waiting for infrastructure to be ready..."
        sleep 15
    fi
    
    # Step 3: Deploy services
    print_status "=== STEP 3: Deploying Services ==="
    
    # Deploy auth service first (other services depend on it)
    if deploy_service "Auth Service" "services/auth"; then
        print_success "Auth service deployed successfully"
    else
        print_error "Failed to deploy auth service. This may affect other services."
    fi
    
    # Wait for auth service to be ready
    print_status "Waiting for auth service to be ready..."
    sleep 10
    
    # Deploy infrastructure first
    if [ -d "infrastructure" ] && [ -f "infrastructure/serverless.yml" ]; then
        if deploy_service "Infrastructure" "infrastructure"; then
            print_success "Infrastructure deployed successfully"
        else
            print_error "Infrastructure deployment failed"
            exit 1
        fi
        sleep 10
    fi

    # Deploy other services
    services=("tenant" "payment" "admin" "events" "security" "user")

    for service in "${services[@]}"; do
        service_path="services/$service"
        if [ -d "$service_path" ] && [ -f "$service_path/serverless.yml" ]; then
            if deploy_service "$service Service" "$service_path"; then
                print_success "$service service deployed successfully"
            else
                print_warning "$service service deployment failed, continuing..."
            fi

            # Small delay between service deployments
            sleep 5
        else
            print_warning "Service $service not found or not ready for deployment"
        fi
    done
    
    # Step 4: Verify deployments
    print_status "=== STEP 4: Verifying Deployments ==="
    
    # Check shared layer
    check_deployment "Shared Layer" "shared"
    
    # Check auth service
    check_deployment "Auth Service" "services/auth"
    
    # Check other services
    for service in "${services[@]}"; do
        service_path="services/$service"
        if [ -d "$service_path" ] && [ -f "$service_path/serverless.yml" ]; then
            check_deployment "$service Service" "$service_path"
        fi
    done
    
    print_success "=== DEPLOYMENT COMPLETED ==="
    print_status "All services have been deployed to stage: $STAGE"
    print_status "Region: $REGION"
    print_status "Project: $PROJECT_NAME"
    
    # Display useful information
    echo ""
    print_status "=== DEPLOYMENT SUMMARY ==="
    echo "✅ Shared Layer: deployed"
    echo "✅ Auth Service: deployed"
    
    for service in "${services[@]}"; do
        if [ -d "services/$service" ] && [ -f "services/$service/serverless.yml" ]; then
            echo "✅ $service Service: deployed"
        else
            echo "⏭️  $service Service: skipped (not ready)"
        fi
    done
    
    echo ""
    print_status "Next steps:"
    echo "1. Test the auth endpoints"
    echo "2. Verify JWT authorizer is working"
    echo "3. Check CloudWatch logs for any issues"
    echo "4. Run integration tests"
    
    print_success "Modular deployment completed successfully! 🎉"
}

# Check if serverless is installed
if ! command -v serverless &> /dev/null; then
    print_error "Serverless Framework is not installed. Please install it first:"
    echo "npm install -g serverless"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "services" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Run main deployment
main

exit 0
