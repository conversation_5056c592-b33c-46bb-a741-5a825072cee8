#!/bin/bash
# scripts/destroy.sh
# Script para eliminar recursos de Serverless Framework
# Reemplaza terraform destroy

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Default values
STAGE="dev"
REGION="us-east-1"
SERVICES=""
INFRASTRUCTURE_ONLY=false
SERVICES_ONLY=false
FORCE=false
VERBOSE=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --stage STAGE          Deployment stage (dev, staging, prod) [default: dev]"
    echo "  -r, --region REGION        AWS region [default: us-east-1]"
    echo "  --services SERVICES        Comma-separated list of services to destroy (auth,payment,tenant,user)"
    echo "  --infrastructure-only      Destroy only infrastructure stack"
    echo "  --services-only           Destroy only services (keep infrastructure)"
    echo "  --force                   Skip confirmation prompts"
    echo "  -v, --verbose             Verbose output"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --stage dev                           # Destroy everything in dev"
    echo "  $0 --stage prod --services-only          # Destroy only services in prod"
    echo "  $0 --stage staging --services auth,user  # Destroy only auth and user services in staging"
    echo ""
    echo "WARNING: This will permanently delete AWS resources!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--stage)
            STAGE="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        --services)
            SERVICES="$2"
            shift 2
            ;;
        --infrastructure-only)
            INFRASTRUCTURE_ONLY=true
            shift
            ;;
        --services-only)
            SERVICES_ONLY=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate stage
if [[ ! "$STAGE" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid stage: $STAGE. Must be one of: dev, staging, prod"
    exit 1
fi

# Validate mutually exclusive options
if [[ "$INFRASTRUCTURE_ONLY" == true && "$SERVICES_ONLY" == true ]]; then
    print_error "Cannot use --infrastructure-only and --services-only together"
    exit 1
fi

# Set verbose mode
if [[ "$VERBOSE" == true ]]; then
    set -x
fi

print_header "SERVERLESS DESTROY - STAGE: $STAGE"

# Warning for production
if [[ "$STAGE" == "prod" && "$FORCE" != true ]]; then
    print_warning "You are about to destroy PRODUCTION resources!"
    print_warning "This action cannot be undone!"
    echo ""
    read -p "Type 'DESTROY PRODUCTION' to confirm: " confirmation
    
    if [[ "$confirmation" != "DESTROY PRODUCTION" ]]; then
        print_info "Destruction cancelled"
        exit 0
    fi
fi

# Confirmation for other stages
if [[ "$FORCE" != true ]]; then
    print_warning "You are about to destroy resources in stage: $STAGE"
    print_warning "This action cannot be undone!"
    echo ""
    read -p "Are you sure? (y/N): " confirmation
    
    if [[ ! "$confirmation" =~ ^[Yy]$ ]]; then
        print_info "Destruction cancelled"
        exit 0
    fi
fi

# Check prerequisites
print_info "Checking prerequisites..."

# Check if serverless is installed
if ! command -v serverless &> /dev/null; then
    print_error "Serverless Framework is not installed"
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured or credentials are invalid"
    exit 1
fi

print_success "Prerequisites check passed"

# Function to destroy a service
destroy_service() {
    local service=$1
    local service_path="serverless/services/$service"
    
    print_info "Destroying $service service..."
    
    if [[ ! -d "$service_path" ]]; then
        print_warning "Service directory not found: $service_path. Skipping..."
        return 0
    fi
    
    # Change to service directory and destroy
    cd "$service_path"
    
    # Check if service is deployed
    if ! serverless info --stage "$STAGE" --region "$REGION" &> /dev/null; then
        print_warning "$service service is not deployed. Skipping..."
        cd - > /dev/null
        return 0
    fi
    
    serverless remove \
        --stage "$STAGE" \
        --region "$REGION" \
        --verbose
    
    local destroy_result=$?
    
    # Return to project root
    cd - > /dev/null
    
    if [[ $destroy_result -eq 0 ]]; then
        print_success "$service service destroyed successfully"
    else
        print_error "$service service destruction failed"
        return 1
    fi
}

# Function to destroy services
destroy_services() {
    print_header "DESTROYING SERVICES"
    
    local services_to_destroy
    
    if [[ -n "$SERVICES" ]]; then
        # Destroy specific services
        IFS=',' read -ra services_to_destroy <<< "$SERVICES"
    else
        # Destroy all services (in reverse order to handle dependencies)
        services_to_destroy=("user" "tenant" "payment" "auth")
    fi
    
    local failed_services=()
    
    for service in "${services_to_destroy[@]}"; do
        service=$(echo "$service" | xargs)  # Trim whitespace
        
        if [[ ! "$service" =~ ^(auth|payment|tenant|user)$ ]]; then
            print_warning "Unknown service: $service. Skipping..."
            continue
        fi
        
        if ! destroy_service "$service"; then
            failed_services+=("$service")
        fi
    done
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        print_error "Failed to destroy services: ${failed_services[*]}"
        exit 1
    fi
    
    print_success "All services destroyed successfully"
}

# Function to destroy infrastructure
destroy_infrastructure() {
    print_header "DESTROYING INFRASTRUCTURE"
    
    print_info "Destroying infrastructure stack..."
    
    # Check if infrastructure is deployed
    if ! serverless info --stage "$STAGE" --region "$REGION" &> /dev/null; then
        print_warning "Infrastructure is not deployed. Skipping..."
        return 0
    fi
    
    # Special handling for production
    if [[ "$STAGE" == "prod" ]]; then
        print_warning "Destroying production infrastructure..."
        print_warning "This will delete all data and cannot be undone!"
        
        if [[ "$FORCE" != true ]]; then
            read -p "Type 'DELETE ALL DATA' to confirm: " final_confirmation
            
            if [[ "$final_confirmation" != "DELETE ALL DATA" ]]; then
                print_info "Infrastructure destruction cancelled"
                return 0
            fi
        fi
    fi
    
    # Destroy infrastructure
    serverless remove \
        --stage "$STAGE" \
        --region "$REGION" \
        --verbose
    
    if [[ $? -eq 0 ]]; then
        print_success "Infrastructure destroyed successfully"
    else
        print_error "Infrastructure destruction failed"
        exit 1
    fi
}

# Function to clean up orphaned resources
cleanup_orphaned_resources() {
    print_header "CLEANING UP ORPHANED RESOURCES"
    
    print_info "Checking for orphaned CloudFormation stacks..."
    
    # List stacks that might be orphaned
    local orphaned_stacks=$(aws cloudformation list-stacks \
        --region "$REGION" \
        --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE \
        --query "StackSummaries[?contains(StackName, 'agent-scl-$STAGE')].StackName" \
        --output text 2>/dev/null || true)
    
    if [[ -n "$orphaned_stacks" ]]; then
        print_warning "Found potentially orphaned stacks:"
        echo "$orphaned_stacks"
        
        if [[ "$FORCE" != true ]]; then
            read -p "Delete these stacks? (y/N): " cleanup_confirmation
            
            if [[ ! "$cleanup_confirmation" =~ ^[Yy]$ ]]; then
                print_info "Cleanup skipped"
                return 0
            fi
        fi
        
        # Delete orphaned stacks
        for stack in $orphaned_stacks; do
            print_info "Deleting orphaned stack: $stack"
            aws cloudformation delete-stack \
                --region "$REGION" \
                --stack-name "$stack" || true
        done
    else
        print_success "No orphaned resources found"
    fi
}

# Main destruction logic
main() {
    local start_time=$(date +%s)
    
    # Destroy services first (if not infrastructure-only)
    if [[ "$INFRASTRUCTURE_ONLY" != true ]]; then
        destroy_services
    fi
    
    # Destroy infrastructure (if not services-only)
    if [[ "$SERVICES_ONLY" != true ]]; then
        destroy_infrastructure
    fi
    
    # Clean up orphaned resources
    cleanup_orphaned_resources
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_header "DESTRUCTION COMPLETED"
    print_success "Total destruction time: ${duration} seconds"
    print_success "All specified resources have been destroyed"
}

# Run main function
main
