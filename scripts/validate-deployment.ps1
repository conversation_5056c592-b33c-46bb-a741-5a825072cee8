# scripts/validate-deployment.ps1
# Validación post-deployment para FASE 1 - Corrección de Configuración
# Verifica que todos los servicios estén funcionando correctamente

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# Configuración
$ErrorActionPreference = "Stop"

# Colores para output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Función para hacer request HTTP
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null,
        [int]$ExpectedStatus = 200
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq $ExpectedStatus) {
            return @{
                Success = $true
                StatusCode = $response.StatusCode
                Content = $response.Content
            }
        } else {
            return @{
                Success = $false
                StatusCode = $response.StatusCode
                Error = "Unexpected status code: $($response.StatusCode)"
            }
        }
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

# Función para obtener API Gateway URL
function Get-ApiGatewayUrl {
    param([string]$Stage)
    
    try {
        # Obtener información del stack de infraestructura
        $stackName = "agent-scl-infrastructure-$Stage"
        $outputs = aws cloudformation describe-stacks --stack-name $stackName --query "Stacks[0].Outputs" | ConvertFrom-Json
        
        $apiGatewayOutput = $outputs | Where-Object { $_.OutputKey -eq "ApiGatewayUrl" }
        if ($apiGatewayOutput) {
            return $apiGatewayOutput.OutputValue
        }
        
        # Fallback: construir URL manualmente
        $region = aws configure get region
        $accountId = (aws sts get-caller-identity --query Account --output text)
        return "https://api-$Stage.execute-api.$region.amazonaws.com/$Stage"
    }
    catch {
        Write-Warning "Could not determine API Gateway URL automatically"
        return $null
    }
}

# Función para validar shared layer
function Test-SharedLayer {
    param([string]$Stage)
    
    Write-Info "🔍 Validating Shared Layer..."
    
    try {
        $stackName = "agent-scl-shared-layer-$Stage"
        $outputs = aws cloudformation describe-stacks --stack-name $stackName --query "Stacks[0].Outputs" | ConvertFrom-Json
        
        $layerOutput = $outputs | Where-Object { $_.OutputKey -like "*SharedLayer*" }
        if ($layerOutput) {
            Write-Success "Shared Layer deployed and exported"
            return $true
        } else {
            Write-Error "Shared Layer outputs not found"
            return $false
        }
    }
    catch {
        Write-Error "Shared Layer validation failed: $($_.Exception.Message)"
        return $false
    }
}

# Función para validar JWT Authorizer
function Test-JwtAuthorizer {
    param([string]$Stage)
    
    Write-Info "🔐 Validating JWT Authorizer..."
    
    try {
        $stackName = "agent-scl-auth-$Stage"
        $outputs = aws cloudformation describe-stacks --stack-name $stackName --query "Stacks[0].Outputs" | ConvertFrom-Json
        
        $authorizerOutput = $outputs | Where-Object { $_.OutputKey -like "*JwtAuthorizer*" }
        if ($authorizerOutput) {
            Write-Success "JWT Authorizer deployed and exported"
            return $true
        } else {
            Write-Error "JWT Authorizer outputs not found"
            return $false
        }
    }
    catch {
        Write-Error "JWT Authorizer validation failed: $($_.Exception.Message)"
        return $false
    }
}

# Función para validar endpoints públicos
function Test-PublicEndpoints {
    param([string]$BaseUrl)
    
    Write-Info "🌐 Testing public endpoints..."
    
    $publicEndpoints = @(
        @{Path="/health"; Service="Infrastructure"},
        @{Path="/auth/health"; Service="Auth Service"},
        @{Path="/setup/health"; Service="Setup Service"}
    )
    
    $results = @{}
    
    foreach ($endpoint in $publicEndpoints) {
        $url = "$BaseUrl$($endpoint.Path)"
        Write-Info "Testing: $url"
        
        $result = Test-Endpoint -Url $url -ExpectedStatus 200
        
        if ($result.Success) {
            Write-Success "$($endpoint.Service) health check passed"
            $results[$endpoint.Service] = "✅ Healthy"
        } else {
            Write-Warning "$($endpoint.Service) health check failed: $($result.Error)"
            $results[$endpoint.Service] = "⚠️ Unhealthy"
        }
    }
    
    return $results
}

# Función para validar endpoints protegidos (sin token - debe fallar)
function Test-ProtectedEndpoints {
    param([string]$BaseUrl)
    
    Write-Info "🔒 Testing protected endpoints (should return 401)..."
    
    $protectedEndpoints = @(
        @{Path="/user/profile"; Service="User Service"},
        @{Path="/tenant/profile"; Service="Tenant Service"},
        @{Path="/payment/plans"; Service="Payment Service"},
        @{Path="/admin/analytics"; Service="Admin Service"}
    )
    
    $results = @{}
    
    foreach ($endpoint in $protectedEndpoints) {
        $url = "$BaseUrl$($endpoint.Path)"
        Write-Info "Testing: $url (expecting 401)"
        
        $result = Test-Endpoint -Url $url -ExpectedStatus 401
        
        if ($result.Success) {
            Write-Success "$($endpoint.Service) properly protected (401 returned)"
            $results[$endpoint.Service] = "✅ Protected"
        } else {
            Write-Warning "$($endpoint.Service) authorization issue: $($result.Error)"
            $results[$endpoint.Service] = "⚠️ Auth Issue"
        }
    }
    
    return $results
}

# Función para validar servicios Lambda
function Test-LambdaFunctions {
    param([string]$Stage)
    
    Write-Info "⚡ Validating Lambda functions..."
    
    try {
        # Obtener todas las funciones del stage
        $functions = aws lambda list-functions --query "Functions[?contains(FunctionName, '$Stage')]" | ConvertFrom-Json
        
        $totalFunctions = $functions.Count
        $healthyFunctions = 0
        
        Write-Info "Found $totalFunctions Lambda functions for stage $Stage"
        
        foreach ($function in $functions) {
            $functionName = $function.FunctionName
            
            try {
                # Verificar configuración de la función
                $config = aws lambda get-function-configuration --function-name $functionName | ConvertFrom-Json
                
                if ($config.State -eq "Active") {
                    $healthyFunctions++
                    if ($Verbose) {
                        Write-Success "✅ $functionName - Active"
                    }
                } else {
                    Write-Warning "⚠️ $functionName - State: $($config.State)"
                }
            }
            catch {
                Write-Warning "⚠️ $functionName - Error checking status"
            }
        }
        
        Write-Info "Lambda Functions Status: $healthyFunctions/$totalFunctions active"
        
        if ($healthyFunctions -eq $totalFunctions) {
            Write-Success "All Lambda functions are active"
            return $true
        } else {
            Write-Warning "Some Lambda functions may have issues"
            return $false
        }
    }
    catch {
        Write-Error "Lambda validation failed: $($_.Exception.Message)"
        return $false
    }
}

# Función principal de validación
function Start-ValidationTests {
    param([string]$Stage)
    
    Write-Info "🔍 FASE 1: VALIDACIÓN POST-DEPLOYMENT"
    Write-Info "====================================="
    
    $validationResults = @{}
    $totalTests = 0
    $passedTests = 0
    
    # 1. Validar Shared Layer
    $totalTests++
    if (Test-SharedLayer $Stage) {
        $validationResults["SharedLayer"] = "✅ Valid"
        $passedTests++
    } else {
        $validationResults["SharedLayer"] = "❌ Invalid"
    }
    
    # 2. Validar JWT Authorizer
    $totalTests++
    if (Test-JwtAuthorizer $Stage) {
        $validationResults["JwtAuthorizer"] = "✅ Valid"
        $passedTests++
    } else {
        $validationResults["JwtAuthorizer"] = "❌ Invalid"
    }
    
    # 3. Validar Lambda Functions
    $totalTests++
    if (Test-LambdaFunctions $Stage) {
        $validationResults["LambdaFunctions"] = "✅ Valid"
        $passedTests++
    } else {
        $validationResults["LambdaFunctions"] = "⚠️ Issues"
    }
    
    # 4. Obtener API Gateway URL y validar endpoints
    $apiUrl = Get-ApiGatewayUrl $Stage
    if ($apiUrl) {
        Write-Success "API Gateway URL: $apiUrl"
        
        # Validar endpoints públicos
        $totalTests++
        $publicResults = Test-PublicEndpoints $apiUrl
        if ($publicResults.Values -contains "✅ Healthy") {
            $validationResults["PublicEndpoints"] = "✅ Valid"
            $passedTests++
        } else {
            $validationResults["PublicEndpoints"] = "⚠️ Issues"
        }
        
        # Validar endpoints protegidos
        $totalTests++
        $protectedResults = Test-ProtectedEndpoints $apiUrl
        if ($protectedResults.Values -contains "✅ Protected") {
            $validationResults["ProtectedEndpoints"] = "✅ Valid"
            $passedTests++
        } else {
            $validationResults["ProtectedEndpoints"] = "⚠️ Issues"
        }
    } else {
        Write-Warning "Could not determine API Gateway URL. Skipping endpoint tests."
        $validationResults["ApiGateway"] = "⚠️ URL Unknown"
    }
    
    # Resumen final
    Write-Info ""
    Write-Info "📊 VALIDATION SUMMARY"
    Write-Info "===================="
    Write-Info "Total Tests: $totalTests"
    Write-Success "Passed: $passedTests"
    Write-Error "Failed: $($totalTests - $passedTests)"
    Write-Info "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 2))%"
    
    Write-Info ""
    Write-Info "📋 Detailed Results:"
    foreach ($result in $validationResults.GetEnumerator()) {
        Write-Host "  $($result.Key): $($result.Value)"
    }
    
    if ($passedTests -eq $totalTests) {
        Write-Success "🎉 All validation tests passed!"
        return $true
    } elseif ($passedTests -ge ($totalTests * 0.8)) {
        Write-Warning "⚠️ Most validation tests passed. Check failed tests."
        return $true
    } else {
        Write-Error "💥 Validation failed. Too many tests failed."
        return $false
    }
}

# Ejecución principal
try {
    $validationSuccess = Start-ValidationTests $Stage
    
    if ($validationSuccess) {
        Write-Success "🎯 Deployment validation completed successfully!"
        Write-Info "✅ FASE 1: CORRECCIÓN DE CONFIGURACIÓN - COMPLETADA"
        Write-Info ""
        Write-Info "Next Phase: FASE 2 - Implementación de Agentes"
        exit 0
    } else {
        Write-Error "💥 Deployment validation failed!"
        Write-Info "❌ FASE 1: CORRECCIÓN DE CONFIGURACIÓN - REQUIERE ATENCIÓN"
        exit 1
    }
}
catch {
    Write-Error "Unexpected error during validation: $($_.Exception.Message)"
    exit 1
}
