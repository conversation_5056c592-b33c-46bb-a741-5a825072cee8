#!/usr/bin/env python3
# scripts/deploy-with-validation.py
# Enhanced deployment script with validation and rollback

"""
Enhanced deployment script with:
- Pre-deployment validation
- Health checks
- Rollback capability
- Deployment monitoring
"""

import os
import sys
import json
import time
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class DeploymentManager:
    """Manages deployment with validation and rollback."""
    
    def __init__(self, stage: str = "dev", region: str = "us-east-1"):
        """Initialize deployment manager."""
        self.stage = stage
        self.region = region
        self.project_root = Path(__file__).parent.parent
        self.services = ["auth", "payment", "tenant", "user", "admin", "events", "security"]
        self.deployment_log = []
        
    def log(self, message: str, level: str = "INFO"):
        """Log deployment message."""
        timestamp = datetime.now().isoformat()
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.deployment_log.append(log_entry)
    
    def run_command(self, command: str, cwd: Optional[Path] = None) -> Tuple[bool, str]:
        """Run shell command and return success status and output."""
        try:
            if cwd is None:
                cwd = self.project_root
            
            self.log(f"Running: {command}")
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                self.log(f"Command succeeded: {command}")
                return True, result.stdout
            else:
                self.log(f"Command failed: {command}\nError: {result.stderr}", "ERROR")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            self.log(f"Command timed out: {command}", "ERROR")
            return False, "Command timed out"
        except Exception as e:
            self.log(f"Command error: {command}\nException: {str(e)}", "ERROR")
            return False, str(e)
    
    def validate_prerequisites(self) -> bool:
        """Validate deployment prerequisites."""
        self.log("🔍 Validating deployment prerequisites...")
        
        # Check if serverless is installed
        success, _ = self.run_command("serverless --version")
        if not success:
            self.log("Serverless Framework not installed", "ERROR")
            return False
        
        # Check AWS credentials
        success, _ = self.run_command("aws sts get-caller-identity")
        if not success:
            self.log("AWS credentials not configured", "ERROR")
            return False
        
        # Check if shared layer exists
        shared_path = self.project_root / "shared" / "serverless.yml"
        if not shared_path.exists():
            self.log("Shared layer configuration not found", "ERROR")
            return False
        
        # Validate service configurations
        for service in self.services:
            service_config = self.project_root / "services" / service / "serverless.yml"
            if not service_config.exists():
                self.log(f"Service configuration not found: {service}", "WARNING")
        
        self.log("✅ Prerequisites validation passed")
        return True
    
    def deploy_shared_layer(self) -> bool:
        """Deploy shared layer first."""
        self.log("🚀 Deploying shared layer...")
        
        shared_dir = self.project_root / "shared"
        success, output = self.run_command(
            f"serverless deploy --stage {self.stage} --region {self.region}",
            cwd=shared_dir
        )
        
        if success:
            self.log("✅ Shared layer deployed successfully")
            return True
        else:
            self.log("❌ Shared layer deployment failed", "ERROR")
            return False
    
    def deploy_service(self, service: str) -> bool:
        """Deploy a specific service."""
        self.log(f"🚀 Deploying service: {service}")
        
        service_dir = self.project_root / "services" / service
        if not service_dir.exists():
            self.log(f"Service directory not found: {service}", "WARNING")
            return True  # Skip missing services
        
        success, output = self.run_command(
            f"serverless deploy --stage {self.stage} --region {self.region}",
            cwd=service_dir
        )
        
        if success:
            self.log(f"✅ Service {service} deployed successfully")
            return True
        else:
            self.log(f"❌ Service {service} deployment failed", "ERROR")
            return False
    
    def check_service_health(self, service: str) -> bool:
        """Check service health after deployment."""
        self.log(f"🏥 Checking health for service: {service}")
        
        # Get service endpoint
        try:
            # Get stack outputs to find API Gateway URL
            success, output = self.run_command(
                f"aws cloudformation describe-stacks --stack-name agent-scl-{service}-{self.stage} --region {self.region}"
            )
            
            if not success:
                self.log(f"Could not get stack info for {service}", "WARNING")
                return True  # Skip health check if can't get endpoint
            
            # Parse stack outputs to find API endpoint
            stack_data = json.loads(output)
            stacks = stack_data.get("Stacks", [])
            if not stacks:
                self.log(f"No stack found for {service}", "WARNING")
                return True
            
            outputs = stacks[0].get("Outputs", [])
            api_endpoint = None
            
            for output in outputs:
                if "ServiceEndpoint" in output.get("OutputKey", ""):
                    api_endpoint = output.get("OutputValue")
                    break
            
            if not api_endpoint:
                self.log(f"No API endpoint found for {service}", "WARNING")
                return True
            
            # Make health check request
            health_url = f"{api_endpoint}/{service}/health"
            response = requests.get(health_url, timeout=10)
            
            if response.status_code == 200:
                self.log(f"✅ Service {service} health check passed")
                return True
            else:
                self.log(f"❌ Service {service} health check failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Health check error for {service}: {str(e)}", "WARNING")
            return True  # Don't fail deployment for health check issues
    
    def rollback_service(self, service: str) -> bool:
        """Rollback a service to previous version."""
        self.log(f"🔄 Rolling back service: {service}")
        
        service_dir = self.project_root / "services" / service
        success, output = self.run_command(
            f"serverless rollback --stage {self.stage} --region {self.region}",
            cwd=service_dir
        )
        
        if success:
            self.log(f"✅ Service {service} rolled back successfully")
            return True
        else:
            self.log(f"❌ Service {service} rollback failed", "ERROR")
            return False
    
    def deploy_all(self, skip_health_checks: bool = False) -> bool:
        """Deploy all services with validation."""
        self.log("🚀 Starting full deployment...")
        
        # Validate prerequisites
        if not self.validate_prerequisites():
            return False
        
        # Deploy shared layer first
        if not self.deploy_shared_layer():
            return False
        
        # Wait for shared layer to be available
        self.log("⏳ Waiting for shared layer to be available...")
        time.sleep(30)
        
        # Deploy services
        failed_services = []
        deployed_services = []
        
        for service in self.services:
            if self.deploy_service(service):
                deployed_services.append(service)
                
                # Health check (if not skipped)
                if not skip_health_checks:
                    time.sleep(10)  # Wait for service to be ready
                    if not self.check_service_health(service):
                        self.log(f"Health check failed for {service}, considering rollback", "WARNING")
                        # Note: Could implement automatic rollback here
            else:
                failed_services.append(service)
        
        # Summary
        self.log(f"📊 Deployment Summary:")
        self.log(f"   ✅ Deployed: {len(deployed_services)} services")
        self.log(f"   ❌ Failed: {len(failed_services)} services")
        
        if failed_services:
            self.log(f"   Failed services: {', '.join(failed_services)}")
            return False
        
        self.log("🎉 All services deployed successfully!")
        return True
    
    def save_deployment_log(self):
        """Save deployment log to file."""
        log_file = self.project_root / "logs" / f"deployment-{self.stage}-{datetime.now().strftime('%Y%m%d-%H%M%S')}.log"
        log_file.parent.mkdir(exist_ok=True)
        
        with open(log_file, 'w') as f:
            f.write('\n'.join(self.deployment_log))
        
        self.log(f"📝 Deployment log saved to: {log_file}")


def main():
    """Main deployment function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deploy services with validation")
    parser.add_argument("--stage", default="dev", help="Deployment stage")
    parser.add_argument("--region", default="us-east-1", help="AWS region")
    parser.add_argument("--service", help="Deploy specific service only")
    parser.add_argument("--skip-health", action="store_true", help="Skip health checks")
    parser.add_argument("--rollback", help="Rollback specific service")
    
    args = parser.parse_args()
    
    deployment_manager = DeploymentManager(args.stage, args.region)
    
    try:
        if args.rollback:
            success = deployment_manager.rollback_service(args.rollback)
        elif args.service:
            success = deployment_manager.deploy_service(args.service)
            if success and not args.skip_health:
                deployment_manager.check_service_health(args.service)
        else:
            success = deployment_manager.deploy_all(args.skip_health)
        
        deployment_manager.save_deployment_log()
        
        if success:
            print("\n🎉 Deployment completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Deployment failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        deployment_manager.log("Deployment interrupted by user", "WARNING")
        deployment_manager.save_deployment_log()
        sys.exit(1)
    except Exception as e:
        deployment_manager.log(f"Unexpected error: {str(e)}", "ERROR")
        deployment_manager.save_deployment_log()
        sys.exit(1)


if __name__ == "__main__":
    main()
