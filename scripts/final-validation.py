#!/usr/bin/env python3
# scripts/final-validation.py
# Script de validación final para la migración

"""
Final validation script for the migration.
"""

import os
import sys
from pathlib import Path

def validate_service_structure():
    """Validate service structure."""
    print("🔍 Validating service structure...")
    
    services_dir = Path("services")
    if not services_dir.exists():
        print("❌ Services directory not found")
        return False
    
    expected_services = [
        "auth", "payment", "tenant", "user", 
        "admin", "events", "security"
    ]
    
    for service in expected_services:
        service_path = services_dir / service / "src"
        if not service_path.exists():
            print(f"❌ Service {service} src directory not found")
            return False
        
        # Check for handlers directory
        handlers_path = service_path / "handlers"
        if not handlers_path.exists():
            print(f"❌ Service {service} handlers directory not found")
            return False
        
        print(f"✅ Service {service} structure valid")
    
    return True

def validate_shared_layer():
    """Validate shared layer structure."""
    print("\n🔍 Validating shared layer...")
    
    shared_path = Path("shared/python/shared")
    if not shared_path.exists():
        print("❌ Shared layer not found")
        return False
    
    expected_files = [
        "auth.py", "config.py", "database.py", 
        "exceptions.py", "logger.py", "responses.py", 
        "validators.py"
    ]
    
    for file in expected_files:
        file_path = shared_path / file
        if not file_path.exists():
            print(f"❌ Shared file {file} not found")
            return False
        
        print(f"✅ Shared file {file} exists")
    
    return True

def validate_imports():
    """Validate that there are no old imports."""
    print("\n🔍 Validating imports...")
    
    # Check for old imports in services
    old_imports_found = False
    
    for root, dirs, files in os.walk("services"):
        for file in files:
            if file.endswith(".py"):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if "from src." in content and not content.startswith("#"):
                            # Check if it's not just a comment
                            lines = content.split('\n')
                            for line in lines:
                                if "from src." in line and not line.strip().startswith("#"):
                                    print(f"❌ Old import found in {file_path}: {line.strip()}")
                                    old_imports_found = True
                except Exception as e:
                    print(f"⚠️ Could not read {file_path}: {e}")
    
    if not old_imports_found:
        print("✅ No old imports found")
        return True
    
    return False

def validate_tests():
    """Validate test structure."""
    print("\n🔍 Validating test structure...")
    
    tests_dir = Path("tests")
    if not tests_dir.exists():
        print("❌ Tests directory not found")
        return False
    
    conftest_path = tests_dir / "conftest.py"
    if not conftest_path.exists():
        print("❌ conftest.py not found")
        return False
    
    print("✅ Test structure valid")
    return True

def main():
    """Main validation function."""
    print("🚀 Starting final validation...")
    
    validations = [
        validate_service_structure,
        validate_shared_layer,
        validate_imports,
        validate_tests
    ]
    
    all_passed = True
    for validation in validations:
        if not validation():
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("✅ Migration completed successfully")
        return 0
    else:
        print("❌ Some validations failed")
        print("⚠️ Please review the issues above")
        return 1

if __name__ == "__main__":
    sys.exit(main())
