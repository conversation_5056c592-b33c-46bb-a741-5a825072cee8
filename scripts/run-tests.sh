#!/bin/bash
# scripts/run-tests.sh
# Implementado según "Development Standards" y "Testing Guidelines"

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_TYPE=${1:-all}
COVERAGE_THRESHOLD=90
ENVIRONMENT="test"

echo -e "${BLUE}🧪 Running Platform Tests${NC}"
echo "=================================="

# Set test environment
export TESTING=true
export ENVIRONMENT=test
export AWS_DEFAULT_REGION=us-east-1
export DYNAMODB_TABLE=platform-main-test
export S3_BUCKET=platform-data-test

# Check if pytest is installed
if ! command -v pytest &> /dev/null; then
    echo -e "${RED}❌ pytest not found. Installing...${NC}"
    pip install pytest pytest-cov pytest-asyncio pytest-mock moto
fi

# Check if coverage is installed
if ! command -v coverage &> /dev/null; then
    echo -e "${RED}❌ coverage not found. Installing...${NC}"
    pip install coverage
fi

# Function to run unit tests
run_unit_tests() {
    echo -e "${BLUE}🔬 Running Unit Tests${NC}"
    echo "------------------------"
    
    pytest tests/unit/ \
        --verbose \
        --tb=short \
        --cov=src \
        --cov-report=term-missing \
        --cov-report=html:htmlcov \
        --cov-fail-under=$COVERAGE_THRESHOLD \
        --junit-xml=test-results/unit-tests.xml
    
    echo -e "${GREEN}✅ Unit tests completed${NC}"
}

# Function to run integration tests
run_integration_tests() {
    echo -e "${BLUE}🔗 Running Integration Tests${NC}"
    echo "------------------------------"
    
    pytest tests/integration/ \
        --verbose \
        --tb=short \
        --junit-xml=test-results/integration-tests.xml
    
    echo -e "${GREEN}✅ Integration tests completed${NC}"
}

# Function to run security tests
run_security_tests() {
    echo -e "${BLUE}🔒 Running Security Tests${NC}"
    echo "---------------------------"
    
    if [ -d "tests/security" ] && [ "$(ls -A tests/security)" ]; then
        pytest tests/security/ \
            --verbose \
            --tb=short \
            --junit-xml=test-results/security-tests.xml
        
        echo -e "${GREEN}✅ Security tests completed${NC}"
    else
        echo -e "${YELLOW}⚠️ No security tests found${NC}"
    fi
}

# Function to run load tests
run_load_tests() {
    echo -e "${BLUE}⚡ Running Load Tests${NC}"
    echo "----------------------"
    
    if [ -d "tests/load" ] && [ "$(ls -A tests/load)" ]; then
        pytest tests/load/ \
            --verbose \
            --tb=short \
            --junit-xml=test-results/load-tests.xml
        
        echo -e "${GREEN}✅ Load tests completed${NC}"
    else
        echo -e "${YELLOW}⚠️ No load tests found${NC}"
    fi
}

# Function to run linting
run_linting() {
    echo -e "${BLUE}🎨 Running Code Linting${NC}"
    echo "------------------------"
    
    # Check if flake8 is installed
    if command -v flake8 &> /dev/null; then
        echo "Running flake8..."
        flake8 src/ tests/ --max-line-length=100 --exclude=__pycache__
        echo -e "${GREEN}✅ Flake8 passed${NC}"
    else
        echo -e "${YELLOW}⚠️ flake8 not installed, skipping${NC}"
    fi
    
    # Check if black is installed
    if command -v black &> /dev/null; then
        echo "Checking code formatting with black..."
        black --check src/ tests/
        echo -e "${GREEN}✅ Black formatting check passed${NC}"
    else
        echo -e "${YELLOW}⚠️ black not installed, skipping${NC}"
    fi
    
    # Check if mypy is installed
    if command -v mypy &> /dev/null; then
        echo "Running type checking with mypy..."
        mypy src/ --ignore-missing-imports
        echo -e "${GREEN}✅ MyPy type checking passed${NC}"
    else
        echo -e "${YELLOW}⚠️ mypy not installed, skipping${NC}"
    fi
}

# Function to generate test report
generate_report() {
    echo -e "${BLUE}📊 Generating Test Report${NC}"
    echo "---------------------------"
    
    # Create test results directory
    mkdir -p test-results
    
    # Generate coverage report
    if [ -f ".coverage" ]; then
        coverage report --show-missing
        coverage html -d htmlcov
        echo -e "${GREEN}✅ Coverage report generated in htmlcov/${NC}"
    fi
    
    # Generate combined test report
    cat > test-results/test-summary.txt << EOF
Platform Test Summary
====================
Date: $(date)
Environment: $ENVIRONMENT

Test Results:
- Unit Tests: $([ -f "test-results/unit-tests.xml" ] && echo "✅ PASSED" || echo "❌ FAILED")
- Integration Tests: $([ -f "test-results/integration-tests.xml" ] && echo "✅ PASSED" || echo "❌ FAILED")
- Security Tests: $([ -f "test-results/security-tests.xml" ] && echo "✅ PASSED" || echo "⚠️ SKIPPED")
- Load Tests: $([ -f "test-results/load-tests.xml" ] && echo "✅ PASSED" || echo "⚠️ SKIPPED")

Coverage: $(coverage report --show-missing | tail -1 | awk '{print $4}' || echo "N/A")
EOF
    
    echo -e "${GREEN}✅ Test summary generated in test-results/test-summary.txt${NC}"
}

# Main execution
case $TEST_TYPE in
    "unit")
        run_unit_tests
        ;;
    "integration")
        run_integration_tests
        ;;
    "security")
        run_security_tests
        ;;
    "load")
        run_load_tests
        ;;
    "lint")
        run_linting
        ;;
    "all")
        echo -e "${BLUE}🚀 Running All Tests${NC}"
        echo "====================="
        
        # Create test results directory
        mkdir -p test-results
        
        # Run all test types
        run_linting
        run_unit_tests
        run_integration_tests
        run_security_tests
        run_load_tests
        
        # Generate report
        generate_report
        
        echo ""
        echo -e "${GREEN}🎉 All tests completed successfully!${NC}"
        echo ""
        echo "📊 Test Results:"
        echo "  - Coverage Report: htmlcov/index.html"
        echo "  - Test Summary: test-results/test-summary.txt"
        echo "  - JUnit XML: test-results/*.xml"
        ;;
    *)
        echo -e "${RED}❌ Invalid test type: $TEST_TYPE${NC}"
        echo ""
        echo "Usage: $0 [unit|integration|security|load|lint|all]"
        echo ""
        echo "Examples:"
        echo "  $0 unit          # Run only unit tests"
        echo "  $0 integration   # Run only integration tests"
        echo "  $0 all           # Run all tests (default)"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}✅ Test execution completed${NC}"
