# scripts/deploy-coordinated.ps1
# Deployment coordinado para FASE 1 - Corrección de Configuración
# Ejecuta deployment en orden correcto para evitar dependencias rotas

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipSharedLayer,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipInfrastructure,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# Configuración
$ErrorActionPreference = "Stop"
$OriginalLocation = Get-Location

# Colores para output
function Write-Success { param($Message) Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "[INFO] $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "[ERROR] $Message" -ForegroundColor Red }

# Función para ejecutar deployment con manejo de errores
function Deploy-Service {
    param(
        [string]$ServicePath,
        [string]$ServiceName,
        [string]$Stage
    )
    
    Write-Info "Deploying $ServiceName to $Stage..."
    
    try {
        Set-Location $ServicePath
        
        if ($Verbose) {
            sls deploy --stage $Stage --verbose
        } else {
            sls deploy --stage $Stage
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$ServiceName deployed successfully"
            return $true
        } else {
            Write-Error "$ServiceName deployment failed with exit code $LASTEXITCODE"
            return $false
        }
    }
    catch {
        Write-Error "$ServiceName deployment failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location $OriginalLocation
    }
}

# Función para validar prerequisitos
function Test-Prerequisites {
    Write-Info "Validating prerequisites..."
    
    # Verificar Serverless Framework
    try {
        $slsVersion = sls --version
        Write-Success "Serverless Framework: $slsVersion"
    }
    catch {
        Write-Error "Serverless Framework not found. Please install: npm install -g serverless"
        return $false
    }
    
    # Verificar AWS CLI
    try {
        $awsVersion = aws --version
        Write-Success "AWS CLI: $awsVersion"
    }
    catch {
        Write-Error "AWS CLI not found. Please install AWS CLI"
        return $false
    }
    
    # Verificar credenciales AWS
    try {
        aws sts get-caller-identity | Out-Null
        Write-Success "AWS credentials configured"
    }
    catch {
        Write-Error "AWS credentials not configured. Run: aws configure"
        return $false
    }
    
    return $true
}

# Función principal de deployment
function Start-CoordinatedDeployment {
    param([string]$Stage)
    
    Write-Info "Starting coordinated deployment to $Stage environment"
    Write-Info "Deployment order: Shared Layer -> Infrastructure -> Auth -> Core -> Support -> Utility"
    
    $deploymentResults = @{}
    $totalServices = 0
    $successfulServices = 0
    
    # 1. Shared Layer (si no se omite)
    if (-not $SkipSharedLayer) {
        $totalServices++
        Write-Info "PHASE 1: Deploying Shared Layer"
        if (Deploy-Service "shared" "Shared Layer" $Stage) {
            $deploymentResults["SharedLayer"] = "[SUCCESS] Success"
            $successfulServices++
        } else {
            $deploymentResults["SharedLayer"] = "[FAILED] Failed"
            Write-Error "Shared Layer deployment failed. Stopping deployment."
            return $false
        }
        
        # Esperar a que el layer esté disponible
        Write-Info "⏳ Waiting for shared layer to be available..."
        Start-Sleep -Seconds 30
    }
    
    # 2. Infrastructure (si no se omite)
    if (-not $SkipInfrastructure) {
        $totalServices++
        Write-Info "🏗️  PHASE 2: Deploying Infrastructure"
        if (Deploy-Service "." "Infrastructure" $Stage) {
            $deploymentResults["Infrastructure"] = "✅ Success"
            $successfulServices++
        } else {
            $deploymentResults["Infrastructure"] = "❌ Failed"
            Write-Warning "Infrastructure deployment failed. Continuing with services..."
        }
        
        # Esperar a que la infraestructura esté lista
        Write-Info "⏳ Waiting for infrastructure to be ready..."
        Start-Sleep -Seconds 45
    }
    
    # 3. Auth Service (Crítico - JWT Authorizer)
    $totalServices++
    Write-Info "🔐 PHASE 3: Deploying Auth Service"
    if (Deploy-Service "services/auth" "Auth Service" $Stage) {
        $deploymentResults["AuthService"] = "✅ Success"
        $successfulServices++
    } else {
        $deploymentResults["AuthService"] = "❌ Failed"
        Write-Error "Auth Service deployment failed. JWT Authorizer may not be available."
        Write-Warning "Continuing with other services, but they may fail without authorizer..."
    }
    
    # Esperar a que el authorizer esté disponible
    Write-Info "⏳ Waiting for JWT Authorizer to be available..."
    Start-Sleep -Seconds 30
    
    # 4. Core Services
    Write-Info "🎯 PHASE 4: Deploying Core Services"
    $coreServices = @(
        @{Path="services/payment"; Name="Payment Service"},
        @{Path="services/tenant"; Name="Tenant Service"},
        @{Path="services/user"; Name="User Service"}
    )
    
    foreach ($service in $coreServices) {
        $totalServices++
        if (Deploy-Service $service.Path $service.Name $Stage) {
            $deploymentResults[$service.Name] = "✅ Success"
            $successfulServices++
        } else {
            $deploymentResults[$service.Name] = "❌ Failed"
        }
    }
    
    # 5. Support Services
    Write-Info "🛠️  PHASE 5: Deploying Support Services"
    $supportServices = @(
        @{Path="services/admin"; Name="Admin Service"},
        @{Path="services/events"; Name="Events Service"},
        @{Path="services/security"; Name="Security Service"}
    )
    
    foreach ($service in $supportServices) {
        $totalServices++
        if (Deploy-Service $service.Path $service.Name $Stage) {
            $deploymentResults[$service.Name] = "✅ Success"
            $successfulServices++
        } else {
            $deploymentResults[$service.Name] = "❌ Failed"
        }
    }
    
    # 6. Utility Services
    Write-Info "⚙️  PHASE 6: Deploying Utility Services"
    $utilityServices = @(
        @{Path="services/jobs"; Name="Jobs Service"},
        @{Path="services/orchestrator"; Name="Orchestrator Service"},
        @{Path="services/setup"; Name="Setup Service"}
    )
    
    foreach ($service in $utilityServices) {
        $totalServices++
        if (Deploy-Service $service.Path $service.Name $Stage) {
            $deploymentResults[$service.Name] = "✅ Success"
            $successfulServices++
        } else {
            $deploymentResults[$service.Name] = "❌ Failed"
        }
    }
    
    # Resumen final
    Write-Info "📊 DEPLOYMENT SUMMARY"
    Write-Info "====================="
    Write-Info "Total Services: $totalServices"
    Write-Success "Successful: $successfulServices"
    Write-Error "Failed: $($totalServices - $successfulServices)"
    Write-Info "Success Rate: $([math]::Round(($successfulServices / $totalServices) * 100, 2))%"
    
    Write-Info ""
    Write-Info "📋 Detailed Results:"
    foreach ($result in $deploymentResults.GetEnumerator()) {
        Write-Host "  $($result.Key): $($result.Value)"
    }
    
    if ($successfulServices -eq $totalServices) {
        Write-Success "🎉 All services deployed successfully!"
        return $true
    } elseif ($successfulServices -ge ($totalServices * 0.8)) {
        Write-Warning "⚠️  Most services deployed successfully. Check failed services."
        return $true
    } else {
        Write-Error "💥 Deployment failed. Too many services failed to deploy."
        return $false
    }
}

# Ejecución principal
try {
    Write-Info "🔍 FASE 1: CORRECCIÓN DE CONFIGURACIÓN - DEPLOYMENT COORDINADO"
    Write-Info "============================================================"
    
    if (-not (Test-Prerequisites)) {
        Write-Error "Prerequisites validation failed. Exiting."
        exit 1
    }
    
    $deploymentSuccess = Start-CoordinatedDeployment $Stage
    
    if ($deploymentSuccess) {
        Write-Success "🎯 Coordinated deployment completed successfully!"
        Write-Info "Next steps:"
        Write-Info "1. Run validation tests"
        Write-Info "2. Verify endpoints are accessible"
        Write-Info "3. Test JWT authorization"
        exit 0
    } else {
        Write-Error "💥 Coordinated deployment failed!"
        Write-Info "Check the logs above for specific service failures."
        exit 1
    }
}
catch {
    Write-Error "Unexpected error during deployment: $($_.Exception.Message)"
    exit 1
}
finally {
    Set-Location $OriginalLocation
}
