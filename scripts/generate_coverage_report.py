#!/usr/bin/env python3
# scripts/generate_coverage_report.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Coverage Reporting

"""
Generate comprehensive coverage report with detailed analysis.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime


class CoverageReportGenerator:
    """Generate detailed coverage reports."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.reports_dir = project_root / "reports" / "coverage"
        
    def load_coverage_data(self, coverage_file: str) -> Optional[Dict]:
        """Load coverage data from JSON file."""
        coverage_path = self.reports_dir / coverage_file
        
        if not coverage_path.exists():
            print(f"⚠️ Coverage file not found: {coverage_path}")
            return None
            
        try:
            with open(coverage_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading coverage data: {e}")
            return None
    
    def analyze_coverage_data(self, coverage_data: Dict) -> Dict:
        """Analyze coverage data and extract metrics."""
        if not coverage_data:
            return {}
            
        files = coverage_data.get('files', {})
        totals = coverage_data.get('totals', {})
        
        analysis = {
            'total_coverage': totals.get('percent_covered', 0),
            'lines_covered': totals.get('covered_lines', 0),
            'lines_missing': totals.get('missing_lines', 0),
            'total_lines': totals.get('num_statements', 0),
            'branch_coverage': totals.get('percent_covered_display', '0%'),
            'files_analyzed': len(files),
            'file_details': []
        }
        
        # Analyze individual files
        for file_path, file_data in files.items():
            file_analysis = {
                'file': file_path,
                'coverage': file_data.get('summary', {}).get('percent_covered', 0),
                'lines_covered': file_data.get('summary', {}).get('covered_lines', 0),
                'lines_missing': file_data.get('summary', {}).get('missing_lines', 0),
                'total_lines': file_data.get('summary', {}).get('num_statements', 0),
                'missing_lines': file_data.get('missing_lines', [])
            }
            analysis['file_details'].append(file_analysis)
        
        # Sort files by coverage (lowest first)
        analysis['file_details'].sort(key=lambda x: x['coverage'])
        
        return analysis
    
    def generate_html_report(self, analyses: Dict[str, Dict]) -> str:
        """Generate HTML coverage report."""
        
        html_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Coverage Report - The Jungle Agents</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .metric {{ background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }}
        .metric-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .metric-label {{ color: #6c757d; margin-top: 5px; }}
        .coverage-high {{ color: #28a745; }}
        .coverage-medium {{ color: #ffc107; }}
        .coverage-low {{ color: #dc3545; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ border-bottom: 2px solid #007bff; padding-bottom: 10px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 15px; }}
        th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f8f9fa; font-weight: bold; }}
        .progress-bar {{ width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }}
        .progress-fill {{ height: 100%; transition: width 0.3s ease; }}
        .timestamp {{ text-align: center; color: #6c757d; margin-top: 20px; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Coverage Report</h1>
            <p>The Jungle Agents Platform</p>
        </div>
        
        {summary_section}
        
        {detailed_sections}
        
        <div class="timestamp">
            Generated on {timestamp}
        </div>
    </div>
</body>
</html>
        """
        
        # Generate summary section
        summary_html = self._generate_summary_html(analyses)
        
        # Generate detailed sections
        detailed_html = ""
        for test_type, analysis in analyses.items():
            if analysis:
                detailed_html += self._generate_test_type_section(test_type, analysis)
        
        # Fill template
        html_content = html_template.format(
            summary_section=summary_html,
            detailed_sections=detailed_html,
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        )
        
        return html_content
    
    def _generate_summary_html(self, analyses: Dict[str, Dict]) -> str:
        """Generate summary section HTML."""
        
        # Calculate overall metrics
        total_coverage = 0
        total_files = 0
        total_lines = 0
        total_covered = 0
        
        valid_analyses = [a for a in analyses.values() if a]
        
        if valid_analyses:
            total_coverage = sum(a['total_coverage'] for a in valid_analyses) / len(valid_analyses)
            total_files = sum(a['files_analyzed'] for a in valid_analyses)
            total_lines = sum(a['total_lines'] for a in valid_analyses)
            total_covered = sum(a['lines_covered'] for a in valid_analyses)
        
        coverage_class = self._get_coverage_class(total_coverage)
        
        summary_html = f"""
        <div class="section">
            <h2>📊 Overall Summary</h2>
            <div class="summary">
                <div class="metric">
                    <div class="metric-value {coverage_class}">{total_coverage:.1f}%</div>
                    <div class="metric-label">Total Coverage</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{total_files}</div>
                    <div class="metric-label">Files Analyzed</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{total_lines}</div>
                    <div class="metric-label">Total Lines</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{total_covered}</div>
                    <div class="metric-label">Lines Covered</div>
                </div>
            </div>
        </div>
        """
        
        return summary_html
    
    def _generate_test_type_section(self, test_type: str, analysis: Dict) -> str:
        """Generate section for specific test type."""
        
        coverage = analysis['total_coverage']
        coverage_class = self._get_coverage_class(coverage)
        
        section_html = f"""
        <div class="section">
            <h2>🧪 {test_type.title()} Tests Coverage</h2>
            <div class="summary">
                <div class="metric">
                    <div class="metric-value {coverage_class}">{coverage:.1f}%</div>
                    <div class="metric-label">Coverage</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{analysis['files_analyzed']}</div>
                    <div class="metric-label">Files</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{analysis['lines_covered']}</div>
                    <div class="metric-label">Lines Covered</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{analysis['lines_missing']}</div>
                    <div class="metric-label">Lines Missing</div>
                </div>
            </div>
            
            <h3>📁 File Details</h3>
            <table>
                <thead>
                    <tr>
                        <th>File</th>
                        <th>Coverage</th>
                        <th>Lines</th>
                        <th>Covered</th>
                        <th>Missing</th>
                        <th>Progress</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for file_detail in analysis['file_details'][:20]:  # Show top 20 files
            file_coverage = file_detail['coverage']
            file_coverage_class = self._get_coverage_class(file_coverage)
            
            section_html += f"""
                    <tr>
                        <td>{file_detail['file']}</td>
                        <td class="{file_coverage_class}">{file_coverage:.1f}%</td>
                        <td>{file_detail['total_lines']}</td>
                        <td>{file_detail['lines_covered']}</td>
                        <td>{file_detail['lines_missing']}</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-fill {file_coverage_class}" style="width: {file_coverage}%"></div>
                            </div>
                        </td>
                    </tr>
            """
        
        section_html += """
                </tbody>
            </table>
        </div>
        """
        
        return section_html
    
    def _get_coverage_class(self, coverage: float) -> str:
        """Get CSS class based on coverage percentage."""
        if coverage >= 80:
            return "coverage-high"
        elif coverage >= 60:
            return "coverage-medium"
        else:
            return "coverage-low"
    
    def generate_markdown_report(self, analyses: Dict[str, Dict]) -> str:
        """Generate Markdown coverage report."""
        
        markdown = "# 🧪 Test Coverage Report\n\n"
        markdown += f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n"
        
        # Overall summary
        markdown += "## 📊 Overall Summary\n\n"
        
        total_coverage = 0
        valid_analyses = [a for a in analyses.values() if a]
        
        if valid_analyses:
            total_coverage = sum(a['total_coverage'] for a in valid_analyses) / len(valid_analyses)
        
        coverage_emoji = "✅" if total_coverage >= 80 else "⚠️" if total_coverage >= 60 else "❌"
        
        markdown += f"**Overall Coverage:** {coverage_emoji} {total_coverage:.1f}%\n\n"
        
        # Test type breakdown
        markdown += "## 📋 Coverage by Test Type\n\n"
        markdown += "| Test Type | Coverage | Files | Lines Covered | Lines Missing |\n"
        markdown += "|-----------|----------|-------|---------------|---------------|\n"
        
        for test_type, analysis in analyses.items():
            if analysis:
                coverage = analysis['total_coverage']
                emoji = "✅" if coverage >= 80 else "⚠️" if coverage >= 60 else "❌"
                
                markdown += f"| {test_type.title()} | {emoji} {coverage:.1f}% | {analysis['files_analyzed']} | {analysis['lines_covered']} | {analysis['lines_missing']} |\n"
        
        markdown += "\n"
        
        # Low coverage files
        markdown += "## ⚠️ Files Needing Attention (< 80% coverage)\n\n"
        
        low_coverage_files = []
        for analysis in valid_analyses:
            for file_detail in analysis['file_details']:
                if file_detail['coverage'] < 80:
                    low_coverage_files.append(file_detail)
        
        if low_coverage_files:
            low_coverage_files.sort(key=lambda x: x['coverage'])
            
            markdown += "| File | Coverage | Lines Missing |\n"
            markdown += "|------|----------|---------------|\n"
            
            for file_detail in low_coverage_files[:10]:  # Show top 10
                markdown += f"| `{file_detail['file']}` | {file_detail['coverage']:.1f}% | {file_detail['lines_missing']} |\n"
        else:
            markdown += "🎉 All files have good coverage (≥80%)!\n"
        
        markdown += "\n"
        
        return markdown
    
    def generate_reports(self):
        """Generate all coverage reports."""
        
        print("📊 Generating Coverage Reports...")
        
        # Load coverage data for different test types
        coverage_files = {
            'unit': 'unit.json',
            'integration': 'integration.json',
            'e2e': 'e2e.json',
            'combined': 'combined.json'
        }
        
        analyses = {}
        
        for test_type, filename in coverage_files.items():
            coverage_data = self.load_coverage_data(filename)
            if coverage_data:
                analyses[test_type] = self.analyze_coverage_data(coverage_data)
                print(f"✅ Loaded {test_type} coverage data")
            else:
                analyses[test_type] = None
                print(f"⚠️ No {test_type} coverage data found")
        
        # Generate HTML report
        html_content = self.generate_html_report(analyses)
        html_path = self.reports_dir / "coverage_report.html"
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"📄 HTML report generated: {html_path}")
        
        # Generate Markdown report
        markdown_content = self.generate_markdown_report(analyses)
        markdown_path = self.reports_dir / "coverage_report.md"
        
        with open(markdown_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"📝 Markdown report generated: {markdown_path}")
        
        # Print summary to console
        self._print_console_summary(analyses)
        
        return analyses
    
    def _print_console_summary(self, analyses: Dict[str, Dict]):
        """Print coverage summary to console."""
        
        print("\n" + "="*60)
        print("📊 COVERAGE SUMMARY")
        print("="*60)
        
        for test_type, analysis in analyses.items():
            if analysis:
                coverage = analysis['total_coverage']
                status = "✅" if coverage >= 80 else "⚠️" if coverage >= 60 else "❌"
                print(f"{test_type.title()} Tests: {status} {coverage:.1f}% ({analysis['lines_covered']}/{analysis['total_lines']} lines)")
        
        print("="*60)


def main():
    """Main entry point."""
    
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Create report generator
    generator = CoverageReportGenerator(project_root)
    
    # Generate reports
    analyses = generator.generate_reports()
    
    # Check if minimum coverage is met
    valid_analyses = [a for a in analyses.values() if a]
    if valid_analyses:
        overall_coverage = sum(a['total_coverage'] for a in valid_analyses) / len(valid_analyses)
        
        if overall_coverage < 80:
            print(f"\n❌ Coverage {overall_coverage:.1f}% is below minimum threshold (80%)")
            sys.exit(1)
        else:
            print(f"\n✅ Coverage {overall_coverage:.1f}% meets minimum threshold (80%)")
    
    print("\n🎉 Coverage report generation completed!")


if __name__ == "__main__":
    main()
