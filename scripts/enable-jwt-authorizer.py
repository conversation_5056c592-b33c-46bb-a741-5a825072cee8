#!/usr/bin/env python3
"""
Script to re-enable JWT Authorizer references in all services
after coordinated deployment strategy.
"""

import os
import re
from pathlib import Path


def enable_jwt_authorizer_in_file(file_path):
    """Re-enable JWT Authorizer references in a serverless.yml file."""
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    changes = 0
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this line contains commented "authorizer:"
        if re.match(r'^(\s+)# authorizer:\s*# DISABLED - Coordinated deployment$', line):
            indent = re.match(r'^(\s+)', line).group(1)
            # Uncomment this line
            new_lines.append(f"{indent}authorizer:\n")
            changes += 1
            
            # Uncomment the next lines in the authorizer block
            for j in range(1, 5):  # name, type, arn, Fn::ImportValue
                if i + j < len(lines):
                    next_line = lines[i + j]
                    if re.match(r'^(\s+)# .+# DISABLED - Coordinated deployment$', next_line):
                        # Extract the commented content and uncomment it
                        match = re.match(r'^(\s+)# (.+?)\s*# DISABLED - Coordinated deployment$', next_line)
                        if match:
                            next_indent = match.group(1)
                            content = match.group(2).strip()
                            new_lines.append(f"{next_indent}{content}\n")
                            changes += 1
                        else:
                            new_lines.append(next_line)
                    else:
                        new_lines.append(next_line)
                else:
                    break
            
            i += 4  # Skip the lines we just processed
        else:
            new_lines.append(line)
        
        i += 1
    
    if changes > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        print(f"  ✅ Re-enabled {changes} JWT authorizer references")
    else:
        print(f"  ℹ️ No disabled JWT authorizer references found")
    
    return changes


def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    total_changes = 0
    
    print("🚀 Re-enabling JWT Authorizer references in all services...")
    print("=" * 70)
    
    # Process all service serverless.yml files except auth
    services_dir = project_root / "services"
    if services_dir.exists():
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir() and service_dir.name != "auth":  # Skip auth service
                serverless_file = service_dir / "serverless.yml"
                if serverless_file.exists():
                    changes = enable_jwt_authorizer_in_file(serverless_file)
                    total_changes += changes
    
    print("=" * 70)
    print(f"✅ Total changes: {total_changes} JWT authorizer references re-enabled")


if __name__ == "__main__":
    main()
