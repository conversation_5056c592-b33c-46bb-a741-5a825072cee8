#!/usr/bin/env python3
# scripts/run_tests.py
# Implementado según "Testing Guidelines" y Fase 1 Plan - Test Coverage

"""
Comprehensive test runner script.
Runs all test suites and generates coverage reports.
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional


class TestRunner:
    """Test runner with coverage reporting."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_results = {}
        
    def run_unit_tests(self, verbose: bool = False) -> Dict:
        """Run unit tests with coverage."""
        print("🧪 Running Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/unit/",
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html:reports/coverage/unit",
            "--cov-report=json:reports/coverage/unit.json",
            "--junit-xml=reports/junit/unit.xml",
            "-v" if verbose else "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        return {
            "name": "Unit Tests",
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr,
            "coverage_file": "reports/coverage/unit.json"
        }
    
    def run_integration_tests(self, verbose: bool = False) -> Dict:
        """Run integration tests."""
        print("🔗 Running Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/integration/",
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html:reports/coverage/integration",
            "--cov-report=json:reports/coverage/integration.json",
            "--junit-xml=reports/junit/integration.xml",
            "-v" if verbose else "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        return {
            "name": "Integration Tests",
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr,
            "coverage_file": "reports/coverage/integration.json"
        }
    
    def run_e2e_tests(self, verbose: bool = False) -> Dict:
        """Run end-to-end tests."""
        print("🌐 Running E2E Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/e2e/",
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html:reports/coverage/e2e",
            "--cov-report=json:reports/coverage/e2e.json",
            "--junit-xml=reports/junit/e2e.xml",
            "-v" if verbose else "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        return {
            "name": "E2E Tests",
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr,
            "coverage_file": "reports/coverage/e2e.json"
        }
    
    def run_performance_tests(self, verbose: bool = False) -> Dict:
        """Run performance tests."""
        print("⚡ Running Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/performance/",
            "--junit-xml=reports/junit/performance.xml",
            "-v" if verbose else "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        return {
            "name": "Performance Tests",
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
    
    def run_security_tests(self, verbose: bool = False) -> Dict:
        """Run security tests."""
        print("🔒 Running Security Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/security/",
            "--junit-xml=reports/junit/security.xml",
            "-v" if verbose else "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        return {
            "name": "Security Tests",
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
    
    def run_infrastructure_tests(self, verbose: bool = False) -> Dict:
        """Run infrastructure tests."""
        print("🏗️ Running Infrastructure Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/infrastructure/",
            "--junit-xml=reports/junit/infrastructure.xml",
            "-v" if verbose else "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        return {
            "name": "Infrastructure Tests",
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
    
    def generate_combined_coverage_report(self) -> Dict:
        """Generate combined coverage report."""
        print("📊 Generating Combined Coverage Report...")
        
        # Combine coverage data
        cmd = [
            "python", "-m", "pytest",
            "tests/unit/", "tests/integration/", "tests/e2e/",
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html:reports/coverage/combined",
            "--cov-report=json:reports/coverage/combined.json",
            "--cov-fail-under=80",
            "-q"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        
        # Parse coverage data
        coverage_data = {}
        coverage_file = self.project_root / "reports/coverage/combined.json"
        
        if coverage_file.exists():
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
        
        return {
            "name": "Combined Coverage",
            "success": result.returncode == 0,
            "coverage_data": coverage_data,
            "coverage_file": "reports/coverage/combined.json"
        }
    
    def create_reports_directory(self):
        """Create reports directory structure."""
        reports_dir = self.project_root / "reports"
        
        directories = [
            "reports/coverage",
            "reports/junit",
            "reports/performance",
            "reports/security"
        ]
        
        for directory in directories:
            (self.project_root / directory).mkdir(parents=True, exist_ok=True)
    
    def run_all_tests(self, test_types: Optional[List[str]] = None, verbose: bool = False) -> Dict:
        """Run all specified test types."""
        
        # Create reports directory
        self.create_reports_directory()
        
        # Define available test types
        available_tests = {
            "unit": self.run_unit_tests,
            "integration": self.run_integration_tests,
            "e2e": self.run_e2e_tests,
            "performance": self.run_performance_tests,
            "security": self.run_security_tests,
            "infrastructure": self.run_infrastructure_tests
        }
        
        # Default to all tests if none specified
        if test_types is None:
            test_types = list(available_tests.keys())
        
        results = {}
        
        # Run specified tests
        for test_type in test_types:
            if test_type in available_tests:
                try:
                    results[test_type] = available_tests[test_type](verbose)
                except Exception as e:
                    results[test_type] = {
                        "name": test_type.title() + " Tests",
                        "success": False,
                        "error": str(e)
                    }
            else:
                print(f"⚠️ Unknown test type: {test_type}")
        
        # Generate combined coverage report if coverage tests were run
        coverage_tests = ["unit", "integration", "e2e"]
        if any(test_type in test_types for test_type in coverage_tests):
            results["coverage"] = self.generate_combined_coverage_report()
        
        return results
    
    def print_summary(self, results: Dict):
        """Print test results summary."""
        print("\n" + "="*60)
        print("📋 TEST RESULTS SUMMARY")
        print("="*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result.get("success", False))
        
        for test_type, result in results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            print(f"{result.get('name', test_type.title())}: {status}")
            
            if not result.get("success", False) and result.get("error"):
                print(f"   Error: {result['error'][:100]}...")
        
        print(f"\nOverall: {passed_tests}/{total_tests} test suites passed")
        
        # Print coverage summary if available
        if "coverage" in results and results["coverage"].get("coverage_data"):
            coverage_data = results["coverage"]["coverage_data"]
            total_coverage = coverage_data.get("totals", {}).get("percent_covered", 0)
            print(f"Total Coverage: {total_coverage:.1f}%")
        
        print("="*60)
        
        return passed_tests == total_tests


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run test suites with coverage reporting")
    
    parser.add_argument(
        "--types",
        nargs="+",
        choices=["unit", "integration", "e2e", "performance", "security", "infrastructure"],
        help="Test types to run (default: all)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--fail-fast",
        action="store_true",
        help="Stop on first failure"
    )
    
    args = parser.parse_args()
    
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Create test runner
    runner = TestRunner(project_root)
    
    # Run tests
    print("🚀 Starting Test Suite Execution...")
    results = runner.run_all_tests(args.types, args.verbose)
    
    # Print summary
    all_passed = runner.print_summary(results)
    
    # Exit with appropriate code
    sys.exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
