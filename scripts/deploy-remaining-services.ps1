# Deploy remaining services without dependencies
Write-Host "🚀 Deploying remaining services without dependencies..." -ForegroundColor Green

$services = @("payment", "tenant", "events", "security")
$results = @()

foreach ($service in $services) {
    Write-Host "📦 Deploying $service service..." -ForegroundColor Yellow
    
    Set-Location "services\$service"
    
    $startTime = Get-Date
    try {
        $output = & serverless deploy --stage dev --region us-east-1 2>&1
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $service service deployed successfully in $([math]::Round($duration))s" -ForegroundColor Green
            $results += @{
                Service = $service
                Status = "SUCCESS"
                Duration = $duration
                Output = $output
            }
        } else {
            Write-Host "❌ $service service deployment failed" -ForegroundColor Red
            $results += @{
                Service = $service
                Status = "FAILED"
                Duration = $duration
                Output = $output
            }
        }
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        Write-Host "❌ $service service deployment error: $_" -ForegroundColor Red
        $results += @{
            Service = $service
            Status = "ERROR"
            Duration = $duration
            Output = $_.Exception.Message
        }
    }
    
    Set-Location "..\..\"
}

Write-Host "`n📊 Deployment Summary:" -ForegroundColor Cyan
Write-Host "=" * 50
foreach ($result in $results) {
    $status = if ($result.Status -eq "SUCCESS") { "✅" } else { "❌" }
    Write-Host "$status $($result.Service): $($result.Status) ($([math]::Round($result.Duration))s)"
}

Write-Host "`n🎯 Next: Deploy Auth Service and Shared Layer" -ForegroundColor Yellow
