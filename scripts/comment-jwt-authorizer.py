#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to comment JWT Authorizer references in all services
for coordinated deployment strategy.
"""

import os
import re
from pathlib import Path


def comment_jwt_authorizer_in_file(file_path):
    """Comment JWT Authorizer references in a serverless.yml file."""
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    changes = 0
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this line contains "authorizer:" (not already commented)
        if re.match(r'^(\s+)authorizer:\s*$', line) and not line.strip().startswith('#'):
            indent = re.match(r'^(\s+)', line).group(1)
            # Comment this line and the next 4 lines (authorizer block)
            new_lines.append(f"{indent}# authorizer:  # DISABLED - Coordinated deployment\n")
            changes += 1
            
            # Comment the next lines in the authorizer block
            for j in range(1, 5):  # name, type, arn, Fn::ImportValue
                if i + j < len(lines):
                    next_line = lines[i + j]
                    if next_line.strip() and not next_line.strip().startswith('#'):
                        # Get the indentation and comment the line
                        if re.match(r'^(\s+)', next_line):
                            next_indent = re.match(r'^(\s+)', next_line).group(1)
                            commented_content = next_line.strip()
                            new_lines.append(f"{next_indent}# {commented_content}  # DISABLED - Coordinated deployment\n")
                        else:
                            new_lines.append(f"# {next_line.strip()}  # DISABLED - Coordinated deployment\n")
                        changes += 1
                    else:
                        new_lines.append(next_line)
                else:
                    break
            
            i += 4  # Skip the lines we just processed
        else:
            new_lines.append(line)
        
        i += 1
    
    if changes > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        print(f"  ✅ Commented {changes} JWT authorizer references")
    else:
        print(f"  ℹ️ No JWT authorizer references found")
    
    return changes


def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    total_changes = 0
    
    print("🔧 Commenting JWT Authorizer references in all services...")
    print("=" * 70)
    
    # Process all service serverless.yml files except auth
    services_dir = project_root / "services"
    if services_dir.exists():
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir() and service_dir.name != "auth":  # Skip auth service
                serverless_file = service_dir / "serverless.yml"
                if serverless_file.exists():
                    changes = comment_jwt_authorizer_in_file(serverless_file)
                    total_changes += changes
    
    print("=" * 70)
    print(f"✅ Total changes: {total_changes} JWT authorizer references commented")


if __name__ == "__main__":
    main()
