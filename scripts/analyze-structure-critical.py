#!/usr/bin/env python3
# scripts/analyze-structure-critical.py
# Análisis crítico de estructura para identificar recursos obsoletos

"""
Critical structure analysis to identify obsolete, duplicate, and unnecessary resources.
"""

import os
import json
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set

def analyze_serverless_configs():
    """Analyze all serverless.yml files for duplicates and obsoletes."""
    print("🔍 Analyzing serverless configurations...")
    
    serverless_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file == 'serverless.yml':
                full_path = os.path.join(root, file)
                serverless_files.append(full_path)
    
    print(f"Found {len(serverless_files)} serverless.yml files:")
    
    # Categorize by purpose
    categories = {
        'services': [],
        'infrastructure': [],
        'backup': [],
        'obsolete': []
    }
    
    for file_path in serverless_files:
        if 'backup' in file_path:
            categories['backup'].append(file_path)
        elif 'services/' in file_path and 'services\\' in file_path:
            # Check if corresponding service exists
            service_name = Path(file_path).parent.name
            service_src = f"services/{service_name}/src"
            if os.path.exists(service_src):
                categories['services'].append(file_path)
            else:
                categories['obsolete'].append(file_path)
        elif 'infrastructure' in file_path:
            categories['infrastructure'].append(file_path)
        else:
            categories['services'].append(file_path)
    
    return categories

def analyze_documentation_files():
    """Analyze documentation files for obsoletes and duplicates."""
    print("\n📚 Analyzing documentation files...")
    
    # Get all .md files in root
    root_md_files = [f for f in os.listdir('.') if f.endswith('.md')]
    
    # Categorize by type
    categories = {
        'reports': [],
        'migration': [],
        'architecture': [],
        'business': [],
        'main': [],
        'obsolete': []
    }
    
    for file in root_md_files:
        file_lower = file.lower()
        if any(word in file_lower for word in ['report', 'audit', 'complete']):
            categories['reports'].append(file)
        elif 'migration' in file_lower:
            categories['migration'].append(file)
        elif any(word in file_lower for word in ['architecture', 'deployment']):
            categories['architecture'].append(file)
        elif 'business' in file_lower:
            categories['business'].append(file)
        elif file in ['README.md', 'DOCUMENTATION_INDEX.md']:
            categories['main'].append(file)
        else:
            categories['obsolete'].append(file)
    
    return categories

def analyze_scripts():
    """Analyze scripts for duplicates and obsoletes."""
    print("\n🔧 Analyzing scripts...")
    
    scripts_dir = Path('scripts')
    if not scripts_dir.exists():
        return {}
    
    scripts = list(scripts_dir.glob('*'))
    
    # Group by functionality
    categories = {
        'deployment': [],
        'testing': [],
        'validation': [],
        'migration': [],
        'audit': [],
        'fix': [],
        'obsolete': []
    }
    
    for script in scripts:
        script_name = script.name.lower()
        if any(word in script_name for word in ['deploy', 'setup']):
            categories['deployment'].append(str(script))
        elif any(word in script_name for word in ['test', 'coverage']):
            categories['testing'].append(str(script))
        elif 'validate' in script_name:
            categories['validation'].append(str(script))
        elif 'migration' in script_name or 'migrate' in script_name:
            categories['migration'].append(str(script))
        elif 'audit' in script_name:
            categories['audit'].append(str(script))
        elif 'fix' in script_name:
            categories['fix'].append(str(script))
        else:
            categories['obsolete'].append(str(script))
    
    return categories

def analyze_cache_and_temp():
    """Analyze cache and temporary files."""
    print("\n🗑️ Analyzing cache and temporary files...")
    
    cache_dirs = []
    temp_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip node_modules and .git
        if 'node_modules' in root or '.git' in root:
            continue
            
        for dir_name in dirs:
            if dir_name in ['__pycache__', 'htmlcov', '.pytest_cache']:
                cache_dirs.append(os.path.join(root, dir_name))
        
        for file in files:
            if file.endswith(('.pyc', '.pyo', '.coverage')):
                temp_files.append(os.path.join(root, file))
    
    return {'cache_dirs': cache_dirs, 'temp_files': temp_files}

def analyze_duplicate_directories():
    """Analyze for duplicate directory structures."""
    print("\n📁 Analyzing duplicate directories...")
    
    duplicates = {
        'serverless_dirs': [],
        'infrastructure_dirs': [],
        'backup_dirs': []
    }
    
    # Find serverless directories
    for root, dirs, files in os.walk('.'):
        if 'serverless' in root.lower() and 'node_modules' not in root:
            duplicates['serverless_dirs'].append(root)
        if 'infrastructure' in root.lower() and 'node_modules' not in root:
            duplicates['infrastructure_dirs'].append(root)
        if 'backup' in root.lower():
            duplicates['backup_dirs'].append(root)
    
    return duplicates

def generate_cleanup_plan():
    """Generate comprehensive cleanup plan."""
    print("\n📋 Generating cleanup plan...")
    
    serverless_analysis = analyze_serverless_configs()
    docs_analysis = analyze_documentation_files()
    scripts_analysis = analyze_scripts()
    cache_analysis = analyze_cache_and_temp()
    duplicates_analysis = analyze_duplicate_directories()
    
    cleanup_plan = {
        'immediate_delete': {
            'backup_dirs': duplicates_analysis['backup_dirs'],
            'cache_dirs': cache_analysis['cache_dirs'],
            'temp_files': cache_analysis['temp_files'],
            'obsolete_serverless': serverless_analysis['obsolete'],
            'backup_serverless': serverless_analysis['backup']
        },
        'consolidate': {
            'report_files': docs_analysis['reports'],
            'migration_files': docs_analysis['migration'],
            'duplicate_scripts': []
        },
        'keep': {
            'active_services': serverless_analysis['services'],
            'main_docs': docs_analysis['main'],
            'essential_scripts': scripts_analysis['deployment'] + scripts_analysis['testing']
        }
    }
    
    return cleanup_plan

def main():
    """Main analysis function."""
    print("🚀 Starting critical structure analysis...")
    
    cleanup_plan = generate_cleanup_plan()
    
    print("\n" + "="*60)
    print("📊 CLEANUP PLAN SUMMARY")
    print("="*60)
    
    print(f"\n🗑️ IMMEDIATE DELETE ({sum(len(v) if isinstance(v, list) else 1 for v in cleanup_plan['immediate_delete'].values())} items):")
    for category, items in cleanup_plan['immediate_delete'].items():
        if items:
            print(f"  {category}: {len(items)} items")
            for item in items[:3]:  # Show first 3
                print(f"    - {item}")
            if len(items) > 3:
                print(f"    ... and {len(items) - 3} more")
    
    print(f"\n📦 CONSOLIDATE ({sum(len(v) for v in cleanup_plan['consolidate'].values())} items):")
    for category, items in cleanup_plan['consolidate'].items():
        if items:
            print(f"  {category}: {len(items)} items")
    
    print(f"\n✅ KEEP ({sum(len(v) for v in cleanup_plan['keep'].values())} items):")
    for category, items in cleanup_plan['keep'].items():
        if items:
            print(f"  {category}: {len(items)} items")
    
    # Save detailed plan
    with open('cleanup_plan.json', 'w') as f:
        json.dump(cleanup_plan, f, indent=2)
    
    print(f"\n💾 Detailed plan saved to: cleanup_plan.json")
    return cleanup_plan

if __name__ == "__main__":
    main()
