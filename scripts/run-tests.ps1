# scripts/run-tests.ps1
# Implementado según "Development Standards" y "Testing Guidelines"

param(
    [string]$TestType = "all"
)

# Test configuration
$CoverageThreshold = 90
$Environment = "test"

Write-Host "🧪 Running Agent SCL Tests" -ForegroundColor Blue
Write-Host "=================================="

# Set test environment
$env:TESTING = "true"
$env:ENVIRONMENT = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"
$env:DYNAMODB_TABLE = "agentscl-main-test"
$env:S3_BUCKET = "agentscl-data-test"

# Check if pytest is installed
try {
    pytest --version | Out-Null
} catch {
    Write-Host "❌ pytest not found. Installing..." -ForegroundColor Red
    pip install pytest pytest-cov pytest-asyncio pytest-mock moto
}

# Check if coverage is installed
try {
    coverage --version | Out-Null
} catch {
    Write-Host "❌ coverage not found. Installing..." -ForegroundColor Red
    pip install coverage
}

# Function to run unit tests
function Run-UnitTests {
    Write-Host "🔬 Running Unit Tests" -ForegroundColor Blue
    Write-Host "------------------------"
    
    pytest tests/unit/ `
        --verbose `
        --tb=short `
        --cov=src `
        --cov-report=term-missing `
        --cov-report=html:htmlcov `
        --cov-fail-under=$CoverageThreshold `
        --junit-xml=test-results/unit-tests.xml
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Unit tests completed" -ForegroundColor Green
    } else {
        Write-Host "❌ Unit tests failed" -ForegroundColor Red
        exit 1
    }
}

# Function to run integration tests
function Run-IntegrationTests {
    Write-Host "🔗 Running Integration Tests" -ForegroundColor Blue
    Write-Host "------------------------------"
    
    pytest tests/integration/ `
        --verbose `
        --tb=short `
        --junit-xml=test-results/integration-tests.xml
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Integration tests completed" -ForegroundColor Green
    } else {
        Write-Host "❌ Integration tests failed" -ForegroundColor Red
        exit 1
    }
}

# Function to run security tests
function Run-SecurityTests {
    Write-Host "🔒 Running Security Tests" -ForegroundColor Blue
    Write-Host "---------------------------"
    
    if ((Test-Path "tests/security") -and (Get-ChildItem "tests/security" | Measure-Object).Count -gt 0) {
        pytest tests/security/ `
            --verbose `
            --tb=short `
            --junit-xml=test-results/security-tests.xml
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Security tests completed" -ForegroundColor Green
        } else {
            Write-Host "❌ Security tests failed" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "⚠️ No security tests found" -ForegroundColor Yellow
    }
}

# Function to run load tests
function Run-LoadTests {
    Write-Host "⚡ Running Load Tests" -ForegroundColor Blue
    Write-Host "----------------------"
    
    if ((Test-Path "tests/load") -and (Get-ChildItem "tests/load" | Measure-Object).Count -gt 0) {
        pytest tests/load/ `
            --verbose `
            --tb=short `
            --junit-xml=test-results/load-tests.xml
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Load tests completed" -ForegroundColor Green
        } else {
            Write-Host "❌ Load tests failed" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "⚠️ No load tests found" -ForegroundColor Yellow
    }
}

# Function to run linting
function Run-Linting {
    Write-Host "🎨 Running Code Linting" -ForegroundColor Blue
    Write-Host "------------------------"
    
    # Check if flake8 is installed
    try {
        flake8 --version | Out-Null
        Write-Host "Running flake8..."
        flake8 src/ tests/ --max-line-length=100 --exclude=__pycache__
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Flake8 passed" -ForegroundColor Green
        } else {
            Write-Host "❌ Flake8 failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️ flake8 not installed, skipping" -ForegroundColor Yellow
    }
    
    # Check if black is installed
    try {
        black --version | Out-Null
        Write-Host "Checking code formatting with black..."
        black --check src/ tests/
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Black formatting check passed" -ForegroundColor Green
        } else {
            Write-Host "❌ Black formatting check failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️ black not installed, skipping" -ForegroundColor Yellow
    }
    
    # Check if mypy is installed
    try {
        mypy --version | Out-Null
        Write-Host "Running type checking with mypy..."
        mypy src/ --ignore-missing-imports
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ MyPy type checking passed" -ForegroundColor Green
        } else {
            Write-Host "❌ MyPy type checking failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️ mypy not installed, skipping" -ForegroundColor Yellow
    }
}

# Function to generate test report
function Generate-Report {
    Write-Host "📊 Generating Test Report" -ForegroundColor Blue
    Write-Host "---------------------------"
    
    # Create test results directory
    New-Item -ItemType Directory -Path "test-results" -Force | Out-Null
    
    # Generate coverage report
    if (Test-Path ".coverage") {
        coverage report --show-missing
        coverage html -d htmlcov
        Write-Host "✅ Coverage report generated in htmlcov/" -ForegroundColor Green
    }
    
    # Generate combined test report
    $testSummary = @"
Platform Test Summary
====================
Date: $(Get-Date)
Environment: $Environment

Test Results:
- Unit Tests: $(if (Test-Path "test-results/unit-tests.xml") { "✅ PASSED" } else { "❌ FAILED" })
- Integration Tests: $(if (Test-Path "test-results/integration-tests.xml") { "✅ PASSED" } else { "❌ FAILED" })
- Security Tests: $(if (Test-Path "test-results/security-tests.xml") { "✅ PASSED" } else { "⚠️ SKIPPED" })
- Load Tests: $(if (Test-Path "test-results/load-tests.xml") { "✅ PASSED" } else { "⚠️ SKIPPED" })

Coverage: $(try { (coverage report --show-missing | Select-String "TOTAL").ToString().Split()[-1] } catch { "N/A" })
"@
    
    $testSummary | Out-File -FilePath "test-results/test-summary.txt" -Encoding UTF8
    
    Write-Host "✅ Test summary generated in test-results/test-summary.txt" -ForegroundColor Green
}

# Main execution
switch ($TestType.ToLower()) {
    "unit" {
        Run-UnitTests
    }
    "integration" {
        Run-IntegrationTests
    }
    "security" {
        Run-SecurityTests
    }
    "load" {
        Run-LoadTests
    }
    "lint" {
        Run-Linting
    }
    "all" {
        Write-Host "🚀 Running All Tests" -ForegroundColor Blue
        Write-Host "====================="
        
        # Create test results directory
        New-Item -ItemType Directory -Path "test-results" -Force | Out-Null
        
        # Run all test types
        Run-Linting
        Run-UnitTests
        Run-IntegrationTests
        Run-SecurityTests
        Run-LoadTests
        
        # Generate report
        Generate-Report
        
        Write-Host ""
        Write-Host "🎉 All tests completed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📊 Test Results:" -ForegroundColor Cyan
        Write-Host "  - Coverage Report: htmlcov/index.html" -ForegroundColor White
        Write-Host "  - Test Summary: test-results/test-summary.txt" -ForegroundColor White
        Write-Host "  - JUnit XML: test-results/*.xml" -ForegroundColor White
    }
    default {
        Write-Host "❌ Invalid test type: $TestType" -ForegroundColor Red
        Write-Host ""
        Write-Host "Usage: .\scripts\run-tests.ps1 [unit|integration|security|load|lint|all]"
        Write-Host ""
        Write-Host "Examples:"
        Write-Host "  .\scripts\run-tests.ps1 unit          # Run only unit tests"
        Write-Host "  .\scripts\run-tests.ps1 integration   # Run only integration tests"
        Write-Host "  .\scripts\run-tests.ps1 all           # Run all tests (default)"
        exit 1
    }
}

Write-Host ""
Write-Host "✅ Test execution completed" -ForegroundColor Green
