#!/usr/bin/env python3
# scripts/verify_consistency.py
# Verification script for code consistency

"""
Verification script to ensure all consistency issues have been resolved.
Checks for duplicated code, inconsistent patterns, and adherence to standards.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Any


class ConsistencyVerifier:
    """Verifies code consistency across the platform."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.issues_found = []
        self.checks_passed = []
    
    def check_email_validation_consistency(self) -> None:
        """Check that all email validation uses shared validator."""
        print("🔍 Checking email validation consistency...")
        
        # Patterns that indicate duplicated email validation
        bad_patterns = [
            r"if\s+['\"]@['\"].*not\s+in\s+email",
            r"email_pattern\s*=\s*r['\"].*@.*['\"]",
            r"re\.match\(.*email.*\)",
            r"if.*@.*not.*in.*email"
        ]
        
        # Find all Python files
        python_files = []
        for service_dir in (self.project_root / "services").iterdir():
            if service_dir.is_dir():
                for py_file in service_dir.rglob("*.py"):
                    if not py_file.name.startswith("__"):
                        python_files.append(py_file)
        
        issues_found = False
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in bad_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        self.issues_found.append(f"Duplicated email validation in {py_file}")
                        issues_found = True
                        print(f"  ❌ Found duplicated email validation: {py_file}")
                        
            except Exception as e:
                print(f"  ⚠️  Error reading {py_file}: {str(e)}")
        
        if not issues_found:
            self.checks_passed.append("Email validation consistency")
            print("  ✅ All email validation uses shared validator")
    
    def check_request_parsing_consistency(self) -> None:
        """Check that request parsing uses shared utility."""
        print("🔍 Checking request parsing consistency...")
        
        # Pattern that indicates duplicated request parsing
        bad_pattern = r"body\s*=\s*event\.get\(['\"]body['\"].*\n.*json\.loads"
        
        # Find all handler files
        handler_files = []
        for service_dir in (self.project_root / "services").iterdir():
            if service_dir.is_dir():
                handlers_dir = service_dir / "src" / "handlers"
                if handlers_dir.exists():
                    for handler_file in handlers_dir.glob("*.py"):
                        if not handler_file.name.startswith("__"):
                            handler_files.append(handler_file)
        
        issues_found = False
        for handler_file in handler_files:
            try:
                with open(handler_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if re.search(bad_pattern, content, re.MULTILINE):
                    # Check if it's using shared utility
                    if "parse_request_body" not in content:
                        self.issues_found.append(f"Duplicated request parsing in {handler_file}")
                        issues_found = True
                        print(f"  ❌ Found duplicated request parsing: {handler_file}")
                        
            except Exception as e:
                print(f"  ⚠️  Error reading {handler_file}: {str(e)}")
        
        if not issues_found:
            self.checks_passed.append("Request parsing consistency")
            print("  ✅ All request parsing uses shared utility")
    
    def check_cors_import_consistency(self) -> None:
        """Check that CORS imports are at module level."""
        print("🔍 Checking CORS import consistency...")
        
        # Pattern that indicates CORS import inside function
        bad_pattern = r"from\s+shared\.responses\s+import.*handle_cors_preflight"
        
        # Find all handler files
        handler_files = []
        for service_dir in (self.project_root / "services").iterdir():
            if service_dir.is_dir():
                handlers_dir = service_dir / "src" / "handlers"
                if handlers_dir.exists():
                    for handler_file in handlers_dir.glob("*.py"):
                        if not handler_file.name.startswith("__"):
                            handler_files.append(handler_file)
        
        issues_found = False
        for handler_file in handler_files:
            try:
                with open(handler_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # Check if CORS is used
                content = ''.join(lines)
                if "handle_cors_preflight" in content:
                    # Check if import is inside a function (indented)
                    for i, line in enumerate(lines):
                        if re.search(bad_pattern, line) and line.startswith('    '):
                            self.issues_found.append(f"CORS import inside function in {handler_file}:{i+1}")
                            issues_found = True
                            print(f"  ❌ Found CORS import inside function: {handler_file}:{i+1}")
                        
            except Exception as e:
                print(f"  ⚠️  Error reading {handler_file}: {str(e)}")
        
        if not issues_found:
            self.checks_passed.append("CORS import consistency")
            print("  ✅ All CORS imports are at module level")
    
    def check_handler_naming_consistency(self) -> None:
        """Check that all handlers use standard naming."""
        print("🔍 Checking handler naming consistency...")
        
        # Find all handler files
        handler_files = []
        for service_dir in (self.project_root / "services").iterdir():
            if service_dir.is_dir():
                handlers_dir = service_dir / "src" / "handlers"
                if handlers_dir.exists():
                    for handler_file in handlers_dir.glob("*.py"):
                        if not handler_file.name.startswith("__"):
                            handler_files.append(handler_file)
        
        issues_found = False
        for handler_file in handler_files:
            try:
                with open(handler_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for non-standard handler function names
                non_standard_patterns = [
                    r"def\s+\w+_handler\s*\(",
                    r"def\s+handle_\w+\s*\(",
                ]
                
                for pattern in non_standard_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        # Check if it also has a standard handler function
                        if not re.search(r"def\s+handler\s*\(", content):
                            self.issues_found.append(f"Non-standard handler name in {handler_file}")
                            issues_found = True
                            print(f"  ❌ Found non-standard handler name: {handler_file}")
                        
            except Exception as e:
                print(f"  ⚠️  Error reading {handler_file}: {str(e)}")
        
        if not issues_found:
            self.checks_passed.append("Handler naming consistency")
            print("  ✅ All handlers use standard naming")
    
    def check_shared_utilities_usage(self) -> None:
        """Check that shared utilities are being used properly."""
        print("🔍 Checking shared utilities usage...")
        
        # Check if shared/request_utils.py exists
        request_utils_path = self.project_root / "shared/python/shared/request_utils.py"
        if not request_utils_path.exists():
            self.issues_found.append("shared/request_utils.py does not exist")
            print("  ❌ shared/request_utils.py not found")
            return
        
        # Check if validate_email_address is used consistently
        validator_issues = 0
        for service_dir in (self.project_root / "services").iterdir():
            if service_dir.is_dir():
                for py_file in service_dir.rglob("*.py"):
                    if not py_file.name.startswith("__"):
                        try:
                            with open(py_file, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # If file validates email, it should use shared validator
                            if "email" in content.lower() and "valid" in content.lower():
                                if "validate_email_address" not in content and "@" in content:
                                    # Check if it's actually doing email validation
                                    if re.search(r"['\"]@['\"].*email|email.*['\"]@['\"]", content):
                                        validator_issues += 1
                                        
                        except Exception:
                            pass
        
        if validator_issues == 0:
            self.checks_passed.append("Shared utilities usage")
            print("  ✅ Shared utilities are used consistently")
        else:
            self.issues_found.append(f"Found {validator_issues} files not using shared validators")
            print(f"  ❌ Found {validator_issues} files not using shared validators")
    
    def generate_report(self) -> None:
        """Generate verification report."""
        print("\n" + "="*60)
        print("📊 CONSISTENCY VERIFICATION REPORT")
        print("="*60)
        
        print(f"\n✅ CHECKS PASSED ({len(self.checks_passed)}):")
        for check in self.checks_passed:
            print(f"  • {check}")
        
        if self.issues_found:
            print(f"\n❌ ISSUES FOUND ({len(self.issues_found)}):")
            for issue in self.issues_found:
                print(f"  • {issue}")
        else:
            print(f"\n🎉 NO ISSUES FOUND!")
        
        print(f"\nSUMMARY:")
        print(f"  Checks passed: {len(self.checks_passed)}")
        print(f"  Issues found: {len(self.issues_found)}")
        
        if len(self.issues_found) == 0:
            print(f"  Status: ✅ ALL CONSISTENCY CHECKS PASSED")
        else:
            print(f"  Status: ❌ ISSUES NEED TO BE RESOLVED")
        
        print("="*60)
    
    def run_all_checks(self) -> bool:
        """Run all consistency checks."""
        print("🔧 Running Consistency Verification")
        print("="*50)
        
        self.check_email_validation_consistency()
        self.check_request_parsing_consistency()
        self.check_cors_import_consistency()
        self.check_handler_naming_consistency()
        self.check_shared_utilities_usage()
        
        self.generate_report()
        
        return len(self.issues_found) == 0


def main():
    """Main function."""
    verifier = ConsistencyVerifier()
    
    success = verifier.run_all_checks()
    
    if success:
        print("\n🎉 All consistency checks passed!")
        sys.exit(0)
    else:
        print("\n⚠️  Some consistency issues found. Please review and fix.")
        sys.exit(1)


if __name__ == "__main__":
    main()
