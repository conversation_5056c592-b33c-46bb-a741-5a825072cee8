# scripts/deploy_complete_platform.ps1
# Complete platform deployment script for Windows

param(
    [string]$Stage = "dev",
    [switch]$SkipValidation = $false,
    [switch]$Verbose = $false
)

# Color functions
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Progress { param($Message) Write-Host "🚀 $Message" -ForegroundColor Blue }

# Global variables
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$DeploymentResults = @()
$FailedDeployments = @()
$StartTime = Get-Date

# Deployment order (respecting dependencies)
$DeploymentOrder = @(
    @{
        Name = "Shared Layer"
        Path = "shared"
        Critical = $true
        Timeout = 300
    },
    @{
        Name = "Auth Service"
        Path = "services/auth"
        Critical = $true
        Timeout = 600
    },
    @{
        Name = "Tenant Service"
        Path = "services/tenant"
        Critical = $true
        Timeout = 600
    },
    @{
        Name = "Payment Service"
        Path = "services/payment"
        Critical = $true
        Timeout = 600
    },
    @{
        Name = "Setup Service"
        Path = "services/setup"
        Critical = $true
        Timeout = 600
    },
    @{
        Name = "Orchestrator Service"
        Path = "services/orchestrator"
        Critical = $true
        Timeout = 600
    },
    @{
        Name = "Jobs Service"
        Path = "services/jobs"
        Critical = $false
        Timeout = 600
    }
)

function Test-Prerequisites {
    Write-Progress "Validating deployment prerequisites..."
    
    # Check Serverless Framework
    try {
        $slsVersion = & serverless --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Serverless Framework: $($slsVersion -split "`n" | Select-Object -First 1)"
        } else {
            Write-Error "Serverless Framework not installed"
            return $false
        }
    } catch {
        Write-Error "Error checking Serverless Framework: $($_.Exception.Message)"
        return $false
    }
    
    # Check AWS CLI
    try {
        $awsIdentity = & aws sts get-caller-identity 2>$null | ConvertFrom-Json
        if ($LASTEXITCODE -eq 0) {
            Write-Success "AWS credentials configured for account: $($awsIdentity.Account)"
        } else {
            Write-Error "AWS credentials not configured"
            return $false
        }
    } catch {
        Write-Error "Error checking AWS credentials: $($_.Exception.Message)"
        return $false
    }
    
    # Check Node.js
    try {
        $nodeVersion = & node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Node.js: $nodeVersion"
        } else {
            Write-Error "Node.js not installed"
            return $false
        }
    } catch {
        Write-Error "Error checking Node.js: $($_.Exception.Message)"
        return $false
    }
    
    Write-Success "All prerequisites validated"
    return $true
}

function Deploy-Service {
    param(
        [hashtable]$Service
    )
    
    $serviceName = $Service.Name
    $servicePath = $Service.Path
    $timeout = $Service.Timeout
    
    Write-Host ""
    Write-Progress "Deploying $serviceName..."
    Write-Info "Path: $servicePath"
    Write-Info "Stage: $Stage"
    Write-Host ("=" * 60)
    
    try {
        # Check service path
        $fullPath = Join-Path $ProjectRoot $servicePath
        
        if (-not (Test-Path $fullPath)) {
            Write-Error "Service path does not exist: $fullPath"
            return $false
        }
        
        # Check serverless.yml
        $serverlessFile = Join-Path $fullPath "serverless.yml"
        if (-not (Test-Path $serverlessFile)) {
            Write-Error "serverless.yml not found in $fullPath"
            return $false
        }
        
        Write-Info "Working directory: $fullPath"
        
        # Run serverless deploy
        $deployStart = Get-Date
        
        Write-Info "Running: serverless deploy --stage $Stage --verbose"
        
        $process = Start-Process -FilePath "serverless" -ArgumentList "deploy", "--stage", $Stage, "--verbose" -WorkingDirectory $fullPath -PassThru -Wait -NoNewWindow -RedirectStandardOutput "$env:TEMP\sls-out.txt" -RedirectStandardError "$env:TEMP\sls-err.txt"
        
        $deployEnd = Get-Date
        $duration = ($deployEnd - $deployStart).TotalSeconds
        
        $stdout = Get-Content "$env:TEMP\sls-out.txt" -Raw -ErrorAction SilentlyContinue
        $stderr = Get-Content "$env:TEMP\sls-err.txt" -Raw -ErrorAction SilentlyContinue
        
        if ($process.ExitCode -eq 0) {
            Write-Success "$serviceName deployed successfully!"
            Write-Info "Duration: $([math]::Round($duration, 1)) seconds"
            
            # Extract endpoints
            $endpoints = @()
            if ($stdout) {
                $lines = $stdout -split "`n"
                foreach ($line in $lines) {
                    if ($line -match "https://.*execute-api.*") {
                        $endpoints += $line.Trim()
                    }
                }
            }
            
            $script:DeploymentResults += @{
                Service = $serviceName
                Status = "SUCCESS"
                Duration = $duration
                Endpoints = $endpoints
            }
            
            if ($endpoints.Count -gt 0) {
                Write-Info "Endpoints deployed:"
                foreach ($endpoint in $endpoints) {
                    Write-Host "   🔗 $endpoint" -ForegroundColor Cyan
                }
            }
            
            return $true
        } else {
            Write-Error "$serviceName deployment failed!"
            Write-Info "Duration: $([math]::Round($duration, 1)) seconds"
            if ($stderr) {
                Write-Error "Error output:"
                Write-Host $stderr -ForegroundColor Red
            }
            
            $script:FailedDeployments += @{
                Service = $serviceName
                Error = $stderr
                Duration = $duration
            }
            
            return $false
        }
        
    } catch {
        Write-Error "$serviceName deployment failed with exception: $($_.Exception.Message)"
        $script:FailedDeployments += @{
            Service = $serviceName
            Error = $_.Exception.Message
            Duration = 0
        }
        return $false
    } finally {
        # Cleanup temp files
        Remove-Item "$env:TEMP\sls-out.txt" -ErrorAction SilentlyContinue
        Remove-Item "$env:TEMP\sls-err.txt" -ErrorAction SilentlyContinue
    }
}

function Deploy-All {
    Write-Progress "Starting Complete Platform Deployment"
    Write-Info "Stage: $Stage"
    Write-Info "Project Root: $ProjectRoot"
    Write-Info "Services to deploy: $($DeploymentOrder.Count)"
    Write-Host ("=" * 80)
    
    $criticalFailures = 0
    
    for ($i = 0; $i -lt $DeploymentOrder.Count; $i++) {
        $service = $DeploymentOrder[$i]
        $serviceNum = $i + 1
        
        Write-Host ""
        Write-Host "📦 [$serviceNum/$($DeploymentOrder.Count)] $($service.Name)" -ForegroundColor Magenta
        
        $success = Deploy-Service -Service $service
        
        if (-not $success) {
            if ($service.Critical) {
                $criticalFailures++
                Write-Error "CRITICAL SERVICE FAILED: $($service.Name)"
                
                # Ask user if they want to continue
                $response = Read-Host "`n❓ Continue with remaining deployments? (y/N)"
                if ($response.ToLower() -ne 'y') {
                    Write-Warning "Deployment stopped by user"
                    break
                }
            } else {
                Write-Warning "Non-critical service failed: $($service.Name)"
            }
        }
        
        # Small delay between deployments
        if ($serviceNum -lt $DeploymentOrder.Count) {
            Write-Info "Waiting 10 seconds before next deployment..."
            Start-Sleep -Seconds 10
        }
    }
    
    $totalDuration = (Get-Date) - $StartTime
    
    # Generate deployment report
    Write-DeploymentReport -TotalDuration $totalDuration.TotalSeconds -CriticalFailures $criticalFailures
    
    return $criticalFailures -eq 0
}

function Write-DeploymentReport {
    param(
        [double]$TotalDuration,
        [int]$CriticalFailures
    )
    
    Write-Host ""
    Write-Host ("=" * 80)
    Write-Host "📊 DEPLOYMENT REPORT" -ForegroundColor Yellow
    Write-Host ("=" * 80)
    
    Write-Host ""
    Write-Info "Total Duration: $([math]::Round($TotalDuration, 1)) seconds ($([math]::Round($TotalDuration/60, 1)) minutes)"
    Write-Success "Successful Deployments: $($DeploymentResults.Count)"
    Write-Error "Failed Deployments: $($FailedDeployments.Count)"
    Write-Error "Critical Failures: $CriticalFailures"
    
    if ($DeploymentResults.Count -gt 0) {
        Write-Host ""
        Write-Success "SUCCESSFUL DEPLOYMENTS:"
        foreach ($result in $DeploymentResults) {
            Write-Host "   • $($result.Service) ($([math]::Round($result.Duration, 1))s)" -ForegroundColor Green
            foreach ($endpoint in $result.Endpoints) {
                Write-Host "     🔗 $endpoint" -ForegroundColor Cyan
            }
        }
    }
    
    if ($FailedDeployments.Count -gt 0) {
        Write-Host ""
        Write-Error "FAILED DEPLOYMENTS:"
        foreach ($failure in $FailedDeployments) {
            Write-Host "   • $($failure.Service) ($([math]::Round($failure.Duration, 1))s)" -ForegroundColor Red
            $errorPreview = if ($failure.Error.Length -gt 100) { $failure.Error.Substring(0, 100) + "..." } else { $failure.Error }
            Write-Host "     💥 $errorPreview" -ForegroundColor Red
        }
    }
    
    # Overall status
    Write-Host ""
    if ($CriticalFailures -eq 0) {
        Write-Host "🎉 DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
        Write-Host "   All critical services deployed successfully" -ForegroundColor Green
        if ($FailedDeployments.Count -gt 0) {
            Write-Warning "   $($FailedDeployments.Count) non-critical services failed"
        }
    } else {
        Write-Host "🚨 DEPLOYMENT FAILED!" -ForegroundColor Red
        Write-Host "   $CriticalFailures critical services failed" -ForegroundColor Red
    }
    
    Write-Host ("=" * 80)
}

# Main execution
try {
    Write-Host "🚀 Complete Platform Deployment Script" -ForegroundColor Blue
    Write-Host "Stage: $Stage" -ForegroundColor Blue
    Write-Host ""
    
    # Validate prerequisites
    if (-not $SkipValidation) {
        if (-not (Test-Prerequisites)) {
            Write-Error "Prerequisites validation failed"
            exit 1
        }
    }
    
    # Deploy all services
    $success = Deploy-All
    
    if ($success) {
        Write-Host ""
        Write-Host "🎉 Platform deployment completed successfully!" -ForegroundColor Green
        exit 0
    } else {
        Write-Host ""
        Write-Host "🚨 Platform deployment failed!" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Error "Deployment script failed: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace -ForegroundColor Red
    exit 1
}
