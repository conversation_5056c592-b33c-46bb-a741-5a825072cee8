# scripts/deploy-simple.ps1
# Deployment coordinado simplificado para FASE 1

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Stage
)

$ErrorActionPreference = "Stop"
$OriginalLocation = Get-Location

function Write-Success { param($Message) Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "[INFO] $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "[ERROR] $Message" -ForegroundColor Red }

function Deploy-Service {
    param(
        [string]$ServicePath,
        [string]$ServiceName,
        [string]$Stage
    )
    
    Write-Info "Deploying $ServiceName to $Stage..."
    
    try {
        Set-Location $ServicePath
        sls deploy --stage=$Stage
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$ServiceName deployed successfully"
            return $true
        } else {
            Write-Error "$ServiceName deployment failed"
            return $false
        }
    }
    catch {
        Write-Error "$ServiceName deployment failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location $OriginalLocation
    }
}

Write-Info "Starting coordinated deployment to $Stage environment"

$deploymentResults = @{}
$totalServices = 0
$successfulServices = 0

# 1. Shared Layer
$totalServices++
Write-Info "PHASE 1: Deploying Shared Layer"
if (Deploy-Service "shared" "Shared Layer" $Stage) {
    $deploymentResults["SharedLayer"] = "SUCCESS"
    $successfulServices++
    Write-Info "Waiting for shared layer to be available..."
    Start-Sleep -Seconds 30
} else {
    $deploymentResults["SharedLayer"] = "FAILED"
    Write-Error "Shared Layer deployment failed. Stopping deployment."
    exit 1
}

# 2. Infrastructure
$totalServices++
Write-Info "PHASE 2: Deploying Infrastructure"
if (Deploy-Service "." "Infrastructure" $Stage) {
    $deploymentResults["Infrastructure"] = "SUCCESS"
    $successfulServices++
    Write-Info "Waiting for infrastructure to be ready..."
    Start-Sleep -Seconds 45
} else {
    $deploymentResults["Infrastructure"] = "FAILED"
    Write-Warning "Infrastructure deployment failed. Continuing with services..."
}

# 3. Auth Service
$totalServices++
Write-Info "PHASE 3: Deploying Auth Service"
if (Deploy-Service "services/auth" "Auth Service" $Stage) {
    $deploymentResults["AuthService"] = "SUCCESS"
    $successfulServices++
    Write-Info "Waiting for JWT Authorizer to be available..."
    Start-Sleep -Seconds 30
} else {
    $deploymentResults["AuthService"] = "FAILED"
    Write-Error "Auth Service deployment failed. JWT Authorizer may not be available."
}

# 4. Core Services
Write-Info "PHASE 4: Deploying Core Services"
$coreServices = @(
    @{Path="services/payment"; Name="Payment Service"},
    @{Path="services/tenant"; Name="Tenant Service"},
    @{Path="services/user"; Name="User Service"}
)

foreach ($service in $coreServices) {
    $totalServices++
    if (Deploy-Service $service.Path $service.Name $Stage) {
        $deploymentResults[$service.Name] = "SUCCESS"
        $successfulServices++
    } else {
        $deploymentResults[$service.Name] = "FAILED"
    }
}

# 5. Support Services
Write-Info "PHASE 5: Deploying Support Services"
$supportServices = @(
    @{Path="services/admin"; Name="Admin Service"},
    @{Path="services/events"; Name="Events Service"},
    @{Path="services/security"; Name="Security Service"}
)

foreach ($service in $supportServices) {
    $totalServices++
    if (Deploy-Service $service.Path $service.Name $Stage) {
        $deploymentResults[$service.Name] = "SUCCESS"
        $successfulServices++
    } else {
        $deploymentResults[$service.Name] = "FAILED"
    }
}

# 6. Utility Services
Write-Info "PHASE 6: Deploying Utility Services"
$utilityServices = @(
    @{Path="services/jobs"; Name="Jobs Service"},
    @{Path="services/orchestrator"; Name="Orchestrator Service"},
    @{Path="services/setup"; Name="Setup Service"}
)

foreach ($service in $utilityServices) {
    $totalServices++
    if (Deploy-Service $service.Path $service.Name $Stage) {
        $deploymentResults[$service.Name] = "SUCCESS"
        $successfulServices++
    } else {
        $deploymentResults[$service.Name] = "FAILED"
    }
}

# Resumen final
Write-Info "DEPLOYMENT SUMMARY"
Write-Info "=================="
Write-Info "Total Services: $totalServices"
Write-Success "Successful: $successfulServices"
Write-Error "Failed: $($totalServices - $successfulServices)"
Write-Info "Success Rate: $([math]::Round(($successfulServices / $totalServices) * 100, 2))%"

Write-Info ""
Write-Info "Detailed Results:"
foreach ($result in $deploymentResults.GetEnumerator()) {
    if ($result.Value -eq "SUCCESS") {
        Write-Success "  $($result.Key): $($result.Value)"
    } else {
        Write-Error "  $($result.Key): $($result.Value)"
    }
}

if ($successfulServices -eq $totalServices) {
    Write-Success "All services deployed successfully!"
    exit 0
} elseif ($successfulServices -ge ($totalServices * 0.8)) {
    Write-Warning "Most services deployed successfully. Check failed services."
    exit 0
} else {
    Write-Error "Deployment failed. Too many services failed to deploy."
    exit 1
}
