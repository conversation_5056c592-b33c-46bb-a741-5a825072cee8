#!/usr/bin/env python3
"""
Script to temporarily disable reserved concurrency in all serverless.yml files
due to AWS account concurrency limit being too low (10 total).
"""

import os
import re
from pathlib import Path


def disable_reserved_concurrency_in_file(file_path):
    """Disable reserved concurrency in a single serverless.yml file."""
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match reservedConcurrency lines
    pattern = r'^(\s+)reservedConcurrency:\s*(.+)$'
    
    # Replace with commented version
    def replace_func(match):
        indent = match.group(1)
        value = match.group(2)
        return f"{indent}# reservedConcurrency: {value}  # DISABLED - Account limit too low"
    
    new_content = re.sub(pattern, replace_func, content, flags=re.MULTILINE)
    
    # Count changes
    changes = len(re.findall(pattern, content, flags=re.MULTILINE))
    
    if changes > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✅ Disabled {changes} reservedConcurrency configurations")
    else:
        print(f"  ℹ️ No reservedConcurrency configurations found")
    
    return changes


def main():
    """Main function to process all serverless.yml files."""
    project_root = Path(__file__).parent.parent
    total_changes = 0
    
    print("🔧 Disabling reserved concurrency due to AWS account limit...")
    print("=" * 60)
    
    # Find all serverless.yml files
    serverless_files = []
    
    # Services
    services_dir = project_root / "services"
    if services_dir.exists():
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir():
                serverless_file = service_dir / "serverless.yml"
                if serverless_file.exists():
                    serverless_files.append(serverless_file)
    
    # Root serverless.yml
    root_serverless = project_root / "serverless.yml"
    if root_serverless.exists():
        serverless_files.append(root_serverless)
    
    # Shared serverless.yml
    shared_serverless = project_root / "shared" / "serverless.yml"
    if shared_serverless.exists():
        serverless_files.append(shared_serverless)
    
    # Process each file
    for file_path in serverless_files:
        changes = disable_reserved_concurrency_in_file(file_path)
        total_changes += changes
    
    print("=" * 60)
    print(f"✅ Total changes: {total_changes} reservedConcurrency configurations disabled")
    print()
    print("📋 Next steps:")
    print("1. Deploy services with disabled concurrency")
    print("2. Wait for AWS to increase account concurrency limit")
    print("3. Re-enable reservedConcurrency configurations")
    print()
    print("💡 To re-enable later, run: scripts/enable-reserved-concurrency.py")


if __name__ == "__main__":
    main()
