# scripts/comment-serverless-deps.ps1
# Script para comentar shared layers y authorizers en todas las plantillas serverless

Write-Host "Comentando shared layers y authorizers en plantillas serverless..." -ForegroundColor Yellow

# Buscar todos los archivos serverless.yml
$serverlessFiles = Get-ChildItem -Path "." -Name "serverless.yml" -Recurse

Write-Host "Archivos serverless.yml encontrados:" -ForegroundColor Blue
foreach ($file in $serverlessFiles) {
    Write-Host "  - $file" -ForegroundColor Gray
}

Write-Host ""

# Procesar cada archivo
foreach ($file in $serverlessFiles) {
    Write-Host "Procesando: $file" -ForegroundColor Yellow

    $content = Get-Content $file
    $modified = $false

    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]

        # Comentar shared layers
        if (($line -match "^\s*layers:\s*$" -or $line -match "^\s*- \$\{self:custom\.sharedLayerArn\}") -and $line -notmatch "^\s*#") {
            $content[$i] = "#" + $line
            $modified = $true
            Write-Host "  Comentado: $($line.Trim())" -ForegroundColor Green
        }

        # Comentar authorizers
        if (($line -match "^\s*authorizer:\s*$" -or
             $line -match "^\s*name: jwtAuthorizer\s*$" -or
             $line -match "^\s*type: request\s*$" -or
             $line -match "^\s*arn:\s*$" -or
             $line -match "^\s*Fn::ImportValue:.*JwtAuthorizer") -and $line -notmatch "^\s*#") {
            $content[$i] = "#" + $line
            $modified = $true
            Write-Host "  Comentado: $($line.Trim())" -ForegroundColor Green
        }
    }

    if ($modified) {
        $content | Set-Content $file -Encoding UTF8
        Write-Host "  Archivo modificado: $file" -ForegroundColor Cyan
    }

    Write-Host ""
}

Write-Host "Proceso completado!" -ForegroundColor Green
