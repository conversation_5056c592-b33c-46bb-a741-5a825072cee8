#!/usr/bin/env python3
"""
Script de validación de dependencias para Agent SCL Platform
Verifica que todas las referencias entre servicios estén correctamente configuradas
"""

import yaml
import re
import sys
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple

class DependencyValidator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.stage = "dev"  # Configurable
        self.project_name = "agent-scl"
        
        # Archivos a validar
        self.infrastructure_file = self.project_root / "serverless.yml"
        self.service_files = [
            self.project_root / "serverless/services/auth/serverless.yml",
            self.project_root / "serverless/services/payment/serverless.yml", 
            self.project_root / "serverless/services/tenant/serverless.yml",
            self.project_root / "serverless/services/user/serverless.yml"
        ]
        
        # Exports esperados de la infraestructura (nombres reales)
        self.expected_exports = {
            # Desde serverless.yml (outputs)
            f"{self.project_name}-{self.stage}-DynamoDBTableName",
            f"{self.project_name}-{self.stage}-S3BucketName",  # Nombre real
            f"{self.project_name}-{self.stage}-ApiGatewayRestApiId",  # Nombre real
            f"{self.project_name}-{self.stage}-ApiGatewayRootResourceId",  # Nombre real

            # Desde s3.yml
            f"{self.project_name}-{self.stage}-PlatformDataBucketName",

            # Desde iam.yml
            f"{self.project_name}-{self.stage}-LambdaExecutionRoleArn",

            # Desde secrets.yml
            f"{self.project_name}-{self.stage}-JWTSecretArn",
            f"{self.project_name}-{self.stage}-DatabaseSecretArn",
            f"{self.project_name}-{self.stage}-StripeSecretArn",
            f"{self.project_name}-{self.stage}-ApplicationSecretArn",

            # Desde vpc.yml
            f"{self.project_name}-{self.stage}-LambdaSecurityGroupId",
            f"{self.project_name}-{self.stage}-PrivateSubnetId1",
            f"{self.project_name}-{self.stage}-PrivateSubnetId2"
        }
        
        # Export del servicio auth
        self.auth_exports = {
            f"{self.project_name}-{self.stage}-JwtAuthorizerArn"
        }

    def load_yaml_file(self, file_path: Path) -> Dict:
        """Carga un archivo YAML"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ Error loading {file_path}: {e}")
            return {}

    def extract_exports(self, config: Dict) -> Set[str]:
        """Extrae los exports de un archivo de configuración"""
        exports = set()

        # Buscar en resources.Outputs
        if 'resources' in config and 'Outputs' in config['resources']:
            for output_name, output_def in config['resources']['Outputs'].items():
                if 'Export' in output_def and 'Name' in output_def['Export']:
                    export_name = output_def['Export']['Name']
                    # Resolver variables básicas
                    export_name = export_name.replace('${self:custom.projectName}', self.project_name)
                    export_name = export_name.replace('${self:custom.stage}', self.stage)
                    exports.add(export_name)

        # Buscar en outputs (nivel raíz)
        if 'outputs' in config:
            for output_name, output_def in config['outputs'].items():
                if 'Export' in output_def and 'Name' in output_def['Export']:
                    export_name = output_def['Export']['Name']
                    export_name = export_name.replace('${self:custom.projectName}', self.project_name)
                    export_name = export_name.replace('${self:custom.stage}', self.stage)
                    exports.add(export_name)

        # Buscar en Outputs (nivel raíz, para archivos de recursos)
        if 'Outputs' in config and config['Outputs'] is not None:
            for output_name, output_def in config['Outputs'].items():
                if 'Export' in output_def and 'Name' in output_def['Export']:
                    export_name = output_def['Export']['Name']
                    # Resolver variables básicas
                    export_name = export_name.replace('${self:custom.projectName}', self.project_name)
                    export_name = export_name.replace('${self:custom.stage}', self.stage)
                    exports.add(export_name)

        return exports

    def extract_imports(self, config: Dict) -> Set[str]:
        """Extrae las referencias Fn::ImportValue de un archivo"""
        imports = set()
        config_str = yaml.dump(config)
        
        # Buscar patrones Fn::ImportValue
        import_pattern = r'Fn::ImportValue:\s*([^\s\n]+)'
        matches = re.findall(import_pattern, config_str)
        
        for match in matches:
            import_name = match.strip()
            # Resolver variables básicas
            import_name = import_name.replace('${self:custom.projectName}', self.project_name)
            import_name = import_name.replace('${self:custom.stage}', self.stage)
            imports.add(import_name)
        
        return imports

    def validate_syntax(self) -> bool:
        """Valida la sintaxis YAML de todos los archivos"""
        print("🔍 Validando sintaxis YAML...")
        all_valid = True
        
        files_to_check = [self.infrastructure_file] + self.service_files
        
        for file_path in files_to_check:
            if not file_path.exists():
                print(f"⚠️  Archivo no encontrado: {file_path}")
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
                print(f"✅ {file_path.name}: Sintaxis válida")
            except yaml.YAMLError as e:
                print(f"❌ {file_path.name}: Error de sintaxis - {e}")
                all_valid = False
            except Exception as e:
                print(f"❌ {file_path.name}: Error - {e}")
                all_valid = False
        
        return all_valid

    def validate_infrastructure_exports(self) -> Tuple[bool, Set[str]]:
        """Valida que la infraestructura exporte todos los recursos necesarios"""
        print("\n🏗️  Validando exports de infraestructura...")

        if not self.infrastructure_file.exists():
            print(f"❌ Archivo de infraestructura no encontrado: {self.infrastructure_file}")
            return False, set()

        # Extraer exports del archivo principal
        config = self.load_yaml_file(self.infrastructure_file)
        actual_exports = self.extract_exports(config)

        # También extraer exports de archivos de recursos
        resource_files = [
            self.project_root / "serverless/resources/dynamodb.yml",
            self.project_root / "serverless/resources/s3.yml",
            self.project_root / "serverless/resources/iam.yml",
            self.project_root / "serverless/resources/api-gateway.yml",
            self.project_root / "serverless/resources/secrets.yml",
            self.project_root / "serverless/resources/vpc.yml",
            self.project_root / "serverless/resources/monitoring.yml"
        ]

        for resource_file in resource_files:
            if resource_file.exists():
                print(f"   📄 Leyendo {resource_file.name}...")
                resource_config = self.load_yaml_file(resource_file)
                resource_exports = self.extract_exports(resource_config)
                if resource_exports:
                    print(f"      ✅ Encontrados {len(resource_exports)} exports")
                    for export in sorted(resource_exports):
                        print(f"         - {export}")
                actual_exports.update(resource_exports)
            else:
                print(f"   ⚠️  Archivo no encontrado: {resource_file}")

        missing_exports = self.expected_exports - actual_exports
        extra_exports = actual_exports - self.expected_exports

        if not missing_exports:
            print("✅ Todos los exports requeridos están presentes")
        else:
            print("❌ Exports faltantes:")
            for export in sorted(missing_exports):
                print(f"   - {export}")

        if extra_exports:
            print("ℹ️  Exports adicionales encontrados:")
            for export in sorted(extra_exports):
                print(f"   - {export}")

        return len(missing_exports) == 0, actual_exports

    def validate_auth_exports(self) -> Tuple[bool, Set[str]]:
        """Valida que el servicio auth exporte el autorizador JWT"""
        print("\n🔒 Validando exports del servicio auth...")
        
        auth_file = self.project_root / "serverless/services/auth/serverless.yml"
        if not auth_file.exists():
            print(f"❌ Archivo del servicio auth no encontrado: {auth_file}")
            return False, set()
        
        config = self.load_yaml_file(auth_file)
        actual_exports = self.extract_exports(config)
        
        missing_exports = self.auth_exports - actual_exports
        
        if not missing_exports:
            print("✅ Export del autorizador JWT presente")
        else:
            print("❌ Exports faltantes del servicio auth:")
            for export in sorted(missing_exports):
                print(f"   - {export}")
        
        return len(missing_exports) == 0, actual_exports

    def validate_service_dependencies(self, available_exports: Set[str]) -> bool:
        """Valida que todos los servicios tengan sus dependencias satisfechas"""
        print("\n🔗 Validando dependencias de servicios...")
        
        all_valid = True
        
        for service_file in self.service_files:
            if not service_file.exists():
                print(f"⚠️  Servicio no encontrado: {service_file}")
                continue
            
            service_name = service_file.parent.name
            print(f"\n📋 Validando servicio: {service_name}")
            
            config = self.load_yaml_file(service_file)
            required_imports = self.extract_imports(config)
            
            missing_imports = required_imports - available_exports
            
            if not missing_imports:
                print(f"✅ {service_name}: Todas las dependencias satisfechas")
            else:
                print(f"❌ {service_name}: Dependencias faltantes:")
                for import_name in sorted(missing_imports):
                    print(f"   - {import_name}")
                all_valid = False
        
        return all_valid

    def validate_naming_conventions(self) -> bool:
        """Valida que se sigan las convenciones de nomenclatura"""
        print("\n📝 Validando convenciones de nomenclatura...")
        
        all_valid = True
        files_to_check = [self.infrastructure_file] + self.service_files
        
        for file_path in files_to_check:
            if not file_path.exists():
                continue
                
            config = self.load_yaml_file(file_path)
            
            # Validar recursos
            if 'resources' in config and 'Resources' in config['resources']:
                for resource_name in config['resources']['Resources'].keys():
                    if not re.match(r'^[A-Z][a-zA-Z0-9]*$', resource_name):
                        print(f"❌ {file_path.name}: Recurso '{resource_name}' no sigue PascalCase")
                        all_valid = False
            
            # Validar outputs
            if 'resources' in config and 'Outputs' in config['resources']:
                for output_name in config['resources']['Outputs'].keys():
                    if not re.match(r'^[A-Z][a-zA-Z0-9]*$', output_name):
                        print(f"❌ {file_path.name}: Output '{output_name}' no sigue PascalCase")
                        all_valid = False
        
        if all_valid:
            print("✅ Todas las convenciones de nomenclatura son correctas")
        
        return all_valid

    def run_validation(self) -> bool:
        """Ejecuta todas las validaciones"""
        print("🚀 Iniciando validación de dependencias para Agent SCL Platform")
        print("=" * 60)
        
        # 1. Validar sintaxis
        syntax_valid = self.validate_syntax()
        
        # 2. Validar exports de infraestructura
        infra_valid, infra_exports = self.validate_infrastructure_exports()
        
        # 3. Validar exports de auth
        auth_valid, auth_exports = self.validate_auth_exports()
        
        # 4. Combinar todos los exports disponibles
        all_exports = infra_exports | auth_exports
        
        # 5. Validar dependencias de servicios
        deps_valid = self.validate_service_dependencies(all_exports)
        
        # 6. Validar convenciones de nomenclatura
        naming_valid = self.validate_naming_conventions()
        
        # Resultado final
        print("\n" + "=" * 60)
        all_valid = syntax_valid and infra_valid and auth_valid and deps_valid and naming_valid
        
        if all_valid:
            print("🎉 ¡Todas las validaciones pasaron exitosamente!")
            print("✅ La infraestructura está lista para el despliegue")
        else:
            print("❌ Se encontraron problemas que deben corregirse antes del despliegue")
        
        return all_valid

def main():
    """Función principal"""
    validator = DependencyValidator()
    success = validator.run_validation()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
