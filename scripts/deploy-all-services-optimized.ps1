# Deploy all services with optimized shared layer and JWT authorizer
Write-Host "🚀 Deploying all services with optimizations..." -ForegroundColor Green

$services = @("user", "admin", "payment", "tenant", "events", "security")
$results = @()
$totalStartTime = Get-Date

foreach ($service in $services) {
    Write-Host "`n📦 Deploying $service service with optimizations..." -ForegroundColor Yellow
    
    Set-Location "services\$service"
    
    $startTime = Get-Date
    try {
        $output = & serverless deploy --stage dev --region us-east-1 2>&1
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $service service deployed successfully in $([math]::Round($duration))s" -ForegroundColor Green
            $results += @{
                Service = $service
                Status = "SUCCESS"
                Duration = $duration
                Message = "Deployed with optimizations"
            }
        } else {
            Write-Host "❌ $service service deployment failed" -ForegroundColor Red
            Write-Host "Error output: $output" -ForegroundColor Red
            $results += @{
                Service = $service
                Status = "FAILED"
                Duration = $duration
                Message = "Deployment failed"
            }
        }
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        Write-Host "❌ $service service deployment error: $_" -ForegroundColor Red
        $results += @{
            Service = $service
            Status = "ERROR"
            Duration = $duration
            Message = $_.Exception.Message
        }
    }
    
    Set-Location "..\..\"
}

$totalEndTime = Get-Date
$totalDuration = ($totalEndTime - $totalStartTime).TotalSeconds

Write-Host "`n🎉 DEPLOYMENT COMPLETE!" -ForegroundColor Green
Write-Host "=" * 60
Write-Host "📊 Final Deployment Summary:" -ForegroundColor Cyan
Write-Host "=" * 60

$successCount = 0
$failCount = 0

foreach ($result in $results) {
    if ($result.Status -eq "SUCCESS") {
        $status = "✅"
        $successCount++
    } else {
        $status = "❌"
        $failCount++
    }
    Write-Host "$status $($result.Service): $($result.Status) ($([math]::Round($result.Duration))s) - $($result.Message)"
}

Write-Host "`n📈 Statistics:" -ForegroundColor Yellow
Write-Host "✅ Successful deployments: $successCount"
Write-Host "❌ Failed deployments: $failCount"
Write-Host "⏱️ Total deployment time: $([math]::Round($totalDuration))s"
Write-Host "🎯 Success rate: $([math]::Round(($successCount / ($successCount + $failCount)) * 100))%"

if ($failCount -eq 0) {
    Write-Host "`n🎉 ALL SERVICES DEPLOYED SUCCESSFULLY WITH OPTIMIZATIONS!" -ForegroundColor Green
    Write-Host "🚀 Platform Agent SCL is now fully optimized and operational!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ Some deployments failed. Please check the errors above." -ForegroundColor Yellow
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Validate all services are using optimized shared layer"
Write-Host "2. Test JWT authorization across services"
Write-Host "3. Monitor performance improvements"
Write-Host "4. Re-enable reserved concurrency when AWS approves limit increase"
