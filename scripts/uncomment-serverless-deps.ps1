# scripts/uncomment-serverless-deps.ps1
# Script para descomentar shared layers y authorizers en todas las plantillas serverless

Write-Host "Descomentando shared layers y authorizers en plantillas serverless..." -ForegroundColor Yellow

# Buscar todos los archivos serverless.yml
$serverlessFiles = Get-ChildItem -Path "." -Name "serverless.yml" -Recurse

Write-Host "Archivos serverless.yml encontrados:" -ForegroundColor Blue
foreach ($file in $serverlessFiles) {
    Write-Host "  - $file" -ForegroundColor Gray
}

Write-Host ""

# Procesar cada archivo
foreach ($file in $serverlessFiles) {
    Write-Host "Procesando: $file" -ForegroundColor Yellow

    $content = Get-Content $file
    $modified = $false

    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]

        # Descomentar líneas que empiecen con #
        if ($line -match "^\s*#(.*)$") {
            $uncommentedLine = $matches[1]

            # Solo descomentar si es una línea de shared layer o authorizer
            if ($uncommentedLine -match "^\s*layers:\s*$" -or
                $uncommentedLine -match "^\s*- \$\{self:custom\.sharedLayerArn\}" -or
                $uncommentedLine -match "^\s*authorizer:\s*$" -or
                $uncommentedLine -match "^\s*name: jwtAuthorizer\s*$" -or
                $uncommentedLine -match "^\s*type: request\s*$" -or
                $uncommentedLine -match "^\s*arn:\s*$" -or
                $uncommentedLine -match "^\s*Fn::ImportValue:.*JwtAuthorizer") {

                $content[$i] = $uncommentedLine
                $modified = $true
                Write-Host "  Descomentado: $($uncommentedLine.Trim())" -ForegroundColor Green
            }
        }
    }

    if ($modified) {
        $content | Set-Content $file -Encoding UTF8
        Write-Host "  Archivo modificado: $file" -ForegroundColor Cyan
    }

    Write-Host ""
}

Write-Host "Proceso completado!" -ForegroundColor Green
