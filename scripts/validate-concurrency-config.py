#!/usr/bin/env python3
"""
Script to validate concurrency configurations across all services.
Checks for optimal settings and potential issues.
"""

import os
import yaml
import sys
from pathlib import Path
from typing import Dict, List, Any


class ConcurrencyValidator:
    """Validates concurrency configurations."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.issues = []
        self.recommendations = []
    
    def validate_all_configurations(self):
        """Validate all concurrency configurations."""
        print("🔍 Validating concurrency configurations...")
        
        # Validate shared variables
        self.validate_shared_variables()
        
        # Validate service configurations
        self.validate_service_configurations()
        
        # Generate report
        self.generate_report()
    
    def validate_shared_variables(self):
        """Validate shared variables configuration."""
        print("📋 Validating shared variables...")
        
        variables_file = self.project_root / "serverless/shared/variables.yml"
        
        if not variables_file.exists():
            self.issues.append("❌ Shared variables file not found")
            return
        
        with open(variables_file, 'r') as f:
            config = yaml.safe_load(f)
        
        stages = config.get('stages', {})
        
        for stage_name, stage_config in stages.items():
            print(f"  🔍 Validating stage: {stage_name}")
            
            lambda_config = stage_config.get('lambda', {})
            profiles = lambda_config.get('profiles', {})
            
            # Validate profiles exist
            required_profiles = ['ultra-quick', 'quick', 'standard', 'heavy', 'critical', 'background', 'admin']
            for profile in required_profiles:
                if profile not in profiles:
                    self.issues.append(f"❌ Missing profile '{profile}' in stage '{stage_name}'")
                else:
                    self.validate_profile(stage_name, profile, profiles[profile])
    
    def validate_profile(self, stage: str, profile_name: str, profile_config: Dict[str, Any]):
        """Validate individual profile configuration."""
        required_fields = ['timeout', 'memorySize', 'reservedConcurrency']
        
        for field in required_fields:
            if field not in profile_config:
                self.issues.append(f"❌ Missing '{field}' in profile '{profile_name}' for stage '{stage}'")
        
        # Validate concurrency values
        concurrency = profile_config.get('reservedConcurrency', 0)
        
        # Profile-specific validations
        if profile_name == 'critical' and concurrency < 50:
            self.issues.append(f"⚠️ Critical profile has low concurrency ({concurrency}) in stage '{stage}'")
        
        if profile_name == 'quick' and concurrency < 10:
            self.issues.append(f"⚠️ Quick profile has very low concurrency ({concurrency}) in stage '{stage}'")
        
        if profile_name == 'background' and concurrency > 10:
            self.recommendations.append(f"💡 Background profile might have excessive concurrency ({concurrency}) in stage '{stage}'")
    
    def validate_service_configurations(self):
        """Validate service-specific configurations."""
        print("🔍 Validating service configurations...")
        
        services_dir = self.project_root / "services"
        
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir():
                self.validate_service(service_dir.name)
    
    def validate_service(self, service_name: str):
        """Validate individual service configuration."""
        print(f"  📋 Validating service: {service_name}")
        
        service_file = self.project_root / f"services/{service_name}/serverless.yml"
        
        if not service_file.exists():
            self.issues.append(f"❌ Service file not found: {service_name}")
            return
        
        with open(service_file, 'r') as f:
            try:
                config = yaml.safe_load(f)
            except yaml.YAMLError as e:
                self.issues.append(f"❌ Invalid YAML in {service_name}: {str(e)}")
                return
        
        functions = config.get('functions', {})
        
        for function_name, function_config in functions.items():
            self.validate_function(service_name, function_name, function_config)
    
    def validate_function(self, service_name: str, function_name: str, function_config: Dict[str, Any]):
        """Validate individual function configuration."""
        
        # Check if reservedConcurrency is configured
        if 'reservedConcurrency' not in function_config:
            # Check if it's using a profile
            if not any(key.startswith('${self:custom.stageConfig.lambda.profiles.') 
                      for key in str(function_config.get('timeout', '')).split()):
                self.issues.append(f"⚠️ Function {service_name}.{function_name} has no concurrency configuration")
        
        # Validate critical functions have appropriate concurrency
        if service_name == 'auth':
            if function_name in ['login', 'register', 'refreshToken']:
                if not self.uses_high_concurrency_profile(function_config):
                    self.issues.append(f"❌ Critical auth function {function_name} should use 'quick' or 'critical' profile")
            
            if function_name == 'jwtAuthorizer':
                if not self.uses_critical_profile(function_config):
                    self.issues.append(f"❌ JWT Authorizer should use 'critical' profile")
        
        # Check for hardcoded values that should use profiles
        if isinstance(function_config.get('timeout'), int):
            self.recommendations.append(f"💡 Function {service_name}.{function_name} uses hardcoded timeout, consider using profiles")
        
        if isinstance(function_config.get('memorySize'), int):
            self.recommendations.append(f"💡 Function {service_name}.{function_name} uses hardcoded memorySize, consider using profiles")
    
    def uses_high_concurrency_profile(self, function_config: Dict[str, Any]) -> bool:
        """Check if function uses high concurrency profile."""
        concurrency_config = str(function_config.get('reservedConcurrency', ''))
        return any(profile in concurrency_config for profile in ['quick', 'critical'])
    
    def uses_critical_profile(self, function_config: Dict[str, Any]) -> bool:
        """Check if function uses critical profile."""
        concurrency_config = str(function_config.get('reservedConcurrency', ''))
        return 'critical' in concurrency_config
    
    def generate_report(self):
        """Generate validation report."""
        print("\n" + "="*60)
        print("📊 CONCURRENCY CONFIGURATION VALIDATION REPORT")
        print("="*60)
        
        if not self.issues and not self.recommendations:
            print("✅ All concurrency configurations are optimal!")
            return
        
        if self.issues:
            print(f"\n🚨 ISSUES FOUND ({len(self.issues)}):")
            for issue in self.issues:
                print(f"  {issue}")
        
        if self.recommendations:
            print(f"\n💡 RECOMMENDATIONS ({len(self.recommendations)}):")
            for recommendation in self.recommendations:
                print(f"  {recommendation}")
        
        print(f"\n📈 SUMMARY:")
        print(f"  Issues: {len(self.issues)}")
        print(f"  Recommendations: {len(self.recommendations)}")
        
        if self.issues:
            print(f"\n⚠️ Please address the issues above for optimal performance.")
            sys.exit(1)
        else:
            print(f"\n✅ No critical issues found. Consider implementing recommendations.")


def main():
    """Main validation function."""
    validator = ConcurrencyValidator()
    validator.validate_all_configurations()


if __name__ == "__main__":
    main()
