#!/usr/bin/env python3
"""
Script to re-enable shared layer references in all services
after coordinated deployment strategy.
"""

import os
import re
from pathlib import Path


def enable_shared_layer_in_file(file_path):
    """Re-enable shared layer references in a single serverless.yml file."""
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    changes = 0
    
    # Pattern 1: Uncomment sharedLayerArn definition
    pattern1 = r'^(\s+)# sharedLayerArn:\s*# DISABLED - Coordinated deployment$'
    def replace1(match):
        indent = match.group(1)
        return f"{indent}sharedLayerArn:"
    
    new_content = re.sub(pattern1, replace1, content, flags=re.MULTILINE)
    if new_content != content:
        changes += 1
        content = new_content
    
    # Pattern 2: Uncomment Fn::ImportValue line
    pattern2 = r'^(\s+)# \s*Fn::ImportValue:\s*sls-.*-SharedLayerLambdaLayerQualifiedArn\s*# DISABLED - Coordinated deployment$'
    def replace2(match):
        indent = match.group(1)
        return f"{indent}Fn::ImportValue: sls-${{self:custom.projectName}}-shared-layer-${{self:custom.stage}}-SharedLayerLambdaLayerQualifiedArn"
    
    new_content = re.sub(pattern2, replace2, content, flags=re.MULTILINE)
    if new_content != content:
        changes += 1
        content = new_content
    
    # Pattern 3: Uncomment layers sections
    pattern3 = r'^(\s+)# layers:\s*# DISABLED - Coordinated deployment$'
    def replace3(match):
        indent = match.group(1)
        return f"{indent}layers:"
    
    new_content = re.sub(pattern3, replace3, content, flags=re.MULTILINE)
    if new_content != content:
        changes += 1
        content = new_content
    
    # Pattern 4: Uncomment layer references
    pattern4 = r'^(\s+)# \s*- \$\{self:custom\.sharedLayerArn\}\s*# DISABLED - Coordinated deployment$'
    def replace4(match):
        indent = match.group(1)
        return f"{indent}- ${{self:custom.sharedLayerArn}}"
    
    new_content = re.sub(pattern4, replace4, content, flags=re.MULTILINE)
    if new_content != content:
        changes += 1
        content = new_content
    
    if changes > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✅ Re-enabled {changes} shared layer references")
    else:
        print(f"  ℹ️ No disabled shared layer references found")
    
    return changes


def main():
    """Main function to process all serverless.yml files."""
    project_root = Path(__file__).parent.parent
    total_changes = 0
    
    print("🚀 Re-enabling shared layer references after coordinated deployment...")
    print("=" * 70)
    
    # Find all serverless.yml files in services
    services_dir = project_root / "services"
    serverless_files = []
    
    if services_dir.exists():
        for service_dir in services_dir.iterdir():
            if service_dir.is_dir():
                serverless_file = service_dir / "serverless.yml"
                if serverless_file.exists():
                    serverless_files.append(serverless_file)
    
    # Process each file
    for file_path in serverless_files:
        changes = enable_shared_layer_in_file(file_path)
        total_changes += changes
    
    print("=" * 70)
    print(f"✅ Total changes: {total_changes} shared layer references re-enabled")
    print()
    print("📋 Next steps:")
    print("1. Re-deploy all services with optimized shared layer")
    print("2. Validate that optimizations are working")
    print("3. Test performance improvements")


if __name__ == "__main__":
    main()
