#!/bin/bash
# scripts/setup-environment.sh
# Implementado según "Infrastructure as Code Configuration"

set -e

ENVIRONMENT=${1:-dev}
AWS_REGION=${2:-us-east-1}
PROJECT_NAME="platform"

echo "Setting up environment: $ENVIRONMENT in region: $AWS_REGION"

# Validate environment
case $ENVIRONMENT in
    dev|staging|prod)
        echo "✅ Valid environment: $ENVIRONMENT"
        ;;
    *)
        echo "❌ Invalid environment. Must be dev, staging, or prod"
        exit 1
        ;;
esac

# Load environment-specific configuration
ENV_CONFIG_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_CONFIG_FILE" ]; then
    echo "❌ Environment configuration file not found: $ENV_CONFIG_FILE"
    echo "Creating template configuration file..."
    cat > "$ENV_CONFIG_FILE" << EOF
# Environment configuration for $ENVIRONMENT
AWS_REGION=$AWS_REGION
PROJECT_NAME=$PROJECT_NAME
ENVIRONMENT=$ENVIRONMENT

# 🚨 CONSULTA REQUERIDA: Configurar estos valores
# Ver documento "Information Gaps & Clarification Requirements"
# AWS_ACCOUNT_ID=
# DOMAIN_NAME=
# SSL_CERTIFICATE_ARN=
EOF
    echo "⚠️ Please configure $ENV_CONFIG_FILE with actual values"
fi

# Source environment variables
export $(cat $ENV_CONFIG_FILE | grep -v '^#' | xargs)

# Setup Terraform backend if not exists
TERRAFORM_BUCKET="${PROJECT_NAME}-terraform-state-${ENVIRONMENT}"
echo "🏗️ Setting up Terraform backend..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI not configured. Please run 'aws configure' first"
    exit 1
fi

# Create S3 bucket for Terraform state
echo "Creating S3 bucket: $TERRAFORM_BUCKET"
aws s3 mb s3://$TERRAFORM_BUCKET --region $AWS_REGION 2>/dev/null || echo "Bucket already exists"

# Enable versioning on state bucket
echo "Enabling versioning on state bucket..."
aws s3api put-bucket-versioning \
    --bucket $TERRAFORM_BUCKET \
    --versioning-configuration Status=Enabled

# Enable encryption on state bucket
echo "Enabling encryption on state bucket..."
aws s3api put-bucket-encryption \
    --bucket $TERRAFORM_BUCKET \
    --server-side-encryption-configuration '{
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }'

# Create DynamoDB table for state locking
echo "Creating DynamoDB table for state locking..."
aws dynamodb create-table \
    --table-name terraform-state-lock \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST \
    --region $AWS_REGION 2>/dev/null || echo "DynamoDB table already exists"

echo "✅ Environment setup completed for: $ENVIRONMENT"
echo "📋 Next steps:"
echo "   1. Configure $ENV_CONFIG_FILE with actual values"
echo "   2. Run: cd terraform/environments/$ENVIRONMENT"
echo "   3. Run: terraform init"
echo "   4. Run: terraform plan"
