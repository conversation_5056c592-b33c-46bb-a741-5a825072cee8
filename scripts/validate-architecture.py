#!/usr/bin/env python3
# scripts/validate-architecture.py
# Architecture validation script

"""
Architecture validation script for:
- Design pattern implementation
- Architectural consistency
- Dependency compliance
- Layer separation
"""

import os
import ast
import sys
import glob
from pathlib import Path
from typing import Dict, List, Set, Tuple


class ArchitectureValidator:
    """Validates architectural patterns and consistency."""
    
    def __init__(self):
        """Initialize architecture validator."""
        self.project_root = Path(__file__).parent.parent
        self.errors = []
        self.warnings = []
        self.patterns_found = {
            'dependency_injection': False,
            'repository_pattern': False,
            'decorator_pattern': False,
            'error_handling_strategy': False,
            'validation_strategy': False
        }
        
    def log_error(self, message: str):
        """Log an error."""
        self.errors.append(message)
        print(f"❌ ERROR: {message}")
    
    def log_warning(self, message: str):
        """Log a warning."""
        self.warnings.append(message)
        print(f"⚠️  WARNING: {message}")
    
    def log_success(self, message: str):
        """Log a success."""
        print(f"✅ {message}")
    
    def validate_dependency_injection_pattern(self) -> bool:
        """Validate dependency injection implementation."""
        print("🔍 Validating Dependency Injection Pattern...")
        
        # Check for IoC container
        container_file = self.project_root / "shared" / "python" / "shared" / "dependency_injection.py"
        if not container_file.exists():
            self.log_error("Dependency injection container not found")
            return False
        
        # Check for service configuration
        config_file = self.project_root / "shared" / "python" / "shared" / "service_configuration.py"
        if not config_file.exists():
            self.log_error("Service configuration not found")
            return False
        
        # Validate container features
        try:
            with open(container_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_features = [
                'ServiceContainer',
                'ServiceLifetime',
                'register_singleton',
                'register_scoped',
                'register_transient',
                'resolve'
            ]
            
            for feature in required_features:
                if feature not in content:
                    self.log_error(f"Missing DI feature: {feature}")
                else:
                    self.log_success(f"DI feature found: {feature}")
            
            self.patterns_found['dependency_injection'] = True
            return True
            
        except Exception as e:
            self.log_error(f"Error validating DI container: {e}")
            return False
    
    def validate_repository_pattern(self) -> bool:
        """Validate repository pattern implementation."""
        print("🔍 Validating Repository Pattern...")
        
        # Look for repository interfaces and implementations
        repository_files = list(glob.glob("services/**/repositories/*.py", recursive=True))
        
        if not repository_files:
            self.log_warning("No repository files found")
            return False
        
        interfaces_found = 0
        implementations_found = 0
        
        for repo_file in repository_files:
            try:
                with open(repo_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for interface pattern (abstract base class)
                if 'ABC' in content or 'abstractmethod' in content:
                    interfaces_found += 1
                    self.log_success(f"Repository interface found: {os.path.basename(repo_file)}")
                
                # Check for implementation pattern
                if 'class' in content and 'Repository' in content and 'def ' in content:
                    implementations_found += 1
                    self.log_success(f"Repository implementation found: {os.path.basename(repo_file)}")
                    
            except Exception as e:
                self.log_warning(f"Error reading repository file {repo_file}: {e}")
        
        if interfaces_found > 0 and implementations_found > 0:
            self.patterns_found['repository_pattern'] = True
            return True
        else:
            self.log_warning("Repository pattern not fully implemented")
            return False
    
    def validate_decorator_pattern(self) -> bool:
        """Validate decorator pattern implementation."""
        print("🔍 Validating Decorator Pattern...")
        
        # Check for business logic decorator
        decorator_file = self.project_root / "shared" / "python" / "shared" / "business_logic_decorator.py"
        if not decorator_file.exists():
            self.log_error("Business logic decorator not found")
            return False
        
        try:
            with open(decorator_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_decorators = [
                'business_logic_handler',
                'authenticated_handler',
                'public_handler',
                'admin_handler'
            ]
            
            for decorator in required_decorators:
                if decorator in content:
                    self.log_success(f"Decorator found: {decorator}")
                else:
                    self.log_error(f"Missing decorator: {decorator}")
            
            # Check for decorator usage in handlers
            handler_files = list(glob.glob("services/**/handlers/*.py", recursive=True))
            decorators_used = 0
            
            for handler_file in handler_files:
                if '__init__' in handler_file:
                    continue
                    
                try:
                    with open(handler_file, 'r', encoding='utf-8') as f:
                        handler_content = f.read()
                    
                    if any(dec in handler_content for dec in required_decorators):
                        decorators_used += 1
                        
                except Exception:
                    continue
            
            if decorators_used > 0:
                self.log_success(f"Decorators used in {decorators_used} handlers")
                self.patterns_found['decorator_pattern'] = True
                return True
            else:
                self.log_warning("Decorators defined but not used in handlers")
                return False
                
        except Exception as e:
            self.log_error(f"Error validating decorator pattern: {e}")
            return False
    
    def validate_error_handling_strategy(self) -> bool:
        """Validate error handling strategy implementation."""
        print("🔍 Validating Error Handling Strategy...")
        
        # Check for error handler
        error_handler_file = self.project_root / "shared" / "python" / "shared" / "error_handler.py"
        if not error_handler_file.exists():
            self.log_error("Error handler not found")
            return False
        
        try:
            with open(error_handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_components = [
                'ErrorHandler',
                'handle_exception',
                'with_error_handling',
                'ValidationException',
                'AuthenticationException'
            ]
            
            for component in required_components:
                if component in content:
                    self.log_success(f"Error handling component found: {component}")
                else:
                    self.log_error(f"Missing error handling component: {component}")
            
            self.patterns_found['error_handling_strategy'] = True
            return True
            
        except Exception as e:
            self.log_error(f"Error validating error handling strategy: {e}")
            return False
    
    def validate_validation_strategy(self) -> bool:
        """Validate validation strategy implementation."""
        print("🔍 Validating Validation Strategy...")
        
        # Check for validation manager
        validation_file = self.project_root / "shared" / "python" / "shared" / "validation_manager.py"
        if not validation_file.exists():
            self.log_error("Validation manager not found")
            return False
        
        # Check for validators
        validators_file = self.project_root / "shared" / "python" / "shared" / "validators.py"
        if not validators_file.exists():
            self.log_error("Validators module not found")
            return False
        
        try:
            with open(validation_file, 'r', encoding='utf-8') as f:
                validation_content = f.read()
            
            with open(validators_file, 'r', encoding='utf-8') as f:
                validators_content = f.read()
            
            validation_components = [
                'ValidationManager',
                'validate_request_body',
                'validate_path_parameters',
                'with_validation'
            ]
            
            for component in validation_components:
                if component in validation_content:
                    self.log_success(f"Validation component found: {component}")
                else:
                    self.log_error(f"Missing validation component: {component}")
            
            # Check for validator classes
            validator_classes = [
                'LoginRequestValidator',
                'RegisterRequestValidator',
                'ValidationResult'
            ]
            
            for validator in validator_classes:
                if validator in validators_content:
                    self.log_success(f"Validator found: {validator}")
                else:
                    self.log_warning(f"Validator not found: {validator}")
            
            self.patterns_found['validation_strategy'] = True
            return True
            
        except Exception as e:
            self.log_error(f"Error validating validation strategy: {e}")
            return False
    
    def validate_layer_separation(self) -> bool:
        """Validate proper layer separation."""
        print("🔍 Validating Layer Separation...")
        
        # Check shared layer structure
        shared_dir = self.project_root / "shared" / "python" / "shared"
        if not shared_dir.exists():
            self.log_error("Shared layer directory not found")
            return False
        
        # Check service layer structure
        services_dir = self.project_root / "services"
        if not services_dir.exists():
            self.log_error("Services directory not found")
            return False
        
        # Validate no circular dependencies between services
        service_dirs = [d for d in services_dir.iterdir() if d.is_dir()]
        
        for service_dir in service_dirs:
            service_name = service_dir.name
            
            # Check for cross-service imports
            service_files = list(glob.glob(f"{service_dir}/**/*.py", recursive=True))
            
            for file_path in service_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Look for imports from other services
                    for other_service in service_dirs:
                        if other_service.name != service_name:
                            if f"from services.{other_service.name}" in content:
                                self.log_error(f"Cross-service dependency: {service_name} -> {other_service.name}")
                                
                except Exception:
                    continue
        
        self.log_success("Layer separation validated")
        return True
    
    def run_full_validation(self) -> bool:
        """Run full architecture validation."""
        print("🏛️ Starting architecture validation...\n")
        
        validations = [
            ("Dependency Injection Pattern", self.validate_dependency_injection_pattern),
            ("Repository Pattern", self.validate_repository_pattern),
            ("Decorator Pattern", self.validate_decorator_pattern),
            ("Error Handling Strategy", self.validate_error_handling_strategy),
            ("Validation Strategy", self.validate_validation_strategy),
            ("Layer Separation", self.validate_layer_separation),
        ]
        
        passed = 0
        total = len(validations)
        
        for name, validation_func in validations:
            print(f"\n--- {name} ---")
            if validation_func():
                passed += 1
            print()
        
        # Summary
        print("=" * 50)
        print(f"📊 ARCHITECTURE VALIDATION SUMMARY:")
        print(f"   ✅ Passed: {passed}/{total}")
        print(f"   ❌ Errors: {len(self.errors)}")
        print(f"   ⚠️  Warnings: {len(self.warnings)}")
        
        # Pattern implementation summary
        print(f"\n🎯 DESIGN PATTERNS IMPLEMENTED:")
        for pattern, implemented in self.patterns_found.items():
            status = "✅" if implemented else "❌"
            print(f"   {status} {pattern.replace('_', ' ').title()}")
        
        if self.errors:
            print(f"\n❌ ERRORS:")
            for error in self.errors:
                print(f"   - {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS:")
            for warning in self.warnings:
                print(f"   - {warning}")
        
        success = len(self.errors) == 0
        patterns_implemented = sum(self.patterns_found.values())
        
        if success and patterns_implemented >= 4:
            print(f"\n🎉 Architecture validation PASSED! ({patterns_implemented}/5 patterns implemented)")
        else:
            print(f"\n❌ Architecture validation FAILED!")
        
        return success and patterns_implemented >= 4


def main():
    """Main validation function."""
    validator = ArchitectureValidator()
    
    try:
        success = validator.run_full_validation()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during validation: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
