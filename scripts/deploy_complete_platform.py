#!/usr/bin/env python3
# scripts/deploy_complete_platform.py
# Complete platform deployment script

"""
Complete platform deployment script.
Deploys all services in the correct order with dependency management.
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any


class PlatformDeployer:
    """Complete platform deployment manager."""
    
    def __init__(self, stage: str = "dev"):
        """Initialize deployer."""
        self.stage = stage
        self.project_root = Path(__file__).parent.parent
        self.deployment_results = []
        self.failed_deployments = []
        
        # Deployment order (respecting dependencies)
        self.deployment_order = [
            {
                'name': 'Shared Layer',
                'path': 'shared',
                'critical': True,
                'timeout': 300
            },
            {
                'name': 'Auth Service',
                'path': 'services/auth',
                'critical': True,
                'timeout': 600
            },
            {
                'name': 'Tenant Service',
                'path': 'services/tenant',
                'critical': True,
                'timeout': 600
            },
            {
                'name': 'Payment Service',
                'path': 'services/payment',
                'critical': True,
                'timeout': 600
            },
            {
                'name': 'Setup Service',
                'path': 'services/setup',
                'critical': True,
                'timeout': 600
            },
            {
                'name': 'Orchestrator Service',
                'path': 'services/orchestrator',
                'critical': True,
                'timeout': 600
            },
            {
                'name': 'Jobs Service',
                'path': 'services/jobs',
                'critical': False,
                'timeout': 600
            }
        ]
    
    def deploy_service(self, service: Dict[str, Any]) -> bool:
        """
        Deploy individual service.
        
        Args:
            service: Service configuration
            
        Returns:
            True if deployment successful
        """
        service_name = service['name']
        service_path = service['path']
        timeout = service['timeout']
        
        print(f"\n🚀 Deploying {service_name}...")
        print(f"   Path: {service_path}")
        print(f"   Stage: {self.stage}")
        print("=" * 60)
        
        try:
            # Change to service directory
            full_path = self.project_root / service_path
            
            if not full_path.exists():
                print(f"❌ Service path does not exist: {full_path}")
                return False
            
            # Check if serverless.yml exists
            serverless_file = full_path / "serverless.yml"
            if not serverless_file.exists():
                print(f"❌ serverless.yml not found in {full_path}")
                return False
            
            print(f"📁 Working directory: {full_path}")
            
            # Run serverless deploy
            cmd = [
                "serverless", "deploy",
                "--stage", self.stage,
                "--verbose"
            ]
            
            print(f"🔧 Running: {' '.join(cmd)}")
            
            start_time = time.time()
            
            result = subprocess.run(
                cmd,
                cwd=str(full_path),
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ {service_name} deployed successfully!")
                print(f"⏱️  Duration: {duration:.1f} seconds")
                
                # Extract important info from output
                output_lines = result.stdout.split('\n')
                endpoints = []
                for line in output_lines:
                    if 'https://' in line and 'execute-api' in line:
                        endpoints.append(line.strip())
                
                self.deployment_results.append({
                    'service': service_name,
                    'status': 'SUCCESS',
                    'duration': duration,
                    'endpoints': endpoints
                })
                
                if endpoints:
                    print("🔗 Endpoints deployed:")
                    for endpoint in endpoints:
                        print(f"   {endpoint}")
                
                return True
            else:
                print(f"❌ {service_name} deployment failed!")
                print(f"⏱️  Duration: {duration:.1f} seconds")
                print(f"📝 Error output:")
                print(result.stderr)
                
                self.failed_deployments.append({
                    'service': service_name,
                    'error': result.stderr,
                    'duration': duration
                })
                
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {service_name} deployment timed out after {timeout} seconds")
            self.failed_deployments.append({
                'service': service_name,
                'error': f"Deployment timed out after {timeout} seconds",
                'duration': timeout
            })
            return False
            
        except Exception as e:
            print(f"💥 {service_name} deployment failed with exception: {str(e)}")
            self.failed_deployments.append({
                'service': service_name,
                'error': str(e),
                'duration': 0
            })
            return False
    
    def deploy_all(self) -> bool:
        """
        Deploy all services in order.
        
        Returns:
            True if all critical services deployed successfully
        """
        print("🚀 Starting Complete Platform Deployment")
        print(f"📊 Stage: {self.stage}")
        print(f"📁 Project Root: {self.project_root}")
        print(f"🔢 Services to deploy: {len(self.deployment_order)}")
        print("=" * 80)
        
        total_start_time = time.time()
        critical_failures = 0
        
        for i, service in enumerate(self.deployment_order, 1):
            print(f"\n📦 [{i}/{len(self.deployment_order)}] {service['name']}")
            
            success = self.deploy_service(service)
            
            if not success:
                if service['critical']:
                    critical_failures += 1
                    print(f"🚨 CRITICAL SERVICE FAILED: {service['name']}")
                    
                    # Ask user if they want to continue
                    response = input(f"\n❓ Continue with remaining deployments? (y/N): ")
                    if response.lower() != 'y':
                        print("🛑 Deployment stopped by user")
                        break
                else:
                    print(f"⚠️  Non-critical service failed: {service['name']}")
            
            # Small delay between deployments
            if i < len(self.deployment_order):
                print("⏳ Waiting 10 seconds before next deployment...")
                time.sleep(10)
        
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time
        
        # Generate deployment report
        self.generate_deployment_report(total_duration, critical_failures)
        
        return critical_failures == 0
    
    def generate_deployment_report(self, total_duration: float, critical_failures: int) -> None:
        """Generate deployment report."""
        print("\n" + "=" * 80)
        print("📊 DEPLOYMENT REPORT")
        print("=" * 80)
        
        print(f"\n⏱️  Total Duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
        print(f"✅ Successful Deployments: {len(self.deployment_results)}")
        print(f"❌ Failed Deployments: {len(self.failed_deployments)}")
        print(f"🚨 Critical Failures: {critical_failures}")
        
        if self.deployment_results:
            print(f"\n✅ SUCCESSFUL DEPLOYMENTS:")
            for result in self.deployment_results:
                print(f"   • {result['service']} ({result['duration']:.1f}s)")
                for endpoint in result['endpoints']:
                    print(f"     🔗 {endpoint}")
        
        if self.failed_deployments:
            print(f"\n❌ FAILED DEPLOYMENTS:")
            for failure in self.failed_deployments:
                print(f"   • {failure['service']} ({failure['duration']:.1f}s)")
                print(f"     💥 {failure['error'][:100]}...")
        
        # Overall status
        if critical_failures == 0:
            print(f"\n🎉 DEPLOYMENT SUCCESSFUL!")
            print(f"   All critical services deployed successfully")
            if self.failed_deployments:
                print(f"   ⚠️  {len(self.failed_deployments)} non-critical services failed")
        else:
            print(f"\n🚨 DEPLOYMENT FAILED!")
            print(f"   {critical_failures} critical services failed")
        
        print("=" * 80)
    
    def validate_prerequisites(self) -> bool:
        """Validate deployment prerequisites."""
        print("🔍 Validating deployment prerequisites...")
        
        # Check if serverless is installed
        try:
            result = subprocess.run(
                ["serverless", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0:
                print("❌ Serverless Framework not installed")
                return False
            print(f"✅ Serverless Framework: {result.stdout.strip()}")
        except Exception as e:
            print(f"❌ Error checking Serverless Framework: {str(e)}")
            return False
        
        # Check AWS credentials
        try:
            result = subprocess.run(
                ["aws", "sts", "get-caller-identity"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0:
                print("❌ AWS credentials not configured")
                return False
            print("✅ AWS credentials configured")
        except Exception as e:
            print(f"❌ Error checking AWS credentials: {str(e)}")
            return False
        
        # Check Node.js
        try:
            result = subprocess.run(
                ["node", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0:
                print("❌ Node.js not installed")
                return False
            print(f"✅ Node.js: {result.stdout.strip()}")
        except Exception as e:
            print(f"❌ Error checking Node.js: {str(e)}")
            return False
        
        print("✅ All prerequisites validated")
        return True


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deploy complete platform")
    parser.add_argument(
        "--stage",
        default="dev",
        help="Deployment stage (default: dev)"
    )
    parser.add_argument(
        "--skip-validation",
        action="store_true",
        help="Skip prerequisite validation"
    )
    
    args = parser.parse_args()
    
    deployer = PlatformDeployer(stage=args.stage)
    
    # Validate prerequisites
    if not args.skip_validation:
        if not deployer.validate_prerequisites():
            print("❌ Prerequisites validation failed")
            sys.exit(1)
    
    # Deploy all services
    success = deployer.deploy_all()
    
    if success:
        print("\n🎉 Platform deployment completed successfully!")
        sys.exit(0)
    else:
        print("\n🚨 Platform deployment failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
