#!/usr/bin/env python3
# scripts/fix-test-imports.py
# Script para reparar imports de tests de forma sistemática

"""
Fix test imports systematically to work with new service structure.
"""

import os
import re
from pathlib import Path
from typing import Dict, List

class TestImportFixer:
    """Fix test imports for new service structure."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.fixes_applied = 0
        
        # Define import mappings
        self.import_mappings = {
            # Handler imports
            r'from handlers\.(\w+) import': r'from handlers.\1 import',
            r'from \.\.handlers\.(\w+) import': r'from handlers.\1 import',
            
            # Model imports  
            r'from models\.(\w+) import': r'from models.\1 import',
            r'from \.\.models\.(\w+) import': r'from models.\1 import',
            
            # Service imports
            r'from services\.(\w+) import': r'from services.\1 import',
            r'from \.\.services\.(\w+) import': r'from services.\1 import',
            
            # Shared imports - these should work as-is
            r'from shared\.(\w+) import': r'from shared.\1 import',
        }
    
    def fix_file_imports(self, file_path: Path) -> bool:
        """Fix imports in a single test file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply import fixes
            for pattern, replacement in self.import_mappings.items():
                content = re.sub(pattern, replacement, content)
            
            # Fix specific problematic imports
            content = self.fix_specific_imports(content, file_path)
            
            # Write back if changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Fixed imports in: {file_path}")
                self.fixes_applied += 1
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error fixing {file_path}: {e}")
            return False
    
    def fix_specific_imports(self, content: str, file_path: Path) -> str:
        """Fix specific import issues based on file location."""
        
        # Fix missing exception imports
        if 'ExternalServiceException' in content and 'from shared.exceptions import' in content:
            content = re.sub(
                r'from shared\.exceptions import ([^E]*)',
                r'from shared.exceptions import \1, ExternalServiceException',
                content
            )
        
        # Fix service-specific imports based on test location
        if 'test_stripe' in file_path.name or 'payment' in str(file_path):
            # Payment service specific fixes
            content = re.sub(
                r'from services\.stripe_client import',
                r'from services.stripe_client import',
                content
            )
        
        # Fix handler imports for e2e tests
        if 'e2e' in str(file_path):
            # Remove manual path additions since conftest.py handles this
            content = re.sub(
                r'# Add path.*\nsys\.path\.insert.*\n',
                '',
                content,
                flags=re.MULTILINE
            )
            content = re.sub(
                r'sys\.path\.insert\(0.*\n',
                '',
                content
            )
        
        return content
    
    def fix_all_tests(self):
        """Fix imports in all test files."""
        print("🔧 Starting systematic test import fixes...")
        
        test_files = []
        for root, dirs, files in os.walk(self.project_root / "tests"):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    test_files.append(Path(root) / file)
        
        print(f"Found {len(test_files)} test files to process")
        
        for test_file in test_files:
            self.fix_file_imports(test_file)
        
        print(f"\n✅ Applied fixes to {self.fixes_applied} files")
    
    def validate_imports(self):
        """Validate that imports are working."""
        print("\n🔍 Validating import fixes...")
        
        # Test basic imports
        test_imports = [
            "import sys; sys.path.insert(0, 'shared/python'); from shared.validators import validate_email_address",
            "import sys; sys.path.insert(0, 'services/auth/src'); from models.user import User",
            "import sys; sys.path.insert(0, 'services/payment/src'); from models.plan import Plan",
        ]
        
        for test_import in test_imports:
            try:
                exec(test_import)
                print(f"✅ Import test passed: {test_import.split(';')[-1].strip()}")
            except Exception as e:
                print(f"❌ Import test failed: {e}")

def main():
    """Main function."""
    fixer = TestImportFixer()
    fixer.fix_all_tests()
    fixer.validate_imports()

if __name__ == "__main__":
    main()
