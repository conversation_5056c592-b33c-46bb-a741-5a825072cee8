# scripts/toggle-serverless-deps.ps1
# Script para alternar entre comentar y descomentar dependencias serverless

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("comment", "uncomment", "status")]
    [string]$Action,
    
    [string]$RootPath = "."
)

function Show-Status {
    param([string]$RootPath)
    
    Write-Host "📊 Estado actual de las dependencias serverless:" -ForegroundColor Cyan
    Write-Host ""
    
    $serverlessFiles = Get-ChildItem -Path $RootPath -Name "serverless.yml" -Recurse
    
    foreach ($file in $serverlessFiles) {
        $fullPath = Join-Path $RootPath $file
        $content = Get-Content $fullPath
        
        $sharedLayersCommented = 0
        $sharedLayersTotal = 0
        $authorizersCommented = 0
        $authorizersTotal = 0
        
        foreach ($line in $content) {
            # Contar shared layers
            if ($line -match "layers:\s*$" -or $line -match "- \$\{self:custom\.sharedLayerArn\}") {
                $sharedLayersTotal++
                if ($line -match "^\s*#") {
                    $sharedLayersCommented++
                }
            }
            
            # Contar authorizers
            if ($line -match "authorizer:\s*$" -or $line -match "name: jwtAuthorizer" -or $line -match "Fn::ImportValue:.*JwtAuthorizer") {
                $authorizersTotal++
                if ($line -match "^\s*#") {
                    $authorizersCommented++
                }
            }
        }
        
        $sharedStatus = if ($sharedLayersTotal -eq 0) { "N/A" } 
                       elseif ($sharedLayersCommented -eq $sharedLayersTotal) { "COMENTADO" }
                       elseif ($sharedLayersCommented -eq 0) { "ACTIVO" }
                       else { "PARCIAL" }
        
        $authStatus = if ($authorizersTotal -eq 0) { "N/A" }
                     elseif ($authorizersCommented -eq $authorizersTotal) { "COMENTADO" }
                     elseif ($authorizersCommented -eq 0) { "ACTIVO" }
                     else { "PARCIAL" }
        
        $sharedColor = switch ($sharedStatus) {
            "ACTIVO" { "Green" }
            "COMENTADO" { "Yellow" }
            "PARCIAL" { "Red" }
            default { "Gray" }
        }
        
        $authColor = switch ($authStatus) {
            "ACTIVO" { "Green" }
            "COMENTADO" { "Yellow" }
            "PARCIAL" { "Red" }
            default { "Gray" }
        }
        
        Write-Host "📄 $file" -ForegroundColor White
        Write-Host "  📦 Shared Layers: " -NoNewline -ForegroundColor Blue
        Write-Host $sharedStatus -ForegroundColor $sharedColor
        Write-Host "  🔐 Authorizers: " -NoNewline -ForegroundColor Blue
        Write-Host $authStatus -ForegroundColor $authColor
        Write-Host ""
    }
}

switch ($Action) {
    "comment" {
        Write-Host "🔧 Comentando dependencias..." -ForegroundColor Yellow
        & "$PSScriptRoot\comment-serverless-deps.ps1" -RootPath $RootPath
    }
    "uncomment" {
        Write-Host "🔧 Descomentando dependencias..." -ForegroundColor Yellow
        & "$PSScriptRoot\uncomment-serverless-deps.ps1" -RootPath $RootPath
    }
    "status" {
        Show-Status -RootPath $RootPath
    }
}

Write-Host ""
Write-Host "💡 Uso:" -ForegroundColor Cyan
Write-Host "  .\toggle-serverless-deps.ps1 comment    # Comentar todas las dependencias" -ForegroundColor White
Write-Host "  .\toggle-serverless-deps.ps1 uncomment  # Descomentar todas las dependencias" -ForegroundColor White
Write-Host "  .\toggle-serverless-deps.ps1 status     # Ver estado actual" -ForegroundColor White
