# scripts/deploy-ses.ps1
# Script para deployment de AWS SES configuration en Windows

param(
    [string]$Stage = "dev",
    [string]$Region = "us-east-1"
)

$ProjectName = "the-jungle-agents"

Write-Host "🚀 Starting SES Deployment for $ProjectName" -ForegroundColor Blue
Write-Host "📍 Stage: $Stage" -ForegroundColor Blue
Write-Host "🌍 Region: $Region" -ForegroundColor Blue
Write-Host ""

# Check if AWS CLI is installed
try {
    aws --version | Out-Null
    Write-Host "✅ AWS CLI configured" -ForegroundColor Green
} catch {
    Write-Host "❌ AWS CLI is not installed. Please install it first." -ForegroundColor Red
    exit 1
}

# Check if AWS credentials are configured
try {
    aws sts get-caller-identity | Out-Null
    Write-Host "✅ AWS credentials configured" -ForegroundColor Green
} catch {
    Write-Host "❌ AWS credentials not configured. Please run 'aws configure' first." -ForegroundColor Red
    exit 1
}

# Check if Serverless Framework is installed
try {
    serverless --version | Out-Null
    Write-Host "✅ Serverless Framework available" -ForegroundColor Green
} catch {
    Write-Host "❌ Serverless Framework is not installed. Please install it first." -ForegroundColor Red
    Write-Host "💡 Run: npm install -g serverless" -ForegroundColor Yellow
    exit 1
}

# Verify SES configuration file exists
if (-not (Test-Path "serverless/resources/ses.yml")) {
    Write-Host "❌ SES configuration file not found: serverless/resources/ses.yml" -ForegroundColor Red
    exit 1
}

Write-Host "✅ SES configuration file found" -ForegroundColor Green

# Create temporary serverless.yml for SES deployment
Write-Host "📝 Creating temporary SES deployment configuration..." -ForegroundColor Yellow

$serverlessConfig = @"
service: $ProjectName-ses

provider:
  name: aws
  runtime: python3.12
  region: $Region
  stage: $Stage
  
custom:
  stage: $Stage
  projectName: $ProjectName

resources:
  - `${file(serverless/resources/ses.yml)}

plugins:
  - serverless-python-requirements
"@

$serverlessConfig | Out-File -FilePath "serverless-ses.yml" -Encoding UTF8

Write-Host "✅ Temporary configuration created" -ForegroundColor Green

# Deploy SES resources
Write-Host "🚀 Deploying SES resources..." -ForegroundColor Yellow
Write-Host ""

try {
    serverless deploy --config serverless-ses.yml --stage $Stage --region $Region
    
    Write-Host ""
    Write-Host "✅ SES deployment completed successfully!" -ForegroundColor Green
    
    # Clean up temporary file
    Remove-Item "serverless-ses.yml" -ErrorAction SilentlyContinue
    
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Blue
    Write-Host "1. Verify domain ownership in AWS SES Console" -ForegroundColor Yellow
    Write-Host "2. Add DNS records for domain verification" -ForegroundColor Yellow
    Write-Host "3. Configure DKIM authentication" -ForegroundColor Yellow
    Write-Host "4. Test email sending functionality" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔗 AWS SES Console: https://console.aws.amazon.com/ses/home?region=$Region" -ForegroundColor Blue
    
} catch {
    Write-Host ""
    Write-Host "❌ SES deployment failed!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    # Clean up temporary file
    Remove-Item "serverless-ses.yml" -ErrorAction SilentlyContinue
    
    exit 1
}
