# scripts/setup-environment.ps1
# Implementado según "Infrastructure as Code Configuration"

param(
    [string]$Environment = "dev",
    [string]$AwsRegion = "us-east-1"
)

$ProjectName = "platform"

Write-Host "Setting up environment: $Environment in region: $AwsRegion" -ForegroundColor Green

# Validate environment
if ($Environment -notin @("dev", "staging", "prod")) {
    Write-Host "❌ Invalid environment. Must be dev, staging, or prod" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Valid environment: $Environment" -ForegroundColor Green

# Load environment-specific configuration
$EnvConfigFile = ".env.$Environment"
if (-not (Test-Path $EnvConfigFile)) {
    Write-Host "❌ Environment configuration file not found: $EnvConfigFile" -ForegroundColor Red
    Write-Host "Creating template configuration file..." -ForegroundColor Yellow
    
    $configContent = @"
# Environment configuration for $Environment
AWS_REGION=$AwsRegion
PROJECT_NAME=$ProjectName
ENVIRONMENT=$Environment

# 🚨 CONSULTA REQUERIDA: Configurar estos valores
# Ver documento "Information Gaps & Clarification Requirements"
# AWS_ACCOUNT_ID=
# DOMAIN_NAME=
# SSL_CERTIFICATE_ARN=
"@
    
    $configContent | Out-File -FilePath $EnvConfigFile -Encoding UTF8
    Write-Host "⚠️ Please configure $EnvConfigFile with actual values" -ForegroundColor Yellow
}

# Setup Terraform backend if not exists
$TerraformBucket = "$ProjectName-terraform-state-$Environment"
Write-Host "🏗️ Setting up Terraform backend..." -ForegroundColor Blue

# Check if AWS CLI is configured
try {
    aws sts get-caller-identity | Out-Null
    Write-Host "✅ AWS CLI is configured" -ForegroundColor Green
} catch {
    Write-Host "❌ AWS CLI not configured. Please run 'aws configure' first" -ForegroundColor Red
    exit 1
}

# Create S3 bucket for Terraform state
Write-Host "Creating S3 bucket: $TerraformBucket" -ForegroundColor Blue
try {
    aws s3 mb "s3://$TerraformBucket" --region $AwsRegion 2>$null
    Write-Host "✅ S3 bucket created successfully" -ForegroundColor Green
} catch {
    Write-Host "ℹ️ Bucket already exists or creation failed" -ForegroundColor Yellow
}

# Enable versioning on state bucket
Write-Host "Enabling versioning on state bucket..." -ForegroundColor Blue
aws s3api put-bucket-versioning --bucket $TerraformBucket --versioning-configuration Status=Enabled

# Enable encryption on state bucket
Write-Host "Enabling encryption on state bucket..." -ForegroundColor Blue
$encryptionConfig = @'
{
    "Rules": [
        {
            "ApplyServerSideEncryptionByDefault": {
                "SSEAlgorithm": "AES256"
            }
        }
    ]
}
'@

aws s3api put-bucket-encryption --bucket $TerraformBucket --server-side-encryption-configuration $encryptionConfig

# Create DynamoDB table for state locking
Write-Host "Creating DynamoDB table for state locking..." -ForegroundColor Blue
try {
    aws dynamodb create-table `
        --table-name terraform-state-lock `
        --attribute-definitions AttributeName=LockID,AttributeType=S `
        --key-schema AttributeName=LockID,KeyType=HASH `
        --billing-mode PAY_PER_REQUEST `
        --region $AwsRegion 2>$null
    Write-Host "✅ DynamoDB table created successfully" -ForegroundColor Green
} catch {
    Write-Host "ℹ️ DynamoDB table already exists or creation failed" -ForegroundColor Yellow
}

Write-Host "✅ Environment setup completed for: $Environment" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Configure $EnvConfigFile with actual values" -ForegroundColor White
Write-Host "   2. Run: cd terraform/environments/$Environment" -ForegroundColor White
Write-Host "   3. Run: terraform init" -ForegroundColor White
Write-Host "   4. Run: terraform plan" -ForegroundColor White
