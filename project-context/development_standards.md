# Development Standards & Best Practices

## 💻 Code Quality Standards

### **Programming Language: Python 3.11**

#### **Current Code Style & Formatting**
```yaml
Tools & Configuration:
  Formatter: Black (line-length: 88)
  Linter: flake8 (primary), pylint (optional)
  Type Checker: mypy (optional, gradual adoption)
  Import Sorter: isort (optional)
  Testing: pytest with coverage reporting

Current Quality Tools:
  - flake8: Primary linting
  - black: Code formatting
  - pytest: Testing framework
  - coverage: Test coverage reporting

Architecture Patterns:
  - Dependency Injection: Custom IoC container
  - Repository Pattern: Data access abstraction
  - Decorator Pattern: Cross-cutting concerns
  - Error Handling: Centralized exception handling
```

#### **Python Code Standards**
```python
"""
Module-level docstring example.

This module provides authentication and authorization utilities
for the platform backend services.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from dataclasses import dataclass

import boto3
from pydantic import BaseModel, Field


# Constants - Use UPPER_CASE
MAX_RETRY_ATTEMPTS = 3
DEFAULT_TOKEN_EXPIRY = 3600
API_VERSION = "v1"

# Type aliases for clarity
TenantId = str
UserId = str
TokenDict = Dict[str, Union[str, int]]


@dataclass
class UserContext:
    """Data class for user context information."""
    
    user_id: UserId
    tenant_id: TenantId
    role: str
    permissions: List[str]
    session_id: Optional[str] = None
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission."""
        return permission in self.permissions


class AuthenticationService:
    """Service for handling user authentication and authorization.
    
    This service provides methods for user login, token generation,
    and permission validation with proper error handling and logging.
    
    Attributes:
        jwt_secret: Secret key for JWT token signing
        token_expiry: Token expiration time in seconds
        logger: Logger instance for this service
    """
    
    def __init__(self, jwt_secret: str, token_expiry: int = DEFAULT_TOKEN_EXPIRY):
        """Initialize authentication service.
        
        Args:
            jwt_secret: Secret key for JWT signing
            token_expiry: Token expiration time in seconds
            
        Raises:
            ValueError: If jwt_secret is empty or invalid
        """
        if not jwt_secret:
            raise ValueError("JWT secret cannot be empty")
            
        self.jwt_secret = jwt_secret
        self.token_expiry = token_expiry
        self.logger = logging.getLogger(__name__)
        
        # Initialize AWS clients
        self._dynamodb = boto3.resource('dynamodb')
        self._table = self._dynamodb.Table('platform-main-prod')
    
    async def authenticate_user(self, email: str, password: str) -> Optional[TokenDict]:
        """Authenticate user with email and password.
        
        Args:
            email: User email address
            password: User password (plain text)
            
        Returns:
            Dictionary containing access and refresh tokens if successful,
            None if authentication fails
            
        Raises:
            ValueError: If email or password is invalid format
            ServiceError: If external service call fails
        """
        # Input validation
        if not self._is_valid_email(email):
            raise ValueError(f"Invalid email format: {email}")
            
        if not password or len(password) < 8:
            raise ValueError("Password must be at least 8 characters")
        
        try:
            # Get user from database
            user = await self._get_user_by_email(email)
            if not user:
                self.logger.warning(f"Authentication attempt for non-existent user: {email}")
                return None
            
            # Verify password
            if not await self._verify_password(password, user['password_hash']):
                self.logger.warning(f"Invalid password attempt for user: {email}")
                await self._log_failed_login(user['user_id'], email)
                return None
            
            # Check if user is active
            if user.get('status') != 'active':
                self.logger.warning(f"Authentication attempt for inactive user: {email}")
                return None
            
            # Generate tokens
            tokens = await self._generate_tokens(user)
            
            # Log successful authentication
            await self._log_successful_login(user['user_id'], email)
            
            self.logger.info(f"Successful authentication for user: {email}")
            return tokens
            
        except Exception as e:
            self.logger.error(f"Authentication error for {email}: {str(e)}")
            raise ServiceError(f"Authentication failed: {str(e)}")
    
    async def _get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user record by email address.
        
        This is a private method that handles database queries
        with proper error handling and retry logic.
        """
        for attempt in range(MAX_RETRY_ATTEMPTS):
            try:
                response = self._table.query(
                    IndexName='GSI1',
                    KeyConditionExpression=Key('GSI1PK').eq(f'EMAIL#{email}')
                )
                
                items = response.get('Items', [])
                return items[0] if items else None
                
            except ClientError as e:
                if attempt == MAX_RETRY_ATTEMPTS - 1:
                    raise ServiceError(f"Database query failed: {str(e)}")
                    
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
    
    @staticmethod
    def _is_valid_email(email: str) -> bool:
        """Validate email format using regex."""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))


# Custom exceptions
class ServiceError(Exception):
    """Base exception for service-level errors."""
    pass


class AuthenticationError(ServiceError):
    """Exception raised for authentication failures."""
    pass


class AuthorizationError(ServiceError):
    """Exception raised for authorization failures."""
    pass
```

#### **Function Design Principles**
```python
# Good: Single responsibility, clear naming, type hints
async def calculate_monthly_usage_cost(
    tenant_id: str, 
    usage_data: Dict[str, int],
    plan_limits: Dict[str, int]
) -> Decimal:
    """Calculate monthly cost including overages.
    
    Args:
        tenant_id: Unique tenant identifier
        usage_data: Dictionary of usage metrics
        plan_limits: Dictionary of plan limits
        
    Returns:
        Total monthly cost including base plan and overages
        
    Raises:
        ValueError: If tenant_id is invalid or usage_data is malformed
    """
    if not tenant_id or not usage_data:
        raise ValueError("Invalid input parameters")
    
    base_cost = await get_plan_base_cost(tenant_id)
    overage_cost = calculate_overage_costs(usage_data, plan_limits)
    
    return base_cost + overage_cost

# Avoid: Large functions with multiple responsibilities
# def process_user_registration_and_billing_and_email(...):  # DON'T DO THIS
```

### **Error Handling Standards**

#### **Exception Hierarchy**
```python
class PlatformError(Exception):
    """Base exception for all platform errors."""
    
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(PlatformError):
    """Raised when input validation fails."""
    pass


class AuthenticationError(PlatformError):
    """Raised when authentication fails."""
    pass


class AuthorizationError(PlatformError):
    """Raised when user lacks required permissions."""
    pass


class TenantError(PlatformError):
    """Raised for tenant-related errors."""
    pass


class ExternalServiceError(PlatformError):
    """Raised when external service calls fail."""
    pass


class DatabaseError(PlatformError):
    """Raised for database operation failures."""
    pass
```

#### **Error Handling Patterns**
```python
import functools
import logging
from typing import Any, Callable, TypeVar

F = TypeVar('F', bound=Callable[..., Any])

def handle_errors(error_response_builder: Callable = None):
    """Decorator for standardized error handling in Lambda functions."""
    
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(event, context):
            try:
                return await func(event, context)
                
            except ValidationError as e:
                logging.warning(f"Validation error in {func.__name__}: {str(e)}")
                return {
                    'statusCode': 400,
                    'body': json.dumps({
                        'success': False,
                        'error': {
                            'code': e.error_code,
                            'message': e.message,
                            'details': e.details
                        }
                    })
                }
                
            except AuthenticationError as e:
                logging.warning(f"Authentication error in {func.__name__}: {str(e)}")
                return {
                    'statusCode': 401,
                    'body': json.dumps({
                        'success': False,
                        'error': {
                            'code': e.error_code,
                            'message': e.message
                        }
                    })
                }
                
            except AuthorizationError as e:
                logging.warning(f"Authorization error in {func.__name__}: {str(e)}")
                return {
                    'statusCode': 403,
                    'body': json.dumps({
                        'success': False,
                        'error': {
                            'code': e.error_code,
                            'message': e.message
                        }
                    })
                }
                
            except ExternalServiceError as e:
                logging.error(f"External service error in {func.__name__}: {str(e)}")
                return {
                    'statusCode': 502,
                    'body': json.dumps({
                        'success': False,
                        'error': {
                            'code': 'EXTERNAL_SERVICE_ERROR',
                            'message': 'External service temporarily unavailable'
                        }
                    })
                }
                
            except Exception as e:
                logging.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
                return {
                    'statusCode': 500,
                    'body': json.dumps({
                        'success': False,
                        'error': {
                            'code': 'INTERNAL_ERROR',
                            'message': 'An unexpected error occurred'
                        }
                    })
                }
        
        return wrapper
    return decorator

# Usage example
@handle_errors()
@tenant_context_required
async def create_user_handler(event, context):
    """Lambda handler for creating new users."""
    tenant_context = event['tenant_context']
    validated_data = event['validated_data']
    
    user_service = UserService()
    result = await user_service.create_user(
        tenant_context.tenant_id,
        validated_data
    )
    
    return {
        'statusCode': 201,
        'body': json.dumps({
            'success': True,
            'data': result
        })
    }
```

## 🧪 Testing Standards

### **Testing Strategy**
```yaml
Testing Pyramid:
  Unit Tests (70%):
    - Individual function testing
    - Mocked dependencies
    - Fast execution (<1s per test)
    - Coverage target: >90%
    
  Integration Tests (20%):
    - Service integration testing
    - Real AWS services (LocalStack)
    - Database operations
    - External API mocking
    
  End-to-End Tests (10%):
    - Complete user workflows
    - Real environment testing
    - Performance validation
    - Security testing

Tools:
  - pytest: Test runner
  - pytest-asyncio: Async test support
  - moto: AWS service mocking
  - httpx: HTTP client testing
  - factory_boy: Test data generation
```

#### **Unit Test Examples**
```python
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from decimal import Decimal

from src.services.auth_service import AuthenticationService
from src.exceptions import AuthenticationError, ValidationError


@pytest.fixture
def auth_service():
    """Create authentication service for testing."""
    return AuthenticationService(jwt_secret="test-secret")


@pytest.fixture
def sample_user():
    """Create sample user data for testing."""
    return {
        'user_id': 'user_123',
        'email': '<EMAIL>',
        'password_hash': '$2b$12$hashed_password',
        'status': 'active',
        'tenant_id': 'tenant_456',
        'role': 'MASTER',
        'permissions': ['agents:read', 'users:write']
    }


class TestAuthenticationService:
    """Test cases for AuthenticationService."""
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, sample_user):
        """Test successful user authentication."""
        
        # Mock dependencies
        with patch.object(auth_service, '_get_user_by_email', return_value=sample_user), \
             patch.object(auth_service, '_verify_password', return_value=True), \
             patch.object(auth_service, '_generate_tokens', return_value={'access_token': 'token123'}), \
             patch.object(auth_service, '_log_successful_login'):
            
            result = await auth_service.authenticate_user('<EMAIL>', 'password123')
            
            assert result is not None
            assert 'access_token' in result
            assert result['access_token'] == 'token123'
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_email(self, auth_service):
        """Test authentication with invalid email format."""
        
        with pytest.raises(ValidationError) as exc_info:
            await auth_service.authenticate_user('invalid-email', 'password123')
        
        assert 'Invalid email format' in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_short_password(self, auth_service):
        """Test authentication with too short password."""
        
        with pytest.raises(ValidationError) as exc_info:
            await auth_service.authenticate_user('<EMAIL>', '123')
        
        assert 'Password must be at least 8 characters' in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, auth_service):
        """Test authentication with non-existent user."""
        
        with patch.object(auth_service, '_get_user_by_email', return_value=None):
            result = await auth_service.authenticate_user('<EMAIL>', 'password123')
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_password(self, auth_service, sample_user):
        """Test authentication with invalid password."""
        
        with patch.object(auth_service, '_get_user_by_email', return_value=sample_user), \
             patch.object(auth_service, '_verify_password', return_value=False), \
             patch.object(auth_service, '_log_failed_login'):
            
            result = await auth_service.authenticate_user('<EMAIL>', 'wrongpassword')
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive_status(self, auth_service, sample_user):
        """Test authentication with inactive user."""
        
        sample_user['status'] = 'inactive'
        
        with patch.object(auth_service, '_get_user_by_email', return_value=sample_user):
            result = await auth_service.authenticate_user('<EMAIL>', 'password123')
            
            assert result is None


# Parametrized tests for edge cases
@pytest.mark.parametrize("email,expected_valid", [
    ("<EMAIL>", True),
    ("<EMAIL>", True),
    ("invalid.email", False),
    ("@example.com", False),
    ("user@", False),
    ("", False)
])
def test_email_validation(email, expected_valid):
    """Test email validation with various inputs."""
    result = AuthenticationService._is_valid_email(email)
    assert result == expected_valid


# Mock external dependencies
class TestUserService:
    """Test cases for UserService with mocked dependencies."""
    
    @pytest.fixture
    def mock_dynamodb_table(self):
        """Mock DynamoDB table."""
        return MagicMock()
    
    @pytest.fixture
    def mock_s3_client(self):
        """Mock S3 client."""
        return MagicMock()
    
    @pytest.fixture
    def user_service(self, mock_dynamodb_table, mock_s3_client):
        """Create UserService with mocked dependencies."""
        with patch('src.services.user_service.boto3.resource') as mock_resource, \
             patch('src.services.user_service.boto3.client') as mock_client:
            
            mock_resource.return_value.Table.return_value = mock_dynamodb_table
            mock_client.return_value = mock_s3_client
            
            from src.services.user_service import UserService
            return UserService()
    
    @pytest.mark.asyncio
    async def test_create_user(self, user_service, mock_dynamodb_table):
        """Test user creation with mocked database."""
        
        user_data = {
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'role': 'MEMBER'
        }
        
        # Mock successful database operations
        mock_dynamodb_table.put_item.return_value = {'ResponseMetadata': {'HTTPStatusCode': 200}}
        
        result = await user_service.create_user('tenant_123', user_data)
        
        assert result['email'] == '<EMAIL>'
        assert 'user_id' in result
        mock_dynamodb_table.put_item.assert_called()
```

#### **Integration Test Examples**
```python
import pytest
import boto3
import asyncio
from moto import mock_dynamodb, mock_s3
from decimal import Decimal

from src.services.tenant_service import TenantService
from src.models.tenant import Tenant


@pytest.fixture(scope="function")
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    import os
    os.environ['AWS_ACCESS_KEY_ID'] = 'testing'
    os.environ['AWS_SECRET_ACCESS_KEY'] = 'testing'
    os.environ['AWS_SECURITY_TOKEN'] = 'testing'
    os.environ['AWS_SESSION_TOKEN'] = 'testing'


@pytest.fixture
@mock_dynamodb
def dynamodb_table(aws_credentials):
    """Create mocked DynamoDB table for testing."""
    
    dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
    
    # Create table
    table = dynamodb.create_table(
        TableName='platform-test',
        KeySchema=[
            {'AttributeName': 'PK', 'KeyType': 'HASH'},
            {'AttributeName': 'SK', 'KeyType': 'RANGE'}
        ],
        AttributeDefinitions=[
            {'AttributeName': 'PK', 'AttributeType': 'S'},
            {'AttributeName': 'SK', 'AttributeType': 'S'},
            {'AttributeName': 'GSI1PK', 'AttributeType': 'S'},
            {'AttributeName': 'GSI1SK', 'AttributeType': 'S'}
        ],
        GlobalSecondaryIndexes=[
            {
                'IndexName': 'GSI1',
                'KeySchema': [
                    {'AttributeName': 'GSI1PK', 'KeyType': 'HASH'},
                    {'AttributeName': 'GSI1SK', 'KeyType': 'RANGE'}
                ],
                'Projection': {'ProjectionType': 'ALL'},
                'BillingMode': 'PAY_PER_REQUEST'
            }
        ],
        BillingMode='PAY_PER_REQUEST'
    )
    
    return table


@pytest.fixture
@mock_s3
def s3_bucket(aws_credentials):
    """Create mocked S3 bucket for testing."""
    
    s3_client = boto3.client('s3', region_name='us-east-1')
    bucket_name = 'platform-test-bucket'
    
    s3_client.create_bucket(Bucket=bucket_name)
    return bucket_name


class TestTenantServiceIntegration:
    """Integration tests for TenantService."""
    
    @pytest.mark.asyncio
    async def test_create_tenant_complete_flow(self, dynamodb_table, s3_bucket):
        """Test complete tenant creation flow."""
        
        tenant_service = TenantService(
            table=dynamodb_table,
            s3_bucket=s3_bucket
        )
        
        tenant_data = {
            'company_name': 'Test Company',
            'industry': 'retail',
            'admin_user': {
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User'
            }
        }
        
        # Create tenant
        result = await tenant_service.create_tenant(tenant_data)
        
        # Verify tenant was created
        assert 'tenant_id' in result
        assert result['company_name'] == 'Test Company'
        
        # Verify database record
        response = dynamodb_table.get_item(
            Key={
                'PK': f"TENANT#{result['tenant_id']}",
                'SK': 'profile'
            }
        )
        
        assert 'Item' in response
        assert response['Item']['data']['company_name'] == 'Test Company'
        
        # Verify S3 structure was created
        s3_client = boto3.client('s3', region_name='us-east-1')
        objects = s3_client.list_objects_v2(
            Bucket=s3_bucket,
            Prefix=f"tenant-{result['tenant_id']}/"
        )
        
        assert 'Contents' in objects  # Folders were created
    
    @pytest.mark.asyncio
    async def test_tenant_suspension_flow(self, dynamodb_table):
        """Test tenant suspension and reactivation."""
        
        tenant_service = TenantService(table=dynamodb_table)
        
        # Create tenant first
        tenant_data = {
            'company_name': 'Test Company',
            'industry': 'retail'
        }
        
        tenant = await tenant_service.create_tenant(tenant_data)
        tenant_id = tenant['tenant_id']
        
        # Suspend tenant
        await tenant_service.suspend_tenant(tenant_id, reason='payment_failed')
        
        # Verify suspension
        suspended_tenant = await tenant_service.get_tenant(tenant_id)
        assert suspended_tenant['status'] == 'suspended'
        assert suspended_tenant['suspension_reason'] == 'payment_failed'
        
        # Reactivate tenant
        await tenant_service.reactivate_tenant(tenant_id)
        
        # Verify reactivation
        active_tenant = await tenant_service.get_tenant(tenant_id)
        assert active_tenant['status'] == 'active'
        assert 'suspension_reason' not in active_tenant
```

#### **Performance Testing**
```python
import pytest
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

class TestPerformance:
    """Performance tests for critical functions."""
    
    @pytest.mark.asyncio
    async def test_authentication_performance(self, auth_service):
        """Test authentication performance under load."""
        
        async def authenticate():
            """Single authentication attempt."""
            return await auth_service.authenticate_user('<EMAIL>', 'password123')
        
        # Measure single request performance
        start_time = time.time()
        await authenticate()
        single_request_time = time.time() - start_time
        
        # Should complete within 100ms
        assert single_request_time < 0.1
        
        # Test concurrent requests
        start_time = time.time()
        tasks = [authenticate() for _ in range(50)]
        await asyncio.gather(*tasks)
        concurrent_time = time.time() - start_time
        
        # 50 concurrent requests should complete within 2 seconds
        assert concurrent_time < 2.0
        
        # Average time per request should be reasonable
        avg_time = concurrent_time / 50
        assert avg_time < 0.2
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, user_service, dynamodb_table):
        """Test database query performance."""
        
        # Insert test data
        for i in range(100):
            await user_service.create_user('tenant_123', {
                'email': f'user{i}@example.com',
                'first_name': f'User{i}',
                'last_name': 'Test'
            })
        
        # Test query performance
        start_time = time.time()
        users = await user_service.get_tenant_users('tenant_123')
        query_time = time.time() - start_time
        
        # Query should complete quickly even with 100 users
        assert query_time < 0.5
        assert len(users) == 100
```

## 📦 Dependency Management

### **Requirements Management**
```ini
# requirements.txt - Production dependencies only
boto3==1.28.17
pydantic==2.1.1
httpx==0.24.1
cryptography==41.0.3
python-jose[cryptography]==3.3.0
bcrypt==4.0.1

# requirements-dev.txt - Development dependencies
-r requirements.txt
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.7.0
pylint==2.17.5
mypy==1.5.1
isort==5.12.0
bandit==1.7.5
safety==2.3.5
moto==4.2.2

# requirements-test.txt - Testing dependencies
-r requirements-dev.txt
factory-boy==3.3.0
freezegun==1.2.2
responses==0.23.3
```

### **Dependency Security**
```yaml
Security Scanning:
  Tools:
    - safety: Check for known vulnerabilities
    - bandit: Static security analysis
    - semgrep: Code pattern security scanning
  
  Policies:
    - No dependencies with known CVEs
    - Regular updates (monthly)
    - Version pinning for stability
    - License compatibility check

Automation:
  - Pre-commit hooks run security scans
  - CI pipeline fails on security issues
  - Automated dependency updates (Dependabot)
  - Security alerts for new vulnerabilities
```

## 🔧 Development Workflow

### **Git Workflow**
```yaml
Branching Strategy: GitFlow
  main: Production-ready code
  develop: Integration branch
  feature/*: Feature development
  release/*: Release preparation
  hotfix/*: Critical bug fixes

Branch Protection:
  - Require pull request reviews (2 reviewers)
  - Require status checks to pass
  - Require up-to-date branches
  - Restrict pushes to main/develop

Commit Standards:
  Format: "type(scope): description"
  Types: feat, fix, docs, style, refactor, test, chore
  
  Examples:
    - feat(auth): add JWT token refresh endpoint
    - fix(tenant): resolve data isolation bug
    - docs(api): update authentication documentation
    - test(user): add unit tests for user creation
```

### **Code Review Standards**
```yaml
Review Checklist:
  Functionality:
    - [ ] Code works as intended
    - [ ] Edge cases handled
    - [ ] Error handling appropriate
    - [ ] Performance considerations addressed
  
  Security:
    - [ ] Input validation implemented
    - [ ] SQL injection prevention
    - [ ] Authentication/authorization correct
    - [ ] Sensitive data properly handled
  
  Code Quality:
    - [ ] Code follows style guidelines
    - [ ] Functions are single responsibility
    - [ ] Variable names are descriptive
    - [ ] Comments explain "why" not "what"
  
  Testing:
    - [ ] Unit tests written and passing
    - [ ] Integration tests if needed
    - [ ] Test coverage meets requirements
    - [ ] Edge cases tested

Review Process:
  1. Self-review before requesting review
  2. Automated checks must pass
  3. Two approvals required for main/develop
  4. Address all feedback before merge
  5. Squash commits on merge
```

### **Continuous Integration**
```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
    
    - name: Lint with pylint
      run: pylint src/
    
    - name: Format check with black
      run: black --check src/
    
    - name: Import sort check
      run: isort --check-only src/
    
    - name: Type check with mypy
      run: mypy src/
    
    - name: Security scan with bandit
      run: bandit -r src/
    
    - name: Dependency security check
      run: safety check
    
    - name: Run tests
      run: |
        pytest --cov=src --cov-report=xml --cov-report=html
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        
  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/owasp-top-ten
```

## 📊 Monitoring & Observability

### **Logging Standards**
```python
import logging
import json
from datetime import datetime
from typing import Dict, Any

# Configure structured logging
class StructuredLogger:
    """Structured logger for Lambda functions."""
    
    def __init__(self, service_name: str, log_level: str = "INFO"):
        self.service_name = service_name
        self.logger = logging.getLogger(service_name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Remove default handlers
        self.logger.handlers.clear()
        
        # Add structured handler
        handler = logging.StreamHandler()
        handler.setFormatter(StructuredFormatter())
        self.logger.addHandler(handler)
    
    def _log(self, level: str, message: str, **kwargs):
        """Log structured message."""
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level,
            'service': self.service_name,
            'message': message,
            **kwargs
        }
        
        getattr(self.logger, level.lower())(json.dumps(log_data))
    
    def info(self, message: str, **kwargs):
        self._log('INFO', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log('WARNING', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log('ERROR', message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        self._log('DEBUG', message, **kwargs)


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record):
        return record.getMessage()


# Usage example
logger = StructuredLogger('auth-service')

async def authenticate_user(email: str, password: str):
    """Authenticate user with structured logging."""
    
    logger.info(
        "Authentication attempt started",
        email=email,
        request_id=get_request_id(),
        tenant_id=get_tenant_id()
    )
    
    try:
        user = await get_user_by_email(email)
        
        if not user:
            logger.warning(
                "Authentication failed - user not found",
                email=email,
                reason="user_not_found"
            )
            return None
        
        if not verify_password(password, user['password_hash']):
            logger.warning(
                "Authentication failed - invalid password",
                email=email,
                user_id=user['user_id'],
                reason="invalid_password"
            )
            return None
        
        logger.info(
            "Authentication successful",
            email=email,
            user_id=user['user_id'],
            tenant_id=user['tenant_id']
        )
        
        return generate_tokens(user)
        
    except Exception as e:
        logger.error(
            "Authentication error occurred",
            email=email,
            error=str(e),
            error_type=type(e).__name__
        )
        raise
```

### **Metrics & Monitoring**
```python
import boto3
from datetime import datetime
from typing import Dict, List

class MetricsCollector:
    """Custom metrics collector for CloudWatch."""
    
    def __init__(self, namespace: str = 'Platform/Backend'):
        self.cloudwatch = boto3.client('cloudwatch')
        self.namespace = namespace
        self.metrics_buffer = []
    
    def put_metric(self, metric_name: str, value: float, 
                   unit: str = 'Count', dimensions: Dict[str, str] = None):
        """Add metric to buffer."""
        
        metric_data = {
            'MetricName': metric_name,
            'Value': value,
            'Unit': unit,
            'Timestamp': datetime.utcnow()
        }
        
        if dimensions:
            metric_data['Dimensions'] = [
                {'Name': k, 'Value': v} for k, v in dimensions.items()
            ]
        
        self.metrics_buffer.append(metric_data)
        
        # Send metrics in batches of 20
        if len(self.metrics_buffer) >= 20:
            self.flush_metrics()
    
    def flush_metrics(self):
        """Send buffered metrics to CloudWatch."""
        if not self.metrics_buffer:
            return
        
        try:
            self.cloudwatch.put_metric_data(
                Namespace=self.namespace,
                MetricData=self.metrics_buffer
            )
            self.metrics_buffer.clear()
            
        except Exception as e:
            logging.error(f"Failed to send metrics: {str(e)}")


# Metrics decorator
def track_performance(metric_name: str):
    """Decorator to track function performance."""
    
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            metrics = MetricsCollector()
            
            try:
                result = await func(*args, **kwargs)
                
                # Track success
                metrics.put_metric(
                    f"{metric_name}_success",
                    1,
                    dimensions={'function': func.__name__}
                )
                
                return result
                
            except Exception as e:
                # Track error
                metrics.put_metric(
                    f"{metric_name}_error",
                    1,
                    dimensions={
                        'function': func.__name__,
                        'error_type': type(e).__name__
                    }
                )
                raise
                
            finally:
                # Track duration
                duration = time.time() - start_time
                metrics.put_metric(
                    f"{metric_name}_duration",
                    duration * 1000,  # Convert to milliseconds
                    unit='Milliseconds',
                    dimensions={'function': func.__name__}
                )
                
                metrics.flush_metrics()
        
        return wrapper
    return decorator

# Usage example
@track_performance('user_authentication')
async def authenticate_user(email: str, password: str):
    """Authenticate user with performance tracking."""
    # Implementation here
    pass
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Development Team  
**Review Cycle:** Weekly during development, monthly post-launch