# Integration Specifications (n8n + Payments)

## 🔌 Integration Overview

### **External Integration Strategy**
- **n8n Agent Integration:** HTTP API calls to existing workflows
- **Payment Processing:** Stripe primary, PayU secondary
- **Email Services:** Amazon SES for transactional emails
- **Event-Driven Architecture:** Webhooks and async processing
- **Circuit Breaker Pattern:** Resilience for external dependencies
- **Retry Logic:** Exponential backoff for failed requests

## 🤖 n8n Agent Integration

### **Current n8n Architecture**
```
n8n Workflow Engine
├── Feedo Agent (Data Ingestion)
│   ├── Input: User messages + file uploads
│   ├── Processing: Data validation + normalization
│   └── Output: Processed data + status updates
└── Forecaster Agent (Predictive Analytics)
    ├── Input: User queries + processed data
    ├── Processing: ML analysis + dashboard generation
    └── Output: Insights + reports + dashboards
```

### **Integration Endpoints**

#### **n8n Webhook Configuration**
```yaml
Base URL: https://n8n.company.com/webhook/
Endpoints:
  - /feedo: Data ingestion agent
  - /forecaster: Predictive analytics agent
  
Authentication: Bearer token
Timeout: 30 seconds (Feedo), 60 seconds (Forecaster)
Rate Limits: 100 requests/minute per tenant
```

### **Agent Proxy Service**

#### **Feedo Agent Integration**

**Request Format:**
```json
{
  "tenant_id": "tenant_123",
  "user_id": "user_456", 
  "conversation_id": "conv_789",
  "message_id": "msg_101",
  "timestamp": "2025-07-16T10:30:00Z",
  "user_message": {
    "text": "I need the retail inventory template",
    "intent": "request_template|upload_data|process_file",
    "attachments": [
      {
        "type": "file",
        "name": "inventory.xlsx",
        "url": "https://s3.amazonaws.com/tenant-123/uploads/inventory.xlsx",
        "size": 1048576,
        "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      }
    ]
  },
  "context": {
    "conversation_history": "summary_of_previous_messages",
    "user_preferences": {
      "industry": "retail",
      "warehouse_count": 3,
      "product_categories": ["electronics", "clothing"]
    }
  }
}
```

**Response Format:**
```json
{
  "success": true,
  "agent_id": "feedo",
  "processing_time_ms": 2500,
  "response": {
    "text": "I've prepared your retail inventory template. Please download it below and fill in your data.",
    "intent": "template_provided|data_processed|error_occurred",
    "attachments": [
      {
        "type": "template",
        "name": "retail_inventory_template.xlsx",
        "url": "https://s3.amazonaws.com/tenant-123/templates/retail_inventory_template.xlsx",
        "description": "Retail inventory template with required columns"
      }
    ],
    "suggested_actions": [
      {
        "action": "download_template",
        "label": "Download Template",
        "url": "https://s3.amazonaws.com/tenant-123/templates/retail_inventory_template.xlsx"
      },
      {
        "action": "view_examples", 
        "label": "View Examples",
        "url": "https://docs.platform.com/examples/retail"
      }
    ]
  },
  "metadata": {
    "data_processed": {
      "records_count": 0,
      "processing_status": "template_generated",
      "file_location": null
    },
    "cost_units": 50,
    "confidence_score": 0.98
  }
}
```

#### **Forecaster Agent Integration**

**Request Format:**
```json
{
  "tenant_id": "tenant_123",
  "user_id": "user_456",
  "conversation_id": "conv_789", 
  "message_id": "msg_102",
  "timestamp": "2025-07-16T10:35:00Z",
  "user_message": {
    "text": "Analyze Q3 inventory trends and predict demand for next quarter",
    "intent": "demand_forecast|reorder_analysis|inventory_optimization",
    "parameters": {
      "analysis_period": "Q3_2025",
      "forecast_period": "Q4_2025",
      "product_categories": ["electronics"],
      "warehouse_locations": ["warehouse_01", "warehouse_02"]
    }
  },
  "context": {
    "data_sources": [
      {
        "type": "processed_inventory",
        "location": "s3://tenant-123/processed/inventory_q3.parquet",
        "records_count": 500000,
        "last_updated": "2025-07-16T09:00:00Z"
      }
    ],
    "previous_analysis": {
      "baseline_accuracy": 0.85,
      "seasonal_patterns": "high_q4_demand"
    }
  }
}
```

**Response Format:**
```json
{
  "success": true,
  "agent_id": "forecaster", 
  "processing_time_ms": 15000,
  "response": {
    "text": "Based on Q3 data analysis, I predict a 23% increase in electronics demand for Q4. Here's the detailed analysis:",
    "intent": "analysis_complete",
    "insights": [
      {
        "type": "demand_forecast",
        "category": "electronics",
        "predicted_increase": 23,
        "confidence": 0.87,
        "key_drivers": ["seasonal_patterns", "market_trends"]
      },
      {
        "type": "reorder_recommendation",
        "products": [
          {
            "sku": "ELECT_001",
            "current_stock": 1500,
            "recommended_order": 2000,
            "reorder_point": 800,
            "safety_stock": 300
          }
        ]
      }
    ],
    "attachments": [
      {
        "type": "dashboard",
        "name": "Q4 Demand Forecast Dashboard", 
        "url": "https://dashboard.platform.com/tenant-123/forecast-q4",
        "description": "Interactive dashboard with detailed forecasts"
      },
      {
        "type": "report",
        "name": "Q4_Forecast_Report.pdf",
        "url": "https://s3.amazonaws.com/tenant-123/reports/Q4_Forecast_Report.pdf",
        "description": "Comprehensive PDF report with recommendations"
      }
    ],
    "suggested_actions": [
      {
        "action": "schedule_meeting",
        "label": "Schedule Review Meeting",
        "data": {
          "subject": "Q4 Inventory Planning Review",
          "suggested_attendees": ["<EMAIL>"],
          "duration": 60
        }
      },
      {
        "action": "send_email_report",
        "label": "Email Report to Team",
        "data": {
          "recipients": ["<EMAIL>", "<EMAIL>"],
          "subject": "Q4 Demand Forecast Analysis"
        }
      }
    ]
  },
  "metadata": {
    "data_processed": {
      "records_analyzed": 500000,
      "processing_status": "analysis_complete",
      "accuracy_score": 0.87,
      "model_version": "forecaster_v2.1"
    },
    "cost_units": 250,
    "confidence_score": 0.87
  }
}
```

### **Agent Proxy Implementation**

#### **Python Agent Connector**
```python
import aiohttp
import asyncio
from typing import Dict, Optional
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

class N8NAgentConnector:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={'Authorization': f'Bearer {self.api_key}'}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def call_agent(self, agent_name: str, payload: Dict) -> Dict:
        """Call n8n agent with retry logic"""
        url = f"{self.base_url}/webhook/{agent_name}"
        
        try:
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    # Rate limited - wait and retry
                    await asyncio.sleep(int(response.headers.get('Retry-After', 60)))
                    raise aiohttp.ClientError("Rate limited")
                else:
                    error_text = await response.text()
                    raise aiohttp.ClientError(f"HTTP {response.status}: {error_text}")
                    
        except asyncio.TimeoutError:
            logging.error(f"Timeout calling {agent_name} agent")
            raise
        except Exception as e:
            logging.error(f"Error calling {agent_name} agent: {str(e)}")
            raise

    async def call_feedo(self, tenant_id: str, user_id: str, 
                        conversation_id: str, message: Dict) -> Dict:
        """Call Feedo data ingestion agent"""
        payload = {
            'tenant_id': tenant_id,
            'user_id': user_id,
            'conversation_id': conversation_id,
            'message_id': message.get('message_id'),
            'timestamp': message.get('timestamp'),
            'user_message': message,
            'context': {
                'conversation_history': await self.get_conversation_summary(conversation_id),
                'user_preferences': await self.get_user_preferences(tenant_id, user_id)
            }
        }
        
        return await self.call_agent('feedo', payload)

    async def call_forecaster(self, tenant_id: str, user_id: str,
                             conversation_id: str, message: Dict) -> Dict:
        """Call Forecaster analytics agent"""
        payload = {
            'tenant_id': tenant_id,
            'user_id': user_id, 
            'conversation_id': conversation_id,
            'message_id': message.get('message_id'),
            'timestamp': message.get('timestamp'),
            'user_message': message,
            'context': {
                'data_sources': await self.get_tenant_data_sources(tenant_id),
                'previous_analysis': await self.get_previous_analysis(tenant_id)
            }
        }
        
        return await self.call_agent('forecaster', payload)
```

#### **Circuit Breaker Implementation**
```python
from enum import Enum
import time
from typing import Callable, Any

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open" 
    HALF_OPEN = "half_open"

class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        if self.state == CircuitState.OPEN:
            if time.time() - self.last_failure_time > self.timeout:
                self.state = CircuitState.HALF_OPEN
                self.failure_count = 0
            else:
                raise Exception("Circuit breaker is OPEN")
                
        try:
            result = await func(*args, **kwargs)
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.CLOSED
            self.failure_count = 0
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
                
            raise e
```

## 💳 Payment Gateway Integration

### **Stripe Integration**

#### **Subscription Management**
```python
import stripe
from typing import Dict, Optional

class StripeService:
    def __init__(self, api_key: str, webhook_secret: str):
        stripe.api_key = api_key
        self.webhook_secret = webhook_secret
        
    async def create_customer(self, tenant_data: Dict) -> stripe.Customer:
        """Create Stripe customer for tenant"""
        return stripe.Customer.create(
            email=tenant_data['admin_email'],
            name=tenant_data['company_name'],
            metadata={
                'tenant_id': tenant_data['tenant_id'],
                'company_name': tenant_data['company_name'],
                'industry': tenant_data.get('industry')
            },
            address={
                'line1': tenant_data.get('address', {}).get('street'),
                'city': tenant_data.get('address', {}).get('city'),
                'state': tenant_data.get('address', {}).get('state'),
                'postal_code': tenant_data.get('address', {}).get('zip'),
                'country': tenant_data.get('address', {}).get('country', 'US')
            }
        )
    
    async def create_subscription(self, customer_id: str, plan_id: str, 
                                 payment_method_id: str) -> stripe.Subscription:
        """Create subscription for customer"""
        # Attach payment method to customer
        stripe.PaymentMethod.attach(
            payment_method_id,
            customer=customer_id
        )
        
        # Set as default payment method
        stripe.Customer.modify(
            customer_id,
            invoice_settings={'default_payment_method': payment_method_id}
        )
        
        # Create subscription
        return stripe.Subscription.create(
            customer=customer_id,
            items=[{'price': plan_id}],
            payment_behavior='default_incomplete',
            expand=['latest_invoice.payment_intent'],
            metadata={
                'tenant_id': await self.get_tenant_from_customer(customer_id)
            }
        )
    
    async def change_subscription_plan(self, subscription_id: str, 
                                      new_plan_id: str) -> stripe.Subscription:
        """Change subscription plan with proration"""
        subscription = stripe.Subscription.retrieve(subscription_id)
        
        return stripe.Subscription.modify(
            subscription_id,
            items=[{
                'id': subscription['items']['data'][0]['id'],
                'price': new_plan_id,
            }],
            proration_behavior='create_prorations',
            metadata={
                'plan_change_date': int(time.time()),
                'previous_plan': subscription['items']['data'][0]['price']['id']
            }
        )
    
    async def cancel_subscription(self, subscription_id: str, 
                                 immediately: bool = False) -> stripe.Subscription:
        """Cancel subscription"""
        if immediately:
            return stripe.Subscription.delete(subscription_id)
        else:
            return stripe.Subscription.modify(
                subscription_id,
                cancel_at_period_end=True,
                metadata={'cancellation_requested_at': int(time.time())}
            )
```

#### **Webhook Handling**
```python
import stripe
import json
from flask import request

class StripeWebhookHandler:
    def __init__(self, webhook_secret: str):
        self.webhook_secret = webhook_secret
        
    def verify_webhook(self, payload: bytes, sig_header: str) -> stripe.Event:
        """Verify webhook signature"""
        try:
            return stripe.Webhook.construct_event(
                payload, sig_header, self.webhook_secret
            )
        except ValueError:
            raise Exception("Invalid payload")
        except stripe.error.SignatureVerificationError:
            raise Exception("Invalid signature")
    
    async def handle_webhook(self, event: stripe.Event) -> Dict:
        """Route webhook to appropriate handler"""
        handlers = {
            'customer.subscription.created': self.handle_subscription_created,
            'customer.subscription.updated': self.handle_subscription_updated,
            'customer.subscription.deleted': self.handle_subscription_deleted,
            'invoice.payment_succeeded': self.handle_payment_succeeded,
            'invoice.payment_failed': self.handle_payment_failed,
            'payment_method.attached': self.handle_payment_method_attached,
        }
        
        handler = handlers.get(event['type'])
        if handler:
            return await handler(event['data']['object'])
        else:
            return {'status': 'unhandled_event', 'type': event['type']}
    
    async def handle_payment_succeeded(self, invoice: Dict) -> Dict:
        """Handle successful payment"""
        tenant_id = invoice['metadata'].get('tenant_id')
        subscription_id = invoice['subscription']
        
        # Update subscription status in database
        await self.update_subscription_status(tenant_id, 'active')
        
        # Resume tenant services if suspended
        await self.resume_tenant_services(tenant_id)
        
        # Send payment confirmation email
        await self.send_payment_confirmation(tenant_id, invoice)
        
        return {'status': 'payment_processed', 'tenant_id': tenant_id}
    
    async def handle_payment_failed(self, invoice: Dict) -> Dict:
        """Handle failed payment"""
        tenant_id = invoice['metadata'].get('tenant_id')
        attempt_count = invoice['attempt_count']
        
        if attempt_count >= 3:
            # Suspend tenant services after 3 failed attempts
            await self.suspend_tenant_services(tenant_id)
            await self.send_suspension_notice(tenant_id)
        else:
            # Send payment failure notice
            await self.send_payment_failure_notice(tenant_id, attempt_count)
        
        return {'status': 'payment_failed', 'tenant_id': tenant_id, 'attempts': attempt_count}
```

### **PayU Integration (Secondary)**

#### **PayU Service Implementation**
```python
import httpx
import hashlib
from typing import Dict

class PayUService:
    def __init__(self, api_login: str, api_key: str, merchant_id: str, 
                 account_id: str, environment: str = 'sandbox'):
        self.api_login = api_login
        self.api_key = api_key
        self.merchant_id = merchant_id
        self.account_id = account_id
        self.base_url = 'https://sandbox.api.payulatam.com' if environment == 'sandbox' \
                       else 'https://api.payulatam.com'
    
    def generate_signature(self, api_key: str, merchant_id: str, 
                          reference: str, amount: str, currency: str) -> str:
        """Generate PayU signature"""
        signature_string = f"{api_key}~{merchant_id}~{reference}~{amount}~{currency}"
        return hashlib.md5(signature_string.encode()).hexdigest()
    
    async def create_payment(self, payment_data: Dict) -> Dict:
        """Create payment in PayU"""
        signature = self.generate_signature(
            self.api_key,
            self.merchant_id,
            payment_data['reference'],
            payment_data['amount'],
            payment_data['currency']
        )
        
        payload = {
            "language": "en",
            "command": "SUBMIT_TRANSACTION",
            "merchant": {
                "apiLogin": self.api_login,
                "apiKey": self.api_key
            },
            "transaction": {
                "order": {
                    "accountId": self.account_id,
                    "referenceCode": payment_data['reference'],
                    "description": payment_data['description'],
                    "language": "en",
                    "signature": signature,
                    "notifyUrl": payment_data['notify_url'],
                    "additionalValues": {
                        "TX_VALUE": {
                            "value": payment_data['amount'],
                            "currency": payment_data['currency']
                        }
                    },
                    "buyer": payment_data['buyer'],
                    "shippingAddress": payment_data['shipping_address']
                },
                "payer": payment_data['payer'],
                "creditCard": payment_data['credit_card'],
                "type": "AUTHORIZATION_AND_CAPTURE",
                "paymentMethod": payment_data['payment_method'],
                "paymentCountry": payment_data.get('country', 'CO'),
                "deviceSessionId": payment_data.get('device_session_id'),
                "ipAddress": payment_data.get('ip_address'),
                "cookie": payment_data.get('cookie'),
                "userAgent": payment_data.get('user_agent')
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/payments-api/4.0/service.cgi",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
        return response.json()
```

## 📧 Email Service Integration

### **Amazon SES Configuration**
```python
import boto3
from botocore.exceptions import ClientError
from jinja2 import Template
import json

class EmailService:
    def __init__(self, region: str = 'us-east-1'):
        self.ses_client = boto3.client('ses', region_name=region)
        self.from_email = '<EMAIL>'
        
    async def send_email(self, to_email: str, subject: str, 
                        html_content: str, text_content: str = None) -> Dict:
        """Send email via SES"""
        try:
            response = self.ses_client.send_email(
                Source=self.from_email,
                Destination={'ToAddresses': [to_email]},
                Message={
                    'Subject': {'Data': subject, 'Charset': 'UTF-8'},
                    'Body': {
                        'Html': {'Data': html_content, 'Charset': 'UTF-8'},
                        'Text': {'Data': text_content or '', 'Charset': 'UTF-8'}
                    }
                }
            )
            return {'success': True, 'message_id': response['MessageId']}
            
        except ClientError as e:
            return {'success': False, 'error': str(e)}
    
    async def send_welcome_email(self, tenant_id: str, user_data: Dict) -> Dict:
        """Send welcome email to new user"""
        template = Template("""
        <h1>Welcome to Platform, {{ user_name }}!</h1>
        <p>Your account has been successfully created for {{ company_name }}.</p>
        <p>Please verify your email address by clicking the link below:</p>
        <a href="{{ verification_link }}">Verify Email Address</a>
        <p>Your verification code is: <strong>{{ verification_code }}</strong></p>
        """)
        
        html_content = template.render(
            user_name=f"{user_data['first_name']} {user_data['last_name']}",
            company_name=user_data['company_name'],
            verification_link=f"https://app.platform.com/verify?code={user_data['verification_code']}",
            verification_code=user_data['verification_code']
        )
        
        return await self.send_email(
            user_data['email'],
            'Welcome to Platform - Please Verify Your Email',
            html_content
        )
    
    async def send_payment_confirmation(self, tenant_id: str, invoice_data: Dict) -> Dict:
        """Send payment confirmation email"""
        template = Template("""
        <h1>Payment Confirmation</h1>
        <p>Thank you for your payment!</p>
        <p><strong>Invoice #:</strong> {{ invoice_number }}</p>
        <p><strong>Amount:</strong> ${{ amount }}</p>
        <p><strong>Plan:</strong> {{ plan_name }}</p>
        <p><strong>Next Billing Date:</strong> {{ next_billing_date }}</p>
        <p>You can download your invoice <a href="{{ invoice_url }}">here</a>.</p>
        """)
        
        # Get tenant admin email
        admin_email = await self.get_tenant_admin_email(tenant_id)
        
        html_content = template.render(**invoice_data)
        
        return await self.send_email(
            admin_email,
            f'Payment Confirmation - Invoice #{invoice_data["invoice_number"]}',
            html_content
        )
```

## 🔄 Event Processing & Workflows

### **Tenant Registration SAGA**
```python
from enum import Enum
import asyncio

class SagaState(Enum):
    STARTED = "started"
    COMPANY_CREATED = "company_created"
    USER_CREATED = "user_created"
    PAYMENT_PROCESSED = "payment_processed"
    RESOURCES_PROVISIONED = "resources_provisioned"
    EMAILS_SENT = "emails_sent"
    COMPLETED = "completed"
    FAILED = "failed"

class TenantRegistrationSaga:
    def __init__(self):
        self.state = SagaState.STARTED
        self.compensation_actions = []
        
    async def execute(self, registration_data: Dict) -> Dict:
        """Execute tenant registration saga"""
        try:
            # Step 1: Create tenant record
            tenant = await self.create_tenant(registration_data['company'])
            self.compensation_actions.append(('delete_tenant', tenant['tenant_id']))
            self.state = SagaState.COMPANY_CREATED
            
            # Step 2: Create admin user
            user = await self.create_user(registration_data['user'], tenant['tenant_id'])
            self.compensation_actions.append(('delete_user', user['user_id']))
            self.state = SagaState.USER_CREATED
            
            # Step 3: Process payment
            payment = await self.process_payment(registration_data['payment'], tenant['tenant_id'])
            self.compensation_actions.append(('refund_payment', payment['payment_id']))
            self.state = SagaState.PAYMENT_PROCESSED
            
            # Step 4: Provision tenant resources
            resources = await self.provision_resources(tenant['tenant_id'])
            self.compensation_actions.append(('deprovision_resources', tenant['tenant_id']))
            self.state = SagaState.RESOURCES_PROVISIONED
            
            # Step 5: Send welcome emails
            await self.send_welcome_emails(user['email'], tenant['tenant_id'])
            self.state = SagaState.EMAILS_SENT
            
            self.state = SagaState.COMPLETED
            return {'success': True, 'tenant_id': tenant['tenant_id'], 'user_id': user['user_id']}
            
        except Exception as e:
            await self.compensate()
            self.state = SagaState.FAILED
            return {'success': False, 'error': str(e)}
    
    async def compensate(self):
        """Execute compensation actions in reverse order"""
        for action, resource_id in reversed(self.compensation_actions):
            try:
                await self.execute_compensation(action, resource_id)
            except Exception as e:
                logging.error(f"Compensation failed for {action}: {str(e)}")
```

### **Usage Tracking & Billing**
```python
import asyncio
from datetime import datetime, timedelta

class UsageTracker:
    def __init__(self, dynamodb_table, redis_client):
        self.table = dynamodb_table
        self.redis = redis_client
        
    async def track_conversation(self, tenant_id: str, user_id: str, 
                                agent_id: str, cost_units: int):
        """Track conversation usage"""
        today = datetime.utcnow().strftime('%Y-%m-%d')
        
        # Update real-time counters in Redis
        await self.redis.hincrby(f"usage:{tenant_id}:{today}", "conversations", 1)
        await self.redis.hincrby(f"usage:{tenant_id}:{today}", f"agent_{agent_id}", cost_units)
        await self.redis.expire(f"usage:{tenant_id}:{today}", 86400 * 32)  # 32 days
        
        # Batch update to DynamoDB (async)
        await self.queue_usage_update(tenant_id, today, {
            'conversations_started': 1,
            'cost_units': cost_units,
            'agent_usage': {agent_id: cost_units}
        })
    
    async def check_usage_limits(self, tenant_id: str) -> Dict:
        """Check if tenant is within usage limits"""
        subscription = await self.get_tenant_subscription(tenant_id)
        today = datetime.utcnow().strftime('%Y-%m-%d')
        
        # Get current month usage
        month_start = datetime.utcnow().replace(day=1).strftime('%Y-%m-%d')
        usage = await self.get_usage_range(tenant_id, month_start, today)
        
        limits = subscription['plan_features']
        overages = {}
        
        if usage['conversations'] > limits['max_conversations_monthly']:
            overages['conversations'] = usage['conversations'] - limits['max_conversations_monthly']
            
        if usage['cost_units'] > limits['max_cost_units_monthly']:
            overages['cost_units'] = usage['cost_units'] - limits['max_cost_units_monthly']
        
        return {
            'within_limits': len(overages) == 0,
            'usage': usage,
            'limits': limits,
            'overages': overages
        }
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Integration Architecture Team  
**Review Cycle:** Weekly during development, bi-weekly post-launch