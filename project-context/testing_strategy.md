# Testing Strategy & Quality Assurance

## 🧪 Testing Overview

### **Testing Philosophy**
- **Shift-Left Testing:** Test early and often in the development cycle
- **Risk-Based Testing:** Prioritize testing based on business risk
- **Automation-First:** Automate repetitive and regression tests
- **Continuous Testing:** Integrate testing into CI/CD pipelines
- **Quality Gates:** Enforce quality standards at each stage
- **Performance by Design:** Include performance testing from the start

### **Testing Pyramid Strategy**
```
                    /\
                   /  \
                  / E2E \         10% - End-to-End Tests
                 /______\
                /        \
               / Integration \    20% - Integration Tests
              /______________\
             /                \
            /   Unit Tests     \   70% - Unit Tests
           /____________________\
```

## 🔬 Unit Testing Strategy

### **Current Coverage Status & Targets**
```yaml
Current Coverage (Phase 6):
  Overall: 16% (significant improvement from 2%)
  Shared Layer: 16% (new modules implemented)
  Auth Module: 58% (comprehensive testing)
  Error Handling: 38% (new error handler)
  Validation: 20% (new validation manager)

Target Coverage Goals:
  Overall: 80% (realistic target)
  Critical Functions: 90%
  Business Logic: 85%
  Error Handlers: 80%
  Shared Utilities: 70%

Coverage Exemptions:
  - Third-party integrations (mocked)
  - Configuration files
  - Lambda entry points (tested via integration)
  - Deprecated or unused code
```

### **Unit Test Structure**
```python
# tests/unit/test_auth_service.py
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from freezegun import freeze_time
from decimal import Decimal

from src.services.auth_service import AuthenticationService
from src.exceptions import AuthenticationError, ValidationError
from tests.factories import UserFactory, TenantFactory


class TestAuthenticationService:
    """Comprehensive unit tests for AuthenticationService."""
    
    @pytest.fixture
    def auth_service(self):
        """Create authentication service with mocked dependencies."""
        with patch('src.services.auth_service.DynamoDBClient') as mock_db, \
             patch('src.services.auth_service.EmailService') as mock_email:
            
            service = AuthenticationService(
                jwt_secret="test-secret",
                token_expiry=3600
            )
            service.db_client = mock_db.return_value
            service.email_service = mock_email.return_value
            return service
    
    @pytest.fixture
    def sample_user(self):
        """Create sample user data."""
        return UserFactory.build(
            user_id="user_123",
            email="<EMAIL>",
            status="active",
            tenant_id="tenant_456"
        )
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, sample_user):
        """Test successful user authentication flow."""
        # Arrange
        auth_service.db_client.get_user_by_email = AsyncMock(return_value=sample_user)
        auth_service._verify_password = AsyncMock(return_value=True)
        auth_service._generate_tokens = AsyncMock(return_value={
            'access_token': 'access_123',
            'refresh_token': 'refresh_456'
        })
        auth_service._log_successful_login = AsyncMock()
        
        # Act
        result = await auth_service.authenticate_user("<EMAIL>", "password123")
        
        # Assert
        assert result is not None
        assert result['access_token'] == 'access_123'
        assert result['refresh_token'] == 'refresh_456'
        
        # Verify interactions
        auth_service.db_client.get_user_by_email.assert_called_once_with("<EMAIL>")
        auth_service._verify_password.assert_called_once_with("password123", sample_user['password_hash'])
        auth_service._log_successful_login.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_email_format(self, auth_service):
        """Test authentication with invalid email format."""
        with pytest.raises(ValidationError) as exc_info:
            await auth_service.authenticate_user("invalid-email", "password123")
        
        assert "Invalid email format" in str(exc_info.value)
        
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, auth_service):
        """Test authentication with non-existent user."""
        # Arrange
        auth_service.db_client.get_user_by_email = AsyncMock(return_value=None)
        
        # Act
        result = await auth_service.authenticate_user("<EMAIL>", "password123")
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self, auth_service, sample_user):
        """Test authentication with incorrect password."""
        # Arrange
        auth_service.db_client.get_user_by_email = AsyncMock(return_value=sample_user)
        auth_service._verify_password = AsyncMock(return_value=False)
        auth_service._log_failed_login = AsyncMock()
        
        # Act
        result = await auth_service.authenticate_user("<EMAIL>", "wrongpassword")
        
        # Assert
        assert result is None
        auth_service._log_failed_login.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive_status(self, auth_service, sample_user):
        """Test authentication with inactive user account."""
        # Arrange
        sample_user['status'] = 'inactive'
        auth_service.db_client.get_user_by_email = AsyncMock(return_value=sample_user)
        
        # Act
        result = await auth_service.authenticate_user("<EMAIL>", "password123")
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_database_error(self, auth_service):
        """Test authentication with database connection error."""
        # Arrange
        auth_service.db_client.get_user_by_email = AsyncMock(
            side_effect=Exception("Database connection failed")
        )
        
        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await auth_service.authenticate_user("<EMAIL>", "password123")
        
        assert "Database connection failed" in str(exc_info.value)
    
    @pytest.mark.parametrize("email,expected_valid", [
        ("<EMAIL>", True),
        ("<EMAIL>", True),
        ("<EMAIL>", True),
        ("invalid.email", False),
        ("@example.com", False),
        ("user@", False),
        ("", False),
        ("user@.com", False)
    ])
    def test_email_validation(self, email, expected_valid):
        """Test email validation with various inputs."""
        result = AuthenticationService._is_valid_email(email)
        assert result == expected_valid
    
    @freeze_time("2025-07-16 10:30:00")
    @pytest.mark.asyncio
    async def test_generate_tokens_with_frozen_time(self, auth_service, sample_user):
        """Test token generation with fixed timestamp."""
        # Act
        tokens = await auth_service._generate_tokens(sample_user)
        
        # Assert
        assert 'access_token' in tokens
        assert 'refresh_token' in tokens
        assert 'expires_in' in tokens
        assert tokens['expires_in'] == 3600
    
    def test_password_hashing_and_verification(self, auth_service):
        """Test password hashing and verification."""
        password = "testpassword123"
        
        # Hash password
        hashed = auth_service._hash_password(password)
        
        # Verify correct password
        assert auth_service._verify_password_sync(password, hashed) is True
        
        # Verify incorrect password
        assert auth_service._verify_password_sync("wrongpassword", hashed) is False


# Test data factories
class UserFactory:
    """Factory for creating test user data."""
    
    @staticmethod
    def build(**kwargs):
        """Build user data with overrides."""
        default_data = {
            'user_id': f'user_{secrets.token_hex(4)}',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'status': 'active',
            'tenant_id': f'tenant_{secrets.token_hex(4)}',
            'role': 'MEMBER',
            'password_hash': '$2b$12$hashed_password',
            'created_at': datetime.utcnow().isoformat(),
            'permissions': ['agents:read']
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def create_batch(count=5, **kwargs):
        """Create multiple users."""
        return [UserFactory.build(**kwargs) for _ in range(count)]


class TenantFactory:
    """Factory for creating test tenant data."""
    
    @staticmethod
    def build(**kwargs):
        """Build tenant data with overrides."""
        default_data = {
            'tenant_id': f'tenant_{secrets.token_hex(4)}',
            'company_name': 'Test Company',
            'industry': 'retail',
            'status': 'active',
            'created_at': datetime.utcnow().isoformat(),
            'plan': 'standard'
        }
        default_data.update(kwargs)
        return default_data
```

### **Performance Unit Tests**
```python
# tests/unit/test_performance.py
import pytest
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

class TestPerformanceRequirements:
    """Performance tests for critical functions."""
    
    @pytest.mark.asyncio
    async def test_user_authentication_performance(self, auth_service):
        """Test authentication performance requirements."""
        
        # Single request should complete within 100ms
        start_time = time.time()
        await auth_service.authenticate_user("<EMAIL>", "password123")
        single_request_time = time.time() - start_time
        
        assert single_request_time < 0.1, f"Authentication took {single_request_time:.3f}s, expected <0.1s"
    
    @pytest.mark.asyncio
    async def test_concurrent_authentication_performance(self, auth_service):
        """Test concurrent authentication performance."""
        
        async def authenticate():
            return await auth_service.authenticate_user("<EMAIL>", "password123")
        
        # 50 concurrent requests should complete within 2 seconds
        start_time = time.time()
        tasks = [authenticate() for _ in range(50)]
        await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        assert total_time < 2.0, f"50 concurrent requests took {total_time:.3f}s, expected <2.0s"
        
        # Average time per request should be reasonable
        avg_time = total_time / 50
        assert avg_time < 0.2, f"Average time per request: {avg_time:.3f}s, expected <0.2s"
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, user_service):
        """Test database query performance with large datasets."""
        
        # Create test data
        users = UserFactory.create_batch(100)
        for user in users:
            await user_service.create_user(user['tenant_id'], user)
        
        # Query should complete quickly
        start_time = time.time()
        result = await user_service.get_tenant_users('tenant_123', limit=100)
        query_time = time.time() - start_time
        
        assert query_time < 0.5, f"Query took {query_time:.3f}s, expected <0.5s"
        assert len(result) <= 100
```

## 🔄 Integration Testing Strategy

### **Testing Scope**
```yaml
Integration Test Coverage:
  - Service-to-service communication
  - Database operations with real AWS services
  - External API integrations (mocked)
  - Event-driven workflows
  - Authentication/authorization flows
  - File upload and processing pipelines

Tools:
  - LocalStack: AWS services simulation
  - TestContainers: Containerized dependencies
  - Moto: AWS service mocking
  - pytest-asyncio: Async test support
```

### **Integration Test Implementation**
```python
# tests/integration/test_user_registration_flow.py
import pytest
import boto3
import asyncio
from moto import mock_dynamodb, mock_s3, mock_ses
from decimal import Decimal

from src.services.tenant_service import TenantService
from src.services.user_service import UserService
from src.services.payment_service import PaymentService
from src.services.email_service import EmailService


@pytest.fixture(scope="function")
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    import os
    os.environ['AWS_ACCESS_KEY_ID'] = 'testing'
    os.environ['AWS_SECRET_ACCESS_KEY'] = 'testing'
    os.environ['AWS_SECURITY_TOKEN'] = 'testing'
    os.environ['AWS_SESSION_TOKEN'] = 'testing'


@pytest.fixture
@mock_dynamodb
def dynamodb_setup(aws_credentials):
    """Set up DynamoDB for testing."""
    dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
    
    # Create table with all required attributes
    table = dynamodb.create_table(
        TableName='platform-test',
        KeySchema=[
            {'AttributeName': 'PK', 'KeyType': 'HASH'},
            {'AttributeName': 'SK', 'KeyType': 'RANGE'}
        ],
        AttributeDefinitions=[
            {'AttributeName': 'PK', 'AttributeType': 'S'},
            {'AttributeName': 'SK', 'AttributeType': 'S'},
            {'AttributeName': 'GSI1PK', 'AttributeType': 'S'},
            {'AttributeName': 'GSI1SK', 'AttributeType': 'S'}
        ],
        GlobalSecondaryIndexes=[
            {
                'IndexName': 'GSI1',
                'KeySchema': [
                    {'AttributeName': 'GSI1PK', 'KeyType': 'HASH'},
                    {'AttributeName': 'GSI1SK', 'KeyType': 'RANGE'}
                ],
                'Projection': {'ProjectionType': 'ALL'},
                'BillingMode': 'PAY_PER_REQUEST'
            }
        ],
        BillingMode='PAY_PER_REQUEST'
    )
    
    return table


@pytest.fixture
@mock_s3
def s3_setup(aws_credentials):
    """Set up S3 for testing."""
    s3_client = boto3.client('s3', region_name='us-east-1')
    bucket_name = 'platform-test-bucket'
    s3_client.create_bucket(Bucket=bucket_name)
    return bucket_name


@pytest.fixture
@mock_ses
def ses_setup(aws_credentials):
    """Set up SES for testing."""
    ses_client = boto3.client('ses', region_name='us-east-1')
    
    # Verify email addresses for testing
    ses_client.verify_email_identity(EmailAddress='<EMAIL>')
    ses_client.verify_email_identity(EmailAddress='<EMAIL>')
    
    return ses_client


class TestUserRegistrationIntegration:
    """Integration tests for complete user registration flow."""
    
    @pytest.mark.asyncio
    async def test_complete_registration_flow(self, dynamodb_setup, s3_setup, ses_setup):
        """Test end-to-end user registration process."""
        
        # Initialize services
        tenant_service = TenantService(table=dynamodb_setup, s3_bucket=s3_setup)
        user_service = UserService(table=dynamodb_setup)
        payment_service = PaymentService(table=dynamodb_setup)
        email_service = EmailService()
        
        # Registration data
        registration_data = {
            'company': {
                'name': 'Test Company Inc.',
                'industry': 'retail',
                'size': 'medium',
                'country': 'US'
            },
            'user': {
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'password': 'SecurePass123!'
            },
            'plan': {
                'plan_id': 'standard',
                'billing_cycle': 'monthly'
            }
        }
        
        # Step 1: Create tenant
        tenant_result = await tenant_service.create_tenant(registration_data['company'])
        assert 'tenant_id' in tenant_result
        assert tenant_result['company_name'] == 'Test Company Inc.'
        tenant_id = tenant_result['tenant_id']
        
        # Step 2: Create admin user
        user_data = registration_data['user'].copy()
        user_data['role'] = 'MASTER'
        user_result = await user_service.create_user(tenant_id, user_data)
        assert 'user_id' in user_result
        assert user_result['email'] == '<EMAIL>'
        user_id = user_result['user_id']
        
        # Step 3: Create subscription
        subscription_data = {
            'tenant_id': tenant_id,
            'plan_id': registration_data['plan']['plan_id'],
            'billing_cycle': registration_data['plan']['billing_cycle']
        }
        subscription_result = await payment_service.create_subscription(subscription_data)
        assert 'subscription_id' in subscription_result
        
        # Step 4: Send welcome email
        email_result = await email_service.send_welcome_email({
            'email': user_data['email'],
            'first_name': user_data['first_name'],
            'last_name': user_data['last_name'],
            'company_name': registration_data['company']['name'],
            'verification_code': '123456'
        })
        assert email_result['success'] is True
        
        # Verify complete registration
        # Check tenant was created
        created_tenant = await tenant_service.get_tenant(tenant_id)
        assert created_tenant['status'] == 'active'
        
        # Check user was associated with tenant
        tenant_users = await user_service.get_tenant_users(tenant_id)
        assert len(tenant_users) == 1
        assert tenant_users[0]['user_id'] == user_id
        
        # Check S3 structure was created
        s3_client = boto3.client('s3', region_name='us-east-1')
        objects = s3_client.list_objects_v2(Bucket=s3_setup, Prefix=f'tenant-{tenant_id}/')
        assert 'Contents' in objects
    
    @pytest.mark.asyncio
    async def test_registration_rollback_on_failure(self, dynamodb_setup, s3_setup):
        """Test registration rollback when payment fails."""
        
        tenant_service = TenantService(table=dynamodb_setup, s3_bucket=s3_setup)
        user_service = UserService(table=dynamodb_setup)
        
        # Mock payment service to fail
        class FailingPaymentService:
            async def create_subscription(self, data):
                raise Exception("Payment processing failed")
        
        payment_service = FailingPaymentService()
        
        registration_data = {
            'company': {'name': 'Test Company', 'industry': 'retail'},
            'user': {'first_name': 'John', 'last_name': 'Doe', 'email': '<EMAIL>'},
            'plan': {'plan_id': 'standard'}
        }
        
        # Registration should fail and rollback
        with pytest.raises(Exception, match="Payment processing failed"):
            # This would be the complete registration saga
            tenant_id = await tenant_service.create_tenant(registration_data['company'])
            user_id = await user_service.create_user(tenant_id, registration_data['user'])
            await payment_service.create_subscription({'tenant_id': tenant_id})
        
        # Verify cleanup happened (in real implementation, saga would handle this)
        # This is simplified for test demonstration


class TestAgentIntegration:
    """Integration tests for agent communication."""
    
    @pytest.fixture
    def mock_n8n_server(self):
        """Mock n8n server for testing."""
        import httpx
        from unittest.mock import AsyncMock
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'success': True,
            'agent_id': 'feedo',
            'response': {
                'text': 'I can help you with data ingestion',
                'attachments': []
            }
        }
        
        return mock_response
    
    @pytest.mark.asyncio
    async def test_agent_communication_flow(self, dynamodb_setup, mock_n8n_server):
        """Test complete agent communication flow."""
        
        from src.services.agent_service import AgentService
        from src.services.conversation_service import ConversationService
        
        # Initialize services
        agent_service = AgentService(table=dynamodb_setup)
        conversation_service = ConversationService(table=dynamodb_setup)
        
        # Create conversation
        conversation_data = {
            'tenant_id': 'tenant_123',
            'user_id': 'user_456',
            'agent_id': 'feedo',
            'title': 'Test Conversation'
        }
        
        conversation = await conversation_service.create_conversation(conversation_data)
        conversation_id = conversation['conversation_id']
        
        # Send message to agent
        with patch('httpx.AsyncClient.post', return_value=mock_n8n_server):
            response = await agent_service.send_message_to_agent(
                agent_id='feedo',
                tenant_id='tenant_123',
                user_id='user_456',
                conversation_id=conversation_id,
                message={'text': 'Hello agent'}
            )
        
        assert response['success'] is True
        assert response['agent_id'] == 'feedo'
        
        # Verify message was stored
        messages = await conversation_service.get_conversation_messages(conversation_id)
        assert len(messages) >= 1
```

## 🌐 End-to-End Testing Strategy

### **E2E Test Scope**
```yaml
User Journeys:
  - Complete user registration and first login
  - User management (invite, activate, deactivate)
  - Agent interaction and conversation flow
  - Payment and subscription management
  - Data upload and processing workflow
  - Security scenarios (failed logins, unauthorized access)

Technical Flows:
  - Multi-tenant data isolation verification
  - Performance under load
  - Disaster recovery scenarios
  - Cross-browser compatibility (if applicable)
```

### **E2E Test Implementation**
```python
# tests/e2e/test_user_journeys.py
import pytest
import asyncio
import httpx
from datetime import datetime, timedelta

class TestCompleteUserJourney:
    """End-to-end user journey tests."""
    
    @pytest.fixture
    def api_client(self):
        """HTTP client for API testing."""
        base_url = os.getenv('API_BASE_URL', 'https://api-dev.platform.com')
        return httpx.AsyncClient(base_url=base_url, timeout=30.0)
    
    @pytest.mark.asyncio
    async def test_complete_user_registration_journey(self, api_client):
        """Test complete user registration to first agent interaction."""
        
        # Step 1: Register new tenant and user
        registration_data = {
            'company': {
                'name': f'Test Company {datetime.now().timestamp()}',
                'industry': 'retail',
                'size': 'medium',
                'country': 'US'
            },
            'user': {
                'first_name': 'Test',
                'last_name': 'User',
                'email': f'test+{datetime.now().timestamp()}@example.com',
                'password': 'SecurePass123!'
            },
            'plan': {
                'plan_id': 'standard',
                'billing_cycle': 'monthly'
            }
        }
        
        # Register
        response = await api_client.post('/auth/register', json=registration_data)
        assert response.status_code == 201
        
        registration_result = response.json()
        assert registration_result['success'] is True
        assert 'tenant_id' in registration_result['data']
        
        # Step 2: Verify email (simulate)
        verification_data = {
            'email': registration_data['user']['email'],
            'verification_code': '123456'  # In real test, extract from email
        }
        
        response = await api_client.post('/auth/verify-email', json=verification_data)
        # Note: This might fail in real test without actual email verification
        
        # Step 3: Login
        login_data = {
            'email': registration_data['user']['email'],
            'password': registration_data['user']['password']
        }
        
        response = await api_client.post('/auth/login', json=login_data)
        assert response.status_code == 200
        
        login_result = response.json()
        assert login_result['success'] is True
        assert 'access_token' in login_result['data']
        
        access_token = login_result['data']['access_token']
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Step 4: Get available agents
        response = await api_client.get('/agents/available', headers=headers)
        assert response.status_code == 200
        
        agents_result = response.json()
        assert len(agents_result['data']['agents']) > 0
        
        # Step 5: Create conversation with Feedo agent
        conversation_data = {
            'agent_id': 'feedo',
            'title': 'Test Data Upload'
        }
        
        response = await api_client.post('/conversations', json=conversation_data, headers=headers)
        assert response.status_code == 201
        
        conversation_result = response.json()
        conversation_id = conversation_result['data']['conversation_id']
        
        # Step 6: Send message to agent
        message_data = {
            'conversation_id': conversation_id,
            'message': 'I need help uploading inventory data'
        }
        
        response = await api_client.post(
            f'/agents/feedo/chat', 
            json=message_data, 
            headers=headers
        )
        assert response.status_code == 200
        
        agent_response = response.json()
        assert agent_response['success'] is True
        assert 'agent_response' in agent_response['data']
        
        # Step 7: Verify conversation history
        response = await api_client.get(
            f'/conversations/{conversation_id}/messages',
            headers=headers
        )
        assert response.status_code == 200
        
        messages_result = response.json()
        assert len(messages_result['data']['messages']) >= 2  # User message + agent response
    
    @pytest.mark.asyncio
    async def test_user_management_journey(self, api_client):
        """Test user management flow by master user."""
        
        # Login as master user (setup required)
        master_token = await self._login_as_master(api_client)
        headers = {'Authorization': f'Bearer {master_token}'}
        
        # Invite new user
        invite_data = {
            'email': f'newuser+{datetime.now().timestamp()}@example.com',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'MEMBER'
        }
        
        response = await api_client.post('/users/invite', json=invite_data, headers=headers)
        assert response.status_code == 201
        
        invite_result = response.json()
        new_user_id = invite_result['data']['user_id']
        
        # Get all users
        response = await api_client.get('/users', headers=headers)
        assert response.status_code == 200
        
        users_result = response.json()
        user_emails = [user['email'] for user in users_result['data']['users']]
        assert invite_data['email'] in user_emails
        
        # Deactivate user
        response = await api_client.put(
            f'/users/{new_user_id}/status',
            json={'status': 'inactive'},
            headers=headers
        )
        assert response.status_code == 200
        
        # Verify user status
        response = await api_client.get('/users', headers=headers)
        users_result = response.json()
        
        deactivated_user = next(
            user for user in users_result['data']['users'] 
            if user['user_id'] == new_user_id
        )
        assert deactivated_user['status'] == 'inactive'
    
    async def _login_as_master(self, api_client):
        """Helper to login as master user."""
        # This would use pre-created test master account
        login_data = {
            'email': '<EMAIL>',
            'password': 'MasterPass123!'
        }
        
        response = await api_client.post('/auth/login', json=login_data)
        result = response.json()
        return result['data']['access_token']


class TestSecurityScenarios:
    """Security-focused E2E tests."""
    
    @pytest.mark.asyncio
    async def test_tenant_isolation_verification(self, api_client):
        """Verify complete tenant data isolation."""
        
        # Create two separate tenants with users
        tenant1_token = await self._create_tenant_and_login(api_client, "tenant1")
        tenant2_token = await self._create_tenant_and_login(api_client, "tenant2")
        
        # Create data for tenant 1
        headers1 = {'Authorization': f'Bearer {tenant1_token}'}
        conversation_data = {
            'agent_id': 'feedo',
            'title': 'Tenant 1 Conversation'
        }
        
        response = await api_client.post('/conversations', json=conversation_data, headers=headers1)
        tenant1_conversation_id = response.json()['data']['conversation_id']
        
        # Try to access tenant 1 data with tenant 2 credentials
        headers2 = {'Authorization': f'Bearer {tenant2_token}'}
        
        # Should not be able to access tenant 1's conversation
        response = await api_client.get(
            f'/conversations/{tenant1_conversation_id}/messages',
            headers=headers2
        )
        assert response.status_code in [403, 404]  # Forbidden or Not Found
        
        # Should not see tenant 1's conversations in tenant 2's list
        response = await api_client.get('/conversations', headers=headers2)
        assert response.status_code == 200
        
        conversations = response.json()['data']['conversations']
        tenant1_conversation_ids = [conv['conversation_id'] for conv in conversations]
        assert tenant1_conversation_id not in tenant1_conversation_ids
    
    @pytest.mark.asyncio
    async def test_authentication_security(self, api_client):
        """Test authentication security measures."""
        
        # Test rate limiting on login attempts
        login_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        # Make multiple failed login attempts
        for _ in range(6):  # Exceed rate limit
            response = await api_client.post('/auth/login', json=login_data)
            # First few should be 401, last ones should be 429 (rate limited)
        
        # Test invalid token access
        invalid_headers = {'Authorization': 'Bearer invalid_token_here'}
        response = await api_client.get('/users', headers=invalid_headers)
        assert response.status_code == 401
        
        # Test expired token access (would need to create expired token)
        # This is typically tested in unit tests with mocked token expiry
    
    async def _create_tenant_and_login(self, api_client, tenant_suffix):
        """Helper to create tenant and return access token."""
        registration_data = {
            'company': {
                'name': f'Test Company {tenant_suffix}',
                'industry': 'retail'
            },
            'user': {
                'first_name': 'Test',
                'last_name': 'User',
                'email': f'test+{tenant_suffix}@example.com',
                'password': 'SecurePass123!'
            },
            'plan': {'plan_id': 'basic'}
        }
        
        # Register
        response = await api_client.post('/auth/register', json=registration_data)
        
        # Login
        login_data = {
            'email': registration_data['user']['email'],
            'password': registration_data['user']['password']
        }
        
        response = await api_client.post('/auth/login', json=login_data)
        return response.json()['data']['access_token']
```

## 🚀 Performance Testing Strategy

### **Performance Test Types**
```yaml
Load Testing:
  - Normal expected load
  - Peak load scenarios
  - Sustained load over time
  
Stress Testing:
  - Beyond normal capacity
  - Breaking point identification
  - Recovery testing
  
Volume Testing:
  - Large data sets
  - Database performance
  - Storage capacity
  
Spike Testing:
  - Sudden load increases
  - Auto-scaling verification
  - Response time degradation
```

### **Performance Test Implementation**
```python
# tests/performance/test_load_performance.py
import pytest
import asyncio
import aiohttp
import statistics
from datetime import datetime
import concurrent.futures

class TestPerformanceRequirements:
    """Performance and load testing."""
    
    @pytest.fixture
    def api_base_url(self):
        return os.getenv('API_BASE_URL', 'https://api-dev.platform.com')
    
    @pytest.mark.asyncio
    async def test_authentication_load(self, api_base_url):
        """Test authentication endpoint under load."""
        
        async def single_auth_request(session, user_id):
            """Single authentication request."""
            auth_data = {
                'email': f'loadtest{user_id}@example.com',
                'password': 'LoadTest123!'
            }
            
            start_time = asyncio.get_event_loop().time()
            
            try:
                async with session.post(
                    f'{api_base_url}/auth/login',
                    json=auth_data,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    end_time = asyncio.get_event_loop().time()
                    
                    return {
                        'status_code': response.status,
                        'response_time': end_time - start_time,
                        'success': response.status == 200 or response.status == 401  # 401 is expected for non-existent users
                    }
            except asyncio.TimeoutError:
                return {
                    'status_code': 0,
                    'response_time': 5.0,
                    'success': False
                }
            except Exception as e:
                return {
                    'status_code': 0,
                    'response_time': 5.0,
                    'success': False,
                    'error': str(e)
                }
        
        # Test configuration
        num_concurrent_users = 100
        requests_per_user = 5
        
        connector = aiohttp.TCPConnector(limit=200, limit_per_host=100)
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Create all tasks
            tasks = []
            for user_id in range(num_concurrent_users):
                for request_num in range(requests_per_user):
                    task = single_auth_request(session, user_id)
                    tasks.append(task)
            
            # Execute all requests
            start_time = asyncio.get_event_loop().time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = asyncio.get_event_loop().time() - start_time
            
            # Analyze results
            successful_results = [r for r in results if isinstance(r, dict) and r['success']]
            failed_results = [r for r in results if not isinstance(r, dict) or not r['success']]
            
            response_times = [r['response_time'] for r in successful_results]
            
            # Assertions
            success_rate = len(successful_results) / len(tasks)
            assert success_rate >= 0.95, f"Success rate {success_rate:.2%} below 95%"
            
            if response_times:
                avg_response_time = statistics.mean(response_times)
                p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
                
                assert avg_response_time < 1.0, f"Average response time {avg_response_time:.3f}s exceeds 1s"
                assert p95_response_time < 2.0, f"95th percentile response time {p95_response_time:.3f}s exceeds 2s"
            
            # Throughput
            total_requests = len(tasks)
            throughput = total_requests / total_time
            assert throughput >= 50, f"Throughput {throughput:.1f} req/s below minimum 50 req/s"
            
            print(f"\nLoad Test Results:")
            print(f"Total requests: {total_requests}")
            print(f"Successful requests: {len(successful_results)}")
            print(f"Failed requests: {len(failed_results)}")
            print(f"Success rate: {success_rate:.2%}")
            print(f"Total time: {total_time:.2f}s")
            print(f"Throughput: {throughput:.1f} req/s")
            if response_times:
                print(f"Average response time: {avg_response_time:.3f}s")
                print(f"95th percentile response time: {p95_response_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_agent_conversation_load(self, api_base_url):
        """Test agent conversation endpoint under load."""
        
        # This would require pre-authenticated users
        access_tokens = await self._create_test_users_and_get_tokens(api_base_url, count=50)
        
        async def conversation_flow(session, token):
            """Complete conversation flow for one user."""
            headers = {'Authorization': f'Bearer {token}'}
            
            try:
                # Create conversation
                conv_data = {'agent_id': 'feedo', 'title': 'Load Test Conversation'}
                async with session.post(
                    f'{api_base_url}/conversations',
                    json=conv_data,
                    headers=headers
                ) as response:
                    if response.status != 201:
                        return {'success': False, 'step': 'create_conversation'}
                    
                    conv_result = await response.json()
                    conversation_id = conv_result['data']['conversation_id']
                
                # Send message to agent
                message_data = {
                    'conversation_id': conversation_id,
                    'message': 'Help me with load testing data'
                }
                
                start_time = asyncio.get_event_loop().time()
                async with session.post(
                    f'{api_base_url}/agents/feedo/chat',
                    json=message_data,
                    headers=headers
                ) as response:
                    end_time = asyncio.get_event_loop().time()
                    
                    return {
                        'success': response.status == 200,
                        'response_time': end_time - start_time,
                        'step': 'agent_chat'
                    }
                    
            except Exception as e:
                return {'success': False, 'error': str(e)}
        
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        async with aiohttp.ClientSession(connector=connector) as session:
            
            # Execute conversations concurrently
            tasks = [conversation_flow(session, token) for token in access_tokens]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze results
            successful_results = [r for r in results if isinstance(r, dict) and r.get('success')]
            failed_results = [r for r in results if not isinstance(r, dict) or not r.get('success')]
            
            success_rate = len(successful_results) / len(tasks)
            assert success_rate >= 0.90, f"Agent conversation success rate {success_rate:.2%} below 90%"
            
            if successful_results:
                response_times = [r['response_time'] for r in successful_results if 'response_time' in r]
                if response_times:
                    avg_response_time = statistics.mean(response_times)
                    # Agent responses can be slower due to processing
                    assert avg_response_time < 10.0, f"Average agent response time {avg_response_time:.3f}s exceeds 10s"
    
    async def _create_test_users_and_get_tokens(self, api_base_url, count=50):
        """Create test users and return their access tokens."""
        # Implementation would create test users and return tokens
        # For demo purposes, returning mock tokens
        return [f'mock_token_{i}' for i in range(count)]
```

## 🔒 Security Testing Strategy

### **Security Test Categories**
```yaml
Authentication Testing:
  - Password policies
  - Account lockout
  - Session management
  - Token security

Authorization Testing:
  - Role-based access control
  - Tenant isolation
  - Permission escalation
  - API endpoint security

Input Validation Testing:
  - SQL injection
  - XSS prevention
  - Data sanitization
  - File upload security

Infrastructure Security:
  - API Gateway security
  - WAF effectiveness
  - Rate limiting
  - DDoS protection
```

### **Security Test Implementation**
```python
# tests/security/test_security_vulnerabilities.py
import pytest
import httpx
import asyncio
import json

class TestSecurityVulnerabilities:
    """Security vulnerability testing."""
    
    @pytest.fixture
    def api_client(self):
        base_url = os.getenv('API_BASE_URL', 'https://api-dev.platform.com')
        return httpx.AsyncClient(base_url=base_url, timeout=30.0)
    
    @pytest.mark.asyncio
    async def test_sql_injection_protection(self, api_client):
        """Test SQL injection attack prevention."""
        
        # SQL injection payloads
        injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'/*",
            "1' UNION SELECT * FROM users--",
            "'; INSERT INTO users VALUES ('hacker', 'pass'); --"
        ]
        
        for payload in injection_payloads:
            # Test in login email field
            login_data = {
                'email': payload,
                'password': 'test123'
            }
            
            response = await api_client.post('/auth/login', json=login_data)
            
            # Should return 400 (validation error) or 401 (authentication failed)
            # Should NOT return 500 (server error indicating SQL injection)
            assert response.status_code in [400, 401], f"SQL injection payload '{payload}' caused unexpected status: {response.status_code}"
            
            # Response should not contain database error messages
            response_text = response.text.lower()
            sql_error_indicators = ['sql', 'database', 'syntax error', 'mysql', 'postgres']
            for indicator in sql_error_indicators:
                assert indicator not in response_text, f"Response contains database error indicator: {indicator}"
    
    @pytest.mark.asyncio
    async def test_xss_protection(self, api_client):
        """Test XSS attack prevention."""
        
        # XSS payloads
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//",
            "<svg onload=alert('xss')>"
        ]
        
        # First, get valid authentication
        valid_token = await self._get_valid_token(api_client)
        headers = {'Authorization': f'Bearer {valid_token}'}
        
        for payload in xss_payloads:
            # Test in user profile update
            profile_data = {
                'first_name': payload,
                'last_name': 'Test'
            }
            
            response = await api_client.put('/users/profile', json=profile_data, headers=headers)
            
            # Should either reject (400) or sanitize the input
            if response.status_code == 200:
                # If accepted, verify the payload was sanitized
                profile_response = await api_client.get('/users/profile', headers=headers)
                profile_data = profile_response.json()
                
                stored_name = profile_data['data']['first_name']
                # Should not contain script tags or javascript
                assert '<script>' not in stored_name.lower()
                assert 'javascript:' not in stored_name.lower()
                assert 'alert(' not in stored_name.lower()
    
    @pytest.mark.asyncio
    async def test_authorization_bypass_attempts(self, api_client):
        """Test authorization bypass prevention."""
        
        # Create two users in different tenants
        user1_token = await self._create_user_and_login(api_client, "<EMAIL>")
        user2_token = await self._create_user_and_login(api_client, "<EMAIL>")
        
        # User 1 creates a conversation
        headers1 = {'Authorization': f'Bearer {user1_token}'}
        conv_data = {'agent_id': 'feedo', 'title': 'Private Conversation'}
        
        response = await api_client.post('/conversations', json=conv_data, headers=headers1)
        conversation_id = response.json()['data']['conversation_id']
        
        # User 2 attempts to access User 1's conversation
        headers2 = {'Authorization': f'Bearer {user2_token}'}
        
        # Direct access attempt
        response = await api_client.get(f'/conversations/{conversation_id}', headers=headers2)
        assert response.status_code in [403, 404], "Cross-tenant conversation access should be forbidden"
        
        # Attempt to modify conversation
        response = await api_client.put(
            f'/conversations/{conversation_id}',
            json={'title': 'Hacked Title'},
            headers=headers2
        )
        assert response.status_code in [403, 404], "Cross-tenant conversation modification should be forbidden"
        
        # Attempt to delete conversation
        response = await api_client.delete(f'/conversations/{conversation_id}', headers=headers2)
        assert response.status_code in [403, 404], "Cross-tenant conversation deletion should be forbidden"
    
    @pytest.mark.asyncio
    async def test_rate_limiting_enforcement(self, api_client):
        """Test rate limiting protection."""
        
        # Test login rate limiting
        login_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        # Make requests up to the rate limit
        rate_limit_responses = []
        for i in range(15):  # Assuming rate limit is 10 per minute
            response = await api_client.post('/auth/login', json=login_data)
            rate_limit_responses.append(response.status_code)
            
            # Small delay to avoid overwhelming
            await asyncio.sleep(0.1)
        
        # Should eventually get rate limited (429)
        assert 429 in rate_limit_responses, "Rate limiting not enforced on login endpoint"
        
        # Verify rate limit headers are present
        response = await api_client.post('/auth/login', json=login_data)
        if response.status_code == 429:
            assert 'x-ratelimit-limit' in response.headers.keys() or 'retry-after' in response.headers.keys()
    
    @pytest.mark.asyncio
    async def test_file_upload_security(self, api_client):
        """Test file upload security measures."""
        
        valid_token = await self._get_valid_token(api_client)
        headers = {'Authorization': f'Bearer {valid_token}'}
        
        # Test malicious file uploads
        malicious_files = [
            ('malware.exe', b'MZ\x90\x00', 'application/octet-stream'),  # Executable
            ('script.php', b'<?php system($_GET["cmd"]); ?>', 'application/x-php'),  # PHP script
            ('bomb.zip', b'PK\x03\x04' + b'\x00' * 1000000, 'application/zip'),  # Zip bomb
            ('oversized.txt', b'A' * (50 * 1024 * 1024), 'text/plain')  # 50MB file
        ]
        
        for filename, content, content_type in malicious_files:
            files = {'file': (filename, content, content_type)}
            
            response = await api_client.post('/agents/feedo/upload', files=files, headers=headers)
            
            # Should reject malicious files
            assert response.status_code in [400, 413, 415], f"Malicious file {filename} should be rejected"
            
            # Should not cause server error
            assert response.status_code != 500, f"File {filename} caused server error"
    
    async def _get_valid_token(self, api_client):
        """Get valid authentication token for testing."""
        # Create and login with test user
        return await self._create_user_and_login(api_client, "<EMAIL>")
    
    async def _create_user_and_login(self, api_client, email):
        """Create user and return access token."""
        # Registration
        registration_data = {
            'company': {'name': f'Test Company {email}', 'industry': 'retail'},
            'user': {'first_name': 'Test', 'last_name': 'User', 'email': email, 'password': 'SecurePass123!'},
            'plan': {'plan_id': 'basic'}
        }
        
        await api_client.post('/auth/register', json=registration_data)
        
        # Login
        login_data = {'email': email, 'password': 'SecurePass123!'}
        response = await api_client.post('/auth/login', json=login_data)
        
        if response.status_code == 200:
            return response.json()['data']['access_token']
        else:
            # Use pre-existing test account
            return 'test_token_for_security_testing'
```

## 📊 Test Reporting & Metrics

### **Test Metrics Collection**
```python
# tests/conftest.py
import pytest
import time
import json
from datetime import datetime
import os

class TestMetricsCollector:
    """Collect and report test metrics."""
    
    def __init__(self):
        self.test_results = []
        self.start_time = None
        self.end_time = None
    
    def pytest_runtest_setup(self, item):
        """Called before each test."""
        item.start_time = time.time()
    
    def pytest_runtest_teardown(self, item, nextitem):
        """Called after each test."""
        if hasattr(item, 'start_time'):
            duration = time.time() - item.start_time
            
            self.test_results.append({
                'test_name': item.name,
                'test_file': str(item.fspath),
                'duration': duration,
                'outcome': getattr(item, 'test_outcome', 'unknown'),
                'timestamp': datetime.utcnow().isoformat()
            })
    
    def pytest_sessionstart(self, session):
        """Called at start of test session."""
        self.start_time = time.time()
    
    def pytest_sessionfinish(self, session, exitstatus):
        """Called at end of test session."""
        self.end_time = time.time()
        
        # Generate test report
        self.generate_test_report(exitstatus)
    
    def generate_test_report(self, exit_status):
        """Generate comprehensive test report."""
        total_duration = self.end_time - self.start_time
        
        # Calculate metrics
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['outcome'] == 'passed'])
        failed_tests = len([r for r in self.test_results if r['outcome'] == 'failed'])
        
        # Performance metrics
        durations = [r['duration'] for r in self.test_results]
        avg_duration = sum(durations) / len(durations) if durations else 0
        slowest_tests = sorted(self.test_results, key=lambda x: x['duration'], reverse=True)[:5]
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_duration': total_duration,
                'average_test_duration': avg_duration,
                'exit_status': exit_status
            },
            'performance': {
                'slowest_tests': slowest_tests,
                'tests_over_threshold': [
                    r for r in self.test_results if r['duration'] > 30  # 30 second threshold
                ]
            },
            'test_results': self.test_results,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Save report
        os.makedirs('test-reports', exist_ok=True)
        with open(f'test-reports/test-report-{int(time.time())}.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print(f"\n{'='*50}")
        print(f"TEST EXECUTION SUMMARY")
        print(f"{'='*50}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['summary']['success_rate']:.1f}%")
        print(f"Total Duration: {total_duration:.2f}s")
        print(f"Average Test Duration: {avg_duration:.2f}s")
        
        if slowest_tests:
            print(f"\nSlowest Tests:")
            for test in slowest_tests:
                print(f"  {test['test_name']}: {test['duration']:.2f}s")


@pytest.fixture(scope="session")
def metrics_collector():
    """Provide metrics collector for test session."""
    return TestMetricsCollector()
```

### **CI/CD Integration**
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-test.txt
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ \
          --cov=src \
          --cov-report=xml \
          --cov-report=html \
          --cov-fail-under=90 \
          --junitxml=test-results/unit-tests.xml \
          -v
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unit-tests
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: test-results/

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      localstack:
        image: localstack/localstack
        env:
          SERVICES: dynamodb,s3,ses
          DEBUG: 1
        ports:
          - 4566:4566
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-test.txt
    
    - name: Wait for LocalStack
      run: |
        pip install awscli-local
        awslocal dynamodb list-tables || sleep 10
    
    - name: Run integration tests
      env:
        AWS_ENDPOINT_URL: http://localhost:4566
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
        AWS_DEFAULT_REGION: us-east-1
      run: |
        pytest tests/integration/ \
          --junitxml=test-results/integration-tests.xml \
          -v

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-test.txt
    
    - name: Deploy test environment
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: |
        ./scripts/deploy.sh test-${{ github.run_id }}
    
    - name: Run E2E tests
      env:
        API_BASE_URL: ${{ env.TEST_API_URL }}
      run: |
        pytest tests/e2e/ \
          --junitxml=test-results/e2e-tests.xml \
          -v
    
    - name: Cleanup test environment
      if: always()
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: |
        ./scripts/destroy.sh test-${{ github.run_id }}

  security-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run security tests
      run: |
        pytest tests/security/ \
          --junitxml=test-results/security-tests.xml \
          -v

  test-report:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, security-tests]
    if: always()
    
    steps:
    - name: Download all test results
      uses: actions/download-artifact@v3
    
    - name: Generate test report
      run: |
        python scripts/generate-test-report.py
    
    - name: Upload test report
      uses: actions/upload-artifact@v3
      with:
        name: complete-test-report
        path: test-reports/
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Quality Assurance Team  
**Review Cycle:** Weekly during development, monthly post-launch