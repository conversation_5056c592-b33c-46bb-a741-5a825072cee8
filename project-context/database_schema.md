# Database Schema & Data Models

## 🗄️ Database Design Overview

### **Design Philosophy**
- **Single Table Design:** DynamoDB optimized for access patterns
- **Tenant-First Partitioning:** All data partitioned by tenant_id
- **Hierarchical Sort Keys:** Enable range queries and relationships
- **Sparse GSIs:** Optimize for specific query patterns
- **No-SQL Best Practices:** Denormalization for performance

### **Access Pattern Driven Design**
1. **User Authentication:** Get user by email
2. **Tenant Data Isolation:** Get all entities for a tenant
3. **User Management:** Get all users in a tenant
4. **Conversation History:** Get conversations by user
5. **Message Threading:** Get messages in conversation order
6. **Subscription Status:** Get active/suspended tenants
7. **Usage Analytics:** Get usage data by tenant and time period

## 📊 Primary Table Schema

### **Table: platform-main-{environment}**

#### **Primary Key Design**
```
Partition Key (PK): {entity_type}#{identifier}
Sort Key (SK): {sort_key}

Examples:
PK: TENANT#tenant_123          SK: profile
PK: USER#user_456              SK: profile  
PK: USER#user_456              SK: tenant_123
PK: CONVERSATION#conv_789      SK: metadata
PK: MESSAGE#msg_101112         SK: 2025-07-16T10:30:00Z
```

#### **Attribute Schema**
```json
{
  "PK": "String",           // Partition Key
  "SK": "String",           // Sort Key  
  "GSI1PK": "String",       // GSI1 Partition Key
  "GSI1SK": "String",       // GSI1 Sort Key
  "GSI2PK": "String",       // GSI2 Partition Key  
  "GSI2SK": "String",       // GSI2 Sort Key
  "entity_type": "String",  // TENANT|USER|CONVERSATION|MESSAGE|SUBSCRIPTION|INVOICE
  "tenant_id": "String",    // Always present for tenant isolation
  "created_at": "String",   // ISO 8601 timestamp
  "updated_at": "String",   // ISO 8601 timestamp
  "status": "String",       // Entity status
  "data": "Map",            // Entity-specific data
  "ttl": "Number"           // Optional TTL for temporary data
}
```

## 🏢 Tenant Entity Model

### **Tenant Profile**
```json
{
  "PK": "TENANT#tenant_123",
  "SK": "profile",
  "GSI1PK": "TENANT",
  "GSI1SK": "tenant_123",
  "GSI2PK": "TENANT_STATUS#active",
  "GSI2SK": "2025-07-16T09:00:00Z",
  "entity_type": "TENANT",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T09:00:00Z",
  "updated_at": "2025-07-16T10:30:00Z",
  "status": "active|suspended|cancelled",
  "data": {
    "company_name": "Acme Logistics",
    "industry": "retail|manufacturing|distribution",
    "company_size": "small|medium|large|enterprise",
    "country": "US",
    "tax_id": "*********",
    "website": "https://acme.com",
    "address": {
      "street": "123 Main St",
      "city": "Anytown", 
      "state": "CA",
      "zip": "12345",
      "country": "US"
    },
    "settings": {
      "timezone": "America/Los_Angeles",
      "date_format": "MM/DD/YYYY",
      "currency": "USD",
      "language": "en"
    }
  }
}
```

### **Tenant Configuration**
```json
{
  "PK": "TENANT#tenant_123",
  "SK": "configuration",
  "entity_type": "TENANT_CONFIG",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T09:00:00Z",
  "updated_at": "2025-07-16T10:30:00Z",
  "data": {
    "provisioned_resources": {
      "s3_bucket": "platform-tenant-123-data",
      "iam_role_arn": "arn:aws:iam::account:role/TenantRole123",
      "cloudwatch_dashboard": "tenant-123-dashboard"
    },
    "feature_flags": {
      "advanced_analytics": true,
      "custom_dashboards": false,
      "api_access": true
    },
    "limits": {
      "max_users": 50,
      "max_conversations_monthly": 1000,
      "max_storage_gb": 100
    }
  }
}
```

## 👤 User Entity Model

### **User Profile**
```json
{
  "PK": "USER#user_456",
  "SK": "profile",
  "GSI1PK": "TENANT#tenant_123",
  "GSI1SK": "USER#user_456",
  "GSI2PK": "EMAIL#<EMAIL>",
  "GSI2SK": "user_456",
  "entity_type": "USER", 
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T09:15:00Z",
  "updated_at": "2025-07-16T10:30:00Z",
  "status": "active|inactive|suspended|pending_verification",
  "data": {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+*********0",
    "role": "MASTER|MEMBER",
    "permissions": [
      "agents:read",
      "agents:write", 
      "users:read",
      "users:write",
      "billing:read",
      "billing:write"
    ],
    "password_hash": "$2b$12$...",
    "last_login": "2025-07-16T10:30:00Z",
    "login_count": 25,
    "email_verified": true,
    "email_verified_at": "2025-07-16T09:20:00Z",
    "profile_image": "https://s3.amazonaws.com/...",
    "preferences": {
      "notifications": {
        "email": true,
        "push": false
      },
      "ui_settings": {
        "theme": "light|dark",
        "language": "en"
      }
    }
  }
}
```

### **User Tenant Association**
```json
{
  "PK": "USER#user_456",
  "SK": "tenant_123",
  "GSI1PK": "TENANT#tenant_123",
  "GSI1SK": "USER#user_456#2025-07-16T09:15:00Z",
  "entity_type": "USER_TENANT",
  "tenant_id": "tenant_123", 
  "created_at": "2025-07-16T09:15:00Z",
  "updated_at": "2025-07-16T10:30:00Z",
  "status": "active",
  "data": {
    "role": "MASTER",
    "joined_at": "2025-07-16T09:15:00Z",
    "invited_by": "system",
    "permissions_granted": [
      "agents:read",
      "agents:write",
      "users:read", 
      "users:write",
      "billing:read",
      "billing:write"
    ]
  }
}
```

## 💳 Subscription Entity Model

### **Subscription Profile**
```json
{
  "PK": "SUBSCRIPTION#sub_789",
  "SK": "profile",
  "GSI1PK": "TENANT#tenant_123", 
  "GSI1SK": "SUBSCRIPTION#sub_789",
  "GSI2PK": "SUBSCRIPTION_STATUS#active",
  "GSI2SK": "2025-08-16T00:00:00Z",
  "entity_type": "SUBSCRIPTION",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T09:00:00Z",
  "updated_at": "2025-07-16T10:30:00Z", 
  "status": "active|cancelled|suspended|past_due",
  "data": {
    "plan_id": "standard",
    "plan_name": "Standard Plan",
    "billing_cycle": "monthly|yearly",
    "current_period_start": "2025-07-16T00:00:00Z",
    "current_period_end": "2025-08-16T00:00:00Z",
    "next_billing_date": "2025-08-16T00:00:00Z",
    "amount": 79900,
    "currency": "usd",
    "payment_provider": "stripe",
    "provider_subscription_id": "sub_stripe_123",
    "provider_customer_id": "cus_stripe_456",
    "trial_end": null,
    "cancelled_at": null,
    "cancel_at_period_end": false,
    "plan_features": {
      "max_records_monthly": 1000000,
      "max_users": 15,
      "agents": ["feedo", "forecaster"],
      "api_access": true,
      "priority_support": true
    }
  }
}
```

### **Usage Tracking**
```json
{
  "PK": "USAGE#tenant_123",
  "SK": "2025-07-16",
  "GSI1PK": "USAGE_DATE#2025-07-16",
  "GSI1SK": "tenant_123",
  "entity_type": "USAGE",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T00:00:00Z",
  "updated_at": "2025-07-16T23:59:59Z",
  "data": {
    "date": "2025-07-16",
    "metrics": {
      "conversations_started": 5,
      "messages_sent": 125,
      "data_records_processed": 50000,
      "api_calls": 245,
      "storage_used_bytes": **********,
      "compute_time_ms": 15000
    },
    "agent_usage": {
      "feedo": {
        "conversations": 2,
        "messages": 45,
        "processing_time_ms": 8000
      },
      "forecaster": {
        "conversations": 3,
        "messages": 80,
        "processing_time_ms": 7000
      }
    },
    "overage": {
      "records": 0,
      "api_calls": 0,
      "storage_gb": 0
    }
  }
}
```

## 💬 Conversation Entity Model

### **Conversation Metadata**
```json
{
  "PK": "CONVERSATION#conv_101",
  "SK": "metadata",
  "GSI1PK": "USER#user_456",
  "GSI1SK": "CONVERSATION#2025-07-16T10:00:00Z",
  "GSI2PK": "TENANT#tenant_123",
  "GSI2SK": "CONVERSATION#conv_101",
  "entity_type": "CONVERSATION",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T10:00:00Z",
  "updated_at": "2025-07-16T10:30:00Z",
  "status": "active|archived|deleted",
  "data": {
    "title": "Q3 Inventory Analysis",
    "agent_id": "forecaster",
    "agent_name": "Forecaster",
    "user_id": "user_456",
    "user_name": "John Doe",
    "message_count": 15,
    "last_message_at": "2025-07-16T10:30:00Z",
    "last_message_preview": "Based on your data, I recommend...",
    "context": {
      "product_category": "electronics",
      "warehouse_location": "warehouse_01",
      "data_source": "erp_integration"
    },
    "tags": ["inventory", "q3", "forecast"],
    "starred": false,
    "archived_at": null
  }
}
```

### **Message Entity**
```json
{
  "PK": "MESSAGE#msg_202",
  "SK": "2025-07-16T10:15:00.123Z",
  "GSI1PK": "CONVERSATION#conv_101",
  "GSI1SK": "2025-07-16T10:15:00.123Z",
  "GSI2PK": "USER#user_456",
  "GSI2SK": "MESSAGE#2025-07-16T10:15:00.123Z",
  "entity_type": "MESSAGE",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T10:15:00.123Z",
  "updated_at": "2025-07-16T10:15:00.123Z",
  "data": {
    "conversation_id": "conv_101",
    "sender_type": "user|agent",
    "sender_id": "user_456",
    "sender_name": "John Doe",
    "content": {
      "text": "Can you analyze the inventory levels for Q3?",
      "type": "text|file|dashboard|template",
      "attachments": [
        {
          "id": "att_303",
          "type": "file",
          "name": "inventory_q3.xlsx", 
          "url": "https://s3.amazonaws.com/bucket/tenant_123/uploads/inventory_q3.xlsx",
          "size": 1048576,
          "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        }
      ]
    },
    "agent_metadata": {
      "processing_time_ms": 2500,
      "confidence_score": 0.95,
      "tools_used": ["data_analyzer", "forecast_generator"],
      "cost_units": 150
    },
    "thread_id": "thread_404",
    "reply_to": null,
    "edited": false,
    "deleted": false
  }
}
```

## 📄 Invoice Entity Model

### **Invoice Record**
```json
{
  "PK": "INVOICE#inv_505",
  "SK": "profile",
  "GSI1PK": "TENANT#tenant_123",
  "GSI1SK": "INVOICE#2025-07-16T00:00:00Z",
  "GSI2PK": "INVOICE_STATUS#paid",
  "GSI2SK": "2025-07-16T00:00:00Z",
  "entity_type": "INVOICE",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T00:00:00Z",
  "updated_at": "2025-07-16T00:30:00Z",
  "status": "pending|paid|failed|refunded",
  "data": {
    "invoice_number": "INV-2025-07-001",
    "billing_period": {
      "start": "2025-07-16T00:00:00Z",
      "end": "2025-08-16T00:00:00Z"
    },
    "line_items": [
      {
        "description": "Standard Plan - Monthly",
        "quantity": 1,
        "unit_price": 79900,
        "total": 79900
      },
      {
        "description": "Overage - Data Processing",
        "quantity": 500000,
        "unit_price": 10,
        "total": 5000,
        "details": {
          "overage_type": "data_records",
          "included": 1000000,
          "used": 1500000,
          "overage": 500000
        }
      }
    ],
    "subtotal": 84900,
    "tax": 7641,
    "total": 92541,
    "currency": "usd",
    "payment_provider": "stripe",
    "provider_invoice_id": "in_stripe_789",
    "payment_method": {
      "type": "card",
      "last4": "4242",
      "brand": "visa"
    },
    "paid_at": "2025-07-16T00:30:00Z",
    "invoice_url": "https://invoices.stripe.com/invoice_123.pdf"
  }
}
```

## 🔐 Authentication Entity Model

### **Verification Codes**
```json
{
  "PK": "VERIFICATION#user_456",
  "SK": "email_verification",
  "entity_type": "VERIFICATION_CODE",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T10:00:00Z",
  "updated_at": "2025-07-16T10:00:00Z",
  "ttl": **********,
  "data": {
    "code": "654321",
    "code_type": "email_verification|password_reset",
    "email": "<EMAIL>",
    "expires_at": "2025-07-16T10:15:00Z",
    "attempts": 0,
    "max_attempts": 5,
    "verified": false,
    "verified_at": null
  }
}
```

### **Refresh Tokens**
```json
{
  "PK": "REFRESH_TOKEN#rt_606",
  "SK": "profile",
  "GSI1PK": "USER#user_456",
  "GSI1SK": "rt_606",
  "entity_type": "REFRESH_TOKEN",
  "tenant_id": "tenant_123",
  "created_at": "2025-07-16T10:30:00Z",
  "updated_at": "2025-07-16T10:30:00Z",
  "ttl": 1629032400,
  "data": {
    "user_id": "user_456",
    "token_hash": "$2b$12$...",
    "expires_at": "2025-08-16T10:30:00Z",
    "revoked": false,
    "revoked_at": null,
    "last_used": "2025-07-16T10:30:00Z",
    "device_info": {
      "user_agent": "Mozilla/5.0...",
      "ip_address": "*************",
      "device_fingerprint": "fp_707"
    }
  }
}
```

## 📊 Global Secondary Indexes

### **GSI1: Tenant-Entity Index**
```
GSI1PK: TENANT#{tenant_id} | USER#{user_id} | EMAIL#{email}
GSI1SK: {entity_type}#{entity_id} | {timestamp}

Purpose: Query all entities within a tenant
Examples:
- Get all users in tenant: GSI1PK = TENANT#tenant_123, GSI1SK begins_with USER#
- Get user by email: GSI1PK = EMAIL#<EMAIL>
- Get user conversations: GSI1PK = USER#user_456, GSI1SK begins_with CONVERSATION#
```

### **GSI2: Status-Date Index**
```
GSI2PK: {entity_type}_STATUS#{status} | USAGE_DATE#{date}
GSI2SK: {timestamp} | {tenant_id}

Purpose: Query entities by status and date
Examples:
- Get active tenants: GSI2PK = TENANT_STATUS#active
- Get suspended subscriptions: GSI2PK = SUBSCRIPTION_STATUS#suspended
- Get usage by date: GSI2PK = USAGE_DATE#2025-07-16
```

## 🔍 Common Query Patterns

### **1. User Authentication**
```python
# Get user by email
response = table.query(
    IndexName='GSI1',
    KeyConditionExpression=Key('GSI1PK').eq('EMAIL#<EMAIL>')
)
```

### **2. Tenant User Management**
```python
# Get all users in tenant
response = table.query(
    IndexName='GSI1',
    KeyConditionExpression=Key('GSI1PK').eq('TENANT#tenant_123') & 
                          Key('GSI1SK').begins_with('USER#')
)
```

### **3. Conversation History**
```python
# Get user conversations
response = table.query(
    IndexName='GSI1',
    KeyConditionExpression=Key('GSI1PK').eq('USER#user_456') &
                          Key('GSI1SK').begins_with('CONVERSATION#'),
    ScanIndexForward=False  # Most recent first
)

# Get messages in conversation
response = table.query(
    IndexName='GSI1',
    KeyConditionExpression=Key('GSI1PK').eq('CONVERSATION#conv_101'),
    ScanIndexForward=True   # Chronological order
)
```

### **4. Subscription Management**
```python
# Get tenant subscription
response = table.get_item(
    Key={
        'PK': 'TENANT#tenant_123',
        'SK': 'subscription'
    }
)

# Get subscriptions expiring soon
response = table.query(
    IndexName='GSI2',
    KeyConditionExpression=Key('GSI2PK').eq('SUBSCRIPTION_STATUS#active') &
                          Key('GSI2SK').between('2025-07-16T00:00:00Z', '2025-07-23T00:00:00Z')
)
```

### **5. Usage Analytics**
```python
# Get tenant usage for date range
response = table.query(
    KeyConditionExpression=Key('PK').eq('USAGE#tenant_123') &
                          Key('SK').between('2025-07-01', '2025-07-31')
)

# Get daily usage across all tenants
response = table.query(
    IndexName='GSI2',
    KeyConditionExpression=Key('GSI2PK').eq('USAGE_DATE#2025-07-16')
)
```

## 🛡️ Data Integrity & Validation

### **Partition Key Validation**
```python
def validate_partition_key(pk: str) -> bool:
    """Validate partition key format"""
    valid_prefixes = ['TENANT#', 'USER#', 'CONVERSATION#', 'MESSAGE#', 
                     'SUBSCRIPTION#', 'INVOICE#', 'VERIFICATION#', 'REFRESH_TOKEN#']
    return any(pk.startswith(prefix) for prefix in valid_prefixes)

def validate_tenant_isolation(item: dict) -> bool:
    """Ensure all items have tenant_id for isolation"""
    return 'tenant_id' in item and item['tenant_id'] is not None
```

### **Data Consistency Rules**
```python
# Atomic operations for related entities
def create_user_with_tenant_association(user_data: dict, tenant_id: str):
    """Create user and tenant association atomically"""
    with table.batch_writer() as batch:
        # User profile
        batch.put_item(Item={
            'PK': f"USER#{user_data['user_id']}",
            'SK': 'profile',
            'entity_type': 'USER',
            'tenant_id': tenant_id,
            'data': user_data
        })
        
        # User-tenant association
        batch.put_item(Item={
            'PK': f"USER#{user_data['user_id']}",
            'SK': tenant_id,
            'entity_type': 'USER_TENANT',
            'tenant_id': tenant_id,
            'data': {'role': user_data['role']}
        })
```

## 📈 Performance Optimization

### **Hot Key Prevention**
```python
# Distribute writes across partitions
def get_usage_partition_key(tenant_id: str, date: str) -> str:
    """Distribute usage data to prevent hot partitions"""
    import hashlib
    hash_suffix = hashlib.md5(f"{tenant_id}{date}".encode()).hexdigest()[:2]
    return f"USAGE#{tenant_id}#{hash_suffix}"
```

### **Cache Strategy**
```python
# Cache frequently accessed data
CACHE_PATTERNS = {
    'user_profile': 'user:{user_id}:profile',      # TTL: 1 hour
    'tenant_config': 'tenant:{tenant_id}:config',  # TTL: 30 minutes
    'subscription': 'tenant:{tenant_id}:sub',      # TTL: 15 minutes
    'conversation_list': 'user:{user_id}:convs',   # TTL: 5 minutes
}
```

### **Batch Operations**
```python
# Efficient batch reads
def get_multiple_conversations(conversation_ids: list) -> list:
    """Get multiple conversations in batch"""
    request_items = {
        'platform-main-prod': {
            'Keys': [
                {'PK': f'CONVERSATION#{conv_id}', 'SK': 'metadata'}
                for conv_id in conversation_ids
            ]
        }
    }
    
    response = dynamodb.batch_get_item(RequestItems=request_items)
    return response['Responses']['platform-main-prod']
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Database Architecture Team  
**Review Cycle:** Bi-weekly during development, monthly post-launch