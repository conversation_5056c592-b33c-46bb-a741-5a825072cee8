# Deployment & Operations Guide

## 🚀 Deployment Overview

### **Current Deployment Strategy**
- **Independent Service Deployment:** Each microservice deploys separately
- **Serverless Framework:** Infrastructure and code deployed together
- **Environment Management:** dev → staging → production
- **Shared Layer Strategy:** Deploy shared layer first, then services
- **Manual Deployment:** Controlled deployment process
- **Rollback Capability:** Revert to previous versions via Serverless Framework

### **Current Environment Architecture**
```
Development Environment:
├── Purpose: Feature development and testing
├── Resources: Minimal Lambda concurrency, on-demand DynamoDB
├── Data: Test data and mocked external services
├── Deployment: Manual via Serverless Framework
└── Access: Development team

Staging Environment:
├── Purpose: Pre-production validation and integration testing
├── Resources: Production-like configuration (scaled down)
├── Data: Anonymized or synthetic data
├── Deployment: Manual promotion from dev
└── Access: QA team and stakeholders

Production Environment:
├── Purpose: Live customer-facing application
├── Resources: Auto-scaling Lambda, production DynamoDB
├── Data: Real customer data with encryption
├── Deployment: Manual promotion from staging
└── Access: Operations team and authorized personnel
```

## 🏗️ Serverless Deployment Process

### **Deployment Order and Process**
```bash
#!/bin/bash
# Deployment process for Agent SCL Platform

set -e

STAGE=${1:-dev}
AWS_REGION=${2:-us-east-1}

echo "Deploying Agent SCL Platform to stage: $STAGE in region: $AWS_REGION"

# Step 1: Deploy shared layer first
echo "1. Deploying shared layer..."
cd shared/python
serverless deploy --stage $STAGE --region $AWS_REGION

# Step 2: Deploy shared infrastructure
echo "2. Deploying shared infrastructure..."
cd ../../serverless/shared
serverless deploy --stage $STAGE --region $AWS_REGION

# Step 3: Deploy individual services
echo "3. Deploying microservices..."
SERVICES=("auth" "payment" "tenant" "user")

for service in "${SERVICES[@]}"; do
    echo "Deploying $service service..."
    cd ../../services/$service
    serverless deploy --stage $STAGE --region $AWS_REGION
done

echo "Deployment completed successfully!"
```

### **Individual Service Deployment**
```bash
# Deploy a single service
cd services/auth
serverless deploy --stage dev

# Deploy with specific configuration
serverless deploy --stage prod --region us-east-1 --verbose

# Deploy only functions (faster for code changes)
serverless deploy function --function login --stage dev
    dev|staging|prod)
        echo "✅ Valid environment: $ENVIRONMENT"
        ;;
    *)
        echo "❌ Invalid environment. Must be dev, staging, or prod"
        exit 1
        ;;
esac

# Load environment-specific configuration
ENV_CONFIG_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_CONFIG_FILE" ]; then
    echo "❌ Environment configuration file not found: $ENV_CONFIG_FILE"
    exit 1
fi

# Source environment variables
export $(cat $ENV_CONFIG_FILE | grep -v '^#' | xargs)

# Setup Terraform backend if not exists
TERRAFORM_BUCKET="${PROJECT_NAME}-terraform-state-${ENVIRONMENT}"
echo "🏗️ Setting up Terraform backend..."

aws s3 mb s3://$TERRAFORM_BUCKET --region $AWS_REGION 2>/dev/null || echo "Bucket already exists"

# Enable versioning on state bucket
aws s3api put-bucket-versioning \
    --bucket $TERRAFORM_BUCKET \
    --versioning-configuration Status=Enabled

# Create DynamoDB table for state locking
aws dynamodb create-table \
    --table-name terraform-state-lock \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST \
    --region $AWS_REGION 2>/dev/null || echo "DynamoDB table already exists"

echo "✅ Environment setup completed for: $ENVIRONMENT"
```

### **Terraform Deployment Pipeline**
```bash
#!/bin/bash
# scripts/deploy-infrastructure.sh

set -e

ENVIRONMENT=${1:-dev}
AWS_REGION=${2:-us-east-1}
ACTION=${3:-apply}  # apply, plan, or destroy

echo "🏗️ Deploying infrastructure for $ENVIRONMENT (action: $ACTION)"

# Navigate to environment directory
cd terraform/environments/$ENVIRONMENT

# Initialize Terraform
echo "📦 Initializing Terraform..."
terraform init \
    -backend-config="bucket=platform-terraform-state-${ENVIRONMENT}" \
    -backend-config="key=infrastructure/terraform.tfstate" \
    -backend-config="region=${AWS_REGION}" \
    -backend-config="encrypt=true" \
    -backend-config="dynamodb_table=terraform-state-lock"

# Validate configuration
echo "🔍 Validating Terraform configuration..."
terraform validate

# Check formatting
echo "🎨 Checking Terraform formatting..."
terraform fmt -check -recursive || (echo "❌ Terraform files need formatting. Run 'terraform fmt -recursive'" && exit 1)

# Security scan
echo "🔒 Running security scan..."
if command -v tfsec &> /dev/null; then
    tfsec . || echo "⚠️ Security issues found, review before proceeding"
fi

case $ACTION in
    plan)
        echo "📋 Creating Terraform plan..."
        terraform plan -out=tfplan
        ;;
    apply)
        echo "📋 Creating Terraform plan..."
        terraform plan -out=tfplan
        
        echo "🚀 Applying Terraform changes..."
        terraform apply tfplan
        
        echo "📊 Terraform outputs:"
        terraform output
        ;;
    destroy)
        echo "⚠️ WARNING: This will destroy all infrastructure in $ENVIRONMENT!"
        read -p "Are you sure? Type 'yes' to continue: " -r
        if [[ $REPLY == "yes" ]]; then
            terraform destroy -auto-approve
        else
            echo "❌ Destruction cancelled"
            exit 1
        fi
        ;;
    *)
        echo "❌ Invalid action. Must be plan, apply, or destroy"
        exit 1
        ;;
esac

cd ../../..
echo "✅ Infrastructure deployment completed"
```

### **Serverless Deployment Pipeline**
```bash
#!/bin/bash
# scripts/deploy-services.sh

set -e

ENVIRONMENT=${1:-dev}
AWS_REGION=${2:-us-east-1}
SERVICE=${3:-all}  # specific service or 'all'

echo "🚀 Deploying services for $ENVIRONMENT"

# Load environment variables from Terraform outputs
if [ -f "terraform/environments/$ENVIRONMENT/terraform.tfstate" ]; then
    echo "📊 Loading infrastructure outputs..."
    
    # Extract Terraform outputs
    cd terraform/environments/$ENVIRONMENT
    
    export DYNAMODB_TABLE=$(terraform output -raw dynamodb_table_name 2>/dev/null || echo "")
    export DYNAMODB_TABLE_ARN=$(terraform output -raw dynamodb_table_arn 2>/dev/null || echo "")
    export S3_DATA_BUCKET=$(terraform output -raw s3_data_bucket_name 2>/dev/null || echo "")
    export S3_DATA_BUCKET_ARN=$(terraform output -raw s3_data_bucket_arn 2>/dev/null || echo "")
    export KMS_KEY_ARN=$(terraform output -raw kms_key_arn 2>/dev/null || echo "")
    export API_GATEWAY_ID=$(terraform output -raw api_gateway_id 2>/dev/null || echo "")
    
    cd ../../..
else
    echo "❌ Terraform state not found. Deploy infrastructure first."
    exit 1
fi

# Validate required outputs
required_vars=("DYNAMODB_TABLE" "S3_DATA_BUCKET" "KMS_KEY_ARN")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Required Terraform output not found: $var"
        exit 1
    fi
done

# Define services in dependency order
SERVICES_ORDER=(
    "auth-service"
    "tenant-service"
    "user-service"
    "payment-service"
    "agent-service"
    "chat-service"
)

# Deploy specific service or all services
if [ "$SERVICE" == "all" ]; then
    SERVICES_TO_DEPLOY=("${SERVICES_ORDER[@]}")
else
    SERVICES_TO_DEPLOY=("$SERVICE")
fi

# Deploy shared layers first
echo "📦 Deploying shared layers..."
cd serverless/shared/layers/python-requirements

if [ -f "requirements.txt" ]; then
    # Check if layer needs update
    LAYER_HASH=$(md5sum requirements.txt | cut -d' ' -f1)
    DEPLOYED_HASH_FILE=".deployed-${ENVIRONMENT}-hash"
    
    if [ ! -f "$DEPLOYED_HASH_FILE" ] || [ "$(cat $DEPLOYED_HASH_FILE)" != "$LAYER_HASH" ]; then
        echo "🔄 Layer requirements changed, redeploying..."
        npx serverless deploy --stage $ENVIRONMENT --region $AWS_REGION
        echo "$LAYER_HASH" > "$DEPLOYED_HASH_FILE"
    else
        echo "✅ Layer up to date"
    fi
fi

cd ../../../..

# Deploy services
for service in "${SERVICES_TO_DEPLOY[@]}"; do
    echo "🚀 Deploying $service..."
    
    service_path="serverless/services/$service"
    if [ ! -d "$service_path" ]; then
        echo "❌ Service directory not found: $service_path"
        continue
    fi
    
    cd "$service_path"
    
    # Install dependencies if package.json exists
    if [ -f "package.json" ] && [ ! -d "node_modules" ]; then
        echo "📦 Installing npm dependencies..."
        npm ci
    fi
    
    # Deploy service
    echo "🚀 Deploying $service to $ENVIRONMENT..."
    npx serverless deploy \
        --stage $ENVIRONMENT \
        --region $AWS_REGION \
        --verbose
    
    # Verify deployment
    echo "🔍 Verifying $service deployment..."
    npx serverless info --stage $ENVIRONMENT --region $AWS_REGION
    
    cd ../../..
    
    echo "✅ $service deployed successfully"
done

echo "🎉 All services deployed successfully!"

# Run post-deployment health checks
echo "🏥 Running health checks..."
./scripts/health-check.sh $ENVIRONMENT

echo "✅ Deployment completed and verified"
```

## 🔄 CI/CD Pipeline Configuration

### **GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy Platform

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      action:
        description: 'Deployment action'
        required: true
        default: 'deploy'
        type: choice
        options:
          - deploy
          - destroy

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: platform

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.determine-env.outputs.environment }}
      should-deploy: ${{ steps.determine-env.outputs.should-deploy }}
    
    steps:
    - name: Determine environment
      id: determine-env
      run: |
        if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
          echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          echo "should-deploy=true" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" == "refs/heads/main" ]; then
          echo "environment=prod" >> $GITHUB_OUTPUT
          echo "should-deploy=true" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" == "refs/heads/develop" ]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "should-deploy=true" >> $GITHUB_OUTPUT
        else
          echo "environment=dev" >> $GITHUB_OUTPUT
          echo "should-deploy=false" >> $GITHUB_OUTPUT
        fi

  validate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v2
      with:
        terraform_version: '1.5.0'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        npm install -g serverless
    
    - name: Validate Terraform
      run: |
        cd terraform/environments/dev
        terraform init -backend=false
        terraform validate
        terraform fmt -check -recursive
    
    - name: Security scan
      run: |
        # Install security scanners
        pip install bandit safety
        
        # Python security scan
        bandit -r src/ || true
        safety check || true
        
        # Terraform security scan
        if command -v tfsec &> /dev/null; then
          tfsec terraform/ || true
        fi
    
    - name: Lint code
      run: |
        pylint src/ || true
        black --check src/
        isort --check-only src/

  test:
    runs-on: ubuntu-latest
    needs: validate
    
    services:
      localstack:
        image: localstack/localstack
        env:
          SERVICES: dynamodb,s3,ses,lambda,apigateway
          DEBUG: 1
        ports:
          - 4566:4566
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-test.txt
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ \
          --cov=src \
          --cov-report=xml \
          --cov-fail-under=90 \
          --junitxml=test-results/unit-tests.xml
    
    - name: Run integration tests
      env:
        AWS_ENDPOINT_URL: http://localhost:4566
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
        AWS_DEFAULT_REGION: us-east-1
      run: |
        pytest tests/integration/ \
          --junitxml=test-results/integration-tests.xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  deploy-infrastructure:
    runs-on: ubuntu-latest
    needs: [setup, test]
    if: needs.setup.outputs.should-deploy == 'true'
    environment: ${{ needs.setup.outputs.environment }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v2
      with:
        terraform_version: '1.5.0'
        terraform_wrapper: false
    
    - name: Setup environment
      run: |
        chmod +x scripts/setup-environment.sh
        ./scripts/setup-environment.sh ${{ needs.setup.outputs.environment }}
    
    - name: Deploy infrastructure
      run: |
        chmod +x scripts/deploy-infrastructure.sh
        ./scripts/deploy-infrastructure.sh ${{ needs.setup.outputs.environment }} ${{ env.AWS_REGION }} apply
    
    - name: Save Terraform outputs
      id: terraform-outputs
      run: |
        cd terraform/environments/${{ needs.setup.outputs.environment }}
        echo "dynamodb-table=$(terraform output -raw dynamodb_table_name)" >> $GITHUB_OUTPUT
        echo "s3-bucket=$(terraform output -raw s3_data_bucket_name)" >> $GITHUB_OUTPUT
        echo "api-gateway-url=$(terraform output -raw api_gateway_url)" >> $GITHUB_OUTPUT

  deploy-services:
    runs-on: ubuntu-latest
    needs: [setup, deploy-infrastructure]
    if: needs.setup.outputs.should-deploy == 'true'
    environment: ${{ needs.setup.outputs.environment }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install Serverless Framework
      run: npm install -g serverless
    
    - name: Deploy services
      run: |
        chmod +x scripts/deploy-services.sh
        ./scripts/deploy-services.sh ${{ needs.setup.outputs.environment }} ${{ env.AWS_REGION }}

  post-deployment-tests:
    runs-on: ubuntu-latest
    needs: [setup, deploy-services]
    if: needs.setup.outputs.should-deploy == 'true'
    environment: ${{ needs.setup.outputs.environment }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-test.txt
    
    - name: Run health checks
      run: |
        chmod +x scripts/health-check.sh
        ./scripts/health-check.sh ${{ needs.setup.outputs.environment }}
    
    - name: Run smoke tests
      env:
        API_BASE_URL: ${{ needs.deploy-infrastructure.outputs.api-gateway-url }}
      run: |
        pytest tests/smoke/ --junitxml=test-results/smoke-tests.xml
    
    - name: Performance baseline check
      if: needs.setup.outputs.environment == 'prod'
      env:
        API_BASE_URL: ${{ needs.deploy-infrastructure.outputs.api-gateway-url }}
      run: |
        pytest tests/performance/test_baseline.py

  notify:
    runs-on: ubuntu-latest
    needs: [setup, post-deployment-tests]
    if: always() && needs.setup.outputs.should-deploy == 'true'
    
    steps:
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          Deployment to ${{ needs.setup.outputs.environment }} completed
          Status: ${{ job.status }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 🏥 Health Checks & Monitoring

### **Health Check Implementation**
```bash
#!/bin/bash
# scripts/health-check.sh

set -e

ENVIRONMENT=${1:-dev}
MAX_RETRIES=5
RETRY_DELAY=10

echo "🏥 Running health checks for $ENVIRONMENT environment"

# Get API Gateway URL from Terraform outputs
cd terraform/environments/$ENVIRONMENT
API_BASE_URL=$(terraform output -raw api_gateway_url 2>/dev/null)
cd ../../..

if [ -z "$API_BASE_URL" ]; then
    echo "❌ Could not determine API base URL"
    exit 1
fi

echo "🔍 Testing endpoint: $API_BASE_URL"

# Health check function
check_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    echo "🔍 Checking $description..."
    
    for i in $(seq 1 $MAX_RETRIES); do
        response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL$endpoint" || echo "000")
        
        if [ "$response" -eq "$expected_status" ]; then
            echo "✅ $description: OK (HTTP $response)"
            return 0
        else
            echo "⚠️ $description: Failed (HTTP $response), attempt $i/$MAX_RETRIES"
            if [ $i -lt $MAX_RETRIES ]; then
                sleep $RETRY_DELAY
            fi
        fi
    done
    
    echo "❌ $description: Failed after $MAX_RETRIES attempts"
    return 1
}

# Database connectivity check
check_database_connectivity() {
    echo "🔍 Checking database connectivity..."
    
    # Create a test endpoint call that requires DB access
    response=$(curl -s -w "%{http_code}" "$API_BASE_URL/auth/health" 2>/dev/null || echo "000")
    
    if [ "$response" -eq "200" ]; then
        echo "✅ Database connectivity: OK"
        return 0
    else
        echo "❌ Database connectivity: Failed (HTTP $response)"
        return 1
    fi
}

# External service connectivity
check_external_services() {
    echo "🔍 Checking external service connectivity..."
    
    # Check if the service can reach external dependencies
    # This could be done through a dedicated health endpoint
    
    services=("n8n" "stripe" "ses")
    all_services_ok=true
    
    for service in "${services[@]}"; do
        # This would call a health endpoint that checks external service connectivity
        response=$(curl -s -w "%{http_code}" "$API_BASE_URL/health/external/$service" 2>/dev/null || echo "000")
        
        if [ "$response" -eq "200" ]; then
            echo "✅ $service connectivity: OK"
        else
            echo "⚠️ $service connectivity: Issues detected"
            all_services_ok=false
        fi
    done
    
    if [ "$all_services_ok" = true ]; then
        return 0
    else
        return 1
    fi
}

# Performance check
check_performance() {
    echo "🔍 Checking response time performance..."
    
    start_time=$(date +%s.%N)
    response=$(curl -s -o /dev/null -w "%{http_code}:%{time_total}" "$API_BASE_URL/health")
    end_time=$(date +%s.%N)
    
    http_code=$(echo $response | cut -d: -f1)
    response_time=$(echo $response | cut -d: -f2)
    
    if [ "$http_code" -eq "200" ]; then
        # Check if response time is under threshold (2 seconds)
        threshold=2.0
        if (( $(echo "$response_time < $threshold" | bc -l) )); then
            echo "✅ Performance: OK (${response_time}s)"
            return 0
        else
            echo "⚠️ Performance: Slow response (${response_time}s > ${threshold}s)"
            return 1
        fi
    else
        echo "❌ Performance: Health endpoint failed (HTTP $http_code)"
        return 1
    fi
}

# Run all health checks
echo "🏥 Starting comprehensive health checks..."

FAILED_CHECKS=0

# Basic endpoint checks
check_endpoint "/health" 200 "Basic health check" || ((FAILED_CHECKS++))
check_endpoint "/auth/health" 200 "Authentication service health" || ((FAILED_CHECKS++))

# Detailed checks
check_database_connectivity || ((FAILED_CHECKS++))
check_external_services || ((FAILED_CHECKS++))
check_performance || ((FAILED_CHECKS++))

# Authentication flow test
echo "🔍 Testing authentication flow..."
auth_response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"invalid"}' \
    -w "%{http_code}")

if echo "$auth_response" | grep -q "401"; then
    echo "✅ Authentication flow: OK (proper rejection of invalid credentials)"
else
    echo "❌ Authentication flow: Unexpected response"
    ((FAILED_CHECKS++))
fi

# Summary
echo ""
echo "🏥 Health Check Summary:"
echo "========================"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo "✅ All health checks passed"
    echo "🎉 Environment $ENVIRONMENT is healthy and ready"
    exit 0
else
    echo "❌ $FAILED_CHECKS health check(s) failed"
    echo "⚠️ Environment $ENVIRONMENT has issues that need attention"
    exit 1
fi
```

### **Monitoring Setup**
```python
# scripts/setup-monitoring.py
import boto3
import json
from typing import Dict, List

class MonitoringSetup:
    """Setup comprehensive monitoring for the platform."""
    
    def __init__(self, environment: str, region: str = 'us-east-1'):
        self.environment = environment
        self.region = region
        self.cloudwatch = boto3.client('cloudwatch', region_name=region)
        self.logs = boto3.client('logs', region_name=region)
        self.sns = boto3.client('sns', region_name=region)
    
    def create_alarms(self):
        """Create CloudWatch alarms for critical metrics."""
        
        alarms = [
            {
                'name': f'platform-{self.environment}-api-gateway-errors',
                'description': 'API Gateway 5xx errors',
                'metric_name': '5XXError',
                'namespace': 'AWS/ApiGateway',
                'statistic': 'Sum',
                'threshold': 10,
                'comparison': 'GreaterThanThreshold',
                'evaluation_periods': 2,
                'period': 300,
                'dimensions': [
                    {'Name': 'ApiName', 'Value': f'platform-api-{self.environment}'}
                ]
            },
            {
                'name': f'platform-{self.environment}-lambda-errors',
                'description': 'Lambda function errors',
                'metric_name': 'Errors',
                'namespace': 'AWS/Lambda',
                'statistic': 'Sum',
                'threshold': 5,
                'comparison': 'GreaterThanThreshold',
                'evaluation_periods': 2,
                'period': 300
            },
            {
                'name': f'platform-{self.environment}-lambda-duration',
                'description': 'Lambda function duration',
                'metric_name': 'Duration',
                'namespace': 'AWS/Lambda',
                'statistic': 'Average',
                'threshold': 10000,  # 10 seconds
                'comparison': 'GreaterThanThreshold',
                'evaluation_periods': 3,
                'period': 300
            },
            {
                'name': f'platform-{self.environment}-dynamodb-throttles',
                'description': 'DynamoDB throttling',
                'metric_name': 'UserErrors',
                'namespace': 'AWS/DynamoDB',
                'statistic': 'Sum',
                'threshold': 1,
                'comparison': 'GreaterThanThreshold',
                'evaluation_periods': 1,
                'period': 300,
                'dimensions': [
                    {'Name': 'TableName', 'Value': f'platform-main-{self.environment}'}
                ]
            }
        ]
        
        # Create SNS topic for alerts
        topic_arn = self.create_sns_topic()
        
        for alarm in alarms:
            self.create_alarm(alarm, topic_arn)
    
    def create_sns_topic(self) -> str:
        """Create SNS topic for alerts."""
        topic_name = f'platform-{self.environment}-alerts'
        
        try:
            response = self.sns.create_topic(Name=topic_name)
            topic_arn = response['TopicArn']
            
            # Subscribe email endpoint (would be configured via environment variable)
            # self.sns.subscribe(
            #     TopicArn=topic_arn,
            #     Protocol='email',
            #     Endpoint='<EMAIL>'
            # )
            
            return topic_arn
            
        except Exception as e:
            print(f"Error creating SNS topic: {e}")
            return ""
    
    def create_alarm(self, alarm_config: Dict, topic_arn: str):
        """Create individual CloudWatch alarm."""
        
        try:
            self.cloudwatch.put_metric_alarm(
                AlarmName=alarm_config['name'],
                ComparisonOperator=alarm_config['comparison'],
                EvaluationPeriods=alarm_config['evaluation_periods'],
                MetricName=alarm_config['metric_name'],
                Namespace=alarm_config['namespace'],
                Period=alarm_config['period'],
                Statistic=alarm_config['statistic'],
                Threshold=alarm_config['threshold'],
                ActionsEnabled=True,
                AlarmActions=[topic_arn] if topic_arn else [],
                AlarmDescription=alarm_config['description'],
                Dimensions=alarm_config.get('dimensions', []),
                Unit='Count'
            )
            
            print(f"✅ Created alarm: {alarm_config['name']}")
            
        except Exception as e:
            print(f"❌ Error creating alarm {alarm_config['name']}: {e}")
    
    def create_dashboard(self):
        """Create CloudWatch dashboard."""
        
        dashboard_body = {
            "widgets": [
                {
                    "type": "metric",
                    "x": 0, "y": 0,
                    "width": 12, "height": 6,
                    "properties": {
                        "metrics": [
                            ["AWS/ApiGateway", "Count", "ApiName", f"platform-api-{self.environment}"],
                            [".", "Latency", ".", "."],
                            [".", "4XXError", ".", "."],
                            [".", "5XXError", ".", "."]
                        ],
                        "view": "timeSeries",
                        "stacked": False,
                        "region": self.region,
                        "title": "API Gateway Metrics",
                        "period": 300
                    }
                },
                {
                    "type": "metric",
                    "x": 12, "y": 0,
                    "width": 12, "height": 6,
                    "properties": {
                        "metrics": [
                            ["AWS/Lambda", "Invocations"],
                            [".", "Errors"],
                            [".", "Duration"],
                            [".", "Throttles"]
                        ],
                        "view": "timeSeries",
                        "stacked": False,
                        "region": self.region,
                        "title": "Lambda Metrics",
                        "period": 300
                    }
                },
                {
                    "type": "metric",
                    "x": 0, "y": 6,
                    "width": 24, "height": 6,
                    "properties": {
                        "metrics": [
                            ["AWS/DynamoDB", "ConsumedReadCapacityUnits", "TableName", f"platform-main-{self.environment}"],
                            [".", "ConsumedWriteCapacityUnits", ".", "."],
                            [".", "SuccessfulRequestLatency", ".", "."]
                        ],
                        "view": "timeSeries",
                        "stacked": False,
                        "region": self.region,
                        "title": "DynamoDB Metrics",
                        "period": 300
                    }
                }
            ]
        }
        
        try:
            self.cloudwatch.put_dashboard(
                DashboardName=f'Platform-{self.environment.title()}',
                DashboardBody=json.dumps(dashboard_body)
            )
            
            print(f"✅ Created dashboard: Platform-{self.environment.title()}")
            
        except Exception as e:
            print(f"❌ Error creating dashboard: {e}")
    
    def setup_log_retention(self):
        """Set up log retention policies."""
        
        retention_days = {
            'dev': 7,
            'staging': 14,
            'prod': 30
        }
        
        # Get all log groups for the environment
        paginator = self.logs.get_paginator('describe_log_groups')
        
        for page in paginator.paginate():
            for log_group in page['logGroups']:
                log_group_name = log_group['logGroupName']
                
                # Only process platform log groups
                if f'platform-{self.environment}' in log_group_name or f'/aws/lambda/platform' in log_group_name:
                    try:
                        self.logs.put_retention_policy(
                            logGroupName=log_group_name,
                            retentionInDays=retention_days[self.environment]
                        )
                        
                        print(f"✅ Set retention for {log_group_name}: {retention_days[self.environment]} days")
                        
                    except Exception as e:
                        print(f"❌ Error setting retention for {log_group_name}: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python setup-monitoring.py <environment>")
        sys.exit(1)
    
    environment = sys.argv[1]
    
    if environment not in ['dev', 'staging', 'prod']:
        print("Environment must be dev, staging, or prod")
        sys.exit(1)
    
    print(f"🔧 Setting up monitoring for {environment} environment...")
    
    monitoring = MonitoringSetup(environment)
    
    print("📊 Creating CloudWatch alarms...")
    monitoring.create_alarms()
    
    print("📈 Creating dashboard...")
    monitoring.create_dashboard()
    
    print("📝 Setting up log retention...")
    monitoring.setup_log_retention()
    
    print("✅ Monitoring setup completed!")
```

## 🔧 Operations Procedures

### **Backup and Recovery**
```bash
#!/bin/bash
# scripts/backup-restore.sh

set -e

ENVIRONMENT=${1:-dev}
ACTION=${2:-backup}  # backup or restore
BACKUP_DATE=${3:-$(date +%Y-%m-%d)}

case $ACTION in
    backup)
        echo "📦 Creating backup for $ENVIRONMENT environment..."
        
        # DynamoDB backup
        echo "💾 Creating DynamoDB backup..."
        TABLE_NAME="platform-main-$ENVIRONMENT"
        
        aws dynamodb create-backup \
            --table-name $TABLE_NAME \
            --backup-name "${TABLE_NAME}-backup-${BACKUP_DATE}"
        
        # S3 backup (versioning enabled, so no additional action needed)
        echo "📁 S3 data is protected by versioning and cross-region replication"
        
        # Export Terraform state
        echo "🏗️ Backing up Terraform state..."
        aws s3 cp \
            "s3://platform-terraform-state-${ENVIRONMENT}/infrastructure/terraform.tfstate" \
            "./backups/terraform-state-${ENVIRONMENT}-${BACKUP_DATE}.json"
        
        echo "✅ Backup completed for $ENVIRONMENT"
        ;;
        
    restore)
        echo "⚠️ WARNING: This will restore $ENVIRONMENT to state from $BACKUP_DATE"
        read -p "Are you sure? Type 'yes' to continue: " -r
        
        if [[ $REPLY == "yes" ]]; then
            echo "🔄 Restoring $ENVIRONMENT from $BACKUP_DATE..."
            
            # This would involve point-in-time recovery for DynamoDB
            # and restoring from S3 versions
            # Implementation depends on specific recovery requirements
            
            echo "✅ Restore completed"
        else
            echo "❌ Restore cancelled"
        fi
        ;;
        
    *)
        echo "❌ Invalid action. Must be 'backup' or 'restore'"
        exit 1
        ;;
esac
```

### **Disaster Recovery Plan**
```yaml
# docs/disaster-recovery-plan.yml
Disaster Recovery Plan:
  RTO: 4 hours  # Recovery Time Objective
  RPO: 1 hour   # Recovery Point Objective
  
  Scenarios:
    - name: AWS Region Failure
      probability: Low
      impact: High
      response:
        - Activate secondary region (us-west-2)
        - Update DNS to point to backup region
        - Restore data from cross-region replicas
        - Validate all services operational
      
    - name: DynamoDB Service Outage
      probability: Very Low
      impact: High
      response:
        - Activate DynamoDB Global Tables failover
        - Update application configuration
        - Monitor data consistency
        
    - name: Lambda Service Outage
      probability: Very Low
      impact: High
      response:
        - Scale containerized alternatives (if available)
        - Implement temporary API responses
        - Communicate with users about limited functionality
        
    - name: Security Breach
      probability: Medium
      impact: Critical
      response:
        - Immediately isolate affected systems
        - Rotate all credentials and keys
        - Conduct security assessment
        - Notify customers and authorities as required
        
  Contact Information:
    - On-Call Engineer: +1-XXX-XXX-XXXX
    - Security Team: <EMAIL>
    - AWS Support: Enterprise Support Plan
    
  Communication Plan:
    - Internal: Slack #incidents channel
    - External: Status page + email notifications
    - Stakeholders: Direct communication within 30 minutes
```

### **Scaling Procedures**
```bash
#!/bin/bash
# scripts/scaling-operations.sh

set -e

ENVIRONMENT=${1:-prod}
OPERATION=${2:-status}  # scale-up, scale-down, status

case $OPERATION in
    scale-up)
        echo "📈 Scaling up $ENVIRONMENT environment..."
        
        # Increase DynamoDB capacity (if using provisioned mode)
        if [ "$ENVIRONMENT" == "prod" ]; then
            aws dynamodb update-table \
                --table-name "platform-main-$ENVIRONMENT" \
                --provisioned-throughput ReadCapacityUnits=1000,WriteCapacityUnits=1000
        fi
        
        # Increase Lambda reserved concurrency
        FUNCTIONS=("auth-login" "auth-register" "agent-proxy")
        for func in "${FUNCTIONS[@]}"; do
            aws lambda put-reserved-concurrency-config \
                --function-name "platform-${func}-${ENVIRONMENT}" \
                --reserved-concurrent-executions 500
        done
        
        echo "✅ Scale-up completed"
        ;;
        
    scale-down)
        echo "📉 Scaling down $ENVIRONMENT environment..."
        
        # Reduce Lambda reserved concurrency
        FUNCTIONS=("auth-login" "auth-register" "agent-proxy")
        for func in "${FUNCTIONS[@]}"; do
            aws lambda put-reserved-concurrency-config \
                --function-name "platform-${func}-${ENVIRONMENT}" \
                --reserved-concurrent-executions 100
        done
        
        echo "✅ Scale-down completed"
        ;;
        
    status)
        echo "📊 Current scaling status for $ENVIRONMENT:"
        
        # DynamoDB capacity
        aws dynamodb describe-table \
            --table-name "platform-main-$ENVIRONMENT" \
            --query 'Table.{ReadCapacity:ProvisionedThroughput.ReadCapacityUnits,WriteCapacity:ProvisionedThroughput.WriteCapacityUnits}' \
            --output table
        
        # Lambda concurrency
        echo "Lambda Reserved Concurrency:"
        FUNCTIONS=("auth-login" "auth-register" "agent-proxy")
        for func in "${FUNCTIONS[@]}"; do
            concurrency=$(aws lambda get-reserved-concurrency-config \
                --function-name "platform-${func}-${ENVIRONMENT}" \
                --query 'ReservedConcurrencyConfig.ReservedConcurrentExecutions' \
                --output text 2>/dev/null || echo "Not set")
            echo "  $func: $concurrency"
        done
        ;;
        
    *)
        echo "❌ Invalid operation. Must be 'scale-up', 'scale-down', or 'status'"
        exit 1
        ;;
esac
```

## 📊 Performance Monitoring

### **Real-time Metrics Dashboard**
```python
# scripts/create-performance-dashboard.py
import boto3
import json
from datetime import datetime, timedelta

def create_performance_dashboard(environment: str):
    """Create a comprehensive performance monitoring dashboard."""
    
    cloudwatch = boto3.client('cloudwatch')
    
    dashboard_body = {
        "widgets": [
            # API Performance Overview
            {
                "type": "metric",
                "x": 0, "y": 0,
                "width": 8, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/ApiGateway", "Latency", "ApiName", f"platform-api-{environment}", {"stat": "Average"}],
                        [".", ".", ".", ".", {"stat": "p95"}],
                        [".", ".", ".", ".", {"stat": "p99"}]
                    ],
                    "view": "timeSeries",
                    "stacked": False,
                    "region": "us-east-1",
                    "title": "API Latency (ms)",
                    "period": 300,
                    "yAxis": {"left": {"min": 0}},
                    "annotations": {
                        "horizontal": [
                            {"label": "SLA Threshold", "value": 500}
                        ]
                    }
                }
            },
            
            # Throughput
            {
                "type": "metric",
                "x": 8, "y": 0,
                "width": 8, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/ApiGateway", "Count", "ApiName", f"platform-api-{environment}"]
                    ],
                    "view": "timeSeries",
                    "stacked": False,
                    "region": "us-east-1",
                    "title": "API Throughput (requests/min)",
                    "period": 300,
                    "stat": "Sum"
                }
            },
            
            # Error Rates
            {
                "type": "metric",
                "x": 16, "y": 0,
                "width": 8, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/ApiGateway", "4XXError", "ApiName", f"platform-api-{environment}"],
                        [".", "5XXError", ".", "."]
                    ],
                    "view": "timeSeries",
                    "stacked": False,
                    "region": "us-east-1",
                    "title": "Error Rates",
                    "period": 300,
                    "stat": "Sum"
                }
            },
            
            # Lambda Performance
            {
                "type": "metric",
                "x": 0, "y": 6,
                "width": 12, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/Lambda", "Duration", {"stat": "Average"}],
                        [".", ".", {"stat": "p95"}],
                        [".", "ConcurrentExecutions", {"stat": "Maximum"}],
                        [".", "Throttles", {"stat": "Sum"}]
                    ],
                    "view": "timeSeries",
                    "stacked": False,
                    "region": "us-east-1",
                    "title": "Lambda Performance",
                    "period": 300
                }
            },
            
            # DynamoDB Performance
            {
                "type": "metric",
                "x": 12, "y": 6,
                "width": 12, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/DynamoDB", "SuccessfulRequestLatency", "TableName", f"platform-main-{environment}", "Operation", "Query"],
                        [".", ".", ".", ".", ".", "GetItem"],
                        [".", ".", ".", ".", ".", "PutItem"],
                        [".", "ConsumedReadCapacityUnits", ".", ".", {"stat": "Sum"}],
                        [".", "ConsumedWriteCapacityUnits", ".", ".", {"stat": "Sum"}]
                    ],
                    "view": "timeSeries",
                    "stacked": False,
                    "region": "us-east-1",
                    "title": "DynamoDB Performance",
                    "period": 300
                }
            },
            
            # Custom Business Metrics
            {
                "type": "metric",
                "x": 0, "y": 12,
                "width": 24, "height": 6,
                "properties": {
                    "metrics": [
                        ["Platform/Business", "UserRegistrations", {"stat": "Sum"}],
                        [".", "ConversationsStarted", {"stat": "Sum"}],
                        [".", "AgentInteractions", {"stat": "Sum"}],
                        [".", "PaymentTransactions", {"stat": "Sum"}]
                    ],
                    "view": "timeSeries",
                    "stacked": False,
                    "region": "us-east-1",
                    "title": "Business Metrics",
                    "period": 300
                }
            }
        ]
    }
    
    try:
        cloudwatch.put_dashboard(
            DashboardName=f'Platform-Performance-{environment.title()}',
            DashboardBody=json.dumps(dashboard_body)
        )
        print(f"✅ Created performance dashboard: Platform-Performance-{environment.title()}")
        
    except Exception as e:
        print(f"❌ Error creating performance dashboard: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python create-performance-dashboard.py <environment>")
        sys.exit(1)
    
    environment = sys.argv[1]
    create_performance_dashboard(environment)
```

## 🔄 Rollback Procedures

### **Automated Rollback Script**
```bash
#!/bin/bash
# scripts/rollback.sh

set -e

ENVIRONMENT=${1:-staging}
COMPONENT=${2:-all}  # all, infrastructure, services
ROLLBACK_VERSION=${3}

echo "🔄 Rolling back $COMPONENT in $ENVIRONMENT environment"

rollback_infrastructure() {
    echo "🏗️ Rolling back infrastructure..."
    
    cd terraform/environments/$ENVIRONMENT
    
    if [ -n "$ROLLBACK_VERSION" ]; then
        # Rollback to specific version
        git checkout $ROLLBACK_VERSION -- .
        terraform plan -out=rollback.tfplan
        terraform apply rollback.tfplan
    else
        # Rollback to previous known good state
        aws s3 cp \
            "s3://platform-terraform-state-${ENVIRONMENT}/infrastructure/terraform.tfstate.backup" \
            "s3://platform-terraform-state-${ENVIRONMENT}/infrastructure/terraform.tfstate"
        
        terraform refresh
    fi
    
    cd ../../..
    echo "✅ Infrastructure rollback completed"
}

rollback_services() {
    echo "🚀 Rolling back services..."
    
    if [ -n "$ROLLBACK_VERSION" ]; then
        # Rollback to specific version
        git checkout $ROLLBACK_VERSION -- serverless/
    fi
    
    # Redeploy services
    ./scripts/deploy-services.sh $ENVIRONMENT
    
    echo "✅ Services rollback completed"
}

# Validation before rollback
echo "⚠️ WARNING: This will rollback $COMPONENT in $ENVIRONMENT"
if [ "$ENVIRONMENT" == "prod" ]; then
    echo "🚨 PRODUCTION ROLLBACK - This action affects live users!"
    read -p "Are you absolutely sure? Type 'ROLLBACK PROD' to continue: " -r
    if [[ $REPLY != "ROLLBACK PROD" ]]; then
        echo "❌ Rollback cancelled"
        exit 1
    fi
fi

# Create backup of current state before rollback
echo "📦 Creating backup of current state..."
BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Backup current Git state
git stash push -m "Pre-rollback backup $BACKUP_TIMESTAMP"

# Backup Terraform state
aws s3 cp \
    "s3://platform-terraform-state-${ENVIRONMENT}/infrastructure/terraform.tfstate" \
    "s3://platform-terraform-state-${ENVIRONMENT}/infrastructure/terraform.tfstate.pre-rollback-${BACKUP_TIMESTAMP}"

# Execute rollback
case $COMPONENT in
    infrastructure)
        rollback_infrastructure
        ;;
    services)
        rollback_services
        ;;
    all)
        rollback_infrastructure
        rollback_services
        ;;
    *)
        echo "❌ Invalid component. Must be 'infrastructure', 'services', or 'all'"
        exit 1
        ;;
esac

# Run health checks after rollback
echo "🏥 Running post-rollback health checks..."
./scripts/health-check.sh $ENVIRONMENT

if [ $? -eq 0 ]; then
    echo "✅ Rollback completed successfully"
    echo "📊 Environment $ENVIRONMENT is healthy after rollback"
else
    echo "❌ Health checks failed after rollback"
    echo "🚨 Manual intervention required"
    exit 1
fi

# Notify team
echo "📢 Notifying team about rollback..."
# This would integrate with Slack or other notification systems
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** DevOps & Operations Team  
**Review Cycle:** Weekly during development, monthly post-launch

---

## 🎯 Knowledge Base Summary

La base de conocimientos está ahora **completa** con los siguientes 10 documentos especializados:

1. **Project Overview & Business Requirements** - Contexto del negocio y objetivos
2. **Technical Architecture Documentation** - Arquitectura técnica detallada
3. **API Specifications & Contracts** - Especificaciones completas de APIs
4. **Database Schema & Data Models** - Modelos de datos y esquemas de BD
5. **Integration Specifications** - Integraciones con n8n y pagos
6. **Security & Multi-tenancy Guidelines** - Seguridad y aislamiento de tenants
7. **Development Standards & Best Practices** - Estándares de desarrollo
8. **Infrastructure as Code Configuration** - Configuración de IaC con Terraform
9. **Testing Strategy & Quality Assurance** - Estrategia completa de testing
10. **Deployment & Operations Guide** - Guía de despliegue y operaciones

Esta base de conocimientos está diseñada para que un agente de Claude pueda entender completamente el contexto del proyecto y ejecutar tareas específicas de desarrollo, siguiendo las mejores prácticas y manteniendo la consistencia arquitectónica.

¿Estás listo para que proceda con la creación del plan de desarrollo paso a paso con tareas puntuales?