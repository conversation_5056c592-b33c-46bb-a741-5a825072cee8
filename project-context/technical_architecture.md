# Technical Architecture Documentation

## 🏗️ Architecture Overview

### **Current High-Level Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   API Gateway    │    │   Microservices │
│   (Web/Mobile)  │◄──►│   + Custom Domain│◄──►│   (Lambda)      │
│                 │    │   + Rate Limiting│    │   Python 3.11   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                │                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudFront    │    │   Shared Layer   │    │   DynamoDB      │
│   CDN           │    │   (Lambda Layer) │    │   Multi-tenant  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                │                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   External APIs │    │   AWS Secrets    │    │   CloudWatch    │
│   Stripe, SES   │    │   Manager        │    │   + X-Ray       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### **Microservices Architecture**

```
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│   Auth Service  │ Payment Service │ Tenant Service  │  User Service   │
├─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ • Login/Register│ • Subscriptions │ • Provisioning │ • Profile Mgmt  │
│ • JWT Tokens    │ • Stripe Integ. │ • Multi-tenancy │ • Permissions   │
│ • Authorization │ • Billing       │ • Configuration │ • User Roles    │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
                              │
                    ┌─────────────────┐
                    │  Shared Layer   │
                    │ • Auth & Valid. │
                    │ • Error Handling│
                    │ • Database      │
                    │ • Logging       │
                    └─────────────────┘
```

### **Core Design Principles**

1. **Serverless-First:** All compute via AWS Lambda functions
2. **Microservices:** Independent, deployable services with shared layer
3. **Multi-tenant Isolation:** Tenant-aware data partitioning in DynamoDB
4. **API-First:** RESTful APIs with standardized responses
5. **Infrastructure as Code:** 100% Serverless Framework managed
6. **Security by Design:** JWT authentication with role-based authorization
7. **Design Patterns:** Dependency injection, repository pattern, decorators
8. **Observability:** Structured logging with CloudWatch and X-Ray tracing

## 🔧 Technology Stack

### **Current Technology Stack**
```yaml
# Compute & Runtime
Compute: AWS Lambda (Python 3.11)
Framework: Serverless Framework
Shared Code: Lambda Layers

# API & Gateway
API Layer: AWS API Gateway REST
Authentication: JWT with AWS Secrets Manager
Rate Limiting: API Gateway built-in

# Database & Storage
Primary Database: Amazon DynamoDB
Multi-tenancy: Partition key isolation
Secrets: AWS Secrets Manager

# Development & Deployment
Language: Python 3.11
Package Management: pip + requirements.txt
Infrastructure: Serverless Framework YAML
Deployment: Independent service deployment
Storage: S3 + CloudFront
Authentication: JWT + API Gateway Authorizers
Message Queue: SQS + SNS
Event Processing: EventBridge
Monitoring: CloudWatch + X-Ray
```

### **Infrastructure & DevOps**
```yaml
# Infrastructure as Code
IaC: Serverless Framework (YAML)
Shared Resources: Centralized configuration
Service Deployment: Independent per service

# Development & Quality
Testing: pytest with comprehensive coverage
Code Quality: flake8, black (formatting)
Type Checking: mypy (optional)
Documentation: Inline docstrings + ADRs

# CI/CD & Version Control
Version Control: Git
Deployment: Manual deployment per service
Environment Management: Dev, staging, production
```

### **External Integrations**
```yaml
# Payment Processing
Payments: Stripe API (subscriptions, webhooks)

# Communication
Email: AWS SES (transactional emails)

# Monitoring & Analytics
Logging: CloudWatch Logs (structured JSON)
Metrics: CloudWatch custom metrics
Tracing: AWS X-Ray distributed tracing
Health Checks: Custom health endpoints
```

## 🏢 Multi-Tenant Architecture

### **Tenant Isolation Strategy**

#### **Data Isolation: Pool Model with Tenant-Aware Partitioning**
```
DynamoDB Partition Strategy:
PK: {tenant_id}#{entity_type}#{entity_id}
SK: {sort_key}

Example:
PK: tenant_123#USER#user_456
SK: profile

GSI1PK: USER#{user_id}  
GSI1SK: {tenant_id}
```

#### **Compute Isolation: Shared Lambda with Context Injection**
```python
# Tenant context middleware
@tenant_context_required
def lambda_handler(event, context):
    tenant_id = event['requestContext']['authorizer']['tenant_id']
    user_id = event['requestContext']['authorizer']['user_id']
    
    # All operations are tenant-scoped
    return business_logic(tenant_id, user_id, event['body'])
```

#### **Storage Isolation: Per-Tenant S3 Prefixes**
```
Bucket Structure:
app-data-{environment}/
├── tenant-{tenant_id}/
│   ├── uploads/
│   ├── processed/
│   └── exports/
```

### **Resource Provisioning Strategy**

#### **Tenant Onboarding Workflow**
```python
# Terraform module per tenant
def provision_tenant(tenant_id: str):
    resources = {
        'dynamodb_indexes': create_tenant_gsi(),
        's3_prefixes': create_tenant_folders(),
        'iam_policies': create_tenant_policies(),
        'elasticache_keys': setup_tenant_cache_namespace(),
        'cloudwatch_dashboard': create_tenant_dashboard()
    }
    return resources
```

## 🎯 Microservices Architecture

### **Service Breakdown**

#### **1. Auth Service**
```yaml
Responsibility: Authentication & Authorization
Functions:
  - auth-register
  - auth-login  
  - auth-refresh
  - auth-verify
  - auth-forgot-password
  - auth-reset-password
Database: Users table, Verification codes
External: SES for emails
```

#### **2. Tenant Service**
```yaml
Responsibility: Tenant management & provisioning
Functions:
  - tenant-create
  - tenant-configure
  - tenant-suspend
  - tenant-resume
Database: Tenants table, Configuration
External: Terraform automation
```

#### **3. User Management Service**
```yaml
Responsibility: User roles & permissions within tenants
Functions:
  - user-invite
  - user-manage
  - user-permissions
  - user-profile
Database: Users table, Roles table
External: SES for invitations
```

#### **4. Payment Service**
```yaml
Responsibility: Billing & subscription management
Functions:
  - payment-subscribe
  - payment-webhook
  - payment-cancel
  - payment-history
Database: Subscriptions, Invoices, Usage
External: Stripe/PayU APIs
```

#### **5. Agent Service**
```yaml
Responsibility: Agent integration & conversation management
Functions:
  - agent-proxy
  - conversation-create
  - conversation-history
  - agent-availability
Database: Conversations, Messages
External: n8n HTTP APIs
```

#### **6. Chat Service** 
```yaml
Responsibility: Real-time WebSocket connections
Functions:
  - websocket-connect
  - websocket-disconnect
  - websocket-message
Database: Connections table
External: Agent Service
```

#### **7. Notification Service**
```yaml
Responsibility: Email & push notifications
Functions:
  - notification-send
  - notification-template
  - notification-preferences
Database: Notification logs
External: SES, SNS
```

## 🔐 Security Architecture

### **Authentication & Authorization**

#### **JWT Token Strategy**
```python
# Token structure
access_token = {
    'user_id': 'user_123',
    'tenant_id': 'tenant_456', 
    'role': 'MASTER|MEMBER|SUSPENDED',
    'permissions': ['agents:read', 'users:write'],
    'exp': 3600  # 1 hour
}

refresh_token = {
    'user_id': 'user_123',
    'tenant_id': 'tenant_456',
    'exp': 2592000  # 30 days
}
```

#### **API Gateway Authorizer**
```python
def authorizer_handler(event, context):
    token = extract_token(event['authorizationToken'])
    claims = verify_jwt(token)
    
    return {
        'principalId': claims['user_id'],
        'policyDocument': generate_iam_policy(claims),
        'context': {
            'user_id': claims['user_id'],
            'tenant_id': claims['tenant_id'],
            'role': claims['role']
        }
    }
```

### **Data Protection**

#### **Encryption Strategy**
```yaml
At Rest:
  - DynamoDB: Customer-managed KMS keys per tenant
  - S3: Server-side encryption with tenant-specific keys
  - ElastiCache: Encryption in transit and at rest

In Transit:
  - TLS 1.3 for all API communications
  - VPC endpoints for internal AWS service communication
  - Certificate pinning for mobile apps (future)

In Memory:
  - Lambda environment variables encrypted
  - Sensitive data never logged
  - Memory scrubbing for credentials
```

#### **Secrets Management**
```python
# AWS Secrets Manager integration
import boto3

def get_secret(secret_name: str, tenant_id: str = None):
    client = boto3.client('secretsmanager')
    secret_key = f"{secret_name}"
    if tenant_id:
        secret_key = f"tenant/{tenant_id}/{secret_name}"
    
    return client.get_secret_value(SecretId=secret_key)
```

### **Network Security**

#### **VPC Configuration**
```
Public Subnets: API Gateway, NAT Gateway
Private Subnets: Lambda functions, ElastiCache
Database Subnets: DynamoDB VPC endpoints
Security Groups: Restrictive ingress/egress rules
```

#### **WAF Rules**
```yaml
Rate Limiting: 1000 requests/minute per IP
Geographic Restrictions: Configurable by tenant
SQL Injection Protection: AWS Managed Rules
XSS Protection: AWS Managed Rules
Known Bad Inputs: AWS Managed Rules
```

## 📊 Data Architecture

### **Database Design Philosophy**

#### **DynamoDB Single-Table Design**
```
Table: platform-main-{environment}

Entity Types:
- TENANT#{tenant_id}
- USER#{user_id} 
- SUBSCRIPTION#{subscription_id}
- CONVERSATION#{conversation_id}
- MESSAGE#{message_id}
- INVOICE#{invoice_id}
```

#### **Access Patterns & Indexes**

**Primary Access Patterns:**
1. Get user by ID → PK: USER#{user_id}
2. Get all users in tenant → GSI1: tenant_id, entity_type=USER
3. Get conversations by user → GSI2: user_id, entity_type=CONVERSATION
4. Get messages in conversation → Query: PK=CONVERSATION#{conv_id}
5. Get tenant subscription → PK: TENANT#{tenant_id}, SK: subscription

**Global Secondary Indexes:**
```
GSI1: tenant_id (PK), entity_type (SK)
GSI2: user_id (PK), created_at (SK)  
GSI3: subscription_status (PK), expiry_date (SK)
GSI4: entity_type (PK), updated_at (SK)
```

### **Caching Strategy**

#### **ElastiCache Redis Patterns**
```python
# Cache layers
CACHE_PATTERNS = {
    'user_profile': 'user:{user_id}:profile',
    'tenant_config': 'tenant:{tenant_id}:config',
    'conversation_history': 'conv:{conversation_id}:messages',
    'agent_availability': 'tenant:{tenant_id}:agents',
    'subscription_status': 'tenant:{tenant_id}:subscription'
}

# TTL strategies
TTL_SETTINGS = {
    'user_profile': 3600,      # 1 hour
    'tenant_config': 1800,     # 30 minutes  
    'conversation_history': 300, # 5 minutes
    'agent_availability': 60,   # 1 minute
    'subscription_status': 900  # 15 minutes
}
```

## 🔄 Event-Driven Architecture

### **Event Flow Design**

#### **Domain Events**
```python
class DomainEvent:
    def __init__(self, event_type: str, tenant_id: str, payload: dict):
        self.event_type = event_type
        self.tenant_id = tenant_id
        self.payload = payload
        self.timestamp = datetime.utcnow()
        self.event_id = str(uuid4())

# Event types
EVENTS = {
    'TENANT_CREATED': 'tenant.created',
    'USER_REGISTERED': 'user.registered', 
    'PAYMENT_SUCCEEDED': 'payment.succeeded',
    'PAYMENT_FAILED': 'payment.failed',
    'SUBSCRIPTION_CANCELLED': 'subscription.cancelled',
    'CONVERSATION_STARTED': 'conversation.started',
    'MESSAGE_SENT': 'message.sent'
}
```

#### **Event Processing Patterns**

**SQS Queue Configuration:**
```yaml
Queues:
  tenant-provisioning:
    visibility_timeout: 300
    message_retention: 1209600  # 14 days
    dlq_max_receives: 3
    
  payment-processing:
    visibility_timeout: 60
    message_retention: 86400    # 1 day
    dlq_max_receives: 5
    
  notification-delivery:
    visibility_timeout: 30
    message_retention: 43200    # 12 hours
    dlq_max_receives: 3
```

### **SAGA Pattern for Distributed Transactions**

#### **User Registration SAGA**
```python
# Registration workflow steps
REGISTRATION_SAGA = [
    'validate_company_data',
    'create_tenant_record', 
    'create_user_record',
    'process_payment',
    'provision_tenant_resources',
    'send_welcome_email',
    'activate_user_account'
]

# Compensation actions
COMPENSATION_ACTIONS = {
    'create_tenant_record': 'delete_tenant_record',
    'create_user_record': 'delete_user_record', 
    'process_payment': 'refund_payment',
    'provision_tenant_resources': 'deprovision_tenant_resources'
}
```

## 🚀 Performance Architecture

### **Auto-Scaling Strategy**

#### **Lambda Concurrency Configuration**
```yaml
Reserved Concurrency:
  auth-functions: 100
  agent-proxy: 500
  chat-websocket: 200
  payment-webhook: 50

Provisioned Concurrency:
  auth-login: 20  # Keep warm for fast response
  agent-proxy: 50 # High-frequency calls
```

#### **DynamoDB Scaling**
```yaml
Auto Scaling:
  Read Capacity: 5-4000 units (70% target)
  Write Capacity: 5-4000 units (70% target)
  
Global Tables: Multi-region setup for disaster recovery

Point-in-Time Recovery: Enabled
Backup Retention: 35 days
```

### **Monitoring & Observability**

#### **Custom CloudWatch Metrics**
```python
# Business metrics
CUSTOM_METRICS = {
    'TenantRegistrations': 'Count per hour',
    'ConversationsStarted': 'Count per tenant',
    'AgentResponseTime': 'Milliseconds p95',
    'PaymentSuccessRate': 'Percentage',
    'DataProcessingVolume': 'Records per minute'
}

# Technical metrics  
TECHNICAL_METRICS = {
    'LambdaColdStarts': 'Count per function',
    'DynamoDBThrottles': 'Count per table',
    'APIGateway4xxErrors': 'Count per endpoint',
    'APIGateway5xxErrors': 'Count per endpoint'
}
```

#### **Distributed Tracing**
```python
# X-Ray tracing segments
@xray_recorder.capture('user_registration')
def register_user(tenant_data, user_data):
    with xray_recorder.in_subsegment('validate_data'):
        validate_registration_data(tenant_data, user_data)
    
    with xray_recorder.in_subsegment('create_tenant'):
        tenant = create_tenant(tenant_data)
    
    with xray_recorder.in_subsegment('process_payment'):
        payment = process_subscription_payment(tenant.id)
    
    return {'tenant': tenant, 'payment': payment}
```

## 🔗 Integration Architecture

### **n8n Agent Integration**

#### **API Contract**
```python
# Agent proxy interface
class AgentProxy:
    def __init__(self, n8n_base_url: str, api_key: str):
        self.base_url = n8n_base_url
        self.api_key = api_key
    
    async def call_agent(self, agent_name: str, tenant_id: str, 
                        user_id: str, message: str) -> dict:
        payload = {
            'tenant_id': tenant_id,
            'user_id': user_id, 
            'message': message,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        response = await self.http_client.post(
            f"{self.base_url}/webhook/{agent_name}",
            json=payload,
            headers={'Authorization': f'Bearer {self.api_key}'}
        )
        
        return response.json()
```

### **Payment Gateway Integration**

#### **Stripe Integration**
```python
# Subscription management
class StripeService:
    def create_subscription(self, customer_data: dict, plan_id: str):
        customer = stripe.Customer.create(
            email=customer_data['email'],
            name=customer_data['company_name'],
            metadata={'tenant_id': customer_data['tenant_id']}
        )
        
        subscription = stripe.Subscription.create(
            customer=customer.id,
            items=[{'price': plan_id}],
            metadata={'tenant_id': customer_data['tenant_id']}
        )
        
        return subscription
    
    def handle_webhook(self, event_type: str, event_data: dict):
        if event_type == 'invoice.payment_succeeded':
            self.handle_payment_success(event_data)
        elif event_type == 'invoice.payment_failed':
            self.handle_payment_failure(event_data)
```

## 🏗️ Infrastructure Patterns

### **Terraform Module Structure**
```
infrastructure/
├── modules/
│   ├── api-gateway/
│   ├── lambda-function/
│   ├── dynamodb-table/
│   ├── s3-bucket/
│   └── monitoring/
├── environments/
│   ├── dev/
│   ├── staging/
│   └── prod/
└── shared/
    ├── iam-roles/
    ├── vpc/
    └── secrets/
```

### **Serverless Framework Structure**
```
services/
├── auth-service/
│   ├── serverless.yml
│   ├── functions/
│   └── handlers/
├── tenant-service/
├── payment-service/
├── agent-service/
└── shared/
    ├── layers/
    ├── middleware/
    └── utils/
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Technical Architecture Team  
**Review Cycle:** Bi-weekly during development, monthly post-launch