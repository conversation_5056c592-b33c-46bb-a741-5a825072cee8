# Infrastructure as Code Configuration

## 🏗️ IaC Overview

### **Current Infrastructure Strategy**
- **Serverless Framework:** Primary IaC tool for all AWS resources
- **YAML Configuration:** Declarative infrastructure definitions
- **Layered Architecture:** Shared layer + independent microservices
- **Environment Management:** Dev, staging, production configurations
- **Version Control:** All infrastructure changes tracked in Git
- **Independent Deployment:** Each service can be deployed separately

### **Current Repository Structure**
```
agent-scl-platform/
├── serverless/
│   ├── shared/
│   │   ├── variables.yml          # Centralized configuration
│   │   └── resources/             # Shared AWS resources
│   │       ├── dynamodb.yml
│   │       ├── secrets.yml
│   │       ├── iam.yml
│   │       ├── api-gateway.yml
│   │       ├── vpc.yml
│   │       └── monitoring.yml
│   └── templates/
│       └── service-template.yml   # Standard service template
├── services/
│   ├── auth/
│   │   └── serverless.yml         # Auth service configuration
│   ├── payment/
│   │   └── serverless.yml         # Payment service configuration
│   ├── tenant/
│   │   └── serverless.yml         # Tenant service configuration
│   └── user/
│       └── serverless.yml         # User service configuration
├── shared/
│   └── python/
│       └── shared/                # Shared Lambda layer code
└── scripts/
    ├── validate-infrastructure.py
    ├── validate-architecture.py
    └── deployment scripts
```

## 🔧 Serverless Framework Configuration

### **Shared Variables Configuration**
```yaml
# serverless/shared/variables.yml
projectName: agent-scl
region: us-east-1

stages:
  dev:
    # Lambda configuration standards
    lambda:
      defaults:
        timeout: 30
        memorySize: 256
        reservedConcurrency: 10
        runtime: python3.11
        tracing: Active
      profiles:
        quick:
          timeout: 15
          memorySize: 128
        standard:
          timeout: 30
          memorySize: 256
        heavy:
          timeout: 60
          memorySize: 512

    # API Gateway configuration
    apiGatewayName: agent-scl-api-gateway-dev


    # DynamoDB configuration
    dynamodb:
      mainTable: agent-scl-main-dev
      billingMode: PAY_PER_REQUEST

    # VPC configuration (optional)
    vpc:
      enabled: false

    # Monitoring configuration
    monitoring:
      enabled: true
      logRetentionInDays: 14
```

### **Service Configuration Template**
```yaml
# serverless/templates/service-template.yml
service: ${self:custom.variables.projectName}-${self:custom.serviceName}

frameworkVersion: '3'

custom:
  serviceName: ${opt:service, 'unknown'}
  variables: ${file(../shared/variables.yml)}
  stage: ${opt:stage, 'dev'}

  # Python requirements configuration
  pythonRequirements:
    dockerizePip: true
    layer:
      name: ${self:service}-dependencies
      description: Dependencies for ${self:service}
      compatibleRuntimes:
        - python3.11

provider:
  name: aws
  runtime: ${self:custom.variables.stages.${self:custom.stage}.lambda.defaults.runtime}
  region: ${self:custom.variables.region}
  stage: ${self:custom.stage}

  # Lambda configuration
  timeout: ${self:custom.variables.stages.${self:custom.stage}.lambda.defaults.timeout}
  memorySize: ${self:custom.variables.stages.${self:custom.stage}.lambda.defaults.memorySize}

  # Tracing and monitoring
  tracing:
    lambda: ${self:custom.variables.stages.${self:custom.stage}.lambda.defaults.tracing}

  # Environment variables
  environment:
    STAGE: ${self:custom.stage}
    SERVICE_NAME: ${self:custom.serviceName}

  # IAM role statements
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - arn:aws:dynamodb:${self:custom.variables.region}:*:table/${self:custom.variables.stages.${self:custom.stage}.dynamodb.mainTable}
            - arn:aws:dynamodb:${self:custom.variables.region}:*:table/${self:custom.variables.stages.${self:custom.stage}.dynamodb.mainTable}/index/*

# Shared layer reference
layers:
  - ${cf:agent-scl-shared-${self:custom.stage}.SharedLayerLambdaLayerQualifiedArn}
```

### **Individual Service Configuration Example**
```yaml
# services/auth/serverless.yml
service: agent-scl-auth

frameworkVersion: '3'

custom:
  serviceName: auth
  variables: ${file(../../serverless/shared/variables.yml)}
  stage: ${opt:stage, 'dev'}

provider:
  name: aws
  runtime: python3.11
  region: ${self:custom.variables.region}
  stage: ${self:custom.stage}

functions:
  login:
    handler: src/handlers/login.handler
    events:
      - http:
          path: /auth/login
          method: post
          cors: true

  register:
    handler: src/handlers/register.handler
    events:
      - http:
          path: /auth/register
          method: post
          cors: true

# Reference shared resources
resources:
  - ${file(../../serverless/shared/resources/dynamodb.yml)}
  - ${file(../../serverless/shared/resources/iam.yml)}
```

### **Shared Resources Configuration**
```yaml
# serverless/shared/resources/dynamodb.yml
Resources:
  MainTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: ${self:custom.variables.stages.${self:custom.stage}.dynamodb.mainTable}
      BillingMode: ${self:custom.variables.stages.${self:custom.stage}.dynamodb.billingMode}

      # Primary key configuration
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
        - AttributeName: GSI1PK
          AttributeType: S
        - AttributeName: GSI1SK
          AttributeType: S
        - AttributeName: GSI2PK
          AttributeType: S
        - AttributeName: GSI2SK
          AttributeType: S

      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE

      # Global Secondary Indexes
      GlobalSecondaryIndexes:
        - IndexName: GSI1
          KeySchema:
            - AttributeName: GSI1PK
              KeyType: HASH
            - AttributeName: GSI1SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        - IndexName: GSI2
          KeySchema:
            - AttributeName: GSI2PK
              KeyType: HASH
            - AttributeName: GSI2SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

      # Point-in-time recovery
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

      # Server-side encryption
      SSESpecification:
        SSEEnabled: true

      # Tags
      Tags:
        - Key: Project
          Value: ${self:custom.variables.projectName}
        - Key: Environment
          Value: ${self:custom.stage}
        - Key: Service
          Value: shared
    type = "S"
  }
  
  attribute {
    name = "GSI1SK"
    type = "S"
  }
  
  attribute {
    name = "GSI2PK"
    type = "S"
  }
  
  attribute {
    name = "GSI2SK"
    type = "S"
  }
  
  # Global Secondary Index 1: Tenant-Entity Index
  global_secondary_index {
    name            = "GSI1"
    hash_key        = "GSI1PK"
    range_key       = "GSI1SK"
    write_capacity  = var.billing_mode == "PROVISIONED" ? var.gsi_write_capacity : null
    read_capacity   = var.billing_mode == "PROVISIONED" ? var.gsi_read_capacity : null
    projection_type = "ALL"
  }
  
  # Global Secondary Index 2: Status-Date Index
  global_secondary_index {
    name            = "GSI2"
    hash_key        = "GSI2PK"
    range_key       = "GSI2SK"
    write_capacity  = var.billing_mode == "PROVISIONED" ? var.gsi_write_capacity : null
    read_capacity   = var.billing_mode == "PROVISIONED" ? var.gsi_read_capacity : null
    projection_type = "ALL"
  }
  
  # Encryption
  server_side_encryption {
    enabled     = true
    kms_key_id  = var.kms_key_id
  }
  
  # Point-in-time recovery
  point_in_time_recovery {
    enabled = var.enable_point_in_time_recovery
  }
  
  # Deletion protection for production
  deletion_protection_enabled = var.environment == "prod"
  
  # Stream configuration for change data capture
  stream_enabled   = var.enable_streams
  stream_view_type = var.stream_view_type
  
  tags = {
    Name        = "${var.project_name}-main-${var.environment}"
    Environment = var.environment
    Purpose     = "Main application data store"
  }
}

# Auto-scaling for provisioned capacity
resource "aws_appautoscaling_target" "dynamodb_table_read_target" {
  count              = var.billing_mode == "PROVISIONED" ? 1 : 0
  max_capacity       = var.max_read_capacity
  min_capacity       = var.min_read_capacity
  resource_id        = "table/${aws_dynamodb_table.main_table.name}"
  scalable_dimension = "dynamodb:table:ReadCapacityUnits"
  service_namespace  = "dynamodb"
}

resource "aws_appautoscaling_policy" "dynamodb_table_read_policy" {
  count              = var.billing_mode == "PROVISIONED" ? 1 : 0
  name               = "DynamoDBReadCapacityUtilization:${aws_appautoscaling_target.dynamodb_table_read_target[0].resource_id}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.dynamodb_table_read_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.dynamodb_table_read_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.dynamodb_table_read_target[0].service_namespace
  
  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "DynamoDBReadCapacityUtilization"
    }
    target_value = 70.0
  }
}

# terraform/modules/dynamodb/variables.tf
variable "project_name" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "billing_mode" {
  description = "DynamoDB billing mode"
  type        = string
  default     = "PAY_PER_REQUEST"
  validation {
    condition     = contains(["PROVISIONED", "PAY_PER_REQUEST"], var.billing_mode)
    error_message = "Billing mode must be PROVISIONED or PAY_PER_REQUEST."
  }
}

variable "read_capacity" {
  description = "Read capacity units for provisioned mode"
  type        = number
  default     = 5
}

variable "write_capacity" {
  description = "Write capacity units for provisioned mode"
  type        = number
  default     = 5
}

variable "gsi_read_capacity" {
  description = "GSI read capacity units for provisioned mode"
  type        = number
  default     = 5
}

variable "gsi_write_capacity" {
  description = "GSI write capacity units for provisioned mode"
  type        = number
  default     = 5
}

variable "max_read_capacity" {
  description = "Maximum read capacity for auto-scaling"
  type        = number
  default     = 4000
}

variable "min_read_capacity" {
  description = "Minimum read capacity for auto-scaling"
  type        = number
  default     = 5
}

variable "enable_point_in_time_recovery" {
  description = "Enable point-in-time recovery"
  type        = bool
  default     = true
}

variable "enable_streams" {
  description = "Enable DynamoDB streams"
  type        = bool
  default     = true
}

variable "stream_view_type" {
  description = "Stream view type"
  type        = string
  default     = "NEW_AND_OLD_IMAGES"
}

variable "kms_key_id" {
  description = "KMS key ID for encryption"
  type        = string
  default     = "alias/aws/dynamodb"
}

# terraform/modules/dynamodb/outputs.tf
output "table_name" {
  description = "DynamoDB table name"
  value       = aws_dynamodb_table.main_table.name
}

output "table_arn" {
  description = "DynamoDB table ARN"
  value       = aws_dynamodb_table.main_table.arn
}

output "table_stream_arn" {
  description = "DynamoDB table stream ARN"
  value       = aws_dynamodb_table.main_table.stream_arn
}
```

### **S3 Module**
```hcl
# terraform/modules/s3-bucket/main.tf
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

resource "aws_s3_bucket" "main_bucket" {
  bucket = "${var.project_name}-${var.bucket_purpose}-${var.environment}-${random_string.bucket_suffix.result}"
}

resource "aws_s3_bucket_public_access_block" "main_bucket_pab" {
  bucket = aws_s3_bucket.main_bucket.id
  
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_versioning" "main_bucket_versioning" {
  bucket = aws_s3_bucket.main_bucket.id
  
  versioning_configuration {
    status = var.enable_versioning ? "Enabled" : "Suspended"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "main_bucket_encryption" {
  bucket = aws_s3_bucket.main_bucket.id
  
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = var.kms_key_id != null ? "aws:kms" : "AES256"
      kms_master_key_id = var.kms_key_id
    }
    bucket_key_enabled = var.kms_key_id != null
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "main_bucket_lifecycle" {
  bucket = aws_s3_bucket.main_bucket.id
  
  rule {
    id     = "tenant_data_lifecycle"
    status = "Enabled"
    
    # Transition to IA after 30 days
    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }
    
    # Transition to Glacier after 90 days
    transition {
      days          = 90
      storage_class = "GLACIER"
    }
    
    # Delete after retention period
    expiration {
      days = var.data_retention_days
    }
    
    # Clean up incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
  }
  
  # Rule for temporary files
  rule {
    id     = "temp_files_cleanup"
    status = "Enabled"
    
    filter {
      prefix = "temp/"
    }
    
    expiration {
      days = 7
    }
  }
}

# Bucket notification configuration for processing
resource "aws_s3_bucket_notification" "main_bucket_notification" {
  bucket = aws_s3_bucket.main_bucket.id
  
  # Notify when files are uploaded to tenant folders
  lambda_function {
    lambda_function_arn = var.data_processing_lambda_arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "tenant-"
    filter_suffix       = ".xlsx"
  }
  
  depends_on = [aws_lambda_permission.allow_bucket]
}

resource "aws_lambda_permission" "allow_bucket" {
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = var.data_processing_lambda_arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.main_bucket.arn
}

# Bucket policy for tenant isolation
resource "aws_s3_bucket_policy" "main_bucket_policy" {
  bucket = aws_s3_bucket.main_bucket.id
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "DenyDirectAccess"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.main_bucket.arn,
          "${aws_s3_bucket.main_bucket.arn}/*"
        ]
        Condition = {
          StringNotEquals = {
            "aws:PrincipalServiceName" = [
              "lambda.amazonaws.com"
            ]
          }
        }
      },
      {
        Sid    = "AllowLambdaAccess"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = "${aws_s3_bucket.main_bucket.arn}/*"
      }
    ]
  })
}

# terraform/modules/s3-bucket/variables.tf
variable "project_name" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "bucket_purpose" {
  description = "Purpose of the bucket (data, logs, artifacts)"
  type        = string
}

variable "enable_versioning" {
  description = "Enable S3 versioning"
  type        = bool
  default     = true
}

variable "kms_key_id" {
  description = "KMS key ID for encryption"
  type        = string
  default     = null
}

variable "data_retention_days" {
  description = "Data retention period in days"
  type        = number
  default     = 2555  # 7 years
}

variable "data_processing_lambda_arn" {
  description = "ARN of lambda function for data processing"
  type        = string
  default     = ""
}

# terraform/modules/s3-bucket/outputs.tf
output "bucket_name" {
  description = "S3 bucket name"
  value       = aws_s3_bucket.main_bucket.id
}

output "bucket_arn" {
  description = "S3 bucket ARN"
  value       = aws_s3_bucket.main_bucket.arn
}

output "bucket_domain_name" {
  description = "S3 bucket domain name"
  value       = aws_s3_bucket.main_bucket.bucket_domain_name
}
```

### **API Gateway Module**
```hcl
# terraform/modules/api-gateway/main.tf
resource "aws_api_gateway_rest_api" "main_api" {
  name        = "${var.project_name}-api-${var.environment}"
  description = "Platform API Gateway for ${var.environment}"
  
  endpoint_configuration {
    types = ["REGIONAL"]
  }
  
  # API policy for additional security
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = "*"
        Action = "execute-api:Invoke"
        Resource = "*"
        Condition = {
          IpAddress = {
            "aws:sourceIp" = var.allowed_ip_ranges
          }
        }
      }
    ]
  })
}

# Custom domain name
resource "aws_api_gateway_domain_name" "main_domain" {
  count           = var.custom_domain_name != "" ? 1 : 0
  domain_name     = var.custom_domain_name
  certificate_arn = var.ssl_certificate_arn
  
  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

# Base path mapping
resource "aws_api_gateway_base_path_mapping" "main_mapping" {
  count       = var.custom_domain_name != "" ? 1 : 0
  api_id      = aws_api_gateway_rest_api.main_api.id
  stage_name  = aws_api_gateway_deployment.main_deployment.stage_name
  domain_name = aws_api_gateway_domain_name.main_domain[0].domain_name
  base_path   = "v1"
}

# Deployment
resource "aws_api_gateway_deployment" "main_deployment" {
  depends_on = [
    aws_api_gateway_integration.lambda_integrations
  ]
  
  rest_api_id = aws_api_gateway_rest_api.main_api.id
  stage_name  = var.environment
  
  # Force new deployment on changes
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_rest_api.main_api.body,
      aws_api_gateway_integration.lambda_integrations
    ]))
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

# Stage configuration
resource "aws_api_gateway_stage" "main_stage" {
  deployment_id = aws_api_gateway_deployment.main_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.main_api.id
  stage_name    = var.environment
  
  # Enable caching
  cache_cluster_enabled = var.enable_caching
  cache_cluster_size    = var.cache_cluster_size
  
  # Access logging
  access_log_destination_arn = aws_cloudwatch_log_group.api_gateway_logs.arn
  access_log_format = jsonencode({
    requestId      = "$context.requestId"
    ip             = "$context.identity.sourceIp"
    caller         = "$context.identity.caller"
    user           = "$context.identity.user"
    requestTime    = "$context.requestTime"
    httpMethod     = "$context.httpMethod"
    resourcePath   = "$context.resourcePath"
    status         = "$context.status"
    protocol       = "$context.protocol"
    responseLength = "$context.responseLength"
    responseTime   = "$context.responseTime"
    error          = "$context.error.message"
    integrationError = "$context.integration.error"
  })
  
  # X-Ray tracing
  xray_tracing_enabled = var.enable_xray_tracing
}

# CloudWatch Logs for API Gateway
resource "aws_cloudwatch_log_group" "api_gateway_logs" {
  name              = "/aws/apigateway/${var.project_name}-${var.environment}"
  retention_in_days = var.log_retention_days
  
  tags = {
    Environment = var.environment
    Purpose     = "API Gateway Access Logs"
  }
}

# WAF for API Gateway
resource "aws_wafv2_web_acl" "api_gateway_waf" {
  name  = "${var.project_name}-api-waf-${var.environment}"
  scope = "REGIONAL"
  
  default_action {
    allow {}
  }
  
  # Rate limiting rule
  rule {
    name     = "RateLimitRule"
    priority = 1
    
    override_action {
      none {}
    }
    
    statement {
      rate_based_statement {
        limit              = var.rate_limit_per_5_minutes
        aggregate_key_type = "IP"
      }
    }
    
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRule"
      sampled_requests_enabled   = true
    }
    
    action {
      block {}
    }
  }
  
  # AWS Managed Rules
  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 2
    
    override_action {
      none {}
    }
    
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }
    
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "CommonRuleSetMetric"
      sampled_requests_enabled   = true
    }
  }
  
  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "platformAPIGatewayWAF"
    sampled_requests_enabled   = true
  }
}

# Associate WAF with API Gateway
resource "aws_wafv2_web_acl_association" "api_gateway_waf_association" {
  resource_arn = aws_api_gateway_stage.main_stage.arn
  web_acl_arn  = aws_wafv2_web_acl.api_gateway_waf.arn
}

# terraform/modules/api-gateway/variables.tf
variable "project_name" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "custom_domain_name" {
  description = "Custom domain name for API"
  type        = string
  default     = ""
}

variable "ssl_certificate_arn" {
  description = "SSL certificate ARN for custom domain"
  type        = string
  default     = ""
}

variable "allowed_ip_ranges" {
  description = "Allowed IP ranges for API access"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "enable_caching" {
  description = "Enable API Gateway caching"
  type        = bool
  default     = false
}

variable "cache_cluster_size" {
  description = "Cache cluster size"
  type        = string
  default     = "0.5"
}

variable "enable_xray_tracing" {
  description = "Enable X-Ray tracing"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "CloudWatch logs retention in days"
  type        = number
  default     = 30
}

variable "rate_limit_per_5_minutes" {
  description = "Rate limit per 5 minutes per IP"
  type        = number
  default     = 2000
}

# terraform/modules/api-gateway/outputs.tf
output "api_gateway_id" {
  description = "API Gateway ID"
  value       = aws_api_gateway_rest_api.main_api.id
}

output "api_gateway_root_resource_id" {
  description = "API Gateway root resource ID"
  value       = aws_api_gateway_rest_api.main_api.root_resource_id
}

output "api_gateway_execution_arn" {
  description = "API Gateway execution ARN"
  value       = aws_api_gateway_rest_api.main_api.execution_arn
}

output "api_gateway_url" {
  description = "API Gateway URL"
  value       = aws_api_gateway_deployment.main_deployment.invoke_url
}

output "custom_domain_name" {
  description = "Custom domain name"
  value       = var.custom_domain_name != "" ? aws_api_gateway_domain_name.main_domain[0].domain_name : null
}
```

### **Environment Configuration**
```hcl
# terraform/environments/prod/main.tf
terraform {
  backend "s3" {
    bucket         = "platform-terraform-state-prod"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-state-lock"
  }
}

module "dynamodb" {
  source = "../../modules/dynamodb"
  
  project_name                   = var.project_name
  environment                   = var.environment
  billing_mode                  = "PAY_PER_REQUEST"  # Production uses on-demand
  enable_point_in_time_recovery = true
  enable_streams                = true
  kms_key_id                    = aws_kms_key.main_key.arn
}

module "s3_data_bucket" {
  source = "../../modules/s3-bucket"
  
  project_name                  = var.project_name
  environment                   = var.environment
  bucket_purpose               = "data"
  enable_versioning            = true
  kms_key_id                   = aws_kms_key.main_key.arn
  data_retention_days          = 2555  # 7 years
  data_processing_lambda_arn   = module.lambda_functions.data_processor_arn
}

module "s3_logs_bucket" {
  source = "../../modules/s3-bucket"
  
  project_name     = var.project_name
  environment      = var.environment
  bucket_purpose   = "logs"
  enable_versioning = false
  data_retention_days = 365  # 1 year for logs
}

module "api_gateway" {
  source = "../../modules/api-gateway"
  
  project_name                = var.project_name
  environment                = var.environment
  custom_domain_name         = var.api_domain_name
  ssl_certificate_arn        = aws_acm_certificate.api_cert.arn
  enable_caching             = true
  cache_cluster_size         = "1.6"  # Production cache size
  enable_xray_tracing        = true
  log_retention_days         = 30
  rate_limit_per_5_minutes   = 10000  # Higher limit for production
}

# KMS key for encryption
resource "aws_kms_key" "main_key" {
  description             = "Platform main encryption key for ${var.environment}"
  deletion_window_in_days = 7
  enable_key_rotation     = true
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow Platform Services"
        Effect = "Allow"
        Principal = {
          Service = [
            "dynamodb.amazonaws.com",
            "s3.amazonaws.com",
            "lambda.amazonaws.com"
          ]
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })
  
  tags = {
    Name        = "${var.project_name}-main-key-${var.environment}"
    Environment = var.environment
    Purpose     = "Main encryption key"
  }
}

resource "aws_kms_alias" "main_key_alias" {
  name          = "alias/${var.project_name}-main-${var.environment}"
  target_key_id = aws_kms_key.main_key.key_id
}

# SSL Certificate for custom domain
resource "aws_acm_certificate" "api_cert" {
  domain_name       = var.api_domain_name
  validation_method = "DNS"
  
  subject_alternative_names = [
    "*.${var.api_domain_name}"
  ]
  
  lifecycle {
    create_before_destroy = true
  }
  
  tags = {
    Name        = "${var.project_name}-api-cert-${var.environment}"
    Environment = var.environment
  }
}

# terraform/environments/prod/variables.tf
variable "project_name" {
  description = "Project name"
  type        = string
  default     = "platform"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "prod"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "api_domain_name" {
  description = "API domain name"
  type        = string
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# terraform/environments/prod/outputs.tf
output "dynamodb_table_name" {
  description = "DynamoDB table name"
  value       = module.dynamodb.table_name
}

output "s3_data_bucket_name" {
  description = "S3 data bucket name"
  value       = module.s3_data_bucket.bucket_name
}

output "api_gateway_url" {
  description = "API Gateway URL"
  value       = module.api_gateway.api_gateway_url
}

output "kms_key_id" {
  description = "KMS key ID"
  value       = aws_kms_key.main_key.key_id
}
```

## ⚡ Serverless Framework Configuration

### **Shared Configuration**
```yaml
# serverless/shared/serverless-base.yml
service: ${env:SERVICE_NAME}

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.11
  region: ${env:AWS_REGION, 'us-east-1'}
  stage: ${env:ENVIRONMENT, 'dev'}
  
  # Environment variables
  environment:
    ENVIRONMENT: ${env:ENVIRONMENT}
    AWS_REGION: ${env:AWS_REGION}
    DYNAMODB_TABLE: ${env:DYNAMODB_TABLE}
    S3_DATA_BUCKET: ${env:S3_DATA_BUCKET}
    JWT_SECRET: ${env:JWT_SECRET}
    ENCRYPTION_KEY: ${env:ENCRYPTION_KEY}
    
  # IAM role statements
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:BatchGetItem
        - dynamodb:BatchWriteItem
      Resource:
        - ${env:DYNAMODB_TABLE_ARN}
        - ${env:DYNAMODB_TABLE_ARN}/index/*
    
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
        - s3:DeleteObject
        - s3:ListBucket
      Resource:
        - ${env:S3_DATA_BUCKET_ARN}
        - ${env:S3_DATA_BUCKET_ARN}/*
    
    - Effect: Allow
      Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey
      Resource: ${env:KMS_KEY_ARN}
    
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "arn:aws:logs:*:*:*"
    
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"

  # VPC configuration for production
  vpc:
    securityGroupIds:
      - ${env:SECURITY_GROUP_ID}
    subnetIds:
      - ${env:SUBNET_ID_1}
      - ${env:SUBNET_ID_2}

  # Tags
  tags:
    Environment: ${env:ENVIRONMENT}
    Project: platform
    Service: ${env:SERVICE_NAME}

  # Tracing
  tracing:
    lambda: true
    apiGateway: true

# Lambda layers
layers:
  pythonRequirements:
    path: ../shared/layers/python-requirements
    name: ${self:service}-python-requirements-${self:provider.stage}
    description: Python requirements layer
    retain: false

# Custom configuration
custom:
  pythonRequirements:
    dockerizePip: true
    layer: true
    
  # Serverless-offline configuration
  serverless-offline:
    httpPort: 3000
    lambdaPort: 3002
    
  # Log retention
  logRetentionInDays: 30
  
  # Alerts configuration
  alerts:
    stages:
      - prod
      - staging
    dashboards: true
    alarms:
      - functionErrors
      - functionThrottles
      - functionInvocations
      - functionDuration

# Plugins
plugins:
  - serverless-python-requirements
  - serverless-plugin-tracing
  - serverless-offline
  - serverless-plugin-aws-alerts

# Package configuration
package:
  exclude:
    - node_modules/**
    - tests/**
    - .pytest_cache/**
    - __pycache__/**
    - "*.pyc"
    - .env
    - README.md
```

### **Auth Service Configuration**
```yaml
# serverless/services/auth-service/serverless.yml
service: platform-auth-service

extends: ../../shared/serverless-base.yml

provider:
  environment:
    SES_REGION: ${env:SES_REGION, 'us-east-1'}
    VERIFICATION_EMAIL_TEMPLATE: ${env:VERIFICATION_EMAIL_TEMPLATE}
    RESET_PASSWORD_EMAIL_TEMPLATE: ${env:RESET_PASSWORD_EMAIL_TEMPLATE}
    
  iamRoleStatements:
    - Effect: Allow
      Action:
        - ses:SendEmail
        - ses:SendRawEmail
      Resource: "*"
      Condition:
        StringEquals:
          "ses:FromAddress": ${env:FROM_EMAIL_ADDRESS}

functions:
  # Authentication functions
  register:
    handler: handlers/auth_handlers.register_handler
    description: User registration endpoint
    timeout: 30
    memorySize: 512
    reservedConcurrency: 100
    events:
      - http:
          path: /auth/register
          method: post
          cors: true
          request:
            schema:
              application/json: ${file(schemas/register-request.json)}
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: register
    
  login:
    handler: handlers/auth_handlers.login_handler
    description: User login endpoint
    timeout: 15
    memorySize: 256
    reservedConcurrency: 200
    events:
      - http:
          path: /auth/login
          method: post
          cors: true
          request:
            schema:
              application/json: ${file(schemas/login-request.json)}
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: login
    
  refresh-token:
    handler: handlers/auth_handlers.refresh_token_handler
    description: Token refresh endpoint
    timeout: 10
    memorySize: 256
    events:
      - http:
          path: /auth/refresh
          method: post
          cors: true
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: refresh_token
    
  forgot-password:
    handler: handlers/auth_handlers.forgot_password_handler
    description: Forgot password endpoint
    timeout: 20
    memorySize: 256
    events:
      - http:
          path: /auth/forgot-password
          method: post
          cors: true
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: forgot_password
    
  reset-password:
    handler: handlers/auth_handlers.reset_password_handler
    description: Reset password endpoint
    timeout: 15
    memorySize: 256
    events:
      - http:
          path: /auth/reset-password
          method: post
          cors: true
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: reset_password
    
  verify-email:
    handler: handlers/auth_handlers.verify_email_handler
    description: Email verification endpoint
    timeout: 15
    memorySize: 256
    events:
      - http:
          path: /auth/verify-email
          method: post
          cors: true
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: verify_email
    
  resend-code:
    handler: handlers/auth_handlers.resend_code_handler
    description: Resend verification code endpoint
    timeout: 20
    memorySize: 256
    events:
      - http:
          path: /auth/resend-code
          method: post
          cors: true
    layers:
      - { Ref: PythonRequirementsLambdaLayer }
    environment:
      FUNCTION_NAME: resend_code

  # Authorizer function
  authorizer:
    handler: handlers/auth_handlers.authorizer_handler
    description: API Gateway authorizer
    timeout: 10
    memorySize: 128
    environment:
      FUNCTION_NAME: authorizer

# Custom alarms
custom:
  alerts:
    alarms:
      - functionErrors:
          threshold: 5
          period: 300
      - functionThrottles:
          threshold: 1
          period: 300
      - functionDuration:
          threshold: 10000
          period: 300
          statistic: Average
      # Custom alarm for failed logins
      - name: FailedLogins
        metric: FailedLoginAttempts
        threshold: 10
        statistic: Sum
        period: 300
        evaluationPeriods: 1
        comparisonOperator: GreaterThanThreshold

resources:
  Resources:
    # CloudWatch custom metrics for security monitoring
    FailedLoginMetricFilter:
      Type: AWS::Logs::MetricFilter
      Properties:
        LogGroupName: !Sub '/aws/lambda/${self:service}-${self:provider.stage}-login'
        FilterPattern: '[timestamp, request_id, level="WARNING", message="Authentication failed"]'
        MetricTransformations:
          - MetricNamespace: Platform/Security
            MetricName: FailedLoginAttempts
            MetricValue: "1"
            DefaultValue: 0
```

### **Deployment Scripts**
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# Configuration
ENVIRONMENT=${1:-dev}
AWS_REGION=${2:-us-east-1}
PROJECT_NAME="platform"

echo "Deploying to environment: $ENVIRONMENT"
echo "AWS Region: $AWS_REGION"

# Check if environment is valid
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo "Error: Environment must be dev, staging, or prod"
    exit 1
fi

# Load environment variables
if [ -f ".env.$ENVIRONMENT" ]; then
    export $(cat .env.$ENVIRONMENT | grep -v '^#' | xargs)
fi

# Deploy infrastructure with Terraform
echo "Deploying infrastructure..."
cd terraform/environments/$ENVIRONMENT

# Initialize Terraform
terraform init \
    -backend-config="bucket=${PROJECT_NAME}-terraform-state-${ENVIRONMENT}" \
    -backend-config="key=infrastructure/terraform.tfstate" \
    -backend-config="region=${AWS_REGION}" \
    -backend-config="encrypt=true" \
    -backend-config="dynamodb_table=terraform-state-lock"

# Plan and apply
terraform plan -out=tfplan
terraform apply tfplan

# Get outputs for Serverless deployment
DYNAMODB_TABLE=$(terraform output -raw dynamodb_table_name)
DYNAMODB_TABLE_ARN=$(terraform output -raw dynamodb_table_arn)
S3_DATA_BUCKET=$(terraform output -raw s3_data_bucket_name)
S3_DATA_BUCKET_ARN=$(terraform output -raw s3_data_bucket_arn)
KMS_KEY_ARN=$(terraform output -raw kms_key_arn)
API_GATEWAY_ID=$(terraform output -raw api_gateway_id)

# Export for Serverless
export DYNAMODB_TABLE
export DYNAMODB_TABLE_ARN
export S3_DATA_BUCKET
export S3_DATA_BUCKET_ARN
export KMS_KEY_ARN
export API_GATEWAY_ID

cd ../../..

# Deploy Serverless services
echo "Deploying Serverless services..."

SERVICES=(
    "auth-service"
    "tenant-service" 
    "user-service"
    "payment-service"
    "agent-service"
    "chat-service"
)

for service in "${SERVICES[@]}"; do
    echo "Deploying $service..."
    cd serverless/services/$service
    
    # Install dependencies if needed
    if [ -f "requirements.txt" ] && [ ! -d "node_modules" ]; then
        npm install
    fi
    
    # Deploy service
    npx serverless deploy --stage $ENVIRONMENT --region $AWS_REGION --verbose
    
    cd ../../..
done

echo "Deployment completed successfully!"
echo "API Gateway URL: $(terraform -chdir=terraform/environments/$ENVIRONMENT output -raw api_gateway_url)"

# Run post-deployment tests
echo "Running post-deployment tests..."
./scripts/test-deployment.sh $ENVIRONMENT

echo "All done! 🚀"
```

```bash
#!/bin/bash
# scripts/test-deployment.sh

set -e

ENVIRONMENT=${1:-dev}
API_BASE_URL=$(terraform -chdir=terraform/environments/$ENVIRONMENT output -raw api_gateway_url)

echo "Testing deployment for environment: $ENVIRONMENT"
echo "API Base URL: $API_BASE_URL"

# Health check
echo "Running health check..."
response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/health")

if [ $response -eq 200 ]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed (HTTP $response)"
    exit 1
fi

# Test auth endpoints
echo "Testing authentication endpoints..."

# Test registration endpoint structure
response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_BASE_URL/auth/register" \
    -H "Content-Type: application/json" \
    -d '{}')

if [ $response -eq 400 ]; then
    echo "✅ Registration validation working"
else
    echo "❌ Registration endpoint unexpected response (HTTP $response)"
fi

# Test login endpoint structure  
response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{}')

if [ $response -eq 400 ]; then
    echo "✅ Login validation working"
else
    echo "❌ Login endpoint unexpected response (HTTP $response)"
fi

echo "✅ Deployment tests completed successfully!"
```

```bash
#!/bin/bash
# scripts/destroy.sh

set -e

ENVIRONMENT=${1:-dev}
AWS_REGION=${2:-us-east-1}

echo "⚠️  WARNING: This will destroy all resources in $ENVIRONMENT environment!"
read -p "Are you sure? (yes/no): " -r
if [[ ! $REPLY =~ ^yes$ ]]; then
    echo "Destruction cancelled."
    exit 1
fi

# Load environment variables
if [ -f ".env.$ENVIRONMENT" ]; then
    export $(cat .env.$ENVIRONMENT | grep -v '^#' | xargs)
fi

# Remove Serverless services first
echo "Removing Serverless services..."

SERVICES=(
    "chat-service"
    "agent-service"
    "payment-service"
    "user-service"
    "tenant-service"
    "auth-service"
)

for service in "${SERVICES[@]}"; do
    echo "Removing $service..."
    cd serverless/services/$service
    npx serverless remove --stage $ENVIRONMENT --region $AWS_REGION || true
    cd ../../..
done

# Destroy Terraform infrastructure
echo "Destroying infrastructure..."
cd terraform/environments/$ENVIRONMENT

terraform init \
    -backend-config="bucket=platform-terraform-state-${ENVIRONMENT}" \
    -backend-config="key=infrastructure/terraform.tfstate" \
    -backend-config="region=${AWS_REGION}"

terraform destroy -auto-approve

echo "✅ Environment $ENVIRONMENT destroyed successfully!"
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Infrastructure Team  
**Review Cycle:** Weekly during development, monthly post-launch