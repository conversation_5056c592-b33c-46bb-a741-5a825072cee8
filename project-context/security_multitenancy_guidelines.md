# Security & Multi-tenancy Guidelines

## 🔒 Security Overview

### **Security-First Design Principles**
- **Zero Trust Architecture:** Never trust, always verify
- **Defense in Depth:** Multiple security layers
- **Least Privilege Access:** Minimal necessary permissions
- **Data Encryption:** Encryption at rest and in transit
- **Audit Everything:** Comprehensive logging and monitoring
- **Incident Response:** Rapid detection and response
- **Compliance Ready:** SOC 2, GDPR, and industry standards

### **Threat Model**
- **External Threats:** Unauthorized access, DDoS, data breaches
- **Internal Threats:** Privilege escalation, data leakage
- **Application Threats:** Injection attacks, broken authentication
- **Infrastructure Threats:** Misconfigurations, supply chain attacks
- **Multi-tenant Threats:** Cross-tenant data access, resource abuse

## 🏢 Multi-Tenancy Security Model

### **Tenant Isolation Strategy**

#### **Data Isolation Levels**
```yaml
Level 1 - Logical Isolation:
  - Shared database with tenant_id partitioning
  - Application-level access control
  - Query filtering by tenant context
  
Level 2 - Schema Isolation:
  - Separate schemas per tenant
  - Database-level isolation
  - Shared compute resources

Level 3 - Database Isolation:
  - Separate databases per tenant
  - Complete data isolation
  - Higher resource overhead

Implementation: Level 1 (Logical) with Level 3 (Database) for Enterprise
```

#### **Tenant Context Enforcement**
```python
from functools import wraps
import jwt
from typing import Optional

class TenantContext:
    def __init__(self, tenant_id: str, user_id: str, role: str, permissions: list):
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.role = role
        self.permissions = permissions
        
    def has_permission(self, permission: str) -> bool:
        return permission in self.permissions

def tenant_context_required(func):
    """Decorator to enforce tenant context in all operations"""
    @wraps(func)
    async def wrapper(event, context):
        # Extract tenant context from JWT token
        token = event.get('headers', {}).get('Authorization', '').replace('Bearer ', '')
        
        try:
            payload = jwt.decode(token, verify=False)  # Verification done by API Gateway
            tenant_context = TenantContext(
                tenant_id=payload['tenant_id'],
                user_id=payload['user_id'],
                role=payload['role'],
                permissions=payload.get('permissions', [])
            )
            
            # Inject tenant context into event
            event['tenant_context'] = tenant_context
            
            # Validate tenant is active
            if not await is_tenant_active(tenant_context.tenant_id):
                return {
                    'statusCode': 403,
                    'body': json.dumps({
                        'error': 'TENANT_SUSPENDED',
                        'message': 'Tenant account is suspended'
                    })
                }
            
            return await func(event, context)
            
        except jwt.InvalidTokenError:
            return {
                'statusCode': 401,
                'body': json.dumps({'error': 'INVALID_TOKEN'})
            }
            
        except Exception as e:
            logging.error(f"Tenant context error: {str(e)}")
            return {
                'statusCode': 500,
                'body': json.dumps({'error': 'INTERNAL_ERROR'})
            }
    
    return wrapper
```

#### **Data Access Layer Security**
```python
class SecureDataAccess:
    def __init__(self, dynamodb_table, tenant_context: TenantContext):
        self.table = dynamodb_table
        self.tenant_context = tenant_context
        
    def _ensure_tenant_isolation(self, item: dict) -> dict:
        """Ensure all items include tenant_id for isolation"""
        if 'tenant_id' not in item:
            item['tenant_id'] = self.tenant_context.tenant_id
        elif item['tenant_id'] != self.tenant_context.tenant_id:
            raise SecurityError("Cross-tenant data access attempted")
        return item
    
    async def get_item(self, key: dict) -> Optional[dict]:
        """Get item with tenant isolation"""
        key = self._ensure_tenant_isolation(key)
        
        response = self.table.get_item(Key=key)
        item = response.get('Item')
        
        if item and item.get('tenant_id') != self.tenant_context.tenant_id:
            logging.warning(f"Cross-tenant access blocked: {key}")
            return None
            
        return item
    
    async def query_items(self, key_condition: str, **kwargs) -> list:
        """Query items with tenant filtering"""
        # Force tenant filtering in all queries
        filter_expression = kwargs.get('FilterExpression')
        tenant_filter = Attr('tenant_id').eq(self.tenant_context.tenant_id)
        
        if filter_expression:
            kwargs['FilterExpression'] = filter_expression & tenant_filter
        else:
            kwargs['FilterExpression'] = tenant_filter
            
        response = self.table.query(
            KeyConditionExpression=key_condition,
            **kwargs
        )
        
        return response.get('Items', [])
    
    async def put_item(self, item: dict) -> dict:
        """Put item with tenant isolation"""
        item = self._ensure_tenant_isolation(item)
        
        # Add security metadata
        item['created_by'] = self.tenant_context.user_id
        item['created_at'] = datetime.utcnow().isoformat()
        
        self.table.put_item(Item=item)
        return item
```

### **Resource Isolation**

#### **S3 Bucket Security**
```python
import boto3
import json

class S3TenantManager:
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.iam = boto3.client('iam')
        
    async def create_tenant_bucket_policy(self, tenant_id: str, bucket_name: str):
        """Create restrictive bucket policy for tenant"""
        policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "TenantOnlyAccess",
                    "Effect": "Allow",
                    "Principal": {
                        "AWS": f"arn:aws:iam::{AWS_ACCOUNT_ID}:role/TenantRole-{tenant_id}"
                    },
                    "Action": [
                        "s3:GetObject",
                        "s3:PutObject",
                        "s3:DeleteObject"
                    ],
                    "Resource": f"arn:aws:s3:::{bucket_name}/tenant-{tenant_id}/*"
                },
                {
                    "Sid": "DenyOtherTenants",
                    "Effect": "Deny",
                    "Principal": "*",
                    "Action": "s3:*",
                    "Resource": [
                        f"arn:aws:s3:::{bucket_name}/tenant-{tenant_id}/*"
                    ],
                    "Condition": {
                        "StringNotEquals": {
                            "aws:PrincipalTag/TenantId": tenant_id
                        }
                    }
                }
            ]
        }
        
        self.s3.put_bucket_policy(
            Bucket=bucket_name,
            Policy=json.dumps(policy)
        )
    
    async def create_tenant_iam_role(self, tenant_id: str) -> str:
        """Create IAM role for tenant with limited permissions"""
        trust_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "lambda.amazonaws.com"
                    },
                    "Action": "sts:AssumeRole"
                }
            ]
        }
        
        # Create role
        role_name = f"TenantRole-{tenant_id}"
        self.iam.create_role(
            RoleName=role_name,
            AssumeRolePolicyDocument=json.dumps(trust_policy),
            Tags=[
                {'Key': 'TenantId', 'Value': tenant_id},
                {'Key': 'Purpose', 'Value': 'TenantIsolation'}
            ]
        )
        
        # Attach tenant-specific policy
        policy_arn = await self.create_tenant_policy(tenant_id)
        self.iam.attach_role_policy(
            RoleName=role_name,
            PolicyArn=policy_arn
        )
        
        return f"arn:aws:iam::{AWS_ACCOUNT_ID}:role/{role_name}"
```

## 🔐 Authentication & Authorization

### **JWT Token Security**

#### **Token Structure & Claims**
```python
import jwt
import secrets
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class JWTManager:
    def __init__(self, secret_key: str, algorithm: str = 'HS256'):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expiry = timedelta(hours=1)
        self.refresh_token_expiry = timedelta(days=30)
        
    def generate_access_token(self, user_data: dict) -> str:
        """Generate access token with security claims"""
        payload = {
            'user_id': user_data['user_id'],
            'tenant_id': user_data['tenant_id'],
            'role': user_data['role'],
            'permissions': user_data.get('permissions', []),
            'iat': datetime.utcnow(),
            'exp': datetime.utcnow() + self.access_token_expiry,
            'jti': secrets.token_urlsafe(32),  # Unique token ID
            'aud': 'platform-api',
            'iss': 'platform-auth',
            'sub': user_data['user_id'],
            'session_id': user_data.get('session_id'),
            'ip_address': user_data.get('ip_address'),
            'user_agent_hash': self._hash_user_agent(user_data.get('user_agent'))
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def generate_refresh_token(self, user_id: str, tenant_id: str) -> str:
        """Generate refresh token"""
        payload = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'type': 'refresh',
            'iat': datetime.utcnow(),
            'exp': datetime.utcnow() + self.refresh_token_expiry,
            'jti': secrets.token_urlsafe(32)
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str, token_type: str = 'access') -> dict:
        """Verify and decode token"""
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                audience='platform-api' if token_type == 'access' else None
            )
            
            # Additional security checks
            if token_type == 'refresh' and payload.get('type') != 'refresh':
                raise jwt.InvalidTokenError("Invalid token type")
                
            return payload
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise AuthenticationError(f"Invalid token: {str(e)}")
    
    def _hash_user_agent(self, user_agent: str) -> str:
        """Hash user agent for fingerprinting"""
        if not user_agent:
            return None
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'user_agent_salt',
            iterations=100000,
        )
        
        return kdf.derive(user_agent.encode()).hex()
```

#### **Role-Based Access Control (RBAC)**
```python
from enum import Enum
from typing import List, Dict

class Permission(Enum):
    # Agent permissions
    AGENTS_READ = "agents:read"
    AGENTS_WRITE = "agents:write"
    
    # User management permissions
    USERS_READ = "users:read"
    USERS_WRITE = "users:write"
    USERS_DELETE = "users:delete"
    
    # Billing permissions
    BILLING_READ = "billing:read"
    BILLING_WRITE = "billing:write"
    
    # Conversation permissions
    CONVERSATIONS_READ = "conversations:read"
    CONVERSATIONS_WRITE = "conversations:write"
    CONVERSATIONS_DELETE = "conversations:delete"
    
    # Admin permissions
    TENANT_ADMIN = "tenant:admin"
    SYSTEM_ADMIN = "system:admin"

class Role(Enum):
    MASTER = "MASTER"
    MEMBER = "MEMBER"
    SUSPENDED = "SUSPENDED"

ROLE_PERMISSIONS = {
    Role.MASTER: [
        Permission.AGENTS_READ,
        Permission.AGENTS_WRITE,
        Permission.USERS_READ,
        Permission.USERS_WRITE,
        Permission.USERS_DELETE,
        Permission.BILLING_READ,
        Permission.BILLING_WRITE,
        Permission.CONVERSATIONS_READ,
        Permission.CONVERSATIONS_WRITE,
        Permission.CONVERSATIONS_DELETE,
        Permission.TENANT_ADMIN
    ],
    Role.MEMBER: [
        Permission.AGENTS_READ,
        Permission.AGENTS_WRITE,
        Permission.CONVERSATIONS_READ,
        Permission.CONVERSATIONS_WRITE
    ],
    Role.SUSPENDED: []  # No permissions when suspended
}

def check_permission(tenant_context: TenantContext, required_permission: Permission) -> bool:
    """Check if user has required permission"""
    user_role = Role(tenant_context.role)
    allowed_permissions = ROLE_PERMISSIONS.get(user_role, [])
    
    return required_permission in allowed_permissions

def require_permission(permission: Permission):
    """Decorator to require specific permission"""
    def decorator(func):
        @wraps(func)
        async def wrapper(event, context):
            tenant_context = event.get('tenant_context')
            
            if not tenant_context:
                return {
                    'statusCode': 401,
                    'body': json.dumps({'error': 'NO_TENANT_CONTEXT'})
                }
            
            if not check_permission(tenant_context, permission):
                return {
                    'statusCode': 403,
                    'body': json.dumps({
                        'error': 'INSUFFICIENT_PERMISSIONS',
                        'required_permission': permission.value
                    })
                }
            
            return await func(event, context)
        return wrapper
    return decorator
```

## 🔒 Data Encryption & Protection

### **Encryption at Rest**

#### **DynamoDB Encryption**
```yaml
DynamoDB Encryption Configuration:
  CustomerManagedKeys: true
  KeyRotation: Enabled (annual)
  PerTenantKeys: Enterprise customers only
  
  Standard Configuration:
    - Single customer-managed KMS key
    - Automatic encryption for all tables
    - Point-in-time recovery enabled
    
  Enterprise Configuration:
    - Dedicated KMS key per tenant
    - Tenant-specific access policies
    - Enhanced audit logging
```

#### **S3 Encryption Strategy**
```python
import boto3
from botocore.exceptions import ClientError

class S3EncryptionManager:
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.kms = boto3.client('kms')
        
    async def create_tenant_kms_key(self, tenant_id: str) -> str:
        """Create dedicated KMS key for enterprise tenant"""
        key_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "Enable IAM policies",
                    "Effect": "Allow",
                    "Principal": {"AWS": f"arn:aws:iam::{AWS_ACCOUNT_ID}:root"},
                    "Action": "kms:*",
                    "Resource": "*"
                },
                {
                    "Sid": "Allow tenant access",
                    "Effect": "Allow",
                    "Principal": {"AWS": f"arn:aws:iam::{AWS_ACCOUNT_ID}:role/TenantRole-{tenant_id}"},
                    "Action": [
                        "kms:Encrypt",
                        "kms:Decrypt",
                        "kms:ReEncrypt*",
                        "kms:GenerateDataKey*",
                        "kms:DescribeKey"
                    ],
                    "Resource": "*"
                }
            ]
        }
        
        response = self.kms.create_key(
            Policy=json.dumps(key_policy),
            Description=f"Tenant {tenant_id} encryption key",
            KeyUsage='ENCRYPT_DECRYPT',
            Origin='AWS_KMS',
            Tags=[
                {'TagKey': 'TenantId', 'TagValue': tenant_id},
                {'TagKey': 'Purpose', 'TagValue': 'TenantDataEncryption'}
            ]
        )
        
        key_id = response['KeyMetadata']['KeyId']
        
        # Create alias for easier management
        self.kms.create_alias(
            AliasName=f'alias/tenant-{tenant_id}',
            TargetKeyId=key_id
        )
        
        return key_id
    
    async def configure_bucket_encryption(self, bucket_name: str, 
                                         kms_key_id: str = None):
        """Configure S3 bucket encryption"""
        if kms_key_id:
            # Customer-managed KMS key
            encryption_config = {
                'Rules': [
                    {
                        'ApplyServerSideEncryptionByDefault': {
                            'SSEAlgorithm': 'aws:kms',
                            'KMSMasterKeyID': kms_key_id
                        },
                        'BucketKeyEnabled': True
                    }
                ]
            }
        else:
            # AWS-managed key (default)
            encryption_config = {
                'Rules': [
                    {
                        'ApplyServerSideEncryptionByDefault': {
                            'SSEAlgorithm': 'AES256'
                        }
                    }
                ]
            }
        
        self.s3.put_bucket_encryption(
            Bucket=bucket_name,
            ServerSideEncryptionConfiguration=encryption_config
        )
```

### **Encryption in Transit**

#### **TLS Configuration**
```yaml
API Gateway TLS Configuration:
  MinimumTLSVersion: 1.3
  CipherSuites: 
    - TLS_AES_256_GCM_SHA384
    - TLS_CHACHA20_POLY1305_SHA256
    - TLS_AES_128_GCM_SHA256
  
  SecurityPolicy: TLSv1.3_2021-06
  CertificateSource: AWS Certificate Manager
  HSTS: Enabled
  
CloudFront Security Headers:
  Strict-Transport-Security: "max-age=31536000; includeSubDomains"
  X-Content-Type-Options: "nosniff"
  X-Frame-Options: "DENY"
  X-XSS-Protection: "1; mode=block"
  Content-Security-Policy: "default-src 'self'"
```

#### **Internal Service Communication**
```python
import httpx
import asyncio
from cryptography.fernet import Fernet

class SecureServiceClient:
    def __init__(self, encryption_key: bytes):
        self.cipher = Fernet(encryption_key)
        self.client = httpx.AsyncClient(
            verify=True,  # Verify SSL certificates
            timeout=30.0
        )
        
    async def secure_request(self, method: str, url: str, 
                           data: dict = None, headers: dict = None) -> dict:
        """Make secure request between services"""
        
        # Encrypt sensitive data
        if data:
            encrypted_data = self.cipher.encrypt(json.dumps(data).encode())
            payload = {'encrypted_payload': encrypted_data.decode()}
        else:
            payload = None
            
        # Add service authentication header
        auth_headers = {
            'Authorization': f'Bearer {await self.get_service_token()}',
            'Content-Type': 'application/json',
            'X-Service-Name': 'platform-backend'
        }
        
        if headers:
            auth_headers.update(headers)
            
        response = await self.client.request(
            method=method,
            url=url,
            json=payload,
            headers=auth_headers
        )
        
        response.raise_for_status()
        
        # Decrypt response if needed
        response_data = response.json()
        if 'encrypted_payload' in response_data:
            decrypted = self.cipher.decrypt(response_data['encrypted_payload'].encode())
            return json.loads(decrypted.decode())
            
        return response_data
```

## 🛡️ Input Validation & Sanitization

### **API Input Validation**
```python
from pydantic import BaseModel, validator, Field
from typing import Optional, List
import re

class CompanyRegistrationModel(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    industry: str = Field(..., regex=r'^(retail|manufacturing|distribution|logistics)$')
    size: str = Field(..., regex=r'^(small|medium|large|enterprise)$')
    country: str = Field(..., regex=r'^[A-Z]{2}$')
    tax_id: Optional[str] = Field(None, max_length=50)
    website: Optional[str] = Field(None, regex=r'^https?://[^\s/$.?#].[^\s]*$')
    
    @validator('name')
    def sanitize_name(cls, v):
        # Remove potentially dangerous characters
        return re.sub(r'[<>"\']', '', v).strip()
    
    @validator('tax_id')
    def validate_tax_id(cls, v):
        if v:
            # Remove all non-alphanumeric characters
            return re.sub(r'[^a-zA-Z0-9]', '', v)
        return v

class UserRegistrationModel(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    email: str = Field(..., regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    password: str = Field(..., min_length=8, max_length=128)
    phone: Optional[str] = Field(None, regex=r'^\+[1-9]\d{1,14}$')
    
    @validator('email')
    def normalize_email(cls, v):
        return v.lower().strip()
    
    @validator('password')
    def validate_password(cls, v):
        # Check password complexity
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain special character')
        return v

def validate_request(model_class):
    """Decorator for request validation"""
    def decorator(func):
        @wraps(func)
        async def wrapper(event, context):
            try:
                body = json.loads(event.get('body', '{}'))
                validated_data = model_class(**body)
                event['validated_data'] = validated_data.dict()
                return await func(event, context)
                
            except ValueError as e:
                return {
                    'statusCode': 400,
                    'body': json.dumps({
                        'error': 'VALIDATION_ERROR',
                        'message': str(e)
                    })
                }
                
        return wrapper
    return decorator
```

### **SQL Injection Prevention**
```python
import re
from typing import Any, Dict

class SecureQueryBuilder:
    """Secure query builder for DynamoDB operations"""
    
    ALLOWED_OPERATORS = ['=', '>', '<', '>=', '<=', 'between', 'begins_with']
    RESERVED_WORDS = ['select', 'insert', 'update', 'delete', 'drop', 'union']
    
    @staticmethod
    def sanitize_attribute_name(name: str) -> str:
        """Sanitize DynamoDB attribute names"""
        # Only allow alphanumeric and underscore
        if not re.match(r'^[a-zA-Z0-9_]+$', name):
            raise ValueError(f"Invalid attribute name: {name}")
            
        # Check against reserved words
        if name.lower() in SecureQueryBuilder.RESERVED_WORDS:
            raise ValueError(f"Reserved word not allowed: {name}")
            
        return name
    
    @staticmethod
    def sanitize_attribute_value(value: Any) -> Any:
        """Sanitize attribute values"""
        if isinstance(value, str):
            # Remove null bytes and control characters
            value = re.sub(r'[\x00-\x1f\x7f]', '', value)
            # Limit string length
            if len(value) > 10000:
                raise ValueError("String value too long")
                
        return value
    
    @staticmethod
    def build_filter_expression(filters: Dict[str, Any]) -> str:
        """Build secure filter expression"""
        conditions = []
        
        for attr_name, condition in filters.items():
            # Sanitize attribute name
            safe_attr = SecureQueryBuilder.sanitize_attribute_name(attr_name)
            
            if isinstance(condition, dict):
                operator = condition.get('operator', '=')
                value = condition.get('value')
                
                if operator not in SecureQueryBuilder.ALLOWED_OPERATORS:
                    raise ValueError(f"Invalid operator: {operator}")
                
                # Sanitize value
                safe_value = SecureQueryBuilder.sanitize_attribute_value(value)
                
                conditions.append(f"{safe_attr} {operator} :{safe_attr}")
                
        return ' AND '.join(conditions)
```

## 🔍 Security Monitoring & Logging

### **Audit Logging**
```python
import json
import boto3
from datetime import datetime
from enum import Enum

class AuditEventType(Enum):
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_CREATED = "user_created"
    USER_DELETED = "user_deleted"
    PERMISSION_CHANGED = "permission_changed"
    DATA_ACCESSED = "data_accessed"
    DATA_MODIFIED = "data_modified"
    PAYMENT_PROCESSED = "payment_processed"
    SECURITY_VIOLATION = "security_violation"
    CONFIGURATION_CHANGED = "configuration_changed"

class SecurityAuditor:
    def __init__(self):
        self.cloudwatch = boto3.client('cloudwatch')
        self.logs_client = boto3.client('logs')
        
    async def log_security_event(self, event_type: AuditEventType, 
                                tenant_id: str, user_id: str = None,
                                details: dict = None, severity: str = 'INFO'):
        """Log security-related events"""
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type.value,
            'tenant_id': tenant_id,
            'user_id': user_id,
            'severity': severity,
            'details': details or {},
            'source_ip': details.get('ip_address') if details else None,
            'user_agent': details.get('user_agent') if details else None,
            'session_id': details.get('session_id') if details else None
        }
        
        # Send to CloudWatch Logs
        await self._send_to_cloudwatch(log_entry)
        
        # Send metrics to CloudWatch
        await self._send_metrics(event_type, tenant_id, severity)
        
        # Check for security violations
        if severity in ['WARNING', 'ERROR', 'CRITICAL']:
            await self._check_security_alerts(log_entry)
    
    async def _send_to_cloudwatch(self, log_entry: dict):
        """Send log entry to CloudWatch"""
        try:
            self.logs_client.put_log_events(
                logGroupName='/aws/lambda/platform-security-audit',
                logStreamName=f"tenant-{log_entry['tenant_id']}",
                logEvents=[
                    {
                        'timestamp': int(datetime.utcnow().timestamp() * 1000),
                        'message': json.dumps(log_entry)
                    }
                ]
            )
        except Exception as e:
            print(f"Failed to send audit log: {str(e)}")
    
    async def _send_metrics(self, event_type: AuditEventType, 
                           tenant_id: str, severity: str):
        """Send custom metrics to CloudWatch"""
        try:
            self.cloudwatch.put_metric_data(
                Namespace='Platform/Security',
                MetricData=[
                    {
                        'MetricName': 'SecurityEvents',
                        'Dimensions': [
                            {'Name': 'TenantId', 'Value': tenant_id},
                            {'Name': 'EventType', 'Value': event_type.value},
                            {'Name': 'Severity', 'Value': severity}
                        ],
                        'Value': 1,
                        'Unit': 'Count'
                    }
                ]
            )
        except Exception as e:
            print(f"Failed to send metrics: {str(e)}")
    
    async def _check_security_alerts(self, log_entry: dict):
        """Check for patterns that require immediate attention"""
        
        # Multiple failed login attempts
        if log_entry['event_type'] == 'user_login' and log_entry['severity'] == 'WARNING':
            await self._check_brute_force_attempt(log_entry)
        
        # Unusual data access patterns
        if log_entry['event_type'] == 'data_accessed':
            await self._check_data_access_patterns(log_entry)
        
        # Permission escalation attempts
        if log_entry['event_type'] == 'permission_changed':
            await self._check_permission_escalation(log_entry)
```

### **Intrusion Detection**
```python
import asyncio
from collections import defaultdict
from datetime import datetime, timedelta

class IntrusionDetectionSystem:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.alert_thresholds = {
            'failed_logins': 5,
            'unusual_access_patterns': 10,
            'suspicious_queries': 3,
            'rate_limit_violations': 5
        }
        
    async def detect_brute_force(self, ip_address: str, user_id: str = None) -> bool:
        """Detect brute force login attempts"""
        key = f"failed_login:{ip_address}"
        if user_id:
            key += f":{user_id}"
            
        # Increment counter
        count = await self.redis.incr(key)
        await self.redis.expire(key, 300)  # 5 minute window
        
        if count >= self.alert_thresholds['failed_logins']:
            await self._trigger_alert('BRUTE_FORCE_DETECTED', {
                'ip_address': ip_address,
                'user_id': user_id,
                'attempt_count': count
            })
            
            # Implement IP blocking
            await self._block_ip_address(ip_address, duration=900)  # 15 minutes
            return True
            
        return False
    
    async def detect_unusual_access_pattern(self, tenant_id: str, 
                                          user_id: str, resource: str):
        """Detect unusual data access patterns"""
        
        # Track access frequency
        key = f"access_pattern:{tenant_id}:{user_id}:{resource}"
        count = await self.redis.incr(key)
        await self.redis.expire(key, 3600)  # 1 hour window
        
        # Get historical average
        historical_key = f"access_avg:{tenant_id}:{user_id}:{resource}"
        historical_avg = await self.redis.get(historical_key) or 0
        
        # Alert if current access is significantly higher than average
        if count > (float(historical_avg) * 3):
            await self._trigger_alert('UNUSUAL_ACCESS_PATTERN', {
                'tenant_id': tenant_id,
                'user_id': user_id,
                'resource': resource,
                'current_count': count,
                'historical_average': historical_avg
            })
    
    async def detect_privilege_escalation(self, tenant_id: str, 
                                        user_id: str, attempted_action: str):
        """Detect privilege escalation attempts"""
        
        # Check if user is attempting actions beyond their role
        user_permissions = await self.get_user_permissions(tenant_id, user_id)
        required_permission = self.get_required_permission(attempted_action)
        
        if required_permission not in user_permissions:
            await self._trigger_alert('PRIVILEGE_ESCALATION_ATTEMPT', {
                'tenant_id': tenant_id,
                'user_id': user_id,
                'attempted_action': attempted_action,
                'user_permissions': user_permissions,
                'required_permission': required_permission
            })
            
            # Temporarily suspend user for investigation
            await self._suspend_user_temporarily(tenant_id, user_id, duration=300)
    
    async def _trigger_alert(self, alert_type: str, details: dict):
        """Trigger security alert"""
        alert_data = {
            'alert_type': alert_type,
            'timestamp': datetime.utcnow().isoformat(),
            'details': details,
            'severity': 'HIGH'
        }
        
        # Send to security monitoring system
        await self._send_to_security_team(alert_data)
        
        # Log for audit trail
        auditor = SecurityAuditor()
        await auditor.log_security_event(
            AuditEventType.SECURITY_VIOLATION,
            details.get('tenant_id'),
            details.get('user_id'),
            alert_data,
            'ERROR'
        )
    
    async def _block_ip_address(self, ip_address: str, duration: int):
        """Block IP address for specified duration"""
        await self.redis.setex(f"blocked_ip:{ip_address}", duration, "1")
        
        # Update WAF rules if necessary
        await self._update_waf_rules(ip_address, action='block')
```

## 🔒 Compliance & Data Protection

### **GDPR Compliance**
```python
from typing import List, Dict
import asyncio

class GDPRComplianceManager:
    def __init__(self, dynamodb_table, s3_client):
        self.table = dynamodb_table
        self.s3 = s3_client
        
    async def handle_data_subject_request(self, request_type: str, 
                                        tenant_id: str, email: str) -> Dict:
        """Handle GDPR data subject requests"""
        
        if request_type == 'access':
            return await self._export_user_data(tenant_id, email)
        elif request_type == 'deletion':
            return await self._delete_user_data(tenant_id, email)
        elif request_type == 'portability':
            return await self._export_portable_data(tenant_id, email)
        elif request_type == 'rectification':
            return await self._update_user_data(tenant_id, email)
        else:
            raise ValueError(f"Unknown request type: {request_type}")
    
    async def _export_user_data(self, tenant_id: str, email: str) -> Dict:
        """Export all user data (Right to Access)"""
        
        # Find user ID from email
        user = await self.find_user_by_email(tenant_id, email)
        if not user:
            return {'found': False, 'message': 'User not found'}
        
        user_id = user['user_id']
        
        # Collect all user data
        user_data = {
            'personal_info': await self.get_user_profile(user_id),
            'conversations': await self.get_user_conversations(user_id),
            'usage_history': await self.get_user_usage_history(tenant_id, user_id),
            'login_history': await self.get_user_login_history(user_id),
            'billing_history': await self.get_user_billing_data(tenant_id, user_id)
        }
        
        # Generate export file
        export_url = await self._create_data_export(tenant_id, user_id, user_data)
        
        # Log GDPR request
        await self._log_gdpr_request('access', tenant_id, user_id, email)
        
        return {
            'found': True,
            'export_url': export_url,
            'expires_at': (datetime.utcnow() + timedelta(days=7)).isoformat()
        }
    
    async def _delete_user_data(self, tenant_id: str, email: str) -> Dict:
        """Delete user data (Right to Erasure)"""
        
        user = await self.find_user_by_email(tenant_id, email)
        if not user:
            return {'found': False, 'message': 'User not found'}
        
        user_id = user['user_id']
        
        # Check if user is the last admin (prevent deletion)
        if await self._is_last_admin(tenant_id, user_id):
            return {
                'success': False,
                'message': 'Cannot delete last admin user'
            }
        
        # Anonymize instead of hard delete to maintain referential integrity
        await self._anonymize_user_data(tenant_id, user_id)
        
        # Log GDPR request
        await self._log_gdpr_request('deletion', tenant_id, user_id, email)
        
        return {
            'success': True,
            'message': 'User data has been anonymized'
        }
    
    async def _anonymize_user_data(self, tenant_id: str, user_id: str):
        """Anonymize user data while preserving system integrity"""
        
        # Generate anonymous identifier
        anonymous_id = f"anon_{secrets.token_hex(8)}"
        
        # Update user record
        anonymized_data = {
            'first_name': 'Anonymous',
            'last_name': 'User',
            'email': f'{anonymous_id}@deleted.local',
            'phone': None,
            'profile_image': None,
            'status': 'deleted',
            'deleted_at': datetime.utcnow().isoformat(),
            'gdpr_deletion_date': datetime.utcnow().isoformat()
        }
        
        await self.table.update_item(
            Key={'PK': f'USER#{user_id}', 'SK': 'profile'},
            UpdateExpression='SET #data = :data',
            ExpressionAttributeNames={'#data': 'data'},
            ExpressionAttributeValues={':data': anonymized_data}
        )
        
        # Anonymize conversations (keep content for business continuity)
        await self._anonymize_conversation_metadata(user_id, anonymous_id)
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** Security Architecture Team  
**Review Cycle:** Weekly during development, monthly post-launch