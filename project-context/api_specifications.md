# API Specifications & Contracts

## 📡 API Overview

### **API Design Principles**
- **RESTful Design:** Standard HTTP methods and status codes
- **Tenant-Aware:** All endpoints require tenant context
- **Consistent Response Format:** Standardized JSON responses
- **Comprehensive Error Handling:** Detailed error codes and messages
- **Rate Limiting:** Per-tenant and per-user limits
- **Versioning:** URI versioning (v1, v2, etc.)
- **OpenAPI 3.0:** Complete API documentation

### **Base URL Structure**
```
Production: https://api.platform.com/v1
Staging: https://api-staging.platform.com/v1  
Development: https://api-dev.platform.com/v1
```

### **Standard Response Format**
```json
{
  "success": true|false,
  "data": {},
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {}
  },
  "meta": {
    "timestamp": "2025-07-16T10:30:00Z",
    "request_id": "req_*********",
    "tenant_id": "tenant_456"
  }
}
```

## 🔐 Authentication Service API

### **POST /auth/register**
Register new tenant and master user

**Request:**
```json
{
  "company": {
    "name": "Acme Logistics",
    "industry": "retail|manufacturing|distribution",
    "size": "small|medium|large|enterprise",
    "country": "US",
    "tax_id": "*********"
  },
  "user": {
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "phone": "+*********0"
  },
  "plan": {
    "plan_id": "basic|standard|premium|enterprise",
    "billing_cycle": "monthly|yearly"
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "tenant_id": "tenant_123",
    "user_id": "user_456", 
    "registration_status": "pending_payment",
    "payment_intent": {
      "client_secret": "pi_123_secret_456",
      "amount": 29900,
      "currency": "usd"
    }
  }
}
```

### **POST /auth/login**
Authenticate user and return tokens

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user": {
      "user_id": "user_456",
      "tenant_id": "tenant_123",
      "role": "MASTER|MEMBER",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "tenant": {
      "tenant_id": "tenant_123",
      "name": "Acme Logistics", 
      "status": "active|suspended|cancelled",
      "plan": "basic|standard|premium|enterprise"
    }
  }
}
```

### **POST /auth/refresh**
Refresh access token using refresh token

**Request:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  }
}
```

### **POST /auth/forgot-password**
Request password reset

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Password reset code sent to email",
    "reset_token_expires": "2025-07-16T11:30:00Z"
  }
}
```

### **POST /auth/reset-password**
Reset password with verification code

**Request:**
```json
{
  "email": "<EMAIL>",
  "reset_code": "123456",
  "new_password": "NewSecurePass123!"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Password successfully reset"
  }
}
```

### **POST /auth/verify-email**
Verify user email with code

**Request:**
```json
{
  "email": "<EMAIL>",
  "verification_code": "654321"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Email successfully verified",
    "user_status": "active"
  }
}
```

### **POST /auth/resend-code**
Resend verification code

**Request:**
```json
{
  "email": "<EMAIL>",
  "code_type": "email_verification|password_reset"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Verification code resent",
    "expires_at": "2025-07-16T11:15:00Z"
  }
}
```

## 👥 User Management API

### **POST /users/invite**
Invite new user to tenant (Master only)

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "email": "<EMAIL>",
  "first_name": "Jane",
  "last_name": "Smith",
  "role": "MEMBER",
  "permissions": ["agents:read", "conversations:write"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user_id": "user_789",
    "invitation_sent": true,
    "invitation_expires": "2025-07-23T10:30:00Z"
  }
}
```

### **GET /users**
Get all users in tenant (Master only)

**Headers:**
```
Authorization: Bearer {access_token}
```

**Query Parameters:**
```
?status=active|inactive&role=MASTER|MEMBER&limit=20&offset=0
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "user_id": "user_456",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "role": "MASTER",
        "status": "active",
        "created_at": "2025-07-16T09:00:00Z",
        "last_login": "2025-07-16T10:30:00Z"
      }
    ],
    "total": 5,
    "limit": 20,
    "offset": 0
  }
}
```

### **PUT /users/{user_id}/status**
Update user status (Master only)

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "status": "active|inactive|suspended"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user_id": "user_789",
    "status": "inactive",
    "updated_at": "2025-07-16T10:30:00Z"
  }
}
```

### **DELETE /users/{user_id}**
Remove user from tenant (Master only)

**Headers:**
```
Authorization: Bearer {access_token}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "User successfully removed",
    "user_id": "user_789"
  }
}
```

## 💳 Payment & Subscription API

### **POST /payments/subscribe**
Create or update subscription

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "plan_id": "basic|standard|premium|enterprise",
  "payment_method": {
    "type": "card",
    "card_token": "pm_*********0"
  },
  "billing_cycle": "monthly|yearly"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "subscription_id": "sub_123",
    "plan": "standard",
    "status": "active",
    "current_period_start": "2025-07-16T00:00:00Z",
    "current_period_end": "2025-08-16T00:00:00Z",
    "next_billing_date": "2025-08-16T00:00:00Z"
  }
}
```

### **PUT /payments/plan**
Change subscription plan

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "new_plan_id": "premium",
  "effective_date": "immediate|next_cycle"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "subscription_id": "sub_123",
    "old_plan": "standard",
    "new_plan": "premium",
    "proration_amount": 1000,
    "effective_date": "2025-07-16T10:30:00Z"
  }
}
```

### **POST /payments/cancel**
Cancel subscription

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "cancel_at": "end_of_period|immediately",
  "reason": "cost|features|switching|other"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "subscription_id": "sub_123",
    "status": "cancelled",
    "cancelled_at": "2025-07-16T10:30:00Z",
    "service_ends_at": "2025-08-16T00:00:00Z"
  }
}
```

### **GET /payments/history**
Get payment history

**Headers:**
```
Authorization: Bearer {access_token}
```

**Query Parameters:**
```
?start_date=2025-01-01&end_date=2025-07-16&limit=20&offset=0
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "payment_id": "pay_123",
        "amount": 79900,
        "currency": "usd",
        "status": "succeeded",
        "payment_date": "2025-07-16T00:00:00Z",
        "plan": "standard",
        "invoice_url": "https://invoices.stripe.com/..."
      }
    ],
    "total": 6,
    "limit": 20,
    "offset": 0
  }
}
```

### **PUT /payments/method**
Update payment method

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "payment_method": {
    "type": "card",
    "card_token": "pm_9876543210"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "payment_method_id": "pm_9876543210",
    "card_last4": "4242",
    "card_brand": "visa",
    "updated_at": "2025-07-16T10:30:00Z"
  }
}
```

### **POST /webhooks/payment**
Handle payment provider webhooks

**Request (Stripe):**
```json
{
  "id": "evt_123",
  "type": "invoice.payment_succeeded",
  "data": {
    "object": {
      "id": "in_123",
      "customer": "cus_123",
      "subscription": "sub_123",
      "amount_paid": 79900,
      "status": "paid"
    }
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Webhook processed successfully"
  }
}
```

## 🤖 Agent Service API

### **GET /agents/available**
Get available agents for tenant

**Headers:**
```
Authorization: Bearer {access_token}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "agent_id": "feedo",
        "name": "Feedo",
        "description": "Data ingestion and normalization agent",
        "capabilities": ["data_upload", "template_generation", "data_validation"],
        "status": "available",
        "plan_required": "basic"
      },
      {
        "agent_id": "forecaster", 
        "name": "Forecaster",
        "description": "Predictive analytics and inventory optimization",
        "capabilities": ["demand_forecasting", "reorder_points", "dashboard_creation"],
        "status": "available",
        "plan_required": "standard"
      }
    ]
  }
}
```

### **POST /agents/{agent_id}/chat**
Send message to agent

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "conversation_id": "conv_123",
  "message": "Please provide me with the inventory template for retail products",
  "attachments": [
    {
      "type": "file",
      "url": "https://s3.amazonaws.com/bucket/file.xlsx",
      "filename": "inventory_data.xlsx"
    }
  ]
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message_id": "msg_456",
    "conversation_id": "conv_123",
    "agent_response": {
      "text": "I've generated a retail inventory template for you. Please find the downloadable template below.",
      "attachments": [
        {
          "type": "template",
          "url": "https://s3.amazonaws.com/bucket/retail_template.xlsx", 
          "filename": "retail_inventory_template.xlsx"
        }
      ],
      "suggested_actions": [
        "download_template",
        "upload_data",
        "view_examples"
      ]
    },
    "timestamp": "2025-07-16T10:30:00Z"
  }
}
```

## 💬 Conversation Management API

### **POST /conversations**
Create new conversation

**Headers:**
```
Authorization: Bearer {access_token}
```

**Request:**
```json
{
  "agent_id": "feedo",
  "title": "July Inventory Upload",
  "context": {
    "product_category": "retail",
    "warehouse_location": "warehouse_01"
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "conversation_id": "conv_789",
    "agent_id": "feedo",
    "title": "July Inventory Upload",
    "status": "active",
    "created_at": "2025-07-16T10:30:00Z"
  }
}
```

### **GET /conversations**
Get user's conversations

**Headers:**
```
Authorization: Bearer {access_token}
```

**Query Parameters:**
```
?agent_id=feedo&status=active&limit=20&offset=0
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "conversation_id": "conv_123",
        "agent_id": "forecaster", 
        "title": "Q3 Demand Forecast",
        "status": "active",
        "last_message": {
          "text": "Based on your historical data, I recommend increasing stock for...",
          "timestamp": "2025-07-16T10:25:00Z"
        },
        "created_at": "2025-07-15T14:30:00Z",
        "updated_at": "2025-07-16T10:25:00Z"
      }
    ],
    "total": 12,
    "limit": 20,
    "offset": 0
  }
}
```

### **GET /conversations/{conversation_id}/messages**
Get conversation messages

**Headers:**
```
Authorization: Bearer {access_token}
```

**Query Parameters:**
```
?limit=50&offset=0&order=asc|desc
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "message_id": "msg_123",
        "sender_type": "user",
        "sender_id": "user_456",
        "content": {
          "text": "Can you analyze my current inventory levels?",
          "attachments": []
        },
        "timestamp": "2025-07-16T10:20:00Z"
      },
      {
        "message_id": "msg_124", 
        "sender_type": "agent",
        "sender_id": "forecaster",
        "content": {
          "text": "I'll analyze your inventory levels. Let me process your data first.",
          "attachments": [
            {
              "type": "dashboard",
              "url": "https://dashboard.platform.com/inventory/123",
              "title": "Current Inventory Analysis"
            }
          ]
        },
        "timestamp": "2025-07-16T10:21:00Z"
      }
    ],
    "total": 15,
    "limit": 50,
    "offset": 0
  }
}
```

## 📊 Analytics & Usage API

### **GET /analytics/usage**
Get tenant usage analytics

**Headers:**
```
Authorization: Bearer {access_token}
```

**Query Parameters:**
```
?start_date=2025-07-01&end_date=2025-07-16&granularity=day|hour
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "period": {
      "start": "2025-07-01T00:00:00Z",
      "end": "2025-07-16T23:59:59Z"
    },
    "usage": {
      "conversations_started": 45,
      "messages_sent": 1250,
      "data_records_processed": 2500000,
      "api_calls": 8500,
      "storage_used_gb": 12.5
    },
    "limits": {
      "plan": "standard",
      "max_records_monthly": 1000000,
      "max_api_calls_monthly": 10000,
      "max_storage_gb": 50
    },
    "overage": {
      "records_overage": 1500000,
      "overage_charges": 150.00
    }
  }
}
```

## 🌐 WebSocket API (Real-time Chat)

### **Connection Endpoint**
```
wss://api.platform.com/v1/chat?token={access_token}
```

### **Message Format**
```json
{
  "type": "message|typing|connection",
  "conversation_id": "conv_123",
  "agent_id": "forecaster",
  "data": {
    "text": "User message content",
    "attachments": []
  },
  "timestamp": "2025-07-16T10:30:00Z"
}
```

### **Server Events**
```json
{
  "type": "agent_response|agent_typing|error",
  "conversation_id": "conv_123", 
  "data": {
    "message_id": "msg_456",
    "text": "Agent response",
    "attachments": [],
    "processing_time_ms": 1250
  },
  "timestamp": "2025-07-16T10:30:05Z"
}
```

## ❌ Error Handling

### **Standard Error Codes**

#### **Authentication Errors (401)**
```json
{
  "success": false,
  "error": {
    "code": "AUTH_INVALID_TOKEN",
    "message": "Access token is invalid or expired",
    "details": {
      "token_expired": true,
      "expires_at": "2025-07-16T09:30:00Z"
    }
  }
}
```

#### **Authorization Errors (403)**
```json
{
  "success": false,
  "error": {
    "code": "AUTH_INSUFFICIENT_PERMISSIONS",
    "message": "User does not have permission to access this resource",
    "details": {
      "required_permission": "users:write",
      "user_permissions": ["agents:read", "conversations:write"]
    }
  }
}
```

#### **Validation Errors (400)**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": {
      "field_errors": {
        "email": "Must be a valid email address",
        "password": "Must be at least 8 characters long"
      }
    }
  }
}
```

#### **Business Logic Errors (422)**
```json
{
  "success": false,
  "error": {
    "code": "SUBSCRIPTION_SUSPENDED",
    "message": "Tenant subscription is suspended due to failed payment",
    "details": {
      "suspension_date": "2025-07-15T00:00:00Z",
      "last_payment_attempt": "2025-07-14T23:45:00Z",
      "action_required": "update_payment_method"
    }
  }
}
```

#### **Rate Limiting Errors (429)**
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "API rate limit exceeded",
    "details": {
      "limit": 1000,
      "window": "1 hour",
      "reset_at": "2025-07-16T11:00:00Z"
    }
  }
}
```

### **Error Code Reference**

| Error Code | Status | Description |
|------------|--------|-------------|
| AUTH_INVALID_TOKEN | 401 | Token invalid/expired |
| AUTH_INSUFFICIENT_PERMISSIONS | 403 | Missing permissions |
| VALIDATION_FAILED | 400 | Request validation error |
| TENANT_NOT_FOUND | 404 | Tenant doesn't exist |
| SUBSCRIPTION_SUSPENDED | 422 | Service suspended |
| RATE_LIMIT_EXCEEDED | 429 | Too many requests |
| AGENT_UNAVAILABLE | 503 | Agent service down |
| PAYMENT_FAILED | 422 | Payment processing error |
| DATA_PROCESSING_ERROR | 500 | Data processing failed |

## 🔒 Rate Limiting

### **Rate Limit Rules**
```yaml
Global Limits:
  - 10,000 requests/hour per tenant
  - 1,000 requests/minute per user
  
Endpoint-Specific Limits:
  auth/login: 10/minute per IP
  auth/register: 5/hour per IP
  agents/chat: 100/minute per user
  payments/*: 20/minute per tenant
  
WebSocket Connections:
  - 50 concurrent connections per tenant
  - 100 messages/minute per connection
```

### **Rate Limit Headers**
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1626439800
X-RateLimit-Retry-After: 60
```

---

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Owner:** API Development Team  
**Review Cycle:** Weekly during development, monthly post-launch