import json
import os
import sys

def lambda_handler(event, context):
    """Test function to debug layer structure"""
    
    result = {
        "python_path": sys.path,
        "opt_contents": [],
        "opt_python_contents": [],
        "shared_module_test": None
    }
    
    # Check /opt directory
    if os.path.exists('/opt'):
        result["opt_contents"] = os.listdir('/opt')
    
    # Check /opt/python directory
    if os.path.exists('/opt/python'):
        result["opt_python_contents"] = os.listdir('/opt/python')
    
    # Try to import shared
    try:
        import shared
        result["shared_module_test"] = "SUCCESS - shared module imported"
        result["shared_dir"] = dir(shared)
    except Exception as e:
        result["shared_module_test"] = f"ERROR: {str(e)}"
    
    return {
        'statusCode': 200,
        'body': json.dumps(result, indent=2)
    }
