# 🧪 **AUTH SERVICE - POSTMAN TESTING GUIDE**

## 📋 **COLLECTIONS INCLUIDAS**

### **1. `auth-service-postman-collection.json`**
- **Collection principal** con flujo completo de registro y autenticación
- **Endpoints organizados** por funcionalidad
- **Variables automáticas** para tokens y IDs
- **Scripts de prueba** integrados

### **2. `auth-service-test-cases.json`**
- **Casos de prueba específicos** con usuarios existentes
- **Escenarios de error** y validaciones
- **Tests automatizados** con assertions
- **Casos edge** y validaciones de seguridad

## 🚀 **INSTRUCCIONES DE IMPORTACIÓN**

### **Paso 1: Importar Collections**
1. Abrir **Postman**
2. Click en **Import** (botón superior izquierdo)
3. Arrastrar ambos archivos JSON o usar **Upload Files**
4. Confirmar importación

### **Paso 2: Configurar Environment**
1. Crear nuevo **Environment** llamado "Auth Service Dev"
2. Agregar las siguientes variables:

```
auth_base_url = https://sus2ukuiqk.execute-api.us-east-1.amazonaws.com/dev
base_url = https://f8u12wibf9.execute-api.us-east-1.amazonaws.com/dev
payment_base_url = https://69fnq5hxj4.execute-api.us-east-1.amazonaws.com/dev
tenant_id = (se llenará automáticamente)
access_token = (se llenará automáticamente)
refresh_token = (se llenará automáticamente)
```

## 🧪 **ORDEN DE PRUEBAS RECOMENDADO**

### **A. PRUEBAS CON USUARIOS EXISTENTES (Más Rápido)**

#### **Collection: auth-service-test-cases.json**

1. **Login - TechCorp CEO (Active)**
   - ✅ Debería retornar 200 con tokens
   - ✅ Usuario activo en base de datos
   - ✅ Guarda tokens automáticamente

2. **Login - Real Logistics Admin (Inactive)**
   - ❌ Debería retornar 401 (ACCOUNT_INACTIVE)
   - ✅ Valida que usuarios inactivos no pueden loguearse

3. **Refresh Token - Valid**
   - ✅ Usa el refresh token del login anterior
   - ✅ Debería retornar nuevo access token

4. **Logout - With Refresh Token**
   - ✅ Invalida la sesión
   - ✅ Debería retornar 200 con confirmación

5. **Verify Email - TechCorp Token**
   - ✅ Usa token real de la base de datos
   - ✅ Debería marcar email como verificado

#### **Casos de Error:**
6. **Login - Invalid Email** (401)
7. **Login - Wrong Password** (401)
8. **Register - Duplicate Email** (400)
9. **Refresh - Invalid Token** (401)
10. **Verify Email - Invalid Token** (400)

### **B. FLUJO COMPLETO DE REGISTRO (Más Completo)**

#### **Collection: auth-service-postman-collection.json**

1. **Register Tenant** (Tenant Service)
2. **Register User** (Auth Service)
3. **Process Payment** (Payment Service)
4. **Activate Registration** (Auth Service)
5. **Login** → **Refresh** → **Logout**

## 📊 **DATOS DE PRUEBA EXISTENTES**

### **Usuarios en Base de Datos:**

#### **TechCorp Solutions (ACTIVO)**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "tenant_id": "044b6833-883b-471e-ac0f-2307bbd2e5f0",
  "status": "active",
  "verification_token": "a1d3e4a8-2087-44f9-b19e-6d5bf137641a"
}
```

#### **Real Logistics Corp (INACTIVO)**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "tenant_id": "1aea7ad6-0028-44ac-b0bf-aeb0a35d61b3",
  "status": "inactive"
}
```

## ✅ **RESULTADOS ESPERADOS**

### **Casos Exitosos:**
- **Login TechCorp**: 200 + tokens JWT válidos
- **Refresh Token**: 200 + nuevo access token
- **Logout**: 200 + sesión invalidada
- **Verify Email**: 200 + email marcado como verificado

### **Casos de Error:**
- **Login Inactivo**: 401 + ACCOUNT_INACTIVE
- **Credenciales Inválidas**: 401 + INVALID_CREDENTIALS
- **Email Duplicado**: 400 + VALIDATION_ERROR
- **Token Inválido**: 401/400 + error específico

## 🔍 **VALIDACIONES AUTOMÁTICAS**

Las collections incluyen **tests automáticos** que validan:
- ✅ **Status codes** correctos
- ✅ **Estructura de respuesta** válida
- ✅ **Presencia de tokens** en respuestas exitosas
- ✅ **Error codes** específicos en fallos
- ✅ **Guardado automático** de tokens para siguientes requests

## 🚨 **TROUBLESHOOTING**

### **Si un endpoint falla:**
1. **Verificar URL** en variables de environment
2. **Revisar headers** Content-Type y Authorization
3. **Validar JSON** en request body
4. **Comprobar tokens** no expirados

### **Errores comunes:**
- **500 Internal Error**: Verificar que el servicio esté desplegado
- **401 Unauthorized**: Token expirado o inválido
- **400 Bad Request**: JSON malformado o campos faltantes

## 📈 **MÉTRICAS DE ÉXITO**

**Auth Service está funcionando correctamente si:**
- ✅ Login con credenciales válidas retorna 200
- ✅ Login con credenciales inválidas retorna 401
- ✅ Refresh token genera nuevo access token
- ✅ Logout invalida sesiones correctamente
- ✅ Verificación de email funciona
- ✅ Validaciones de seguridad están activas

¡**Listo para probar todos los endpoints del Auth Service!** 🚀
